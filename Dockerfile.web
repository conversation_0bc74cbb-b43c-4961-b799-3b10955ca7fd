FROM node:lts-alpine AS base

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable

# ----------------------
# Build Stage
# ----------------------
FROM base AS build

WORKDIR /repo

ARG NEXT_TELEMETRY_DISABLED=${NEXT_TELEMETRY_DISABLED}
ARG TURBO_TELEMETRY_DISABLED=${TURBO_TELEMETRY_DISABLED}

ARG NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
ARG NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
ARG NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY=${NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY}

ARG SMTP_FROM_NAME=${SMTP_FROM_NAME}
ARG SMTP_FROM_EMAIL=${SMTP_FROM_EMAIL}
ARG SMTP_HOST=${SMTP_HOST}
ARG SMTP_USER=${SMTP_USER}
ARG SMTP_PASS=${SMTP_PASS}

ARG TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID}
ARG TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN}
ARG TWILIO_PHONE_NUMBER=${TWILIO_PHONE_NUMBER}
ARG TWILIO_WHATSAPP_NUMBER=${TWILIO_WHATSAPP_NUMBER}
ARG TWILIO_WEBHOOK_URL=${TWILIO_WEBHOOK_URL}

ARG SAFECUBE_API_KEY=${SAFECUBE_API_KEY}
ARG NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=${NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}

RUN apk add --no-cache libc6-compat

# Copy lockfiles and manifests
COPY .npmrc pnpm-lock.yaml package.json turbo.json pnpm-workspace.yaml ./
COPY apps/admin/ apps/admin/
COPY packages/ui/ packages/ui/
COPY packages/mail/ packages/mail/
COPY packages/messaging/ packages/messaging/
COPY packages/pdf-generator/ packages/pdf-generator/

# Install dependencies with cache
RUN --mount=type=cache,id=pnpm,target=/pnpm/store \
    pnpm install --frozen-lockfile


# Copy the whole repo (after installing deps to preserve cache)
COPY . .

# Build only the web app
RUN pnpm build --filter=admin
# RUN pnpm --filter web --prod deploy web

# ----------------------
# Runtime Stage
# ----------------------
    
FROM base AS web

WORKDIR /app

ENV PORT=3000

# Copy standalone build output from Next.js
COPY --from=build /repo/apps/admin/.next/standalone ./
COPY --from=build /repo/apps/admin/public ./public
COPY --from=build /repo/apps/admin/public ./apps/admin/public
COPY --from=build /repo/apps/admin/.next/static ./.next/static
COPY --from=build /repo/apps/admin/.next/static ./apps/admin/.next/static
COPY --from=build /repo/node_modules ./node_modules

EXPOSE 3000

CMD ["node", "apps/admin/server.js"]
