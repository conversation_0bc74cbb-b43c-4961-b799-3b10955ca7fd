import { NextResponse, type NextRequest } from "next/server";

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  // Check for session token in cookies or headers
  let sessionToken = request.cookies.get("auth_session")?.value;

  if (pathname.startsWith("/health")) {
    return NextResponse.next();
  }

  if (
    (!sessionToken && pathname.startsWith("/login")) ||
    pathname.startsWith("/register")
  )
    return NextResponse.next();

  if (
    !sessionToken &&
    !pathname.startsWith("/login") &&
    !pathname.startsWith("/register")
  )
    return NextResponse.redirect(new URL("/login", request.url));

  if (sessionToken && pathname.startsWith("/dashboard")) return response;
  if (
    sessionToken &&
    (pathname.startsWith("/login") || pathname.startsWith("/register"))
  )
    return NextResponse.redirect(new URL("/dashboard", request.url));

  return NextResponse.next();
}

export const config = {
  matcher: [
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};
