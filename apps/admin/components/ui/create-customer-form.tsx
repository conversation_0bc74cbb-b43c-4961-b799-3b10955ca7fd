"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
  DialogDescription,
  DialogClose,
} from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Loader2, UserPlus, Mail, Phone, MapPin } from "lucide-react";
import { toast } from "sonner";
import { customerService } from "@/lib/logistics/operations/customers";
import { useAppSelector } from "@/store/hooks";

interface CreateCustomerFormProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

interface CustomerFormData {
  name: string;
  email: string;
  phone: string;
  location: string;
  status: "ACTIVE" | "INACTIVE" | "PENDING";
}

interface CustomerFormErrors {
  name?: string;
  email?: string;
  phone?: string;
  location?: string;
  status?: string;
}

const InputField = ({
  id,
  label,
  icon: Icon,
  value,
  onChange,
  error,
  placeholder,
  type = "text",
  required = false,
}: {
  id: string;
  label: string;
  icon?: React.ElementType;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  error?: string;
  placeholder?: string;
  type?: string;
  required?: boolean;
}) => (
  <div>
    <Label htmlFor={id} className="block text-sm font-medium text-gray-700 mb-1">
      {label} {required && <span className="text-red-500">*</span>}
    </Label>
    <div className="relative">
      {Icon && (
        <Icon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
      )}
      <Input
        id={id}
        type={type}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        className={`${Icon ? "pl-10" : ""} ${
          error ? "border-red-300 focus:border-red-500" : ""
        }`}
        required={required}
      />
    </div>
    {error && <p className="text-sm text-red-600 mt-1">{error}</p>}
  </div>
);

export const CreateCustomerForm: React.FC<CreateCustomerFormProps> = ({
  isOpen,
  onOpenChange,
  onSuccess,
}) => {
  const { user: authUser } = useAppSelector((state) => state.auth);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<CustomerFormData>({
    name: "",
    email: "",
    phone: "",
    location: "",
    status: "ACTIVE",
  });
  const [formErrors, setFormErrors] = useState<CustomerFormErrors>({});

  const resetForm = () => {
    setFormData({
      name: "",
      email: "",
      phone: "",
      location: "",
      status: "ACTIVE",
    });
    setFormErrors({});
  };

  const handleInputChange = (field: keyof CustomerFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const errors: CustomerFormErrors = {};

    if (!formData.name.trim()) {
      errors.name = "Customer name is required";
    }

    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = "Please enter a valid email address";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleCreateCustomer = async () => {
    if (!authUser || !validateForm()) return;

    setLoading(true);
    setFormErrors({});

    try {
      const customerData = {
        ...formData,
        email: formData.email || null,
        phone: formData.phone || null,
        location: formData.location || null,
      };

      const result = await customerService.createCustomer(customerData);

      if (result.success) {
        toast.success("Customer Created Successfully", {
          description: `Customer ${formData.name} has been created.`,
        });
        onOpenChange(false);
        resetForm();
        onSuccess();
      } else {
        setFormErrors({
          name: result.error || "Failed to create customer",
        });
        toast.error("Error Creating Customer", {
          description: result.error || "Failed to create customer. Please try again.",
        });
      }
    } catch (error: any) {
      const errorMessage = error.message || "An unexpected error occurred";
      setFormErrors({
        name: errorMessage,
      });
      toast.error("Failed to Create Customer", {
        description: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleCreateCustomer();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[540px] p-0 overflow-hidden">
        <div className="flex flex-col h-full">
          <DialogHeader className="p-6 pb-3 bg-primary/5 border-b">
            <DialogTitle className="text-xl text-primary font-semibold flex items-center gap-2">
              <UserPlus size={20} />
              Add New Customer
            </DialogTitle>
            <DialogDescription className="text-sm pt-1.5">
              Enter the customer details to create a new account.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-5 p-6 max-h-[70vh] overflow-y-auto">
              <InputField
                id="name"
                label="Contact Name"
                icon={UserPlus}
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                error={formErrors.name}
                placeholder="Enter primary contact name"
                required
              />
              
              <InputField
                id="email"
                label="Email Address"
                icon={Mail}
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                error={formErrors.email}
                placeholder="<EMAIL>"
              />
              
              <InputField
                id="phone"
                label="Phone Number"
                icon={Phone}
                value={formData.phone}
                onChange={(e) => handleInputChange("phone", e.target.value)}
                error={formErrors.phone}
                placeholder="+****************"
              />
              
              <InputField
                id="location"
                label="Location"
                icon={MapPin}
                value={formData.location}
                onChange={(e) => handleInputChange("location", e.target.value)}
                error={formErrors.location}
                placeholder="City, Country"
              />

              <div>
                <Label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </Label>
                <Select
                  value={formData.status}
                  onValueChange={(value: "ACTIVE" | "INACTIVE" | "PENDING") =>
                    handleInputChange("status", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ACTIVE">Active</SelectItem>
                    <SelectItem value="INACTIVE">Inactive</SelectItem>
                    <SelectItem value="PENDING">Pending</SelectItem>
                  </SelectContent>
                </Select>
                {formErrors.status && (
                  <p className="text-sm text-red-600 mt-1">{formErrors.status}</p>
                )}
              </div>
            </div>
            
            <div className="flex justify-end gap-3 p-6 pt-0 border-t bg-gray-50/50">
              <DialogClose asChild>
                <Button
                  type="button"
                  variant="outline"
                  disabled={loading}
                >
                  Cancel
                </Button>
              </DialogClose>
              <Button
                type="submit"
                disabled={loading}
                className="bg-primary hover:bg-primary/90"
              >
                {loading ? (
                  <>
                    <Loader2 size={16} className="animate-spin mr-2" />
                    Creating...
                  </>
                ) : (
                  <>
                    <UserPlus size={16} className="mr-2" />
                    Create Customer
                  </>
                )}
              </Button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
};
