/**
 * Document Toolbar Component
 *
 * A reusable toolbar component for document quick actions including
 * view, download, print, and share functionality.
 */

"use client";

import React, { useState } from "react";
import {
  Eye,
  Download,
  Printer,
  Share2,
  MessageCircle,
  Mail,
  Copy,
  ExternalLink,
  Loader2,
  CheckCircle,
  AlertCircle,
} from "lucide-react";

export interface DocumentToolbarProps {
  document?: {
    fileName: string;
    documentNumber: string;
    releaseCode?: string;
    downloadUrl?: string;
    pdfBlob?: Blob;
  };
  onView?: () => void;
  onDownload?: () => void;
  onPrint?: () => void;
  onShare?: (method: "whatsapp" | "email" | "copy") => void;
  isLoading?: boolean;
  className?: string;
  size?: "sm" | "md" | "lg";
  variant?: "default" | "compact" | "minimal";
}

export function DocumentToolbar({
  document,
  onView,
  onDownload,
  onPrint,
  onShare,
  isLoading = false,
  className = "",
  size = "md",
  variant = "default",
}: DocumentToolbarProps) {
  const [copySuccess, setCopySuccess] = useState(false);
  const [shareLoading, setShareLoading] = useState<string | null>(null);

  // Size configurations
  const sizeConfig = {
    sm: {
      button: "py-1 px-2 text-xs",
      icon: "h-3 w-3",
      gap: "gap-1",
    },
    md: {
      button: "py-2 px-4 text-sm",
      icon: "h-4 w-4",
      gap: "gap-2",
    },
    lg: {
      button: "py-3 px-6 text-base",
      icon: "h-5 w-5",
      gap: "gap-3",
    },
  };

  const config = sizeConfig[size];

  // Handle copy to clipboard
  const handleCopy = async () => {
    if (!document) return;

    try {
      const text = `Release Authorization Document\n\nDocument: ${document.fileName}\nDocument Number: ${document.documentNumber}${document.releaseCode ? `\nRelease Code: ${document.releaseCode}` : ""}\n\nGenerated by Shamwaa Logistics`;

      await navigator.clipboard.writeText(text);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);

      if (onShare) {
        onShare("copy");
      }
    } catch (error) {
      console.error("Failed to copy to clipboard:", error);
    }
  };

  // Handle WhatsApp share
  const handleWhatsAppShare = async () => {
    if (!document) return;

    setShareLoading("whatsapp");
    try {
      const message = `Release Authorization Document Generated\n\nDocument: ${document.fileName}\nDocument Number: ${document.documentNumber}${document.releaseCode ? `\nRelease Code: ${document.releaseCode}` : ""}\n\nPlease download the PDF document for cargo release authorization.`;
      const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
      window.open(whatsappUrl, "_blank");

      if (onShare) {
        onShare("whatsapp");
      }
    } finally {
      setShareLoading(null);
    }
  };

  // Handle email share
  const handleEmailShare = async () => {
    if (!document) return;

    setShareLoading("email");
    try {
      const subject = `Release Authorization - ${document.documentNumber}`;
      const body = `Dear Team,\n\nA release authorization document has been generated:\n\nDocument: ${document.fileName}\nDocument Number: ${document.documentNumber}${document.releaseCode ? `\nRelease Code: ${document.releaseCode}` : ""}\n\nPlease download the attached PDF document for cargo release authorization.\n\nBest regards,\nShamwaa Logistics`;
      const mailtoUrl = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
      window.open(mailtoUrl);

      if (onShare) {
        onShare("email");
      }
    } finally {
      setShareLoading(null);
    }
  };

  // Handle external link
  const handleExternalLink = () => {
    if (document?.downloadUrl) {
      window.open(document.downloadUrl, "_blank");
    }
  };

  // Render button based on variant
  const renderButton = (
    icon: React.ReactNode,
    label: string,
    onClick: () => void,
    disabled = false,
    loading = false,
    variant_style = "border-gray-300 text-gray-700 hover:bg-gray-50"
  ) => {
    const baseClasses = `flex items-center justify-center ${config.button} border rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed`;

    if (variant === "minimal") {
      return (
        <button
          onClick={onClick}
          disabled={disabled || loading}
          className={`${config.button} text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors disabled:opacity-50`}
          title={label}
        >
          {loading ? (
            <Loader2 className={`${config.icon} animate-spin`} />
          ) : (
            icon
          )}
        </button>
      );
    }

    if (variant === "compact") {
      return (
        <button
          onClick={onClick}
          disabled={disabled || loading}
          className={`${baseClasses} ${variant_style}`}
          title={label}
        >
          {loading ? (
            <Loader2 className={`${config.icon} animate-spin`} />
          ) : (
            icon
          )}
        </button>
      );
    }

    return (
      <button
        onClick={onClick}
        disabled={disabled || loading}
        className={`${baseClasses} ${config.gap} ${variant_style}`}
      >
        {loading ? <Loader2 className={`${config.icon} animate-spin`} /> : icon}
        <span>{label}</span>
      </button>
    );
  };

  if (!document && !isLoading) {
    return null;
  }

  return (
    <div className={`flex items-center ${config.gap} ${className}`}>
      {isLoading ? (
        <div className="flex items-center gap-2 text-gray-500">
          <Loader2 className={`${config.icon} animate-spin`} />
          <span
            className={
              config.button.includes("text-xs") ? "text-xs" : "text-sm"
            }
          >
            Preparing document...
          </span>
        </div>
      ) : (
        <>
          {/* Download Button */}
          {onDownload &&
            renderButton(
              <Download className={config.icon} />,
              "Download",
              onDownload,
              false,
              false,
              "border-primary-300 text-primary-700 hover:bg-primary-50"
            )}

          {/* External Link Button */}
          {document?.downloadUrl &&
            renderButton(
              <ExternalLink className={config.icon} />,
              "Open",
              handleExternalLink,
              false,
              false,
              "border-purple-300 text-purple-700 hover:bg-purple-50"
            )}

          {/* Share Dropdown or Buttons */}
          {variant === "default" && (
            <div className="flex items-center gap-1">
              {/* WhatsApp Share */}
              {renderButton(
                <MessageCircle className={config.icon} />,
                "WhatsApp",
                handleWhatsAppShare,
                false,
                shareLoading === "whatsapp",
                "border-green-300 text-green-700 hover:bg-green-50"
              )}

              {/* Email Share */}
              {renderButton(
                <Mail className={config.icon} />,
                "Email",
                handleEmailShare,
                false,
                shareLoading === "email",
                "border-blue-300 text-blue-700 hover:bg-blue-50"
              )}
            </div>
          )}

          {/* Compact Share Button */}
          {variant === "compact" && (
            <div className="relative group">
              {renderButton(
                <Share2 className={config.icon} />,
                "Share",
                () => {},
                false,
                false,
                "border-blue-300 text-blue-700 hover:bg-blue-50"
              )}

              {/* Dropdown Menu */}
              <div className="absolute top-full right-0 mt-1 w-48 bg-white border border-gray-200 rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
                <div className="py-1">
                  <button
                    onClick={handleWhatsAppShare}
                    className="flex items-center gap-2 w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
                    disabled={shareLoading === "whatsapp"}
                  >
                    {shareLoading === "whatsapp" ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <MessageCircle className="h-4 w-4" />
                    )}
                    WhatsApp
                  </button>
                  <button
                    onClick={handleEmailShare}
                    className="flex items-center gap-2 w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
                    disabled={shareLoading === "email"}
                  >
                    {shareLoading === "email" ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Mail className="h-4 w-4" />
                    )}
                    Email
                  </button>
                  <button
                    onClick={handleCopy}
                    className="flex items-center gap-2 w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
                    disabled={copySuccess}
                  >
                    {copySuccess ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                    {copySuccess ? "Copied!" : "Copy Details"}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Minimal Share Buttons */}
          {variant === "minimal" && (
            <>
              {renderButton(
                <MessageCircle className={config.icon} />,
                "WhatsApp",
                handleWhatsAppShare,
                false,
                shareLoading === "whatsapp"
              )}
              {renderButton(
                <Mail className={config.icon} />,
                "Email",
                handleEmailShare,
                false,
                shareLoading === "email"
              )}
              {renderButton(
                copySuccess ? (
                  <CheckCircle className={config.icon} />
                ) : (
                  <Copy className={config.icon} />
                ),
                "Copy",
                handleCopy,
                copySuccess
              )}
            </>
          )}
        </>
      )}
    </div>
  );
}

// Hook for easier toolbar management
export function useDocumentToolbar(
  document?: DocumentToolbarProps["document"]
) {
  const [isLoading, setIsLoading] = useState(false);

  const handleAction = async (action: () => Promise<void> | void) => {
    setIsLoading(true);
    try {
      await action();
    } catch (error) {
      console.error("Toolbar action failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    handleAction,
    document,
  };
}
