"use client";

import { useState, useEffect, use<PERSON>emo, useCallback } from "react";
import { motion } from "framer-motion";
import {
  X,
  Calendar,
  Tag,
  RefreshCw,
  Search,
  Check,
  Filter,
} from "lucide-react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";

import { Badge } from "@workspace/ui/components/badge";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@workspace/ui/components/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";

// Utility functions for dynamic filtering
const inferFilterType = (dataType?: string): FilterConfig["type"] => {
  switch (dataType) {
    case "number":
      return "number";
    case "date":
      return "date";
    case "boolean":
      return "boolean";
    case "enum":
      return "text"; // Convert enum to text input
    default:
      return "text";
  }
};

const extractColumnsFromData = (
  data: Record<string, any>[]
): FilterConfig[] => {
  if (data.length === 0) return [];

  const firstRow = data[0];
  if (!firstRow) return [];

  return Object.keys(firstRow).map((key) => {
    const value = firstRow[key];
    const dataType = inferDataType(value);

    return {
      key,
      label: key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, " "),
      type: inferFilterType(dataType),
      searchable: true,
      dataType,
    };
  });
};

const inferDataType = (
  value: any
): "string" | "number" | "date" | "boolean" | "enum" => {
  if (value === null || value === undefined) return "string";
  if (typeof value === "number") return "number";
  if (typeof value === "boolean") return "boolean";
  if (value instanceof Date) return "date";
  if (typeof value === "string") {
    // Try to detect date strings
    if (/^\d{4}-\d{2}-\d{2}/.test(value)) return "date";
    // Try to detect numbers
    if (!isNaN(Number(value))) return "number";
  }
  return "string";
};

export interface FilterConfig {
  key: string;
  label: string;
  type: "date" | "text" | "tags" | "number" | "boolean";
  placeholder?: string;
  defaultValue?: string | string[];
  searchable?: boolean; // Whether this column can be used in breadcrumb search
  dataType?: "string" | "number" | "date" | "boolean" | "enum"; // For dynamic type inference
  getValues?: () => Promise<string[]> | string[]; // Dynamic value fetcher
}

export interface FilterState {
  [key: string]: string | string[] | null;
}

export interface ColumnFilter {
  column: string;
  value: string;
  label: string;
}

export interface TableColumn {
  key: string;
  label: string;
  type?: "string" | "number" | "date" | "boolean" | "enum";
  searchable?: boolean;
}

export interface DynamicFilterProps {
  tableData?: Record<string, any>[]; // Raw table data for value extraction
  columns?: TableColumn[]; // Table column definitions
  onValueSearch?: (
    column: string,
    query: string
  ) => Promise<string[]> | string[]; // Custom value search
}

// Autocomplete option interface
export interface AutocompleteOption {
  id: string;
  label: string;
  description?: string;
  metadata?: Record<string, any>;
}

interface FilterPanelProps extends DynamicFilterProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  filters?: FilterConfig[]; // Made optional for dynamic mode
  onRefresh?: () => void;
  loading?: boolean;
  className?: string;
  popularTags?: string[];
  onTagSelect?: (tag: string) => void;
  columnFilters?: ColumnFilter[];
  onColumnFilterAdd?: (filter: ColumnFilter) => void;
  onColumnFilterRemove?: (index: number) => void;
  // Dynamic mode props
  enableDynamicFilters?: boolean; // Enable auto-detection from table data
  defaultFilterColumn?: string; // Default column to select (defaults to "name")
  autoSelectDefaultColumn?: boolean; // Whether to auto-select default column (defaults to true)
  // Autocomplete props
  enableAutocomplete?: boolean;
  autocompleteLabel?: string;
  autocompletePlaceholder?: string;
  autocompleteOptions?: AutocompleteOption[];
  selectedAutocompleteId?: string;
  onAutocompleteSelect?: (option: AutocompleteOption) => void;
  autocompleteLoading?: boolean;
  onAutocompleteSearch?: (query: string) => void;
  // Custom action buttons
  customActions?: React.ReactNode;
  // Bulk action support
  bulkActions?: React.ReactNode;
  selectedCount?: number;
  showBulkActions?: boolean;
  onClearSelections?: () => void;
}

// Autocomplete Field Component
interface AutocompleteFieldProps {
  placeholder: string;
  options: AutocompleteOption[];
  selectedId?: string;
  onSelect?: (option: AutocompleteOption) => void;
  loading?: boolean;
  onSearch?: (query: string) => void;
}

function AutocompleteField({
  placeholder,
  options,
  selectedId,
  onSelect,
  loading = false,
  onSearch,
}: AutocompleteFieldProps) {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const selectedOption = options.find((opt) => opt.id === selectedId);

  const filteredOptions = useMemo(() => {
    if (!searchQuery) return options;
    return options.filter(
      (option) =>
        option.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
        option.description?.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [options, searchQuery]);

  const handleSearch = useCallback(
    (query: string) => {
      setSearchQuery(query);
      onSearch?.(query);
    },
    [onSearch]
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
        >
          {selectedOption ? selectedOption.label : placeholder}
          <Search className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0">
        <Command>
          <CommandInput
            placeholder={`Search ${placeholder.toLowerCase()}...`}
            value={searchQuery}
            onValueChange={handleSearch}
          />
          <CommandList>
            {loading ? (
              <CommandEmpty>Loading...</CommandEmpty>
            ) : filteredOptions.length === 0 ? (
              <CommandEmpty>No options found.</CommandEmpty>
            ) : (
              <CommandGroup>
                {filteredOptions.map((option) => (
                  <CommandItem
                    key={option.id}
                    value={option.id}
                    onSelect={() => {
                      onSelect?.(option);
                      setOpen(false);
                    }}
                  >
                    <Check
                      className={`mr-2 h-4 w-4 ${
                        selectedId === option.id ? "opacity-100" : "opacity-0"
                      }`}
                    />
                    <div className="flex flex-col">
                      <span className="font-medium">{option.label}</span>
                      {option.description && (
                        <span className="text-xs text-gray-500">
                          {option.description}
                        </span>
                      )}
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

export function FilterPanel({
  searchTerm,
  onSearchChange,
  filters = [],
  onRefresh,
  loading = false,
  className = "",
  popularTags = [],
  onTagSelect,
  columnFilters = [],
  onColumnFilterAdd,
  onColumnFilterRemove,
  // Autocomplete props
  enableAutocomplete = false,
  autocompleteLabel = "Select Entity",
  autocompletePlaceholder = "Search and select...",
  autocompleteOptions = [],
  selectedAutocompleteId,
  onAutocompleteSelect,
  autocompleteLoading = false,
  onAutocompleteSearch,
  // Dynamic props
  tableData = [],
  columns = [],
  onValueSearch,
  enableDynamicFilters = false,
  defaultFilterColumn = "name",
  autoSelectDefaultColumn = true,
  // Custom actions
  customActions,
  // Bulk actions
  bulkActions,
  selectedCount = 0,
  showBulkActions = false,
  onClearSelections,
}: FilterPanelProps) {
  const [selectedColumn, setSelectedColumn] = useState<string>("");
  const [columnValue, setColumnValue] = useState<string>("");
  const [dynamicValues, setDynamicValues] = useState<string[]>([]);
  const [originalDynamicValues, setOriginalDynamicValues] = useState<string[]>(
    []
  );
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(
    null
  );

  // Generate dynamic filters from table data or columns (memoized for performance)
  const dynamicFilters = useMemo((): FilterConfig[] => {
    if (!enableDynamicFilters) return [];

    if (columns.length > 0) {
      return columns
        .filter((col) => col.searchable !== false)
        .map((col) => ({
          key: col.key,
          label: col.label,
          type: inferFilterType(col.type),
          searchable: true,
          dataType: col.type,
        }));
    }

    if (tableData.length > 0) {
      return extractColumnsFromData(tableData);
    }

    return [];
  }, [enableDynamicFilters, columns, tableData]);

  // Combine static and dynamic filters with deduplication (memoized for performance)
  const allFilters = useMemo(() => {
    const combined = [...filters, ...dynamicFilters];
    // Remove duplicates based on key, keeping the first occurrence
    const seen = new Set<string>();
    return combined.filter((filter) => {
      if (seen.has(filter.key)) {
        return false;
      }
      seen.add(filter.key);
      return true;
    });
  }, [filters, dynamicFilters]);

  // Filter searchable columns (memoized for performance)
  const searchableFilters = useMemo(
    () => allFilters.filter((f) => f.searchable !== false),
    [allFilters]
  );

  // Filter dynamic values based on current input (memoized for performance)
  // Use original values to ensure filtering is based on complete dataset
  const filteredDynamicValues = useMemo(
    () =>
      originalDynamicValues
        .filter((val) => val.toLowerCase().includes(columnValue.toLowerCase()))
        .slice(0, 10),
    [originalDynamicValues, columnValue]
  );

  // Extract unique values for selected column (memoized for performance)
  const extractValuesForColumn = useCallback(
    async (columnKey: string): Promise<string[]> => {
      if (onValueSearch) {
        return await onValueSearch(columnKey, "");
      }

      if (tableData.length > 0) {
        const values = tableData
          .map((row) => row[columnKey])
          .filter((value) => value != null && value !== "")
          .map((value) => String(value));

        return [...new Set(values)].sort();
      }

      return [];
    },
    [onValueSearch, tableData]
  );

  // Load dynamic values when column changes (memoized for performance)
  const handleColumnChange = useCallback(
    async (columnKey: string) => {
      setSelectedColumn(columnKey);
      setColumnValue("");

      if (columnKey) {
        try {
          const values = await extractValuesForColumn(columnKey);
          setDynamicValues(values);
          setOriginalDynamicValues(values); // Store original values for filtering
        } catch (error) {
          console.error("Error loading column values:", error);
          setDynamicValues([]);
          setOriginalDynamicValues([]);
        }
      } else {
        setDynamicValues([]);
        setOriginalDynamicValues([]);
      }
    },
    [extractValuesForColumn]
  );

  // Set default column when filters are available and auto-select is enabled
  useEffect(() => {
    if (autoSelectDefaultColumn && !selectedColumn && allFilters.length > 0) {
      const defaultColumn = allFilters.find(
        (f) => f.key === defaultFilterColumn && f.searchable !== false
      );
      if (defaultColumn) {
        setSelectedColumn(defaultFilterColumn);
        // Trigger value loading for the default column
        handleColumnChange(defaultFilterColumn);
      } else {
        // Fallback to first searchable column if default column doesn't exist
        const firstSearchableColumn = allFilters.find(
          (f) => f.searchable !== false
        );
        if (firstSearchableColumn) {
          setSelectedColumn(firstSearchableColumn.key);
          handleColumnChange(firstSearchableColumn.key);
        }
      }
    }
  }, [
    allFilters,
    selectedColumn,
    autoSelectDefaultColumn,
    defaultFilterColumn,
    handleColumnChange,
  ]);

  // Simple filter addition on blur
  const applyFilter = useCallback(
    (column: string, value: string) => {
      if (!onColumnFilterAdd || !onColumnFilterRemove) return;

      if (value.trim()) {
        // Check if filter already exists for this column (with safety check)
        const existingFilterIndex = Array.isArray(columnFilters)
          ? columnFilters.findIndex((f) => f.column === column)
          : -1;

        if (existingFilterIndex >= 0) {
          // Update existing filter by removing and adding new one
          onColumnFilterRemove(existingFilterIndex);
        }

        // Find the column label
        const columnConfig = allFilters.find((f) => f.key === column);
        const label = columnConfig?.label || column;

        // Add new filter
        onColumnFilterAdd({ column, value: value.trim(), label });
      } else {
        // Remove filter if value is empty - this is the critical path for reset (with safety check)
        const existingFilterIndex = Array.isArray(columnFilters)
          ? columnFilters.findIndex((f) => f.column === column)
          : -1;
        if (existingFilterIndex >= 0) {
          // Force removal of the filter to reset data
          onColumnFilterRemove(existingFilterIndex);
        }
      }
    },
    [onColumnFilterAdd, onColumnFilterRemove, columnFilters, allFilters]
  );

  // Debounced real-time filter application (200ms)
  const applyFilterDebounced = useCallback(
    (column: string, value: string) => {
      // Clear existing timer
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }

      // Set new timer for 200ms debounce
      const timer = setTimeout(() => {
        applyFilter(column, value);
      }, 200);

      setDebounceTimer(timer);
    },
    [debounceTimer, applyFilter]
  );

  // Handle real-time column value changes with debounced filtering
  const handleColumnValueChange = useCallback(
    (value: string) => {
      setColumnValue(value);

      // Apply debounced real-time filtering
      if (selectedColumn) {
        // If value is empty, clear any pending debounced timer and apply filter immediately to remove it
        // Otherwise use debounced filtering
        if (value.trim() === "") {
          // Clear any pending debounced timer
          if (debounceTimer) {
            clearTimeout(debounceTimer);
            setDebounceTimer(null);
          }
          // Apply filter immediately to remove it
          applyFilter(selectedColumn, value);
        } else {
          applyFilterDebounced(selectedColumn, value);
        }
      }
    },
    [selectedColumn, applyFilterDebounced, applyFilter, debounceTimer]
  );

  // Cleanup debounce timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
    };
  }, [debounceTimer]);

  return (
    <div
      className={`bg-white rounded-xl border shadow-sm p-1.5 space-y-4 ${className}`}
    >
      {/* Main Search Input */}
      <div className="flex items-center gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search across all fields..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10 pr-8 border border-gray-200 focus:ring-200 focus:border-primary"
          />
          {searchTerm && (
            <button
              onClick={() => onSearchChange("")}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
        {/* Bulk Actions */}
        {showBulkActions && selectedCount > 0 && bulkActions && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{
              duration: 0.3,
              mode: "easeIn",
              staggerChildren: 0.3,
              delayChildren: 0.6,
            }}
            className="flex items-center justify-between space-x-2"
          >
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.4 }}
              className="flex flex-row items-center text-sm font-medium text-primary min-w-28 text-center px-4"
            >
              <X
                className="h-5 w-5 mr-2 text-gray-400 hover:text-primary hover:bg-primary/10 rounded-full bg-gray-100 p-1 cursor-pointer transition-colors"
                onClick={() => onClearSelections?.()}
              />
              <span className="mr-1">
                {selectedCount} Item{selectedCount !== 1 ? "s" : ""}
              </span>
              <span className="font-light">selected</span>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{
                duration: 0.5,
                mode: "easeIn",
                staggerChildren: 0.4,
                delayChildren: 0.8,
              }}
              className="flex items-center gap-2"
            >
              {bulkActions}
            </motion.div>
          </motion.div>
        )}
        {onRefresh && (
          <Button
            variant="outline"
            size="sm"
            onClick={onRefresh}
            disabled={loading}
            className="shrink-0"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? "animate-spin" : ""}`} />
          </Button>
        )}
        {customActions}
      </div>

      {/* Popular Tags */}
      {popularTags.length > 0 && onTagSelect && (
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Tag className="h-4 w-4" />
            Popular Tags:
          </div>
          <div className="flex flex-wrap gap-2">
            {popularTags.slice(0, 8).map((tag) => (
              <Badge
                key={tag}
                variant="outline"
                className="cursor-pointer hover:bg-primary/10 hover:border-primary/30 transition-colors"
                onClick={() => onTagSelect(tag)}
              >
                {tag}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Active Search Display */}
      {searchTerm && (
        <div className="flex flex-wrap gap-2 pt-2 border-t">
          <Badge variant="secondary" className="gap-1">
            Search: "{searchTerm}"
            <button
              onClick={() => onSearchChange("")}
              className="ml-1 hover:bg-gray-300 rounded-full p-0.5"
            >
              <X className="h-3 w-3" />
            </button>
          </Badge>
        </div>
      )}
    </div>
  );
}
