"use client";

import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import dynamic from "next/dynamic";

const DocViewer = dynamic(() => import("react-doc-viewer"), {
  ssr: false,
});
import { DocViewerRenderers } from "react-doc-viewer";

import {
  X,
  Download,
  Printer,
  Maximize2,
  Minimize2,
  Loader2,
  AlertCircle,
  FileText,
} from "lucide-react";

export interface DocumentPreviewProps {
  isOpen: boolean;
  onClose: () => void;
  document: {
    uri: string;
    fileName: string;
    fileType?: string;
  } | null;
  title?: string;
  onDownload?: () => void;
  onPrint?: () => void;
  className?: string;
}

export function DocumentPreviewDialog({
  isOpen,
  onClose,
  document,
  title,
  onDownload,
  onPrint,
  className = "",
}: DocumentPreviewProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [zoom, setZoom] = useState(1);

  // Reset state when dialog opens/closes
  useEffect(() => {
    if (isOpen) {
      setIsLoading(true);
      setError(null);
      setZoom(1);
      setIsFullscreen(false);
    }
  }, [isOpen, document]);

  const handleResetZoom = () => setZoom(1);

  // Handle fullscreen toggle
  const toggleFullscreen = () => setIsFullscreen((prev) => !prev);

  // Handle print
  const handlePrint = () => {
    if (onPrint) {
      onPrint();
    } else {
      window.print();
    }
  };

  // Handle download
  const handleDownload = () => {
    if (onDownload) {
      onDownload();
    } else if (document) {
      // Fallback download
      const link = document.createElement("a") as HTMLAnchorElement;
      link.href = document.uri;
      link.download = document.fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // Prepare document for viewer
  const docs = document
    ? [
        {
          uri: document.uri,
          fileName: document.fileName,
          fileType: document.fileType || document.fileName.split(".").pop(),
        },
      ]
    : [];

  if (!isOpen || !document) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black/60 backdrop-blur-sm"
          onClick={onClose}
        />

        {/* Dialog */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className={`relative bg-white rounded-lg shadow-2xl overflow-hidden ${
            isFullscreen
              ? "w-screen h-screen rounded-none"
              : "w-full max-w-6xl h-[90vh] mx-4"
          } ${className}`}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
            <div className="flex items-center gap-3">
              <FileText className="h-5 w-5 text-gray-600" />
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {title || "Document Preview"}
                </h3>
                <p className="text-sm text-gray-600">{document.fileName}</p>
              </div>
            </div>

            {/* Toolbar */}
            <div className="flex items-center gap-2">
              {/* Action Buttons */}
              <button
                onClick={handleDownload}
                className="p-2 hover:bg-gray-100 rounded-md transition-colors"
                title="Download"
              >
                <Download className="h-4 w-4" />
              </button>

              <button
                onClick={handlePrint}
                className="p-2 hover:bg-gray-100 rounded-md transition-colors"
                title="Print"
              >
                <Printer className="h-4 w-4" />
              </button>

              <button
                onClick={toggleFullscreen}
                className="p-2 hover:bg-gray-100 rounded-md transition-colors"
                title={isFullscreen ? "Exit Fullscreen" : "Fullscreen"}
              >
                {isFullscreen ? (
                  <Minimize2 className="h-4 w-4" />
                ) : (
                  <Maximize2 className="h-4 w-4" />
                )}
              </button>

              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 rounded-md transition-colors"
                title="Close"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>

          {/* Document Viewer */}
          <div className="flex-1 relative overflow-hidden">
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
                <div className="flex flex-col items-center gap-3">
                  <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
                  <p className="text-sm text-gray-600">Loading document...</p>
                </div>
              </div>
            )}

            {error && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
                <div className="flex flex-col items-center gap-3 text-center max-w-md">
                  <AlertCircle className="h-12 w-12 text-red-500" />
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-2">
                      Unable to Preview Document
                    </h4>
                    <p className="text-sm text-gray-600 mb-4">{error}</p>
                    <button
                      onClick={handleDownload}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                    >
                      Download Document
                    </button>
                  </div>
                </div>
              </div>
            )}

            {!error && (
              <div
                className="h-full w-full"
                style={{
                  transform: `scale(${zoom})`,
                  transformOrigin: "top left",
                  transition: "transform 0.2s ease",
                }}
              >
                <DocViewer
                  documents={docs}
                  pluginRenderers={DocViewerRenderers}
                  config={{
                    header: {
                      disableHeader: true,
                    },
                  }}
                  style={{
                    height: "77vh",
                    width: "100%",
                  }}
                />
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between p-3 border-t border-gray-200 bg-gray-50">
            <div className="text-sm text-gray-600">
              {document.fileType?.toUpperCase() || "PDF"} • {document.fileName}
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={handleResetZoom}
                className="text-sm text-blue-600 hover:text-blue-700 transition-colors"
              >
                Reset Zoom
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
}

// Hook for easier usage
export function useDocumentPreview() {
  const [isOpen, setIsOpen] = useState(false);
  const [document, setDocument] =
    useState<DocumentPreviewProps["document"]>(null);

  const openPreview = (doc: NonNullable<DocumentPreviewProps["document"]>) => {
    setDocument(doc);
    setIsOpen(true);
  };

  const closePreview = () => {
    setIsOpen(false);
    setDocument(null);
  };

  return {
    isOpen,
    document,
    openPreview,
    closePreview,
  };
}
