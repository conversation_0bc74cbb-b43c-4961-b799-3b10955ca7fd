"use client";

import React, { useState, useEffect } from "react";
import dynamic from "next/dynamic";

const DocViewer = dynamic(() => import("react-doc-viewer"), {
  ssr: false,
});
import { <PERSON><PERSON><PERSON><PERSON>, DocViewerRenderers } from "react-doc-viewer";

import {
  Download,
  Printer,
  Loader2,
  AlertCircle,
  FileText,
} from "lucide-react";

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";

export interface DocumentPreviewProps {
  isOpen: boolean;
  onClose: () => void;
  document: {
    uri: string;
    fileName: string;
    fileType?: string;
  } | null;
  title?: string;
  onDownload?: () => void;
  onPrint?: () => void;
  className?: string;
}

const CustomPDFRender: DocRenderer = ({ mainState: { currentDocument } }) => {
  if (!currentDocument) return null;

  return (
    <iframe
      width="100%"
      height="100%"
      id="pdf-file"
      src={currentDocument.fileData as string}
    />
  );
};

CustomPDFRender.fileTypes = ["pdf", "application/pdf"];
CustomPDFRender.weight = 1;

export function DocumentPreviewDialog({
  isOpen,
  onClose,
  document,
  title,
  onDownload,
  onPrint,
  className = "",
}: DocumentPreviewProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [zoom, setZoom] = useState(1);

  // Reset state when dialog opens/closes
  useEffect(() => {
    if (isOpen) {
      setIsLoading(true);
      setError(null);
      setZoom(1);
      setIsFullscreen(false);
    }
  }, [isOpen, document]);

  const handleResetZoom = () => setZoom(1);

  // Handle fullscreen toggle
  const toggleFullscreen = () => setIsFullscreen((prev) => !prev);

  // Handle print
  const handlePrint = () => {
    if (onPrint) {
      onPrint();
    } else if (document) {
      const preview = window.open("", "_blank");
      if (preview) {
        preview.document.write(`
          <html>
            <head>
              <title>Document Preview</title>
            </head>
            <body>
              <iframe src="${document.uri}" width="100%" height="100%"></iframe>
            </body>
          </html>
          `);
        preview.document.close();
        setTimeout(() => {
          preview.print();
        }, 200);
      }
    }
  };

  // Handle download
  const handleDownload = () => {
    if (onDownload) {
      onDownload();
    } else if (document) {
      // Fallback download
    }
  };

  // Prepare document for viewer
  const docs = document
    ? [
        {
          uri: document.uri,
          fileName: document.fileName,
          fileType: document.fileType || document.fileName.split(".").pop(),
        },
      ]
    : [];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className={`overflow-hidden p-0 ${
          isFullscreen
            ? "w-screen h-screen max-w-none rounded-none"
            : "w-full max-w-6xl h-[90vh]"
        } ${className}`}
      >
        {/* Header */}
        <DialogHeader className="flex-row items-center justify-between p-4 border-b border-gray-200 bg-gray-50 space-y-0">
          <div className="flex items-center gap-3">
            <FileText className="h-5 w-5 text-gray-600" />
            <div>
              <DialogTitle className="text-lg font-semibold text-gray-900">
                {title || "Document Preview"}
              </DialogTitle>
              <p className="text-sm text-gray-600">{document?.fileName}</p>
            </div>
          </div>
        </DialogHeader>

        {/* Document Viewer */}
        <div className="flex-1 relative overflow-hidden">
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
              <div className="flex flex-col items-center gap-3">
                <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
                <p className="text-sm text-gray-600">Loading document...</p>
              </div>
            </div>
          )}

          {error && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
              <div className="flex flex-col items-center gap-3 text-center max-w-md">
                <AlertCircle className="h-12 w-12 text-red-500" />
                <div>
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">
                    Unable to Preview Document
                  </h4>
                  <p className="text-sm text-gray-600 mb-4">{error}</p>
                  <Button
                    onClick={handleDownload}
                    className="bg-blue-600 text-white hover:bg-blue-700"
                  >
                    Download Document
                  </Button>
                </div>
              </div>
            </div>
          )}

          {!error && document && (
            <div
              className="h-full w-full"
              style={{
                transform: `scale(${zoom})`,
                transformOrigin: "top left",
                transition: "transform 0.2s ease",
              }}
            >
              <DocViewer
                documents={docs}
                pluginRenderers={[CustomPDFRender, ...DocViewerRenderers]}
                config={{
                  header: {
                    disableHeader: true,
                  },
                }}
                style={{
                  height: "75vh",
                  width: "100%",
                }}
              />
            </div>
          )}
        </div>

        {/* Footer */}
        <DialogFooter className="flex items-center justify-between p-3 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600">
            {document?.fileType?.toUpperCase() || "PDF"} • {document?.fileName}
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleResetZoom}
              className="text-sm text-blue-600 hover:text-blue-700"
            >
              Reset Zoom
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// Hook for easier usage
export function useDocumentPreview() {
  const [isOpen, setIsOpen] = useState(false);
  const [document, setDocument] =
    useState<DocumentPreviewProps["document"]>(null);

  const openPreview = (doc: NonNullable<DocumentPreviewProps["document"]>) => {
    setDocument(doc);
    setIsOpen(true);
  };

  const closePreview = () => {
    setIsOpen(false);
    setDocument(null);
  };

  return {
    isOpen,
    document,
    openPreview,
    closePreview,
  };
}
