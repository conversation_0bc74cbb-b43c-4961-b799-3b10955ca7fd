"use client";

import { motion } from "framer-motion";
import { ShieldBan, LogOut, Mail } from "lucide-react";
import { Button } from "@workspace/ui/components/button";
import { useAuth } from "@/components/providers/AuthProvider";

interface AccessDeniedPlaceholderProps {
  title?: string;
  message?: string;
  supportEmail?: string;
  showLogout?: boolean;
  showMailSupport?: boolean;
}

export function AccessDeniedPlaceholder({
  title = "Access Denied",
  message = "You don't have permission to access this system. Please contact support or try logging in with a different account.",
  supportEmail = "<EMAIL>",
  showLogout = true,
  showMailSupport = true,
}: AccessDeniedPlaceholderProps) {
  const { signOut } = useAuth();

  const handleMailSupport = () => {
    const subject = encodeURIComponent("Access Request - Shamwaa Logistics");
    const body = encodeURIComponent(
      "Hello Support Team,\n\nI am requesting access to the Shamwaa Logistics system. Please review my account permissions.\n\nThank you."
    );
    window.open(`mailto:${supportEmail}?subject=${subject}&body=${body}`);
  };

  return (
    <div className="flex items-center justify-center min-h-screen p-6 bg-background-secondary">
      <motion.div
        className="w-full max-w-md mx-auto"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
          type: "spring",
          stiffness: 300,
          damping: 30,
        }}
      >
        <div className="bg-card p-8 rounded-lg shadow-lg text-center border">
          {/* Icon */}
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="mb-6 flex justify-center"
          >
            <div className="p-4 rounded-full bg-red-50 dark:bg-red-900/20">
              <ShieldBan size={48} className="text-red-500" />
            </div>
          </motion.div>

          {/* Title and Message */}
          <motion.h1
            className="text-2xl font-bold mb-2 text-foreground"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            {title}
          </motion.h1>

          <motion.p
            className="text-muted-foreground mb-8"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.4 }}
          >
            {message}
          </motion.p>

          {/* Action Buttons */}
          {(showLogout || showMailSupport) && (
            <motion.div
              className="flex gap-3 justify-center"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5 }}
            >
              {showLogout && (
                <Button
                  variant="outline"
                  onClick={signOut}
                  className="flex items-center gap-2"
                >
                  <LogOut size={16} />
                  Logout
                </Button>
              )}
              
              {showMailSupport && (
                <Button
                  onClick={handleMailSupport}
                  className="flex items-center gap-2"
                >
                  <Mail size={16} />
                  Mail Support
                </Button>
              )}
            </motion.div>
          )}
        </div>
      </motion.div>
    </div>
  );
}
