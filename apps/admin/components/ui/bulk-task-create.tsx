"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Badge } from "@workspace/ui/components/badge";
import { Plus, Loader2, User, Flag, Tag, X, CheckSquare } from "lucide-react";
import {
  taskService,
  userService,
  type TaskInsert,
  type TaskPriorityEnum,
  type StatusEnum,
  type CategoryEnum,
} from "@/lib/logistics";
import { useAppSelector } from "@/store/hooks";
import { toast } from "sonner";

interface SelectedItem {
  id: string;
  name: string;
  identifier?: string; // tracking_number, code, etc.
}

interface BulkTaskCreateDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onTasksCreated?: () => void;
  selectedItems: SelectedItem[];
  associatedTable: string; // e.g., "cargos", "batches", etc.
  title?: string;
}

// Task priority options
const TASK_PRIORITY_OPTIONS = [
  { value: "LOW", label: "Low", icon: Flag },
  { value: "NORMAL", label: "Normal", icon: Flag },
  { value: "HIGH", label: "High", icon: Flag },
  { value: "URGENT", label: "Urgent", icon: Flag },
];

// Task status options
const TASK_STATUS_OPTIONS = [
  { value: "CREATED", label: "Created" },
  { value: "IN_PROGRESS", label: "In Progress" },
  { value: "COMPLETED", label: "Completed" },
  { value: "CANCELLED", label: "Cancelled" },
];

// Task category options
const TASK_CATEGORY_OPTIONS = [
  { value: "GENERAL", label: "General" },
  { value: "LOGISTICS", label: "Logistics" },
  { value: "FINANCE", label: "Finance" },
  { value: "CUSTOMER_SERVICE", label: "Customer Service" },
  { value: "OPERATIONS", label: "Operations" },
];

export function BulkTaskCreateDialog({
  isOpen,
  onClose,
  onTasksCreated,
  selectedItems,
  associatedTable,
  title = "Create Tasks for Selected Items",
}: BulkTaskCreateDialogProps) {
  const [isCreating, setIsCreating] = useState(false);
  const [users, setUsers] = useState<any[]>([]);
  const [usersLoading, setUsersLoading] = useState(false);
  const [usersError, setUsersError] = useState<string | null>(null);

  // Get current user from Redux store
  const authUser = useAppSelector((state) => state.auth.user);

  const [formData, setFormData] = useState<TaskInsert>({
    name: "",
    status: "CREATED" as StatusEnum,
    priority: "NORMAL" as TaskPriorityEnum,
    category: null,
    assignee: null,
    start: null,
    due: null,
    associated_table: associatedTable,
    associated_id: null,
    associated_ids: null, // Will be set to selected item IDs
  });

  // Fetch users for assignee selection
  useEffect(() => {
    const fetchUsers = async () => {
      if (!isOpen) return;

      setUsersLoading(true);
      setUsersError(null);

      try {
        const result = await userService.getAllUsersWithAccounts();
        if (result.success && result.data) {
          setUsers(result.data);
        } else {
          setUsersError(result.error || "Failed to load users");
        }
      } catch (error: any) {
        setUsersError(error.message || "Failed to load users");
      } finally {
        setUsersLoading(false);
      }
    };

    fetchUsers();
  }, [isOpen]);

  const handleInputChange = (field: keyof TaskInsert, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleRemoveItem = (_itemId: string) => {
    // This function could be used to remove items from selection
    // For now, we'll just show a toast since the parent component manages selection
    toast.info("To remove items, use the selection controls in the main table");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error("Task name is required");
      return;
    }

    if (selectedItems.length === 0) {
      toast.error("No items selected for task creation");
      return;
    }

    try {
      setIsCreating(true);

      // Create a single task with multiple associations
      const taskData: TaskInsert = {
        ...formData,
        associated_table: associatedTable,
        associated_ids: selectedItems.map((item) => item.id),
        // Keep associated_id as null since we're using associated_ids
        associated_id: null,
      };

      const result = await taskService.createTask(
        taskData,
        authUser?.accountId
      );

      if (result.success) {
        toast.success(
          `Successfully created task with ${selectedItems.length} associated ${associatedTable}`
        );

        if (onTasksCreated) {
          onTasksCreated();
        }

        handleClose();
      } else {
        toast.error(result.error || "Failed to create task");
      }
    } catch (error: any) {
      console.error("Error creating bulk task:", error);
      toast.error("Failed to create task");
    } finally {
      setIsCreating(false);
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      status: "CREATED" as StatusEnum,
      priority: "NORMAL" as TaskPriorityEnum,
      category: null,
      assignee: null,
      start: null,
      due: null,
      associated_table: associatedTable,
      associated_id: null,
      associated_ids: null,
    });
  };

  const handleClose = () => {
    onClose();
    resetForm();
  };

  // Format datetime for input
  const formatDateTimeForInput = (dateString: string | null | undefined) => {
    if (!dateString) return "";
    return new Date(dateString).toISOString().slice(0, 16);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CheckSquare className="h-5 w-5" />
            {title}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="flex-1 overflow-y-auto">
          <div className="space-y-6 p-1">
            {/* Selected Items Display */}
            <div>
              <Label className="text-sm font-medium text-gray-700 mb-2">
                Selected Items ({selectedItems.length})
              </Label>
              <div className="flex flex-wrap gap-2 p-3 bg-gray-50 rounded-lg max-h-32 overflow-y-auto">
                {selectedItems.map((item) => (
                  <Badge
                    key={item.id}
                    variant="secondary"
                    className="flex items-center gap-1 px-2 py-1"
                  >
                    <span className="text-xs">
                      {item.identifier || item.name}
                    </span>
                    <button
                      type="button"
                      onClick={() => handleRemoveItem(item.id)}
                      className="ml-1 hover:bg-gray-300 rounded-full p-0.5"
                    >
                      <X size={12} />
                    </button>
                  </Badge>
                ))}
              </div>
            </div>

            {/* Task Name */}
            <div>
              <Label
                htmlFor="name"
                className="text-sm font-medium text-gray-700 mb-2"
              >
                Task Name *
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder="Enter task name (will be suffixed with item name)"
                className="w-full"
                required
              />
            </div>

            {/* Status and Priority Row */}
            <div className="grid grid-cols-2 gap-4">
              {/* Status */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2">
                  Status
                </Label>
                <Select
                  value={formData.status || "CREATED"}
                  onValueChange={(value) =>
                    handleInputChange("status", value as StatusEnum)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {TASK_STATUS_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Priority */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2">
                  Priority
                </Label>
                <Select
                  value={formData.priority || "NORMAL"}
                  onValueChange={(value) =>
                    handleInputChange("priority", value as TaskPriorityEnum)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    {TASK_PRIORITY_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center gap-2">
                          <Flag size={14} />
                          {option.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Category and Assignee Row */}
            <div className="grid grid-cols-2 gap-4">
              {/* Category */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2">
                  Category
                </Label>
                <Select
                  value={formData.category || "none"}
                  onValueChange={(value) =>
                    handleInputChange(
                      "category",
                      value === "none" ? null : (value as CategoryEnum)
                    )
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">No Category</SelectItem>
                    {TASK_CATEGORY_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center gap-2">
                          <Tag size={14} />
                          {option.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Assignee */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2">
                  Assignee
                </Label>
                <Select
                  value={formData.assignee || "unassigned"}
                  onValueChange={(value) =>
                    handleInputChange(
                      "assignee",
                      value === "unassigned" ? null : value
                    )
                  }
                  disabled={usersLoading}
                >
                  <SelectTrigger>
                    <SelectValue
                      placeholder={
                        usersLoading ? "Loading..." : "Select assignee"
                      }
                    />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="unassigned">No Assignee</SelectItem>
                    {users.map((user) => (
                      <SelectItem key={user.id} value={user.accounts.id}>
                        <div className="flex items-center gap-2">
                          <User size={14} />
                          {user.name || user.accounts?.email || "Unknown User"}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {usersError && (
                  <p className="text-sm text-red-600 mt-1">{usersError}</p>
                )}
              </div>
            </div>

            {/* Start and Due Date Row */}
            <div className="grid grid-cols-2 gap-4">
              {/* Start Date */}
              <div>
                <Label
                  htmlFor="start"
                  className="text-sm font-medium text-gray-700 mb-2"
                >
                  Start Date
                </Label>
                <Input
                  id="start"
                  type="datetime-local"
                  value={formatDateTimeForInput(formData.start)}
                  onChange={(e) =>
                    handleInputChange("start", e.target.value || null)
                  }
                  className="w-full"
                />
              </div>

              {/* Due Date */}
              <div>
                <Label
                  htmlFor="due"
                  className="text-sm font-medium text-gray-700 mb-2"
                >
                  Due Date
                </Label>
                <Input
                  id="due"
                  type="datetime-local"
                  value={formatDateTimeForInput(formData.due)}
                  onChange={(e) =>
                    handleInputChange("due", e.target.value || null)
                  }
                  className="w-full"
                />
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isCreating}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isCreating || selectedItems.length === 0}
              className="gap-2"
            >
              {isCreating ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Creating Task...
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4" />
                  Create Task for {selectedItems.length} Item
                  {selectedItems.length > 1 ? "s" : ""}
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
