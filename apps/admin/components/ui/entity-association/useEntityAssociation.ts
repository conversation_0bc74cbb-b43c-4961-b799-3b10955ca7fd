"use client";

import { useState, useCallback } from "react";
import {
  COMMON_ENTITY_TYPES,
  fetchEntityInstances,
  type EntityInstance,
} from "@/lib/constants/entity-types";

export interface EntityAssociationResult {
  association_table: string | null;
  association_id: string | null;
  association_ids: string[] | null;
}

export interface EntityAssociationState {
  selectedTable: string;
  selectedId: string;
  selectedIds: string[];
  entities: any[];
  loading: boolean;
  error: string | null;
}

export interface UseEntityAssociationReturn {
  state: EntityAssociationState;
  result: EntityAssociationResult;
  handleTableChange: (table: string) => void;
  handleIdChange: (id: string) => void;
  handleIdsChange: (ids: string[]) => void;
  reset: () => void;
  getEntityDisplayName: (entity: any, entityType: string) => string;
  setCustomEntities: (entities: any[]) => void;
}

export function useEntityAssociation(): UseEntityAssociationReturn {
  const [state, setState] = useState<EntityAssociationState>({
    selectedTable: "",
    selectedId: "",
    selectedIds: [],
    entities: [],
    loading: false,
    error: null,
  });

  // Get display name for entity based on type
  const getEntityDisplayName = useCallback(
    (entity: any, entityType: string): string => {
      switch (entityType) {
        case "cargo":
          return `${entity.tracking_number || entity.id} - ${entity.particular || "No description"}`;
        case "shipments":
          return `${entity.tracking_number || entity.id} - ${entity.name || "Shipment"}`;
        case "freights":
          return `${entity.name || entity.id} - ${entity.origin || ""} to ${entity.destination || ""}`;
        case "batches":
          return `${entity.code || entity.code || entity.id} - ${entity.name || "Batch"}`;
        case "customers":
          return `${entity.name || entity.id} - ${entity.email || ""}`;
        case "users":
          return `${entity.name || entity.id} - ${entity.accounts?.email || entity.email || ""}`;
        case "invoices":
          return `${entity.inv_number || entity.id} - $${entity.total || 0} (${entity.status || "Unknown"})`;
        default:
          return entity.name || entity.id || "Unknown";
      }
    },
    []
  );

  // Fetch entities based on selected entity type
  const fetchEntities = useCallback(async (entityType: string) => {
    if (!entityType) {
      setState((prev) => ({ ...prev, entities: [], error: null }));
      return;
    }

    setState((prev) => ({ ...prev, loading: true, error: null }));

    try {
      const response = await fetchEntityInstances(entityType, { limit: 100 });

      if (response.success) {
        // Convert EntityInstance[] to the format expected by the component
        const formattedEntities = response.data.map(
          (entity: EntityInstance) => ({
            id: entity.id,
            name: entity.label,
            // Preserve original entity data in metadata for display purposes
            ...entity.metadata,
            // Add the original entity data for backward compatibility
            tracking_number: entity.metadata?.tracking_number,
            particular: entity.metadata?.particular,
            email: entity.metadata?.email,
            code: entity.metadata?.code,
            code: entity.metadata?.code,
            origin: entity.metadata?.origin,
            destination: entity.metadata?.destination,
            accounts: entity.metadata?.email
              ? { email: entity.metadata.email }
              : undefined,
            // Invoice specific fields
            inv_number: entity.metadata?.inv_number,
            total: entity.metadata?.total,
            status: entity.metadata?.status,
          })
        );

        setState((prev) => ({
          ...prev,
          entities: formattedEntities,
          loading: false,
        }));
      } else {
        setState((prev) => ({
          ...prev,
          entities: [],
          loading: false,
          error: response.error || "Failed to fetch entities",
        }));
      }
    } catch (error: any) {
      setState((prev) => ({
        ...prev,
        entities: [],
        loading: false,
        error: error.message || "Failed to fetch entities",
      }));
    }
  }, []);

  // Handle entity type change
  const handleTableChange = useCallback(
    (table: string) => {
      setState((prev) => ({
        ...prev,
        selectedTable: table,
        selectedId: "",
        selectedIds: [],
        entities: [],
        error: null,
      }));

      if (table) {
        fetchEntities(table);
      }
    },
    [fetchEntities]
  );

  // Handle entity ID change (single selection)
  const handleIdChange = useCallback((id: string) => {
    setState((prev) => ({ ...prev, selectedId: id }));
  }, []);

  // Handle entity IDs change (multiple selection)
  const handleIdsChange = useCallback((ids: string[]) => {
    setState((prev) => ({ ...prev, selectedIds: ids }));
  }, []);

  // Reset state
  const reset = useCallback(() => {
    setState({
      selectedTable: "",
      selectedId: "",
      selectedIds: [],
      entities: [],
      loading: false,
      error: null,
    });
  }, []);

  // Set custom entities (for special cases like invoices)
  const setCustomEntities = useCallback((entities: any[]) => {
    setState((prev) => ({ ...prev, entities, loading: false, error: null }));
  }, []);

  // Compute result
  const result: EntityAssociationResult = {
    association_table: state.selectedTable || null,
    association_id: state.selectedId || null,
    association_ids: state.selectedIds.length > 0 ? state.selectedIds : null,
  };

  return {
    state,
    result,
    handleTableChange,
    handleIdChange,
    handleIdsChange,
    reset,
    getEntityDisplayName,
    setCustomEntities,
  };
}
