"use client";

import React, { useCallback } from "react";
import { Badge } from "@workspace/ui/components/badge";
import { Loader2, ExternalLink } from "lucide-react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import {
  COMMON_ENTITY_TYPES,
  type EntityInstance,
} from "@/lib/constants/entity-types";
import { bulkFetchService, DetailLevel } from "@/lib/logistics";
import { invoiceService } from "@/lib/logistics";

export interface EntityAssociationViewerProps {
  /**
   * The entity table/type
   */
  entityTable: string | null;

  /**
   * Single entity ID or array of entity IDs
   */
  entityId: string | string[] | null;

  /**
   * Multiple entity ID or array of entity IDs
   */
  entityIds?: string[] | null;

  /**
   * Display variant
   */
  variant?: "default" | "compact" | "detailed";

  /**
   * Whether to show the entity type badge
   */
  showEntityType?: boolean;

  /**
   * Whether to show a link to view the entity (if applicable)
   */
  showViewLink?: boolean;

  /**
   * Custom class name
   */
  className?: string;

  /**
   * Callback when view link is clicked
   */
  onViewEntity?: (entityId: string, entityTable: string) => void;

  /**
   * Custom entity renderer
   */
  customRenderer?: (entity: any, entityType: string) => React.ReactNode;

  /**
   * Maximum number of entities to display (for arrays)
   */
  maxDisplay?: number;

  /**
   * Whether to show a "show more" button for large arrays
   */
  showExpandButton?: boolean;

  /**
   * Sort entities by a specific field
   */
  sortBy?: "name" | "code" | "tracking_number" | "status" | "created_at";

  /**
   * Sort direction
   */
  sortDirection?: "asc" | "desc";
}

interface EntityDisplayData {
  id: string;
  name: string;
  code?: string;
  tracking_number?: string;
  subtitle?: string;
  details?: string[];
  status?: string;
  metadata?: any;
  relationshipInfo?: string[];
}

// Helper function to enhance entity data with relationship information from detailed responses
function enhanceEntityWithRelationships(
  baseEntity: EntityDisplayData,
  metadata: any,
  entityType: string
): EntityDisplayData {
  const relationshipInfo: string[] = [];

  switch (entityType) {
    case "customers":
      if (metadata.cargos && Array.isArray(metadata.cargos)) {
        relationshipInfo.push(`${metadata.cargos.length} cargo(s)`);
      }
      break;

    case "cargos":
      if (metadata.customers) {
        relationshipInfo.push(`Customer: ${metadata.customers.name}`);
      }
      if (metadata.batches) {
        relationshipInfo.push(
          `Batch: ${metadata.batches.name || metadata.batches.code}`
        );
      }
      if (metadata.suppliers) {
        relationshipInfo.push(
          `Supplier: ${metadata.suppliers.tracking_number || metadata.suppliers.id}`
        );
      }
      if (metadata.users) {
        relationshipInfo.push(`Assigned to: ${metadata.users.name}`);
      }
      break;

    case "batches":
      if (metadata.cargos && Array.isArray(metadata.cargos)) {
        relationshipInfo.push(`${metadata.cargos.length} cargo(s)`);
        const totalWeight = metadata.cargos.reduce(
          (sum: number, cargo: any) => sum + (cargo.weight_value || 0),
          0
        );
        const totalCBM = metadata.cargos.reduce(
          (sum: number, cargo: any) => sum + (cargo.cbm_value || 0),
          0
        );
        if (totalWeight > 0)
          relationshipInfo.push(`Total Weight: ${totalWeight}kg`);
        if (totalCBM > 0) relationshipInfo.push(`Total CBM: ${totalCBM}m³`);
      }
      if (metadata.accounts?.users) {
        relationshipInfo.push(`Created by: ${metadata.accounts.users.name}`);
      }
      break;

    case "freights":
      if (metadata.shipments && Array.isArray(metadata.shipments)) {
        relationshipInfo.push(`${metadata.shipments.length} shipment(s)`);
      }
      if (metadata.capacity_weight) {
        relationshipInfo.push(`Capacity: ${metadata.capacity_weight}kg`);
      }
      if (metadata.capacity_cbm) {
        relationshipInfo.push(`Capacity: ${metadata.capacity_cbm}m³`);
      }
      break;

    case "shipments":
      if (metadata.freights) {
        relationshipInfo.push(`Freight: ${metadata.freights.name}`);
      }
      if (metadata.batches) {
        relationshipInfo.push(
          `Batch: ${metadata.batches.name || metadata.batches.code}`
        );
      }
      if (metadata.bill_of_lading) {
        relationshipInfo.push(`B/L: ${metadata.bill_of_lading}`);
      }
      break;
  }

  return {
    ...baseEntity,
    relationshipInfo:
      relationshipInfo.length > 0 ? relationshipInfo : undefined,
  };
}

export function EntityAssociationViewer({
  entityTable,
  entityId,
  entityIds,
  variant = "default",
  showEntityType = true,
  showViewLink = false,
  className = "",
  onViewEntity,
  customRenderer,
  maxDisplay = 10,
  showExpandButton = true,
  sortBy = "name",
  sortDirection = "asc",
}: EntityAssociationViewerProps) {
  const [entities, setEntities] = React.useState<EntityDisplayData[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [isExpanded, setIsExpanded] = React.useState(false);

  // Get entity type label
  const entityTypeLabel = React.useMemo(() => {
    if (!entityTable) return null;
    return (
      COMMON_ENTITY_TYPES.find((type) => type.value === entityTable)?.label ||
      entityTable
    );
  }, [entityTable]);

  const fetchEntityData = useCallback(async () => {
    if (!entityTable) {
      setEntities([]);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Properly flatten and filter out null/undefined values to ensure we have string[]
      const ids = entityIds
        ? [entityIds, entityId].flat().filter((id): id is string => Boolean(id))
        : Array.isArray(entityId)
          ? entityId.filter((id): id is string => Boolean(id))
          : entityId
            ? [entityId]
            : [];

      // Early return if no IDs to fetch
      if (ids.length === 0) {
        setEntities([]);
        return;
      }

      // Map entity table names to bulk fetch service entity types
      const entityTypeMap: Record<string, string> = {
        cargo: "cargos",
        cargos: "cargos",
        customers: "customers",
        batches: "batches",
        freights: "freights",
        shipments: "shipments",
        invoices: "invoices",
      };

      const mappedEntityType = entityTypeMap[entityTable] || entityTable;

      // Check if the entity type is supported by bulk fetch service
      const supportedTypes = [
        "customers",
        "cargos",
        "batches",
        "freights",
        "shipments",
      ];

      let response;
      if (supportedTypes.includes(mappedEntityType)) {
        // Map component variant to bulk fetch detail level
        const getDetailLevel = (variant: string) => {
          switch (variant) {
            case "compact":
              return DetailLevel.MINIMAL;
            case "detailed":
              return DetailLevel.DETAILED;
            default:
              return DetailLevel.STANDARD;
          }
        };

        // Use bulk fetch service with ID filtering for supported entity types
        response = await bulkFetchService.fetchEntitiesByIds(
          mappedEntityType as any,
          ids,
          {
            detailLevel: getDetailLevel(variant),
            cacheResults: false, // Disable caching for ID-specific queries
          }
        );

        // Transform bulk fetch response to match expected format
        if (response.success) {
          const entityInstances: EntityInstance[] = response.data.map(
            (entity: any) => ({
              id: entity.id,
              label:
                entity.name ||
                entity.particular ||
                entity.tracking_number ||
                `${mappedEntityType} ${entity.id.slice(0, 8)}`,
              description: entity.description || "",
              metadata: {
                ...entity,
                type: mappedEntityType,
              },
            })
          );

          // Enhanced formatting that leverages bulk service detail levels
          const formattedEntities = entityInstances.map(
            (entity: EntityInstance) => {
              const baseEntity = {
                id: entity.id,
                name: getEntityDisplayName(entity, entityTable),
                code: getEntityCode(entity, entityTable),
                tracking_number: getEntityTrackingNumber(entity, entityTable),
                subtitle: getEntitySubtitle(entity, entityTable),
                details: getEntityDetails(entity, entityTable),
                status: getEntityStatus(entity, entityTable),
                metadata: entity.metadata,
              };

              // Enhance with relationship data from detailed responses
              if (variant === "detailed") {
                return enhanceEntityWithRelationships(
                  baseEntity,
                  entity.metadata,
                  mappedEntityType
                );
              }

              return baseEntity;
            }
          );

          setEntities(formattedEntities);
        } else {
          setError(response.error || "Failed to fetch entity data");
        }
      } else {
        // Fallback for unsupported entity types (like invoices)
        if (mappedEntityType === "invoices") {
          // Use invoice service for fetching invoices by IDs
          const invoicePromises = ids.map((id) =>
            invoiceService.getInvoiceWithCustomer(id)
          );
          const invoiceResults = await Promise.all(invoicePromises);

          const successfulInvoices = invoiceResults
            .filter((result) => result.success && result.data)
            .map((result) => result.data!);

          if (successfulInvoices.length > 0) {
            const entityInstances: EntityInstance[] = successfulInvoices.map(
              (invoice: any) => ({
                id: invoice.id,
                label:
                  invoice.inv_number || `Invoice ${invoice.id.slice(0, 8)}`,
                description: invoice.notes || "",
                metadata: {
                  ...invoice,
                  type: "invoices",
                },
              })
            );

            const formattedEntities = entityInstances.map(
              (entity: EntityInstance) => {
                const baseEntity = {
                  id: entity.id,
                  name: getEntityDisplayName(entity, entityTable),
                  code: getEntityCode(entity, entityTable),
                  tracking_number: getEntityTrackingNumber(entity, entityTable),
                  subtitle: getEntitySubtitle(entity, entityTable),
                  details: getEntityDetails(entity, entityTable),
                  status: getEntityStatus(entity, entityTable),
                  metadata: entity.metadata,
                };

                return baseEntity;
              }
            );

            setEntities(formattedEntities);
          } else {
            setEntities([]);
          }
        } else {
          // Error for truly unsupported entity types
          setError(
            `Entity type "${entityTable}" is not supported by the bulk fetch service`
          );
        }
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch entity data");
    } finally {
      setLoading(false);
    }
  }, [entityTable, entityId, entityIds]);

  // Fetch entity data
  React.useEffect(() => {
    fetchEntityData();
  }, [entityTable, entityId, entityIds]);

  // Sort and filter entities for display
  const displayEntities = React.useMemo(() => {
    if (!entities.length) return [];

    // Sort entities
    const sorted = [...entities].sort((a, b) => {
      let aValue: string | number = "";
      let bValue: string | number = "";

      switch (sortBy) {
        case "name":
          aValue = a.name || "";
          bValue = b.name || "";
          break;
        case "code":
          aValue = a.code || "";
          bValue = b.code || "";
          break;
        case "tracking_number":
          aValue = a.tracking_number || "";
          bValue = b.tracking_number || "";
          break;
        case "status":
          aValue = a.status || "";
          bValue = b.status || "";
          break;
        case "created_at":
          aValue = a.metadata?.created_at || "";
          bValue = b.metadata?.created_at || "";
          break;
        default:
          aValue = a.name || "";
          bValue = b.name || "";
      }

      if (typeof aValue === "string" && typeof bValue === "string") {
        const comparison = aValue.localeCompare(bValue);
        return sortDirection === "asc" ? comparison : -comparison;
      }

      return 0;
    });

    // Limit display if not expanded
    if (!isExpanded && sorted.length > maxDisplay) {
      return sorted.slice(0, maxDisplay);
    }

    return sorted;
  }, [entities, sortBy, sortDirection, isExpanded, maxDisplay]);

  // Get entity display name
  const getEntityDisplayName = (
    entity: EntityInstance,
    entityType: string
  ): string => {
    const metadata = entity.metadata || {};

    switch (entityType) {
      case "cargo":
        return (
          metadata.particular ||
          metadata.name ||
          `Cargo ${entity.id.slice(0, 8)}`
        );
      case "shipments":
        return (
          metadata.name ||
          metadata.tracking_number ||
          `Shipment ${entity.id.slice(0, 8)}`
        );
      case "freights":
        return metadata.name || `Freight ${entity.id.slice(0, 8)}`;
      case "batches":
        return (
          metadata.name || metadata.code || `Batch ${entity.id.slice(0, 8)}`
        );
      case "customers":
        return (
          metadata.name || metadata.email || `Customer ${entity.id.slice(0, 8)}`
        );
      case "users":
        return (
          metadata.name || metadata.email || `User ${entity.id.slice(0, 8)}`
        );
      case "invoices":
        return metadata.inv_number || `Invoice ${entity.id.slice(0, 8)}`;
      default:
        return (
          entity.label ||
          metadata.name ||
          metadata.title ||
          `${entityType} ${entity.id.slice(0, 8)}`
        );
    }
  };

  // Get entity subtitle
  const getEntitySubtitle = (
    entity: EntityInstance,
    entityType: string
  ): string | undefined => {
    const metadata = entity.metadata || {};

    switch (entityType) {
      case "cargo":
        return metadata.tracking_number;
      case "shipments":
        return metadata.status;
      case "freights":
        return metadata.origin && metadata.destination
          ? `${metadata.origin} → ${metadata.destination}`
          : undefined;
      case "batches":
        return metadata.status;
      case "customers":
        return metadata.email;
      case "users":
        return metadata.email || metadata.phone;
      case "invoices":
        return metadata.total ? `$${metadata.total}` : undefined;
      default:
        return undefined;
    }
  };

  // Get entity code
  const getEntityCode = (
    entity: EntityInstance,
    entityType: string
  ): string | undefined => {
    const metadata = entity.metadata || {};

    switch (entityType) {
      case "cargo":
        return metadata.code || metadata.cargo_code;
      case "shipments":
        return metadata.code || metadata.shipment_code;
      case "freights":
        return metadata.code || metadata.freight_code || metadata.scac_code;
      case "batches":
        return metadata.code || metadata.code;
      case "customers":
        return metadata.code || metadata.customer_code;
      case "users":
        return metadata.code || metadata.user_code || metadata.employee_id;
      case "invoices":
        return metadata.inv_number || metadata.invoice_code;
      default:
        return metadata.code;
    }
  };

  // Get entity tracking number
  const getEntityTrackingNumber = (
    entity: EntityInstance,
    entityType: string
  ): string | undefined => {
    const metadata = entity.metadata || {};

    switch (entityType) {
      case "cargo":
        return metadata.tracking_number || metadata.china_tracking_number;
      case "shipments":
        return metadata.tracking_number || metadata.bill_of_lading;
      case "freights":
        return metadata.tracking_number || metadata.freight_reference_id;
      case "batches":
        return metadata.tracking_number;
      default:
        return metadata.tracking_number;
    }
  };

  // Get entity status
  const getEntityStatus = (
    entity: EntityInstance,
    _entityType: string
  ): string | undefined => {
    const metadata = entity.metadata || {};
    return metadata.status;
  };

  // Get entity details array
  const getEntityDetails = (
    entity: EntityInstance,
    entityType: string
  ): string[] => {
    const metadata = entity.metadata || {};
    const details: string[] = [];

    switch (entityType) {
      case "cargos":
        if (metadata.particular) details.push(`Type: ${metadata.particular}`);
        if (metadata.weight) details.push(`Weight: ${metadata.weight}kg`);
        if (metadata.cbm) details.push(`CBM: ${metadata.cbm}`);
        if (metadata.ctn) details.push(`CTN: ${metadata.ctn}`);
        break;
      case "shipments":
        if (metadata.vessel_name)
          details.push(`Vessel: ${metadata.vessel_name}`);
        if (metadata.eta)
          details.push(`ETA: ${new Date(metadata.eta).toLocaleDateString()}`);
        if (metadata.etd)
          details.push(`ETD: ${new Date(metadata.etd).toLocaleDateString()}`);
        break;
      case "freights":
        if (metadata.freight_type)
          details.push(`Type: ${metadata.freight_type}`);
        if (metadata.carrier) details.push(`Carrier: ${metadata.carrier}`);
        break;
      case "batches":
        if (metadata.freight_type)
          details.push(`Type: ${metadata.freight_type}`);
        if (metadata.total_weight)
          details.push(`Weight: ${metadata.total_weight}kg`);
        if (metadata.total_cbm) details.push(`CBM: ${metadata.total_cbm}`);
        break;
      case "customers":
        if (metadata.phone) details.push(`Phone: ${metadata.phone}`);
        if (metadata.company) details.push(`Company: ${metadata.company}`);
        break;
      case "users":
        if (metadata.role) details.push(`Role: ${metadata.role}`);
        if (metadata.department) details.push(`Dept: ${metadata.department}`);
        break;
      case "invoices":
        if (metadata.customer_name)
          details.push(`Customer: ${metadata.customer_name}`);
        if (metadata.due_date)
          details.push(
            `Due: ${new Date(metadata.due_date).toLocaleDateString()}`
          );
        break;
    }

    return details;
  };

  // Handle view entity click
  const handleViewEntity = (entityId: string) => {
    if (onViewEntity && entityTable) {
      onViewEntity(entityId, entityTable);
    }
  };

  // Render loading state
  if (loading) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Loader2 className="h-4 w-4 animate-spin" />
        <span className="text-sm text-gray-500">Loading...</span>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className={`text-sm text-red-500 ${className}`}>Error: {error}</div>
    );
  }

  // Render empty state
  if (!entityTable || (!entityId && !entityIds) || entities.length === 0) {
    return (
      <div className={`text-sm text-gray-500 ${className}`}>No association</div>
    );
  }

  // Render single entity
  if (entities.length === 1) {
    const entity = displayEntities[0];

    if (!entity) {
      return (
        <div className={`text-sm text-gray-500 ${className}`}>
          Entity not found
        </div>
      );
    }

    if (customRenderer) {
      return (
        <div className={className}>{customRenderer(entity, entityTable)}</div>
      );
    }

    return (
      <div className={`flex items-start gap-3 ${className}`}>
        {showEntityType && entityTypeLabel && (
          <Badge variant="secondary" className="text-xs">
            {entityTypeLabel}
          </Badge>
        )}

        <div className="flex flex-col flex-1 min-w-0">
          {/* Primary Information */}
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium truncate">{entity.name}</span>
            {entity.status && (
              <Badge variant="outline" className="text-xs">
                {entity.status}
              </Badge>
            )}
          </div>

          {/* Secondary Information */}
          {variant !== "compact" && (
            <div className="space-y-1 mt-1">
              {/* Code and Tracking */}
              <div className="flex flex-wrap gap-3 text-xs text-gray-500">
                {entity.code && (
                  <span className="flex items-center gap-1">
                    <span className="font-medium">Code:</span>
                    <span className="font-mono">{entity.code}</span>
                  </span>
                )}
                {entity.tracking_number && (
                  <span className="flex items-center gap-1">
                    <span className="font-medium">Tracking:</span>
                    <span className="font-mono">{entity.tracking_number}</span>
                  </span>
                )}
              </div>

              {/* Subtitle */}
              {entity.subtitle && (
                <div className="text-xs text-gray-500">{entity.subtitle}</div>
              )}

              {/* Additional Details */}
              {variant === "detailed" &&
                entity.details &&
                entity.details.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-2">
                    {entity.details.map((detail, index) => (
                      <span
                        key={index}
                        className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded"
                      >
                        {detail}
                      </span>
                    ))}
                  </div>
                )}

              {/* Relationship Information from Bulk Service */}
              {variant === "detailed" &&
                entity.relationshipInfo &&
                entity.relationshipInfo.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-2">
                    {entity.relationshipInfo.map((info, index) => (
                      <span
                        key={index}
                        className="text-xs bg-blue-50 text-blue-700 px-2 py-1 rounded border border-blue-200"
                      >
                        {info}
                      </span>
                    ))}
                  </div>
                )}
            </div>
          )}
        </div>

        {showViewLink && (
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 flex-shrink-0"
            onClick={() => handleViewEntity(entity.id)}
          >
            <ExternalLink className="h-3 w-3" />
          </Button>
        )}
      </div>
    );
  }

  // Render multiple entities
  return (
    <div className={`space-y-2 ${className}`}>
      {showEntityType && entityTypeLabel && (
        <Badge variant="secondary" className="text-xs capitalize">
          {entityTypeLabel} ({entities.length})
        </Badge>
      )}

      <div className="space-y-3 flex flex-col h-[17em] overflow-y-auto">
        {displayEntities.map((entity) => (
          <div
            key={entity.id}
            className="flex items-start gap-2 p-2 border rounded-lg bg-gray-100"
          >
            {customRenderer ? (
              customRenderer(entity, entityTable)
            ) : (
              <>
                <div className="flex flex-col flex-1 min-w-0">
                  {/* Primary Information */}
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-sm font-medium truncate capitalize">
                      {entity.name}
                    </span>
                    {entity.status && (
                      <Badge variant="outline" className="text-xs">
                        {entity.status}
                      </Badge>
                    )}
                  </div>

                  {/* Secondary Information */}
                  {variant !== "compact" && (
                    <div className="space-y-1">
                      {/* Code and Tracking */}
                      <div className="flex flex-wrap gap-3 text-xs text-gray-500">
                        {entity.code && (
                          <span className="flex items-center gap-1">
                            <span className="font-medium">Code:</span>
                            <span className="font-mono">{entity.code}</span>
                          </span>
                        )}
                        {entity.tracking_number && (
                          <span className="flex items-center gap-1">
                            <span className="font-medium">Tracking:</span>
                            <span className="font-mono">
                              {entity.tracking_number}
                            </span>
                          </span>
                        )}
                      </div>

                      {/* Subtitle */}
                      {entity.subtitle && (
                        <div className="text-xs text-gray-500">
                          {entity.subtitle}
                        </div>
                      )}

                      {/* Additional Details for detailed variant */}
                      {variant === "detailed" &&
                        entity.details &&
                        entity.details.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-1">
                            {entity.details.slice(0, 3).map((detail, index) => (
                              <span
                                key={index}
                                className="text-xs bg-white text-gray-600 px-2 py-1 rounded border"
                              >
                                {detail}
                              </span>
                            ))}
                            {entity.details.length > 3 && (
                              <span className="text-xs text-gray-400">
                                +{entity.details.length - 3} more
                              </span>
                            )}
                          </div>
                        )}

                      {/* Relationship Information from Bulk Service */}
                      {variant === "detailed" &&
                        entity.relationshipInfo &&
                        entity.relationshipInfo.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-1">
                            {entity.relationshipInfo
                              .slice(0, 2)
                              .map((info, index) => (
                                <span
                                  key={index}
                                  className="text-xs bg-blue-50 text-blue-700 px-2 py-1 rounded border border-blue-200"
                                >
                                  {info}
                                </span>
                              ))}
                            {entity.relationshipInfo.length > 2 && (
                              <span className="text-xs text-blue-400">
                                +{entity.relationshipInfo.length - 2} more
                              </span>
                            )}
                          </div>
                        )}
                    </div>
                  )}
                </div>

                {showViewLink && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 flex-shrink-0"
                    onClick={() => handleViewEntity(entity.id)}
                  >
                    <ExternalLink className="h-3 w-3" />
                  </Button>
                )}
              </>
            )}
          </div>
        ))}
      </div>

      {/* Show More/Less Button */}
      {showExpandButton && entities.length > maxDisplay && (
        <div className="flex justify-center pt-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-xs text-gray-500 hover:text-gray-700"
          >
            {isExpanded ? (
              <>Show Less ({entities.length - maxDisplay} hidden)</>
            ) : (
              <>Show More ({entities.length - maxDisplay} more)</>
            )}
          </Button>
        </div>
      )}
    </div>
  );
}
