"use client";

import { useState } from "react";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { CheckCircle, ChevronDown } from "lucide-react";

export interface StatusOption {
  value: string;
  label: string;
  icon?: React.ComponentType<{ size?: number }>;
  variant?: "default" | "success" | "warning" | "destructive";
}

interface BulkStatusSelectorProps {
  statusOptions: StatusOption[];
  onStatusUpdate: (status: string) => void;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
  onAfterUpdate?: () => void; // Callback for soft refresh after status update
}

// Global status mapping for consistent status values across the application
const GLOBAL_STATUS_MAPPING = {
  ACTIVE: "ACTIVE",
  INACTIVE: "INACTIVE",
  PENDING: "PENDING",
  COMPLETED: "COMPLETED",
  CANCELLED: "CANCELLED",
  CREATED: "CREATED",
  PROCESSING: "PROCESSING",
  IN_TRANSIT: "IN_TRANSIT",
  DELIVERED: "DELIVERED",
  PICKED_UP: "PICKED_UP",
  RELEASED: "RELEASED",
  APPROVED: "APPROVED",
  REJECTED: "REJECTED",
  DRAFT: "DRAFT",
  PAID: "PAID",
  OVERDUE: "OVERDUE",
  CANCELED: "CANCELED",
  READ: "READ",
  UNREAD: "UNREAD",
  LOADED: "LOADED",
  UNLOADED: "UNLOADED",
  ARRIVED: "ARRIVED",
  CLEARED: "CLEARED",
  READY_FOR_PICKUP: "READY_FOR_PICKUP",
  PARTIAL_PAYMENT: "PARTIAL_PAYMENT",
  CLOSED: "CLOSED",
  ARCHIVED: "ARCHIVED",
} as const;

/**
 * Reusable bulk status selector component
 *
 * Provides a dropdown selector for bulk status updates instead of individual buttons.
 * Supports dynamic status options and consistent styling across modules.
 */
export function BulkStatusSelector({
  statusOptions,
  onStatusUpdate,
  disabled = false,
  placeholder = "Mark as...",
  className = "",
  onAfterUpdate,
}: BulkStatusSelectorProps) {
  const [selectedStatus, setSelectedStatus] = useState<string>("");

  const handleStatusChange = (status: string) => {
    setSelectedStatus(status);
    onStatusUpdate(status);

    // Call the after update callback for soft refresh
    if (onAfterUpdate) {
      // Small delay to allow the status update to complete
      setTimeout(() => {
        onAfterUpdate();
      }, 500);
    }

    // Reset selection after action
    setTimeout(() => setSelectedStatus(""), 100);
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Select
        value={selectedStatus}
        onValueChange={handleStatusChange}
        disabled={disabled}
      >
        <SelectTrigger className="w-[140px] h-8">
          <div className="flex items-center gap-2">
            <CheckCircle size={14} />
            <SelectValue placeholder={placeholder} />
          </div>
        </SelectTrigger>
        <SelectContent>
          {statusOptions.map((option) => {
            const IconComponent = option.icon || CheckCircle;
            return (
              <SelectItem
                key={option.value}
                value={option.value}
                className="flex items-center gap-2"
              >
                <div className="flex items-center gap-2">
                  <IconComponent size={14} />
                  <span>{option.label}</span>
                </div>
              </SelectItem>
            );
          })}
        </SelectContent>
      </Select>
    </div>
  );
}

// Predefined status options for different entity types
export const CARGO_STATUS_OPTIONS: StatusOption[] = [
  { value: GLOBAL_STATUS_MAPPING.CREATED, label: "Created" },
  { value: GLOBAL_STATUS_MAPPING.PROCESSING, label: "Processing" },
  { value: GLOBAL_STATUS_MAPPING.IN_TRANSIT, label: "In Transit" },
  { value: GLOBAL_STATUS_MAPPING.DELIVERED, label: "Delivered" },
  { value: GLOBAL_STATUS_MAPPING.COMPLETED, label: "Completed" },
  { value: GLOBAL_STATUS_MAPPING.ARCHIVED, label: "Archived" },
];

export const FREIGHT_STATUS_OPTIONS: StatusOption[] = [
  { value: GLOBAL_STATUS_MAPPING.ACTIVE, label: "Active" },
  { value: GLOBAL_STATUS_MAPPING.IN_TRANSIT, label: "In Transit" },
  { value: GLOBAL_STATUS_MAPPING.DELIVERED, label: "Delivered" },
  { value: GLOBAL_STATUS_MAPPING.COMPLETED, label: "Completed" },
  { value: GLOBAL_STATUS_MAPPING.ARCHIVED, label: "Archived" },
];

export const INVOICE_STATUS_OPTIONS: StatusOption[] = [
  { value: GLOBAL_STATUS_MAPPING.PENDING, label: "Pending" },
  { value: GLOBAL_STATUS_MAPPING.PAID, label: "Paid" },
  { value: GLOBAL_STATUS_MAPPING.OVERDUE, label: "Overdue" },
  { value: GLOBAL_STATUS_MAPPING.CANCELLED, label: "Cancelled" },
  { value: GLOBAL_STATUS_MAPPING.CLOSED, label: "Closed" },
];

export const SHIPPING_STATUS_OPTIONS: StatusOption[] = [
  { value: GLOBAL_STATUS_MAPPING.ACTIVE, label: "Active" },
  { value: GLOBAL_STATUS_MAPPING.IN_TRANSIT, label: "In Transit" },
  { value: GLOBAL_STATUS_MAPPING.PENDING, label: "Delayed" },
  { value: GLOBAL_STATUS_MAPPING.ARRIVED, label: "Arrived" },
  { value: GLOBAL_STATUS_MAPPING.ARCHIVED, label: "Archived" },
];

export const TASK_STATUS_OPTIONS: StatusOption[] = [
  { value: GLOBAL_STATUS_MAPPING.CREATED, label: "Created" },
  { value: GLOBAL_STATUS_MAPPING.PENDING, label: "Pending" },
  { value: GLOBAL_STATUS_MAPPING.PROCESSING, label: "In Progress" },
  { value: GLOBAL_STATUS_MAPPING.COMPLETED, label: "Completed" },
  { value: GLOBAL_STATUS_MAPPING.CANCELLED, label: "Cancelled" },
  { value: GLOBAL_STATUS_MAPPING.ARCHIVED, label: "Archived" },
];

export const LEDGER_STATUS_OPTIONS: StatusOption[] = [
  { value: GLOBAL_STATUS_MAPPING.ACTIVE, label: "Active" },
  { value: GLOBAL_STATUS_MAPPING.CLOSED, label: "Closed" },
  { value: GLOBAL_STATUS_MAPPING.ARCHIVED, label: "Archived" },
];

export const TRANSACTION_STATUS_OPTIONS: StatusOption[] = [
  { value: GLOBAL_STATUS_MAPPING.PENDING, label: "Pending" },
  { value: GLOBAL_STATUS_MAPPING.COMPLETED, label: "Completed" },
  { value: GLOBAL_STATUS_MAPPING.CANCELLED, label: "Cancelled" },
  { value: GLOBAL_STATUS_MAPPING.ARCHIVED, label: "Archived" },
];
