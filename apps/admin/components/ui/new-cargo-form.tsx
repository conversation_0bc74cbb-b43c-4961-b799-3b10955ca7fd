"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import {
  cargoService,
  customerService,
  supplierService,
  invoiceService,
  type Customer,
  type Supplier,
  type InvoiceWithCustomer,
  Cargo_Categories,
  CARGO_CATEGORY_LABELS,
} from "@/lib/logistics";
import { AnimatedTabGroup } from "@/components/animated-tab-group";
import { useAppSelector } from "@/store/hooks";
import { cn } from "@workspace/ui/lib/utils";
import { trimming } from "@/lib/utils";
import { DialogFooter, DialogClose } from "@workspace/ui/components/dialog";
import { codeGeneratorService } from "@/lib/logistics/operations/code-generator";
import { batchService } from "@/lib/logistics/operations/batches";

interface NewCargoFormProps {
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
  batchId?: string;
}

const InputField = ({
  id,
  label,
  icon: Icon,
  ...props
}: {
  id: string;
  label: string;
  icon?: React.ElementType;
  [key: string]: any;
} & React.InputHTMLAttributes<HTMLInputElement>) => (
  <div>
    <label
      htmlFor={id}
      className="block text-sm font-medium text-gray-700 mb-1.5"
    >
      {label}
    </label>
    <div className="relative">
      {Icon && (
        <Icon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
      )}
      <Input
        id={id}
        className={cn(
          "w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white text-gray-900 shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary",
          Icon ? "pl-10" : ""
        )}
        {...props}
      />
    </div>
  </div>
);

const SelectField = ({
  id,
  label,
  placeholder,
  defaultValue,
  value,
  options,
  onChange,
  ...props
}: {
  id: string;
  label: string;
  placeholder?: string;
  defaultValue?: string;
  value?: string;
  options: (string | { value: string; label: string })[];
  onChange?: (value: string) => void;
  [key: string]: any;
}) => (
  <div>
    <label
      htmlFor={id}
      className="block text-sm font-medium text-gray-700 mb-1.5"
    >
      {label}
    </label>
    <Select
      defaultValue={defaultValue}
      value={value}
      onValueChange={onChange}
      {...props}
    >
      <SelectTrigger
        id={id}
        className="w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white text-gray-900 shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary data-[placeholder]:text-gray-500 h-10"
      >
        <SelectValue placeholder={placeholder ?? "Select..."} />
      </SelectTrigger>
      <SelectContent>
        {options.map((option) => {
          const value =
            typeof option === "string"
              ? option === "All Types" || option === "All Statuses"
                ? "all"
                : option.toLowerCase().replace(/\s+/g, "-")
              : option.value;
          const displayLabel =
            typeof option === "string" ? option : option.label;
          return (
            <SelectItem key={value} value={value} className="text-sm text-wrap">
              {trimming(displayLabel, 20)}
            </SelectItem>
          );
        })}
      </SelectContent>
    </Select>
  </div>
);

export const NewCargoForm: React.FC<NewCargoFormProps> = ({
  onOpenChange,
  onSuccess,
  batchId,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [invoices, setInvoices] = useState<InvoiceWithCustomer[]>([]);
  const [loadingCustomers, setLoadingCustomers] = useState(true);
  const [loadingSuppliers, setLoadingSuppliers] = useState(true);
  const [loadingInvoices, setLoadingInvoices] = useState(false);
  const [factor, setFactor] = useState("Weight");
  const [entityType, setEntityType] = useState<"customer" | "supplier">(
    "customer"
  );

  const INITIAL_FORM_DATA = {
    type: "",
    customer_id: "",
    supplier_id: "",
    particular: "",
    china_tracking_number: "",
    quantity: 1,
    dimensionLength: 0.0,
    dimensionWidth: 0.0,
    dimensionHeight: 0.0,
    weightValue: 0.0,
    weightUnit: "KILOGRAMS",
    totalPrice: 0.0,
    CTN: 0,
    unit_price: 0.0,
    invoice_id: "",
    factor_unit: "",
    factor_value: 1,
  };

  const [formData, setFormData] = useState(INITIAL_FORM_DATA);
  const { user: authUser } = useAppSelector((state) => state.auth);

  // Fetch customers and suppliers
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [customersResult, suppliersResult] = await Promise.all([
          customerService.getActiveCustomers({ limit: 100 }),
          supplierService.getActiveSuppliers({ limit: 100 }),
        ]);

        if (customersResult.success) {
          setCustomers(customersResult.data);
        }
        if (suppliersResult.success) {
          setSuppliers(suppliersResult.data);
        }
      } catch (error) {
        console.error("Error fetching form data:", error);
      } finally {
        setLoadingCustomers(false);
        setLoadingSuppliers(false);
      }
    };

    fetchData();
  }, []);

  // Fetch invoices when customer is selected
  useEffect(() => {
    const fetchInvoices = async () => {
      if (entityType !== "customer" || !formData.customer_id) {
        setInvoices([]);
        return;
      }

      try {
        setLoadingInvoices(true);
        const invoicesResult = await invoiceService.getAllInvoicesWithCustomers(
          { limit: 50 }
        );

        if (invoicesResult.success && invoicesResult.data) {
          const customerInvoices = invoicesResult.data.filter(
            (invoice) => invoice.customer_id === formData.customer_id
          );
          setInvoices(customerInvoices);
        }
      } catch (error) {
        console.error("Error fetching invoices:", error);
      } finally {
        setLoadingInvoices(false);
      }
    };

    fetchInvoices();
  }, [formData.customer_id, entityType]);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleEntityTypeChange = (type: "customer" | "supplier") => {
    setEntityType(type);
    if (type === "customer") {
      setFormData((prev) => ({ ...prev, supplier_id: "" }));
    } else {
      setFormData((prev) => ({ ...prev, customer_id: "", invoice_id: "" }));
      setInvoices([]);
    }
  };

  function calculateCBM() {
    const { dimensionLength, dimensionWidth, dimensionHeight, CTN } = formData;
    let cbm = dimensionLength * dimensionWidth * dimensionHeight * CTN;
    return cbm / 1000000 || 0;
  }

  function calculateTotalPrice() {
    const { unit_price, weightValue, quantity } = formData;
    let CBM = calculateCBM();
    let weight = weightValue;
    let unitPrice = unit_price;
    let qty = quantity || 1;
    let factor = weight || CBM;

    if (weight) {
      setFactor("Weight");
      factor = weight;
    } else if (CBM) {
      setFactor("CBM");
      factor = CBM;
    }

    return unitPrice * factor * qty;
  }

  useEffect(() => {
    setFormData((prev) => ({
      ...prev,
      totalPrice: calculateTotalPrice(),
    }));
  }, [
    formData.unit_price,
    formData.weightValue,
    formData.dimensionLength,
    formData.dimensionWidth,
    formData.dimensionHeight,
    formData.quantity,
    formData.CTN,
  ]);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!authUser) return;

    setIsSubmitting(true);
    try {
      // Generate cargo tracking number using centralized service
      // First, get the batch code if batch is assigned
      let batchCode = "";
      if (batchId) {
        try {
          const batchResult = await batchService.getBatchWithRelations(batchId);
          if (batchResult.success && batchResult.data) {
            batchCode = batchResult.data.code || "";
          }
        } catch (error) {
          console.warn("Failed to fetch batch code:", error);
        }
      }

      // Fetch existing cargo tracking numbers for sequential generation
      const existingCargosResult = await cargoService.getAllCargosWithRelations(
        {
          filters: {},
          page: 1,
          limit: 1000, // Get all to check tracking numbers
        }
      );

      const existingTrackingNumbers =
        existingCargosResult.success && existingCargosResult.data
          ? existingCargosResult.data
              .map((cargo) => cargo.tracking_number)
              .filter(
                (trackingNumber): trackingNumber is string => !!trackingNumber
              )
              .filter((trackingNumber) =>
                trackingNumber.startsWith(`CG/${batchCode || "GENERAL"}/`)
              )
          : [];

      const trackingNumber =
        codeGeneratorService.generateSequentialCargoTrackingNumber(
          {
            batchCode: batchCode || "GENERAL", // Use batch code or fallback
          },
          existingTrackingNumbers
        );

      // Validate entity selection
      if (entityType === "customer" && !formData.customer_id) {
        toast.error("Validation Error", {
          description: "Please select a customer.",
        });
        setIsSubmitting(false);
        return;
      }

      if (entityType === "supplier" && !formData.supplier_id) {
        toast.error("Validation Error", {
          description: "Please select a supplier.",
        });
        setIsSubmitting(false);
        return;
      }

      let cbm_value = calculateCBM();
      let weightValue = formData.weightValue;
      let weightUnit = formData.weightUnit;

      if (!weightValue) {
        weightUnit = "KILOGRAMS";
        weightValue = cbm_value * 167;
      }

      // Prepare cargo data
      const cargoData = {
        tracking_number: trackingNumber,
        type: formData.type || "GENERAL", // Add required type field
        customer_id: entityType === "customer" ? formData.customer_id : null,
        supplier_id: entityType === "supplier" ? formData.supplier_id : null,
        batch_id: batchId || null,
        particular: formData.particular || null,
        china_tracking_number: formData.china_tracking_number || null,
        quantity: formData.quantity ? formData.quantity : 1,
        dimension_length: formData.dimensionLength || null,
        dimension_width: formData.dimensionWidth || null,
        dimension_height: formData.dimensionHeight || null,
        dimension_unit: "METERS" as any,
        weight_value: weightValue || null,
        weight_unit: weightUnit as any,
        factor_unit: factor,
        factor_value:
          factor === "Weight" ? formData.weightValue : calculateCBM(),
        total_price: formData.totalPrice || null,
        unit_price: formData.unit_price || null,
        ctn: formData.CTN,
        cbm_value: cbm_value,
        cbm_unit: "METER_CUBIC" as any,
        account_id: authUser.accountId,
        invoice_id: formData.invoice_id || undefined,
      };

      // First create the cargo without invoice association
      const { invoice_id, ...cargoDataWithoutInvoice } = cargoData;
      const result = await cargoService.createCargo(cargoDataWithoutInvoice);

      if (result.success && result.data) {
        const createdCargo = result.data;
        const entityName = entityType === "customer" ? "customer" : "supplier";

        // Handle invoice association for customers using handleInvoiceUpsert
        if (entityType === "customer") {
          try {
            await cargoService.handleInvoiceUpsert(
              {
                ...cargoDataWithoutInvoice,
                tracking_number: createdCargo.tracking_number,
                invoice_id: formData.invoice_id || undefined,
              },
              formData.customer_id || "",
              "",
              createdCargo.id
            );

            if (formData.invoice_id) {
              const selectedInvoice = invoices.find(
                (inv) => inv.id === formData.invoice_id
              );
              toast.success("Cargo Created Successfully", {
                description: `Cargo has been created for ${entityName} and added as a line item to Invoice ${selectedInvoice?.inv_number || formData.invoice_id}. Invoice total has been updated.`,
              });
            } else {
              toast.success("Cargo Created Successfully", {
                description: `Cargo has been created for ${entityName} and a new invoice has been automatically generated.`,
              });
            }
          } catch (error) {
            toast.warning("Cargo Created with Invoice Warning", {
              description: `Cargo created successfully, but failed to handle invoice. Please manage invoice manually.`,
            });
          }
        } else {
          toast.success("Cargo Created Successfully", {
            description: `Cargo has been created for ${entityName}. Note: Invoices are not automatically generated for supplier cargo.`,
          });
        }

        onSuccess();
        onOpenChange(false);
        setFormData(INITIAL_FORM_DATA);
        setEntityType("customer");
      } else {
        toast.error("Error Creating Cargo", {
          description:
            result.error || "Failed to create cargo. Please try again.",
        });
      }
    } catch (error) {
      toast.error("Failed to Create Cargo", {
        description: "An unexpected error occurred. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="w-4xl grid gap-5 py-4">
      {/* Tab Selector for Customer/Supplier */}
      <div className="mb-3">
        <AnimatedTabGroup
          tabs={[
            {
              id: "customer",
              label: "Customer",
              content: (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">
                    Customer{" "}
                    {loadingCustomers && (
                      <Loader2 size={12} className="inline animate-spin ml-1" />
                    )}
                  </label>
                  <Select
                    value={formData.customer_id}
                    onValueChange={(value) =>
                      handleInputChange("customer_id", value)
                    }
                    required
                  >
                    <SelectTrigger className="w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white text-gray-900 shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary data-[placeholder]:text-gray-500 h-10">
                      <SelectValue placeholder="Select Customer" />
                    </SelectTrigger>
                    <SelectContent>
                      {customers.map((customer) => (
                        <SelectItem key={customer.id} value={customer.id}>
                          {customer.name}{" "}
                          {customer.email && `(${customer.email})`}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              ),
            },
            {
              id: "supplier",
              label: "Supplier",
              content: (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">
                    Supplier{" "}
                    {loadingSuppliers && (
                      <Loader2 size={12} className="inline animate-spin ml-1" />
                    )}
                  </label>
                  <Select
                    value={formData.supplier_id}
                    onValueChange={(value) =>
                      handleInputChange("supplier_id", value)
                    }
                    required
                  >
                    <SelectTrigger className="w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white text-gray-900 shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary data-[placeholder]:text-gray-500 h-10">
                      <SelectValue placeholder="Select Supplier" />
                    </SelectTrigger>
                    <SelectContent>
                      {suppliers.map((supplier) => (
                        <SelectItem key={supplier.id} value={supplier.id}>
                          {supplier.tracking_number ||
                            supplier.phone ||
                            supplier.location ||
                            `Supplier ${supplier.id}`}
                          {supplier.phone && ` (${supplier.phone})`}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              ),
            },
          ]}
          defaultTabId={entityType}
          onTabChange={(tabId: string) =>
            handleEntityTypeChange(tabId as "customer" | "supplier")
          }
          className="border border-gray-200 rounded-lg bg-gray-50 p-2"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
        <InputField
          id="description"
          label="Cargo Description"
          placeholder="e.g: Electronics, 10 pallets"
          value={formData.particular}
          onChange={(e) => handleInputChange("particular", e.target.value)}
        />
        <div>
          <SelectField
            id="cargoType"
            label="Cargo Type"
            placeholder="Select Cargo Type"
            value={formData.type}
            options={[
              {
                value: Cargo_Categories.DANGEROUS,
                label: CARGO_CATEGORY_LABELS[Cargo_Categories.DANGEROUS],
              },
              {
                value: Cargo_Categories.SAFE,
                label: CARGO_CATEGORY_LABELS[Cargo_Categories.SAFE],
              },
            ]}
            onChange={(value) => handleInputChange("type", value)}
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
        <InputField
          id="china_tracking_number"
          label="China Tracking Number"
          placeholder="e.g: Previous tracking reference (optional)"
          value={formData.china_tracking_number}
          onChange={(e) =>
            handleInputChange("china_tracking_number", e.target.value)
          }
        />
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium text-gray-700">
            Freight Details (Optional)
          </h3>
          <span className="text-xs text-gray-500">
            Dimensions help with batch optimization
          </span>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-5">
          <InputField
            id="length"
            label="Length (cm)"
            type="number"
            step="0.01"
            placeholder="0"
            value={formData.dimensionLength}
            onChange={(e) =>
              handleInputChange("dimensionLength", e.target.value)
            }
          />
          <InputField
            id="width"
            label="Width (cm)"
            type="number"
            step="0.01"
            placeholder="0"
            value={formData.dimensionWidth}
            onChange={(e) =>
              handleInputChange("dimensionWidth", e.target.value)
            }
          />
          <InputField
            id="height"
            label="Height (cm)"
            type="number"
            step="0.01"
            placeholder="0"
            value={formData.dimensionHeight}
            onChange={(e) =>
              handleInputChange("dimensionHeight", e.target.value)
            }
          />
          <InputField
            id="ctn"
            label="CTN"
            type="number"
            placeholder="0"
            value={formData.CTN}
            onChange={(e) => handleInputChange("CTN", e.target.value)}
          />
          <InputField
            id="cbm"
            label="CBM (calculated)"
            value={calculateCBM().toFixed(4)}
            readOnly={true}
            className="bg-gray-50"
            placeholder="Auto-calculated"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
        <SelectField
          id="weightUnit"
          label="Weight Unit"
          placeholder="Select weight unit"
          value={formData.weightUnit}
          options={[
            { value: "KILOGRAMS", label: "Kilograms (kg)" },
            { value: "POUNDS", label: "Pounds (lb)" },
            { value: "GRAMS", label: "Grams (g)" },
            { value: "TONS", label: "Tons (t)" },
            { value: "OUNCES", label: "Ounces (oz)" },
            { value: "SHORT_TON", label: "Short Ton" },
            { value: "LONG_TON", label: "Long Ton" },
          ]}
          onChange={(value) => handleInputChange("weightUnit", value)}
        />
        <InputField
          id="weight"
          label={`Weight (${formData.weightUnit.toLowerCase()})`}
          type="number"
          step="0.01"
          placeholder="0"
          value={formData.weightValue}
          onChange={(e) => handleInputChange("weightValue", e.target.value)}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
        <InputField
          id="quantity"
          label="Quantity"
          type="number"
          placeholder="1"
          value={formData.quantity}
          onChange={(e) => handleInputChange("quantity", e.target.value)}
          min="1"
        />
        <InputField
          id="unit_price"
          label="Unit Price (USD)"
          type="number"
          step="0.01"
          placeholder="0.00"
          value={formData.unit_price}
          onChange={(e) => handleInputChange("unit_price", e.target.value)}
          required
        />
        <InputField
          id="totalPrice"
          label={`Total (USD) / (${factor} × Qty)`}
          type="number"
          step="0.01"
          placeholder="0.00"
          value={formData.totalPrice}
          readOnly={true}
          onChange={(e) => handleInputChange("totalPrice", e.target.value)}
        />
      </div>

      {/* Invoice Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium text-gray-700">
            Invoice Management
          </h3>
          <span className="text-xs text-gray-500">
            {entityType === "customer"
              ? "Link to existing invoice or create new"
              : "Automatic invoice creation for suppliers"}
          </span>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1.5">
            {entityType === "customer"
              ? "Select Invoice (Optional)"
              : "Invoice Creation"}{" "}
            {loadingInvoices && (
              <Loader2 size={12} className="inline animate-spin ml-1" />
            )}
          </label>
          <Select
            value={formData.invoice_id}
            onValueChange={(value) => {
              handleInputChange("invoice_id", value);
              if (value) {
                const selectedInvoice = invoices.find(
                  (inv) => inv.id === value
                );
                if (selectedInvoice) {
                  toast.info("Invoice Selected", {
                    description: `New cargo data will be added as a line item to Invoice ${selectedInvoice.inv_number}, affecting the total invoice amount.`,
                  });
                }
              }
            }}
            disabled={
              (entityType === "customer" && !formData.customer_id) ||
              (entityType === "supplier" && !formData.supplier_id) ||
              loadingInvoices
            }
          >
            <SelectTrigger className="w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white text-gray-900 shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary data-[placeholder]:text-gray-500 h-10">
              <SelectValue
                placeholder={
                  entityType === "customer" && !formData.customer_id
                    ? "Select customer first"
                    : entityType === "supplier" && !formData.supplier_id
                      ? "Select supplier first"
                      : invoices.length === 0
                        ? "No invoices found - will create new"
                        : "Select existing invoice or leave empty for new"
                }
              />
            </SelectTrigger>
            <SelectContent>
              {invoices.map((invoice) => (
                <SelectItem key={invoice.id} value={invoice.id}>
                  {invoice.inv_number} - ${invoice.total?.toFixed(2) || "0.00"}{" "}
                  ({invoice.status})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {entityType === "customer" &&
            formData.customer_id &&
            invoices.length === 0 &&
            !loadingInvoices && (
              <p className="text-xs text-gray-500 mt-1">
                No existing invoices found for this customer. A new invoice will
                be created automatically.
              </p>
            )}
          {entityType === "customer" && formData.invoice_id && (
            <p className="text-xs text-amber-600 mt-1">
              ⚠️ This cargo will be added as a line item to the selected
              invoice, updating the total amount.
            </p>
          )}
          {entityType === "supplier" && formData.supplier_id && (
            <p className="text-xs text-blue-600 mt-1">
              ℹ️ A new invoice will be created automatically for this supplier
              cargo. Supplier invoices are used for tracking and record-keeping
              purposes.
            </p>
          )}
        </div>
      </div>

      <DialogFooter className="mt-4 pt-4 border-t border-gray-200">
        <DialogClose asChild>
          <Button
            type="button"
            variant="outline"
            className="px-4 py-2 text-sm border-gray-200 text-gray-700 hover:bg-gray-50 rounded-md"
            disabled={isSubmitting}
          >
            Cancel
          </Button>
        </DialogClose>
        <Button
          type="submit"
          className="px-4 py-2 text-sm bg-primary text-white rounded-md hover:bg-primary/90"
          disabled={isSubmitting || !formData.type}
        >
          {isSubmitting ? (
            <Loader2 size={16} className="animate-spin mr-2" />
          ) : null}
          {isSubmitting ? "Creating..." : "Confirm & Create"}
        </Button>
      </DialogFooter>
    </form>
  );
};
