"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alogT<PERSON>le,
  DialogClose,
} from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Loader2, FileText, Phone, MapPin, Truck } from "lucide-react";
import { toast } from "sonner";
import { supplierService, type SupplierInsert } from "@/lib/logistics";
import { useAppSelector } from "@/store/hooks";

interface CreateSupplierFormProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

interface SupplierFormData {
  tracking_number: string;
  phone: string;
  location: string;
  status: "ACTIVE" | "INACTIVE" | "PENDING";
}

interface SupplierFormErrors {
  tracking_number?: string;
  phone?: string;
  location?: string;
  status?: string;
}

const InputField = ({
  id,
  label,
  icon: Icon,
  value,
  onChange,
  error,
  placeholder,
  type = "text",
}: {
  id: string;
  label: string;
  icon?: React.ElementType;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  error?: string;
  placeholder?: string;
  type?: string;
}) => (
  <div>
    <Label htmlFor={id} className="block text-xs font-medium text-gray-700 mb-1">
      {label}
    </Label>
    <div className="relative">
      {Icon && (
        <Icon className="absolute left-2.5 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-gray-400" />
      )}
      <Input
        id={id}
        type={type}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        className={`h-8 text-xs ${Icon ? "pl-8" : ""} ${
          error ? "border-red-300 focus:border-red-500" : ""
        }`}
      />
    </div>
    {error && <p className="text-xs text-red-600 mt-1">{error}</p>}
  </div>
);

export const CreateSupplierForm: React.FC<CreateSupplierFormProps> = ({
  isOpen,
  onOpenChange,
  onSuccess,
}) => {
  const { user: authUser } = useAppSelector((state) => state.auth);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<SupplierFormData>({
    tracking_number: "",
    phone: "",
    location: "",
    status: "ACTIVE",
  });
  const [formErrors, setFormErrors] = useState<SupplierFormErrors>({});

  const resetForm = () => {
    setFormData({
      tracking_number: "",
      phone: "",
      location: "",
      status: "ACTIVE",
    });
    setFormErrors({});
  };

  const handleInputChange = (field: keyof SupplierFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const errors: SupplierFormErrors = {};

    if (!formData.tracking_number.trim()) {
      errors.tracking_number = "Tracking number is required";
    }

    if (!formData.phone.trim()) {
      errors.phone = "Phone number is required";
    }

    if (!formData.location.trim()) {
      errors.location = "Location is required";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleCreateSupplier = async () => {
    if (!authUser || !validateForm()) return;

    setLoading(true);
    setFormErrors({});

    try {
      const supplierData: SupplierInsert = {
        ...formData,
      };

      const result = await supplierService.createSupplier(supplierData);

      if (result.success) {
        toast.success("Supplier Created Successfully", {
          description: `Supplier ${formData.tracking_number} has been created.`,
        });
        onOpenChange(false);
        resetForm();
        onSuccess();
      } else {
        setFormErrors({
          tracking_number: result.error || "Failed to create supplier",
        });
        toast.error("Error Creating Supplier", {
          description: result.error || "Failed to create supplier. Please try again.",
        });
      }
    } catch (error: any) {
      const errorMessage = error.message || "An unexpected error occurred";
      setFormErrors({
        tracking_number: errorMessage,
      });
      toast.error("Failed to Create Supplier", {
        description: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleCreateSupplier();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md p-0 mx-auto">
        <DialogHeader className="px-4 py-3 border-b border-gray-100">
          <DialogTitle className="text-lg font-medium text-gray-900 text-center flex items-center justify-center gap-2">
            <Truck size={18} className="text-primary" />
            Add Supplier
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="p-4">
          <div className="space-y-3">
            <InputField
              id="tracking_number"
              label="Tracking Number"
              icon={FileText}
              value={formData.tracking_number}
              onChange={(e) =>
                handleInputChange("tracking_number", e.target.value)
              }
              error={formErrors.tracking_number}
              placeholder="Enter tracking number"
            />
            <InputField
              id="phone"
              label="Phone"
              icon={Phone}
              value={formData.phone}
              onChange={(e) => handleInputChange("phone", e.target.value)}
              error={formErrors.phone}
              placeholder="+****************"
            />
            <InputField
              id="location"
              label="Location"
              icon={MapPin}
              value={formData.location}
              onChange={(e) => handleInputChange("location", e.target.value)}
              error={formErrors.location}
              placeholder="City, Country"
            />
            <div>
              <Label className="block text-xs font-medium text-gray-700 mb-1">
                Status
              </Label>
              <Select
                value={formData.status}
                onValueChange={(value: "ACTIVE" | "INACTIVE" | "PENDING") =>
                  handleInputChange("status", value)
                }
              >
                <SelectTrigger className="h-8 text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ACTIVE">Active</SelectItem>
                  <SelectItem value="INACTIVE">Inactive</SelectItem>
                  <SelectItem value="PENDING">Pending</SelectItem>
                </SelectContent>
              </Select>
              {formErrors.status && (
                <p className="text-xs text-red-600 mt-1">{formErrors.status}</p>
              )}
            </div>
          </div>
          <div className="flex gap-2 mt-4">
            <DialogClose asChild>
              <Button
                type="button"
                variant="outline"
                className="flex-1 h-8 text-xs"
                disabled={loading}
              >
                Cancel
              </Button>
            </DialogClose>
            <Button
              type="submit"
              disabled={loading}
              className="flex-1 h-8 text-xs bg-primary text-white hover:bg-primary/90 disabled:opacity-50"
            >
              {loading ? (
                <Loader2 size={14} className="animate-spin" />
              ) : (
                "Add Supplier"
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
