# Export Dialog Component

A comprehensive dialog component for exporting data in Excel (.xlsx) or PDF (.pdf) formats with table view preview functionality.

## Features

- 🚀 **Dual Format Support**: Export to both Excel and PDF formats
- 📊 **Data Preview**: Built-in table preview of data before export
- 🎨 **Professional Styling**: Consistent with Shamwaa Logistics design system
- 📱 **Responsive Design**: Works on desktop and mobile devices
- 🔧 **Configurable**: Customizable column mapping, exclusions, and formatting
- ⚡ **Performance**: Efficient handling of large datasets
- 🎯 **Type Safe**: Full TypeScript support with proper type definitions

## Installation

The component uses the existing Excel module and PDF generator packages:

```bash
# Dependencies are already installed in the project
# - @/lib/excel (Excel generation)
# - @workspace/pdf-generator (PDF generation)
# - @workspace/ui/components/* (UI components)
```

## Basic Usage

### Simple Export Dialog

```tsx
import { ExportDialog } from "@/components/ui/export-dialog";
import { useExportDialog } from "@/components/ui/useExportDialog";

function MyComponent() {
  const { isOpen, data, config, openExportDialog, closeExportDialog } = useExportDialog();

  const handleExport = () => {
    openExportDialog(myData, {
      title: "My Data Export",
      filename: "my_data_export"
    });
  };

  return (
    <>
      <Button onClick={handleExport}>Export Data</Button>
      
      <ExportDialog
        isOpen={isOpen}
        onClose={closeExportDialog}
        data={data}
        {...config}
      />
    </>
  );
}
```

### Specialized Export Hooks

```tsx
import { 
  useInvoiceExportDialog,
  useCargoExportDialog,
  useTransactionExportDialog 
} from "@/components/ui/useExportDialog";

function InvoiceList() {
  const exportDialog = useInvoiceExportDialog();

  return (
    <>
      <Button onClick={() => exportDialog.openInvoiceExport(invoices)}>
        Export Invoices
      </Button>
      
      <ExportDialog
        isOpen={exportDialog.isOpen}
        onClose={exportDialog.closeExportDialog}
        data={exportDialog.data}
        {...exportDialog.config}
      />
    </>
  );
}
```

## Component Props

### ExportDialog Props

```tsx
interface ExportDialogProps {
  isOpen: boolean;                              // Dialog open state
  onClose: () => void;                          // Close handler
  data: Record<string, any>[];                  // Data to export
  title?: string;                               // Dialog title
  filename?: string;                            // Base filename
  excludeColumns?: string[];                    // Columns to exclude
  columnMapping?: Record<string, string>;       // Column name mapping
  onSuccess?: (type: 'excel' | 'pdf', filename: string) => void;  // Success callback
  onError?: (error: string) => void;            // Error callback
}
```

### Configuration Options

```tsx
interface ExportDialogConfig {
  title?: string;                               // Export title
  filename?: string;                            // Base filename
  excludeColumns?: string[];                    // Columns to exclude from export
  columnMapping?: Record<string, string>;       // Map column names for display
  onSuccess?: (type: 'excel' | 'pdf', filename: string) => void;
  onError?: (error: string) => void;
}
```

## Available Hooks

### useExportDialog()
Basic hook for managing export dialog state.

### useInvoiceExportDialog()
Pre-configured for invoice data with appropriate column mappings.

### useCargoExportDialog()
Pre-configured for cargo manifest data.

### useTransactionExportDialog()
Pre-configured for financial transaction data.

### useShipmentExportDialog()
Pre-configured for shipment tracking data.

### useCustomerExportDialog()
Pre-configured for customer directory data.

### useGenericExportDialog()
Flexible hook for any data type with custom configuration.

## Integration Examples

### Adding to Table Headers

```tsx
function DataTable({ data }) {
  const exportDialog = useExportDialog();

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2>Data Table</h2>
        <Button onClick={() => exportDialog.openExportDialog(data)}>
          Export
        </Button>
      </div>
      
      {/* Your table component */}
      
      <ExportDialog
        isOpen={exportDialog.isOpen}
        onClose={exportDialog.closeExportDialog}
        data={exportDialog.data}
        {...exportDialog.config}
      />
    </div>
  );
}
```

### Bulk Actions Integration

```tsx
function BulkActions({ selectedIds, allData }) {
  const exportDialog = useExportDialog();

  const handleExportSelected = () => {
    const selectedData = allData.filter(item => selectedIds.includes(item.id));
    exportDialog.openExportDialog(selectedData, {
      title: "Selected Items Export",
      filename: `selected_items_${selectedIds.length}`
    });
  };

  return (
    <div className="flex gap-2">
      <Button onClick={handleExportSelected} disabled={selectedIds.length === 0}>
        Export Selected ({selectedIds.length})
      </Button>
      
      <ExportDialog
        isOpen={exportDialog.isOpen}
        onClose={exportDialog.closeExportDialog}
        data={exportDialog.data}
        {...exportDialog.config}
      />
    </div>
  );
}
```

### Management Hook Integration

```tsx
// In your management hook
function useInvoiceManagement() {
  const exportDialog = useInvoiceExportDialog();
  
  const handleBulkExport = useCallback((selectedIds: string[]) => {
    const selectedData = invoices.filter(invoice => selectedIds.includes(invoice.id));
    exportDialog.openInvoiceExport(selectedData);
  }, [invoices, exportDialog]);

  return {
    // ... existing returns
    handleBulkExport,
    exportDialog
  };
}

// In your component
function InvoiceManagement() {
  const { handleBulkExport, exportDialog } = useInvoiceManagement();

  return (
    <>
      <Button onClick={() => handleBulkExport(selectedIds)}>
        Export Selected
      </Button>
      
      <ExportDialog
        isOpen={exportDialog.isOpen}
        onClose={exportDialog.closeExportDialog}
        data={exportDialog.data}
        {...exportDialog.config}
      />
    </>
  );
}
```

## Customization

### Column Mapping

```tsx
const columnMapping = {
  'inv_number': 'Invoice Number',
  'customer_name': 'Customer',
  'created_at': 'Created Date',
  'total': 'Total Amount'
};

exportDialog.openExportDialog(data, {
  columnMapping,
  excludeColumns: ['id', 'metadata']
});
```

### Custom Formatting

The component automatically handles:
- Date formatting for ISO date strings
- JSON stringification for objects
- Null/undefined value handling
- Column header formatting (camelCase → Title Case)

### Success/Error Handling

```tsx
const handleSuccess = (type: 'excel' | 'pdf', filename: string) => {
  showToast(`${type.toUpperCase()} export completed: ${filename}`);
  // Log analytics, update UI state, etc.
};

const handleError = (error: string) => {
  showToast(`Export failed: ${error}`, 'error');
  // Log error, show detailed error message, etc.
};

<ExportDialog
  onSuccess={handleSuccess}
  onError={handleError}
  // ... other props
/>
```

## File Formats

### Excel (.xlsx)
- Preserves data types (numbers, dates, text)
- Includes column headers with proper formatting
- Supports large datasets efficiently
- Compatible with Excel, Google Sheets, etc.

### PDF (.pdf)
- Professional table formatting
- Automatic page breaks for large datasets
- Headers and footers with metadata
- Landscape orientation for wide tables
- Print-ready formatting

## Performance Considerations

- **Large Datasets**: Component handles datasets up to 10,000 rows efficiently
- **Memory Usage**: PDF generation uses more memory than Excel
- **Browser Limits**: Very large exports may hit browser memory limits
- **Preview Limit**: Data preview shows only first 10 rows for performance

## Browser Compatibility

- Modern browsers with ES2017+ support
- File download requires user interaction (security requirement)
- Excel export works in all modern browsers
- PDF export requires Canvas API support

## Troubleshooting

### Common Issues

1. **Export button not working**: Ensure data is not empty
2. **PDF formatting issues**: Check for very wide tables (use landscape)
3. **Excel file corruption**: Verify data doesn't contain invalid characters
4. **Memory errors**: Reduce dataset size or use pagination

### Error Messages

- "No data to export": Data array is empty or undefined
- "Export failed": Generic error, check browser console
- "PDF generation failed": Usually memory or data formatting issue
- "Excel generation failed": Data structure or formatting issue

## Contributing

When extending the export functionality:

1. Follow existing TypeScript patterns
2. Add proper error handling
3. Update documentation
4. Test with various data sizes
5. Ensure accessibility compliance
