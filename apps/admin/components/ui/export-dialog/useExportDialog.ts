/**
 * Hook for managing export dialog state and functionality
 */

import { useState, useCallback } from "react";

export interface ExportDialogConfig {
  title?: string;
  filename?: string;
  excludeColumns?: string[];
  columnMapping?: Record<string, string>;
  onSuccess?: (type: "excel" | "pdf", filename: string) => void;
  onError?: (error: string) => void;
}

export function useExportDialog() {
  const [isOpen, setIsOpen] = useState(false);
  const [data, setData] = useState<Record<string, any>[]>([]);
  const [config, setConfig] = useState<ExportDialogConfig>({});

  const openExportDialog = useCallback(
    (exportData: Record<string, any>[], exportConfig?: ExportDialogConfig) => {
      setData(exportData);
      setConfig(exportConfig || {});
      setIsOpen(true);
    },
    []
  );

  const closeExportDialog = useCallback(() => {
    setIsOpen(false);
    setData([]);
    setConfig({});
  }, []);

  return {
    isOpen,
    data,
    config,
    openExportDialog,
    closeExportDialog,
  };
}

/**
 * Specialized hooks for common export scenarios
 */

// Invoice export hook
export function useInvoiceExportDialog() {
  const dialog = useExportDialog();

  const openInvoiceExport = useCallback(
    (invoices: any[]) => {
      dialog.openExportDialog(invoices, {
        title: "Invoice Export",
        filename: `invoices_${new Date().toISOString().split("T")[0]}`,
        excludeColumns: ["line_items", "attachments", "metadata"],
        columnMapping: {
          inv_number: "Invoice Number",
          customer_name: "Customer",
          created_at: "Created Date",
          due_at: "Due Date",
          total: "Total Amount",
          subtotal: "Subtotal",
          status: "Status",
        },
      });
    },
    [dialog]
  );

  return {
    ...dialog,
    openInvoiceExport,
  };
}

// Cargo export hook
export function useCargoExportDialog() {
  const dialog = useExportDialog();

  const openCargoExport = useCallback(
    (cargo: any[]) => {
      dialog.openExportDialog(cargo, {
        title: "Cargo Manifest",
        filename: `cargo_manifest_${new Date().toISOString().split("T")[0]}`,
        excludeColumns: ["metadata", "attachments"],
        columnMapping: {
          tracking_number: "Tracking Number",
          china_tracking_number: "China Tracking",
          description: "Description",
          customer_name: "Customer",
          weight_value: "Weight (kg)",
          cbm_value: "Volume (m³)",
          quantity: "Quantity",
          status: "Status",
          code: "Batch Code",
          created_at: "Created Date",
        },
      });
    },
    [dialog]
  );

  return {
    ...dialog,
    openCargoExport,
  };
}

// Transaction export hook
export function useTransactionExportDialog() {
  const dialog = useExportDialog();

  const openTransactionExport = useCallback(
    (transactions: any[]) => {
      dialog.openExportDialog(transactions, {
        title: "Transaction Report",
        filename: `transactions_${new Date().toISOString().split("T")[0]}`,
        excludeColumns: ["metadata", "attachments"],
        columnMapping: {
          type: "Type",
          amount: "Amount",
          description: "Description",
          created_at: "Date",
          ledger_name: "Ledger",
          status: "Status",
          reference: "Reference",
        },
      });
    },
    [dialog]
  );

  return {
    ...dialog,
    openTransactionExport,
  };
}

// Shipment export hook
export function useShipmentExportDialog() {
  const dialog = useExportDialog();

  const openShipmentExport = useCallback(
    (shipments: any[]) => {
      dialog.openExportDialog(shipments, {
        title: "Shipment Tracking Report",
        filename: `shipments_${new Date().toISOString().split("T")[0]}`,
        excludeColumns: ["metadata", "attachments"],
        columnMapping: {
          tracking_number: "Tracking Number",
          bill_of_lading: "Bill of Lading",
          freight_name: "Freight",
          code: "Batch Code",
          status: "Status",
          current_location: "Current Location",
          origin: "Origin",
          destination: "Destination",
          created_at: "Created Date",
          updated_at: "Last Updated",
        },
      });
    },
    [dialog]
  );

  return {
    ...dialog,
    openShipmentExport,
  };
}

// Customer export hook
export function useCustomerExportDialog() {
  const dialog = useExportDialog();

  const openCustomerExport = useCallback(
    (customers: any[]) => {
      dialog.openExportDialog(customers, {
        title: "Customer Directory",
        filename: `customers_${new Date().toISOString().split("T")[0]}`,
        excludeColumns: ["metadata", "attachments"],
        columnMapping: {
          name: "Customer Name",
          email: "Email Address",
          phone: "Phone Number",
          location: "Location",
          company: "Company",
          status: "Status",
          created_at: "Registration Date",
        },
      });
    },
    [dialog]
  );

  return {
    ...dialog,
    openCustomerExport,
  };
}

// Generic data export hook with custom configuration
export function useGenericExportDialog() {
  const dialog = useExportDialog();

  const openGenericExport = useCallback(
    (
      data: any[],
      title: string,
      customConfig?: Partial<ExportDialogConfig>
    ) => {
      dialog.openExportDialog(data, {
        title,
        filename: `${title.toLowerCase().replace(/\s+/g, "_")}_${new Date().toISOString().split("T")[0]}`,
        ...customConfig,
      });
    },
    [dialog]
  );

  return {
    ...dialog,
    openGenericExport,
  };
}
