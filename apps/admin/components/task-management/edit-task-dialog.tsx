"use client";

import { useState, useEffect, useRef } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Combobox,
  ComboboxTrigger,
  ComboboxContent,
  ComboboxCommand,
  ComboboxInput,
  ComboboxList,
  ComboboxEmpty,
  ComboboxGroup,
  ComboboxItem,
  ComboboxItemIndicator,
} from "@workspace/ui/components/combobox";
import {
  Edit,
  Loader2,
  Calendar,
  User,
  Flag,
  Tag,
  Upload,
  X,
  FileText,
  Eye,
  Download,
  Trash2,
} from "lucide-react";
import {
  taskService,
  userService,
  documentService,
  type TaskUpdate,
  type TaskWithAssignee,
  type TaskPriorityEnum,
  type StatusEnum,
  type CategoryEnum,
  type DocumentWithAccount,
  type DocumentInsert,
} from "@/lib/logistics";
import { useAppSelector } from "@/store/hooks";
import { toast } from "sonner";
import {
  DocumentPreviewDialog,
  useDocumentPreview,
} from "@/components/ui/document-preview-dialog";
import {
  EntityAssociation,
  useEntityAssociation,
} from "@/components/ui/entity-association";
import { EntityAssociationViewer } from "@/components/ui/entity-association/EntityAssociationViewer";

interface EditTaskDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onTaskUpdated?: () => void;
  task: TaskWithAssignee | null;
}

// Priority options
const PRIORITY_OPTIONS: {
  value: TaskPriorityEnum;
  label: string;
  color: string;
}[] = [
  { value: "LOW", label: "Low", color: "text-green-600" },
  { value: "NORMAL", label: "Normal", color: "text-blue-600" },
  { value: "HIGH", label: "High", color: "text-orange-600" },
  { value: "URGENT", label: "Urgent", color: "text-red-600" },
];

// Status options
const STATUS_OPTIONS: { value: StatusEnum; label: string }[] = [
  { value: "CREATED", label: "Created" },
  { value: "PENDING", label: "Pending" },
  { value: "PROCESSING", label: "In Progress" },
  { value: "COMPLETED", label: "Completed" },
  { value: "CANCELLED", label: "Cancelled" },
];

export function EditTaskDialog({
  isOpen,
  onClose,
  onTaskUpdated,
  task,
}: EditTaskDialogProps) {
  const [isUpdating, setIsUpdating] = useState(false);
  const [formData, setFormData] = useState<TaskUpdate>({});

  // Users for assignee selection
  const [users, setUsers] = useState<any[]>([]);
  const [usersLoading, setUsersLoading] = useState(false);
  const [assigneeComboboxOpen, setAssigneeComboboxOpen] = useState(false);

  // Entity association hook
  const entityAssociation = useEntityAssociation();

  // Ref to track if entity association has been initialized for current task
  const entityAssociationInitialized = useRef<string | null>(null);

  // Document attachment state
  const [attachedFiles, setAttachedFiles] = useState<File[]>([]);
  const [existingDocuments, setExistingDocuments] = useState<
    DocumentWithAccount[]
  >([]);
  const [documentsLoading, setDocumentsLoading] = useState(false);
  const [deletingDocuments, setDeletingDocuments] = useState<Set<string>>(
    new Set()
  );

  const { user: authUser } = useAppSelector((state) => state.auth);

  // Document preview hook
  const {
    isOpen: isPreviewOpen,
    document: previewDocument,
    openPreview,
    closePreview,
  } = useDocumentPreview();

  // Initialize form data when task changes
  useEffect(() => {
    if (task && isOpen) {
      setFormData({
        name: task.name,
        status: task.status,
        priority: task.priority,
        category: task.category,
        assignee: task.assignee,
        start: task.start,
        due: task.due,
        associated_table: task.associated_table,
        associated_id: task.associated_id,
        associated_ids: task.associated_ids,
      });

      // Initialize entity association hook with existing values (only once per task)
      if (entityAssociationInitialized.current !== task.id) {
        entityAssociationInitialized.current = task.id;

        if (task.associated_table) {
          entityAssociation.handleTableChange(task.associated_table);

          // Set the ID(s) with a more robust approach
          const initializeSelection = () => {
            if (task.associated_ids && task.associated_ids.length > 0) {
              // Multiple IDs - use multiselect mode (primary mode)
              entityAssociation.handleIdsChange(task.associated_ids);
            } else if (task.associated_id) {
              // Single ID - convert to array for multiselect consistency
              entityAssociation.handleIdsChange([task.associated_id]);
            }
          };

          // Try immediate initialization first
          initializeSelection();

          // Also set with delay as fallback to ensure entities are loaded
          setTimeout(initializeSelection, 300);
        } else {
          entityAssociation.reset();
        }
      }

      // Fetch existing documents
      fetchTaskDocuments(task.id);

      // Reset new file attachments
      setAttachedFiles([]);
    }
  }, [task, isOpen]);

  // Fetch users for assignee selection
  const fetchUsers = async () => {
    try {
      setUsersLoading(true);
      const result = await userService.getUsersForAssignment([], {
        limit: 100,
      });
      if (result.success && result.data) {
        setUsers(result.data);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
      toast.error("Failed to load users");
    } finally {
      setUsersLoading(false);
    }
  };

  // Update form data when entity association changes
  useEffect(() => {
    setFormData((prev) => {
      // Only update if values actually changed to prevent unnecessary re-renders
      if (
        prev.associated_table !== entityAssociation.result.association_table ||
        JSON.stringify(prev.associated_ids) !==
          JSON.stringify(entityAssociation.result.association_ids)
      ) {
        return {
          ...prev,
          associated_table: entityAssociation.result.association_table,
          // For multiselect mode, prioritize associated_ids
          associated_id:
            entityAssociation.result.association_ids &&
            entityAssociation.result.association_ids.length === 1
              ? entityAssociation.result.association_ids[0]
              : null,
          associated_ids: entityAssociation.result.association_ids,
        };
      }
      return prev;
    });
  }, [
    entityAssociation.result.association_table,
    entityAssociation.result.association_ids,
  ]);

  // Re-initialize selection when entities are loaded and we have existing associations
  useEffect(() => {
    if (
      task &&
      entityAssociation.state.entities.length > 0 &&
      entityAssociation.state.selectedTable &&
      entityAssociation.state.selectedIds.length === 0 && // Only if not already selected
      (task.associated_ids?.length > 0 || task.associated_id)
    ) {
      // Re-apply the selection now that entities are loaded
      if (task.associated_ids && task.associated_ids.length > 0) {
        entityAssociation.handleIdsChange(task.associated_ids);
      } else if (task.associated_id) {
        entityAssociation.handleIdsChange([task.associated_id]);
      }
    }
  }, [
    entityAssociation.state.entities.length,
    entityAssociation.state.selectedTable,
    entityAssociation.state.selectedIds.length,
    task?.associated_ids,
    task?.associated_id,
    entityAssociation.handleIdsChange,
  ]);

  // Fetch existing task documents
  const fetchTaskDocuments = async (taskId: string) => {
    try {
      setDocumentsLoading(true);
      const result = await documentService.getDocumentsByEntity(
        "tasks",
        taskId
      );
      if (result.success && result.data) {
        setExistingDocuments(result.data);
      } else {
        console.error("Failed to fetch task documents:", result.error);
      }
    } catch (error) {
      console.error("Error fetching task documents:", error);
    } finally {
      setDocumentsLoading(false);
    }
  };

  // File upload handlers
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    addFiles(files);
    // Reset input
    event.target.value = "";
  };

  const addFiles = (files: File[]) => {
    // Filter out files that are too large (10MB limit)
    const validFiles = files.filter((file) => {
      if (file.size > 10 * 1024 * 1024) {
        toast.error(`File ${file.name} is too large. Maximum size is 10MB.`);
        return false;
      }
      return true;
    });

    setAttachedFiles((prev) => [...prev, ...validFiles]);
  };

  const removeAttachedFile = (index: number) => {
    setAttachedFiles((prev) => prev.filter((_, i) => i !== index));
  };

  // Delete existing document
  const handleDeleteDocument = async (documentId: string) => {
    if (!authUser?.accountId) return;

    try {
      setDeletingDocuments((prev) => new Set(prev).add(documentId));

      const result = await documentService.deleteDocument(
        documentId,
        authUser.accountId
      );

      if (result.success) {
        setExistingDocuments((prev) =>
          prev.filter((doc) => doc.id !== documentId)
        );
        toast.success("Document deleted successfully");
      } else {
        toast.error(result.error || "Failed to delete document");
      }
    } catch (error) {
      console.error("Error deleting document:", error);
      toast.error("An error occurred while deleting the document");
    } finally {
      setDeletingDocuments((prev) => {
        const newSet = new Set(prev);
        newSet.delete(documentId);
        return newSet;
      });
    }
  };

  // Drag and drop handlers
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const files = Array.from(e.dataTransfer.files);
    addFiles(files);
  };

  // Get file type icon
  const getFileTypeIcon = (fileName: string) => {
    const extension = fileName.split(".").pop()?.toLowerCase();
    switch (extension) {
      case "pdf":
        return <FileText className="h-4 w-4 text-red-500" />;
      case "doc":
      case "docx":
        return <FileText className="h-4 w-4 text-blue-500" />;
      case "xls":
      case "xlsx":
        return <FileText className="h-4 w-4 text-green-500" />;
      case "jpg":
      case "jpeg":
      case "png":
      case "gif":
        return <FileText className="h-4 w-4 text-purple-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchUsers();
    }
  }, [isOpen]);

  const handleInputChange = (field: keyof TaskUpdate, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!task?.id) {
      toast.error("Task ID is required");
      return;
    }

    if (!formData.name?.trim()) {
      toast.error("Task name is required");
      return;
    }

    try {
      setIsUpdating(true);

      const result = await taskService.update(task.id, formData);

      if (result.success) {
        // Upload new documents if any are provided
        if (attachedFiles.length > 0 && authUser?.accountId) {
          let uploadedCount = 0;
          let failedCount = 0;

          for (const file of attachedFiles) {
            try {
              // Upload file to storage
              const uploadResult = await documentService.uploadToStorage({
                content: file,
                fileName: file.name,
                contentType: file.type,
                folder: "task-documents",
                metadata: {
                  taskId: task.id,
                },
              });

              if (uploadResult.success && uploadResult.data) {
                // Create document record
                const documentData: DocumentInsert = {
                  name: file.name,
                  path: uploadResult.data,
                  category: "task",
                  description: `Document attached to task: ${formData.name}`,
                  associated_table: "tasks",
                  associated_id: task.id,
                  account_id: authUser.accountId,
                  status: "ACTIVE" as StatusEnum,
                };

                const createResult =
                  await documentService.createDocument(documentData);
                if (createResult.success) {
                  uploadedCount++;
                } else {
                  failedCount++;
                  console.warn(
                    `Failed to create document record for ${file.name}:`,
                    createResult.error
                  );
                }
              } else {
                failedCount++;
                console.warn(
                  `Failed to upload ${file.name}:`,
                  uploadResult.error
                );
              }
            } catch (error) {
              failedCount++;
              console.error(`Error uploading ${file.name}:`, error);
            }
          }

          // Refresh documents list
          await fetchTaskDocuments(task.id);

          if (failedCount > 0) {
            toast.warning(
              `Task updated successfully. ${uploadedCount} document(s) uploaded, ${failedCount} failed.`
            );
          } else {
            toast.success(
              `Task updated successfully with ${uploadedCount} new document(s)`
            );
          }
        } else {
          toast.success("Task updated successfully");
        }

        onTaskUpdated?.();
        onClose();
      } else {
        toast.error(result.error || "Failed to update task");
      }
    } catch (error: any) {
      console.error("Error updating task:", error);
      toast.error("An error occurred while updating the task");
    } finally {
      setIsUpdating(false);
    }
  };

  const handleClose = () => {
    // Reset entity association initialization tracking
    entityAssociationInitialized.current = null;
    onClose();
  };

  // Format datetime for input
  const formatDateTimeForInput = (dateString: string | null) => {
    if (!dateString) return "";
    return new Date(dateString).toISOString().slice(0, 16);
  };

  // Parse datetime from input
  const parseDateTimeFromInput = (value: string) => {
    return value ? new Date(value).toISOString() : null;
  };

  if (!task) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="text-xl font-semibold flex items-center gap-2">
            <Edit className="h-5 w-5" />
            Edit Task
          </DialogTitle>
          <p className="text-sm text-gray-500 mt-1">
            Update task details and assignments
          </p>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="flex-1 overflow-y-auto">
          <div className="space-y-6 p-1">
            {/* Task Name */}
            <div>
              <Label
                htmlFor="name"
                className="text-sm font-medium text-gray-700 mb-2"
              >
                Task Name *
              </Label>
              <Input
                id="name"
                value={formData.name || ""}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder="Enter task name"
                className="w-full"
                required
              />
            </div>

            {/* Status and Priority Row */}
            <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
              <div className="col-span-1">
                <Label className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-1">
                  <Tag className="h-4 w-4" />
                  Status
                </Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) =>
                    handleInputChange("status", value as StatusEnum)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {STATUS_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="col-span-1">
                <Label className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-1">
                  <Flag className="h-4 w-4" />
                  Priority
                </Label>
                <Select
                  value={formData.priority || "NORMAL"}
                  onValueChange={(value) =>
                    handleInputChange("priority", value as TaskPriorityEnum)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    {PRIORITY_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <span className={option.color}>{option.label}</span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="col-span-4">
                <Label className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-1">
                  <User className="h-4 w-4" />
                  Assignee
                </Label>
                <Combobox
                  open={assigneeComboboxOpen}
                  onOpenChange={setAssigneeComboboxOpen}
                >
                  <ComboboxTrigger
                    placeholder={
                      usersLoading
                        ? "Loading users..."
                        : "Select assignee (optional)"
                    }
                    className={
                      usersLoading ? "opacity-50 cursor-not-allowed" : ""
                    }
                    disabled={usersLoading}
                  >
                    {formData.assignee
                      ? (() => {
                          const selectedUser = users.find(
                            (user) =>
                              (user.accounts?.id || user.id) ===
                              formData.assignee
                          );
                          if (selectedUser) {
                            return `${selectedUser.name}${selectedUser.accounts?.email ? ` (${selectedUser.accounts.email})` : ""}`;
                          }
                          return "Unknown user";
                        })()
                      : usersLoading
                        ? "Loading users..."
                        : "Select assignee (optional)"}
                  </ComboboxTrigger>
                  <ComboboxContent className="max-h-[200px]">
                    <ComboboxCommand>
                      <ComboboxInput placeholder="Search users..." />
                      <ComboboxList>
                        {usersLoading ? (
                          <div className="flex items-center justify-center p-2">
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            Loading users...
                          </div>
                        ) : users.length === 0 ? (
                          <ComboboxEmpty>No users found.</ComboboxEmpty>
                        ) : (
                          <ComboboxGroup>
                            <ComboboxItem
                              value="none"
                              onSelect={() => {
                                handleInputChange("assignee", null);
                                setAssigneeComboboxOpen(false);
                              }}
                            >
                              No assignee
                              <ComboboxItemIndicator
                                isSelected={!formData.assignee}
                              />
                            </ComboboxItem>
                            {users.map((user) => (
                              <ComboboxItem
                                key={user.id}
                                value={user.accounts?.id || user.id}
                                onSelect={(currentValue) => {
                                  handleInputChange(
                                    "assignee",
                                    currentValue === formData.assignee
                                      ? null
                                      : currentValue
                                  );
                                  setAssigneeComboboxOpen(false);
                                }}
                              >
                                {user.name}{" "}
                                {user.accounts?.email &&
                                  `(${user.accounts.email})`}
                                <ComboboxItemIndicator
                                  isSelected={
                                    formData.assignee ===
                                    (user.accounts?.id || user.id)
                                  }
                                />
                              </ComboboxItem>
                            ))}
                          </ComboboxGroup>
                        )}
                      </ComboboxList>
                    </ComboboxCommand>
                  </ComboboxContent>
                </Combobox>
              </div>
            </div>

            {/* Start and Due Date Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  Start Date
                </Label>
                <Input
                  type="datetime-local"
                  value={formatDateTimeForInput(formData.start)}
                  onChange={(e) =>
                    handleInputChange(
                      "start",
                      parseDateTimeFromInput(e.target.value)
                    )
                  }
                  className="w-full"
                />
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  Due Date
                </Label>
                <Input
                  type="datetime-local"
                  value={formatDateTimeForInput(formData.due)}
                  onChange={(e) =>
                    handleInputChange(
                      "due",
                      parseDateTimeFromInput(e.target.value)
                    )
                  }
                  className="w-full"
                />
              </div>
            </div>
            {/* Entity Association Section */}
            <EntityAssociation
              entityAssociation={entityAssociation}
              title="Entity Association (Optional)"
              description="Associate this task with specific entities like cargo, shipments, or customers. Use multiselect to associate with multiple entities."
              layout="horizontal"
              required={false}
              showIcon={true}
              entityTypePlaceholder="Select entity type"
              entityPlaceholder="Select entity"
              multiselect={true}
              maxSelected={20}
              multiselectPlaceholder="Select entities to associate with this task..."
            />

            {/* Entity Association Viewer Section - Preview bulk selected components */}
            {task.associated_table &&
              task.associated_ids &&
              task.associated_ids.length > 0 && (
                <div>
                  <Label className="text-sm font-medium text-gray-700 mb-2">
                    Associated Entities ({task.associated_ids.length})
                  </Label>
                  <p className="text-xs text-gray-500 mb-3">
                    Preview of entities associated with this task
                  </p>
                  <div className="p-3 bg-gray-50 rounded-lg border">
                    <EntityAssociationViewer
                      entityTable={task.associated_table}
                      entityId={null}
                      entityIds={task.associated_ids}
                      showEntityType={true}
                      showViewLink={false}
                      maxDisplay={5}
                      showExpandButton={true}
                      sortBy="name"
                      sortDirection="asc"
                      className="space-y-2"
                    />
                  </div>
                </div>
              )}

            {/* Document Management Section */}
            <div>
              <Label className="text-sm font-medium text-gray-700 mb-2">
                <FileText className="h-4 w-4 inline mr-1" />
                Task Documents
              </Label>
              <p className="text-xs text-gray-500 mb-3">
                Manage documents attached to this task
              </p>

              {/* Existing Documents */}
              {documentsLoading ? (
                <div className="flex items-center justify-center py-4 bg-gray-50 rounded-lg mb-4">
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  <span className="text-sm text-gray-600">
                    Loading documents...
                  </span>
                </div>
              ) : existingDocuments.length > 0 ? (
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">
                    Existing Documents ({existingDocuments.length})
                  </h4>
                  <div className="space-y-2 p-3 bg-gray-50 rounded-lg">
                    {existingDocuments.map((doc) => (
                      <div
                        key={doc.id}
                        className="flex items-center justify-between p-2 bg-white rounded border"
                      >
                        <div className="flex items-center gap-2 flex-1">
                          {getFileTypeIcon(doc.file_name)}
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {doc.file_name}
                            </p>
                            <p className="text-xs text-gray-500">
                              {doc.file_size
                                ? `${(doc.file_size / 1024 / 1024).toFixed(2)} MB`
                                : "Unknown size"}{" "}
                              • Uploaded by {doc.accounts?.name || "Unknown"}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-1">
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => openPreview(doc)}
                            className="h-8 w-8 p-0 text-blue-500 hover:text-blue-700 hover:bg-blue-50"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => window.open(doc.file_url, "_blank")}
                            className="h-8 w-8 p-0 text-green-500 hover:text-green-700 hover:bg-green-50"
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteDocument(doc.id)}
                            disabled={deletingDocuments.has(doc.id)}
                            className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                          >
                            {deletingDocuments.has(doc.id) ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="mb-4 p-3 bg-gray-50 rounded-lg text-center">
                  <p className="text-sm text-gray-500">
                    No documents attached to this task
                  </p>
                </div>
              )}

              {/* New Files to Upload */}
              {attachedFiles.length > 0 && (
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">
                    New Files to Upload ({attachedFiles.length})
                  </h4>
                  <div className="space-y-2 p-3 bg-blue-50 rounded-lg">
                    {attachedFiles.map((file, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-2 bg-white rounded border"
                      >
                        <div className="flex items-center gap-2 flex-1">
                          {getFileTypeIcon(file.name)}
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {file.name}
                            </p>
                            <p className="text-xs text-gray-500">
                              {(file.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeAttachedFile(index)}
                          className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* File Upload Area */}
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors"
                onDragOver={handleDragOver}
                onDragEnter={handleDragEnter}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <input
                  type="file"
                  id="document-upload-edit"
                  className="hidden"
                  multiple
                  accept=".pdf,.doc,.docx,.xlsx,.xls,.png,.jpg,.jpeg"
                  onChange={handleFileSelect}
                  disabled={isUpdating}
                />
                <div className="space-y-2">
                  <Upload className="h-8 w-8 mx-auto text-gray-400" />
                  <div>
                    <button
                      type="button"
                      onClick={() =>
                        document.getElementById("document-upload-edit")?.click()
                      }
                      className="text-primary hover:text-primary/80 font-medium"
                      disabled={isUpdating}
                    >
                      Click to upload
                    </button>
                    <span className="text-gray-500"> or drag and drop</span>
                  </div>
                  <p className="text-xs text-gray-400">
                    PDF, DOC, XLS, PNG, JPG up to 10MB each
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex justify-end gap-3 pt-6 border-t mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isUpdating}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!formData.name?.trim() || isUpdating}
              className="flex items-center gap-2"
            >
              {isUpdating ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Edit className="h-4 w-4" />
              )}
              {isUpdating
                ? "Updating Task..."
                : `Update Task${attachedFiles.length > 0 ? ` & Upload ${attachedFiles.length} File${attachedFiles.length > 1 ? "s" : ""}` : ""}`}
            </Button>
          </div>
        </form>
      </DialogContent>

      {/* Document Preview Dialog */}
      <DocumentPreviewDialog
        isOpen={isPreviewOpen}
        onClose={closePreview}
        document={previewDocument}
        title="Task Document Preview"
      />
    </Dialog>
  );
}
