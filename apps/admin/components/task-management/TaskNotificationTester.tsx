"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import {
  <PERSON>,
  CardContent,
  Card<PERSON>eader,
  Card<PERSON>itle,
} from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import {
  <PERSON>,
  Clock,
  AlertTriangle,
  CheckCircle,
  Trash2,
  Play,
  Pause,
  RefreshCw,
} from "lucide-react";
import { taskDeadlineService } from "@/lib/services/taskDeadlineService";
import { taskService, notificationService } from "@/lib/logistics";
import { useAppSelector } from "@/store/hooks";
import { toast } from "sonner";

export function TaskNotificationTester() {
  const { user: authUser } = useAppSelector((state) => state.auth);
  const [isLoading, setIsLoading] = useState(false);
  const [serviceStatus, setServiceStatus] = useState(
    taskDeadlineService.getStatus()
  );

  // Mock task data for testing
  const mockTask = {
    id: "test-task-id",
    name: "Test Task for Notifications",
    status: "PENDING" as any,
    priority: "HIGH" as any,
    assignee: authUser?.accountId || "",
    account_id: authUser?.accountId || "",
    due: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Due tomorrow
    start: new Date().toISOString(),
    associated_table: null,
    associated_id: null,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    category: null,
    assignee_details: {
      id: authUser?.accountId || "",
      email: authUser?.email || "",
      user: {
        id: authUser?.id || "",
        name: authUser?.name || "Test User",
      },
    },
    creator_details: {
      id: authUser?.accountId || "",
      email: authUser?.email || "",
      user: {
        id: authUser?.id || "",
        name: authUser?.name || "Test User",
      },
    },
  };

  const handleTestNotification = async (type: string) => {
    if (!authUser?.accountId) {
      toast.error("Please sign in to test notifications");
      return;
    }

    setIsLoading(true);
    try {
      let message = "";
      let notificationName = "";

      switch (type) {
        case "assigned":
          notificationName = "Task Assigned";
          message = `You have been assigned a new task: "${mockTask.name}" by Test User`;
          break;
        case "updated":
          notificationName = "Task Updated";
          message = `Task "${mockTask.name}" has been updated (status, priority)`;
          break;
        case "deleted":
          notificationName = "Task Deleted";
          message = `Task "${mockTask.name}" has been deleted`;
          break;
        case "deadline":
          notificationName = "Task Deadline Approaching";
          message = `Task "${mockTask.name}" is due tomorrow!`;
          break;
        case "overdue":
          notificationName = "Task Overdue";
          message = `Task "${mockTask.name}" is 1 day overdue!`;
          break;
        default:
          toast.error("Unknown notification type");
          return;
      }

      // Create test notification directly
      const result = await notificationService.createTargetedNotification({
        account_id: authUser.accountId,
        name: notificationName,
        message,
        associated_table: "tasks",
        associated_id: mockTask.id,
        to: authUser.accountId,
        details: {
          taskId: mockTask.id,
          taskName: mockTask.name,
          priority: mockTask.priority,
          assigneeId: authUser.accountId,
          type: `task_${type}`,
          timestamp: new Date().toISOString(),
        },
      });

      if (result.success) {
        toast.success(`${notificationName} notification sent!`);
      } else {
        toast.error(`Failed to send notification: ${result.error}`);
      }
    } catch (error) {
      console.error("Error sending test notification:", error);
      toast.error("Failed to send test notification");
    } finally {
      setIsLoading(false);
    }
  };

  const handleServiceControl = (action: "start" | "stop") => {
    try {
      if (action === "start") {
        taskDeadlineService.start();
        toast.success("Task deadline service started");
      } else {
        taskDeadlineService.stop();
        toast.success("Task deadline service stopped");
      }
      setServiceStatus(taskDeadlineService.getStatus());
    } catch (error) {
      console.error("Error controlling service:", error);
      toast.error(`Failed to ${action} service`);
    }
  };

  const handleCleanupHistory = () => {
    try {
      taskDeadlineService.cleanupNotificationHistory();
      toast.success("Notification history cleaned up");
    } catch (error) {
      console.error("Error cleaning up history:", error);
      toast.error("Failed to cleanup history");
    }
  };

  const refreshStatus = () => {
    setServiceStatus(taskDeadlineService.getStatus());
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Task Notification System Tester
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Test the task notification system and manage the deadline service
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Service Status */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Deadline Service Status</h3>
            <Button variant="outline" size="sm" onClick={refreshStatus}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <p className="text-sm font-medium">Status</p>
              <Badge
                variant={serviceStatus.isRunning ? "default" : "secondary"}
              >
                {serviceStatus.isRunning ? "Running" : "Stopped"}
              </Badge>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium">Check Interval</p>
              <p className="text-sm text-muted-foreground">
                {serviceStatus.checkIntervalHours}h
              </p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium">Warning Days</p>
              <p className="text-sm text-muted-foreground">
                {serviceStatus.deadlineWarningDays?.join(", ")} days
              </p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium">Notifications</p>
              <Badge
                variant={
                  serviceStatus.enableNotifications ? "default" : "secondary"
                }
              >
                {serviceStatus.enableNotifications ? "Enabled" : "Disabled"}
              </Badge>
            </div>
          </div>

          <div className="flex gap-2">
            <Button
              variant={serviceStatus.isRunning ? "secondary" : "default"}
              size="sm"
              onClick={() => handleServiceControl("start")}
              disabled={serviceStatus.isRunning}
            >
              <Play className="h-4 w-4 mr-2" />
              Start Service
            </Button>
            <Button
              variant={serviceStatus.isRunning ? "destructive" : "secondary"}
              size="sm"
              onClick={() => handleServiceControl("stop")}
              disabled={!serviceStatus.isRunning}
            >
              <Pause className="h-4 w-4 mr-2" />
              Stop Service
            </Button>
            <Button variant="outline" size="sm" onClick={handleCleanupHistory}>
              <Trash2 className="h-4 w-4 mr-2" />
              Cleanup History
            </Button>
          </div>
        </div>

        {/* Test Notifications */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Test Notifications</h3>
          <p className="text-sm text-muted-foreground">
            Click the buttons below to test different types of task
            notifications
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Button
              variant="outline"
              className="h-auto p-4 flex flex-col items-center gap-2"
              onClick={() => handleTestNotification("assigned")}
              disabled={isLoading}
            >
              <CheckCircle className="h-6 w-6 text-green-500" />
              <span className="font-medium">Task Assigned</span>
              <span className="text-xs text-muted-foreground text-center">
                Test notification when a task is assigned to you
              </span>
            </Button>

            <Button
              variant="outline"
              className="h-auto p-4 flex flex-col items-center gap-2"
              onClick={() => handleTestNotification("updated")}
              disabled={isLoading}
            >
              <RefreshCw className="h-6 w-6 text-blue-500" />
              <span className="font-medium">Task Updated</span>
              <span className="text-xs text-muted-foreground text-center">
                Test notification when your task is updated
              </span>
            </Button>

            <Button
              variant="outline"
              className="h-auto p-4 flex flex-col items-center gap-2"
              onClick={() => handleTestNotification("deleted")}
              disabled={isLoading}
            >
              <Trash2 className="h-6 w-6 text-red-500" />
              <span className="font-medium">Task Deleted</span>
              <span className="text-xs text-muted-foreground text-center">
                Test notification when your task is deleted
              </span>
            </Button>

            <Button
              variant="outline"
              className="h-auto p-4 flex flex-col items-center gap-2"
              onClick={() => handleTestNotification("deadline")}
              disabled={isLoading}
            >
              <Clock className="h-6 w-6 text-orange-500" />
              <span className="font-medium">Deadline Approaching</span>
              <span className="text-xs text-muted-foreground text-center">
                Test notification for task due tomorrow
              </span>
            </Button>

            <Button
              variant="outline"
              className="h-auto p-4 flex flex-col items-center gap-2"
              onClick={() => handleTestNotification("overdue")}
              disabled={isLoading}
            >
              <AlertTriangle className="h-6 w-6 text-red-500" />
              <span className="font-medium">Task Overdue</span>
              <span className="text-xs text-muted-foreground text-center">
                Test notification for overdue task
              </span>
            </Button>
          </div>
        </div>

        {/* Instructions */}
        <div className="space-y-2 p-4 bg-muted rounded-lg">
          <h4 className="font-medium">How to Test:</h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• Click any notification button to send a test notification</li>
            <li>• Check the notifications page to see the new notifications</li>
            <li>
              • The deadline service automatically checks for due/overdue tasks
              every hour
            </li>
            <li>
              • Browser notifications require permission (check your browser
              settings)
            </li>
            <li>• Toast notifications appear in the bottom-right corner</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
