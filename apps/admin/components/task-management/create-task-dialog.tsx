"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";

import {
  Combobox,
  ComboboxTrigger,
  ComboboxContent,
  ComboboxCommand,
  ComboboxInput,
  ComboboxList,
  ComboboxEmpty,
  ComboboxGroup,
  ComboboxItem,
  ComboboxItemIndicator,
} from "@workspace/ui/components/combobox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Plus,
  Loader2,
  Calendar,
  User,
  Flag,
  Tag,
  Upload,
  X,
  FileText,
} from "lucide-react";
import {
  taskService,
  userService,
  documentService,
  type TaskInsert,
  type TaskPriorityEnum,
  type StatusEnum,
  type CategoryEnum,
  type DocumentInsert,
} from "@/lib/logistics";
import { useAppSelector } from "@/store/hooks";
import { toast } from "sonner";
import {
  DocumentPreviewDialog,
  useDocumentPreview,
} from "@/components/ui/document-preview-dialog";

interface CreateTaskDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onTaskCreated?: () => void;
}

// Priority options
const PRIORITY_OPTIONS: {
  value: TaskPriorityEnum;
  label: string;
  color: string;
}[] = [
  { value: "LOW", label: "Low", color: "text-green-600" },
  { value: "NORMAL", label: "Normal", color: "text-blue-600" },
  { value: "HIGH", label: "High", color: "text-orange-600" },
  { value: "URGENT", label: "Urgent", color: "text-red-600" },
];

// Status options
const STATUS_OPTIONS: { value: StatusEnum; label: string }[] = [
  { value: "CREATED", label: "Created" },
  { value: "PENDING", label: "Pending" },
  { value: "PROCESSING", label: "In Progress" },
  { value: "COMPLETED", label: "Completed" },
  { value: "CANCELLED", label: "Cancelled" },
];

// Category options
const CATEGORY_OPTIONS: { value: CategoryEnum; label: string }[] = [
  { value: "DANGEROUS", label: "Dangerous" },
  { value: "SAFE", label: "Safe" },
  { value: "AIR", label: "Air" },
  { value: "SEA", label: "Sea" },
  { value: "ROAD", label: "Road" },
];

// Import entity association components
import {
  EntityAssociation,
  useEntityAssociation,
} from "@/components/ui/entity-association";

export function CreateTaskDialog({
  isOpen,
  onClose,
  onTaskCreated,
}: CreateTaskDialogProps) {
  const [isCreating, setIsCreating] = useState(false);
  const [formData, setFormData] = useState<TaskInsert>({
    name: "",
    status: "CREATED" as StatusEnum,
    priority: "NORMAL" as TaskPriorityEnum,
    category: null,
    assignee: null,
    start: null,
    due: null,
    associated_table: null,
    associated_id: null,
  });

  // Users for assignee selection
  const [users, setUsers] = useState<any[]>([]);
  const [usersLoading, setUsersLoading] = useState(false);
  const [assigneeComboboxOpen, setAssigneeComboboxOpen] = useState(false);

  // Entity association hook
  const entityAssociation = useEntityAssociation();

  // Document attachment state
  const [attachedFiles, setAttachedFiles] = useState<File[]>([]);

  const { user: authUser } = useAppSelector((state) => state.auth);

  // Document preview hook
  const {
    isOpen: isPreviewOpen,
    document: previewDocument,
    openPreview,
    closePreview,
  } = useDocumentPreview();

  // Fetch users for assignee selection
  const fetchUsers = async () => {
    try {
      setUsersLoading(true);
      const result = await userService.getUsersForAssignment([], {
        limit: 100,
      });
      if (result.success && result.data) {
        setUsers(result.data);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
      toast.error("Failed to load users");
    } finally {
      setUsersLoading(false);
    }
  };

  // Update form data when entity association changes
  React.useEffect(() => {
    setFormData((prev) => ({
      ...prev,
      associated_table: entityAssociation.result.association_table,
      // For multiselect mode, prioritize associated_ids
      associated_id:
        entityAssociation.result.association_ids &&
        entityAssociation.result.association_ids.length === 1
          ? entityAssociation.result.association_ids[0]
          : null,
      associated_ids: entityAssociation.result.association_ids,
    }));
  }, [
    entityAssociation.result.association_table,
    entityAssociation.result.association_ids,
  ]);

  useEffect(() => {
    if (isOpen) {
      fetchUsers();
    }
  }, [isOpen]);

  const handleInputChange = (field: keyof TaskInsert, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error("Task name is required");
      return;
    }

    try {
      setIsCreating(true);

      const result = await taskService.createTask(
        formData,
        authUser?.accountId
      );

      if (result.success && result.data) {
        // Upload documents if any are provided
        if (attachedFiles.length > 0 && authUser?.accountId) {
          let uploadedCount = 0;
          let failedCount = 0;

          for (const file of attachedFiles) {
            try {
              // Upload file to storage
              const uploadResult = await documentService.uploadToStorage({
                content: file,
                fileName: file.name,
                contentType: file.type,
                folder: "task-documents",
                metadata: {
                  taskId: result.data.id,
                },
              });

              if (uploadResult.success && uploadResult.data) {
                // Create document record
                const documentData: DocumentInsert = {
                  name: file.name,
                  path: uploadResult.data,
                  category: "task",
                  description: `Document attached to task: ${formData.name}`,
                  associated_table: "tasks",
                  associated_id: result.data.id,
                  account_id: authUser.accountId,
                  status: "ACTIVE" as StatusEnum,
                };

                const createResult =
                  await documentService.createDocument(documentData);
                if (createResult.success) {
                  uploadedCount++;
                } else {
                  failedCount++;
                  console.warn(
                    `Failed to create document record for ${file.name}:`,
                    createResult.error
                  );
                }
              } else {
                failedCount++;
                console.warn(
                  `Failed to upload ${file.name}:`,
                  uploadResult.error
                );
              }
            } catch (error) {
              failedCount++;
              console.error(`Error uploading ${file.name}:`, error);
            }
          }

          if (failedCount > 0) {
            toast.warning(
              `Task created successfully. ${uploadedCount} document(s) uploaded, ${failedCount} failed.`
            );
          } else {
            toast.success(
              `Task created successfully with ${uploadedCount} document(s)`
            );
          }
        } else {
          toast.success("Task created successfully");
        }

        onTaskCreated?.();
        onClose();
        resetForm();
      } else {
        toast.error(result.error || "Failed to create task");
      }
    } catch (error: any) {
      console.error("Error creating task:", error);
      toast.error("An error occurred while creating the task");
    } finally {
      setIsCreating(false);
    }
  };

  // File upload handlers
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    addFiles(files);
    // Reset input
    event.target.value = "";
  };

  const addFiles = (files: File[]) => {
    // Filter out files that are too large (10MB limit)
    const validFiles = files.filter((file) => {
      if (file.size > 10 * 1024 * 1024) {
        toast.error(`File ${file.name} is too large. Maximum size is 10MB.`);
        return false;
      }
      return true;
    });

    setAttachedFiles((prev) => [...prev, ...validFiles]);
  };

  const removeAttachedFile = (index: number) => {
    setAttachedFiles((prev) => prev.filter((_, i) => i !== index));
  };

  // Drag and drop handlers
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const files = Array.from(e.dataTransfer.files);
    addFiles(files);
  };

  // Get file type icon
  const getFileTypeIcon = (fileName: string) => {
    const extension = fileName.split(".").pop()?.toLowerCase();
    switch (extension) {
      case "pdf":
        return <FileText className="h-4 w-4 text-red-500" />;
      case "doc":
      case "docx":
        return <FileText className="h-4 w-4 text-blue-500" />;
      case "xls":
      case "xlsx":
        return <FileText className="h-4 w-4 text-green-500" />;
      case "jpg":
      case "jpeg":
      case "png":
      case "gif":
        return <FileText className="h-4 w-4 text-purple-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      status: "CREATED" as StatusEnum,
      priority: "NORMAL" as TaskPriorityEnum,
      category: null,
      assignee: null,
      start: null,
      due: null,
      associated_table: null,
      associated_id: null,
    });

    // Reset entity association state
    entityAssociation.reset();

    // Reset document state
    setAttachedFiles([]);
  };

  const handleClose = () => {
    onClose();
    resetForm();
  };

  // Format datetime for input
  const formatDateTimeForInput = (dateString: string | null | undefined) => {
    if (!dateString) return "";
    return new Date(dateString).toISOString().slice(0, 16);
  };

  // Parse datetime from input
  const parseDateTimeFromInput = (value: string) => {
    return value ? new Date(value).toISOString() : null;
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="text-xl font-semibold flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Create New Task
          </DialogTitle>
          <p className="text-sm text-gray-500 mt-1">
            Create a new task and assign it to a team member
          </p>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="flex-1 overflow-y-auto">
          <div className="space-y-6 p-1">
            {/* Task Name */}
            <div>
              <Label
                htmlFor="name"
                className="text-sm font-medium text-gray-700 mb-2"
              >
                Task Name *
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder="Enter task name"
                className="w-full"
                required
              />
            </div>

            {/* Status and Priority Row */}
            <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
              <div className="col-span-1">
                <Label className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-1">
                  <Tag className="h-4 w-4" />
                  Status
                </Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) =>
                    handleInputChange("status", value as StatusEnum)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {STATUS_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="col-span-1">
                <Label className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-1">
                  <Flag className="h-4 w-4" />
                  Priority
                </Label>
                <Select
                  value={formData.priority || "NORMAL"}
                  onValueChange={(value) =>
                    handleInputChange("priority", value as TaskPriorityEnum)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    {PRIORITY_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <span className={option.color}>{option.label}</span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="col-span-4">
                <Label className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-1">
                  <User className="h-4 w-4" />
                  Assignee
                </Label>
                <Combobox
                  open={assigneeComboboxOpen}
                  onOpenChange={setAssigneeComboboxOpen}
                >
                  <ComboboxTrigger
                    placeholder={
                      usersLoading
                        ? "Loading users..."
                        : "Select assignee (optional)"
                    }
                    disabled={usersLoading}
                  >
                    {formData.assignee
                      ? (() => {
                          const selectedUser = users.find(
                            (user) =>
                              (user.accounts?.id || user.id) ===
                              formData.assignee
                          );
                          return selectedUser
                            ? `${selectedUser.name}${
                                selectedUser.accounts?.email
                                  ? ` (${selectedUser.accounts.email})`
                                  : ""
                              }`
                            : "Select assignee (optional)";
                        })()
                      : "Select assignee (optional)"}
                  </ComboboxTrigger>
                  <ComboboxContent>
                    <ComboboxCommand>
                      <ComboboxInput placeholder="Search users..." />
                      <ComboboxList>
                        {usersLoading ? (
                          <div className="flex items-center justify-center p-2">
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            Loading users...
                          </div>
                        ) : users.length === 0 ? (
                          <ComboboxEmpty>No users found.</ComboboxEmpty>
                        ) : (
                          <ComboboxGroup>
                            <ComboboxItem
                              value="none"
                              onSelect={() => {
                                handleInputChange("assignee", null);
                                setAssigneeComboboxOpen(false);
                              }}
                            >
                              No assignee
                              <ComboboxItemIndicator
                                isSelected={!formData.assignee}
                              />
                            </ComboboxItem>
                            {users.map((user) => {
                              const userId = user.accounts?.id || user.id;
                              const searchableValue =
                                `${user.name} ${user.accounts?.email || ""}`.trim();
                              return (
                                <ComboboxItem
                                  key={user.id}
                                  value={searchableValue}
                                  onSelect={() => {
                                    const newValue =
                                      formData.assignee === userId
                                        ? null
                                        : userId;
                                    handleInputChange("assignee", newValue);
                                    setAssigneeComboboxOpen(false);
                                  }}
                                >
                                  {user.name}{" "}
                                  {user.accounts?.email &&
                                    `(${user.accounts.email})`}
                                  <ComboboxItemIndicator
                                    isSelected={formData.assignee === userId}
                                  />
                                </ComboboxItem>
                              );
                            })}
                          </ComboboxGroup>
                        )}
                      </ComboboxList>
                    </ComboboxCommand>
                  </ComboboxContent>
                </Combobox>
              </div>
            </div>
            {/* Start and Due Date Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  Start Date
                </Label>
                <Input
                  type="datetime-local"
                  value={formatDateTimeForInput(formData.start)}
                  onChange={(e) =>
                    handleInputChange(
                      "start",
                      parseDateTimeFromInput(e.target.value)
                    )
                  }
                  className="w-full"
                />
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  Due Date
                </Label>
                <Input
                  type="datetime-local"
                  value={formatDateTimeForInput(formData.due)}
                  onChange={(e) =>
                    handleInputChange(
                      "due",
                      parseDateTimeFromInput(e.target.value)
                    )
                  }
                  className="w-full"
                />
              </div>
            </div>

            {/* Entity Association Section */}
            <EntityAssociation
              entityAssociation={entityAssociation}
              title="Entity Association (Optional)"
              description="Associate this task with specific entities like cargo, shipments, or customers. Use multiselect to associate with multiple entities."
              layout="horizontal"
              multiselect={true}
              maxSelected={20}
              multiselectPlaceholder="Select entities to associate with this task..."
            />

            {/* Document Upload Section */}
            <div>
              <Label className="text-sm font-medium text-gray-700 mb-2">
                <FileText className="h-4 w-4 inline mr-1" />
                Attach Documents (Optional)
              </Label>
              <p className="text-xs text-gray-500 mb-3">
                Upload supporting documents for this task (PDF, DOC, XLS,
                Images)
              </p>

              {/* Attached Files List */}
              {attachedFiles.length > 0 && (
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">
                    Attached Files ({attachedFiles.length})
                  </h4>
                  <div className="space-y-2 p-3 bg-gray-50 rounded-lg">
                    {attachedFiles.map((file, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-2 bg-white rounded border"
                      >
                        <div className="flex items-center gap-2 flex-1">
                          {getFileTypeIcon(file.name)}
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {file.name}
                            </p>
                            <p className="text-xs text-gray-500">
                              {(file.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeAttachedFile(index)}
                          className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* File Upload Area */}
              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors"
                onDragOver={handleDragOver}
                onDragEnter={handleDragEnter}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <input
                  type="file"
                  id="document-upload"
                  className="hidden"
                  multiple
                  accept=".pdf,.doc,.docx,.xlsx,.xls,.png,.jpg,.jpeg"
                  onChange={handleFileSelect}
                  disabled={isCreating}
                />
                <div className="space-y-2">
                  <Upload className="h-8 w-8 mx-auto text-gray-400" />
                  <div>
                    <button
                      type="button"
                      onClick={() =>
                        document.getElementById("document-upload")?.click()
                      }
                      className="text-primary hover:text-primary/80 font-medium"
                      disabled={isCreating}
                    >
                      Click to upload
                    </button>
                    <span className="text-gray-500"> or drag and drop</span>
                  </div>
                  <p className="text-xs text-gray-400">
                    PDF, DOC, XLS, PNG, JPG up to 10MB each
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex justify-end gap-3 pt-6 border-t mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isCreating}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!formData.name.trim() || isCreating}
              className="flex items-center gap-2"
            >
              {isCreating ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Plus className="h-4 w-4" />
              )}
              {isCreating
                ? "Creating Task..."
                : `Create Task${attachedFiles.length > 0 ? ` & Upload ${attachedFiles.length} File${attachedFiles.length > 1 ? "s" : ""}` : ""}`}
            </Button>
          </div>
        </form>
      </DialogContent>

      {/* Document Preview Dialog */}
      <DocumentPreviewDialog
        isOpen={isPreviewOpen}
        onClose={closePreview}
        document={previewDocument}
        title="Task Document Preview"
      />
    </Dialog>
  );
}
