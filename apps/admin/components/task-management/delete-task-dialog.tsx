"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>T<PERSON>le,
  DialogDescription,
} from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import {
  Trash2,
  <PERSON>ader2,
  <PERSON><PERSON><PERSON><PERSON>gle,
  User,
  Calendar,
  Flag,
} from "lucide-react";
import { taskService, type TaskWithAssignee } from "@/lib/logistics";
import { toast } from "sonner";

interface DeleteTaskDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onTaskDeleted?: () => void;
  task: TaskWithAssignee | null;
}

export function DeleteTaskDialog({
  isOpen,
  onClose,
  onTaskDeleted,
  task,
}: DeleteTaskDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    if (!task?.id) {
      toast.error("Task ID is required");
      return;
    }

    try {
      setIsDeleting(true);

      const result = await taskService.delete(task.id);

      if (result.success) {
        toast.success("Task deleted successfully");
        onTaskDeleted?.();
        onClose();
      } else {
        toast.error(result.error || "Failed to delete task");
      }
    } catch (error: any) {
      console.error("Error deleting task:", error);
      toast.error("An error occurred while deleting the task");
    } finally {
      setIsDeleting(false);
    }
  };

  const getPriorityBadge = (priority: string | null) => {
    const priorityConfig = {
      LOW: { color: "bg-green-100 text-green-800" },
      NORMAL: { color: "bg-blue-100 text-blue-800" },
      HIGH: { color: "bg-orange-100 text-orange-800" },
      URGENT: { color: "bg-red-100 text-red-800" },
    };

    const priorityValue = priority || "NORMAL";
    const config = priorityConfig[priorityValue as keyof typeof priorityConfig];

    return (
      <Badge className={`${config.color} gap-1`}>
        <Flag className="h-3 w-3" />
        {priorityValue}
      </Badge>
    );
  };

  const formatDateTime = (dateString: string | null) => {
    if (!dateString) return "Not set";
    return new Date(dateString).toLocaleDateString();
  };

  if (!task) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Delete Task
          </DialogTitle>
          <DialogDescription className="text-gray-600">
            Are you sure you want to delete this task? This action cannot be
            undone.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          {/* Task Summary */}
          <div className="bg-gray-50 rounded-lg p-4 space-y-3">
            <div>
              <h4 className="font-medium text-gray-900 mb-1">{task.name}</h4>
              <div className="flex items-center gap-2">
                {getPriorityBadge(task.priority)}
                {task.category && (
                  <Badge variant="outline">{task.category}</Badge>
                )}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <div className="flex items-center gap-1 text-gray-500 mb-1">
                  <User className="h-3 w-3" />
                  <span>Assignee</span>
                </div>
                <div className="text-gray-700">
                  {task.assignee_details?.user?.name ||
                    task.assignee_details?.email ||
                    "Unassigned"}
                </div>
              </div>

              <div>
                <div className="flex items-center gap-1 text-gray-500 mb-1">
                  <Calendar className="h-3 w-3" />
                  <span>Due Date</span>
                </div>
                <div className="text-gray-700">{formatDateTime(task.due)}</div>
              </div>
            </div>

            {(task.entity_details || task.associated_table) && (
              <div>
                <div className="text-xs text-gray-500 mb-1">
                  Associated Entity
                </div>
                {task.entity_details ? (
                  <div className="text-sm text-gray-700">
                    <div className="font-medium">
                      {task.entity_details.name}
                    </div>
                    <div className="text-xs text-gray-500 capitalize">
                      {task.entity_details.type}
                      {task.entity_details.identifier &&
                        ` • ${task.entity_details.identifier}`}
                    </div>
                  </div>
                ) : (
                  <div className="text-sm text-gray-700 capitalize">
                    {task.associated_table}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Warning Message */}
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-start gap-2">
              <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-red-700">
                <p className="font-medium mb-1">Warning</p>
                <p>
                  Deleting this task will permanently remove it from the system.
                  Any associated data and history will be lost.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 pt-4 border-t">
          <Button variant="outline" onClick={onClose} disabled={isDeleting}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isDeleting}
            className="flex items-center gap-2"
          >
            {isDeleting ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Trash2 className="h-4 w-4" />
            )}
            Delete Task
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
