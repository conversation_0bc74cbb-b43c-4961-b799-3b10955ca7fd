"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import {
  Eye,
  Calendar,
  User,
  Flag,
  Link,
  Clock,
  CheckCircle2,
  XCircle,
  Timer,
  FileText,
  Download,
  Loader2,
} from "lucide-react";
import {
  type TaskWithAssignee,
  type DocumentWithAccount,
  documentService,
} from "@/lib/logistics";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  DocumentPreviewDialog,
  useDocumentPreview,
} from "@/components/ui/document-preview-dialog";
import { EntityAssociationViewer } from "@/components/ui/entity-association";

interface ViewTaskDialogProps {
  isOpen: boolean;
  onClose: () => void;
  task: TaskWithAssignee | null;
}

export function ViewTaskDialog({ isOpen, onClose, task }: ViewTaskDialogProps) {
  const router = useRouter();

  const [documents, setDocuments] = useState<DocumentWithAccount[]>([]);
  const [documentsLoading, setDocumentsLoading] = useState(false);

  // Document preview hook
  const {
    isOpen: isPreviewOpen,
    document: previewDocument,
    openPreview,
    closePreview,
  } = useDocumentPreview();

  // Fetch task documents when dialog opens
  useEffect(() => {
    if (task && isOpen) {
      fetchTaskDocuments(task.id);
    }
  }, [task, isOpen]);

  const fetchTaskDocuments = async (taskId: string) => {
    try {
      setDocumentsLoading(true);
      const result = await documentService.getDocumentsByEntity(
        "tasks",
        taskId
      );
      if (result.success && result.data) {
        setDocuments(result.data);
      } else {
        console.error("Failed to fetch task documents:", result.error);
        setDocuments([]);
      }
    } catch (error) {
      console.error("Error fetching task documents:", error);
      setDocuments([]);
    } finally {
      setDocumentsLoading(false);
    }
  };

  // Get file type icon
  const getFileTypeIcon = (fileName: string) => {
    const extension = fileName?.split(".").pop()?.toLowerCase();
    switch (extension) {
      case "pdf":
        return <FileText className="h-4 w-4 text-red-500" />;
      case "doc":
      case "docx":
        return <FileText className="h-4 w-4 text-blue-500" />;
      case "xls":
      case "xlsx":
        return <FileText className="h-4 w-4 text-green-500" />;
      case "jpg":
      case "jpeg":
      case "png":
      case "gif":
        return <FileText className="h-4 w-4 text-purple-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  if (!task) return null;

  const getStatusBadge = (status: string) => {
    const defaultConfig = { color: "bg-gray-100 text-gray-800", icon: Clock };
    const statusConfig: Record<string, { color: string; icon: any }> = {
      CREATED: { color: "bg-gray-100 text-gray-800", icon: Clock },
      PENDING: { color: "bg-yellow-100 text-yellow-800", icon: Clock },
      PROCESSING: { color: "bg-blue-100 text-blue-800", icon: Timer },
      COMPLETED: { color: "bg-green-100 text-green-800", icon: CheckCircle2 },
      CANCELLED: { color: "bg-red-100 text-red-800", icon: XCircle },
      ACTIVE: { color: "bg-blue-100 text-blue-800", icon: CheckCircle2 },
      INACTIVE: { color: "bg-gray-100 text-gray-800", icon: XCircle },
      APPROVED: { color: "bg-green-100 text-green-800", icon: CheckCircle2 },
      REJECTED: { color: "bg-red-100 text-red-800", icon: XCircle },
      IN_TRANSIT: { color: "bg-blue-100 text-blue-800", icon: Timer },
      DELIVERED: { color: "bg-green-100 text-green-800", icon: CheckCircle2 },
      PICKED_UP: { color: "bg-green-100 text-green-800", icon: CheckCircle2 },
      RELEASED: { color: "bg-green-100 text-green-800", icon: CheckCircle2 },
    };

    const config = statusConfig[status] || defaultConfig;
    const Icon = config.icon;

    return (
      <Badge className={`${config.color} gap-1`}>
        <Icon className="h-3 w-3" />
        {status.replace("_", " ")}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string | null) => {
    const priorityConfig = {
      LOW: { color: "bg-green-100 text-green-800" },
      NORMAL: { color: "bg-blue-100 text-blue-800" },
      HIGH: { color: "bg-orange-100 text-orange-800" },
      URGENT: { color: "bg-red-100 text-red-800" },
    };

    const priorityValue = priority || "NORMAL";
    const config = priorityConfig[priorityValue as keyof typeof priorityConfig];

    return (
      <Badge className={`${config.color} gap-1`}>
        <Flag className="h-3 w-3" />
        {priorityValue}
      </Badge>
    );
  };

  const formatDateTime = (dateString: string | null) => {
    if (!dateString) return "Not set";
    return new Date(dateString).toLocaleString();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="text-xl font-semibold flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Task Details
          </DialogTitle>
          <p className="text-sm text-gray-500 mt-1">
            View task information and assignments
          </p>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          <div className="space-y-6 p-1">
            {/* Task Name */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {task.name}
              </h3>
              <div className="flex items-center gap-2">
                {getStatusBadge(task.status)}
                {getPriorityBadge(task.priority)}
                {task.category && (
                  <Badge variant="outline">{task.category}</Badge>
                )}
              </div>
            </div>

            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <User className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-medium text-gray-700">
                      Assignee
                    </span>
                  </div>
                  <div className="pl-6">
                    {task.assignee_details ? (
                      <div className="flex items-center gap-2">
                        <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                          <User className="h-4 w-4 text-primary" />
                        </div>
                        <div>
                          <div className="font-medium text-sm">
                            {task.assignee_details.user?.name ||
                              task.assignee_details.email}
                          </div>
                          {task.assignee_details.email && (
                            <div className="text-xs text-gray-500">
                              {task.assignee_details.email}
                            </div>
                          )}
                        </div>
                      </div>
                    ) : (
                      <span className="text-sm text-gray-500">Unassigned</span>
                    )}
                  </div>
                </div>
              </div>

              <div>
                <div className="flex items-center gap-2 mb-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">
                    Assigned By
                  </span>
                </div>
                <div className="pl-6">
                  {task.creator_details ? (
                    <div className="flex items-center gap-2">
                      <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <User className="h-4 w-4 text-green-600" />
                      </div>
                      <div>
                        <div className="font-medium text-sm">
                          {task.creator_details.user?.name ||
                            task.creator_details.email}
                        </div>
                        {task.creator_details.email && (
                          <div className="text-xs text-gray-500">
                            {task.creator_details.email}
                          </div>
                        )}
                      </div>
                    </div>
                  ) : (
                    <span className="text-sm text-gray-500">Unknown</span>
                  )}
                </div>
              </div>

              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">
                    Created
                  </span>
                </div>
                <div className="pl-6">
                  <span className="text-sm text-gray-600">
                    {formatDateTime(task.created_at)}
                  </span>
                </div>
              </div>
            </div>

            {/* Entity Association */}
            {(task.associated_table ||
              task.associated_id ||
              task.associated_ids) && (
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Link className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">
                    Entity Association
                  </span>
                </div>
                <div className="pl-6">
                  <div className="bg-gray-50 rounded-lg p-3">
                    <EntityAssociationViewer
                      entityTable={task.associated_table}
                      entityId={task.associated_id}
                      entityIds={task.associated_ids}
                      variant="default"
                      showEntityType={true}
                      showViewLink={true}
                      onViewEntity={(id, table) => {
                        // Navigate to the entity based on its type
                        const routeMap: Record<string, string> = {
                          cargo: `/cargo-management/${id}`,
                          shipments: `/shipment-management/${id}`,
                          batches: `/batch-management/${id}`,
                          invoices: `/invoice-workflow?id=${id}`,
                        };

                        const route = routeMap[table];
                        if (route) {
                          router.push(route);
                          onClose(); // Close the dialog when navigating
                        } else {
                          console.log(`No route defined for ${table}:`, id);
                        }
                      }}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Task Timeline */}
            <div>
              <div className="flex items-center gap-2 mb-3">
                <Clock className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">
                  Timeline
                </span>
              </div>
              <div className="pl-6">
                <div className="space-y-2">
                  <div className="flex items-center gap-3 text-sm">
                    <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                    <span className="text-gray-600">
                      Created: {formatDateTime(task.created_at)}
                    </span>
                  </div>
                  {task.start && (
                    <div className="flex items-center gap-3 text-sm">
                      <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                      <span className="text-gray-600">
                        Start: {formatDateTime(task.start)}
                      </span>
                    </div>
                  )}
                  {task.due && (
                    <div className="flex items-center gap-3 text-sm">
                      <div
                        className={`w-2 h-2 rounded-full ${
                          new Date(task.due) < new Date() &&
                          task.status !== "COMPLETED"
                            ? "bg-red-400"
                            : "bg-orange-400"
                        }`}
                      ></div>
                      <span className="text-gray-600">
                        Due: {formatDateTime(task.due)}
                      </span>
                    </div>
                  )}
                  <div className="flex items-center gap-3 text-sm">
                    <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                    <span className="text-gray-600">
                      Last updated: {formatDateTime(task.updated_at)}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Task Documents */}
            <div>
              <div className="flex items-center gap-2 mb-3">
                <FileText className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">
                  Attached Documents
                </span>
              </div>
              <div className="pl-6">
                {documentsLoading ? (
                  <div className="flex items-center justify-center py-4 bg-gray-50 rounded-lg">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    <span className="text-sm text-gray-600">
                      Loading documents...
                    </span>
                  </div>
                ) : documents.length > 0 ? (
                  <div className="space-y-2 p-3 bg-gray-50 rounded-lg">
                    {documents.map((doc) => (
                      <div
                        key={doc.id}
                        className="flex items-center justify-between p-2 bg-white rounded border"
                      >
                        <div className="flex items-center gap-2 flex-1">
                          {getFileTypeIcon(doc.name)}
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {doc.name}
                            </p>
                            <p className="text-xs text-gray-500">
                              Uploaded by {doc.accounts?.email || "Unknown"}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-1">
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              openPreview({
                                uri: doc.path || "",
                                fileName: doc.name,
                                fileType: doc.name.split(".").pop() || "",
                              })
                            }
                            className="h-8 w-8 p-0 text-blue-500 hover:text-blue-700 hover:bg-blue-50"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => window.open(doc.path, "_blank")}
                            className="h-8 w-8 p-0 text-green-500 hover:text-green-700 hover:bg-green-50"
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="p-3 bg-gray-50 rounded-lg text-center">
                    <p className="text-sm text-gray-500">
                      No documents attached to this task
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end pt-6 border-t">
          <Button onClick={onClose}>Close</Button>
        </div>
      </DialogContent>

      {/* Document Preview Dialog */}
      <DocumentPreviewDialog
        isOpen={isPreviewOpen}
        onClose={closePreview}
        document={previewDocument}
        title="Task Document Preview"
      />
    </Dialog>
  );
}
