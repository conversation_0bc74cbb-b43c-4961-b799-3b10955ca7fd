"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import {
  ChevronLeft,
  ChevronRight,
  Home,
  Bell,
  CheckSquare,
  Package,
  BarChart3,
  FileText,
  Unlock,
  Users,
  MessageSquare,
  PieChart,
  UserCog,
  Settings,
  LogOut,
  UserCircle,
  Loader2,
  Ship,
} from "lucide-react";
import { cn } from "@workspace/ui/lib/utils";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { signOut, getCurrentUser } from "@/store/slices/authSlice";

interface SidebarLinkProps {
  href: string;
  icon: React.ReactNode;
  label: string;
  collapsed: boolean;
}

const SidebarLink = ({ href, icon, label, collapsed }: SidebarLinkProps) => {
  const pathname = usePathname();
  const isActive =
    pathname === href || (href !== "/" && pathname.startsWith(href));

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.2 }}
    >
      <Link
        href={href}
        className={cn(
          "flex items-center gap-3 px-3 py-2.5 text-sm rounded-md transition-colors duration-150",
          isActive
            ? "bg-sidebar-active-bg text-sidebar-text"
            : "text-sidebar-text-muted hover:text-sidebar-text hover:bg-sidebar-hover-bg",
          collapsed ? "justify-center" : ""
        )}
        title={collapsed ? label : undefined}
      >
        <div className="flex-shrink-0 w-5 h-5">{icon}</div>
        <AnimatePresence mode="wait">
          {!collapsed && (
            <motion.span
              className="truncate"
              initial={{ opacity: 0, width: 0 }}
              animate={{ opacity: 1, width: "auto" }}
              exit={{ opacity: 0, width: 0 }}
              transition={{ duration: 0.2 }}
            >
              {label}
            </motion.span>
          )}
        </AnimatePresence>
      </Link>
    </motion.div>
  );
};

export function Sidebar() {
  const [collapsed, setCollapsed] = useState(false);
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { user, isLoading } = useAppSelector((state) => state.auth);

  const toggleSidebar = () => setCollapsed(!collapsed);

  // Get current user on component mount
  useEffect(() => {
    dispatch(getCurrentUser());
  }, [dispatch]);

  // Handle logout
  const handleLogout = async () => {
    try {
      await dispatch(signOut()).unwrap();
      router.push("/auth/login");
    } catch (error) {
      console.error("Logout failed:", error);
      // Still redirect to login page even if logout fails
      router.push("/auth/login");
    }
  };

  // Generate avatar initial from user name
  const getAvatarInitial = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  // Animation variants
  const sidebarVariants = {
    expanded: { width: "15rem" },
    collapsed: { width: "4rem" },
  };

  return (
    <motion.div
      className={cn(
        "flex flex-col h-screen bg-sidebar-bg text-sidebar-text",
        collapsed ? "w-16" : "w-60"
      )}
      initial={false}
      animate={collapsed ? "collapsed" : "expanded"}
      variants={sidebarVariants}
      transition={{ duration: 0.3, ease: "easeInOut" }}
    >
      <div className="flex items-center h-16 px-4 border-b border-neutral-800 flex-shrink-0">
        <AnimatePresence mode="wait">
          {!collapsed && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
              className="flex items-center gap-2 overflow-hidden mr-2"
            >
              <Link href="/dashboard">
                <Image
                  src=""
                  alt="Shamwaa Logo"
                  width={80}
                  height={30}
                  className="flex-shrink-0 object-contain"
                />
              </Link>
            </motion.div>
          )}
        </AnimatePresence>
        <motion.button
          onClick={toggleSidebar}
          className={cn(
            "ml-auto rounded-full p-1 text-sidebar-text-muted hover:bg-sidebar-hover-bg hover:text-sidebar-text focus:outline-none focus:ring-2 focus:ring-neutral-700",
            collapsed ? "mx-auto" : ""
          )}
          aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
        >
          {collapsed ? <ChevronRight size={18} /> : <ChevronLeft size={18} />}
        </motion.button>
      </div>
      <motion.nav
        className="flex-1 overflow-y-auto px-2 py-4 space-y-1"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.2, delay: 0.1 }}
      >
        <SidebarLink
          href="/dashboard"
          icon={<Home size={18} />}
          label="Dashboard"
          collapsed={collapsed}
        />
        <SidebarLink
          href="/notifications"
          icon={<Bell size={18} />}
          label="Notifications"
          collapsed={collapsed}
        />
        <SidebarLink
          href="/task-manager"
          icon={<CheckSquare size={18} />}
          label="Task Manager"
          collapsed={collapsed}
        />
        <div className="pt-3 mt-3 border-t border-neutral-800/50">
          <SidebarLink
            href="/batch-management"
            icon={<Package size={18} />}
            label="Batch Management"
            collapsed={collapsed}
          />
          <SidebarLink
            href="/cargo-management"
            icon={<Package size={18} />}
            label="Cargo Management"
            collapsed={collapsed}
          />
          <SidebarLink
            href="/shipping-management"
            icon={<Ship size={18} />}
            label="Shipping Management"
            collapsed={collapsed}
          />
        </div>
        <div className="pt-3 mt-3 border-t border-neutral-800/50">
          <SidebarLink
            href="/finance"
            icon={<BarChart3 size={18} />}
            label="Finance"
            collapsed={collapsed}
          />
          <SidebarLink
            href="/invoice-workflow"
            icon={<FileText size={18} />}
            label="Invoice Workflow"
            collapsed={collapsed}
          />
          <SidebarLink
            href="/release-authorization"
            icon={<Unlock size={18} />}
            label="Release Authorization"
            collapsed={collapsed}
          />
        </div>
        <div className="pt-3 mt-3 border-t border-neutral-800/50">
          <SidebarLink
            href="/customer-relations"
            icon={<Users size={18} />}
            label="Customer Relations"
            collapsed={collapsed}
          />
          <SidebarLink
            href="/broadcast-messages"
            icon={<MessageSquare size={18} />}
            label="Broadcast Messages"
            collapsed={collapsed}
          />
          <SidebarLink
            href="/analytics"
            icon={<PieChart size={18} />}
            label="Analytics"
            collapsed={collapsed}
          />
        </div>
        <div className="pt-3 mt-3 border-t border-neutral-800/50">
          <SidebarLink
            href="/manage-staff"
            icon={<UserCog size={18} />}
            label="Manage Staff"
            collapsed={collapsed}
          />
          <SidebarLink
            href="/my-account"
            icon={<UserCircle size={18} />}
            label="My Account"
            collapsed={collapsed}
          />
          <SidebarLink
            href="/settings"
            icon={<Settings size={18} />}
            label="Settings"
            collapsed={collapsed}
          />
        </div>
      </motion.nav>
      <motion.div
        className="p-3 border-t border-neutral-800 flex-shrink-0"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        {isLoading ? (
          <motion.div
            className="flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            {!collapsed && (
              <div className="flex items-center gap-2 text-sidebar-text-muted">
                <Loader2 size={16} className="animate-spin" />
                <span className="text-sm">Loading...</span>
              </div>
            )}
            {collapsed && (
              <Loader2
                size={16}
                className="animate-spin text-sidebar-text-muted"
              />
            )}
          </motion.div>
        ) : user ? (
          <motion.div
            className="flex items-center"
            whileHover={{ scale: 1.02 }}
          >
            <motion.div
              className="flex-shrink-0 flex items-center justify-center rounded-full bg-neutral-700 text-sidebar-text w-8 h-8"
              whileHover={{ backgroundColor: "rgba(70, 70, 70, 1)" }}
              title={collapsed ? user.name : undefined}
            >
              {user.avatar ? (
                <Image
                  src={user.avatar}
                  alt={user.name}
                  width={32}
                  height={32}
                  className="rounded-full"
                />
              ) : (
                <span className="text-sm font-medium">
                  {getAvatarInitial(user.name)}
                </span>
              )}
            </motion.div>
            <AnimatePresence mode="wait">
              {!collapsed && (
                <motion.div
                  initial={{ opacity: 0, width: 0 }}
                  animate={{ opacity: 1, width: "auto" }}
                  exit={{ opacity: 0, width: 0 }}
                  transition={{ duration: 0.2 }}
                  className="ml-3 overflow-hidden flex-1"
                >
                  <p className="text-sm font-medium text-sidebar-text truncate">
                    {user.name}
                  </p>
                  <p className="text-xs text-sidebar-text-muted truncate">
                    {user.email}
                  </p>
                </motion.div>
              )}
            </AnimatePresence>
            <AnimatePresence mode="wait">
              {!collapsed && (
                <motion.button
                  onClick={handleLogout}
                  className="ml-2 text-sidebar-text-muted hover:text-sidebar-text flex-shrink-0"
                  aria-label="Logout"
                  title="Logout"
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0 }}
                  whileHover={{ scale: 1.2, rotate: 10 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <LogOut size={16} />
                </motion.button>
              )}
            </AnimatePresence>
          </motion.div>
        ) : (
          <motion.div
            className="flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            {!collapsed && (
              <div className="text-sm text-sidebar-text-muted text-center">
                Not authenticated
              </div>
            )}
            {collapsed && (
              <div className="w-8 h-8 rounded-full bg-neutral-700 flex items-center justify-center">
                <span className="text-xs text-sidebar-text-muted">?</span>
              </div>
            )}
          </motion.div>
        )}
      </motion.div>
    </motion.div>
  );
}
