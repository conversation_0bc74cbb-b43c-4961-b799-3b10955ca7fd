"use client";

import React from "react";
import { AlertTriangle, CheckCircle, AlertCircle, XCircle } from "lucide-react";
import { Alert, AlertDescription } from "@workspace/ui/components/alert";
import { Badge } from "@workspace/ui/components/badge";
import { Progress } from "@workspace/ui/components/progress";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@workspace/ui/components/alert-dialog";
import {
  formatCBMDisplay,
  calculateCBMUtilization,
  getCBMCapacityStatus,
} from "@/lib/utils/unit-mappings";

interface CBMCapacityIndicatorProps {
  usedCBM: number;
  totalCapacity: number;
  unit?: string;
  showProgress?: boolean;
  className?: string;
}

export const CBMCapacityIndicator: React.FC<CBMCapacityIndicatorProps> = ({
  usedCBM,
  totalCapacity,
  unit = "METER_CUBIC",
  showProgress = true,
  className = "",
}) => {
  const status = getCBMCapacityStatus(usedCBM, totalCapacity);
  const remainingCBM = Math.max(0, totalCapacity - usedCBM);

  const getStatusIcon = () => {
    switch (status.level) {
      case "critical":
        return <XCircle className="h-4 w-4 text-red-500" />;
      case "high":
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case "medium":
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      default:
        return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {getStatusIcon()}
          <span className="text-sm font-medium">CBM Capacity</span>
        </div>
        <Badge
          variant={status.level === "critical" ? "destructive" : "secondary"}
          className="text-xs"
        >
          {status.percentage.toFixed(1)}%
        </Badge>
      </div>

      {showProgress && <Progress value={status.percentage} className="h-2" />}

      <div className="grid grid-cols-2 gap-4 text-xs text-gray-600">
        <div>
          <span className="font-medium">Used:</span>{" "}
          {formatCBMDisplay(usedCBM, unit)}
        </div>
        <div>
          <span className="font-medium">Remaining:</span>{" "}
          {formatCBMDisplay(remainingCBM, unit)}
        </div>
      </div>
    </div>
  );
};

interface CBMCapacityAlertProps {
  usedCBM: number;
  totalCapacity: number;
  unit?: string;
  showAlert?: boolean;
  className?: string;
}

export const CBMCapacityAlert: React.FC<CBMCapacityAlertProps> = ({
  usedCBM,
  totalCapacity,
  unit = "METER_CUBIC",
  showAlert = true,
  className = "",
}) => {
  const status = getCBMCapacityStatus(usedCBM, totalCapacity);

  if (!showAlert || status.level === "low") {
    return null;
  }

  const getAlertVariant = () => {
    switch (status.level) {
      case "critical":
        return "destructive";
      case "high":
        return "destructive";
      case "medium":
        return "default";
      default:
        return "default";
    }
  };

  const getAlertMessage = () => {
    switch (status.level) {
      case "critical":
        return "CBM capacity exceeded! Cannot assign more cargo.";
      case "high":
        return "CBM capacity is critically high (>90%). Consider reviewing assignments.";
      case "medium":
        return "CBM capacity is getting high (>70%). Monitor closely.";
      default:
        return "";
    }
  };

  return (
    <Alert variant={getAlertVariant()} className={className}>
      <AlertTriangle className="h-4 w-4" />
      <AlertDescription className="text-sm">
        {getAlertMessage()}
        <div className="mt-1 text-xs opacity-80">
          Current usage: {formatCBMDisplay(usedCBM, unit)} of{" "}
          {formatCBMDisplay(totalCapacity, unit)} (
          {status.percentage.toFixed(1)}%)
        </div>
      </AlertDescription>
    </Alert>
  );
};

interface CBMExceededDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  cargoTrackingNumber: string;
  cargoCBM: number;
  usedCBM: number;
  totalCapacity: number;
  unit?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
}

export const CBMExceededDialog: React.FC<CBMExceededDialogProps> = ({
  open,
  onOpenChange,
  cargoTrackingNumber,
  cargoCBM,
  usedCBM,
  totalCapacity,
  unit = "METER_CUBIC",
  onConfirm,
  onCancel,
}) => {
  const remainingCapacity = Math.max(0, totalCapacity - usedCBM);
  const excessCBM = Math.max(0, cargoCBM - remainingCapacity);
  const newTotal = usedCBM + cargoCBM;
  const newUtilization = calculateCBMUtilization(newTotal, totalCapacity);

  const handleCancel = () => {
    onCancel?.();
    onOpenChange(false);
  };

  const handleConfirm = () => {
    onConfirm?.();
    onOpenChange(false);
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2 text-orange-600">
            <AlertTriangle className="h-5 w-5" />
            CBM Capacity Warning
          </AlertDialogTitle>
          <AlertDialogDescription className="space-y-3">
            <p>
              Assigning cargo <strong>{cargoTrackingNumber}</strong> will exceed
              the batch CBM capacity.
            </p>

            <div className="bg-orange-50 p-3 rounded-lg space-y-2 text-sm">
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <span className="font-medium">Cargo CBM:</span>
                  <br />
                  {formatCBMDisplay(cargoCBM, unit)}
                </div>
                <div>
                  <span className="font-medium">Available:</span>
                  <br />
                  {formatCBMDisplay(remainingCapacity, unit)}
                </div>
              </div>

              <div className="border-t pt-2">
                <div className="flex justify-between">
                  <span className="font-medium">Excess CBM:</span>
                  <span className="text-orange-600 font-semibold">
                    {formatCBMDisplay(excessCBM, unit)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">New Utilization:</span>
                  <span className="text-orange-600 font-semibold">
                    {newUtilization.toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={handleCancel}>
            Close Assignment
          </AlertDialogCancel>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

interface CBMSummaryCardProps {
  usedCBM: number;
  totalCapacity: number;
  cargoCount: number;
  unit?: string;
  className?: string;
}

export const CBMSummaryCard: React.FC<CBMSummaryCardProps> = ({
  usedCBM,
  totalCapacity,
  cargoCount,
  unit = "METER_CUBIC",
  className = "",
}) => {
  const status = getCBMCapacityStatus(usedCBM, totalCapacity);
  const remainingCBM = Math.max(0, totalCapacity - usedCBM);

  return (
    <div className={`bg-white rounded-lg border p-4 space-y-3 ${className}`}>
      <div className="flex items-center justify-between">
        <h3 className="font-semibold text-gray-900">CBM Summary</h3>
        <Badge
          variant={status.level === "critical" ? "destructive" : "secondary"}
        >
          {cargoCount} Items
        </Badge>
      </div>

      <CBMCapacityIndicator
        usedCBM={usedCBM}
        totalCapacity={totalCapacity}
        unit={unit}
        showProgress={true}
      />

      <div className="grid grid-cols-3 gap-3 text-center text-xs">
        <div className="bg-blue-50 p-2 rounded">
          <div className="font-semibold text-blue-900">
            {formatCBMDisplay(totalCapacity, unit)}
          </div>
          <div className="text-blue-600">Total</div>
        </div>
        <div className="bg-green-50 p-2 rounded">
          <div className="font-semibold text-green-900">
            {formatCBMDisplay(usedCBM, unit)}
          </div>
          <div className="text-green-600">Used</div>
        </div>
        <div className="bg-gray-50 p-2 rounded">
          <div className="font-semibold text-gray-900">
            {formatCBMDisplay(remainingCBM, unit)}
          </div>
          <div className="text-gray-600">Available</div>
        </div>
      </div>
    </div>
  );
};

// Weight-related utility functions
export function getWeightCapacityStatus(
  usedWeight: number,
  totalCapacity: number
) {
  const percentage = totalCapacity > 0 ? (usedWeight / totalCapacity) * 100 : 0;

  let level: "low" | "medium" | "high" | "critical";
  if (percentage >= 100) {
    level = "critical";
  } else if (percentage >= 90) {
    level = "high";
  } else if (percentage >= 70) {
    level = "medium";
  } else {
    level = "low";
  }

  return { percentage, level };
}

// Helper function to format weight with dual units (kg / tonnes)
export function formatWeightWithTonnes(weightInKg: number): string {
  const tonnes = weightInKg / 1000;

  if (weightInKg === 0) {
    return "0 kg / 0 tonnes";
  }

  // Format kg with appropriate decimal places
  const kgFormatted =
    weightInKg % 1 === 0
      ? weightInKg.toString()
      : weightInKg.toFixed(weightInKg < 10 ? 2 : 1);

  // Format tonnes with appropriate decimal places
  const tonnesFormatted =
    tonnes < 0.01
      ? "0"
      : tonnes < 1
        ? tonnes.toFixed(3).replace(/\.?0+$/, "")
        : tonnes.toFixed(2).replace(/\.?0+$/, "");

  return `${kgFormatted} kg / ${tonnesFormatted} tonnes`;
}

export function calculateWeightUtilization(
  usedWeight: number,
  totalCapacity: number
): number {
  if (totalCapacity <= 0) return 0;
  return (usedWeight / totalCapacity) * 100;
}

// Weight components
interface WeightCapacityIndicatorProps {
  usedWeight: number;
  totalCapacity: number;
  showProgress?: boolean;
  className?: string;
}

export const WeightCapacityIndicator: React.FC<
  WeightCapacityIndicatorProps
> = ({ usedWeight, totalCapacity, showProgress = true, className = "" }) => {
  const status = getWeightCapacityStatus(usedWeight, totalCapacity);
  const remainingWeight = Math.max(0, totalCapacity - usedWeight);

  const getStatusIcon = () => {
    switch (status.level) {
      case "critical":
        return <XCircle className="h-4 w-4 text-red-500" />;
      case "high":
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case "medium":
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      default:
        return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {getStatusIcon()}
          <span className="text-sm font-medium">
            {status.percentage.toFixed(1)}% Utilized
          </span>
        </div>
        <span className="text-xs text-gray-500">
          {formatWeightWithTonnes(usedWeight)} /{" "}
          {formatWeightWithTonnes(totalCapacity)}
        </span>
      </div>

      {showProgress && <Progress value={status.percentage} className="h-2" />}

      <div className="grid grid-cols-2 gap-4 text-xs text-gray-600">
        <div>
          <span className="font-medium">Used:</span>{" "}
          {formatWeightWithTonnes(usedWeight)}
        </div>
        <div>
          <span className="font-medium">Remaining:</span>{" "}
          {formatWeightWithTonnes(remainingWeight)}
        </div>
      </div>
    </div>
  );
};

interface WeightCapacityAlertProps {
  usedWeight: number;
  totalCapacity: number;
  showAlert?: boolean;
  className?: string;
}

export const WeightCapacityAlert: React.FC<WeightCapacityAlertProps> = ({
  usedWeight,
  totalCapacity,
  showAlert = true,
  className = "",
}) => {
  const status = getWeightCapacityStatus(usedWeight, totalCapacity);

  if (!showAlert || status.level === "low") {
    return null;
  }

  const getAlertVariant = () => {
    switch (status.level) {
      case "critical":
        return "destructive";
      case "high":
        return "destructive";
      case "medium":
        return "default";
      default:
        return "default";
    }
  };

  const getAlertMessage = () => {
    switch (status.level) {
      case "critical":
        return "Weight capacity exceeded! Cannot assign more cargo.";
      case "high":
        return "Weight capacity is critically high (>90%). Consider reviewing assignments.";
      case "medium":
        return "Weight capacity is getting high (>70%). Monitor closely.";
      default:
        return "";
    }
  };

  return (
    <Alert variant={getAlertVariant()} className={className}>
      <AlertTriangle className="h-4 w-4" />
      <AlertDescription className="text-sm">
        {getAlertMessage()}
        <div className="mt-1 text-xs opacity-80">
          Current usage: {formatWeightWithTonnes(usedWeight)} of{" "}
          {formatWeightWithTonnes(totalCapacity)} (
          {status.percentage.toFixed(1)}%)
        </div>
      </AlertDescription>
    </Alert>
  );
};

interface WeightSummaryCardProps {
  usedWeight: number;
  totalCapacity: number;
  cargoCount: number;
  className?: string;
}

export const WeightSummaryCard: React.FC<WeightSummaryCardProps> = ({
  usedWeight,
  totalCapacity,
  cargoCount,
  className = "",
}) => {
  const status = getWeightCapacityStatus(usedWeight, totalCapacity);
  const remainingWeight = Math.max(0, totalCapacity - usedWeight);

  return (
    <div className={`bg-white rounded-lg border p-4 space-y-3 ${className}`}>
      <div className="flex items-center justify-between">
        <h3 className="font-semibold text-gray-900">Weight Summary</h3>
        <Badge
          variant={status.level === "critical" ? "destructive" : "secondary"}
        >
          {cargoCount} Items
        </Badge>
      </div>

      <WeightCapacityIndicator
        usedWeight={usedWeight}
        totalCapacity={totalCapacity}
        showProgress={true}
      />

      <div className="grid grid-cols-3 gap-2 text-center text-xs">
        <div className="bg-blue-50 p-2 rounded">
          <div className="font-semibold text-blue-900">
            {formatWeightWithTonnes(totalCapacity)}
          </div>
          <div className="text-blue-600">Total</div>
        </div>
        <div className="bg-green-50 p-2 rounded">
          <div className="font-semibold text-green-900">
            {formatWeightWithTonnes(usedWeight)}
          </div>
          <div className="text-green-600">Used</div>
        </div>
        <div className="bg-gray-50 p-2 rounded">
          <div className="font-semibold text-gray-900">
            {formatWeightWithTonnes(remainingWeight)}
          </div>
          <div className="text-gray-600">Available</div>
        </div>
      </div>
    </div>
  );
};
