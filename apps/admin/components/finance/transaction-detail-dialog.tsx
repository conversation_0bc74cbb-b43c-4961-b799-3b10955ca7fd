"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import {
  DollarSign,
  Calendar,
  Tag,
  User,
  BookOpen,
  TrendingUp,
  TrendingDown,
  CheckCircle2,
  Clock,
  AlertCircle,
  Copy,
  FileText,
  Eye,
  Download,
  Loader2,
  Paperclip,
  Link,
  ExternalLink,
} from "lucide-react";
import { AnimatedCard } from "@/components/animated-card";
import { StatusBadge } from "@/components/status-badge";
import {
  DocumentPreviewDialog,
  useDocumentPreview,
} from "@/components/ui/document-preview-dialog";
import {
  transactionService,
  documentService,
  type DocumentWithAccount,
} from "@/lib/logistics";

interface TransactionDetailDialogProps {
  isOpen: boolean;
  onClose: () => void;
  transaction: any;
}

export function TransactionDetailDialog({
  isOpen,
  onClose,
  transaction,
}: TransactionDetailDialogProps) {
  const [copying, setCopying] = useState(false);
  const [documents, setDocuments] = useState<DocumentWithAccount[]>([]);
  const [documentsLoading, setDocumentsLoading] = useState(false);

  // Document preview hook
  const {
    isOpen: isPreviewOpen,
    document: previewDocument,
    openPreview,
    closePreview,
  } = useDocumentPreview();

  // Fetch documents when dialog opens
  useEffect(() => {
    if (isOpen && transaction?.id) fetchDocuments();
  }, [isOpen, transaction?.id]);

  const fetchDocuments = async () => {
    if (!transaction?.id) return;

    setDocumentsLoading(true);
    try {
      const result = await transactionService.getTransactionDocuments(
        transaction.id
      );
      if (result.success && result.data) {
        setDocuments(result.data);
      } else {
        console.error("Failed to fetch documents:", result.error);
        setDocuments([]);
      }
    } catch (error) {
      console.error("Error fetching documents:", error);
      setDocuments([]);
    } finally {
      setDocumentsLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getTransactionIcon = (amount: number) => {
    return amount >= 0 ? (
      <TrendingUp className="h-5 w-5 text-green-600" />
    ) : (
      <TrendingDown className="h-5 w-5 text-red-600" />
    );
  };

  const getStatusIcon = (status: string) => {
    switch (status?.toLowerCase()) {
      case "completed":
      case "active":
        return <CheckCircle2 className="h-4 w-4" />;
      case "pending":
        return <Clock className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      setCopying(true);
      await navigator.clipboard.writeText(text);
      setTimeout(() => setCopying(false), 1000);
    } catch (error) {
      console.error("Failed to copy:", error);
      setCopying(false);
    }
  };

  // Handle document preview
  const handleViewDocument = async (doc: DocumentWithAccount) => {
    try {
      const downloadResult = await documentService.getDocumentDownloadUrl(
        doc.path
      );

      if (downloadResult.success && downloadResult.data) {
        const fileExtension = doc.name.split(".").pop()?.toLowerCase() || "pdf";
        const fileType = ["jpg", "jpeg", "png", "gif"].includes(fileExtension)
          ? "image"
          : fileExtension === "pdf"
            ? "pdf"
            : "document";

        openPreview({
          uri: downloadResult.data,
          fileName: doc.name,
          fileType: fileType,
        });
      } else {
        alert("Failed to load document for preview");
      }
    } catch (error) {
      console.error("Error loading document:", error);
      alert("Failed to load document");
    }
  };

  // Handle document download
  const handleDownloadDocument = async (doc: DocumentWithAccount) => {
    try {
      const downloadResult = await documentService.getDocumentDownloadUrl(
        doc.path
      );

      if (downloadResult.success && downloadResult.data) {
        const link = document.createElement("a");
        link.href = downloadResult.data;
        link.download = doc.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        alert("Failed to download document");
      }
    } catch (error) {
      console.error("Error downloading document:", error);
      alert("Failed to download document");
    }
  };

  // Get file type icon
  const getFileTypeIcon = (fileName: string) => {
    const extension = fileName.split(".").pop()?.toLowerCase();
    switch (extension) {
      case "pdf":
        return <FileText className="h-4 w-4 text-red-500" />;
      case "jpg":
      case "jpeg":
      case "png":
      case "gif":
        return <FileText className="h-4 w-4 text-blue-500" />;
      case "doc":
      case "docx":
        return <FileText className="h-4 w-4 text-blue-600" />;
      case "xls":
      case "xlsx":
        return <FileText className="h-4 w-4 text-green-600" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  if (!transaction) return <></>;
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[60rem] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-primary/10">
              <DollarSign className="h-5 w-5 text-primary" />
            </div>
            <div className="flex-1">
              <h2 className="text-xl font-semibold">Transaction Details</h2>
              <p className="text-sm text-gray-500 mt-1">
                ID: {transaction.id}
                <Button
                  variant="ghost"
                  size="sm"
                  className="ml-2 h-6 w-6 p-0"
                  onClick={() => copyToClipboard(transaction.id)}
                  title="Copy ID"
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </p>
            </div>
          </DialogTitle>
        </DialogHeader>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="space-y-6"
        >
          {/* Main Transaction Info */}
          <AnimatedCard className="p-6">
            <div className="flex items-start justify-between mb-8">
              <div className="flex items-center gap-3">
                {getTransactionIcon(transaction.amount)}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    {transaction.name}
                  </h3>
                  {transaction.context && (
                    <p className="text-sm text-gray-600 mt-1">
                      {transaction.context}
                    </p>
                  )}
                </div>
              </div>
              <div className="text-right">
                <p
                  className={`text-2xl font-bold ${
                    transaction.type === "DEBIT"
                      ? "text-green-600"
                      : "text-red-600"
                  }`}
                >
                  {formatCurrency(transaction.amount)}
                </p>
                <p className="text-sm text-gray-500">
                  {transaction.type === "DEBIT" ? "Income" : "Expense"}
                </p>
              </div>
            </div>

            <div className="w-full grid grid-cols-3 gap-4">
              <div className="flex items-center gap-2">
                <StatusBadge status={transaction.status} />
                {getStatusIcon(transaction.status || "")}
              </div>
              {transaction.book && (
                <div className="flex items-center gap-2">
                  <BookOpen className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-700">
                    {transaction.book}
                  </span>
                </div>
              )}
              <div className="flex flex-row gap-1 items-center">
                <Tag className="h-4 w-4 text-gray-500" />
                <div className="flex flex-wrap gap-1">
                  {transaction.tags && transaction.tags.length > 0 ? (
                    transaction.tags.map((tag: string, index: number) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="text-xs capitalize"
                      >
                        {tag}
                      </Badge>
                    ))
                  ) : (
                    <p className="text-sm text-gray-500">No tags</p>
                  )}
                </div>
              </div>
            </div>
          </AnimatedCard>

          {/* Details Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Dates */}
            <AnimatedCard className="p-4">
              <div className="flex items-center gap-2 mb-3">
                <Calendar className="h-4 w-4 text-gray-500" />
                <h4 className="font-medium text-gray-900">Dates</h4>
              </div>
              <div className="space-y-2">
                <div>
                  <p className="text-xs text-gray-500">Created</p>
                  <p className="text-sm">
                    {formatDate(transaction.created_at)}
                  </p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">Updated</p>
                  <p className="text-sm">
                    {formatDate(transaction.updated_at)}
                  </p>
                </div>
              </div>
            </AnimatedCard>

            {/* Account Information */}
            {transaction.accounts && (
              <AnimatedCard className="p-4">
                <div className="flex items-center gap-2 mb-3">
                  <User className="h-4 w-4 text-gray-500" />
                  <h4 className="font-medium text-gray-900">
                    Account Information
                  </h4>
                </div>
                <div className="space-y-2">
                  <div>
                    <p className="text-xs text-gray-500">Account ID</p>
                    <p className="text-sm font-mono">
                      {transaction.accounts.role.name}
                    </p>
                  </div>
                  {transaction.accounts.users && (
                    <div>
                      <p className="text-xs text-gray-500">User</p>
                      <p className="text-sm">
                        {transaction.accounts.users.name} (
                        {transaction.accounts.email})
                      </p>
                    </div>
                  )}
                </div>
              </AnimatedCard>
            )}

            {/* Associated Entity */}
            {transaction.associated_entity && (
              <AnimatedCard className="p-4">
                <div className="flex items-center gap-2 mb-3">
                  <Link className="h-4 w-4 text-gray-500" />
                  <h4 className="font-medium text-gray-900">
                    Associated Entity
                  </h4>
                </div>
                <div className="space-y-2">
                  <div>
                    <p className="text-xs text-gray-500">Entity Type</p>
                    <p className="text-sm font-medium capitalize">
                      {transaction.associated_table}
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Entity Details</p>
                    <p className="text-sm">
                      {transaction.associated_entity.label}
                    </p>
                  </div>
                  {transaction.associated_entity.metadata && (
                    <div>
                      <p className="text-xs text-gray-500">Additional Info</p>
                      <div className="text-sm space-y-1">
                        {transaction.associated_entity.metadata.customer && (
                          <p className="text-gray-600">
                            Customer:{" "}
                            {transaction.associated_entity.metadata.customer}
                          </p>
                        )}
                        {transaction.associated_entity.metadata
                          .tracking_number && (
                          <p className="text-gray-600">
                            Tracking:{" "}
                            {
                              transaction.associated_entity.metadata
                                .tracking_number
                            }
                          </p>
                        )}
                        {transaction.associated_entity.metadata
                          .invoice_number && (
                          <p className="text-gray-600">
                            Invoice:{" "}
                            {
                              transaction.associated_entity.metadata
                                .invoice_number
                            }
                          </p>
                        )}
                        {transaction.associated_entity.metadata
                          .total_amount && (
                          <p className="text-gray-600">
                            Amount: $
                            {
                              transaction.associated_entity.metadata
                                .total_amount
                            }
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                  <div className="pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="gap-2"
                      onClick={() => {
                        // Navigate to entity details using the original associated_id
                        const entityType = transaction.associated_table;
                        const entityId = transaction.associated_id;
                        if (entityType === "invoices") {
                          window.open(
                            `/invoice-workflow?id=${entityId}`,
                            "_blank"
                          );
                        } else if (entityType === "cargo") {
                          window.open(
                            `/cargo-management?id=${entityId}`,
                            "_blank"
                          );
                        }
                        // Add more entity type navigation as needed
                      }}
                    >
                      <ExternalLink className="h-3 w-3" />
                      View Details
                    </Button>
                  </div>
                </div>
              </AnimatedCard>
            )}
          </div>

          {/* Documents & Attachments */}
          <span className="p-4">
            <div className="flex items-center gap-2 mb-3">
              <Paperclip className="h-4 w-4 text-gray-500" />
              <h4 className="font-medium text-gray-900">
                Documents & Attachments
              </h4>
              <Badge variant="secondary" className="text-xs">
                {documents.length} files
              </Badge>
            </div>

            {documentsLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
                <span className="ml-2 text-sm text-gray-500">
                  Loading documents...
                </span>
              </div>
            ) : documents.length > 0 ? (
              <div className="border border-gray-200 rounded-lg overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12"></TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {documents.map((doc) => (
                      <TableRow key={doc.id} className="hover:bg-gray-50">
                        <TableCell>{getFileTypeIcon(doc.name)}</TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium text-sm">{doc.name}</p>
                            {doc.description && (
                              <p className="text-xs text-gray-500">
                                {doc.description}
                              </p>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="text-sm text-gray-500">
                          {formatDate(doc.created_at)}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => handleViewDocument(doc)}
                              title="View Document"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => handleDownloadDocument(doc)}
                              title="Download Document"
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-8">
                <FileText className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                <p className="text-sm text-gray-500">No documents attached</p>
                <p className="text-xs text-gray-400 mt-1">
                  Documents will appear here when uploaded
                </p>
              </div>
            )}
          </span>

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </motion.div>
      </DialogContent>

      {/* Document Preview Dialog */}
      <DocumentPreviewDialog
        isOpen={isPreviewOpen}
        onClose={closePreview}
        document={previewDocument}
        title="Transaction Document Preview"
      />
    </Dialog>
  );
}
