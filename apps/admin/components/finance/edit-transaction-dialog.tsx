"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Textarea } from "@workspace/ui/components/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  DollarSign,
  Save,
  X,
  Loader2,
  Tag,
  AlertCircle,
  Upload,
  FileText,
  Trash2,
  Paperclip,
  BookOpen,
  Eye,
  Download,
  RefreshCw,
} from "lucide-react";
import {
  transactionService,
  StatusEnum,
  ledgerService,
  documentService,
  type DocumentWithAccount,
} from "@/lib/logistics";
import { useAppSelector } from "@/store/hooks";
import {
  DocumentPreviewDialog,
  useDocumentPreview,
} from "@/components/ui/document-preview-dialog";

// Import entity association components
import {
  EntityAssociation,
  useEntityAssociation,
} from "@/components/ui/entity-association";

interface EditTransactionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  transaction: any;
  onTransactionUpdated?: () => void;
}

// Popular tags for quick selection
const POPULAR_TAGS = [
  "income",
  "expense",
  "payment",
  "refund",
  "fee",
  "commission",
  "shipping",
  "freight",
  "customs",
  "insurance",
  "fuel",
  "maintenance",
  "office",
  "marketing",
];

// Status options
const STATUS_OPTIONS = [
  {
    value: "PENDING",
    label: "Pending",
    color: "bg-yellow-100 text-yellow-800",
  },
  { value: "ACTIVE", label: "Active", color: "bg-green-100 text-green-800" },
  {
    value: "COMPLETED",
    label: "Completed",
    color: "bg-blue-100 text-blue-800",
  },
  { value: "CANCELLED", label: "Cancelled", color: "bg-red-100 text-red-800" },
];

// Default book options for ledgers
const DEFAULT_BOOKS = [
  "General Ledger",
  "Sales Revenue",
  "Operating Expenses",
  "Freight Costs",
  "Customer Payments",
  "Shipping Fees",
  "Fuel Costs",
  "Equipment Maintenance",
  "Insurance",
  "Office Supplies",
  "Marketing",
  "Accounts Receivable",
  "Accounts Payable",
  "Cash Flow",
  "Petty Cash",
];

type TransactionType = "CREDIT" | "DEBIT";

export function EditTransactionDialog({
  isOpen,
  onClose,
  transaction,
  onTransactionUpdated,
}: EditTransactionDialogProps) {
  const [formData, setFormData] = useState({
    name: "",
    amount: "",
    type: "DEBIT" as TransactionType,
    context: "",
    status: "PENDING" as StatusEnum,
    tags: [] as string[],
    book: "", // Selected book from ledger
  });
  const [tagInput, setTagInput] = useState("");
  const [isUpdating, setIsUpdating] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  // Document attachment state
  const [attachedFiles, setAttachedFiles] = useState<File[]>([]);
  const [existingDocuments, setExistingDocuments] = useState<
    DocumentWithAccount[]
  >([]);
  const [documentsLoading, setDocumentsLoading] = useState(false);
  const [deletingDocuments, setDeletingDocuments] = useState<Set<string>>(
    new Set()
  );

  // Ledger and book state
  const [ledgerData, setLedgerData] = useState<any>(null);
  const [availableBooks, setAvailableBooks] = useState<string[]>([]);

  const { user: authUser } = useAppSelector((state) => state.auth);

  // Entity association hook
  const entityAssociation = useEntityAssociation();

  // Document preview hook
  const {
    isOpen: isPreviewOpen,
    document: previewDocument,
    openPreview,
    closePreview,
  } = useDocumentPreview();

  // Fetch ledger data when dialog opens
  useEffect(() => {
    if (isOpen && transaction?.ledger_id) {
      fetchLedgerData();
    }
  }, [isOpen, transaction?.ledger_id]);

  // Fetch existing documents when dialog opens
  useEffect(() => {
    if (isOpen && transaction?.id) {
      fetchExistingDocuments();
    }
  }, [isOpen, transaction?.id]);

  // Populate form when transaction changes
  useEffect(() => {
    if (transaction && isOpen) {
      setFormData({
        name: transaction.name || "",
        amount: transaction.amount?.toString() || "",
        type: transaction.type || "DEBIT",
        context: transaction.context || "",
        status: transaction.status || "PENDING",
        tags: transaction.tags || [],
        book: transaction.book || "",
      });
      setErrors({});
    }
  }, [transaction, isOpen]);

  // Separate effect for entity association initialization
  useEffect(() => {
    if (transaction && isOpen) {
      // Initialize entity association with existing data
      if (transaction.associated_table && transaction.associated_id) {
        entityAssociation.handleTableChange(transaction.associated_table);
        entityAssociation.handleIdChange(transaction.associated_id);
      } else {
        entityAssociation.reset();
      }
    }
  }, [transaction?.id, isOpen]); // Only depend on transaction ID and isOpen

  const fetchLedgerData = async () => {
    if (!transaction?.ledger_id) return;

    try {
      const result = await ledgerService.getLedgerById(transaction.ledger_id);
      if (result.success && result.data) {
        setLedgerData(result.data);
        // Set available books from ledger or use default books
        const books =
          result.data.books && result.data.books.length > 0
            ? result.data.books
            : DEFAULT_BOOKS;

        setAvailableBooks(books || []);
      }
    } catch (error) {
      console.error("Error fetching ledger data:", error);
      // Fallback to default books
      setAvailableBooks(DEFAULT_BOOKS);
    }
  };

  const fetchExistingDocuments = async () => {
    if (!transaction?.id) return;

    setDocumentsLoading(true);
    try {
      const result = await transactionService.getTransactionDocuments(
        transaction.id
      );
      if (result.success && result.data) {
        setExistingDocuments(result.data);
      } else {
        console.error("Failed to fetch documents:", result.error);
        setExistingDocuments([]);
      }
    } catch (error) {
      console.error("Error fetching documents:", error);
      setExistingDocuments([]);
    } finally {
      setDocumentsLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      amount: "",
      type: "DEBIT" as TransactionType,
      context: "",
      status: "PENDING" as StatusEnum,
      tags: [],
      book: "",
    });
    setTagInput("");
    setErrors({});
    setAttachedFiles([]);
    setExistingDocuments([]);
    setDeletingDocuments(new Set());
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.name.trim()) {
      newErrors.name = "Transaction name is required";
    }

    if (!formData.amount || isNaN(Number(formData.amount))) {
      newErrors.amount = "Valid amount is required";
    } else if (Number(formData.amount) === 0) {
      newErrors.amount = "Amount cannot be zero";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm() || !transaction?.id || !authUser?.accountId) return;

    try {
      setIsUpdating(true);

      const updateData = {
        name: formData.name.trim(),
        amount: Number(formData.amount),
        type: formData.type,
        context: formData.context.trim() || undefined,
        status: formData.status,
        tags: formData.tags.length > 0 ? formData.tags : undefined,
        book: formData.book || undefined, // Include selected book
        // Include entity association data
        associated_table: entityAssociation.result.association_table || null,
        associated_id: entityAssociation.result.association_id || null,
      };

      // First update the transaction
      const result = await transactionService.updateTransaction(
        transaction.id,
        updateData
      );

      if (!result.success) {
        setErrors({ general: result.error || "Failed to update transaction" });
        return;
      }

      // Upload documents if any are provided
      if (attachedFiles.length > 0 && authUser?.accountId) {
        const documentsResult =
          await transactionService.uploadTransactionDocuments(
            transaction.id,
            attachedFiles,
            authUser.accountId,
            transaction.ledger_id
          );

        if (!documentsResult.success) {
          // Transaction was updated but documents failed - show warning but don't fail
          console.warn(
            "Transaction updated but document upload failed:",
            documentsResult.error
          );
          setErrors({
            documents: `Transaction updated successfully, but failed to upload ${attachedFiles.length} document(s): ${documentsResult.error}`,
          });
        }
      }

      onTransactionUpdated?.();
      handleClose();
    } catch (error) {
      console.error("Error updating transaction:", error);
      setErrors({ general: "An unexpected error occurred" });
    } finally {
      setIsUpdating(false);
    }
  };

  const addTag = (tag: string) => {
    const trimmedTag = tag.trim().toLowerCase();
    if (trimmedTag && !formData.tags.includes(trimmedTag)) {
      setFormData((prev) => ({
        ...prev,
        tags: [...prev.tags, trimmedTag],
      }));
    }
    setTagInput("");
  };

  const removeTag = (tagToRemove: string) => {
    setFormData((prev) => ({
      ...prev,
      tags: prev.tags.filter((tag) => tag !== tagToRemove),
    }));
  };

  const handleTagInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && tagInput.trim()) {
      e.preventDefault();
      addTag(tagInput);
    }
  };

  // File upload handlers
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    addFiles(files);
    // Reset input
    event.target.value = "";
  };

  const addFiles = (files: File[]) => {
    // Filter out files that are too large (10MB limit)
    const validFiles = files.filter((file) => {
      if (file.size > 10 * 1024 * 1024) {
        setErrors((prev) => ({
          ...prev,
          documents: `File ${file.name} is too large. Maximum size is 10MB.`,
        }));
        return false;
      }
      return true;
    });

    setAttachedFiles((prev) => [...prev, ...validFiles]);
  };

  const removeAttachedFile = (index: number) => {
    setAttachedFiles((prev) => prev.filter((_, i) => i !== index));
  };

  // Drag and drop handlers
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const files = Array.from(e.dataTransfer.files);
    addFiles(files);
  };

  // Document management functions
  const handleViewDocument = async (doc: DocumentWithAccount) => {
    try {
      const downloadResult = await documentService.getDocumentDownloadUrl(
        doc.path
      );

      if (downloadResult.success && downloadResult.data) {
        const fileExtension = doc.name.split(".").pop()?.toLowerCase() || "pdf";
        const fileType = ["jpg", "jpeg", "png", "gif"].includes(fileExtension)
          ? "image"
          : fileExtension === "pdf"
            ? "pdf"
            : "document";

        openPreview({
          uri: downloadResult.data,
          fileName: doc.name,
          fileType: fileType,
        });
      } else {
        setErrors((prev) => ({
          ...prev,
          documents: "Failed to load document for preview",
        }));
      }
    } catch (error) {
      console.error("Error loading document:", error);
      setErrors((prev) => ({
        ...prev,
        documents: "Failed to load document",
      }));
    }
  };

  const handleDownloadDocument = async (doc: DocumentWithAccount) => {
    try {
      const downloadResult = await documentService.getDocumentDownloadUrl(
        doc.path
      );

      if (downloadResult.success && downloadResult.data) {
        const link = document.createElement("a");
        link.href = downloadResult.data;
        link.download = doc.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        setErrors((prev) => ({
          ...prev,
          documents: "Failed to download document",
        }));
      }
    } catch (error) {
      console.error("Error downloading document:", error);
      setErrors((prev) => ({
        ...prev,
        documents: "Failed to download document",
      }));
    }
  };

  const handleDeleteDocument = async (doc: DocumentWithAccount) => {
    if (!confirm(`Are you sure you want to delete "${doc.name}"?`)) {
      return;
    }

    setDeletingDocuments((prev) => new Set(prev).add(doc.id));

    try {
      const result = await transactionService.deleteTransactionDocument(doc.id);

      if (result.success) {
        // Remove from existing documents list
        setExistingDocuments((prev) => prev.filter((d) => d.id !== doc.id));
        // Clear any document errors
        setErrors((prev) => {
          const newErrors = { ...prev };
          delete newErrors.documents;
          return newErrors;
        });
      } else {
        setErrors((prev) => ({
          ...prev,
          documents: `Failed to delete document: ${result.error}`,
        }));
      }
    } catch (error) {
      console.error("Error deleting document:", error);
      setErrors((prev) => ({
        ...prev,
        documents: "Failed to delete document",
      }));
    } finally {
      setDeletingDocuments((prev) => {
        const newSet = new Set(prev);
        newSet.delete(doc.id);
        return newSet;
      });
    }
  };

  // Get file type icon
  const getFileTypeIcon = (fileName: string) => {
    const extension = fileName.split(".").pop()?.toLowerCase();
    switch (extension) {
      case "pdf":
        return <FileText className="h-4 w-4 text-red-500" />;
      case "jpg":
      case "jpeg":
      case "png":
      case "gif":
        return <FileText className="h-4 w-4 text-blue-500" />;
      case "doc":
      case "docx":
        return <FileText className="h-4 w-4 text-blue-600" />;
      case "xls":
      case "xlsx":
        return <FileText className="h-4 w-4 text-green-600" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  if (!transaction) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[60rem] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div className="p-2 rounded-lg bg-primary/10">
              <DollarSign className="h-5 w-5 text-primary" />
            </div>
            Edit Transaction
          </DialogTitle>
        </DialogHeader>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="space-y-6"
        >
          {/* Error Message */}
          {errors.general && (
            <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <span className="text-sm text-red-600">{errors.general}</span>
            </div>
          )}

          {/* Basic Information */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="name" className="text-sm font-medium">
                Transaction Name *
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, name: e.target.value }))
                }
                placeholder="Enter transaction name"
                className={errors.name ? "border-red-300" : ""}
              />
              {errors.name && (
                <p className="text-xs text-red-600 mt-1">{errors.name}</p>
              )}
            </div>

            <div className="w-full flex flex-row space-x-4">
              <div>
                <Label htmlFor="type" className="text-sm font-medium">
                  Type *
                </Label>
                <div className="w-max flex border border-gray-200 rounded-lg overflow-hidden">
                  <button
                    type="button"
                    onClick={() =>
                      setFormData((prev) => ({ ...prev, type: "DEBIT" }))
                    }
                    className={`px-3 py-2 text-sm ${
                      formData.type === "DEBIT"
                        ? "bg-primary text-white"
                        : "bg-white text-gray-700 hover:bg-gray-50"
                    }`}
                  >
                    Debit
                  </button>
                  <button
                    type="button"
                    onClick={() =>
                      setFormData((prev) => ({ ...prev, type: "CREDIT" }))
                    }
                    className={`px-3 py-2 text-sm ${
                      formData.type === "CREDIT"
                        ? "bg-primary text-white"
                        : "bg-white text-gray-700 hover:bg-gray-50"
                    }`}
                  >
                    Credit
                  </button>
                </div>
              </div>

              <div>
                <Label htmlFor="amount" className="text-sm font-medium">
                  Amount *
                </Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  value={formData.amount}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, amount: e.target.value }))
                  }
                  placeholder="0.00"
                  className={errors.amount ? "border-red-300" : ""}
                />
                {errors.amount && (
                  <p className="text-xs text-red-600 mt-1">{errors.amount}</p>
                )}
              </div>

              <div>
                <Label htmlFor="status" className="text-sm font-medium">
                  Status
                </Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) =>
                    setFormData((prev) => ({
                      ...prev,
                      status: value as StatusEnum,
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {STATUS_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center gap-2">
                          <span
                            className={`px-2 py-1 rounded-full text-xs ${option.color}`}
                          >
                            {option.label}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Book Selector */}
            <div>
              <Label
                htmlFor="book"
                className="text-sm font-medium mb-2 flex items-center gap-1"
              >
                <BookOpen className="h-4 w-4" />
                Ledger Book
              </Label>
              <Select
                value={formData.book}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, book: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a book category" />
                </SelectTrigger>
                <SelectContent className="max-h-60">
                  {availableBooks &&
                    availableBooks.map((book) => (
                      <SelectItem key={book} value={book}>
                        <div className="flex items-center gap-2">
                          <span className="text-sm">{book}</span>
                        </div>
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500 mt-1">
                Select the book category to organize this transaction within the
                ledger
              </p>
            </div>

            <div>
              <Label htmlFor="context" className="text-sm font-medium">
                Context
              </Label>
              <Textarea
                id="context"
                value={formData.context}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, context: e.target.value }))
                }
                placeholder="Additional context or description"
                rows={3}
              />
            </div>
          </div>

          {/* Entity Association */}
          <EntityAssociation
            entityAssociation={entityAssociation}
            title="Entity Association (Optional)"
            layout="horizontal"
          />

          {/* Tags Section */}
          <div>
            <Label className="text-sm font-medium mb-2 flex items-center gap-1">
              <Tag className="h-4 w-4" />
              Tags
            </Label>

            {/* Current Tags */}
            {formData.tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-3">
                {formData.tags.map((tag, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => removeTag(tag)}
                      className="ml-1 hover:text-red-600"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            )}

            {/* Tag Input */}
            <div className="flex gap-2 mb-3">
              <Input
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyDown={handleTagInputKeyPress}
                placeholder="Add a tag"
                className="flex-1"
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => addTag(tagInput)}
                disabled={!tagInput.trim()}
              >
                Add
              </Button>
            </div>

            {/* Popular Tags */}
            <div>
              <p className="text-xs text-gray-500 mb-2">Popular tags:</p>
              <div className="flex flex-wrap gap-1">
                {POPULAR_TAGS.map((tag) => (
                  <button
                    key={tag}
                    type="button"
                    onClick={() => addTag(tag)}
                    className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                    disabled={formData.tags.includes(tag)}
                  >
                    {tag}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Document Attachments Section */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <Label className="text-sm font-medium flex items-center gap-1">
                <Paperclip className="h-4 w-4" />
                Document Attachments
              </Label>
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs">
                  {existingDocuments.length + attachedFiles.length} total
                </Badge>
                {documentsLoading && (
                  <RefreshCw className="h-4 w-4 animate-spin text-gray-400" />
                )}
              </div>
            </div>

            {/* Existing Documents */}
            {existingDocuments.length > 0 && (
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium text-gray-700">
                    Current Documents ({existingDocuments.length})
                  </h4>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={fetchExistingDocuments}
                    disabled={documentsLoading}
                    className="h-6 px-2 text-xs"
                  >
                    <RefreshCw className="h-3 w-3 mr-1" />
                    Refresh
                  </Button>
                </div>
                <div className="space-y-2 p-3 bg-gray-50 rounded-lg">
                  {existingDocuments.map((doc) => (
                    <div
                      key={doc.id}
                      className="flex items-center justify-between p-2 bg-white rounded border"
                    >
                      <div className="flex items-center gap-2 flex-1">
                        {getFileTypeIcon(doc.name)}
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {doc.name}
                          </p>
                          <div className="flex items-center gap-2 text-xs text-gray-500">
                            <span>{doc.category}</span>
                            <span>•</span>
                            <span>{formatDate(doc.created_at)}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                          onClick={() => handleViewDocument(doc)}
                          title="View Document"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                          onClick={() => handleDownloadDocument(doc)}
                          title="Download Document"
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                          onClick={() => handleDeleteDocument(doc)}
                          disabled={deletingDocuments.has(doc.id)}
                          title="Delete Document"
                        >
                          {deletingDocuments.has(doc.id) ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Trash2 className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* New Document Upload */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">
                Add New Documents
              </h4>
            </div>

            {/* File Upload Area */}
            <div
              className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors"
              onDragOver={handleDragOver}
              onDragEnter={handleDragEnter}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <input
                type="file"
                id="document-upload"
                className="hidden"
                multiple
                accept=".pdf,.doc,.docx,.xlsx,.xls,.png,.jpg,.jpeg"
                onChange={handleFileSelect}
                disabled={isUpdating}
              />
              <div className="space-y-2">
                <Upload className="h-8 w-8 mx-auto text-gray-400" />
                <div>
                  <button
                    type="button"
                    onClick={() =>
                      document.getElementById("document-upload")?.click()
                    }
                    className="text-primary hover:text-primary/80 font-medium"
                    disabled={isUpdating}
                  >
                    Click to upload files
                  </button>
                  <p className="text-sm text-gray-500">or drag and drop</p>
                </div>
                <p className="text-xs text-gray-400">
                  PDF, DOC, DOCX, XLS, XLSX, PNG, JPG up to 10MB each
                </p>
              </div>
            </div>

            {/* New Attached Files List */}
            {attachedFiles.length > 0 && (
              <div className="mt-3 space-y-2">
                <p className="text-sm font-medium text-gray-700">
                  New Files to Upload ({attachedFiles.length})
                </p>
                <div className="space-y-2">
                  {attachedFiles.map((file, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                    >
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4 text-gray-500" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {file.name}
                          </p>
                          <p className="text-xs text-gray-500">
                            {(file.size / 1024 / 1024).toFixed(2)} MB
                          </p>
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => removeAttachedFile(index)}
                        className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                        disabled={isUpdating}
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Document Upload Error */}
            {errors.documents && (
              <div className="mt-3 flex items-center gap-2 p-2 bg-red-50 border border-red-200 rounded-lg">
                <AlertCircle className="h-4 w-4 text-red-600" />
                <span className="text-sm text-red-600">{errors.documents}</span>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isUpdating}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isUpdating}
              className="gap-2"
            >
              {isUpdating ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Save className="h-4 w-4" />
              )}
              {isUpdating
                ? "Updating Transaction..."
                : `Update Transaction${attachedFiles.length > 0 ? ` & Upload ${attachedFiles.length} File${attachedFiles.length > 1 ? "s" : ""}` : ""}`}
            </Button>
          </div>
        </motion.div>
      </DialogContent>

      {/* Document Preview Dialog */}
      <DocumentPreviewDialog
        isOpen={isPreviewOpen}
        onClose={closePreview}
        document={previewDocument}
        title="Transaction Document Preview"
      />
    </Dialog>
  );
}
