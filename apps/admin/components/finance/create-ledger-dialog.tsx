"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import {
  Building,
  CreditCard,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Repeat,
  Plus,
  X,
  Loader2,
  Check,
  Tag,
  Link,
  ChevronRight,
  ArrowLeft,
  ArrowRight,
  FileText,
} from "lucide-react";
import { AnimatedCard } from "@/components/animated-card";
import { ledgerService } from "@/lib/logistics";
import {
  LEDGER_CATEGORIES,
  type LedgerInsert,
} from "@/lib/logistics/operations/ledgers";
import { useAppSelector } from "@/store/hooks";

interface CreateLedgerDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onLedgerCreated?: () => void;
}

// Popular books for quick selection
const POPULAR_BOOKS = [
  "Sales Revenue",
  "Operating Expenses",
  "Freight Costs",
  "Customer Payments",
  "Shipping Fees",
  "Fuel Costs",
  "Equipment Maintenance",
  "Insurance",
  "Office Supplies",
  "Marketing",
];

// Import entity association components
import {
  EntityAssociation,
  useEntityAssociation,
} from "@/components/ui/entity-association";

// Popular tags for quick selection
const POPULAR_TAGS = [
  "revenue",
  "expenses",
  "recurring",
  "monthly",
  "quarterly",
  "annual",
  "operational",
  "administrative",
  "freight",
  "customer",
];

const CATEGORY_ICONS = {
  EXPENSE: TrendingDown,
  REVENUE: TrendingUp,
  RECURRING_EXPENSE: RefreshCw,
  RECURRING_REVENUE: Repeat,
  ASSET: Building,
  LIABILITY: CreditCard,
  EQUITY: Building,
  REALISED_INVOICE: FileText,
} as const;

// Step configuration
const FORM_STEPS = [
  { id: 1, title: "Basic Info", description: "Name and code" },
  { id: 2, title: "Books", description: "Manage books" },
  { id: 3, title: "Tags & Association", description: "Tags and entity links" },
  { id: 4, title: "Review", description: "Review and create" },
];

// Step Indicator Component
const StepIndicator = ({
  currentStep,
  totalSteps,
  onStepClick,
}: {
  currentStep: number;
  totalSteps: number;
  onStepClick: (step: number) => void;
}) => (
  <div className="flex items-center justify-between mb-6 px-4 py-3 bg-gray-50 rounded-lg">
    <div className="flex items-center space-x-4">
      {FORM_STEPS.map((step, index) => (
        <div key={step.id} className="flex items-center">
          <button
            onClick={() => onStepClick(step.id)}
            className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium transition-colors ${
              currentStep === step.id
                ? "bg-primary text-white"
                : currentStep > step.id
                  ? "bg-green-500 text-white"
                  : "bg-gray-200 text-gray-600 hover:bg-gray-300"
            }`}
          >
            {currentStep > step.id ? <Check className="h-4 w-4" /> : step.id}
          </button>
          <div className="ml-2 hidden md:block">
            <p
              className={`text-sm font-medium ${
                currentStep === step.id ? "text-primary" : "text-gray-600"
              }`}
            >
              {step.title}
            </p>
            <p className="text-xs text-gray-500">{step.description}</p>
          </div>
          {index < FORM_STEPS.length - 1 && (
            <ChevronRight className="h-4 w-4 text-gray-400 mx-2" />
          )}
        </div>
      ))}
    </div>
    <div className="text-sm text-gray-500">
      Step {currentStep} of {totalSteps}
    </div>
  </div>
);

export function CreateLedgerDialog({
  isOpen,
  onClose,
  onLedgerCreated,
}: CreateLedgerDialogProps) {
  const [selectedCategory, setSelectedCategory] = useState<
    keyof typeof LEDGER_CATEGORIES | null
  >(null);
  const [customName, setCustomName] = useState("");
  const [isCreating, setIsCreating] = useState(false);
  const [showCustomForm, setShowCustomForm] = useState(false);

  // New state for enhanced features
  const [books, setBooks] = useState<string[]>([]);
  const [bookInput, setBookInput] = useState("");
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState("");
  // Entity association hook
  const entityAssociation = useEntityAssociation();
  const [ledgerCode, setLedgerCode] = useState("");

  // Multistep form state
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 4;

  const { user: authUser } = useAppSelector((state) => state.auth);

  // Generate unique ledger code
  const generateLedgerCode = () => {
    const prefix = "LG";
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    return `${prefix}:${randomSuffix}`;
  };

  // Handle adding books
  const addBook = (book: string) => {
    if (book.trim() && !books.includes(book.trim())) {
      setBooks([...books, book.trim()]);
      setBookInput("");
    }
  };

  const removeBook = (bookToRemove: string) => {
    setBooks(books.filter((book) => book !== bookToRemove));
  };

  // Handle adding tags
  const addTag = (tag: string) => {
    if (tag.trim() && !tags.includes(tag.trim())) {
      setTags([...tags, tag.trim()]);
      setTagInput("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter((tag) => tag !== tagToRemove));
  };

  // Step navigation functions
  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const goToStep = (step: number) => {
    if (step >= 1 && step <= totalSteps) {
      setCurrentStep(step);
    }
  };

  const handleCategorySelect = (category: keyof typeof LEDGER_CATEGORIES) => {
    setSelectedCategory(category);
    setCustomName(LEDGER_CATEGORIES[category].name);
    // Auto-populate tags from category
    setTags([...LEDGER_CATEGORIES[category].tags]);
    // Generate ledger code
    setLedgerCode(generateLedgerCode());
    // Move to first step of the form
    setCurrentStep(1);
  };

  const handleCreateFromCategory = async () => {
    if (!selectedCategory || !authUser?.accountId) return;

    try {
      setIsCreating(true);

      // Use the enhanced createLedgerFromCategory method
      const result = await ledgerService.createLedgerFromCategory(
        selectedCategory,
        authUser.accountId,
        customName.trim(),
        books.length > 0 ? books : undefined,
        tags.length > 0 ? tags : undefined,
        entityAssociation.result.association_table || undefined,
        entityAssociation.result.association_id || undefined
      );

      if (result.success) {
        onLedgerCreated?.();
        onClose();
        resetForm();
      } else {
        console.error("Failed to create ledger:", result.error);
      }
    } catch (error) {
      console.error("Error creating ledger:", error);
    } finally {
      setIsCreating(false);
    }
  };

  const handleCreateCustom = async () => {
    if (!customName.trim() || !authUser?.accountId) return;

    try {
      setIsCreating(true);

      const ledgerData: LedgerInsert = {
        name: customName.trim(),
        books: books.length > 0 ? books : undefined,
        status: "ACTIVE" as any, // Fix for StatusEnum issue
        account_id: authUser.accountId,
        tags: tags.length > 0 ? tags : ["custom"],
        associated_table:
          entityAssociation.result.association_table || undefined,
        associated_id: entityAssociation.result.association_id || undefined,
      };

      const result = await ledgerService.createLedger(ledgerData);

      if (result.success) {
        onLedgerCreated?.();
        onClose();
        resetForm();
      } else {
        console.error("Failed to create custom ledger:", result.error);
      }
    } catch (error) {
      console.error("Error creating custom ledger:", error);
    } finally {
      setIsCreating(false);
    }
  };

  const resetForm = () => {
    setSelectedCategory(null);
    setCustomName("");
    setShowCustomForm(false);
    setBooks([]);
    setBookInput("");
    setTags([]);
    setTagInput("");
    setLedgerCode("");
    setCurrentStep(1);
    entityAssociation.reset();
  };

  const handleClose = () => {
    onClose();
    resetForm();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-semibold">
              Create New Ledger
            </DialogTitle>
          </div>
          <p className="text-sm text-gray-500 mt-1">
            Choose from popular book categories or create a custom ledger
          </p>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          <div className="space-y-6 p-1">
            {!showCustomForm ? (
              <>
                {/* Popular Categories */}
                <div>
                  <h3 className="text-lg font-medium mb-4">
                    Popular Book Categories
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {Object.entries(LEDGER_CATEGORIES).map(
                      ([key, category]) => {
                        const IconComponent =
                          CATEGORY_ICONS[key as keyof typeof CATEGORY_ICONS];
                        const isSelected = selectedCategory === key;

                        return (
                          <motion.div
                            key={key}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <div
                              className={`cursor-pointer transition-all border-2 rounded-lg ${
                                isSelected
                                  ? "border-primary bg-primary/5"
                                  : "border-gray-200 hover:border-gray-300"
                              }`}
                              onClick={() =>
                                handleCategorySelect(
                                  key as keyof typeof LEDGER_CATEGORIES
                                )
                              }
                            >
                              <AnimatedCard className="border-0 shadow-none p-4">
                                <div className="flex items-start gap-3">
                                  <div
                                    className="p-2 rounded-lg"
                                    style={{
                                      backgroundColor: `${category.color}20`,
                                      color: category.color,
                                    }}
                                  >
                                    <IconComponent className="h-5 w-5" />
                                  </div>
                                  <div className="flex-1">
                                    <div className="flex items-center justify-between">
                                      <h4 className="font-medium text-gray-900">
                                        {category.name}
                                      </h4>
                                      {isSelected && (
                                        <Check className="h-4 w-4 text-primary" />
                                      )}
                                    </div>
                                    <p className="text-sm text-gray-500 mt-1">
                                      {category.description}
                                    </p>
                                    <div className="flex flex-wrap gap-1 mt-2">
                                      {category.tags
                                        .slice(0, 2)
                                        .map((tag, index) => (
                                          <Badge
                                            key={index}
                                            variant="secondary"
                                            className="text-xs"
                                          >
                                            {tag}
                                          </Badge>
                                        ))}
                                      {category.tags.length > 2 && (
                                        <Badge
                                          variant="secondary"
                                          className="text-xs"
                                        >
                                          +{category.tags.length - 2}
                                        </Badge>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </AnimatedCard>
                            </div>
                          </motion.div>
                        );
                      }
                    )}
                  </div>
                </div>

                {/* Selected Category Form */}
                {selectedCategory && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="border-t pt-6"
                  >
                    <h3 className="text-lg font-medium mb-4">
                      Customize Ledger
                    </h3>

                    {/* Step Indicator */}
                    <StepIndicator
                      currentStep={currentStep}
                      totalSteps={totalSteps}
                      onStepClick={goToStep}
                    />

                    <div className="space-y-6">
                      {/* Step 1: Basic Info */}
                      {currentStep === 1 && (
                        <motion.div
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -20 }}
                          className="space-y-4"
                        >
                          {/* Ledger Name */}
                          <div>
                            <Label className="text-sm font-medium text-gray-700 mb-2">
                              Ledger Name
                            </Label>
                            <Input
                              value={customName}
                              onChange={(e) => setCustomName(e.target.value)}
                              placeholder="Enter ledger name"
                              className="w-full"
                            />
                          </div>
                        </motion.div>
                      )}

                      {/* Step 2: Books */}
                      {currentStep === 2 && (
                        <motion.div
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -20 }}
                          className="space-y-4"
                        >
                          <div>
                            <Label className="text-sm font-medium text-gray-700 mb-2">
                              Books
                            </Label>
                            <div className="space-y-3">
                              <div className="flex gap-2">
                                <Input
                                  value={bookInput}
                                  onChange={(e) => setBookInput(e.target.value)}
                                  placeholder="Add book name..."
                                  className="flex-1"
                                  onKeyDown={(e) => {
                                    if (e.key === "Enter") {
                                      e.preventDefault();
                                      addBook(bookInput);
                                    }
                                  }}
                                />
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={() => addBook(bookInput)}
                                  disabled={!bookInput.trim()}
                                >
                                  <Plus className="h-4 w-4" />
                                </Button>
                              </div>

                              {/* Selected Books */}
                              {books.length > 0 && (
                                <div className="flex flex-wrap gap-2">
                                  {books.map((book, index) => (
                                    <Badge
                                      key={index}
                                      variant="secondary"
                                      className="flex items-center gap-1"
                                    >
                                      {book}
                                      <X
                                        className="h-3 w-3 cursor-pointer hover:text-red-500"
                                        onClick={() => removeBook(book)}
                                      />
                                    </Badge>
                                  ))}
                                </div>
                              )}

                              {/* Popular Books */}
                              <div>
                                <p className="text-xs text-gray-500 mb-2">
                                  Popular books:
                                </p>
                                <div className="flex flex-wrap gap-2">
                                  {POPULAR_BOOKS.map((book) => (
                                    <Badge
                                      key={book}
                                      variant="outline"
                                      className="cursor-pointer hover:bg-gray-50"
                                      onClick={() => addBook(book)}
                                    >
                                      {book}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      )}

                      {/* Step 3: Tags & Association */}
                      {currentStep === 3 && (
                        <motion.div
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -20 }}
                          className="space-y-6"
                        >
                          {/* Tags Section */}
                          <div>
                            <Label className="text-sm font-medium text-gray-700 mb-2">
                              <Tag className="h-4 w-4 inline mr-1" />
                              Tags
                            </Label>
                            <div className="space-y-3">
                              <div className="flex gap-2">
                                <Input
                                  value={tagInput}
                                  onChange={(e) => setTagInput(e.target.value)}
                                  placeholder="Add tag..."
                                  className="flex-1"
                                  onKeyDown={(e) => {
                                    if (e.key === "Enter") {
                                      e.preventDefault();
                                      addTag(tagInput);
                                    }
                                  }}
                                />
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={() => addTag(tagInput)}
                                  disabled={!tagInput.trim()}
                                >
                                  <Plus className="h-4 w-4" />
                                </Button>
                              </div>

                              {/* Selected Tags */}
                              {tags.length > 0 && (
                                <div className="flex flex-wrap gap-2">
                                  {tags.map((tag, index) => (
                                    <Badge
                                      key={index}
                                      variant="secondary"
                                      className="flex items-center gap-1"
                                    >
                                      {tag}
                                      <X
                                        className="h-3 w-3 cursor-pointer hover:text-red-500"
                                        onClick={() => removeTag(tag)}
                                      />
                                    </Badge>
                                  ))}
                                </div>
                              )}

                              {/* Popular Tags */}
                              <div>
                                <p className="text-xs text-gray-500 mb-2">
                                  Popular tags:
                                </p>
                                <div className="flex flex-wrap gap-2">
                                  {POPULAR_TAGS.map((tag) => (
                                    <Badge
                                      key={tag}
                                      variant="outline"
                                      className="cursor-pointer hover:bg-gray-50"
                                      onClick={() => addTag(tag)}
                                    >
                                      {tag}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Entity Association Section */}
                          <EntityAssociation
                            entityAssociation={entityAssociation}
                            title="Entity Association (Optional)"
                            layout="horizontal"
                          />
                        </motion.div>
                      )}

                      {/* Step 4: Review */}
                      {currentStep === 4 && (
                        <motion.div
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -20 }}
                          className="space-y-6"
                        >
                          <div className="bg-gray-50 p-4 rounded-lg">
                            <h4 className="font-medium text-gray-900 mb-3">
                              Review Ledger Details
                            </h4>

                            <div className="space-y-3">
                              <div className="flex items-center gap-3">
                                <div
                                  className="p-2 rounded-lg"
                                  style={{
                                    backgroundColor: `${LEDGER_CATEGORIES[selectedCategory].color}20`,
                                    color:
                                      LEDGER_CATEGORIES[selectedCategory].color,
                                  }}
                                >
                                  {(() => {
                                    const IconComponent =
                                      CATEGORY_ICONS[selectedCategory];
                                    return (
                                      <IconComponent className="h-4 w-4" />
                                    );
                                  })()}
                                </div>
                                <div>
                                  <p className="font-medium">
                                    {LEDGER_CATEGORIES[selectedCategory].name}
                                  </p>
                                  <p className="text-sm text-gray-500">
                                    {
                                      LEDGER_CATEGORIES[selectedCategory]
                                        .description
                                    }
                                  </p>
                                </div>
                              </div>

                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                  <span className="font-medium text-gray-700">
                                    Name:
                                  </span>
                                  <span className="ml-2">{customName}</span>
                                </div>
                                <div>
                                  <span className="font-medium text-gray-700">
                                    Code:
                                  </span>
                                  <span className="ml-2">{ledgerCode}</span>
                                </div>
                                <div>
                                  <span className="font-medium text-gray-700">
                                    Books:
                                  </span>
                                  <span className="ml-2">
                                    {books.length > 0
                                      ? books.join(", ")
                                      : "None"}
                                  </span>
                                </div>
                                <div>
                                  <span className="font-medium text-gray-700">
                                    Tags:
                                  </span>
                                  <span className="ml-2">
                                    {tags.length > 0 ? tags.join(", ") : "None"}
                                  </span>
                                </div>
                                {entityAssociation.result.association_table && (
                                  <div className="md:col-span-2">
                                    <span className="font-medium text-gray-700">
                                      Association:
                                    </span>
                                    <span className="ml-2">
                                      {
                                        entityAssociation.result
                                          .association_table
                                      }
                                      {entityAssociation.result
                                        .association_id && (
                                        <span className="text-gray-500">
                                          {" - "}
                                          {entityAssociation.state.entities.find(
                                            (e) =>
                                              e.id ===
                                              entityAssociation.result
                                                .association_id
                                          )
                                            ? entityAssociation.getEntityDisplayName(
                                                entityAssociation.state.entities.find(
                                                  (e) =>
                                                    e.id ===
                                                    entityAssociation.result
                                                      .association_id
                                                )!,
                                                entityAssociation.result
                                                  .association_table
                                              )
                                            : entityAssociation.result
                                                .association_id}
                                        </span>
                                      )}
                                    </span>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      )}

                      {/* Step Navigation */}
                      <div className="flex justify-between items-center pt-6 border-t">
                        <div className="flex gap-2">
                          {currentStep > 1 && (
                            <Button
                              variant="outline"
                              onClick={prevStep}
                              className="flex items-center gap-2"
                            >
                              <ArrowLeft className="h-4 w-4" />
                              Previous
                            </Button>
                          )}
                          <Button
                            variant="outline"
                            onClick={() => setSelectedCategory(null)}
                          >
                            Cancel
                          </Button>
                        </div>

                        <div className="flex gap-2">
                          {currentStep < totalSteps ? (
                            <Button
                              onClick={nextStep}
                              disabled={currentStep === 1 && !customName.trim()}
                              className="flex items-center gap-2"
                            >
                              Next
                              <ArrowRight className="h-4 w-4" />
                            </Button>
                          ) : (
                            <Button
                              onClick={handleCreateFromCategory}
                              disabled={!customName.trim() || isCreating}
                              className="flex items-center gap-2"
                            >
                              {isCreating ? (
                                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                              ) : (
                                <Plus className="h-4 w-4 mr-2" />
                              )}
                              Create Ledger
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Custom Ledger Option */}
                {!selectedCategory && (
                  <div className="border-t pt-6">
                    <div className="text-center">
                      <p className="text-gray-500 mb-4">
                        Don't see what you're looking for?
                      </p>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setShowCustomForm(true);
                          setLedgerCode(generateLedgerCode());
                          setCurrentStep(1);
                        }}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Create Custom Ledger
                      </Button>
                    </div>
                  </div>
                )}
              </>
            ) : (
              /* Custom Ledger Form */
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <h3 className="text-lg font-medium mb-4">
                  Create Custom Ledger
                </h3>

                {/* Step Indicator */}
                <StepIndicator
                  currentStep={currentStep}
                  totalSteps={totalSteps}
                  onStepClick={goToStep}
                />

                <div className="space-y-6">
                  {/* Ledger Name */}
                  <div>
                    <Label className="text-sm font-medium text-gray-700 mb-2">
                      Ledger Name *
                    </Label>
                    <Input
                      value={customName}
                      onChange={(e) => setCustomName(e.target.value)}
                      placeholder="Enter ledger name"
                      className="w-full"
                    />
                  </div>

                  {/* Books Section */}
                  <div>
                    <Label className="text-sm font-medium text-gray-700 mb-2">
                      Books
                    </Label>
                    <div className="space-y-3">
                      <div className="flex gap-2">
                        <Input
                          value={bookInput}
                          onChange={(e) => setBookInput(e.target.value)}
                          placeholder="Add book name..."
                          className="flex-1"
                          onKeyDown={(e) => {
                            if (e.key === "Enter") {
                              e.preventDefault();
                              addBook(bookInput);
                            }
                          }}
                        />
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => addBook(bookInput)}
                          disabled={!bookInput.trim()}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>

                      {/* Selected Books */}
                      {books.length > 0 && (
                        <div className="flex flex-wrap gap-2">
                          {books.map((book, index) => (
                            <Badge
                              key={index}
                              variant="secondary"
                              className="flex items-center gap-1"
                            >
                              {book}
                              <X
                                className="h-3 w-3 cursor-pointer hover:text-red-500"
                                onClick={() => removeBook(book)}
                              />
                            </Badge>
                          ))}
                        </div>
                      )}

                      {/* Popular Books */}
                      <div>
                        <p className="text-xs text-gray-500 mb-2">
                          Popular books:
                        </p>
                        <div className="flex flex-wrap gap-2">
                          {POPULAR_BOOKS.map((book) => (
                            <Badge
                              key={book}
                              variant="outline"
                              className="cursor-pointer hover:bg-gray-50"
                              onClick={() => addBook(book)}
                            >
                              {book}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Tags Section */}
                  <div>
                    <Label className="text-sm font-medium text-gray-700 mb-2">
                      <Tag className="h-4 w-4 inline mr-1" />
                      Tags
                    </Label>
                    <div className="space-y-3">
                      <div className="flex gap-2">
                        <Input
                          value={tagInput}
                          onChange={(e) => setTagInput(e.target.value)}
                          placeholder="Add tag..."
                          className="flex-1"
                          onKeyDown={(e) => {
                            if (e.key === "Enter") {
                              e.preventDefault();
                              addTag(tagInput);
                            }
                          }}
                        />
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => addTag(tagInput)}
                          disabled={!tagInput.trim()}
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>

                      {/* Selected Tags */}
                      {tags.length > 0 && (
                        <div className="flex flex-wrap gap-2">
                          {tags.map((tag, index) => (
                            <Badge
                              key={index}
                              variant="secondary"
                              className="flex items-center gap-1"
                            >
                              {tag}
                              <X
                                className="h-3 w-3 cursor-pointer hover:text-red-500"
                                onClick={() => removeTag(tag)}
                              />
                            </Badge>
                          ))}
                        </div>
                      )}

                      {/* Popular Tags */}
                      <div>
                        <p className="text-xs text-gray-500 mb-2">
                          Popular tags:
                        </p>
                        <div className="flex flex-wrap gap-2">
                          {POPULAR_TAGS.map((tag) => (
                            <Badge
                              key={tag}
                              variant="outline"
                              className="cursor-pointer hover:bg-gray-50"
                              onClick={() => addTag(tag)}
                            >
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Entity Association Section */}
                  <EntityAssociation
                    entityAssociation={entityAssociation}
                    title="Entity Association (Optional)"
                    layout="horizontal"
                  />

                  <div className="flex justify-end gap-3">
                    <Button
                      variant="outline"
                      onClick={() => setShowCustomForm(false)}
                    >
                      Back
                    </Button>
                    <Button
                      onClick={handleCreateCustom}
                      disabled={!customName.trim() || isCreating}
                    >
                      {isCreating ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      ) : (
                        <Plus className="h-4 w-4 mr-2" />
                      )}
                      Create Ledger
                    </Button>
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
