"use client";

import { useMemo, useState, useEffect } from "react";
import { redirect } from "next/navigation";
import { motion } from "framer-motion";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  Activity,
  Calendar,
  Tag,
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  Loader2,
  AlertCircle,
  CheckCircle2,
  Clock,
  X,
} from "lucide-react";
import { AnimatedCard } from "@/components/animated-card";
import { StatusBadge } from "@/components/status-badge";
import {
  ledgerService,
  transactionService,
  type Ledger,
  type LedgerWithTransactions,
  type LedgerStats,
  type Transaction,
  type TransactionInsert,
  StatusEnum,
} from "@/lib/logistics";
import { useAppSelector } from "@/store/hooks";

interface LedgerDialogProps {
  isOpen: boolean;
  onClose: () => void;
  ledger: Ledger | null;
  onLedgerUpdate?: () => void;
}

export function LedgerDialog({
  isOpen,
  onClose,
  ledger,
  onLedgerUpdate,
}: LedgerDialogProps) {
  const [loading, setLoading] = useState(true);
  const [ledgerData, setLedgerData] = useState<LedgerWithTransactions | null>(
    null
  );
  const [ledgerStats, setLedgerStats] = useState<LedgerStats | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [isAddingTransaction, setIsAddingTransaction] = useState(false);

  const { user: authUser } = useAppSelector((state) => state.auth);

  // Fetch ledger data when dialog opens
  useEffect(() => {
    if (isOpen && ledger) {
      fetchLedgerData();
    }
  }, [isOpen, ledger]);

  const fetchLedgerData = async () => {
    if (!ledger) return;

    try {
      setLoading(true);

      // Fetch ledger with transactions
      const [ledgerResult, statsResult, transactionsResult] = await Promise.all(
        [
          ledgerService.getLedgerById(ledger.id),
          ledgerService.getLedgerStats(ledger.id),
          transactionService.getTransactionsByLedger(ledger.id, {
            limit: 100,
            column: "created_at",
            ascending: false,
          }),
        ]
      );

      if (ledgerResult.success && ledgerResult.data) {
        setLedgerData(ledgerResult.data);
      }

      if (statsResult.success && statsResult.data) {
        setLedgerStats(statsResult.data);
      }

      if (transactionsResult.success && transactionsResult.data) {
        setTransactions(transactionsResult.data);
      }
    } catch (error) {
      console.error("Error fetching ledger data:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async (searchTerm: string) => {
    setSearchTerm(searchTerm);
    if (!ledger) return;

    try {
      const result = await transactionService.getTransactionsByLedger(
        ledger.id,
        {
          search: searchTerm,
          limit: 100,
          column: "created_at",
          ascending: false,
        }
      );

      if (result.success && result.data) {
        setTransactions(result.data);
      }
    } catch (error) {
      console.error("Error searching transactions:", error);
    }
  };

  const filteredTransactions = useMemo(
    () =>
      transactions.filter((transaction) => {
        const matchesStatus =
          filterStatus === "all" ||
          transaction.status?.toLowerCase() === filterStatus.toLowerCase();
        // const matchesBook = filterBook === "all" || transaction.book === filterBook;
        const matchesSearch =
          !searchTerm ||
          transaction.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          transaction.context?.toLowerCase().includes(searchTerm.toLowerCase());
        return matchesStatus && matchesSearch;
      }),
    [transactions]
  );

  // Calculate filtered stats based on the currently filtered transactions
  const totalIncomeFiltered = useMemo(
    () =>
      filteredTransactions
        .filter((t: any) => t.type === "DEBIT")
        .reduce((sum: number, t: any) => sum + t.amount, 0),
    [filteredTransactions]
  );

  const totalExpensesFiltered = useMemo(
    () =>
      Math.abs(
        filteredTransactions
          .filter((t: any) => t.type === "CREDIT")
          .reduce((sum: number, t: any) => sum + t.amount, 0)
      ),
    [filteredTransactions]
  );

  const filteredStats = {
    totalIncome: totalIncomeFiltered,
    totalExpenses: totalExpensesFiltered,
    netAmount: totalIncomeFiltered - totalExpensesFiltered,
    totalTransactions: filteredTransactions.length,
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getTransactionIcon = (amount: number) => {
    return amount >= 0 ? (
      <TrendingUp className="h-4 w-4 text-green-600" />
    ) : (
      <TrendingDown className="h-4 w-4 text-red-600" />
    );
  };

  const getStatusIcon = (status: string) => {
    switch (status?.toLowerCase()) {
      case "completed":
      case "active":
        return <CheckCircle2 className="h-3 w-3" />;
      case "pending":
        return <Clock className="h-3 w-3" />;
      default:
        return <AlertCircle className="h-3 w-3" />;
    }
  };

  function OpenLedger(id: string) {
    redirect(`/finance/${id}`);
  }

  if (!ledger) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-primary/10">
                <DollarSign className="h-5 w-5 text-primary" />
              </div>
              <div className="space-y-4">
                <div>
                  <DialogTitle className="text-xl font-semibold">
                    {ledger.name}
                  </DialogTitle>
                  <div className="flex items-center gap-2 mt-1">
                    <StatusBadge status={ledger.status} />
                    {ledger.tags && ledger.tags.length > 0 && (
                      <div className="flex gap-1">
                        {ledger.tags.slice(0, 3).map((tag, index) => (
                          <Badge
                            key={index}
                            variant="secondary"
                            className="text-xs"
                          >
                            {tag}
                          </Badge>
                        ))}
                        {ledger.tags.length > 3 && (
                          <Badge variant="secondary" className="text-xs">
                            +{ledger.tags.length - 3}
                          </Badge>
                        )}
                      </div>
                    )}
                  </div>
                </div>
                <Button size="sm" onClick={() => OpenLedger(ledger.id)}>
                  Open Ledger
                </Button>
              </div>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col gap-6">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <>
              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 flex-shrink-0">
                <AnimatedCard className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-green-100">
                      <TrendingUp className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Total Income</p>
                      <p className="text-lg font-semibold text-green-600">
                        {formatCurrency(filteredStats?.totalIncome || 0)}
                      </p>
                    </div>
                  </div>
                </AnimatedCard>

                <AnimatedCard className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-red-100">
                      <TrendingDown className="h-4 w-4 text-red-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Total Expenses</p>
                      <p className="text-lg font-semibold text-red-600">
                        {formatCurrency(filteredStats?.totalExpenses || 0)}
                      </p>
                    </div>
                  </div>
                </AnimatedCard>

                <AnimatedCard className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-blue-100">
                      <DollarSign className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Net Amount</p>
                      <p
                        className={`text-lg font-semibold ${
                          (filteredStats?.netAmount || 0) >= 0
                            ? "text-green-600"
                            : "text-red-600"
                        }`}
                      >
                        {formatCurrency(filteredStats?.netAmount || 0)}
                      </p>
                    </div>
                  </div>
                </AnimatedCard>

                <AnimatedCard className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-purple-100">
                      <Activity className="h-4 w-4 text-purple-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Transactions</p>
                      <p className="text-lg font-semibold">
                        {filteredStats?.totalTransactions || 0}
                      </p>
                    </div>
                  </div>
                </AnimatedCard>
              </div>

              {/* Transactions Section */}
              <div className="flex-1 overflow-hidden flex flex-col">
                <div className="flex items-center justify-between mb-4 flex-shrink-0">
                  <div className="flex items-center gap-2">
                    <h3 className="text-lg font-medium">Transactions</h3>
                    <Badge variant="secondary">
                      {filteredTransactions.length} items
                    </Badge>
                  </div>

                  <div className="flex items-center gap-2">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                      <input
                        type="text"
                        placeholder="Search transactions..."
                        value={searchTerm}
                        onChange={(e) => handleSearch(e.target.value)}
                        className="pl-10 pr-4 py-2 text-sm border border-gray-200 rounded-lg w-64 bg-white focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary"
                      />
                    </div>
                  </div>
                </div>

                {/* Filter Buttons */}
                <div className="flex gap-2 mb-4 flex-shrink-0">
                  {["all", "active", "pending", "completed", "cancelled"].map(
                    (status) => (
                      <Button
                        key={status}
                        variant={
                          filterStatus === status ? "default" : "outline"
                        }
                        size="sm"
                        onClick={() => setFilterStatus(status)}
                        className="capitalize"
                      >
                        {status}
                      </Button>
                    )
                  )}
                </div>

                {/* Transactions Table */}
                <div className="flex-1 overflow-auto border border-gray-200 rounded-lg">
                  <table className="w-full">
                    <thead className="bg-gray-50 sticky top-0">
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                          Date
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                          Transaction
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                          Amount
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {filteredTransactions.length > 0 ? (
                        filteredTransactions.map((transaction: any) => (
                          <tr key={transaction.id} className="hover:bg-gray-50">
                            <td className="px-4 py-3 text-sm text-gray-500">
                              {formatDate(transaction?.created_at)}
                            </td>
                            <td className="px-4 py-3">
                              <div className="flex items-center gap-3">
                                {getTransactionIcon(transaction.amount)}
                                <div className="-space-y-4">
                                  <p className="font-medium text-gray-900">
                                    {transaction.name}
                                  </p>
                                  {transaction.context && (
                                    <small className="text-gray-500">
                                      {transaction.context}
                                    </small>
                                  )}
                                </div>
                              </div>
                            </td>
                            <td className="px-4 py-3">
                              <span
                                className={`font-medium ${
                                  transaction.amount >= 0
                                    ? "text-green-600"
                                    : "text-red-600"
                                }`}
                              >
                                {formatCurrency(transaction.amount)}
                              </span>
                            </td>
                            <td className="px-4 py-3">
                              <Badge
                                variant={
                                  transaction.status === "ACTIVE"
                                    ? "default"
                                    : transaction.status === "PENDING"
                                      ? "secondary"
                                      : "outline"
                                }
                                className="flex items-center gap-1"
                              >
                                {getStatusIcon(transaction.status)}
                                {transaction.status}
                              </Badge>
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td
                            colSpan={5}
                            className="px-4 py-8 text-center text-gray-500"
                          >
                            <Activity className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                            <p className="text-sm">No transactions found</p>
                            <p className="text-xs text-gray-400 mt-1">
                              {searchTerm
                                ? "Try adjusting your search terms"
                                : "Transactions will appear here when created"}
                            </p>
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
