"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  Di<PERSON>Header,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import {
  AlertTriangle,
  Trash2,
  X,
  Loader2,
  DollarSign,
  Calendar,
  Tag,
  AlertCircle,
} from "lucide-react";
import { transactionService } from "@/lib/logistics";

interface DeleteTransactionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  transaction: any;
  onTransactionDeleted?: () => void;
}

export function DeleteTransactionDialog({
  isOpen,
  onClose,
  transaction,
  onTransactionDeleted,
}: DeleteTransactionDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string>("");

  const handleClose = () => {
    setError("");
    onClose();
  };

  const handleDelete = async () => {
    if (!transaction?.id) return;

    try {
      setIsDeleting(true);
      setError("");

      const result = await transactionService.deleteTransaction(transaction.id);

      if (result.success) {
        onTransactionDeleted?.();
        handleClose();
      } else {
        setError(result.error || "Failed to delete transaction");
      }
    } catch (error) {
      console.error("Error deleting transaction:", error);
      setError("An unexpected error occurred");
    } finally {
      setIsDeleting(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case "completed":
      case "active":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (!transaction) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <div className="p-2 rounded-lg bg-red-100">
              <AlertTriangle className="h-5 w-5 text-red-600" />
            </div>
            Delete Transaction
          </DialogTitle>
        </DialogHeader>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="space-y-6"
        >
          {/* Warning Message */}
          <div className="flex items-start gap-3 p-4 bg-red-50 border border-red-200 rounded-lg">
            <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="font-medium text-red-800 mb-1">
                Are you sure you want to delete this transaction?
              </h4>
              <p className="text-sm text-red-700">
                This action cannot be undone. The transaction will be
                permanently removed from the ledger and all associated data will
                be lost.
              </p>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <span className="text-sm text-red-600">{error}</span>
            </div>
          )}

          {/* Transaction Details */}
          <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-3">
              Transaction Details
            </h4>

            <div className="space-y-3">
              {/* Name and Amount */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-gray-500" />
                  <span className="font-medium text-gray-900">
                    {transaction.name}
                  </span>
                </div>
                <span
                  className={`font-bold ${
                    transaction.amount >= 0 ? "text-green-600" : "text-red-600"
                  }`}
                >
                  {formatCurrency(transaction.amount)}
                </span>
              </div>

              {/* Status */}
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Status</span>
                <Badge className={getStatusColor(transaction.status)}>
                  {transaction.status}
                </Badge>
              </div>

              {/* Value */}
              {transaction.value !== transaction.amount && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">Value</span>
                  <span className="text-sm font-medium">
                    {formatCurrency(transaction.value)}
                  </span>
                </div>
              )}

              {/* Created Date */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-500">Created</span>
                </div>
                <span className="text-sm text-gray-700">
                  {formatDate(transaction.created_at)}
                </span>
              </div>

              {/* Context */}
              {transaction.context && (
                <div>
                  <span className="text-sm text-gray-500 block mb-1">
                    Context
                  </span>
                  <p className="text-sm text-gray-700 bg-white p-2 rounded border">
                    {transaction.context}
                  </p>
                </div>
              )}

              {/* Tags */}
              {transaction.tags && transaction.tags.length > 0 && (
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <Tag className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-500">Tags</span>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {transaction.tags.map((tag: string, index: number) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isDeleting}
              className="gap-2"
            >
              {isDeleting ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Trash2 className="h-4 w-4" />
              )}
              Delete Transaction
            </Button>
          </div>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
