"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Textarea } from "@workspace/ui/components/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  DollarSign,
  Plus,
  X,
  Loader2,
  Tag,
  AlertCircle,
  Upload,
  FileText,
  Trash2,
  Paperclip,
  BookOpen,
} from "lucide-react";
import {
  transactionService,
  StatusEnum,
  ledgerService,
  invoiceService,
  notificationService,
} from "@/lib/logistics";
import { useAppSelector } from "@/store/hooks";
import { showToast } from "@/lib/utils";

// Import entity association components
import {
  EntityAssociation,
  useEntityAssociation,
} from "@/components/ui/entity-association";

interface CreateTransactionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  ledgerId: string;
  onTransactionCreated?: () => void;
}

// Popular tags for quick selection
const POPULAR_TAGS = [
  "income",
  "expense",
  "payment",
  "refund",
  "fee",
  "commission",
  "shipping",
  "freight",
  "customs",
  "insurance",
  "fuel",
  "maintenance",
  "office",
  "marketing",
];

// Default book options for ledgers
const DEFAULT_BOOKS = [
  "General Ledger",
  "Sales Revenue",
  "Operating Expenses",
  "Freight Costs",
  "Customer Payments",
  "Shipping Fees",
  "Fuel Costs",
  "Equipment Maintenance",
  "Insurance",
  "Office Supplies",
  "Marketing",
  "Accounts Receivable",
  "Accounts Payable",
  "Cash Flow",
  "Petty Cash",
];

// Status options
const STATUS_OPTIONS = [
  {
    value: "PENDING",
    label: "Pending",
    color: "bg-yellow-100 text-yellow-800",
  },
  { value: "ACTIVE", label: "Active", color: "bg-green-100 text-green-800" },
  {
    value: "COMPLETED",
    label: "Completed",
    color: "bg-blue-100 text-blue-800",
  },
  { value: "CANCELLED", label: "Cancelled", color: "bg-red-100 text-red-800" },
  { value: "EXCESS", label: "Excess", color: "bg-amber-100 text-amber-800" },
];

type TransactionType = "CREDIT" | "DEBIT";

export function CreateTransactionDialog({
  isOpen,
  onClose,
  ledgerId,
  onTransactionCreated,
}: CreateTransactionDialogProps) {
  const [formData, setFormData] = useState({
    name: "",
    amount: "",
    type: "DEBIT" as TransactionType,
    context: "",
    status: "PENDING" as StatusEnum,
    tags: [] as string[],
    book: "", // Selected book from ledger
    associated_table: "",
    associated_id: "",
  });
  const [tagInput, setTagInput] = useState("");
  const [isCreating, setIsCreating] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  // Entity association hook
  const entityAssociation = useEntityAssociation();

  // Document attachment state
  const [attachedFiles, setAttachedFiles] = useState<File[]>([]);

  // Ledger and book state
  const [ledgerData, setLedgerData] = useState<any>(null);
  const [availableBooks, setAvailableBooks] = useState<string[]>([]);

  // Entity association state
  const [availableInvoices, setAvailableInvoices] = useState<any[]>([]);
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null);
  const [isLoadingInvoices, setIsLoadingInvoices] = useState(false);

  // Update form data when entity association changes
  React.useEffect(() => {
    setFormData((prev) => ({
      ...prev,
      associated_table: entityAssociation.result.association_table || "",
      associated_id: entityAssociation.result.association_id || "",
    }));
  }, [
    entityAssociation.result.association_table,
    entityAssociation.result.association_id,
  ]);

  // Handle invoice selection separately
  React.useEffect(() => {
    if (entityAssociation.result.association_table === "invoices") {
      if (entityAssociation.result.association_id) {
        const invoice = availableInvoices.find(
          (inv) => inv.id === entityAssociation.result.association_id
        );
        setSelectedInvoice(invoice);
      } else {
        setSelectedInvoice(null);
      }
      // Update entity association with invoice data if available
      if (availableInvoices.length > 0) {
        entityAssociation.setCustomEntities(availableInvoices);
      }
    } else {
      setSelectedInvoice(null);
    }
  }, [
    entityAssociation.result.association_table,
    entityAssociation.result.association_id,
    availableInvoices,
  ]);

  // Load invoices when invoice entity type is selected
  React.useEffect(() => {
    if (
      entityAssociation.result.association_table === "invoices" &&
      availableInvoices.length === 0
    ) {
      fetchInvoices();
    }
  }, [entityAssociation.result.association_table]);

  const { user: authUser } = useAppSelector((state) => state.auth);

  // Fetch ledger data when dialog opens
  useEffect(() => {
    if (isOpen && ledgerId) {
      fetchLedgerData();
    }
  }, [isOpen, ledgerId]);

  const fetchLedgerData = async () => {
    try {
      const result = await ledgerService.getLedgerById(ledgerId);
      if (result.success && result.data) {
        setLedgerData(result.data);
        // Set available books from ledger or use default books
        const books =
          result.data.books && result.data.books.length > 0
            ? result.data.books
            : DEFAULT_BOOKS;

        setAvailableBooks(books || []);
      }
    } catch (error) {
      console.error("Error fetching ledger data:", error);
      // Fallback to default books
      setAvailableBooks(DEFAULT_BOOKS);
    }
  };

  const fetchInvoices = async () => {
    if (!authUser?.accountId) return;

    try {
      setIsLoadingInvoices(true);
      const result = await invoiceService.getAllInvoicesWithCustomers({
        limit: 100,
        offset: 0,
      });

      if (result.success && result.data) {
        // Filter for unpaid or partially paid invoices
        const unpaidInvoices = result.data.filter(
          (invoice: any) =>
            invoice.status !== "PAID" && invoice.status !== "CANCELLED"
        );
        setAvailableInvoices(unpaidInvoices);

        // Update entity association if invoices are selected
        if (entityAssociation.state.selectedTable === "invoices") {
          entityAssociation.setCustomEntities(unpaidInvoices);
        }
      }
    } catch (error) {
      console.error("Error fetching invoices:", error);
      showToast("Failed to load invoices", "error");
    } finally {
      setIsLoadingInvoices(false);
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      amount: "",
      type: "DEBIT" as TransactionType,
      context: "",
      status: "PENDING" as StatusEnum,
      tags: [],
      book: "",
      associated_table: "",
      associated_id: "",
    });
    setTagInput("");
    setErrors({});
    setAttachedFiles([]);
    setSelectedInvoice(null);
    entityAssociation.reset();
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.name.trim()) {
      newErrors.name = "Transaction name is required";
    }

    if (!formData.amount || isNaN(Number(formData.amount))) {
      newErrors.amount = "Valid amount is required";
    } else if (Number(formData.amount) === 0) {
      newErrors.amount = "Amount cannot be zero";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm() || !authUser?.accountId) return;

    try {
      setIsCreating(true);

      const transactionData = {
        name: formData.name.trim(),
        amount: Number(formData.amount),
        type: formData.type,
        context: formData.context.trim() || undefined,
        status: formData.status,
        tags: formData.tags.length > 0 ? formData.tags : undefined,
        book: formData.book || undefined, // Include selected book
        ledger_id: ledgerId,
        account_id: authUser.accountId,
        associated_table: formData.associated_table || undefined,
        associated_id: formData.associated_id || undefined,
      };

      // Special handling for invoice payments (including excess payments)
      if (formData.associated_table === "invoices" && selectedInvoice) {
        const transactionAmount = Number(formData.amount);
        const invoiceTotal = selectedInvoice.total || 0;

        // Create transaction with invoice association and excess payment handling
        const result = await transactionService.createTransactionWithDocuments(
          transactionData,
          attachedFiles,
          { invoiceTotal, invoiceId: selectedInvoice.id }
        );

        if (result.success && result.data) {
          const isExcessPayment = transactionAmount > invoiceTotal;
          const excessAmount = isExcessPayment
            ? transactionAmount - invoiceTotal
            : 0;

          // Update invoice amount (deduct payment up to invoice total)
          const paymentToInvoice = Math.min(transactionAmount, invoiceTotal);
          const newInvoiceTotal = invoiceTotal - paymentToInvoice;

          // Determine new status based on payment amount
          let newStatus: string;
          if (newInvoiceTotal <= 0) {
            newStatus = "PAID";
          } else {
            // Mark as partial payment for follow-up
            newStatus = "PARTIAL_PAYMENT";
          }

          const updateResult = await invoiceService.updateInvoice(
            selectedInvoice.id,
            {
              total: newInvoiceTotal,
              status: newStatus,
            }
          );

          if (updateResult.success) {
            // Create notification for payment
            let notificationMessage: string;
            if (isExcessPayment) {
              notificationMessage = `Payment of $${transactionAmount} processed for invoice ${selectedInvoice.inv_number || selectedInvoice.id}. Invoice paid in full ($${paymentToInvoice}). Excess amount of $${excessAmount} recorded as separate transaction with EXCESS status.`;
            } else if (newInvoiceTotal <= 0) {
              notificationMessage = `Full payment of $${transactionAmount} applied to invoice ${selectedInvoice.inv_number || selectedInvoice.id}. Invoice marked as PAID.`;
            } else {
              notificationMessage = `Partial payment of $${transactionAmount} applied to invoice ${selectedInvoice.inv_number || selectedInvoice.id}. Status updated to PARTIAL_PAYMENT. Remaining balance: $${newInvoiceTotal}`;
            }

            await notificationService.createEntityNotification(
              authUser.accountId,
              isExcessPayment
                ? "Invoice Excess Payment"
                : newInvoiceTotal <= 0
                  ? "Invoice Full Payment"
                  : "Invoice Partial Payment",
              notificationMessage,
              "invoices",
              selectedInvoice.id,
              {
                transactionId: result.data.transaction.id,
                excessTransactionId: result.data.excessTransaction?.id,
                paymentAmount: transactionAmount,
                invoicePayment: paymentToInvoice,
                excessAmount: excessAmount,
                remainingBalance: newInvoiceTotal,
                invoiceNumber: selectedInvoice.inv_number,
                newStatus: newStatus,
                isExcessPayment,
              }
            );

            // Show appropriate success message
            if (isExcessPayment) {
              showToast(
                `Payment processed: $${paymentToInvoice} applied to invoice (now PAID), $${excessAmount} recorded as excess payment`,
                "success"
              );
            } else if (newInvoiceTotal <= 0) {
              showToast(
                `Invoice paid in full with $${transactionAmount}. Status: PAID`,
                "success"
              );
            } else {
              showToast(
                `Partial payment of $${transactionAmount} applied. Status: ${newStatus}. Remaining: $${newInvoiceTotal}`,
                "success"
              );
            }
          }

          onTransactionCreated?.();
          handleClose();
        } else {
          setErrors({
            general: result.error || "Failed to create transaction",
          });
        }
      } else {
        // Regular transaction creation
        const result = await transactionService.createTransactionWithDocuments(
          transactionData,
          attachedFiles
        );

        if (result.success && result.data) {
          onTransactionCreated?.();
          handleClose();
        } else {
          setErrors({
            general: result.error || "Failed to create transaction",
          });
        }
      }
    } catch (error) {
      console.error("Error creating transaction:", error);
      setErrors({ general: "An unexpected error occurred" });
    } finally {
      setIsCreating(false);
    }
  };

  const addTag = (tag: string) => {
    const trimmedTag = tag.trim().toLowerCase();
    if (trimmedTag && !formData.tags.includes(trimmedTag)) {
      setFormData((prev) => ({
        ...prev,
        tags: [...prev.tags, trimmedTag],
      }));
    }
    setTagInput("");
  };

  const removeTag = (tagToRemove: string) => {
    setFormData((prev) => ({
      ...prev,
      tags: prev.tags.filter((tag) => tag !== tagToRemove),
    }));
  };

  const handleTagInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && tagInput.trim()) {
      e.preventDefault();
      addTag(tagInput);
    }
  };

  // File upload handlers
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    addFiles(files);
    // Reset input
    event.target.value = "";
  };

  const addFiles = (files: File[]) => {
    // Filter out files that are too large (10MB limit)
    const validFiles = files.filter((file) => {
      if (file.size > 10 * 1024 * 1024) {
        setErrors((prev) => ({
          ...prev,
          documents: `File ${file.name} is too large. Maximum size is 10MB.`,
        }));
        return false;
      }
      return true;
    });

    setAttachedFiles((prev) => [...prev, ...validFiles]);
  };

  const removeAttachedFile = (index: number) => {
    setAttachedFiles((prev) => prev.filter((_, i) => i !== index));
  };

  // Drag and drop handlers
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const files = Array.from(e.dataTransfer.files);
    addFiles(files);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-3xl max-h-[85vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div className="p-2 rounded-lg bg-primary/10">
              <DollarSign className="h-5 w-5 text-primary" />
            </div>
            Create New Transaction
          </DialogTitle>
        </DialogHeader>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="space-y-6"
        >
          {/* Error Message */}
          {errors.general && (
            <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <span className="text-sm text-red-600">{errors.general}</span>
            </div>
          )}

          {/* Basic Information */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="name" className="text-sm font-medium">
                Transaction Name *
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, name: e.target.value }))
                }
                placeholder="Enter transaction name"
                className={errors.name ? "border-red-300" : ""}
              />
              {errors.name && (
                <p className="text-xs text-red-600 mt-1">{errors.name}</p>
              )}
            </div>

            <div className="w-full flex flex-row space-x-4">
              <div>
                <Label htmlFor="value" className="text-sm font-medium">
                  Type *
                </Label>
                <div className="w-max flex border border-gray-200 rounded-lg overflow-hidden">
                  <button
                    onClick={() =>
                      setFormData((prev) => ({ ...prev, type: "DEBIT" }))
                    }
                    className={`px-3 py-2 text-sm ${
                      formData.type === "DEBIT"
                        ? "bg-primary text-white"
                        : "bg-white text-gray-700 hover:bg-gray-50"
                    }`}
                  >
                    Debit
                  </button>
                  <button
                    onClick={() =>
                      setFormData((prev) => ({ ...prev, type: "CREDIT" }))
                    }
                    className={`px-3 py-2 text-sm ${
                      formData.type === "CREDIT"
                        ? "bg-primary text-white"
                        : "bg-white text-gray-700 hover:bg-gray-50"
                    }`}
                  >
                    Credit
                  </button>
                </div>
              </div>

              <div>
                <Label htmlFor="amount" className="text-sm font-medium">
                  Amount *
                </Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  value={formData.amount}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, amount: e.target.value }))
                  }
                  placeholder="0.00"
                  className={errors.amount ? "border-red-300" : ""}
                />
                {errors.amount && (
                  <p className="text-xs text-red-600 mt-1">{errors.amount}</p>
                )}
              </div>

              <div>
                <Label htmlFor="status" className="text-sm font-medium">
                  Status
                </Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) =>
                    setFormData((prev) => ({
                      ...prev,
                      status: value as StatusEnum,
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {STATUS_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center gap-2">
                          <span
                            className={`px-2 py-1 rounded-full text-xs ${option.color}`}
                          >
                            {option.label}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Book Selector */}
            <div>
              <Label
                htmlFor="book"
                className="text-sm font-medium mb-2 flex items-center gap-1"
              >
                <BookOpen className="h-4 w-4" />
                Ledger Book
              </Label>
              <Select
                value={formData.book}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, book: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a book category" />
                </SelectTrigger>
                <SelectContent className="max-h-60">
                  {availableBooks &&
                    availableBooks.map((book) => (
                      <SelectItem key={book} value={book}>
                        <div className="flex items-center gap-2">
                          <span className="text-sm">{book}</span>
                        </div>
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500 mt-1">
                Select the book category to organize this transaction within the
                ledger
              </p>
            </div>

            {/* Entity Association Section */}
            <EntityAssociation
              entityAssociation={entityAssociation}
              title="Entity Association (Optional)"
              layout="horizontal"
              entityTypeFilter={[
                "cargo",
                "shipments",
                "freights",
                "batches",
                "customers",
                "users",
                "invoices",
              ]}
              customEntityRenderer={(entity, entityType) => {
                if (entityType === "invoices") {
                  return (
                    <div className="flex flex-col">
                      <span className="font-medium">
                        {entity.inv_number ||
                          `Invoice ${entity.id.slice(0, 8)}`}
                      </span>
                      <span className="text-xs text-gray-500">
                        {entity.customer?.name} - ${entity.total || 0}
                      </span>
                    </div>
                  );
                }
                return entityAssociation.getEntityDisplayName(
                  entity,
                  entityType
                );
              }}
            />

            {/* Invoice Payment Details */}
            {selectedInvoice && formData.associated_table === "invoices" && (
              <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">
                  Invoice Payment Details
                </h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Invoice Number:</span>
                    <span className="ml-2 font-medium">
                      {selectedInvoice.inv_number ||
                        selectedInvoice.id.slice(0, 8)}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600">Customer:</span>
                    <span className="ml-2 font-medium">
                      {selectedInvoice.customer?.name || "N/A"}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600">Total Amount:</span>
                    <span className="ml-2 font-medium text-green-600">
                      ${selectedInvoice.total || 0}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600">Status:</span>
                    <span className="ml-2 font-medium">
                      {selectedInvoice.status || "PENDING"}
                    </span>
                  </div>
                </div>
                {formData.amount && Number(formData.amount) > 0 && (
                  <div className="mt-3 pt-3 border-t border-blue-200">
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-600">Payment Amount:</span>
                      <span className="font-medium text-blue-600">
                        ${Number(formData.amount)}
                      </span>
                    </div>

                    {Number(formData.amount) > (selectedInvoice.total || 0) ? (
                      // Excess payment scenario
                      <>
                        <div className="flex justify-between items-center text-sm mt-1">
                          <span className="text-gray-600">
                            Invoice Payment:
                          </span>
                          <span className="font-medium text-green-600">
                            ${selectedInvoice.total || 0}
                          </span>
                        </div>
                        <div className="flex justify-between items-center text-sm mt-1">
                          <span className="text-gray-600">Excess Amount:</span>
                          <span className="font-medium text-amber-600">
                            $
                            {Number(formData.amount) -
                              (selectedInvoice.total || 0)}
                          </span>
                        </div>
                        <div className="mt-2 p-2 bg-amber-50 border border-amber-200 rounded text-xs text-amber-800">
                          <strong>Excess Payment:</strong> The excess amount
                          will be recorded as a separate transaction with
                          "EXCESS" status.
                        </div>
                      </>
                    ) : (
                      // Regular payment scenario
                      <div className="flex justify-between items-center text-sm mt-1">
                        <span className="text-gray-600">
                          Remaining Balance:
                        </span>
                        <span className="font-medium text-orange-600">
                          $
                          {Math.max(
                            0,
                            (selectedInvoice.total || 0) -
                              Number(formData.amount)
                          )}
                        </span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>

          <div>
            <Label htmlFor="context" className="text-sm font-medium">
              Context
            </Label>
            <Textarea
              id="context"
              value={formData.context}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, context: e.target.value }))
              }
              placeholder="Additional context or description"
              rows={3}
            />
          </div>

          {/* Tags Section */}
          <div>
            <Label className="text-sm font-medium mb-2 flex items-center gap-1">
              <Tag className="h-4 w-4" />
              Tags
            </Label>

            {/* Current Tags */}
            {formData.tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-3">
                {formData.tags.map((tag, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => removeTag(tag)}
                      className="ml-1 hover:text-red-600"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            )}

            {/* Tag Input */}
            <div className="flex gap-2 mb-3">
              <Input
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyDown={handleTagInputKeyPress}
                placeholder="Add a tag"
                className="flex-1"
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => addTag(tagInput)}
                disabled={!tagInput.trim()}
              >
                Add
              </Button>
            </div>

            {/* Popular Tags */}
            <div>
              <p className="text-xs text-gray-500 mb-2">Popular tags:</p>
              <div className="flex flex-wrap gap-1">
                {POPULAR_TAGS.map((tag) => (
                  <button
                    key={tag}
                    type="button"
                    onClick={() => addTag(tag)}
                    className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                    disabled={formData.tags.includes(tag)}
                  >
                    {tag}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Document Attachments Section */}
          <div>
            <Label className="text-sm font-medium mb-2 flex items-center gap-1">
              <Paperclip className="h-4 w-4" />
              Document Attachments
            </Label>

            {/* File Upload Area */}
            <div
              className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors"
              onDragOver={handleDragOver}
              onDragEnter={handleDragEnter}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <input
                type="file"
                id="document-upload"
                className="hidden"
                multiple
                accept=".pdf,.doc,.docx,.xlsx,.xls,.png,.jpg,.jpeg"
                onChange={handleFileSelect}
                disabled={isCreating}
              />
              <div className="space-y-2">
                <Upload className="h-8 w-8 mx-auto text-gray-400" />
                <div>
                  <button
                    type="button"
                    onClick={() =>
                      document.getElementById("document-upload")?.click()
                    }
                    className="text-primary hover:text-primary/80 font-medium"
                    disabled={isCreating}
                  >
                    Click to upload files
                  </button>
                  <p className="text-sm text-gray-500">or drag and drop</p>
                </div>
                <p className="text-xs text-gray-400">
                  PDF, DOC, DOCX, XLS, XLSX, PNG, JPG up to 10MB each
                </p>
              </div>
            </div>

            {/* Attached Files List */}
            {attachedFiles.length > 0 && (
              <div className="mt-3 space-y-2">
                <p className="text-sm font-medium text-gray-700">
                  Attached Files ({attachedFiles.length})
                </p>
                <div className="space-y-2">
                  {attachedFiles.map((file, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                    >
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4 text-gray-500" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {file.name}
                          </p>
                          <p className="text-xs text-gray-500">
                            {(file.size / 1024 / 1024).toFixed(2)} MB
                          </p>
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => removeAttachedFile(index)}
                        className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                        disabled={isCreating}
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Document Upload Error */}
            {errors.documents && (
              <div className="mt-3 flex items-center gap-2 p-2 bg-red-50 border border-red-200 rounded-lg">
                <AlertCircle className="h-4 w-4 text-red-600" />
                <span className="text-sm text-red-600">{errors.documents}</span>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isCreating}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isCreating}
              className="gap-2"
            >
              {isCreating ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Plus className="h-4 w-4" />
              )}
              {isCreating
                ? "Creating Transaction..."
                : `Create Transaction${attachedFiles.length > 0 ? ` & Upload ${attachedFiles.length} File${attachedFiles.length > 1 ? "s" : ""}` : ""}`}
            </Button>
          </div>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
