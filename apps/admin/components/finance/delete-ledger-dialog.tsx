"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import {
  AlertTriangle,
  Trash2,
  X,
  Loader2,
  DollarSign,
  Calendar,
  Tag,
  AlertCircle,
  Hash,
  Activity,
} from "lucide-react";
import { ledgerService } from "@/lib/logistics";

interface DeleteLedgerDialogProps {
  isOpen: boolean;
  onClose: () => void;
  ledger: any;
  onLedgerDeleted?: () => void;
}

export function DeleteLedgerDialog({
  isOpen,
  onClose,
  ledger,
  onLedgerDeleted,
}: DeleteLedgerDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string>("");

  const handleClose = () => {
    setError("");
    onClose();
  };

  const handleDelete = async () => {
    if (!ledger?.id) return;

    try {
      setIsDeleting(true);
      setError("");

      const result = await ledgerService.deleteLedger(ledger.id);

      if (result.success) {
        onLedgerDeleted?.();
        handleClose();
      } else {
        setError(result.error || "Failed to delete ledger");
      }
    } catch (error) {
      console.error("Error deleting ledger:", error);
      setError("An unexpected error occurred");
    } finally {
      setIsDeleting(false);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  if (!ledger) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-red-100">
              <AlertTriangle className="h-5 w-5 text-red-600" />
            </div>
            <div>
              <DialogTitle className="text-xl font-semibold text-red-900">
                Delete Ledger
              </DialogTitle>
              <p className="text-sm text-red-600 mt-1">
                This action cannot be undone
              </p>
            </div>
          </div>
        </DialogHeader>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="space-y-4"
        >
          {/* Error Message */}
          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{error}</span>
            </div>
          )}

          {/* Warning Message */}
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-red-900 mb-1">
                  Are you sure you want to delete this ledger?
                </h4>
                <p className="text-sm text-red-700">
                  This will permanently delete the ledger and all associated data. 
                  Any transactions linked to this ledger may be affected.
                </p>
              </div>
            </div>
          </div>

          {/* Ledger Details */}
          <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Ledger Details
            </h4>
            
            <div className="space-y-3">
              {/* Name */}
              <div className="flex justify-between items-start">
                <span className="text-sm text-gray-600">Name:</span>
                <span className="text-sm font-medium text-gray-900 text-right">
                  {ledger.name}
                </span>
              </div>

              {/* Code */}
              {ledger.code && (
                <div className="flex justify-between items-start">
                  <span className="text-sm text-gray-600 flex items-center gap-1">
                    <Hash className="h-3 w-3" />
                    Code:
                  </span>
                  <span className="text-sm font-medium text-gray-900">
                    {ledger.code}
                  </span>
                </div>
              )}

              {/* Status */}
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Status:</span>
                <Badge 
                  variant={ledger.status === "ACTIVE" ? "default" : "secondary"}
                  className="text-xs"
                >
                  {ledger.status}
                </Badge>
              </div>

              {/* Books */}
              {ledger.books && ledger.books.length > 0 && (
                <div className="flex justify-between items-start">
                  <span className="text-sm text-gray-600">Books:</span>
                  <div className="flex flex-wrap gap-1 max-w-48">
                    {ledger.books.slice(0, 3).map((book: string, index: number) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {book}
                      </Badge>
                    ))}
                    {ledger.books.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{ledger.books.length - 3}
                      </Badge>
                    )}
                  </div>
                </div>
              )}

              {/* Tags */}
              {ledger.tags && ledger.tags.length > 0 && (
                <div className="flex justify-between items-start">
                  <span className="text-sm text-gray-600 flex items-center gap-1">
                    <Tag className="h-3 w-3" />
                    Tags:
                  </span>
                  <div className="flex flex-wrap gap-1 max-w-48">
                    {ledger.tags.slice(0, 3).map((tag: string, index: number) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {ledger.tags.length > 3 && (
                      <Badge variant="secondary" className="text-xs">
                        +{ledger.tags.length - 3}
                      </Badge>
                    )}
                  </div>
                </div>
              )}

              {/* Created Date */}
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600 flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  Created:
                </span>
                <span className="text-sm text-gray-900">
                  {formatDate(ledger.created_at)}
                </span>
              </div>
            </div>
          </div>

          {/* Confirmation Input */}
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-yellow-900 mb-1">
                  Important Notice
                </h4>
                <p className="text-sm text-yellow-700">
                  Deleting this ledger will mark it as inactive rather than permanently 
                  removing it from the system. This helps maintain data integrity and 
                  audit trails.
                </p>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button variant="outline" onClick={handleClose} disabled={isDeleting}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isDeleting}
              className="gap-2"
            >
              {isDeleting ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Trash2 className="h-4 w-4" />
              )}
              Delete Ledger
            </Button>
          </div>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
