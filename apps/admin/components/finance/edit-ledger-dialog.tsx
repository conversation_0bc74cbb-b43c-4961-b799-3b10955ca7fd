"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Plus,
  X,
  Loader2,
  Check,
  Hash,
  Tag,
  Save,
  AlertCircle,
} from "lucide-react";
import { AnimatedCard } from "@/components/animated-card";
import { ledgerService } from "@/lib/logistics";
import { useAppSelector } from "@/store/hooks";

// Import entity association components
import {
  EntityAssociation,
  useEntityAssociation,
} from "@/components/ui/entity-association";

interface EditLedgerDialogProps {
  isOpen: boolean;
  onClose: () => void;
  ledger: any;
  onLedgerUpdated?: () => void;
}

// Popular books for quick selection
const POPULAR_BOOKS = [
  "Sales Revenue",
  "Operating Expenses",
  "Freight Costs",
  "Customer Payments",
  "Shipping Fees",
  "Fuel Costs",
  "Equipment Maintenance",
  "Insurance",
  "Office Supplies",
  "Marketing",
  "Accounts Receivable",
  "Accounts Payable",
  "Cash Flow",
  "Petty Cash",
];

// Popular tags for quick selection
const POPULAR_TAGS = [
  "revenue",
  "expenses",
  "recurring",
  "monthly",
  "quarterly",
  "annual",
  "operational",
  "administrative",
  "freight",
  "customer",
];

// Status options
const STATUS_OPTIONS = [
  { value: "ACTIVE", label: "Active" },
  { value: "INACTIVE", label: "Inactive" },
  { value: "PENDING", label: "Pending" },
];

export function EditLedgerDialog({
  isOpen,
  onClose,
  ledger,
  onLedgerUpdated,
}: EditLedgerDialogProps) {
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string>("");

  // Form state
  const [name, setName] = useState("");
  const [code, setCode] = useState("");
  const [status, setStatus] = useState("ACTIVE");
  const [books, setBooks] = useState<string[]>([]);
  const [bookInput, setBookInput] = useState("");
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState("");

  // Entity association hook
  const entityAssociation = useEntityAssociation();

  const { user: authUser } = useAppSelector((state) => state.auth);

  // Initialize form with ledger data when dialog opens
  useEffect(() => {
    if (isOpen && ledger) {
      setName(ledger.name || "");
      setCode(ledger.code || "");
      setStatus(ledger.status || "ACTIVE");
      setBooks(ledger.books || []);
      setTags(ledger.tags || []);
      setError("");
    }
  }, [isOpen, ledger]);

  // Separate effect for entity association initialization
  useEffect(() => {
    if (isOpen && ledger) {
      // Initialize entity association with existing data
      if (ledger.associated_table && ledger.associated_id) {
        entityAssociation.handleTableChange(ledger.associated_table);
        entityAssociation.handleIdChange(ledger.associated_id);
      } else {
        entityAssociation.reset();
      }
    }
  }, [isOpen, ledger?.id]); // Only depend on ledger ID and isOpen

  const handleClose = () => {
    setError("");
    onClose();
  };

  const handleAddBook = (book: string) => {
    if (book && !books.includes(book)) {
      setBooks([...books, book]);
      setBookInput("");
    }
  };

  const handleRemoveBook = (bookToRemove: string) => {
    setBooks(books.filter((book) => book !== bookToRemove));
  };

  const handleAddTag = (tag: string) => {
    if (tag && !tags.includes(tag)) {
      setTags([...tags, tag]);
      setTagInput("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter((tag) => tag !== tagToRemove));
  };

  const handleSubmit = async () => {
    if (!name.trim()) {
      setError("Ledger name is required");
      return;
    }

    if (!ledger?.id) {
      setError("Ledger ID is missing");
      return;
    }

    try {
      setIsUpdating(true);
      setError("");

      const updateData = {
        name: name.trim(),
        code: code.trim() || undefined,
        status: status as any,
        books: books.length > 0 ? books : undefined,
        tags: tags.length > 0 ? tags : undefined,
        // Include entity association data
        associated_table: entityAssociation.result.association_table || null,
        associated_id: entityAssociation.result.association_id || null,
      };

      const result = await ledgerService.updateLedger(ledger.id, updateData);

      if (result.success) {
        onLedgerUpdated?.();
        handleClose();
      } else {
        setError(result.error || "Failed to update ledger");
      }
    } catch (error) {
      console.error("Error updating ledger:", error);
      setError("An unexpected error occurred");
    } finally {
      setIsUpdating(false);
    }
  };

  if (!ledger) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="text-xl font-semibold">
            Edit Ledger
          </DialogTitle>
          <p className="text-sm text-gray-500 mt-1">
            Update ledger details and configuration
          </p>
        </DialogHeader>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="flex-1 overflow-y-auto space-y-6 p-1"
        >
          {/* Error Message */}
          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{error}</span>
            </div>
          )}

          {/* Basic Information */}
          <AnimatedCard className="p-4">
            <h3 className="text-lg font-medium mb-4">Basic Information</h3>
            <div className="space-y-4">
              {/* Ledger Name */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2">
                  Ledger Name *
                </Label>
                <Input
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Enter ledger name"
                  className="w-full"
                />
              </div>

              {/* Status */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2">
                  Status
                </Label>
                <Select value={status} onValueChange={setStatus}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {STATUS_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </AnimatedCard>

          {/* Books Management */}
          <AnimatedCard className="p-4">
            <h3 className="text-lg font-medium mb-4">Books</h3>
            <div className="space-y-4">
              {/* Current Books */}
              {books.length > 0 && (
                <div>
                  <Label className="text-sm font-medium text-gray-700 mb-2">
                    Current Books
                  </Label>
                  <div className="flex flex-wrap gap-2">
                    {books.map((book, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="flex items-center gap-1"
                      >
                        {book}
                        <X
                          className="h-3 w-3 cursor-pointer hover:text-red-600"
                          onClick={() => handleRemoveBook(book)}
                        />
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Add Book */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2">
                  Add Book
                </Label>
                <div className="flex gap-2">
                  <Input
                    value={bookInput}
                    onChange={(e) => setBookInput(e.target.value)}
                    placeholder="Enter book name"
                    className="flex-1"
                    onKeyPress={(e) => {
                      if (e.key === "Enter") {
                        handleAddBook(bookInput);
                      }
                    }}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => handleAddBook(bookInput)}
                    disabled={!bookInput.trim()}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Popular Books */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2">
                  Popular Books
                </Label>
                <div className="flex flex-wrap gap-2">
                  {POPULAR_BOOKS.filter((book) => !books.includes(book))
                    .slice(0, 6)
                    .map((book) => (
                      <Button
                        key={book}
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => handleAddBook(book)}
                        className="text-xs"
                      >
                        <Plus className="h-3 w-3 mr-1" />
                        {book}
                      </Button>
                    ))}
                </div>
              </div>
            </div>
          </AnimatedCard>

          {/* Entity Association */}
          <EntityAssociation
            entityAssociation={entityAssociation}
            title="Entity Association (Optional)"
            layout="horizontal"
          />

          {/* Tags Management */}
          <AnimatedCard className="p-4">
            <h3 className="text-lg font-medium mb-4">
              <Tag className="h-5 w-5 inline mr-2" />
              Tags
            </h3>
            <div className="space-y-4">
              {/* Current Tags */}
              {tags.length > 0 && (
                <div>
                  <Label className="text-sm font-medium text-gray-700 mb-2">
                    Current Tags
                  </Label>
                  <div className="flex flex-wrap gap-2">
                    {tags.map((tag, index) => (
                      <Badge
                        key={index}
                        variant="outline"
                        className="flex items-center gap-1"
                      >
                        {tag}
                        <X
                          className="h-3 w-3 cursor-pointer hover:text-red-600"
                          onClick={() => handleRemoveTag(tag)}
                        />
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Add Tag */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2">
                  Add Tag
                </Label>
                <div className="flex gap-2">
                  <Input
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    placeholder="Enter tag"
                    className="flex-1"
                    onKeyPress={(e) => {
                      if (e.key === "Enter") {
                        handleAddTag(tagInput);
                      }
                    }}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => handleAddTag(tagInput)}
                    disabled={!tagInput.trim()}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Popular Tags */}
              <div>
                <Label className="text-sm font-medium text-gray-700 mb-2">
                  Popular Tags
                </Label>
                <div className="flex flex-wrap gap-2">
                  {POPULAR_TAGS.filter((tag) => !tags.includes(tag))
                    .slice(0, 8)
                    .map((tag) => (
                      <Button
                        key={tag}
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => handleAddTag(tag)}
                        className="text-xs"
                      >
                        <Plus className="h-3 w-3 mr-1" />
                        {tag}
                      </Button>
                    ))}
                </div>
              </div>
            </div>
          </AnimatedCard>
        </motion.div>

        {/* Actions */}
        <div className="flex justify-end gap-3 pt-4 border-t flex-shrink-0">
          <Button variant="outline" onClick={handleClose} disabled={isUpdating}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isUpdating || !name.trim()}
            className="gap-2"
          >
            {isUpdating ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Save className="h-4 w-4" />
            )}
            Update Ledger
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
