"use client";

import { getC<PERSON><PERSON>, deleteC<PERSON>ie } from "cookies-next/client";
import { redirect } from "next/navigation";
import React, { createContext, useContext, useEffect, ReactNode } from "react";

import { useAppDispatch, useAppSelector } from "@/store/hooks";

import { RootState } from "@/store";
import { getCurrentUser, setUser, signOut } from "@/store/slices/authSlice";

import { AuthUser } from "@/lib/auth";
import { AccessDeniedPlaceholder } from "@/components/ui/access-denied-placeholder";

interface AuthContextType {
  user: AuthUser | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isAuthorized: boolean;
  error: string | null;
  signOut: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const dispatch = useAppDispatch();
  const { user, isLoading, isAuthenticated, isAuthorized, error } =
    useAppSelector((state: RootState) => state.auth);

  // Initialize auth state on app load
  const initAuth = async () => {
    try {
      // Check if there's a session token in localStorage
      const sessionToken = getCookie("auth_session");
      if (sessionToken) {
        // @ts-ignore - dispatch returns a promise for async thunks
        await dispatch(getCurrentUser());
      }
    } catch (error) {
      console.error("Auth initialization error:", error);
    }
  };

  useEffect(() => {
    initAuth();
  }, [dispatch]);

  const handleSignOut = async () => {
    try {
      // Clear auth state immediately for better UX
      await dispatch(signOut());
      dispatch(setUser(null));
      // Remove session token
      deleteCookie("auth_session");
      // Redirect to login page
      redirect("/");
    } catch (error) {
      console.error("Sign out error:", error);
    }
  };

  const contextValue: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    isAuthorized,
    error,
    signOut: handleSignOut,
  };

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

// Role-based permission hooks
export function usePermissions() {
  const { user } = useAuth();

  const hasRole = (roleName: string) => {
    return user?.role?.name.toLowerCase() === roleName.toLowerCase();
  };

  const hasDepartment = (departmentName: string) => {
    return (
      user?.role?.department.name.toLowerCase() === departmentName.toLowerCase()
    );
  };

  const isAuthorized = () => {
    return (
      user?.role?.permissions && Object.keys(user.role.permissions).length > 0
    );
  };

  const isAdmin = () => {
    return hasRole("admin") || hasRole("administrator");
  };

  const isManager = () => {
    return hasRole("manager") || isAdmin();
  };

  const canAccess = (requiredRoles: string[]) => {
    if (!user?.role) return false;
    return requiredRoles.some((role) => hasRole(role)) || isAdmin();
  };

  return {
    hasRole,
    hasDepartment,
    isAuthorized,
    isAdmin,
    isManager,
    canAccess,
    userRole: user?.role?.name,
    userDepartment: user?.role?.department.name,
  };
}

export function VerifyAccess({ children }: { children: ReactNode }) {
  const { isAuthorized, isAuthenticated } = useAuth();
  //
  if (!isAuthorized && isAuthenticated) return <AccessDeniedPlaceholder />;
  if (!isAuthenticated) return <></>;
  return children;
}
