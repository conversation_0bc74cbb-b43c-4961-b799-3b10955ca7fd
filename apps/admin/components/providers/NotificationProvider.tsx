"use client";

import React, { createContext, useContext, ReactNode } from 'react';
import { useRealtimeNotifications } from '@/lib/hooks/useRealtimeNotifications';
import { Database } from '@/lib/types/supabase';

type NotificationRow = Database['public']['Tables']['notifications']['Row'];

interface NotificationContextType {
  notifications: NotificationRow[];
  unreadCount: number;
  isConnected: boolean;
  isLoading: boolean;
  error: string | null;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  deleteNotification: (notificationId: string) => void;
  refreshNotifications: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

interface NotificationProviderProps {
  children: ReactNode;
}

export function NotificationProvider({ children }: NotificationProviderProps) {
  const notificationState = useRealtimeNotifications();

  return (
    <NotificationContext.Provider value={notificationState}>
      {children}
    </NotificationContext.Provider>
  );
}

export function useNotifications() {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
}

// Hook for getting just the unread count (useful for badges)
export function useUnreadNotificationCount() {
  const { unreadCount, isLoading } = useNotifications();
  return { unreadCount, isLoading };
}

// Hook for getting connection status
export function useNotificationConnection() {
  const { isConnected, error } = useNotifications();
  return { isConnected, error };
}
