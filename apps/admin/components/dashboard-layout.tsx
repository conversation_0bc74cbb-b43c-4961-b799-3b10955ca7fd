"use client";

import ProtectedSidebar from "@/components/ProtectedSidebar";
import { motion } from "framer-motion";
import { VerifyAccess, useAuth } from "./providers/AuthProvider";

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  return (
    <VerifyAccess>
      <div className="flex h-screen bg-background-secondary">
        <ProtectedSidebar />
        <motion.main
          className="flex-1 overflow-auto bg-slate-50/10"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          transition={{
            type: "spring",
            stiffness: 300,
            damping: 30,
            duration: 0.4,
          }}
        >
          {children}
        </motion.main>
      </div>
    </VerifyAccess>
  );
}
