"use client";

import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@workspace/ui/components/dialog";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Switch } from "@workspace/ui/components/switch";
import { Badge } from "@workspace/ui/components/badge";
import {
  Tabs,
  Ta<PERSON>Content,
  TabsList,
  TabsTrigger,
} from "@workspace/ui/components/tabs";
import {
  Search,
  Shield,
  Eye,
  Plus,
  Edit,
  Trash2,
  Save,
  RotateCcw,
  AlertCircle,
  CheckCircle,
  Loader2,
  Users,
  Settings,
  DollarSign,
  Building,
  Package,
  Truck,
} from "lucide-react";
import { cn } from "@workspace/ui/lib/utils";
import { RoleWithDepartment } from "@/lib/logistics/types";
import { permissionService } from "@/lib/logistics/system/permissions";
import {
  RolePermissions,
  EntityType,
  PermissionAction,
} from "@/lib/logistics/types/permissions";

interface PermissionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  role?: RoleWithDepartment;
  onSave?: () => void;
}

// Mock permissions data structure
interface EntityPermission {
  entity: string;
  displayName: string;
  description: string;
  category: "system" | "operations" | "finances";
  permissions: {
    view: boolean;
    create: boolean;
    update: boolean;
    delete: boolean;
  };
}

// Mock entities configuration
const MOCK_ENTITIES: EntityPermission[] = [
  // System entities
  {
    entity: "users",
    displayName: "Users",
    description: "System user profiles and personal information",
    category: "system",
    permissions: { view: false, create: false, update: false, delete: false },
  },
  {
    entity: "accounts",
    displayName: "Accounts",
    description: "User authentication and login accounts",
    category: "system",
    permissions: { view: false, create: false, update: false, delete: false },
  },
  {
    entity: "roles",
    displayName: "Roles",
    description: "User roles and permission groups",
    category: "system",
    permissions: { view: false, create: false, update: false, delete: false },
  },
  {
    entity: "departments",
    displayName: "Departments",
    description: "Organizational departments and divisions",
    category: "system",
    permissions: { view: false, create: false, update: false, delete: false },
  },
  {
    entity: "documents",
    displayName: "Documents",
    description: "Document management and file storage",
    category: "system",
    permissions: { view: false, create: false, update: false, delete: false },
  },
  {
    entity: "notifications",
    displayName: "Notifications",
    description: "System notifications and alerts",
    category: "system",
    permissions: { view: false, create: false, update: false, delete: false },
  },
  {
    entity: "logs",
    displayName: "Logs",
    description: "System activity logs and audit trails",
    category: "system",
    permissions: { view: false, create: false, update: false, delete: false },
  },
  // Operations entities
  {
    entity: "customers",
    displayName: "Customers",
    description: "Customer profiles and contact information",
    category: "operations",
    permissions: { view: false, create: false, update: false, delete: false },
  },
  {
    entity: "suppliers",
    displayName: "Suppliers",
    description: "Supplier profiles and contact information",
    category: "operations",
    permissions: { view: false, create: false, update: false, delete: false },
  },
  {
    entity: "tasks",
    displayName: "Tasks",
    description: "Task and resource assignments",
    category: "operations",
    permissions: { view: false, create: false, update: false, delete: false },
  },
  {
    entity: "cargo",
    displayName: "Cargo",
    description: "Individual cargo items and shipments",
    category: "operations",
    permissions: { view: false, create: false, update: false, delete: false },
  },
  {
    entity: "batches",
    displayName: "Batches",
    description: "Cargo batch consolidation and management",
    category: "operations",
    permissions: { view: false, create: false, update: false, delete: false },
  },
  {
    entity: "freights",
    displayName: "Freights",
    description: "Freight booking and transportation methods",
    category: "operations",
    permissions: { view: false, create: false, update: false, delete: false },
  },
  {
    entity: "shipments",
    displayName: "Shipments",
    description: "Shipment tracking and logistics coordination",
    category: "operations",
    permissions: { view: false, create: false, update: false, delete: false },
  },
  {
    entity: "handovers",
    displayName: "Handovers",
    description: "Cargo delivery and handover verification",
    category: "operations",
    permissions: { view: false, create: false, update: false, delete: false },
  },
  {
    entity: "schedules",
    displayName: "Schedules",
    description: "Scheduling and calendar management",
    category: "operations",
    permissions: { view: false, create: false, update: false, delete: false },
  },
  // Financial entities
  {
    entity: "ledgers",
    displayName: "Ledgers",
    description: "Financial ledgers and accounting records",
    category: "finances",
    permissions: { view: false, create: false, update: false, delete: false },
  },
  {
    entity: "transactions",
    displayName: "Transactions",
    description: "Financial transactions and payments",
    category: "finances",
    permissions: { view: false, create: false, update: false, delete: false },
  },
];

const categoryIcons = {
  system: Settings,
  operations: Truck,
  finances: DollarSign,
};

const actionIcons = {
  view: Eye,
  create: Plus,
  update: Edit,
  delete: Trash2,
};

const actionColors = {
  view: "text-blue-600",
  create: "text-green-600",
  update: "text-yellow-600",
  delete: "text-red-600",
};

const StaffPermissions = ["view"];
const StaffPermissionsTwo = [...StaffPermissions, "update"];
const StaffPermissionsThree = [...StaffPermissionsTwo, "create"];
const ManagerPermissions = [...StaffPermissionsThree, "update"];
const AdminPermissions = [...ManagerPermissions, "delete"];

// Default permissions for common roles
const DEFAULT_PERMISSIONS: Record<string, Partial<Record<string, string[]>>> = {
  administrator: {
    // Full access to everything
    users: AdminPermissions,
    accounts: AdminPermissions,
    roles: AdminPermissions,
    departments: AdminPermissions,
    customers: AdminPermissions,
    cargo: AdminPermissions,
    batches: AdminPermissions,
    freights: AdminPermissions,
    shipments: AdminPermissions,
    handovers: AdminPermissions,
    documents: AdminPermissions,
    notifications: AdminPermissions,
    logs: StaffPermissions,
    schedules: AdminPermissions,
    ledgers: AdminPermissions,
    transactions: AdminPermissions,
  },
  manager: {
    // Management level access
    users: ManagerPermissions,
    accounts: StaffPermissionsTwo,
    roles: StaffPermissions,
    departments: StaffPermissions,
    customers: AdminPermissions,
    cargo: AdminPermissions,
    batches: AdminPermissions,
    freights: AdminPermissions,
    shipments: AdminPermissions,
    handovers: ManagerPermissions,
    documents: ManagerPermissions,
    notifications: StaffPermissionsThree,
    logs: StaffPermissions,
    schedules: AdminPermissions,
    ledgers: ManagerPermissions,
    transactions: ManagerPermissions,
  },
  operator: {
    // Operational level access
    customers: StaffPermissionsThree,
    cargo: StaffPermissionsThree,
    batches: StaffPermissionsThree,
    freights: StaffPermissionsTwo,
    shipments: StaffPermissionsTwo,
    handovers: StaffPermissionsThree,
    documents: StaffPermissionsThree,
    notifications: StaffPermissions,
    schedules: StaffPermissionsTwo,
    ledgers: StaffPermissions,
    transactions: StaffPermissions,
  },
  staff: {
    // Basic staff access
    customers: StaffPermissions,
    cargo: StaffPermissionsThree,
    batches: StaffPermissions,
    freights: StaffPermissions,
    shipments: StaffPermissions,
    handovers: StaffPermissions,
    documents: StaffPermissions,
    notifications: StaffPermissions,
    schedules: StaffPermissions,
    ledgers: StaffPermissions,
    transactions: StaffPermissions,
  },
};

export function PermissionsModal({
  isOpen,
  onClose,
  role,
  onSave,
}: PermissionsModalProps) {
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeCategory, setActiveCategory] = useState("system");
  const [permissions, setPermissions] = useState<EntityPermission[]>([]);
  const [hasChanges, setHasChanges] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize permissions when role changes
  useEffect(() => {
    if (role) {
      initializePermissions();
    }
  }, [role]);

  const initializePermissions = async () => {
    if (!role) return;

    try {
      setLoading(true);
      setError(null);

      // Fetch current permissions for the role
      const result = await permissionService.getPermissionsByRole(role.id);

      console.log("Loaded permissions from database:", result);

      if (result.success && result.data) {
        // Convert the permissions data from JSONB format to UI format
        // Database format: {"users": ["view", "create"], "cargo": ["view", "create", "update"]}
        // UI format: {entity: {view: true, create: true, update: false, delete: false}}
        const initialPermissions = MOCK_ENTITIES.map((entity) => {
          const rolePermissions =
            result.data?.permissions[entity.entity as EntityType] || {};

          return {
            ...entity,
            permissions: {
              view: rolePermissions.view || false,
              create: rolePermissions.create || false,
              update: rolePermissions.update || false,
              delete: rolePermissions.delete || false,
            },
          };
        });

        console.log("Converted permissions for UI:", initialPermissions);
        setPermissions(initialPermissions);
      } else {
        console.log("No permissions found, initializing empty permissions");
        // If no permissions found, start with empty permissions
        const initialPermissions = MOCK_ENTITIES.map((entity) => ({
          ...entity,
          permissions: {
            view: false,
            create: false,
            update: false,
            delete: false,
          },
        }));
        setPermissions(initialPermissions);
      }

      setHasChanges(false);
    } catch (error) {
      console.error("Error loading permissions:", error);
      setError("Failed to load permissions");
    } finally {
      setLoading(false);
    }
  };

  const handlePermissionChange = (
    entityIndex: number,
    action: keyof EntityPermission["permissions"],
    granted: boolean
  ) => {
    const updatedPermissions = [...permissions];
    updatedPermissions[entityIndex].permissions[action] = granted;
    setPermissions(updatedPermissions);
    setHasChanges(true);
  };

  const handleSavePermissions = async () => {
    if (!role) return;

    try {
      setSaving(true);
      setError(null);

      // Convert permissions to the format expected by the service
      const permissionsToUpdate: {
        entity: EntityType;
        action: PermissionAction;
        granted: boolean;
      }[] = [];

      permissions.forEach((entity) => {
        Object.entries(entity.permissions).forEach(([action, granted]) => {
          permissionsToUpdate.push({
            entity: entity.entity as EntityType,
            action: action as PermissionAction,
            granted,
          });
        });
      });

      console.log("Converting UI permissions to service format:", {
        roleId: role.id,
        roleName: role.name,
        uiPermissions: permissions,
        servicePermissions: permissionsToUpdate,
        grantedPermissions: permissionsToUpdate.filter((p) => p.granted).length,
        totalPermissions: permissionsToUpdate.length,
      });

      // Call the permissions service
      const result = await permissionService.updateRolePermissions(
        role.id,
        permissionsToUpdate
      );

      if (result.success) {
        console.log("Permissions saved successfully");
        setHasChanges(false);
        onSave?.();
        // Close modal after successful save
        setTimeout(() => {
          onClose();
        }, 500);
      } else {
        console.error("Failed to save permissions:", result.error);
        setError(result.error || "Failed to save permissions");
      }
    } catch (error) {
      console.error("Error saving permissions:", error);
      setError("An unexpected error occurred while saving permissions");
    } finally {
      setSaving(false);
    }
  };

  const handleApplyDefaults = async () => {
    if (!role) return;

    const confirmApply = window.confirm(
      `Apply default permissions for ${role.name} role? This will overwrite current permissions.`
    );
    if (!confirmApply) return;

    try {
      setSaving(true);
      setError(null);

      console.log("Applying default permissions for role:", role.name);

      // Use the permissions service to apply defaults
      const result = await permissionService.applyDefaultPermissions(
        role.id,
        role.name
      );

      if (result.success) {
        console.log("Default permissions applied successfully");
        // Reload permissions to reflect the changes
        await initializePermissions();
        setHasChanges(false);
      } else {
        console.warn(
          "Service failed, applying defaults locally:",
          result.error
        );
        // Fallback to local update if service fails
        const roleName = role.name.toLowerCase();
        const defaultPerms =
          DEFAULT_PERMISSIONS[roleName] || DEFAULT_PERMISSIONS["staff"];

        if (defaultPerms) {
          const updatedPermissions = permissions.map((entity) => {
            const entityDefaults = defaultPerms[entity.entity] || [];
            return {
              ...entity,
              permissions: {
                view: entityDefaults.includes("view"),
                create: entityDefaults.includes("create"),
                update: entityDefaults.includes("update"),
                delete: entityDefaults.includes("delete"),
              },
            };
          });

          setPermissions(updatedPermissions);
          setHasChanges(true);
          setError(
            `Applied defaults locally. Service error: ${result.error || "Unknown error"}`
          );
        } else {
          setError(`No default permissions found for role: ${role.name}`);
        }
      }
    } catch (error) {
      console.error("Error applying defaults:", error);
      setError(
        "An unexpected error occurred while applying default permissions"
      );
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    initializePermissions();
  };

  const handleTestConnection = async () => {
    try {
      setError(null);

      const result = await permissionService.testConnection();

      if (result.success && result.data) {
        setError(
          `✅ Connection successful! Found ${result.data.sampleRoleCount} roles with permissions column.`
        );
      } else {
        setError(`❌ Connection failed: ${result.error}`);
      }
    } catch (error) {
      setError(`❌ Test error: ${error}`);
    }
  };

  const filteredEntities = permissions.filter(
    (entity) =>
      entity.category === activeCategory &&
      entity.displayName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose} modal={true}>
      <DialogContent className="lg:w-[52rem] max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-primary" />
            Permissions Management
            {role && (
              <Badge variant="secondary" className="ml-2">
                {role.name} - {role.departments?.name || "No Department"}
              </Badge>
            )}
          </DialogTitle>
          <DialogDescription>
            Configure CRUD permissions for database entities
          </DialogDescription>
        </DialogHeader>

        {!role ? (
          <div className="flex items-center justify-center h-64 text-gray-500">
            <div className="text-center">
              <Shield className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No role selected for permissions management</p>
            </div>
          </div>
        ) : (
          <div className="h-[60vh] flex flex-col">
            {/* Error Display */}
            {error && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <div className="flex items-center">
                  <AlertCircle className="h-4 w-4 text-red-600 mr-2" />
                  <p className="text-sm text-red-800">{error}</p>
                </div>
              </div>
            )}

            {/* Search and Controls */}
            <div className="mb-4">
              <div className="flex items-center gap-4 mb-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search entities..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleApplyDefaults}
                  disabled={saving}
                >
                  Apply Defaults
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleReset}
                  disabled={!hasChanges || saving}
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Reset
                </Button>
                {hasChanges && (
                  <Badge
                    variant="secondary"
                    className="bg-yellow-100 text-yellow-800"
                  >
                    Unsaved Changes
                  </Badge>
                )}
              </div>

              <Tabs value={activeCategory} onValueChange={setActiveCategory}>
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger
                    value="system"
                    className="flex items-center gap-2"
                  >
                    <Settings className="h-4 w-4" />
                    System
                  </TabsTrigger>
                  <TabsTrigger
                    value="operations"
                    className="flex items-center gap-2"
                  >
                    <Truck className="h-4 w-4" />
                    Operations
                  </TabsTrigger>
                  <TabsTrigger
                    value="finances"
                    className="flex items-center gap-2"
                  >
                    <DollarSign className="h-4 w-4" />
                    Finances
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>

            {/* Permissions Grid */}
            <div className="flex-1 overflow-y-auto">
              {loading ? (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
                    <p className="text-gray-500">Loading permissions...</p>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredEntities.map((entity, index) => {
                    const originalIndex = permissions.findIndex(
                      (p) => p.entity === entity.entity
                    );
                    return (
                      <PermissionEntityRow
                        key={entity.entity}
                        entity={entity}
                        onPermissionChange={(action, granted) =>
                          handlePermissionChange(originalIndex, action, granted)
                        }
                      />
                    );
                  })}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-200">
          <div className="flex items-center gap-2">
            {error && (
              <div className="flex items-center gap-2 text-red-600">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm">{error}</span>
              </div>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={onClose} disabled={saving}>
              Cancel
            </Button>
            <Button
              onClick={handleSavePermissions}
              disabled={!hasChanges || saving || !role || loading}
              className={cn(
                hasChanges && !saving && "bg-green-600 hover:bg-green-700"
              )}
            >
              {saving ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              {saving
                ? "Saving Permissions..."
                : hasChanges
                  ? "Save Changes"
                  : "Save Permissions"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

interface PermissionEntityRowProps {
  entity: EntityPermission;
  onPermissionChange: (
    action: keyof EntityPermission["permissions"],
    granted: boolean
  ) => void;
}

function PermissionEntityRow({
  entity,
  onPermissionChange,
}: PermissionEntityRowProps) {
  return (
    <div className="border border-gray-200 rounded-lg p-4">
      <div className="flex items-center justify-between mb-3">
        <div>
          <h4 className="font-medium text-gray-900">{entity.displayName}</h4>
          <p className="text-sm text-gray-500">{entity.description}</p>
        </div>
      </div>

      <div className="grid grid-cols-4 gap-4">
        {(
          Object.keys(entity.permissions) as Array<
            keyof EntityPermission["permissions"]
          >
        ).map((action) => {
          const Icon = actionIcons[action];
          const isGranted = entity.permissions[action];

          return (
            <div
              key={action}
              className="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
            >
              <div className="flex items-center gap-2">
                <Icon className={cn("h-4 w-4", actionColors[action])} />
                <span className="text-sm font-medium capitalize">{action}</span>
              </div>
              <Switch
                checked={isGranted}
                onCheckedChange={(checked) =>
                  onPermissionChange(action, checked)
                }
              />
            </div>
          );
        })}
      </div>
    </div>
  );
}
