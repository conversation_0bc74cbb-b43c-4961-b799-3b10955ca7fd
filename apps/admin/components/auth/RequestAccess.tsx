'use client'

import { useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Button } from '@workspace/ui/components/button'
import { Input } from '@workspace/ui/components/input'
// Using standard HTML textarea instead of custom component
import { AuthCard } from './auth-card'
import { requestAccess } from '@/store/slices/authSlice'
import { RootState } from '@/store'
import { UserCheck, Mail, User, MessageSquare, ArrowLeft, CheckCircle } from 'lucide-react'

interface RequestAccessProps {
  onBackToLogin: () => void
}

export function RequestAccess({ onBackToLogin }: RequestAccessProps) {
  const dispatch = useDispatch()
  const { isLoading } = useSelector((state: RootState) => state.auth)
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  })
  const [error, setError] = useState('')
  const [submitted, setSubmitted] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    if (error) setError('')
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')

    // Validation
    if (!formData.name || !formData.email || !formData.message) {
      setError('Please fill in all fields')
      return
    }

    if (!formData.email.includes('@')) {
      setError('Please enter a valid email address')
      return
    }

    if (formData.message.length < 20) {
      setError('Please provide a more detailed message (minimum 20 characters)')
      return
    }

    try {
      // @ts-ignore - dispatch returns a promise for async thunks
      const result = await dispatch(requestAccess({
        name: formData.name,
        email: formData.email,
        message: formData.message
      }))

      if (requestAccess.fulfilled.match(result)) {
        setSubmitted(true)
      } else if (requestAccess.rejected.match(result)) {
        setError(result.payload as string || 'Failed to submit access request')
      }
    } catch (error: any) {
      setError(error.message || 'Failed to submit access request')
    }
  }

  if (submitted) {
    return (
      <AuthCard
        icon={<CheckCircle size={24} className="text-green-600" />}
        title="Request Submitted"
        description="Your access request has been sent to the administrators."
      >
        <div className="space-y-4 text-center">
          <div className="p-4 bg-green-50 border border-green-200 rounded-md">
            <p className="text-sm text-green-800">
              We've received your request for access to the Shamwaa Logistics admin panel.
              An administrator will review your request and contact you at <strong>{formData.email}</strong>.
            </p>
          </div>
          
          <Button
            onClick={onBackToLogin}
            variant="outline"
            className="w-full button-md flex items-center justify-center gap-2"
          >
            <ArrowLeft size={16} />
            Back to Login
          </Button>
        </div>
      </AuthCard>
    )
  }

  return (
    <AuthCard
      icon={<UserCheck size={24} className="text-brand" />}
      title="Request Access"
      description="Request access to the Shamwaa Logistics admin panel."
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        {error && (
          <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
            {error}
          </div>
        )}
        
        <div>
          <label
            htmlFor="name"
            className="block text-sm font-medium text-foreground-secondary mb-1.5 text-left"
          >
            Full Name
          </label>
          <div className="relative">
            <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <Input
              id="name"
              name="name"
              type="text"
              placeholder="Enter your full name"
              value={formData.name}
              onChange={handleChange}
              required
              className="input-base pl-10"
              disabled={isLoading}
            />
          </div>
        </div>

        <div>
          <label
            htmlFor="email"
            className="block text-sm font-medium text-foreground-secondary mb-1.5 text-left"
          >
            Email Address
          </label>
          <div className="relative">
            <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <Input
              id="email"
              name="email"
              type="email"
              placeholder="<EMAIL>"
              value={formData.email}
              onChange={handleChange}
              required
              className="input-base pl-10"
              disabled={isLoading}
            />
          </div>
        </div>

        <div>
          <label
            htmlFor="message"
            className="block text-sm font-medium text-foreground-secondary mb-1.5 text-left"
          >
            Why do you need access?
          </label>
          <div className="relative">
            <MessageSquare className="absolute left-3 top-3 text-gray-400" size={18} />
            <textarea
              id="message"
              name="message"
              placeholder="Please explain your role and why you need access to the admin panel..."
              value={formData.message}
              onChange={handleChange}
              required
              className="input-base pl-10 min-h-[100px] resize-none w-full rounded-md border border-gray-300 px-3 py-2 text-sm placeholder-gray-400 focus:border-brand focus:outline-none focus:ring-1 focus:ring-brand disabled:bg-gray-50 disabled:text-gray-500"
              disabled={isLoading}
              rows={4}
            />
          </div>
          <p className="text-xs text-gray-500 mt-1">
            Minimum 20 characters. Be specific about your role and responsibilities.
          </p>
        </div>

        <div className="space-y-3">
          <Button
            type="submit"
            className="w-full button-primary button-md"
            disabled={isLoading}
          >
            {isLoading ? "Submitting Request..." : "Submit Access Request"}
          </Button>

          <Button
            type="button"
            onClick={onBackToLogin}
            variant="outline"
            className="w-full button-md flex items-center justify-center gap-2"
            disabled={isLoading}
          >
            <ArrowLeft size={16} />
            Back to Login
          </Button>
        </div>

        <div className="text-xs text-gray-500 text-center pt-2">
          Your request will be reviewed by an administrator. You'll receive an email once your access is approved.
        </div>
      </form>
    </AuthCard>
  )
} 