import React, { ReactNode } from "react";
import { cn } from "@workspace/ui/lib/utils";

interface AuthCardProps {
  icon?: ReactNode;
  title: string;
  description?: string | ReactNode;
  children: ReactNode;
  className?: string;
}

export function AuthCard({
  icon,
  title,
  description,
  children,
  className,
}: AuthCardProps) {
  return (
    <div
      className={cn(
        "bg-background p-8 rounded-lg shadow-md max-w-sm w-full mx-auto",
        className
      )}
    >
      <div className="flex flex-col items-center text-center mb-6">
        {icon && (
          <div className="w-12 h-12 rounded-full bg-[#FFF0ED] flex items-center justify-center mb-4">
            {icon}
          </div>
        )}
        <h1 className="text-2xl font-heading font-medium text-foreground mb-1">
          {title}
        </h1>
        {description && (
          <p className="text-sm text-foreground-tertiary leading-relaxed">
            {description}
          </p>
        )}
      </div>
      {children}
    </div>
  );
}
