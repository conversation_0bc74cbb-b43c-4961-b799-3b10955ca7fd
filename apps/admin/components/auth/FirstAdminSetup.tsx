'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useDispatch, useSelector } from 'react-redux'
import { Button } from '@workspace/ui/components/button'
import { Input } from '@workspace/ui/components/input'
import { AuthCard } from './auth-card'
import { createFirstAdmin } from '@/store/slices/authSlice'
import { RootState } from '@/store'
import { UserPlus, Mail, Lock, User, Phone, MapPin } from 'lucide-react'

export function FirstAdminSetup() {
  const router = useRouter()
  const dispatch = useDispatch()
  const { isLoading } = useSelector((state: RootState) => state.auth)
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    location: ''
  })
  const [error, setError] = useState('')

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    if (error) setError('')
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')

    // Validation
    if (!formData.name || !formData.email || !formData.password) {
      setError('Please fill in all required fields')
      return
    }

    if (!formData.email.includes('@')) {
      setError('Please enter a valid email address')
      return
    }

    if (formData.password.length < 8) {
      setError('Password must be at least 8 characters long')
      return
    }

    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match')
      return
    }

    try {
      // @ts-ignore - dispatch returns a promise for async thunks
      const result = await dispatch(createFirstAdmin({
        name: formData.name,
        email: formData.email,
        password: formData.password,
        phone: formData.phone || undefined,
        location: formData.location || undefined
      }))

      if (createFirstAdmin.fulfilled.match(result)) {
        router.push('/dashboard')
      } else if (createFirstAdmin.rejected.match(result)) {
        setError(result.payload as string || 'Failed to create admin account')
      }
    } catch (error: any) {
      setError(error.message || 'Failed to create admin account')
    }
  }

  return (
    <AuthCard
      icon={<UserPlus size={24} className="text-brand" />}
      title="Setup Admin Account"
      description="Welcome! Create the first administrator account to get started."
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        {error && (
          <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
            {error}
          </div>
        )}
        
        <div>
          <label
            htmlFor="name"
            className="block text-sm font-medium text-foreground-secondary mb-1.5 text-left"
          >
            Full Name *
          </label>
          <div className="relative">
            <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <Input
              id="name"
              name="name"
              type="text"
              placeholder="Enter your full name"
              value={formData.name}
              onChange={handleChange}
              required
              className="input-base pl-10"
              disabled={isLoading}
            />
          </div>
        </div>

        <div>
          <label
            htmlFor="email"
            className="block text-sm font-medium text-foreground-secondary mb-1.5 text-left"
          >
            Email Address *
          </label>
          <div className="relative">
            <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <Input
              id="email"
              name="email"
              type="email"
              placeholder="<EMAIL>"
              value={formData.email}
              onChange={handleChange}
              required
              className="input-base pl-10"
              disabled={isLoading}
            />
          </div>
        </div>

        <div>
          <label
            htmlFor="password"
            className="block text-sm font-medium text-foreground-secondary mb-1.5 text-left"
          >
            Password *
          </label>
          <div className="relative">
            <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <Input
              id="password"
              name="password"
              type="password"
              placeholder="••••••••"
              value={formData.password}
              onChange={handleChange}
              required
              className="input-base pl-10"
              disabled={isLoading}
            />
          </div>
        </div>

        <div>
          <label
            htmlFor="confirmPassword"
            className="block text-sm font-medium text-foreground-secondary mb-1.5 text-left"
          >
            Confirm Password *
          </label>
          <div className="relative">
            <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <Input
              id="confirmPassword"
              name="confirmPassword"
              type="password"
              placeholder="••••••••"
              value={formData.confirmPassword}
              onChange={handleChange}
              required
              className="input-base pl-10"
              disabled={isLoading}
            />
          </div>
        </div>

        <div>
          <label
            htmlFor="phone"
            className="block text-sm font-medium text-foreground-secondary mb-1.5 text-left"
          >
            Phone Number
          </label>
          <div className="relative">
            <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <Input
              id="phone"
              name="phone"
              type="tel"
              placeholder="+****************"
              value={formData.phone}
              onChange={handleChange}
              className="input-base pl-10"
              disabled={isLoading}
            />
          </div>
        </div>

        <div>
          <label
            htmlFor="location"
            className="block text-sm font-medium text-foreground-secondary mb-1.5 text-left"
          >
            Location
          </label>
          <div className="relative">
            <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <Input
              id="location"
              name="location"
              type="text"
              placeholder="City, Country"
              value={formData.location}
              onChange={handleChange}
              className="input-base pl-10"
              disabled={isLoading}
            />
          </div>
        </div>

        <Button
          type="submit"
          className="w-full button-primary button-md mt-2"
          disabled={isLoading}
        >
          {isLoading ? "Creating Admin Account..." : "Create Admin Account"}
        </Button>

        <div className="text-xs text-gray-500 text-center pt-2">
          This will create the first administrator account with full system access.
        </div>
      </form>
    </AuthCard>
  )
} 