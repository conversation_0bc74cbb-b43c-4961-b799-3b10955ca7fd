"use client";

import { useState } from "react";
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  <PERSON>alogTitle,
  Di<PERSON>Trigger,
  DialogFooter
} from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Map, MapPin, LocateFixed, Route, Clock, Truck, Fuel, RotateCw, Plus } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@workspace/ui/components/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@workspace/ui/components/tabs";

export function RoutesModal() {
  const [open, setOpen] = useState(false);
  const [routeType, setRouteType] = useState("fastest");
  const [optimizing, setOptimizing] = useState(false);

  const mockRoutes = [
    { 
      id: "RT-001", 
      name: "East Africa Corridor", 
      stops: ["Nairobi", "Kampala", "Kigali"],
      distance: "1,250 km",
      time: "25h 40m",
      vehicles: 4
    },
    { 
      id: "RT-002", 
      name: "West Africa Express", 
      stops: ["Lagos", "Accra", "Abidjan"],
      distance: "980 km",
      time: "18h 20m", 
      vehicles: 3
    },
    { 
      id: "RT-003", 
      name: "North Africa Network", 
      stops: ["Cairo", "Tripoli", "Tunis"],
      distance: "2,340 km",
      time: "38h 15m",
      vehicles: 5
    }
  ];

  const handleOptimize = () => {
    setOptimizing(true);
    // Simulate API call
    setTimeout(() => {
      setOptimizing(false);
    }, 2000);
  };
  
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button 
          variant="outline" 
          className="flex items-center gap-3 h-auto py-4 px-4 border border-gray-200 justify-start text-left hover:bg-gray-50 hover:text-foreground"
        >
          <div className="w-9 h-9 rounded-full bg-red-500/10 flex items-center justify-center flex-shrink-0">
            <Map size={16} className="text-red-500" />
          </div>
          <div>
            <span className="block font-medium">Routes</span>
            <span className="text-xs text-muted-foreground">Optimize routes</span>
          </div>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[640px] p-0 overflow-hidden">
        <div className="flex flex-col h-full">
          <DialogHeader className="p-6 pb-3 bg-primary/5 border-b">
            <DialogTitle className="text-xl text-primary font-semibold">Route Management</DialogTitle>
            <DialogDescription className="text-sm pt-1.5">
              View and optimize logistics routes.
            </DialogDescription>
          </DialogHeader>
          <div className="p-6 max-h-[70vh] overflow-y-auto">
            <Tabs defaultValue="existing" className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-6 bg-gray-100/80">
                <TabsTrigger value="existing" className="data-[state=active]:bg-primary data-[state=active]:text-white">
                  Existing Routes
                </TabsTrigger>
                <TabsTrigger value="create" className="data-[state=active]:bg-primary data-[state=active]:text-white">
                  Create New Route
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="existing">
                <div className="space-y-5">
                  {mockRoutes.map((route) => (
                    <div key={route.id} className="border border-gray-200 rounded-lg p-5 hover:shadow-sm transition-shadow">
                      <div className="flex justify-between items-start mb-3">
                        <div>
                          <p className="font-medium text-base">{route.name}</p>
                          <p className="text-xs text-muted-foreground">{route.id}</p>
                        </div>
                        <div className="flex items-center gap-1 bg-blue-50 text-blue-700 px-2 py-1 rounded-full">
                          <Truck size={14} />
                          <span className="text-xs font-medium">{route.vehicles} vehicles</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2 mb-4 text-sm">
                        <div className="flex items-center gap-1 text-primary">
                          <Route size={14} />
                          <span>{route.distance}</span>
                        </div>
                        <div className="w-1 h-1 bg-gray-300 rounded-full"></div>
                        <div className="flex items-center gap-1">
                          <Clock size={14} className="text-muted-foreground" />
                          <span>{route.time}</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2 relative pl-5 pb-3">
                        <div className="absolute left-0 top-2 bottom-2 w-0.5 bg-gray-200"></div>
                        <div className="flex flex-col text-sm space-y-2">
                          {route.stops.map((stop, index) => (
                            <div key={index} className="flex items-center">
                              <div className={`
                                absolute left-0 w-2 h-2 rounded-full 
                                ${index === 0 ? 'bg-green-500' : index === route.stops.length - 1 ? 'bg-primary' : 'bg-blue-500'}
                              `}></div>
                              <p className={`
                                ${index === 0 ? 'font-medium' : index === route.stops.length - 1 ? 'font-medium' : 'text-muted-foreground'}
                              `}>{stop}</p>
                            </div>
                          ))}
                        </div>
                      </div>
                      
                      <div className="flex gap-2 mt-3">
                        <Button variant="outline" size="sm" className="flex-1 text-xs gap-1">
                          <MapPin size={12} />
                          <span>View Map</span>
                        </Button>
                        <Button size="sm" className="flex-1 text-xs gap-1 bg-primary hover:bg-primary/90 text-white">
                          <RotateCw size={12} />
                          <span>Optimize</span>
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </TabsContent>
              
              <TabsContent value="create">
                <div className="space-y-5">
                  <div className="grid gap-5">
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="route-name" className="text-right font-medium">
                        Route Name
                      </Label>
                      <Input
                        id="route-name"
                        placeholder="Enter route name"
                        className="col-span-3"
                      />
                    </div>
                    
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="origin" className="text-right font-medium">
                        Origin
                      </Label>
                      <div className="col-span-3 flex gap-2">
                        <Input
                          id="origin"
                          placeholder="Enter starting point"
                          className="flex-1"
                        />
                        <Button variant="outline" size="icon" className="h-9 w-9 border-gray-200">
                          <LocateFixed size={14} />
                        </Button>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="destination" className="text-right font-medium">
                        Destination
                      </Label>
                      <Input
                        id="destination"
                        placeholder="Enter final destination"
                        className="col-span-3"
                      />
                    </div>
                    
                    {/* Add stops section */}
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label className="text-right font-medium">
                        Via
                      </Label>
                      <div className="col-span-3">
                        <Button variant="outline" className="w-full justify-start text-muted-foreground text-sm hover:text-primary hover:border-primary">
                          <Plus size={14} className="mr-2" />
                          Add stop
                        </Button>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="optimization" className="text-right font-medium">
                        Optimize For
                      </Label>
                      <Select value={routeType} onValueChange={setRouteType}>
                        <SelectTrigger className="col-span-3">
                          <SelectValue placeholder="Select optimization" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="fastest">Fastest Route</SelectItem>
                          <SelectItem value="fuel">Fuel Efficiency</SelectItem>
                          <SelectItem value="cost">Lowest Cost</SelectItem>
                          <SelectItem value="distance">Shortest Distance</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <Button 
                    className="w-full mt-6 bg-primary hover:bg-primary/90 text-white" 
                    onClick={handleOptimize}
                    disabled={optimizing}
                  >
                    {optimizing ? (
                      <>
                        <RotateCw className="mr-2 h-4 w-4 animate-spin" />
                        Optimizing...
                      </>
                    ) : (
                      <>Calculate Optimal Route</>
                    )}
                  </Button>
                </div>
              </TabsContent>
            </Tabs>
          </div>
          <DialogFooter className="px-6 py-4 border-t flex flex-row justify-end gap-2">
            <Button type="button" variant="outline" className="border-gray-200" onClick={() => setOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
} 