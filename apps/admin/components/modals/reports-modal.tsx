"use client";

import { useRouter } from "next/navigation";
import { But<PERSON> } from "@workspace/ui/components/button";
import { BarChart2 } from "lucide-react";

export function ReportsModal() {
  const router = useRouter();

  const handleReports = () => {
    router.push("/finance");
  };

  return (
    <Button
      variant="outline"
      className="flex items-center gap-3 h-auto py-4 px-4 border border-gray-200 justify-start text-left hover:bg-gray-50 hover:text-foreground"
      onClick={handleReports}
    >
      <div className="w-9 h-9 rounded-full bg-amber-500/10 flex items-center justify-center flex-shrink-0">
        <BarChart2 size={16} className="text-amber-500" />
      </div>
      <div>
        <span className="block font-medium">Reports</span>
        <span className="text-xs text-muted-foreground">View analytics</span>
      </div>
    </Button>
  );
}
