"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  DialogDescription,
  <PERSON>alog<PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
  <PERSON><PERSON>Footer,
} from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { UserPlus, Loader2 } from "lucide-react";

import { showToast } from "@/lib/utils";
import { useAppSelector } from "@/store/hooks";
import { customerService } from "@/lib/logistics/operations/customers";

export function CustomerModal() {
  const [open, setOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: "", // character varying(255) not null
    email: "", // character varying(255) null
    phone: "", // character varying(50) null
    location: "", // text null
    status: "ACTIVE", // status_enum default 'ACTIVE'
  });

  let { user: authUser } = useAppSelector((state) => state.auth);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate required fields
      if (!formData.name.trim()) {
        showToast("Customer name is required", "error");
        return;
      }

      // Prepare customer data matching the database schema
      const customerData = {
        name: formData.name.trim(), // character varying(255) not null
        email: formData.email.trim() || null, // character varying(255) null
        phone: formData.phone.trim() || null, // character varying(50) null
        location: formData.location.trim() || null, // text null
        status: formData.status as any, // status_enum default 'ACTIVE'
        account_id: authUser?.accountId, // uuid null
      };

      // Create customer using service
      const result = await customerService.createCustomer(customerData);

      if (result.success) {
        showToast("Customer created successfully", "success");
        setOpen(false);
        // Reset form
        setFormData({
          name: "",
          email: "",
          phone: "",
          location: "",
          status: "ACTIVE",
        });
      } else {
        showToast(result.error || "Failed to create customer", "error");
      }
    } catch (error: any) {
      showToast(error.message || "Failed to create customer", "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className="flex items-center gap-3 h-auto py-4 px-4 border border-gray-200 justify-start text-left hover:bg-gray-50 hover:text-foreground"
        >
          <div className="w-9 h-9 rounded-full bg-green-500/10 flex items-center justify-center flex-shrink-0">
            <UserPlus size={16} className="text-green-500" />
          </div>
          <div>
            <span className="block font-medium">New Customer</span>
            <span className="text-xs text-muted-foreground">Add customer</span>
          </div>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[540px] p-0 overflow-hidden">
        <div className="flex flex-col h-full">
          <DialogHeader className="p-6 pb-3 bg-primary/5 border-b">
            <DialogTitle className="text-xl text-primary font-semibold">
              Add New Customer
            </DialogTitle>
            <DialogDescription className="text-sm pt-1.5">
              Enter the customer details to create a new account.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-5 p-6 max-h-[70vh] overflow-y-auto">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label
                  htmlFor="contact-name"
                  className="text-right font-medium"
                >
                  Contact Name *
                </Label>
                <Input
                  id="contact-name"
                  placeholder="Enter primary contact name"
                  className="col-span-3"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  required
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="email" className="text-right font-medium">
                  Email
                </Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter email address"
                  className="col-span-3"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="phone" className="text-right font-medium">
                  Phone
                </Label>
                <Input
                  id="phone"
                  placeholder="Enter phone number"
                  className="col-span-3"
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="location" className="text-right font-medium">
                  Location
                </Label>
                <Input
                  id="location"
                  placeholder="Enter location"
                  className="col-span-3"
                  value={formData.location}
                  onChange={(e) =>
                    handleInputChange("location", e.target.value)
                  }
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="status" className="text-right font-medium">
                  Status
                </Label>
                <div className="col-span-3">
                  <span className="text-sm text-gray-600 bg-green-50 px-2 py-1 rounded">
                    {formData.status}
                  </span>
                  <p className="text-xs text-gray-500 mt-1">
                    New customers are automatically set to ACTIVE status
                  </p>
                </div>
              </div>
            </div>
            <DialogFooter className="px-6 py-4 border-t flex flex-row justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                className="border-gray-200"
                onClick={() => setOpen(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-primary hover:bg-primary/90 text-white"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Add Customer"
                )}
              </Button>
            </DialogFooter>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
