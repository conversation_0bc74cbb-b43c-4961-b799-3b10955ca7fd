"use client";

import { useRouter } from "next/navigation";
import { But<PERSON> } from "@workspace/ui/components/button";
import { FileText } from "lucide-react";

export function InvoiceModal() {
  const router = useRouter();

  const handleInvoices = () => {
    router.push("/invoice-workflow");
  };

  return (
    <Button
      variant="outline"
      className="flex items-center gap-3 h-auto py-4 px-4 border border-gray-200 justify-start text-left hover:bg-gray-50 hover:text-foreground"
      onClick={handleInvoices}
    >
      <div className="w-9 h-9 rounded-full bg-green-500/10 flex items-center justify-center flex-shrink-0">
        <FileText size={16} className="text-green-500" />
      </div>
      <div>
        <span className="block font-medium">Invoices</span>
        <span className="text-xs text-muted-foreground">Manage billing</span>
      </div>
    </Button>
  );
}
