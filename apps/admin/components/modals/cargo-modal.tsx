"use client";

import { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
  Di<PERSON>Footer,
} from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Package, Loader2 } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { useAppSelector } from "@/store/hooks";
import { cargoService } from "@/lib/logistics/operations/cargos";
import { customerService } from "@/lib/logistics/operations/customers";
import { supplierService } from "@/lib/logistics/operations/suppliers";
import { codeGeneratorService } from "@/lib/logistics/operations/code-generator";
import { batchService } from "@/lib/logistics/operations/batches";
import { showToast } from "@/lib/utils";

import type { Customer } from "@/lib/logistics/types";
import { trimming } from "@/lib/utils";

const SelectField = ({
  id,
  label,
  placeholder,
  defaultValue,
  value,
  options,
  onChange,
  ...props
}: {
  id: string;
  label: string;
  placeholder?: string;
  defaultValue?: string;
  value?: string;
  options: (string | { value: string; label: string })[];
  onChange?: (value: string) => void;
  [key: string]: any;
}) => (
  <div>
    <label
      htmlFor={id}
      className="block text-sm font-medium text-gray-700 mb-1.5"
    >
      {label}
    </label>
    <Select
      defaultValue={defaultValue}
      value={value}
      onValueChange={onChange}
      {...props}
    >
      <SelectTrigger
        id={id}
        className="w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white text-gray-900 shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary data-[placeholder]:text-gray-500 h-10"
      >
        <SelectValue placeholder={placeholder ?? "Select..."} />
      </SelectTrigger>
      <SelectContent>
        {options.map((option) => {
          const value =
            typeof option === "string"
              ? option === "All Types" || option === "All Statuses"
                ? "all"
                : option.toLowerCase().replace(/\s+/g, "-")
              : option.value;
          const displayLabel =
            typeof option === "string" ? option : option.label;
          return (
            <SelectItem key={value} value={value} className="text-sm text-wrap">
              {trimming(displayLabel, 20)}
            </SelectItem>
          );
        })}
      </SelectContent>
    </Select>
  </div>
);

export function CargoModal() {
  const [open, setOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [suppliers, setSuppliers] = useState<any[]>([]);
  const [loadingCustomers, setLoadingCustomers] = useState(true);
  const [loadingSuppliers, setLoadingSuppliers] = useState(true);
  const [formData, setFormData] = useState({
    type: "SAFE", // safety_type enum
    particular: "",
    customer_id: "",
    supplier_id: "",
    quantity: 1,
    dimension_length: 0.0,
    dimension_width: 0.0,
    dimension_height: 0.0,
    dimension_unit: "METERS",
    weight_value: 0.0,
    weight_unit: "KILOGRAMS",
    unit_price: 0.0,
    total_price: 0.0,
    factor_unit: "Weight", // Weight or CBM
    factor_value: 1,
  });

  let { user: authUser } = useAppSelector((state) => state.auth);

  const [entityType, setEntityType] = useState<"customer" | "supplier">(
    "customer"
  );

  // Load customers and suppliers on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        // Load customers
        const customersResult = await customerService.getAll();
        if (customersResult.success) {
          setCustomers(customersResult.data);
        }
        setLoadingCustomers(false);

        // Load suppliers
        const suppliersResult = await supplierService.getAll();
        if (suppliersResult.success) {
          setSuppliers(suppliersResult.data);
        }
        setLoadingSuppliers(false);
      } catch (error) {
        console.error("Failed to load data:", error);
        setLoadingCustomers(false);
        setLoadingSuppliers(false);
      }
    };

    if (open) {
      loadData();
    }
  }, [open]);

  const handleInputChange = (field: string, value: string | number) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate required fields
      if (!formData.particular.trim()) {
        showToast("Cargo description is required", "error");
        return;
      }

      // Validate that either customer or supplier is selected
      if (entityType === "customer" && !formData.customer_id) {
        showToast("Customer selection is required", "error");
        return;
      }
      if (entityType === "supplier" && !formData.supplier_id) {
        showToast("Supplier selection is required", "error");
        return;
      }

      // Generate cargo tracking number using centralized service
      // First, get the batch code if batch is assigned
      let batchCode = "";
      if (formData.batch_id) {
        try {
          const batchResult = await batchService.getBatchWithRelations(
            formData.batch_id
          );
          if (batchResult.success && batchResult.data) {
            batchCode = batchResult.data.code || "";
          }
        } catch (error) {
          console.warn("Failed to fetch batch code:", error);
        }
      }

      // Fetch existing cargo tracking numbers for sequential generation
      const existingCargosResult = await cargoService.getAllCargosWithRelations(
        {
          filters: {},
          page: 1,
          limit: 1000, // Get all to check tracking numbers
        }
      );

      const existingTrackingNumbers =
        existingCargosResult.success && existingCargosResult.data
          ? existingCargosResult.data
              .map((cargo) => cargo.tracking_number)
              .filter(
                (trackingNumber): trackingNumber is string => !!trackingNumber
              )
              .filter((trackingNumber) =>
                trackingNumber.startsWith(`CG/${batchCode || "GENERAL"}/`)
              )
          : [];

      const trackingNumber =
        codeGeneratorService.generateSequentialCargoTrackingNumber(
          {
            batchCode: batchCode || "GENERAL", // Use batch code or fallback
          },
          existingTrackingNumbers
        );

      // Calculate CBM value
      const cbmValue =
        formData.dimension_length &&
        formData.dimension_width &&
        formData.dimension_height
          ? (formData.dimension_length *
              formData.dimension_width *
              formData.dimension_height) /
            1000000
          : null;

      // Calculate factor value based on factor unit
      const factorValue =
        formData.factor_unit === "Weight"
          ? formData.weight_value || 1
          : cbmValue || 1;

      // Calculate total price
      const totalPrice = formData.quantity * formData.unit_price;

      // Prepare cargo data matching the database schema
      const cargoData = {
        tracking_number: trackingNumber,
        type: formData.type as "SAFE" | "DANGEROUS",
        particular: formData.particular.trim() || null,
        customer_id: entityType === "customer" ? formData.customer_id : null,
        supplier_id: entityType === "supplier" ? formData.supplier_id : null,
        quantity: formData.quantity,
        dimension_length: formData.dimension_length || null,
        dimension_width: formData.dimension_width || null,
        dimension_height: formData.dimension_height || null,
        dimension_unit: formData.dimension_unit as any,
        weight_value: formData.weight_value || null,
        weight_unit: formData.weight_unit as any,
        unit_price: formData.unit_price || null,
        total_price: totalPrice || null,
        cbm_value: cbmValue,
        cbm_unit: "METER_CUBIC" as any,
        factor_unit: formData.factor_unit,
        factor_value: factorValue,
        status: "CREATED" as any,
        account_id: authUser?.accountId,
      };

      // Create cargo using service
      const result = await cargoService.createCargo(cargoData);

      if (result.success) {
        showToast("Cargo created successfully", "success");
        setOpen(false);
        // Reset form
        setFormData({
          type: "SAFE",
          particular: "",
          customer_id: "",
          supplier_id: "",
          quantity: 1,
          dimension_length: 0.0,
          dimension_width: 0.0,
          dimension_height: 0.0,
          dimension_unit: "METERS",
          weight_value: 0.0,
          weight_unit: "KILOGRAMS",
          unit_price: 0.0,
          total_price: 0.0,
          factor_unit: "Weight",
          factor_value: 1,
        });
      } else {
        showToast(result.error || "Failed to create cargo", "error");
      }
    } catch (error: any) {
      showToast(error.message || "Failed to create cargo", "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className="flex items-center gap-3 h-auto py-4 px-4 border border-gray-200 justify-start text-left hover:bg-gray-50 hover:text-foreground"
        >
          <div className="w-9 h-9 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
            <Package size={16} className="text-primary" />
          </div>
          <div>
            <span className="block font-medium">New Cargo</span>
            <span className="text-xs text-muted-foreground">
              Create shipment
            </span>
          </div>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[540px] p-0 overflow-hidden">
        <div className="flex flex-col h-full">
          <DialogHeader className="p-6 pb-3 bg-primary/5 border-b">
            <DialogTitle className="text-xl text-primary font-semibold">
              Create New Shipment
            </DialogTitle>
            <DialogDescription className="text-sm pt-1.5">
              Enter the shipment details to create a new cargo entry.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-5 p-6 max-h-[70vh] overflow-y-auto">
              {/* Entity Type Selector */}
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right font-medium">Entity Type</Label>
                <div className="col-span-3 flex gap-2">
                  <Button
                    type="button"
                    variant={entityType === "customer" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setEntityType("customer")}
                    className="flex-1"
                  >
                    Customer
                  </Button>
                  <Button
                    type="button"
                    variant={entityType === "supplier" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setEntityType("supplier")}
                    className="flex-1"
                  >
                    Supplier
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="particular" className="text-right font-medium">
                  Description *
                </Label>
                <Input
                  id="particular"
                  placeholder="Enter cargo description"
                  className="col-span-3"
                  value={formData.particular}
                  onChange={(e) =>
                    handleInputChange("particular", e.target.value)
                  }
                  required
                />
              </div>
              {/* Customer/Supplier Selection */}
              {entityType === "customer" ? (
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="customer" className="text-right font-medium">
                    Customer *
                    {loadingCustomers && (
                      <Loader2 size={12} className="inline animate-spin ml-1" />
                    )}
                  </Label>
                  <Select
                    value={formData.customer_id}
                    onValueChange={(value) =>
                      handleInputChange("customer_id", value)
                    }
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select customer" />
                    </SelectTrigger>
                    <SelectContent>
                      {customers.map((customer) => (
                        <SelectItem key={customer.id} value={customer.id}>
                          {customer.name}{" "}
                          {customer.location && `(${customer.location})`}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              ) : (
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="supplier" className="text-right font-medium">
                    Supplier *
                    {loadingSuppliers && (
                      <Loader2 size={12} className="inline animate-spin ml-1" />
                    )}
                  </Label>
                  <Select
                    value={formData.supplier_id}
                    onValueChange={(value) =>
                      handleInputChange("supplier_id", value)
                    }
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select supplier" />
                    </SelectTrigger>
                    <SelectContent>
                      {suppliers.map((supplier) => (
                        <SelectItem key={supplier.id} value={supplier.id}>
                          {supplier.name}{" "}
                          {supplier.location && `(${supplier.location})`}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="type" className="text-right font-medium">
                  Safety Type
                </Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) => handleInputChange("type", value)}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="SAFE">Safe Goods</SelectItem>
                    <SelectItem value="DANGEROUS">Dangerous Goods</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="quantity" className="text-right font-medium">
                  Quantity
                </Label>
                <Input
                  id="quantity"
                  type="number"
                  placeholder="Enter quantity"
                  className="col-span-3"
                  value={formData.quantity}
                  onChange={(e) =>
                    handleInputChange("quantity", e.target.value)
                  }
                  min="1"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="weight" className="text-right font-medium">
                  Weight
                </Label>
                <div className="col-span-3 flex gap-2">
                  <Input
                    id="weight"
                    type="number"
                    placeholder="Enter weight"
                    className="flex-1"
                    value={formData.weight_value}
                    onChange={(e) =>
                      handleInputChange(
                        "weight_value",
                        parseFloat(e.target.value) || 0
                      )
                    }
                    step="0.01"
                  />
                  <SelectField
                    id="weightUnit"
                    placeholder="Select weight unit"
                    value={formData.weight_unit}
                    options={[
                      { value: "KILOGRAMS", label: "Kilograms (kg)" },
                      { value: "POUNDS", label: "Pounds (lb)" },
                      { value: "GRAMS", label: "Grams (g)" },
                      { value: "TONS", label: "Tons (t)" },
                      { value: "OUNCES", label: "Ounces (oz)" },
                      { value: "SHORT_TON", label: "Short Ton" },
                      { value: "LONG_TON", label: "Long Ton" },
                    ]}
                    onChange={(value) => handleInputChange("weightUnit", value)}
                  />
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="dimensions" className="text-right font-medium">
                  Dimensions
                </Label>
                <div className="col-span-3 flex gap-2">
                  <Input
                    placeholder="Length"
                    type="number"
                    className="flex-1"
                    value={formData.dimension_length}
                    onChange={(e) =>
                      handleInputChange(
                        "dimension_length",
                        parseFloat(e.target.value) || 0
                      )
                    }
                    step="0.01"
                  />
                  <Input
                    placeholder="Width"
                    type="number"
                    className="flex-1"
                    value={formData.dimension_width}
                    onChange={(e) =>
                      handleInputChange(
                        "dimension_width",
                        parseFloat(e.target.value) || 0
                      )
                    }
                    step="0.01"
                  />
                  <Input
                    placeholder="Height"
                    type="number"
                    className="flex-1"
                    value={formData.dimension_height}
                    onChange={(e) =>
                      handleInputChange(
                        "dimension_height",
                        parseFloat(e.target.value) || 0
                      )
                    }
                    step="0.01"
                  />
                  <Select
                    value={formData.dimension_unit}
                    onValueChange={(value) =>
                      handleInputChange("dimension_unit", value)
                    }
                  >
                    <SelectTrigger className="w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="CM">cm</SelectItem>
                      <SelectItem value="M">m</SelectItem>
                      <SelectItem value="IN">in</SelectItem>
                      <SelectItem value="FT">ft</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="unitPrice" className="text-right font-medium">
                  Unit Price ($)
                </Label>
                <Input
                  id="unitPrice"
                  type="number"
                  placeholder="Enter unit price"
                  className="col-span-3"
                  value={formData.unit_price}
                  onChange={(e) =>
                    handleInputChange(
                      "unit_price",
                      parseFloat(e.target.value) || 0
                    )
                  }
                  step="0.01"
                />
              </div>
            </div>
            <DialogFooter className="px-6 py-4 border-t flex flex-row justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                className="border-gray-200"
                onClick={() => setOpen(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-primary hover:bg-primary/90 text-white"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Create Cargo"
                )}
              </Button>
            </DialogFooter>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
