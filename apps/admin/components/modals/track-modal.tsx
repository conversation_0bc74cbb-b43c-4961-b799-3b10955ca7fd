"use client";

import { useRouter } from "next/navigation";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Truck } from "lucide-react";

export function TrackModal() {
  const router = useRouter();

  const handleTrackShipment = () => {
    router.push("/shipping-management");
  };

  return (
    <Button
      variant="outline"
      className="flex items-center gap-3 h-auto py-4 px-4 border border-gray-200 justify-start text-left hover:bg-gray-50 hover:text-foreground"
      onClick={handleTrackShipment}
    >
      <div className="w-9 h-9 rounded-full bg-blue-500/10 flex items-center justify-center flex-shrink-0">
        <Truck size={16} className="text-blue-500" />
      </div>
      <div>
        <span className="block font-medium">Track Shipment</span>
        <span className="text-xs text-muted-foreground">Get location</span>
      </div>
    </Button>
  );
}
