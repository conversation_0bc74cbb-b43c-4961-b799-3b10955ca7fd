"use client";

import { ThemeProvider } from "next-themes";
import { LazyMotion, domAnimation, MotionConfig } from "framer-motion";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import { store, persistor } from "@/store";
import { AuthProvider } from "./providers/AuthProvider";
import { NotificationProvider } from "./providers/NotificationProvider";
import { Toaster } from "@workspace/ui/components/sonner";

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <AuthProvider>
          <NotificationProvider>
            <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
              <LazyMotion features={domAnimation}>
                <MotionConfig reducedMotion="user">
                  {children}
                  <Toaster position="top-center" richColors />
                </MotionConfig>
              </LazyMotion>
            </ThemeProvider>
          </NotificationProvider>
        </AuthProvider>
      </PersistGate>
    </Provider>
  );
}
