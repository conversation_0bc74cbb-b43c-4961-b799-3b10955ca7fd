"use client";

import { useRouter } from "next/navigation";
import React from "react";
import { motion } from "framer-motion";
import {
  Archive,
  Trash2,
  Check,
  MoreH<PERSON>zon<PERSON>,
  Clock,
  CheckCircle,
} from "lucide-react";
import { cn } from "@workspace/ui/lib/utils";
import { Button } from "@workspace/ui/components/button";
import { Checkbox } from "@workspace/ui/components/checkbox";

import { Badge } from "@workspace/ui/components/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";

interface NotificationDisplay {
  id: string;
  read: boolean;
  type: string;
  objectType: string;
  objectId: string;
  message: string;
  timestamp: string;
  icon: React.ElementType;
  iconColor: string;
}

interface NotificationCardProps {
  notification: NotificationDisplay;
  isSelected: boolean;
  onSelect: (id: string, selected: boolean) => void;
  onArchive: (id: string) => void;
  onDelete: (id: string) => void;
  onDismiss: (id: string) => void;
  index: number;
}

// Get minimalistic theme colors for light icons
const getMinimalisticColors = (iconColor: string) => {
  const colorMap: Record<string, { icon: string; bg: string; text: string }> = {
    "text-green-600": {
      icon: "text-green-400/60",
      bg: "bg-green-50/30",
      text: "text-green-600/70",
    },
    "text-red-600": {
      icon: "text-red-400/60",
      bg: "bg-red-50/30",
      text: "text-red-600/70",
    },
    "text-amber-600": {
      icon: "text-amber-400/60",
      bg: "bg-amber-50/30",
      text: "text-amber-600/70",
    },
    "text-blue-600": {
      icon: "text-blue-400/60",
      bg: "bg-blue-50/30",
      text: "text-blue-600/70",
    },
    "text-purple-600": {
      icon: "text-purple-400/60",
      bg: "bg-purple-50/30",
      text: "text-purple-600/70",
    },
    "text-indigo-600": {
      icon: "text-indigo-400/60",
      bg: "bg-indigo-50/30",
      text: "text-indigo-600/70",
    },
    "text-orange-600": {
      icon: "text-orange-400/60",
      bg: "bg-orange-50/30",
      text: "text-orange-600/70",
    },
  };

  return (
    colorMap[iconColor] || {
      icon: "text-gray-400/60",
      bg: "bg-gray-50/30",
      text: "text-gray-600/70",
    }
  );
};

export function NotificationCard({
  notification,
  isSelected,
  onSelect,
  onArchive,
  onDelete,
  onDismiss,
  index,
}: NotificationCardProps) {
  const router = useRouter();
  const IconComponent = notification.icon;
  const themeColors = getMinimalisticColors(notification.iconColor);

  function entityDirect(entityType: string, entityId: string) {
    if (!entityId || !entityType) alert(`Entity not found`);

    switch (entityType.toLowerCase()) {
      case "freights":
        router.push(`/batch-management/${entityId}`);
        break;
      case "batches":
        router.push(`/batch-management/${entityId}`);
        break;
      case "cargos":
        router.push(`/cargo-management/${entityId}`);
        break;
      case "invoices":
        router.push(`/invoice-workflow/${entityId}`);
        break;
      case "shipments":
        router.push(`/shipping-management/${entityId}`);
        break;
      case "handovers":
        router.push(`/release-authorization/${entityId}`);
        break;
      case "tasks":
        router.push(`/task-management`);
        break;
      case "finance":
        router.push(`/finance/${entityId}`);
        break;
      default:
        router.push("#");
        break;
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 5 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2, delay: index * 0.02 }}
      className="mb-1"
    >
      <div
        className={cn(
          "group relative flex items-center gap-4 p-3 transition-all duration-300 cursor-pointer",
          // Base minimalistic styling
          "hover:bg-gray-50",
          // Selection state
          isSelected && "bg-blue-50/30 ring-1 ring-blue-200",
          // Unread notifications - subtle prominence
          !notification.read && [
            "bg-white border-l-2 border-l-blue-300",
            "hover:bg-blue-50 hover:border-l-blue-400",
          ],
          // Read notifications - minimal styling
          notification.read && [
            "bg-gray-50 border-l-2 border-l-gray-200",
            "hover:bg-gray-50",
          ]
        )}
      >
        {/* Selection Checkbox */}
        <Checkbox
          checked={isSelected}
          onCheckedChange={(checked) => onSelect(notification.id, !!checked)}
          className="flex-shrink-0"
          hidden={notification.read}
        />

        {/* Icon */}
        <div
          onClick={() => {
            let { objectType, objectId }: any = notification ?? {};
            entityDirect(objectType, objectId);
          }}
          className={cn(
            "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300",
            // Unread notifications - subtle background
            !notification.read && [themeColors.bg, "border border-gray-100"],
            // Read notifications - minimal styling
            notification.read && ["bg-gray-50 border border-gray-100"]
          )}
        >
          <IconComponent
            className={cn(
              "h-3.5 w-3.5 transition-all duration-300",
              // Unread notifications - light icon color
              !notification.read && themeColors.icon,
              // Read notifications - very muted icon
              notification.read && "text-gray-300"
            )}
          />
        </div>

        {/* Content */}
        <div
          className="flex-1 min-w-0"
          onClick={() => {
            let { objectType, objectId }: any = notification ?? {};
            entityDirect(objectType, objectId);
          }}
        >
          <div className="flex items-start justify-between gap-2">
            <div className="flex-1 min-w-0">
              {/* Header with type and unread indicator */}
              <div className="flex items-center gap-2 mb-1">
                <Badge
                  variant="outline"
                  className={cn(
                    "text-xs px-1.5 py-0.5 h-5 font-normal border-0 transition-all duration-300",
                    // Unread notifications - subtle badge
                    !notification.read && [
                      themeColors.bg,
                      themeColors.text,
                      "font-medium",
                    ],
                    // Read notifications - minimal badge
                    notification.read && ["bg-gray-50 text-gray-400/80"]
                  )}
                >
                  {notification.type}
                </Badge>
                {!notification.read && (
                  <div className="w-1.5 h-1.5 bg-blue-400 rounded-full flex-shrink-0" />
                )}
                {notification.read && (
                  <CheckCircle className="w-3 h-3 text-green-400 flex-shrink-0" />
                )}
              </div>

              {/* Message */}
              <p
                className={cn(
                  "text-sm leading-relaxed line-clamp-2 transition-all duration-300",
                  // Unread notifications - subtle prominence
                  !notification.read && [
                    "text-gray-700 font-medium",
                    "tracking-normal",
                  ],
                  // Read notifications - muted text
                  notification.read && ["text-gray-500", "font-normal"]
                )}
              >
                {notification.message}
              </p>
            </div>

            {/* Timestamp and Actions */}
            <div className="flex items-center gap-1 flex-shrink-0">
              <div
                className={cn(
                  "text-xs flex items-center gap-1 transition-all duration-300",
                  // Unread notifications - subtle timestamp
                  !notification.read && ["text-gray-500", "font-normal"],
                  // Read notifications - very muted timestamp
                  notification.read && ["text-gray-400", "font-normal"]
                )}
              >
                <Clock className="h-2.5 w-2.5" />
                {notification.timestamp}
              </div>

              {/* Actions */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 opacity-0 group-hover:opacity-60 transition-all duration-300 ml-1 hover:bg-gray-100"
                  >
                    <MoreHorizontal className="h-3 w-3 text-gray-400" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-44">
                  {!notification.read && (
                    <DropdownMenuItem
                      onClick={() => onDismiss(notification.id)}
                      className="text-xs"
                    >
                      <Check className="h-3.5 w-3.5 mr-2 text-green-600" />
                      Mark as read
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem
                    onClick={() => onArchive(notification.id)}
                    className="text-xs"
                  >
                    <Archive className="h-3.5 w-3.5 mr-2 text-gray-600" />
                    Archive
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => onDelete(notification.id)}
                    className="text-xs text-red-600 focus:text-red-600"
                  >
                    <Trash2 className="h-3.5 w-3.5 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
