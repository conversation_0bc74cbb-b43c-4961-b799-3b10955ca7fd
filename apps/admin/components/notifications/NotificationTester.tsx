"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Badge } from "@workspace/ui/components/badge";
import { Bell, Send, Volume2, Wifi, WifiOff } from "lucide-react";
import { useAppSelector } from "@/store/hooks";
import { notificationService } from "@/lib/logistics";
import { useRealtimeNotifications } from "@/lib/hooks/useRealtimeNotifications";
import { toast } from "sonner";

export function NotificationTester() {
  const { user } = useAppSelector((state) => state.auth);
  const {
    unreadCount,
    isConnected,
    isLoading,
    error,
    notifications,
    playNotificationSound,
  } = useRealtimeNotifications();

  const [testNotification, setTestNotification] = useState({
    name: "Test Notification",
    message: "This is a test notification from Supabase realtime!",
  });
  const [isSending, setIsSending] = useState(false);

  const handleSendTestNotification = async () => {
    if (!user?.accountId) {
      toast.error("Please sign in to send test notifications");
      return;
    }

    setIsSending(true);

    try {
      const result = await notificationService.createNotification({
        account_id: user.accountId,
        name: testNotification.name,
        message: testNotification.message,
        status: "ACTIVE",
        associated_table: "accounts",
        associated_id: user.accountId,
      });

      if (result.success) {
        toast.success("Test notification sent!");
      } else {
        toast.error(`Failed to send notification: ${result.error}`);
      }
    } catch (error: any) {
      console.error("Error sending test notification:", error);
      toast.error("Failed to send test notification");
    } finally {
      setIsSending(false);
    }
  };

  // const playTestSound = () => {
  //   try {
  //     const audio = new Audio("/ringtone_antic_ios_17.mp3");
  //     audio.volume = 0.5;
  //     audio.play().catch(() => {
  //       toast.error("Could not play sound - check browser permissions");
  //     });
  //   } catch (error) {
  //     toast.error("Error playing sound");
  //   }
  // };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Supabase Realtime Notification Tester
        </CardTitle>
        <CardDescription>
          Test the realtime notification system with sound alerts
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Connection Status */}
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2">
            {isConnected ? (
              <Wifi className="h-4 w-4 text-green-600" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-600" />
            )}
            <span className="text-sm font-medium">
              Realtime Connection:{" "}
              {isLoading
                ? "Connecting..."
                : isConnected
                  ? "Connected"
                  : "Disconnected"}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant={unreadCount > 0 ? "destructive" : "secondary"}>
              {unreadCount} unread
            </Badge>
            <Badge variant="outline">{notifications.length} total</Badge>
          </div>
        </div>

        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-600">Error: {error}</p>
          </div>
        )}

        {/* Test Notification Form */}
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="notification-name">Notification Title</Label>
            <Input
              id="notification-name"
              value={testNotification.name}
              onChange={(e) =>
                setTestNotification((prev) => ({
                  ...prev,
                  name: e.target.value,
                }))
              }
              placeholder="Enter notification title"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="notification-message">Notification Message</Label>
            <Input
              id="notification-message"
              value={testNotification.message}
              onChange={(e) =>
                setTestNotification((prev) => ({
                  ...prev,
                  message: e.target.value,
                }))
              }
              placeholder="Enter notification message"
            />
          </div>

          <div className="flex gap-2">
            <Button
              onClick={handleSendTestNotification}
              disabled={isSending || !user?.accountId}
              className="flex-1"
            >
              <Send className="h-4 w-4 mr-2" />
              {isSending ? "Sending..." : "Send Test Notification"}
            </Button>

            <Button variant="outline" onClick={playNotificationSound}>
              <Volume2 className="h-4 w-4 mr-2" />
              Test Sound
            </Button>
          </div>
        </div>

        {/* Instructions */}
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">How it works:</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>
              • Click "Send Test Notification" to create a new notification
            </li>
            <li>
              • The notification will appear in real-time via Supabase realtime
            </li>
            <li>• You'll hear the iOS 17 notification sound</li>
            <li>• A toast notification will appear using Sonner</li>
            <li>• The notification count will update automatically</li>
            <li>• Check the notifications page to see the full list</li>
          </ul>
        </div>

        {/* Recent Notifications Preview */}
        {notifications.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-medium text-gray-900">Recent Notifications</h4>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {notifications.slice(0, 5).map((notification) => (
                <div
                  key={notification.id}
                  className="p-2 bg-gray-50 rounded border text-sm"
                >
                  <div className="font-medium">{notification.name}</div>
                  <div className="text-gray-600">{notification.message}</div>
                  <div className="text-xs text-gray-400 mt-1">
                    {new Date(notification.created_at || "").toLocaleString()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
