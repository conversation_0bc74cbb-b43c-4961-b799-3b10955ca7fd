"use client";

import React from 'react';
import { <PERSON>, BellRing, Wifi, WifiOff } from 'lucide-react';
import { cn } from '@workspace/ui/lib/utils';
import { Button } from '@workspace/ui/components/button';
import { Badge } from '@workspace/ui/components/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@workspace/ui/components/tooltip';
import { useNotifications } from '@/components/providers/NotificationProvider';
import Link from 'next/link';

interface NotificationBadgeProps {
  className?: string;
  showConnectionStatus?: boolean;
  variant?: 'default' | 'ghost' | 'outline';
  size?: 'default' | 'sm' | 'lg' | 'icon';
}

export function NotificationBadge({ 
  className,
  showConnectionStatus = false,
  variant = 'ghost',
  size = 'icon'
}: NotificationBadgeProps) {
  const { unreadCount, isConnected, isLoading, error } = useNotifications();

  const hasUnread = unreadCount > 0;
  const displayCount = unreadCount > 99 ? '99+' : unreadCount.toString();

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant={variant}
            size={size}
            className={cn(
              'relative',
              hasUnread && 'text-primary',
              className
            )}
            asChild
          >
            <Link href="/notifications">
              <div className="relative">
                {hasUnread ? (
                  <BellRing className="h-5 w-5" />
                ) : (
                  <Bell className="h-5 w-5" />
                )}
                
                {/* Unread count badge */}
                {hasUnread && (
                  <Badge
                    variant="destructive"
                    className={cn(
                      'absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs font-bold',
                      'min-w-[1.25rem] rounded-full',
                      unreadCount > 9 && 'px-1'
                    )}
                  >
                    {displayCount}
                  </Badge>
                )}

                {/* Connection status indicator */}
                {showConnectionStatus && (
                  <div className="absolute -bottom-1 -right-1">
                    {isConnected ? (
                      <Wifi className="h-3 w-3 text-green-500" />
                    ) : (
                      <WifiOff className="h-3 w-3 text-red-500" />
                    )}
                  </div>
                )}
              </div>
            </Link>
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <div className="text-center">
            <p className="font-medium">
              {hasUnread ? `${unreadCount} unread notification${unreadCount === 1 ? '' : 's'}` : 'No unread notifications'}
            </p>
            {showConnectionStatus && (
              <p className="text-xs text-muted-foreground mt-1">
                {isLoading ? 'Connecting...' : isConnected ? 'Real-time connected' : error ? `Error: ${error}` : 'Disconnected'}
              </p>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

// Simple notification count component for use in navigation
export function NotificationCount() {
  const { unreadCount } = useNotifications();
  
  if (unreadCount === 0) return null;
  
  return (
    <Badge
      variant="destructive"
      className="ml-2 h-5 w-5 flex items-center justify-center p-0 text-xs font-bold rounded-full"
    >
      {unreadCount > 99 ? '99+' : unreadCount}
    </Badge>
  );
}

// Connection status indicator component
export function NotificationConnectionStatus() {
  const { isConnected, isLoading, error } = useNotifications();
  
  if (isLoading) {
    return (
      <div className="flex items-center gap-2 text-xs text-muted-foreground">
        <div className="h-2 w-2 bg-yellow-500 rounded-full animate-pulse" />
        Connecting...
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="flex items-center gap-2 text-xs text-red-600">
        <div className="h-2 w-2 bg-red-500 rounded-full" />
        Connection error
      </div>
    );
  }
  
  return (
    <div className="flex items-center gap-2 text-xs text-muted-foreground">
      <div className={cn(
        "h-2 w-2 rounded-full",
        isConnected ? "bg-green-500" : "bg-gray-400"
      )} />
      {isConnected ? 'Real-time connected' : 'Disconnected'}
    </div>
  );
}
