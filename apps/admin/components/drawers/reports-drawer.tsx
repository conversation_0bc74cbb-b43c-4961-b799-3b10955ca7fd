"use client";

import { useState } from "react";
import { 
  Sheet, 
  Sheet<PERSON>ontent, 
  SheetDescription, 
  She<PERSON><PERSON><PERSON><PERSON>, 
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>ooter,
  SheetClose
} from "@workspace/ui/components/sheet";
import { Button } from "@workspace/ui/components/button";
import { Label } from "@workspace/ui/components/label";
import { BarChart2, DownloadCloud, Calendar, ArrowRight, PieChart, TrendingUp, RefreshCw } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@workspace/ui/components/select";

export function ReportsDrawer() {
  const [timeRange, setTimeRange] = useState("month");

  const reportTypes = [
    { 
      id: "shipment-volume", 
      title: "Shipment Volume", 
      description: "Track monthly shipment volumes and growth trends",
      icon: TrendingUp,
      color: "text-blue-500 bg-blue-100" 
    },
    { 
      id: "delivery-performance", 
      title: "Delivery Performance", 
      description: "Analyze on-time delivery rates and delays",
      icon: RefreshCw,
      color: "text-green-500 bg-green-100" 
    },
    { 
      id: "cargo-distribution", 
      title: "Cargo Distribution", 
      description: "Breakdown of cargo types handled over time",
      icon: PieChart,
      color: "text-purple-500 bg-purple-100" 
    },
    { 
      id: "revenue-trends", 
      title: "Revenue Analysis", 
      description: "Revenue trends across routes and customers",
      icon: BarChart2,
      color: "text-amber-500 bg-amber-100" 
    },
    { 
      id: "customer-activity", 
      title: "Customer Activity", 
      description: "Customer shipment frequency and retention",
      icon: Calendar,
      color: "text-indigo-500 bg-indigo-100" 
    },
  ];

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button 
          variant="outline" 
          className="flex items-center gap-3 h-auto py-4 px-4 border border-gray-200 justify-start text-left hover:bg-gray-50 hover:text-foreground"
        >
          <div className="w-9 h-9 rounded-full bg-purple-500/10 flex items-center justify-center flex-shrink-0">
            <BarChart2 size={16} className="text-purple-500" />
          </div>
          <div>
            <span className="block font-medium">Reports</span>
            <span className="text-xs text-muted-foreground">View analytics</span>
          </div>
        </Button>
      </SheetTrigger>
      <SheetContent className="w-[400px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle>Analytics & Reports</SheetTitle>
          <SheetDescription>
            Access and generate detailed logistics analytics and reports.
          </SheetDescription>
        </SheetHeader>
        <div className="py-6">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-sm font-medium">Select Time Range</h3>
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Select time range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="week">Last 7 days</SelectItem>
                <SelectItem value="month">Last 30 days</SelectItem>
                <SelectItem value="quarter">Last 90 days</SelectItem>
                <SelectItem value="year">Last 12 months</SelectItem>
                <SelectItem value="custom">Custom range</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-3">
            {reportTypes.map((report) => (
              <Button
                key={report.id}
                variant="outline"
                className="w-full justify-between items-center py-4 px-4 h-auto border border-gray-200 text-left hover:bg-gray-50"
              >
                <div className="flex items-start gap-3">
                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${report.color}`}>
                    <report.icon size={18} />
                  </div>
                  <div className="text-left">
                    <h4 className="font-medium">{report.title}</h4>
                    <p className="text-xs text-muted-foreground">{report.description}</p>
                  </div>
                </div>
                <ArrowRight size={16} className="text-muted-foreground" />
              </Button>
            ))}
          </div>

          <div className="border-t border-gray-200 mt-6 pt-6">
            <Button className="w-full gap-2 justify-center">
              <DownloadCloud size={16} />
              <span>Export All Reports</span>
            </Button>
          </div>
        </div>
        <SheetFooter>
          <SheetClose asChild>
            <Button type="button" variant="outline">Close</Button>
          </SheetClose>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
} 