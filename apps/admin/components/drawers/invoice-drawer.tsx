"use client";

import { useState } from "react";
import { 
  Sheet, 
  She<PERSON><PERSON>ontent, 
  She<PERSON><PERSON><PERSON><PERSON>, 
  She<PERSON><PERSON><PERSON><PERSON>, 
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>ooter,
  SheetClose
} from "@workspace/ui/components/sheet";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { FileText, Search, Download, Eye, Filter } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@workspace/ui/components/select";

export function InvoiceDrawer() {
  const [filterStatus, setFilterStatus] = useState("all");
  
  const mockInvoices = [
    { id: "INV-2023-0425", customer: "Acme Corp", amount: "$1,250.00", date: "Apr 25, 2023", status: "Paid" },
    { id: "INV-2023-0418", customer: "TechGlobal Inc", amount: "$3,780.50", date: "Apr 18, 2023", status: "Pending" },
    { id: "INV-2023-0410", customer: "Savannah Traders", amount: "$950.75", date: "Apr 10, 2023", status: "Overdue" },
    { id: "INV-2023-0402", customer: "Desert Logistics", amount: "$2,340.00", date: "Apr 02, 2023", status: "Paid" },
    { id: "INV-2023-0325", customer: "Coastal Union", amount: "$1,670.25", date: "Mar 25, 2023", status: "Paid" },
  ];

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "paid": return "bg-green-100 text-green-800";
      case "pending": return "bg-amber-100 text-amber-800";
      case "overdue": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const filteredInvoices = filterStatus === "all" 
    ? mockInvoices 
    : mockInvoices.filter(inv => inv.status.toLowerCase() === filterStatus.toLowerCase());

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button 
          variant="outline" 
          className="flex items-center gap-3 h-auto py-4 px-4 border border-gray-200 justify-start text-left hover:bg-gray-50 hover:text-foreground"
        >
          <div className="w-9 h-9 rounded-full bg-amber-500/10 flex items-center justify-center flex-shrink-0">
            <FileText size={16} className="text-amber-500" />
          </div>
          <div>
            <span className="block font-medium">Invoices</span>
            <span className="text-xs text-muted-foreground">View invoices</span>
          </div>
        </Button>
      </SheetTrigger>
      <SheetContent className="w-[400px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle>Invoices</SheetTitle>
          <SheetDescription>
            View and manage your invoices.
          </SheetDescription>
        </SheetHeader>
        <div className="py-6">
          <div className="flex gap-2 mb-6">
            <Input
              placeholder="Search invoices..."
              className="flex-1"
            />
            <Select 
              value={filterStatus} 
              onValueChange={setFilterStatus}
            >
              <SelectTrigger className="w-[130px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="paid">Paid</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="overdue">Overdue</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-3">
            {filteredInvoices.map((invoice) => (
              <div 
                key={invoice.id} 
                className="flex items-center justify-between border border-gray-200 rounded-lg p-4 hover:bg-gray-50"
              >
                <div>
                  <p className="font-medium">{invoice.id}</p>
                  <p className="text-sm text-muted-foreground">{invoice.customer}</p>
                  <div className="flex items-center gap-2 mt-1">
                    <span className={`text-xs px-2 py-0.5 rounded-full ${getStatusColor(invoice.status)}`}>
                      {invoice.status}
                    </span>
                    <span className="text-xs text-muted-foreground">{invoice.date}</span>
                  </div>
                </div>
                <div className="flex flex-col items-end gap-2">
                  <p className="font-bold">{invoice.amount}</p>
                  <div className="flex gap-1">
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <Eye size={14} />
                    </Button>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <Download size={14} />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-4 text-center">
            <Button variant="outline" className="text-xs">Load More</Button>
          </div>
        </div>
        <SheetFooter>
          <SheetClose asChild>
            <Button type="button" variant="outline">Close</Button>
          </SheetClose>
          <Button type="button">Create Invoice</Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
} 