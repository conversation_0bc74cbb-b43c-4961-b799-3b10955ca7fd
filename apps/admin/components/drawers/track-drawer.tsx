"use client";

import { useState } from "react";
import { 
  Sheet, 
  <PERSON><PERSON><PERSON>onte<PERSON>, 
  SheetDescription, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>ooter,
  SheetClose
} from "@workspace/ui/components/sheet";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Truck, Search, MapPin, CheckCircle, Clock, AlertTriangle } from "lucide-react";

export function TrackDrawer() {
  const [trackingId, setTrackingId] = useState("");
  const [searching, setSearching] = useState(false);
  const [trackingResult, setTrackingResult] = useState<null | {
    id: string;
    status: string;
    origin: string;
    destination: string;
    currentLocation: string;
    eta: string;
    updates: { date: string; location: string; status: string; }[]
  }>(null);

  const mockTrackingData = {
    id: "SHM-2023-0634",
    status: "In Transit",
    origin: "Shanghai, China",
    destination: "Nairobi, Kenya",
    currentLocation: "Dubai, UAE",
    eta: "May 15, 2023",
    updates: [
      { date: "May 10, 2023", location: "Dubai, UAE", status: "Arrived at transit point" },
      { date: "May 8, 2023", location: "Dubai, UAE", status: "Departed for transit" },
      { date: "May 5, 2023", location: "Shanghai, China", status: "Cleared customs" },
      { date: "May 3, 2023", location: "Shanghai, China", status: "Processing" }
    ]
  };

  const handleSearch = () => {
    setSearching(true);
    // Simulate API call
    setTimeout(() => {
      setTrackingResult(mockTrackingData);
      setSearching(false);
    }, 1000);
  };

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button 
          variant="outline" 
          className="flex items-center gap-3 h-auto py-4 px-4 border border-gray-200 justify-start text-left hover:bg-gray-50 hover:text-foreground"
        >
          <div className="w-9 h-9 rounded-full bg-blue-500/10 flex items-center justify-center flex-shrink-0">
            <Truck size={16} className="text-blue-500" />
          </div>
          <div>
            <span className="block font-medium">Track Shipment</span>
            <span className="text-xs text-muted-foreground">Get location</span>
          </div>
        </Button>
      </SheetTrigger>
      <SheetContent className="w-[400px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle>Track Shipment</SheetTitle>
          <SheetDescription>
            Enter shipment ID to track its current status and location.
          </SheetDescription>
        </SheetHeader>
        <div className="py-6">
          <div className="flex gap-2 mb-6">
            <Input
              placeholder="Enter shipment ID (e.g., SHM-2023-XXXX)"
              value={trackingId}
              onChange={(e) => setTrackingId(e.target.value)}
              className="flex-1"
            />
            <Button 
              onClick={handleSearch} 
              disabled={!trackingId || searching}
              className="gap-1"
            >
              {searching ? (
                <><Clock className="h-4 w-4 animate-spin" /> Searching</>
              ) : (
                <><Search className="h-4 w-4" /> Track</>
              )}
            </Button>
          </div>

          {!trackingResult && !searching && (
            <div className="flex flex-col items-center justify-center py-8 text-center text-muted-foreground">
              <Truck className="h-12 w-12 mb-4 text-muted-foreground/40" />
              <p>Enter a shipment ID to track its status and location.</p>
              <p className="text-xs mt-2">You can find the shipment ID on your invoice or confirmation email.</p>
            </div>
          )}

          {trackingResult && (
            <div className="space-y-6">
              <div className="flex justify-between items-start border-b border-gray-200 pb-4">
                <div>
                  <h3 className="font-medium">{trackingResult.id}</h3>
                  <p className="text-sm text-muted-foreground">Status: 
                    <span className={`ml-1 font-medium ${
                      trackingResult.status === "Delivered" ? "text-green-600" : 
                      trackingResult.status === "In Transit" ? "text-blue-600" : "text-amber-600"
                    }`}>
                      {trackingResult.status}
                    </span>
                  </p>
                </div>
                <div className="text-right text-sm">
                  <p className="text-muted-foreground">Estimated Delivery</p>
                  <p className="font-medium">{trackingResult.eta}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-muted-foreground">Origin</p>
                  <p className="font-medium">{trackingResult.origin}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Destination</p>
                  <p className="font-medium">{trackingResult.destination}</p>
                </div>
                <div className="col-span-2">
                  <p className="text-muted-foreground">Current Location</p>
                  <div className="flex items-center gap-1">
                    <MapPin className="h-4 w-4 text-red-500" />
                    <p className="font-medium">{trackingResult.currentLocation}</p>
                  </div>
                </div>
              </div>

              <div className="border-t border-gray-200 pt-4">
                <h4 className="font-medium mb-3">Shipment Updates</h4>
                <div className="space-y-3">
                  {trackingResult.updates.map((update, index) => (
                    <div key={index} className="relative pl-6 pb-3">
                      {index !== trackingResult.updates.length - 1 && (
                        <div className="absolute top-2 left-[7px] w-[1px] h-full bg-gray-200"></div>
                      )}
                      <div className="absolute top-1 left-0 w-3.5 h-3.5 rounded-full bg-blue-100">
                        <div className="w-2 h-2 rounded-full bg-blue-600 absolute top-[3px] left-[3px]"></div>
                      </div>
                      <p className="font-medium text-sm">{update.status}</p>
                      <p className="text-xs text-muted-foreground">
                        {update.date} • {update.location}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
        <SheetFooter>
          <SheetClose asChild>
            <Button type="button" variant="outline">Close</Button>
          </SheetClose>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
} 