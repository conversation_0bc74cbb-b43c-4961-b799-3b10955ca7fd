"use client";

import { useState } from "react";
import { 
  Sheet, 
  Sheet<PERSON>ontent, 
  Sheet<PERSON><PERSON><PERSON>, 
  Sheet<PERSON>eader, 
  Sheet<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
  She<PERSON><PERSON>ooter,
  SheetClose
} from "@workspace/ui/components/sheet";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Package, Plus } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@workspace/ui/components/select";

export function CargoDrawer() {
  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button 
          variant="outline" 
          className="flex items-center gap-3 h-auto py-4 px-4 border border-gray-200 justify-start text-left hover:bg-gray-50 hover:text-foreground"
        >
          <div className="w-9 h-9 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
            <Package size={16} className="text-primary" />
          </div>
          <div>
            <span className="block font-medium">New Cargo</span>
            <span className="text-xs text-muted-foreground">Create shipment</span>
          </div>
        </Button>
      </SheetTrigger>
      <SheetContent className="w-[400px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle>Create New Shipment</SheetTitle>
          <SheetDescription>
            Enter the shipment details to create a new cargo entry.
          </SheetDescription>
        </SheetHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="shipment-id" className="text-right">
              Shipment ID
            </Label>
            <Input
              id="shipment-id"
              placeholder="Auto-generated"
              className="col-span-3"
              disabled
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="customer" className="text-right">
              Customer
            </Label>
            <Select>
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select customer" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="acme">Acme Corp</SelectItem>
                <SelectItem value="global">Global Shipping Co.</SelectItem>
                <SelectItem value="tech">TechGlobal Inc</SelectItem>
                <SelectItem value="savannah">Savannah Traders</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="cargo-type" className="text-right">
              Cargo Type
            </Label>
            <Select>
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select cargo type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="electronics">Electronics</SelectItem>
                <SelectItem value="textile">Textile</SelectItem>
                <SelectItem value="food">Food</SelectItem>
                <SelectItem value="machinery">Machinery</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="origin" className="text-right">
              Origin
            </Label>
            <Input
              id="origin"
              placeholder="Enter origin location"
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="destination" className="text-right">
              Destination
            </Label>
            <Input
              id="destination"
              placeholder="Enter destination"
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="weight" className="text-right">
              Weight (kg)
            </Label>
            <Input
              id="weight"
              type="number"
              placeholder="Enter weight"
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="ship-date" className="text-right">
              Ship Date
            </Label>
            <Input
              id="ship-date"
              type="date"
              className="col-span-3"
            />
          </div>
        </div>
        <SheetFooter>
          <SheetClose asChild>
            <Button type="button" variant="outline">Cancel</Button>
          </SheetClose>
          <Button type="submit">Create Shipment</Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
} 