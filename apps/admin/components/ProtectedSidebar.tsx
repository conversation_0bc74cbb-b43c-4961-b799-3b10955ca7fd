"use client";

import Link from "next/link";
import Image from "next/image";
import { redirect, usePathname } from "next/navigation";

import { useState } from "react";

import {
  ShieldBan,
  ChevronLeft,
  ChevronRight,
  Home,
  Bell,
  CheckSquare,
  Package,
  BarChart3,
  FileText,
  Unlock,
  Users,
  MessageSquare,
  PieChart,
  UserCog,
  Settings,
  LogOut,
  UserCircle,
  Loader2,
  Truck,
  Ship,
  Container,
} from "lucide-react";

import { cn } from "@workspace/ui/lib/utils";
import { motion, AnimatePresence } from "framer-motion";
import { Badge } from "@workspace/ui/components/badge";

import { useRBAC } from "@/lib/hooks/useRBAC";
import { EntityType } from "@/lib/logistics/types/permissions";
import { useAuth } from "./providers/AuthProvider";
import { useRealtimeNotifications } from "@/lib/hooks/useRealtimeNotifications";

interface SidebarLinkProps {
  href: string;
  icon: React.ReactNode;
  label: string;
  collapsed: boolean;
  entity?: EntityType;
  badge?: number; // For notification count
}

interface NavigationItem {
  href: string;
  icon: React.ReactNode;
  label: string;
  entity?: EntityType;
  requiresAuth?: boolean;
}

const ProtectedSidebarLink = ({
  href,
  icon,
  label,
  entity,
  collapsed,
  badge,
}: SidebarLinkProps) => {
  const pathname = usePathname();
  const { authorized, shouldShowNavRoute, isLoading } = useRBAC();

  const isActive =
    pathname === href || (href !== "/" && pathname.startsWith(href));

  // If entity is specified, check permissions
  if (entity && !isLoading && !shouldShowNavRoute(entity)) {
    return null; // Hide the link if user doesn't have view permission
  }

  if (!authorized) return <></>;

  // Show loading state for protected routes
  if (entity && isLoading) {
    return (
      <div
        className={cn(
          "flex items-center gap-3 p-3 mb-4 text-sm rounded-md animate-pulse bg-zinc-800",
          collapsed ? "justify-center" : ""
        )}
      />
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.2 }}
    >
      <Link
        href={href}
        className={cn(
          "flex items-center gap-3 px-3 py-2.5 text-sm rounded-md transition-colors duration-150 relative",
          isActive
            ? "bg-sidebar-active-bg text-sidebar-text"
            : "text-sidebar-text-muted hover:text-sidebar-text hover:bg-sidebar-hover-bg",
          collapsed ? "justify-center" : ""
        )}
      >
        {icon}
        <AnimatePresence>
          {!collapsed && (
            <motion.span
              initial={{ opacity: 0, width: 0 }}
              animate={{ opacity: 1, width: "auto" }}
              exit={{ opacity: 0, width: 0 }}
              transition={{ duration: 0.2 }}
              className="whitespace-nowrap flex-1"
            >
              {label}
            </motion.span>
          )}
        </AnimatePresence>

        {/* Notification Badge */}
        {badge && badge > 0 && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className={cn(
              "flex items-center justify-center min-w-[20px] h-5 rounded-full bg-red-500 text-white text-xs font-medium",
              collapsed ? "absolute -top-1 -right-1" : ""
            )}
          >
            {badge > 99 ? "99+" : badge}
          </motion.div>
        )}
      </Link>
    </motion.div>
  );
};

export default function ProtectedSidebar() {
  const [collapsed, setCollapsed] = useState(false);
  const { user, signOut: handleSignOut } = useAuth();
  const { unreadCount } = useRealtimeNotifications();

  // Calculate unread notifications count

  const renderNavigationSection = (items: NavigationItem[], title?: string) => {
    const visibleItems = items.filter((item) => {
      // Always show items that don't require entity permissions
      if (!item.entity) return true;
      // For items with entities, they'll be filtered by the ProtectedSidebarLink component
      return true;
    });

    if (visibleItems.length === 0) return null;

    return (
      <div className="pt-3 mt-3 border-t border-neutral-800/50">
        {title && !collapsed && (
          <div className="px-3 mb-2">
            <span className="text-xs font-medium text-sidebar-text-muted uppercase tracking-wider">
              {title}
            </span>
          </div>
        )}
        {visibleItems.map((item) => (
          <ProtectedSidebarLink
            key={item.href}
            href={item.href}
            icon={item.icon}
            label={item.label}
            collapsed={collapsed}
            entity={item.entity}
            badge={item.href === "/notifications" ? unreadCount : undefined}
          />
        ))}
      </div>
    );
  };

  return (
    <motion.aside
      className={cn(
        "bg-sidebar-bg border-r border-sidebar-border flex flex-col h-screen transition-all duration-300 ease-in-out",
        collapsed ? "w-16" : "w-64"
      )}
      initial={{ width: collapsed ? 64 : 256 }}
      animate={{ width: collapsed ? 64 : 256 }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-neutral-800/50">
        <AnimatePresence>
          {!collapsed && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.2 }}
              className="flex items-center gap-3"
            >
              <Image
                src="/shamwaa-logo-white.png"
                alt="Shamwaa Logistics"
                width={100}
                height={100}
                className="rounded-lg"
              />
            </motion.div>
          )}
        </AnimatePresence>
        <motion.button
          onClick={() => setCollapsed(!collapsed)}
          className="p-1.5 rounded-md hover:bg-sidebar-hover-bg text-sidebar-text-muted hover:text-sidebar-text transition-colors"
          aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
        >
          {collapsed ? <ChevronRight size={18} /> : <ChevronLeft size={18} />}
        </motion.button>
      </div>

      {/* Navigation */}
      <motion.nav
        className="flex-1 overflow-y-auto px-2 py-4 space-y-1"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.2, delay: 0.1 }}
      >
        {/* Core Navigation */}
        <ProtectedSidebarLink
          href="/dashboard"
          icon={<Home size={18} />}
          label="Home"
          collapsed={collapsed}
        />

        {renderNavigationSection(
          navigationItems.filter((item) =>
            ["notifications"].includes(item.href.slice(1))
          )
        )}

        <ProtectedSidebarLink
          href="/task-management"
          icon={<CheckSquare size={18} />}
          label="Task Management"
          collapsed={collapsed}
        />

        {/* Operations Section */}
        {renderNavigationSection(
          navigationItems.filter((item) =>
            [
              "batch-management",
              "cargo-management",
              "shipping-management",
            ].includes(item.href.slice(1))
          )
        )}

        {/* Finance Section */}
        {renderNavigationSection(
          navigationItems.filter((item) =>
            ["finance", "invoice-workflow", "release-authorization"].includes(
              item.href.slice(1)
            )
          )
        )}

        {/* Customer & Analytics Section */}
        {renderNavigationSection(
          navigationItems.filter((item) =>
            ["customer-relations", "broadcast-messages"].includes(
              item.href.slice(1)
            )
          )
        )}

        {/* Admin Section */}
        {renderNavigationSection(
          navigationItems.filter((item) =>
            ["manage-staff", "settings"].includes(item.href.slice(1))
          )
        )}
      </motion.nav>

      {/* User Profile & Sign Out */}
      <div className="border-t border-neutral-800/50 p-4">
        {user && (
          <div className="flex items-center gap-3 mb-3">
            <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center">
              <UserCircle size={16} className="text-primary" />
            </div>
            <AnimatePresence>
              {!collapsed && (
                <motion.div
                  initial={{ opacity: 0, width: 0 }}
                  animate={{ opacity: 1, width: "auto" }}
                  exit={{ opacity: 0, width: 0 }}
                  transition={{ duration: 0.2 }}
                  className="flex-1 min-w-0"
                >
                  <p className="text-sm font-medium text-sidebar-text truncate">
                    {user.name}
                  </p>
                  <p className="text-xs text-sidebar-text-muted truncate">
                    {user.role?.name || "No Role"}
                  </p>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        )}
        <motion.button
          onClick={() => {
            handleSignOut();
            redirect("/");
          }}
          className={cn(
            "flex items-center gap-3 w-full px-3 py-2 text-sm rounded-md transition-colors duration-150",
            "text-sidebar-text-muted hover:text-sidebar-text hover:bg-sidebar-hover-bg",
            collapsed ? "justify-center" : ""
          )}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <LogOut size={18} />
          <AnimatePresence>
            {!collapsed && (
              <motion.span
                initial={{ opacity: 0, width: 0 }}
                animate={{ opacity: 1, width: "auto" }}
                exit={{ opacity: 0, width: 0 }}
                transition={{ duration: 0.2 }}
                className="whitespace-nowrap"
              >
                Sign Out
              </motion.span>
            )}
          </AnimatePresence>
        </motion.button>
      </div>
    </motion.aside>
  );
}

// Navigation items with their corresponding entities for permission checking
const navigationItems: NavigationItem[] = [
  {
    href: "/dashboard",
    icon: <Home size={18} />,
    label: "Home",
    requiresAuth: true,
  },
  {
    href: "/notifications",
    icon: <Bell size={18} />,
    label: "Notifications",
    entity: "notifications",
  },
  {
    href: "/task-management",
    icon: <CheckSquare size={18} />,
    label: "Task Management",
    entity: "tasks",
  },
  // Operations Section
  {
    href: "/batch-management",
    icon: <Container size={18} />,
    label: "Batch Management",
    entity: "batches",
  },
  {
    href: "/cargo-management",
    icon: <Package size={18} />,
    label: "Cargo Management",
    entity: "cargo",
  },
  {
    href: "/shipping-management",
    icon: <Ship size={18} />,
    label: "Shipping Management",
    entity: "shipments",
  },
  // Finance Section
  {
    href: "/finance",
    icon: <BarChart3 size={18} />,
    label: "Finance",
    entity: "ledgers",
  },
  {
    href: "/invoice-workflow",
    icon: <FileText size={18} />,
    label: "Invoice Workflow",
    entity: "documents",
  },
  {
    href: "/release-authorization",
    icon: <Unlock size={18} />,
    label: "Release Authorization",
    entity: "handovers",
  },
  // Customer & Analytics Section
  {
    href: "/customer-relations",
    icon: <Users size={18} />,
    label: "Customer Relations",
    entity: "customers",
  },
  {
    href: "/broadcast-messages",
    icon: <MessageSquare size={18} />,
    label: "Broadcast Messages",
    entity: "notifications",
  },
  {
    href: "/analytics",
    icon: <PieChart size={18} />,
    label: "Analytics",
    entity: "logs",
  },
  // Admin Section
  {
    href: "/manage-staff",
    icon: <UserCog size={18} />,
    label: "Manage Staff",
    entity: "users",
  },
  {
    href: "/my-account",
    icon: <UserCircle size={18} />,
    label: "My Account",
    requiresAuth: true,
  },
  {
    href: "/settings",
    icon: <Settings size={18} />,
    label: "Settings",
    requiresAuth: true,
  },
];
