"use client";

import React from "react";
import { motion } from "framer-motion";
import { cn } from "@workspace/ui/lib/utils";

interface AnimatedCardProps {
  children: React.ReactNode;
  className?: string;
  delay?: number;
  hoverEffect?: boolean;
}

export function AnimatedCard({
  children,
  className,
  delay = 0,
  hoverEffect = true,
}: AnimatedCardProps) {
  return (
    <motion.div
      className={cn(
        "rounded-lg border border-neutral-200 dark:border-neutral-200 bg-card text-card-foreground shadow-sm p-4",
        className
      )}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        type: "spring",
        stiffness: 300,
        damping: 30,
        delay: delay * 0.1,
      }}
    >
      {children}
    </motion.div>
  );
}

// Grid container for multiple cards with staggered animation
export function AnimatedCardGrid({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <motion.div
      className={cn("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4", className)}
      initial="hidden"
      animate="visible"
      variants={{
        visible: {
          transition: {
            staggerChildren: 0.1,
          },
        },
      }}
    >
      {React.Children.map(children, (child, index) => {
        if (React.isValidElement(child)) {
          return React.cloneElement(child as React.ReactElement<AnimatedCardProps>, {
            delay: index,
          });
        }
        return child;
      })}
    </motion.div>
  );
} 