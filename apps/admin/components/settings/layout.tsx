"use client"

import { usePathname } from "next/navigation"
import Link from "next/link"
import { Search } from "lucide-react"

import { cn } from "@workspace/ui/lib/utils"
import { Input } from "@workspace/ui/components/input"

interface SettingsLayoutProps {
  children: React.ReactNode
}

export function SettingsLayout({ children }: SettingsLayoutProps) {
  const pathname = usePathname()
  
  const navItems = [
    { name: "Profile", href: "/settings/profile" },
    { name: "Security", href: "/settings/security" },
    { name: "Notifications", href: "/settings/notifications" },
    { name: "Company", href: "/settings/company" },
    { name: "System", href: "/settings/system" },
  ]

  return (
    <div className="settings-container">
      <div className="settings-header">
        <h1 className="settings-title">Settings</h1>
        <div className="settings-search">
          <Search className="settings-search-icon" size={18} />
          <Input 
            className="settings-search-input" 
            placeholder="Search" 
          />
        </div>
      </div>
      
      <div className="settings-layout">
        <div className="settings-nav">
          <nav className="settings-nav-list">
            {navItems.map((item) => {
              const isActive = pathname.includes(item.href)
              
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    "settings-nav-item",
                    isActive && "settings-nav-item-active"
                  )}
                >
                  {item.name}
                </Link>
              )
            })}
          </nav>
        </div>
        
        <div className="settings-content">
          {children}
        </div>
      </div>
    </div>
  )
}