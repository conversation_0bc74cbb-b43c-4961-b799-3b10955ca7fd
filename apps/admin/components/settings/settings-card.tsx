import { Button } from "@workspace/ui/components/button";

interface SettingsCardProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  actions?: boolean;
}

export function SettingsCard({
  title,
  description,
  children,
  actions = true,
}: SettingsCardProps) {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-medium">{title}</h2>
        {description && (
          <p className="text-sm text-neutral-500 mt-1">{description}</p>
        )}
      </div>

      {children}

      {actions && (
        <div className="flex justify-end gap-3 pt-4">
          <Button variant="outline">Discard</Button>
          <Button className="bg-red-500 hover:bg-red-600">Apply Changes</Button>
        </div>
      )}
    </div>
  );
}
