import { cn } from "@workspace/ui/lib/utils";

interface StatusBadgeProps {
  status: string;
  className?: string;
}

export const StatusBadge = ({ status, className }: StatusBadgeProps) => {
  const statusStyles: { [key: string]: string } = {
    "In Transit": "bg-blue-100 text-blue-700 border border-blue-200",
    Delivered: "bg-green-100 text-green-700 border border-green-200",
    Processing: "bg-yellow-100 text-yellow-700 border border-yellow-200",
    Pending: "bg-gray-100 text-gray-700 border border-gray-200",
    // Add other statuses if needed
  };

  return (
    <span
      className={cn(
        "inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-medium",
        statusStyles[status] || statusStyles["Pending"], // Default style
        className
      )}
    >
      {status}
    </span>
  );
};
