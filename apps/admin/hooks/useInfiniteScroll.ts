import { useState, useCallback, useRef } from "react";
import { Paginator, type PaginatorConfig } from "@/lib/paginator";

interface UseInfiniteScrollConfig<T> {
  initialData?: T[];
  pageSize?: number;
  fetchData: (page: number, pageSize: number) => Promise<{
    data: T[];
    totalItems: number;
    totalPages: number;
    currentPage: number;
    hasNextPage: boolean;
  }>;
  onError?: (error: Error) => void;
}

interface UseInfiniteScrollReturn<T> {
  data: T[];
  loading: boolean;
  error: Error | null;
  hasMore: boolean;
  loadMore: () => Promise<void>;
  refresh: () => Promise<void>;
  paginator: Paginator;
  totalItems: number;
  currentPage: number;
}

export function useInfiniteScroll<T>({
  initialData = [],
  pageSize = 20,
  fetchData,
  onError,
}: UseInfiniteScrollConfig<T>): UseInfiniteScrollReturn<T> {
  const [data, setData] = useState<T[]>(initialData);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [hasNextPage, setHasNextPage] = useState(true);
  
  const isInitialLoad = useRef(true);
  const isLoadingMore = useRef(false);

  const loadMore = useCallback(async () => {
    if (isLoadingMore.current || !hasNextPage || loading) {
      return;
    }

    isLoadingMore.current = true;
    setLoading(true);
    setError(null);

    try {
      const nextPage = isInitialLoad.current ? 1 : currentPage + 1;
      const response = await fetchData(nextPage, pageSize);

      if (isInitialLoad.current) {
        // Initial load - replace data
        setData(response.data);
        isInitialLoad.current = false;
      } else {
        // Load more - append data
        setData(prev => [...prev, ...response.data]);
      }

      setCurrentPage(response.currentPage);
      setTotalPages(response.totalPages);
      setTotalItems(response.totalItems);
      setHasNextPage(response.hasNextPage);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to load data');
      setError(error);
      if (onError) {
        onError(error);
      }
    } finally {
      setLoading(false);
      isLoadingMore.current = false;
    }
  }, [currentPage, hasNextPage, loading, fetchData, pageSize, onError]);

  const refresh = useCallback(async () => {
    setData([]);
    setCurrentPage(1);
    setTotalPages(1);
    setTotalItems(0);
    setHasNextPage(true);
    setError(null);
    isInitialLoad.current = true;
    isLoadingMore.current = false;
    
    await loadMore();
  }, [loadMore]);

  // Create paginator instance
  const paginator = new Paginator({
    currentPage,
    totalPages,
    totalItems,
    itemsPerPage: pageSize,
    onPageChange: () => {}, // Not used for infinite scroll
    onLoadMore: loadMore,
    hasNextPage,
    isLoading: loading,
  });

  return {
    data,
    loading,
    error,
    hasMore: hasNextPage,
    loadMore,
    refresh,
    paginator,
    totalItems,
    currentPage,
  };
}
