"use client";

import * as React from "react";
import { use<PERSON>emo, useCallback } from "react";
import { cn } from "@workspace/ui/lib/utils";
import { motion } from "framer-motion";
import {
  Loader2,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  LayoutGrid,
  Table as TableIcon,
} from "lucide-react";
import { Button } from "@workspace/ui/components/button";
import { Checkbox } from "@workspace/ui/components/checkbox";
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import {
  FilterPanel,
  type ColumnFilter,
  type TableColumn,
} from "@/components/ui/filter-panel";
import { abbreviateNumber } from "js-abbreviation-number";

import dynamic from "next/dynamic";
const InfiniteScroll = dynamic(
  () => import("react-infinite-scroll-component"),
  {
    ssr: false,
  }
);

import { Paginator, type PaginatorConfig } from "@/lib/paginator";

/**
 * Listing Layout Components
 *
 * A compound composable layout component for creating listing pages with:
 * - Header: Title, caption, and actions
 * - Filters: Filter panel with search and dynamic filtering
 * - Controls: View mode toggle, category chips, and action buttons
 * - Cards: Grid view for card-based display
 * - Table: Table view with pagination and optional checkbox selection
 *
 * Usage:
 * ```tsx
 * import { Listing } from "@/modules/layouts/listing";
 *
 * <Listing className="p-6">
 *   <Listing.Header
 *     title="Items"
 *     caption="Manage your items"
 *     actions={<Button>New Item</Button>}
 *   />
 *
 *   // Table with checkbox selection
 *   <Listing.Table
 *     data={items}
 *     columns={columns}
 *     enableCheckboxes={true}
 *     selectedRowIds={selectedIds}
 *     onSelectionChange={setSelectedIds}
 *     getRowId={(item) => item.id}
 *   />
 * ```
 *
 *   <Listing.Filters
 *     searchTerm={searchTerm}
 *     onSearchChange={setSearchTerm}
 *     onRefresh={handleRefresh}
 *   />
 *
 *   <Listing.Controls
 *     viewMode={viewMode}
 *     onViewModeChange={setViewMode}
 *     categories={categories}
 *   />
 *
 *   {viewMode === "cards" ? (
 *     <Listing.Cards>
 *       {items.map(item => <ItemCard key={item.id} item={item} />)}
 *     </Listing.Cards>
 *   ) : (
 *     <Listing.Table
 *       data={items}
 *       columns={tableColumns}
 *       pagination={paginationConfig}
 *     />
 *   )}
 * </Listing>
 * ```
 */

// Custom Hook for Data Filtering
interface UseDataFilterOptions {
  data: any[];
  searchTerm: string;
  columnFilters: ColumnFilter[];
  searchableColumns?: string[];
  categoryFilter?: string;
  categoryKey?: string;
}

interface FilteredDataResult {
  filteredData: any[];
  totalItems: number;
  appliedFilters: {
    searchTerm: string;
    columnFilters: ColumnFilter[];
    categoryFilter?: string;
  };
}

function useDataFilter({
  data,
  searchTerm,
  columnFilters,
  searchableColumns = [],
  categoryFilter,
  categoryKey,
}: UseDataFilterOptions): FilteredDataResult {
  const filteredData = useMemo(() => {
    let result = [...data];

    // Apply search term filter
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      result = result.filter((item) => {
        // If searchableColumns are specified, search only in those columns
        if (searchableColumns.length > 0) {
          return searchableColumns.some((column) => {
            const value = item[column];
            return (
              value && value.toString().toLowerCase().includes(searchLower)
            );
          });
        }

        // Otherwise, search in all string properties
        return Object.values(item).some((value) => {
          if (typeof value === "string") {
            return value.toLowerCase().includes(searchLower);
          }
          if (typeof value === "number") {
            return value.toString().includes(searchLower);
          }
          return false;
        });
      });
    }

    // Apply column filters (with safety check)
    if (Array.isArray(columnFilters)) {
      columnFilters.forEach((filter) => {
        result = result.filter((item) => {
          const value = item[filter.column];
          if (value === null || value === undefined) return false;

          const valueStr = value.toString().toLowerCase();
          const filterStr = filter.value.toLowerCase();

          return valueStr.includes(filterStr);
        });
      });
    }

    // Apply category filter
    if (categoryFilter && categoryFilter !== "all" && categoryKey) {
      result = result.filter((item) => {
        const itemCategory = item[categoryKey];
        return (
          itemCategory &&
          itemCategory.toLowerCase() === categoryFilter.toLowerCase()
        );
      });
    }

    return result;
  }, [
    data,
    searchTerm,
    columnFilters,
    searchableColumns,
    categoryFilter,
    categoryKey,
  ]);

  const appliedFilters = useMemo(
    () => ({
      searchTerm,
      columnFilters,
      categoryFilter,
    }),
    [searchTerm, columnFilters, categoryFilter]
  );

  return useMemo(
    () => ({
      filteredData,
      totalItems: filteredData.length,
      appliedFilters,
    }),
    [filteredData, appliedFilters]
  );
}

// Enhanced Listing Component with Filtering
interface FilterableListingProps {
  children: React.ReactNode;
  className?: string;
  gap?: "gap-4" | "gap-6" | "gap-8" | "gap-12";
  data: any[];
  searchableColumns?: string[];
  categoryFilter?: string;
  categoryKey?: string;
  onDataFiltered?: (filteredData: FilteredDataResult) => void;
}

function FilterableListing({
  children,
  className,
  gap = "gap-6",
  data,
  searchableColumns,
  categoryFilter,
  categoryKey,
  onDataFiltered,
}: FilterableListingProps) {
  const [searchTerm, setSearchTerm] = React.useState("");
  const [columnFilters, setColumnFilters] = React.useState<ColumnFilter[]>([]);

  // Apply filtering directly in this component
  const filteredResult = useDataFilter({
    data,
    searchTerm,
    columnFilters,
    searchableColumns,
    categoryFilter,
    categoryKey,
  });

  // Notify parent of changes
  React.useEffect(() => {
    onDataFiltered?.(filteredResult);
  }, [filteredResult, onDataFiltered]);

  const handleFilteredDataChange = useCallback(
    (newFilteredData: FilteredDataResult) => {
      // This is now just for external updates, not internal filtering
      onDataFiltered?.(newFilteredData);
    },
    [onDataFiltered]
  );

  // Provide filtered data to child components through context
  const contextValue = React.useMemo(
    () => ({
      filteredData: filteredResult.filteredData,
      totalItems: filteredResult.totalItems,
      appliedFilters: filteredResult.appliedFilters,
      originalData: data,
      onFilteredDataChange: handleFilteredDataChange,
      searchableColumns,
      categoryFilter,
      categoryKey,
      searchTerm,
      setSearchTerm,
      columnFilters,
      setColumnFilters,
    }),
    [
      filteredResult,
      data,
      handleFilteredDataChange,
      searchableColumns,
      categoryFilter,
      categoryKey,
      searchTerm,
      columnFilters,
    ]
  );

  return (
    <FilteredDataContext.Provider value={contextValue}>
      <div className={cn("flex flex-col", gap, className)}>{children}</div>
    </FilteredDataContext.Provider>
  );
}

// Context for filtered data
interface FilteredDataContextType {
  filteredData: any[];
  totalItems: number;
  appliedFilters: {
    searchTerm: string;
    columnFilters: ColumnFilter[];
    categoryFilter?: string;
  };
  originalData: any[];
  onFilteredDataChange: (filteredData: FilteredDataResult) => void;
  searchableColumns?: string[];
  categoryFilter?: string;
  categoryKey?: string;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  columnFilters: ColumnFilter[];
  setColumnFilters: (filters: ColumnFilter[]) => void;
}

const FilteredDataContext = React.createContext<FilteredDataContextType | null>(
  null
);

// Hook to use filtered data context
function useFilteredData() {
  const context = React.useContext(FilteredDataContext);
  if (!context) {
    throw new Error(
      "useFilteredData must be used within a FilterableListing component"
    );
  }
  return context;
}

// Types
interface ListingLayoutProps {
  children: React.ReactNode;
  className?: string;
  gap?: "gap-4" | "gap-6" | "gap-8" | "gap-12";
}

interface HeaderProps {
  title: string;
  caption?: string;
  actions?: React.ReactNode;
  className?: string;
}

interface FiltersProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  filters?: any[];
  onRefresh?: () => void;
  loading?: boolean;
  popularTags?: string[];
  onTagSelect?: (tag: string) => void;
  columnFilters?: ColumnFilter[];
  onColumnFilterAdd?: (filter: ColumnFilter) => void;
  onColumnFilterRemove?: (index: number) => void;
  enableDynamicFilters?: boolean;
  columns?: TableColumn[];
  tableData?: any[];
  onValueSearch?: (column: string, query: string) => Promise<string[]>;
  defaultFilterColumn?: string;
  autoSelectDefaultColumn?: boolean;
  enableAutocomplete?: boolean;
  autocompleteLabel?: string;
  autocompletePlaceholder?: string;
  autocompleteOptions?: any[];
  selectedAutocompleteId?: string;
  onAutocompleteSelect?: (option: any) => void;
  autocompleteLoading?: boolean;
  onAutocompleteSearch?: (query: string) => void;
  customActions?: React.ReactNode;
  className?: string;
  // New props for filtering functionality
  searchableColumns?: string[];
  categoryFilter?: string;
  categoryKey?: string;
  // Bulk action support
  bulkActions?: React.ReactNode;
  selectedCount?: number;
  showBulkActions?: boolean;
  onClearSelections?: () => void;
}

interface ControlsProps {
  entity: string;
  length: number;
  children?: React.ReactNode;
  viewMode?: "cards" | "table";
  onViewModeChange?: (mode: "cards" | "table") => void;
  categoryFilter?: string;
  onCategoryFilterChange?: (category: string) => void;
  categories?: Array<{ key: string; label: string; count?: number }>;
  actions?: React.ReactNode;
  className?: string;
}

interface CardsProps {
  children?: React.ReactNode;
  data?: any[];
  categories?: Array<{
    key: string;
    name: string;
    description?: string;
    color?: string;
    icon?: React.ElementType;
  }>;
  renderCard?: (item: any, category?: any) => React.ReactNode;
  onItemClick?: (item: any) => void;
  loading?: boolean;
  emptyState?: React.ReactNode;
  className?: string;
  columns?:
    | "grid-cols-1"
    | "grid-cols-2"
    | "grid-cols-3"
    | "grid-cols-4"
    | "grid-cols-5";
  groupByCategory?: boolean;
  getCategoryKey?: (item: any) => string;
}

interface StatisticsProps {
  children: React.ReactNode;
  className?: string;
  columns?:
    | "grid-cols-1"
    | "grid-cols-2"
    | "grid-cols-3"
    | "grid-cols-4"
    | "grid-cols-5";
}

interface StatCardProps {
  icon: React.ElementType;
  name: string;
  value: string | number;
  valueType?: "number" | "percent" | "dollar";
  caption?: React.ReactNode;
  color?: "primary" | "blue" | "green" | "amber" | "red" | "purple";
  loading?: boolean;
  className?: string;
}

interface TableProps {
  data?: any[];
  columns: Array<{
    key: string;
    label: string;
    render?: (item: any, index: number) => React.ReactNode;
    className?: string;
  }>;
  loading?: boolean;
  emptyState?: React.ReactNode;
  onRowClick?: (item: any) => void;
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    onPageChange: (page: number) => void;
  };
  // Infinite scroll support
  infiniteScroll?: {
    hasMore: boolean;
    loadMore: () => Promise<void>;
    isLoading: boolean;
    threshold?: number;
  };
  paginator?: Paginator;
  className?: string;
  // Checkbox functionality
  enableCheckboxes?: boolean;
  selectedRowIds?: string[];
  onSelectionChange?: (selectedIds: string[]) => void;
  getRowId?: (item: any) => string; // Function to extract ID from item
}

// Main Layout Component
function Layout({ children, className, gap = "gap-6" }: ListingLayoutProps) {
  return <div className={cn("flex flex-col", gap, className)}>{children}</div>;
}

// Header Component
function Header({ title, caption, actions, className }: HeaderProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
      className={cn("flex items-center justify-between", className)}
    >
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold text-foreground">{title}</h1>
        <p className="text-muted-foreground" hidden={!caption}>
          {caption}
        </p>
      </div>
      <div className="flex items-center gap-2" hidden={!actions}>
        {actions}
      </div>
    </motion.div>
  );
}

// Filters Component
function Filters({
  searchTerm,
  onSearchChange,
  filters = [],
  onRefresh,
  loading = false,
  popularTags = [],
  onTagSelect,
  columnFilters = [],
  onColumnFilterAdd,
  onColumnFilterRemove,
  enableDynamicFilters = false,
  columns = [],
  tableData = [],
  onValueSearch,
  defaultFilterColumn = "name",
  autoSelectDefaultColumn = true,
  enableAutocomplete = false,
  autocompleteLabel,
  autocompletePlaceholder,
  autocompleteOptions = [],
  selectedAutocompleteId,
  onAutocompleteSelect,
  autocompleteLoading = false,
  onAutocompleteSearch,
  customActions,
  className,
  // New filtering props
  searchableColumns = [],
  categoryFilter,
  categoryKey,
  // Bulk actions
  bulkActions,
  selectedCount = 0,
  showBulkActions = false,
  onClearSelections,
}: FiltersProps) {
  // Try to use context first, fallback to props
  const context = React.useContext(FilteredDataContext);

  // Use context values if available, otherwise use props
  const effectiveSearchTerm = context?.searchTerm ?? searchTerm;
  const effectiveOnSearchChange = context?.setSearchTerm ?? onSearchChange;
  const effectiveColumnFilters = context?.columnFilters ?? columnFilters ?? [];
  const effectiveTableData = context?.originalData ?? tableData ?? [];

  // For display purposes, calculate filtered result
  const filteredResult = useDataFilter({
    data: effectiveTableData,
    searchTerm: effectiveSearchTerm,
    columnFilters: effectiveColumnFilters,
    searchableColumns: context?.searchableColumns || searchableColumns,
    categoryFilter: context?.categoryFilter || categoryFilter,
    categoryKey: context?.categoryKey || categoryKey,
  });

  return (
    <div className={cn("space-y-4", className)}>
      <FilterPanel
        searchTerm={effectiveSearchTerm}
        onSearchChange={effectiveOnSearchChange}
        filters={filters}
        onRefresh={onRefresh}
        loading={loading}
        popularTags={popularTags}
        onTagSelect={onTagSelect}
        columnFilters={effectiveColumnFilters}
        onColumnFilterAdd={
          context?.setColumnFilters
            ? (filter: ColumnFilter) =>
                context.setColumnFilters([...effectiveColumnFilters, filter])
            : onColumnFilterAdd
        }
        onColumnFilterRemove={
          context?.setColumnFilters
            ? (index: number) =>
                context.setColumnFilters(
                  effectiveColumnFilters.filter((_, i) => i !== index)
                )
            : onColumnFilterRemove
        }
        enableDynamicFilters={enableDynamicFilters}
        columns={columns}
        tableData={effectiveTableData}
        onValueSearch={onValueSearch}
        defaultFilterColumn={defaultFilterColumn}
        autoSelectDefaultColumn={autoSelectDefaultColumn}
        enableAutocomplete={enableAutocomplete}
        autocompleteLabel={autocompleteLabel}
        autocompletePlaceholder={autocompletePlaceholder}
        autocompleteOptions={autocompleteOptions}
        selectedAutocompleteId={selectedAutocompleteId}
        onAutocompleteSelect={onAutocompleteSelect}
        autocompleteLoading={autocompleteLoading}
        onAutocompleteSearch={onAutocompleteSearch}
        customActions={customActions}
        bulkActions={bulkActions}
        selectedCount={selectedCount}
        showBulkActions={showBulkActions}
        onClearSelections={onClearSelections}
      />

      {/* Filter Summary */}
      <div
        className="text-sm text-muted-foreground"
        hidden={
          !filteredResult.appliedFilters.searchTerm &&
          !(
            Array.isArray(filteredResult.appliedFilters.columnFilters) &&
            filteredResult.appliedFilters.columnFilters.length
          )
        }
      >
        <span>
          Showing {filteredResult.totalItems} of {effectiveTableData.length}{" "}
          results
          <span hidden={!filteredResult.appliedFilters.searchTerm}>
            {" "}
            for "{filteredResult.appliedFilters.searchTerm}"
          </span>
          <span
            hidden={
              !(
                Array.isArray(filteredResult.appliedFilters.columnFilters) &&
                filteredResult.appliedFilters.columnFilters.length
              )
            }
          >
            {" "}
            with{" "}
            {Array.isArray(filteredResult.appliedFilters.columnFilters)
              ? filteredResult.appliedFilters.columnFilters.length
              : 0}{" "}
            filter
            {Array.isArray(filteredResult.appliedFilters.columnFilters) &&
            filteredResult.appliedFilters.columnFilters.length !== 1
              ? "s"
              : ""}
          </span>
        </span>
      </div>
    </div>
  );
}

// Controls Component
function Controls({
  entity = "Title",
  length = 0,
  children,
  viewMode = "table",
  onViewModeChange,
  categoryFilter = "all",
  onCategoryFilterChange,
  categories = [],
  actions,
  className,
}: ControlsProps) {
  return (
    <div className="space-y-6">
      <div
        className={cn(
          "flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",
          className
        )}
      >
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-medium text-gray-900 capitalize">
            {entity} Management
          </h3>
          <span className="bg-primary/10 text-primary text-xs px-2 py-1 rounded-full">
            {length} {entity}
          </span>
        </div>

        <div className="flex items-center gap-2">
          {/* View Mode Toggle */}
          <div
            className="flex border border-gray-200 rounded-lg overflow-hidden"
            hidden={!onViewModeChange}
          >
            <Button
              variant={viewMode === "table" ? "default" : "outline"}
              onClick={() => onViewModeChange?.("table")}
              className="rounded-none rounded-r-lg"
            >
              <TableIcon className="h-4 w-4" />
              Table
            </Button>
            <Button
              onClick={() => onViewModeChange?.("cards")}
              variant={viewMode === "cards" ? "default" : "outline"}
              className="rounded-none rounded-l-lg"
            >
              <LayoutGrid className="h-4 w-4" />
              Cards
            </Button>
          </div>

          {/* Action Buttons */}
          {actions}
        </div>
      </div>
      <div className="flex flex-wrap items-center gap-2">
        {/* Category Filter Pills */}
        <div className="flex flex-wrap gap-2" hidden={categories.length === 0}>
          <Button
            variant={categoryFilter === "all" ? "default" : "outline"}
            size="sm"
            onClick={() => onCategoryFilterChange?.("all")}
            className="gap-2"
          >
            <div
              className="w-2 h-2 rounded-full"
              style={{
                backgroundColor: categoryFilter === "all" ? "#fff" : "#6b7280",
              }}
            />
            All
          </Button>
          {categories.map((category) => (
            <Button
              key={category.key}
              variant={categoryFilter === category.key ? "default" : "outline"}
              size="sm"
              onClick={() =>
                onCategoryFilterChange?.(category.key.toLowerCase())
              }
              className="gap-2 capitalize"
            >
              <div
                className="w-2 h-2 rounded-full"
                style={{
                  backgroundColor:
                    categoryFilter === category.key ? "#fff" : "#6b7280",
                }}
              />
              {category.label}
            </Button>
          ))}
        </div>

        {children}
      </div>
    </div>
  );
}

// Cards Component
function Cards({
  children,
  data = [],
  categories = [],
  renderCard,
  onItemClick,
  loading = false,
  emptyState,
  className,
  columns = "grid-cols-1",
  groupByCategory = false,
  getCategoryKey,
}: CardsProps) {
  // Try to use filtered data from context, fallback to props
  const context = React.useContext(FilteredDataContext);
  const dataToUse = context?.filteredData || data;

  // Animation variants for cards
  const cardContainerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 2,
        delayChildren: 4,
      },
    },
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 2,
        ease: "easeOut",
      },
    },
  };

  const cardHoverVariants = {
    hover: {
      scale: 1.03,
      y: -5,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
    tap: {
      scale: 0.97,
      transition: {
        duration: 0.4,
      },
    },
  };
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // If using children (legacy mode)
  if (children && !dataToUse.length) {
    if (!children && emptyState) {
      return <div className="py-12">{emptyState}</div>;
    }

    return (
      <motion.div
        variants={cardContainerVariants}
        initial="hidden"
        animate="visible"
        className={cn(
          "grid gap-4",
          columns,
          "md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",
          className
        )}
      >
        {React.Children.map(children, (child, index) => (
          <motion.div
            key={index}
            variants={cardVariants}
            whileHover="hover"
            whileTap="tap"
            {...cardHoverVariants}
          >
            {child}
          </motion.div>
        ))}
      </motion.div>
    );
  }

  // Data-driven mode with optional category grouping
  if (dataToUse.length === 0 && emptyState) {
    return <div className="py-12">{emptyState}</div>;
  }

  if (groupByCategory && categories.length > 0 && getCategoryKey) {
    // Group items by category
    const itemsByCategory = categories.map((category) => ({
      category,
      items: dataToUse.filter((item) => getCategoryKey(item) === category.key),
    }));

    return (
      <div className={cn("space-y-8", className)}>
        {itemsByCategory.map(({ category, items }) => (
          <div key={category.key} hidden={items.length === 0}>
            <div className="flex items-center gap-3 mb-4">
              <div
                className="p-2 rounded-lg"
                style={{
                  backgroundColor: category.color
                    ? `${category.color}20`
                    : "#f3f4f6",
                  color: category.color || "#6b7280",
                }}
              >
                <div hidden={!category.icon}>
                  {category.icon &&
                    React.createElement(category.icon, {
                      className: "h-4 w-4",
                    })}
                </div>
                <div className="h-4 w-4" hidden={!!category.icon} />
              </div>
              <div>
                <h4 className="font-medium text-gray-900">{category.name}</h4>
                <p
                  className="text-sm text-gray-500"
                  hidden={!category.description}
                >
                  {category.description}
                </p>
              </div>
              <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">
                {items.length}
              </span>
            </div>

            <motion.div
              variants={cardContainerVariants}
              initial="hidden"
              animate="visible"
              className={cn(
                "grid gap-4",
                columns,
                "md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
              )}
            >
              {items.map((item, index) => (
                <motion.div
                  key={item.id || index}
                  variants={cardVariants}
                  whileHover="hover"
                  whileTap="tap"
                  {...cardHoverVariants}
                  className="cursor-pointer"
                  onClick={() => onItemClick?.(item)}
                >
                  <div hidden={!renderCard}>{renderCard?.(item, category)}</div>
                  <div
                    className="p-4 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-lg transition-all duration-200"
                    hidden={!!renderCard}
                  >
                    <pre className="text-xs">
                      {JSON.stringify(item, null, 2)}
                    </pre>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        ))}
      </div>
    );
  }

  // Simple grid layout without categories
  return (
    <motion.div
      variants={cardContainerVariants}
      initial="hidden"
      animate="visible"
      className={cn(
        "grid gap-4",
        columns,
        "md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",
        className
      )}
    >
      {dataToUse.map((item, index) => (
        <motion.div
          key={item.id || index}
          variants={cardVariants}
          whileHover="hover"
          whileTap="tap"
          {...cardHoverVariants}
          className="cursor-pointer"
          onClick={() => onItemClick?.(item)}
        >
          <div hidden={!renderCard}>{renderCard?.(item)}</div>
          <div
            className="p-4 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-lg transition-all duration-200"
            hidden={!!renderCard}
          >
            <pre className="text-xs">{JSON.stringify(item, null, 2)}</pre>
          </div>
        </motion.div>
      ))}
    </motion.div>
  );
}

// Statistics Component
function Statistics({
  children,
  className,
  columns = "grid-cols-5",
}: StatisticsProps) {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.4,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, x: -30 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className={cn(
        "grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 2xl:",
        columns,
        className
      )}
    >
      {React.Children.map(children, (child, index) => (
        <motion.div key={index} variants={itemVariants}>
          {child}
        </motion.div>
      ))}
    </motion.div>
  );
}

// StatCard Component (Finance Page Style)
function StatCard({
  icon: Icon,
  name,
  value,
  valueType = "number",
  caption,
  color = "primary",
  loading = false,
  className,
}: StatCardProps) {
  const colorClasses: Record<string, string> = {
    primary: "bg-primary/10 text-primary",
    blue: "bg-blue-500/10 text-blue-500",
    green: "bg-green-500/10 text-green-500",
    amber: "bg-amber-500/10 text-amber-500",
    red: "bg-red-500/10 text-red-500",
    purple: "bg-purple-500/10 text-purple-500",
  };

  const formatValue = (val: any | number, type: string) => {
    if (loading) return "...";

    let valueToFormat: number = parseFloat(val);
    let formattedValue: any = abbreviateNumber(valueToFormat.toFixed(2), 2);

    switch (type) {
      case "percent":
        return formattedValue + "%";
      case "dollar":
        return "$" + formattedValue;
      default:
        return formattedValue;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn(
        "flex items-center gap-4 bg-background border border-gray-200 rounded-xl p-4 shadow-sm",
        className
      )}
    >
      <div className={cn("p-3 rounded-full", colorClasses[color])}>
        <div hidden={!loading}>
          <Loader2 className="h-6 w-6 animate-spin" />
        </div>
        <div hidden={loading}>
          <Icon className="h-6 w-6" />
        </div>
      </div>
      <div>
        <p className="text-sm text-muted-foreground">{name}</p>
        <h3 className="text-2xl font-bold">
          <div hidden={!loading}>
            <Loader2 size={20} className="animate-spin" />
          </div>
          <span hidden={loading}>{formatValue(value, valueType)}</span>
        </h3>
        <div className="text-xs flex items-center gap-1 mt-1" hidden={!caption}>
          {caption}
        </div>
      </div>
    </motion.div>
  );
}

// Table Component with Pagination
function TableView({
  data = [],
  columns,
  loading = false,
  emptyState,
  onRowClick,
  pagination,
  infiniteScroll,
  paginator,
  className,
  enableCheckboxes = false,
  selectedRowIds = [],
  onSelectionChange,
  getRowId = (item) => item.id,
}: TableProps) {
  // Try to use filtered data from context, fallback to props
  const context = React.useContext(FilteredDataContext);
  const dataToUse = context?.filteredData || data || [];

  // Determine if we're using infinite scroll or traditional pagination
  const useInfiniteScroll = Boolean(
    infiniteScroll || paginator?.getInfiniteScrollConfig()
  );

  const paginatedData = useMemo(() => {
    if (useInfiniteScroll) {
      // For infinite scroll, show all loaded data
      return dataToUse;
    }
    if (!pagination) return dataToUse;
    const start = (pagination.currentPage - 1) * pagination.itemsPerPage;
    const end = start + pagination.itemsPerPage;
    return dataToUse.slice(start, end);
  }, [dataToUse, pagination, useInfiniteScroll]);

  // Checkbox functionality
  const allRowIds = useMemo(() => {
    return paginatedData.map(getRowId);
  }, [paginatedData, getRowId]);

  const isAllSelected = useMemo(() => {
    return (
      allRowIds.length > 0 &&
      allRowIds.every((id) => selectedRowIds.includes(id))
    );
  }, [allRowIds, selectedRowIds]);

  const isIndeterminate = useMemo(() => {
    return (
      (allRowIds.some((id) => selectedRowIds.includes(id)) && !isAllSelected) ??
      false
    );
  }, [allRowIds, selectedRowIds, isAllSelected]);

  const handleSelectAll = useCallback(() => {
    if (!onSelectionChange) return;

    if (isAllSelected) {
      // Deselect all current page items
      const newSelection = selectedRowIds.filter(
        (id) => !allRowIds.includes(id)
      );
      onSelectionChange(newSelection);
    } else {
      // Select all current page items
      const newSelection = [...new Set([...selectedRowIds, ...allRowIds])];
      onSelectionChange(newSelection);
    }
  }, [isAllSelected, selectedRowIds, allRowIds, onSelectionChange]);

  const handleRowSelect = useCallback(
    (rowId: string) => {
      if (!onSelectionChange) return;

      if (selectedRowIds.includes(rowId)) {
        onSelectionChange(selectedRowIds.filter((id) => id !== rowId));
      } else {
        onSelectionChange([...selectedRowIds, rowId]);
      }
    },
    [selectedRowIds, onSelectionChange]
  );

  const handleRowClick = useCallback(
    (item: any, event: React.MouseEvent) => {
      // Prevent row click when clicking on checkbox
      if ((event.target as HTMLElement).closest('[role="checkbox"]')) {
        return;
      }
      onRowClick?.(item);
    },
    [onRowClick]
  );

  if (loading) {
    return (
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
        <div className="flex items-center justify-center py-12">
          <div className="text-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto" />
            <p className="text-gray-600">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  if (dataToUse.length === 0 && emptyState) {
    return (
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
        <div className="py-12">{emptyState}</div>
      </div>
    );
  }

  const tableVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut",
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const rowVariants = {
    hidden: { opacity: 0, y: 15 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  return (
    <motion.div
      variants={tableVariants}
      initial="hidden"
      animate="visible"
      className={cn(
        "bg-white border border-gray-200 rounded-lg overflow-hidden p-4",
        className
      )}
    >
      <Table className="overflow-auto">
        <TableHeader>
          <TableRow className="bg-gray-50/50">
            {enableCheckboxes && (
              <TableHead className="font-semibold text-gray-900 w-12">
                <Checkbox
                  checked={isAllSelected}
                  // @ts-ignore - Radix checkbox supports indeterminate
                  indeterminate={isIndeterminate.toString()}
                  onCheckedChange={handleSelectAll}
                  aria-label="Select all rows"
                  className="translate-y-[2px]"
                />
              </TableHead>
            )}
            <TableHead className="font-semibold text-gray-900 w-16">
              #
            </TableHead>
            {columns.map((column) => (
              <TableHead
                key={column.key}
                className={cn("font-semibold text-gray-900", column.className)}
              >
                {column.label}
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {useInfiniteScroll ? (
            <InfiniteScroll
              dataLength={paginatedData.length}
              hasMore={infiniteScroll?.hasMore ?? paginator?.hasMore() ?? false}
              next={
                infiniteScroll?.loadMore ??
                paginator?.loadMore.bind(paginator) ??
                (() => Promise.resolve())
              }
              loader={
                <tr>
                  <td
                    colSpan={columns.length + (enableCheckboxes ? 2 : 1)}
                    className="text-center py-4"
                  >
                    <Loader2 className="h-6 w-6 animate-spin text-primary mx-auto" />
                    <p className="text-sm text-gray-500 mt-2">
                      Loading more...
                    </p>
                  </td>
                </tr>
              }
              endMessage={
                <tr>
                  <td
                    colSpan={columns.length + (enableCheckboxes ? 2 : 1)}
                    className="text-center py-4"
                  >
                    <p className="text-sm text-gray-500">
                      No more items to load
                    </p>
                  </td>
                </tr>
              }
              className="w-full"
              style={{ overflow: "visible" }}
            >
              {paginatedData.map((item, index) => {
                const rowId = getRowId(item);
                const isSelected = selectedRowIds.includes(rowId);

                return (
                  <motion.tr
                    key={item.id || index}
                    variants={rowVariants}
                    className={cn(
                      "hover:bg-gray-50/50 transition-colors border-b px-4 py-6",
                      onRowClick && "cursor-pointer",
                      isSelected && "bg-blue-50/50"
                    )}
                    onClick={(event) => handleRowClick(item, event)}
                  >
                    {enableCheckboxes && (
                      <td className="px-2 py-4">
                        <Checkbox
                          checked={isSelected}
                          onCheckedChange={() => handleRowSelect(rowId)}
                          aria-label={`Select row ${index + 1}`}
                          className="translate-y-[2px]"
                        />
                      </td>
                    )}
                    <td className="text-sm text-gray-500 font-mono px-2 py-4">
                      {index + 1}
                    </td>
                    {columns.map((column) => (
                      <td
                        key={column.key}
                        className={cn("px-2 py-4", column.className)}
                      >
                        {column.render
                          ? column.render(item, index)
                          : item[column.key]}
                      </td>
                    ))}
                  </motion.tr>
                );
              })}
            </InfiniteScroll>
          ) : (
            // Traditional table rows for regular pagination
            paginatedData.map((item, index) => {
              const rowId = getRowId(item);
              const isSelected = selectedRowIds.includes(rowId);

              return (
                <motion.tr
                  key={item.id || index}
                  variants={rowVariants}
                  className={cn(
                    "hover:bg-gray-50/50 transition-colors border-b px-4 py-6",
                    onRowClick && "cursor-pointer",
                    isSelected && "bg-blue-50/50"
                  )}
                  onClick={(event) => handleRowClick(item, event)}
                >
                  {enableCheckboxes && (
                    <td className="px-2 py-4">
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={() => handleRowSelect(rowId)}
                        aria-label={`Select row ${index + 1}`}
                        className="translate-y-[2px]"
                      />
                    </td>
                  )}
                  <td className="text-sm text-gray-500 font-mono px-2 py-4">
                    <span hidden={!pagination}>
                      {pagination &&
                        (pagination.currentPage - 1) * pagination.itemsPerPage +
                          index +
                          1}
                    </span>
                    <span hidden={!!pagination}>{index + 1}</span>
                  </td>
                  {columns.map((column) => (
                    <td
                      key={column.key}
                      className={cn("px-2 py-4", column.className)}
                    >
                      {column.render
                        ? column.render(item, index)
                        : item[column.key]}
                    </td>
                  ))}
                </motion.tr>
              );
            })
          )}
        </TableBody>
      </Table>

      {/* Pagination - Hidden when using infinite scroll */}
      <div
        className="flex items-center justify-between px-6 py-4 border-t border-gray-200"
        hidden={useInfiniteScroll || !pagination || pagination.totalPages <= 1}
      >
        <div className="text-sm text-gray-500">
          Showing {(pagination!.currentPage - 1) * pagination!.itemsPerPage + 1}{" "}
          to{" "}
          {Math.min(
            pagination!.currentPage * pagination!.itemsPerPage,
            pagination!.totalItems
          )}{" "}
          of {pagination!.totalItems} results
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => pagination!.onPageChange(1)}
            disabled={pagination!.currentPage === 1}
            className="gap-1"
          >
            <ChevronsLeft size={16} />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() =>
              pagination!.onPageChange(pagination!.currentPage - 1)
            }
            disabled={pagination!.currentPage === 1}
            className="gap-1"
          >
            <ChevronLeft size={16} />
            Previous
          </Button>
          <span className="px-3 py-2 text-sm text-gray-900">
            Page {pagination!.currentPage} of {pagination!.totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() =>
              pagination!.onPageChange(pagination!.currentPage + 1)
            }
            disabled={pagination!.currentPage === pagination!.totalPages}
            className="gap-1"
          >
            Next
            <ChevronRight size={16} />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => pagination!.onPageChange(pagination!.totalPages)}
            disabled={pagination!.currentPage === pagination!.totalPages}
            className="gap-1"
          >
            <ChevronsRight size={16} />
          </Button>
        </div>
      </div>
    </motion.div>
  );
}

// Export all components
export {
  Layout,
  Header,
  Filters,
  Controls,
  Cards,
  Statistics,
  StatCard,
  TableView,
};

// Export compound component with sub-components
export const Listing = Object.assign(Layout, {
  Header,
  Filters,
  Controls,
  Cards,
  Statistics,
  StatCard,
  Table: TableView,
});

// Export additional components
export { FilterableListing, useFilteredData };
