"use client";

import * as React from "react";
import { cn } from "@workspace/ui/lib/utils";
import { motion } from "framer-motion";

/**
 * Overview Layout Components
 *
 * A compound composable layout component for creating overview pages with:
 * - Header: Title, caption, and actions
 * - Statistics: Grid of stat cards with icons, values, and captions
 * - Content: Flexible content wrapper
 */

// Types for the overview layout components
interface OverviewLayoutProps {
  children: React.ReactNode;
  className?: string;
  gap?: "gap-4" | "gap-6" | "gap-8" | "gap-12";
}

interface HeaderProps {
  title: string;
  caption?: string;
  actions?: React.ReactNode;
  className?: string;
}

interface ContentProps {
  children: React.ReactNode;
  className?: string;
}

interface StatisticsProps {
  children: React.ReactNode;
  className?: string;
  columns?:
    | "grid-cols-1"
    | "grid-cols-2"
    | "grid-cols-3"
    | "grid-cols-4"
    | "grid-cols-5";
}

// Main Overview Layout Component
function Layout({ children, className, gap = "gap-12" }: OverviewLayoutProps) {
  return <div className={cn("flex flex-col", gap, className)}>{children}</div>;
}

// Header Component
function Header({ title, caption, actions, className }: HeaderProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
      className={cn("flex items-center justify-between", className)}
    >
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold text-foreground">{title}</h1>
        {caption && <p className="text-muted-foreground">{caption}</p>}
      </div>
      {actions && <div className="flex items-center gap-2">{actions}</div>}
    </motion.div>
  );
}

// Statistics Component
function Statistics({
  children,
  className,
  columns = "grid-cols-5",
}: StatisticsProps) {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.4,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, x: -30 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className={cn(
        "grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-5 xl:grid-cols-5 2xl:",
        columns,
        className
      )}
    >
      {React.Children.map(children, (child, index) => (
        <motion.div key={index} variants={itemVariants}>
          {child}
        </motion.div>
      ))}
    </motion.div>
  );
}

// Content Wrapper Component
function Content({ children, className }: ContentProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut", delay: 0.3 }}
      className={cn("flex flex-col gap-6", className)}
    >
      {children}
    </motion.div>
  );
}

// Export all components
export { Layout, Header, Statistics, Content };

// Export compound component with sub-components
export const Overview = Object.assign(Layout, {
  Header,
  Statistics,
  Content,
});
