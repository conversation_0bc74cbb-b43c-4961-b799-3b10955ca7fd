# -----------------------------
# 🔧 STAGE 1: Build the project
# -----------------------------
FROM node:lts-alpine AS builder

# Set working directory
WORKDIR /app

# Install required tools
RUN apk add --no-cache libc6-compat

# Install pnpm
RUN corepack enable && corepack prepare pnpm@latest --activate

# Copy the full monorepo (for Turborepo context)
COPY . .

# Install dependencies (from root)
RUN pnpm install --no-frozen-lockfile

# Build only the web app using turbo
RUN pnpm turbo run build --filter=web...

# -----------------------------
# 🏁 STAGE 2: Create runtime image
# -----------------------------
FROM node:lts-alpine AS runner

# Set working directory
WORKDIR /app

# Set NODE_ENV explicitly
ENV NODE_ENV=production
ENV PORT=3000

RUN mkdir -p /tmp/dir && touch /tmp/dir/file1 /tmp//dir/file2
RUN file="$(ls -1 /tmp/dir)" && echo $file
RUN echo $(ls -1 /tmp/dir)

# RUN file="$(ls -lah)" && echo $file

# Install pnpm
RUN corepack enable && corepack prepare pnpm@latest --activate
# Copy production package.json and lockfile
COPY apps/web/package.json ./package.json
COPY pnpm-lock.yaml ./

# Install only prod dependencies
RUN pnpm install

# Copy built output from builder
COPY --from=builder /app/apps/web/.next ./.next
COPY --from=builder /app/apps/web/public ./public
COPY --from=builder /app/apps/web/next.config.js ./next.config.js

# Optional: If using App Router + next/font or images
COPY --from=builder /app/apps/web/package.json ./package.json
COPY --from=builder /app/apps/web/next.config.js ./next.config.js

# Expose app port
EXPOSE 3000

# Launch app
CMD ["pnpm", "start"]
