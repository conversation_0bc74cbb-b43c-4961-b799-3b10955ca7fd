"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@workspace/ui/components/button";
import { AuthCard } from "@/components/auth/auth-card";
import { CheckCircle } from "lucide-react";

export default function PasswordResetPage() {
  const router = useRouter();

  const handleContinue = () => {
    console.log("Continue clicked -> Navigating to login");
    router.push("/login"); 
  };

  return (
    <AuthCard
      icon={<CheckCircle size={24} className="text-brand" />}
      title="Password reset"
      description="Your password has been successfully reset. Click below to log in automatically."
    >
      <div className="text-center space-y-4 mt-2">
        <Button
          onClick={handleContinue}
          className="w-full button-primary button-md"
        >
          Continue
        </Button>
        <Link
          href="/login"
          className="text-sm font-medium text-brand hover:text-brand-hover hover:underline flex items-center justify-center gap-1"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
          >
            <path
              d="M12.6667 8H3.33333"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M8.00004 12.6667L3.33337 8.00004L8.00004 3.33337"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          Back to log in
        </Link>
      </div>
    </AuthCard>
  );
}
