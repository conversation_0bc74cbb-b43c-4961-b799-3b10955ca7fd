"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import { Button } from "@workspace/ui/components/button";
import { AuthCard } from "@/components/auth/auth-card";
import { Mail } from "lucide-react";

export default function CheckEmailPage() {
  const router = useRouter();
  const userEmail = "mail@john***.com";

  const handleResend = () => {
    console.log("Resend email clicked (placeholder)");
  };

  const simulateEmailLinkClick = () => {
    console.log(
      "Simulating email link click -> Navigating to set new password"
    );
    router.push("/set-new-password");
  };

  return (
    <AuthCard
      icon={<Mail size={24} className="text-brand" />}
      title="Check your email"
      description={
        <>
          We sent a password reset link to <br />
          <span className="font-medium text-foreground-secondary">
            {userEmail}
          </span>
        </>
      }
    >
      <div className="text-center space-y-4 mt-2">
        <p className="text-sm text-foreground-tertiary">
          Didn&apos; receive the email?{" "}
          <button
            onClick={handleResend}
            className="font-medium text-brand hover:text-brand-hover hover:underline"
          >
            Click to resend
          </button>
        </p>

        <Button
          variant="link"
          onClick={simulateEmailLinkClick}
          className="text-sm text-blue-600 hover:underline"
        >
          (Dev Only: Simulate Email Link Click)
        </Button>

        <Link
          href="/login"
          className="text-sm font-medium text-brand hover:text-brand-hover hover:underline flex items-center justify-center gap-1"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
          >
            <path
              d="M12.6667 8H3.33333"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M8.00004 12.6667L3.33337 8.00004L8.00004 3.33337"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          Back to log in
        </Link>
      </div>
    </AuthCard>
  );
}
