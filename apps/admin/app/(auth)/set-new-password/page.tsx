"use client";

import { useRouter } from "next/navigation";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { AuthCard } from "@/components/auth/auth-card";
import { LockKeyhole } from "lucide-react";

export default function SetNewPasswordPage() {
  const router = useRouter(); 

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    console.log(
      "Set new password submitted -> Navigating to password reset success"
    );
    router.push("/password-reset"); 
  };

  return (
    <AuthCard
      icon={<LockKeyhole size={24} className="text-brand" />}
      title="Set new password"
      description="Your new password must be different to previously used passwords."
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label
            htmlFor="password"
            className="block text-sm font-medium text-foreground-secondary mb-1.5 text-left"
          >
            Password
          </label>
          <Input
            id="password"
            type="password"
            placeholder="••••••••"
            required
            className="input-base"
          />
        </div>
        <div>
          <label
            htmlFor="confirm-password"
            className="block text-sm font-medium text-foreground-secondary mb-1.5 text-left"
          >
            Confirm password
          </label>
          <Input
            id="confirm-password"
            type="password"
            placeholder="••••••••"
            required
            className="input-base"
          />
        </div>
        <Button type="submit" className="w-full button-primary button-md mt-2">
          Reset password
        </Button>
      </form>
    </AuthCard>
  );
}
