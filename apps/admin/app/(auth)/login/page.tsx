"use client";

import Link from "next/link";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { AuthCard } from "@/components/auth/auth-card";
import { FirstAdminSetup } from "@/components/auth/FirstAdminSetup";
import { RequestAccess } from "@/components/auth/RequestAccess";
import {
  signInWithEmailPassword,
  checkAccountsExist,
} from "@/store/slices/authSlice";
import { RootState } from "@/store";

type ViewMode = "loading" | "login" | "firstAdmin" | "requestAccess";

export default function LoginPage() {
  const router = useRouter();
  const dispatch = useDispatch();
  const {
    isLoading,
    error: authError,
    accountsExist,
  } = useSelector((state: RootState) => state.auth);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [viewMode, setViewMode] = useState<ViewMode>("loading");

  let searchParams = useSearchParams();

  let emailParam = searchParams.get("email");
  let passwordParam = searchParams.get("password");

  async function instantLogin() {
    if (emailParam && passwordParam) {
      setEmail(emailParam);
      setPassword(passwordParam);

      await login(emailParam, passwordParam);
    }
  }

  useEffect(() => {
    instantLogin();
  }, []);

  useEffect(() => {
    // Check if any accounts exist when the page loads
    const checkAccounts = async () => {
      try {
        // @ts-ignore - dispatch returns a promise for async thunks
        await dispatch(checkAccountsExist());
      } catch (error) {
        console.error("Failed to check accounts:", error);
        setViewMode("login"); // Default to login on error
      }
    };

    checkAccounts();
  }, [dispatch]);

  useEffect(() => {
    // Update view mode based on accountsExist state
    if (accountsExist !== null) {
      setViewMode(accountsExist ? "login" : "firstAdmin");
    }
  }, [accountsExist]);

  async function login(email: string, password: string) {
    setError("");

    try {
      // @ts-ignore - dispatch returns a promise for async thunks
      const result = await dispatch(
        signInWithEmailPassword({ email, password })
      );

      if (signInWithEmailPassword.fulfilled.match(result)) {
        router.push("/dashboard");
      } else if (signInWithEmailPassword.rejected.match(result)) {
        setError((result.payload as string) || "Login failed");
      }
    } catch (error: any) {
      setError(error.message || "Failed to sign in");
    }
  }

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setError("");

    if (!email || !password) {
      setError("Please fill in all fields");
      return;
    }

    if (!email.includes("@")) {
      setError("Please enter a valid email address");
      return;
    }

    await login(email, password);
  };

  const handleRequestAccess = () => {
    setViewMode("requestAccess");
  };

  const handleBackToLogin = () => {
    setViewMode("login");
  };

  // Show loading state while checking accounts
  if (viewMode === "loading") {
    return (
      <div className="flex flex-col items-center">
        <div className="mb-6 p-2 bg-background rounded-md shadow-sm">
          <Image
            src="/shamwaa-logo-black.png"
            alt="Shamwaa Logo"
            width={40}
            height={64}
            className="object-contain"
            priority
          />
        </div>
        <AuthCard title="Loading...">
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand"></div>
          </div>
        </AuthCard>
      </div>
    );
  }

  // Show first admin setup if no accounts exist
  if (viewMode === "firstAdmin") {
    return (
      <div className="flex flex-col items-center">
        <div className="mb-6 p-2 bg-background rounded-md shadow-sm">
          <Image
            src="/shamwaa-logo-black.png"
            alt="Shamwaa Logo"
            width={220}
            height={100}
            className="object-contain"
            priority
          />
        </div>
        <FirstAdminSetup />
      </div>
    );
  }

  // Show request access form
  if (viewMode === "requestAccess") {
    return (
      <div className="flex flex-col items-center">
        <div className="mb-6 p-2 bg-background rounded-md shadow-sm">
          <Image
            src="/shamwaa-logo-black.png"
            alt="Shamwaa Logo"
            width={220}
            height={100}
            className="object-contain"
            priority
          />
        </div>
        <RequestAccess onBackToLogin={handleBackToLogin} />
      </div>
    );
  }

  // Show normal login form
  return (
    <div className="flex flex-col items-center">
      <div className="mb-6 p-2 rounded-md shadow-sm">
        <Image
          src="/shamwaa-logo-black.png"
          alt="Shamwaa Logo"
          width={220}
          height={100}
          className="object-contain"
          priority
        />
      </div>
      <AuthCard title="Log in to your account">
        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
              {error}
            </div>
          )}
          <div>
            <label
              htmlFor="email"
              className="block text-sm font-medium text-foreground-secondary mb-1.5 text-left"
            >
              Enter your email
            </label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="input-base"
              disabled={isLoading}
            />
          </div>
          <div>
            <label
              htmlFor="password"
              className="block text-sm font-medium text-foreground-secondary mb-1.5 text-left"
            >
              Enter your password
            </label>
            <Input
              id="password"
              type="password"
              placeholder="••••••••"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="input-base"
              disabled={isLoading}
            />
          </div>
          <Button
            type="submit"
            className="w-full button-primary button-md mt-2"
            disabled={isLoading}
          >
            {isLoading ? "Signing in..." : "Sign in"}
          </Button>

          <div className="space-y-2 text-center pt-2">
            <Link
              href="/forgot-password"
              className="block text-sm font-medium text-brand hover:text-brand-hover hover:underline"
            >
              Forgot password?
            </Link>

            <Button
              type="button"
              onClick={handleRequestAccess}
              variant="outline"
              className="w-full button-md text-sm"
              disabled={isLoading}
            >
              Request Access
            </Button>
          </div>
        </form>
      </AuthCard>
    </div>
  );
}
