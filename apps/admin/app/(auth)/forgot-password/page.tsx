"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { AuthCard } from "@/components/auth/auth-card";
import { KeyRound } from "lucide-react";
import { sendPasswordReset } from "@/store/slices/authSlice";
import { RootState } from "@/store";

export default function ForgotPasswordPage() {
  const router = useRouter();
  const dispatch = useDispatch();
  const { isLoading } = useSelector((state: RootState) => state.auth);
  const [email, setEmail] = useState("");
  const [error, setError] = useState("");

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setError("");

    if (!email || !email.includes("@")) {
      setError("Please enter a valid email address");
      return;
    }

    try {
      // @ts-ignore - dispatch returns a promise for async thunks
      const result = await dispatch(sendPasswordReset(email));
      
      if (sendPasswordReset.fulfilled.match(result)) {
        router.push("/check-email");
      } else if (sendPasswordReset.rejected.match(result)) {
        setError(result.payload as string || "Failed to send reset email");
      }
    } catch (error: any) {
      setError(error.message || "Failed to send reset email");
    }
  };

  return (
    <AuthCard
      icon={<KeyRound size={24} className="text-brand" />}
      title="Forgot password?"
      description="No worries, we'll send you reset instructions."
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        {error && (
          <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
            {error}
          </div>
        )}
        <div>
          <label
            htmlFor="email"
            className="block text-sm font-medium text-foreground-secondary mb-1.5 text-left"
          >
            Email
          </label>
          <Input
            id="email"
            type="email"
            placeholder="Enter your email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            className="input-base"
            disabled={isLoading}
          />
        </div>
        <Button 
          type="submit" 
          className="w-full button-primary button-md mt-2"
          disabled={isLoading}
        >
          {isLoading ? "Sending..." : "Reset password"}
        </Button>
        <div className="text-center pt-2">
          <Link
            href="/login"
            className="text-sm font-medium text-brand hover:text-brand-hover hover:underline flex items-center justify-center gap-1"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
            >
              <path
                d="M12.6667 8H3.33333"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M8.00004 12.6667L3.33337 8.00004L8.00004 3.33337"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            Back to log in
          </Link>
        </div>
      </form>
    </AuthCard>
  );
}
