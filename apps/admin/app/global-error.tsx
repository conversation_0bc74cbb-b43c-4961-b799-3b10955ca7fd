"use client";

import { useEffect } from "react";
import { Inter } from "next/font/google";
import { motion } from "framer-motion";
import { AlertOctagon, Home } from "lucide-react";
import Link from "next/link";

const inter = Inter({ subsets: ["latin"] });

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    // TODO: Implement proper error reporting service
  }, [error]);

  return (
    <html lang="en">
      <body className={`${inter.className} bg-background-secondary`}>
        <div className="min-h-screen flex flex-col items-center justify-center p-4">
          <motion.div
            className="w-full max-w-md mx-auto bg-card p-6 rounded-lg shadow-lg text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{
              type: "spring",
              stiffness: 300,
              damping: 30,
            }}
          >
            <motion.div
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ duration: 0.5, type: "spring" }}
              className="mx-auto mb-6 w-20 h-20 flex items-center justify-center bg-red-100 rounded-full"
            >
              <AlertOctagon size={40} className="text-red-600" />
            </motion.div>

            <motion.h1
              className="text-2xl font-bold mb-2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              Oops! Something went wrong
            </motion.h1>

            <motion.p
              className="text-gray-500 mb-6"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              The application encountered a critical error. Our team has been
              notified.
            </motion.p>

            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={reset}
                className="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-md transition-colors w-full sm:w-auto"
              >
                Try again
              </motion.button>

              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="w-full sm:w-auto"
              >
                <Link
                  href="/"
                  className="border border-neutral-200 bg-white hover:bg-neutral-50 px-4 py-2 rounded-md transition-colors flex items-center justify-center gap-2 w-full"
                >
                  <Home size={16} />
                  Back to home
                </Link>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </body>
    </html>
  );
}
