"use client";

import { memo } from "react";
import { withRBAC } from "@/lib/components/RBACWrapper";
import { FinanceManagementContainer } from "./components/FinanceManagementContainer";

/**
 * Finance Management Page Component
 *
 * This is the main page component for finance management.
 * It's wrapped with RBAC for permission control and uses a container component
 * for better separation of concerns and performance optimization.
 */
const FinanceManagementPage = memo(() => {
  return <FinanceManagementContainer />;
});

FinanceManagementPage.displayName = "FinanceManagementPage";

export default withRBAC(
  FinanceManagementPage,
  "ledgers", // Entity
  "view", // Required action
  <div className="p-6 text-center">
    <h1 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h1>
    <p className="text-gray-500">
      You don't have permission to view finance management.
    </p>
  </div> // Fallback content
);
