"use client";

import { memo } from "react";
import {
  BarChart3,
  FileText,
  DollarSign,
  TrendingUp,
  Building,
} from "lucide-react";
import {
  Tabs,
  Ta<PERSON>Content,
  TabsList,
  TabsTrigger,
} from "@workspace/ui/components/tabs";
import { type LedgerWithStats } from "@/lib/logistics";
import { type TransactionWithAssociations } from "@/lib/logistics/operations/transactions";
import { type ColumnFilter } from "@/components/ui/filter-panel";
import { LedgersList } from "./LedgersList";
import { FinanceOverview } from "./FinanceOverview";
import { FinanceTransactions } from "./FinanceTransactions";
import { FinanceReports } from "./FinanceReports";
import { FinanceCostCenter } from "./FinanceCostCenter";

interface FinanceTabsProps {
  activeTab:
    | "overview"
    | "ledgers"
    | "transactions"
    | "reports"
    | "cost-center";
  viewMode: "cards" | "table";
  searchTerm: string;
  filterStatus: string;
  filterCategory: string; // Add category filter
  columnFilters: ColumnFilter[];
  loading: boolean;
  filteredLedgers: Ledger[];
  filteredTransactions: TransactionWithAssociations[];
  currentPage: number; // Add pagination
  itemsPerPage: number; // Add pagination
  onTabChange: (
    tab: "overview" | "ledgers" | "transactions" | "reports" | "cost-center"
  ) => void;
  onViewModeChange: (mode: "cards" | "table") => void;
  onSearchChange: (term: string) => void;
  onFilterStatusChange: (status: string) => void;
  onFilterCategoryChange: (category: string) => void; // Add category filter handler
  onColumnFiltersChange: (filters: ColumnFilter[]) => void;
  onPageChange: (page: number) => void; // Add pagination handler
  onRefresh: () => void;
  onLedgerAction: (action: string, ledger?: LedgerWithStats) => void;
  onTransactionAction: (
    action: string,
    transaction?: TransactionWithAssociations
  ) => void;
  // Checkbox support
  selectedLedgers: Set<string>;
  setSelectedLedgers: (items: Set<string>) => void;
  selectedTransactions: Set<string>;
  setSelectedTransactions: (items: Set<string>) => void;
  onBulkLedgerStatusUpdate: (status: string) => void;
  onBulkLedgerDelete: () => void;
  onBulkTransactionStatusUpdate: (status: string) => void;
  onBulkTransactionDelete: () => void;
  onBulkCreateTasks: (entityType: "ledgers" | "transactions") => void;
  onClearLedgerSelections?: () => void;
  onClearTransactionSelections?: () => void;
}

/**
 * Tabs component for Finance Management
 *
 * Handles the tabbed interface for different finance views.
 * Each tab contains appropriate content for finance management.
 */
export const FinanceTabs = memo<FinanceTabsProps>(
  ({
    activeTab,
    viewMode,
    searchTerm,
    filterStatus,
    filterCategory,
    columnFilters,
    loading,
    filteredLedgers,
    filteredTransactions,
    currentPage,
    itemsPerPage,
    onTabChange,
    onViewModeChange,
    onSearchChange,
    onFilterStatusChange,
    onFilterCategoryChange,
    onColumnFiltersChange,
    onPageChange,
    onRefresh,
    onLedgerAction,
    onTransactionAction,
    selectedLedgers,
    setSelectedLedgers,
    selectedTransactions,
    setSelectedTransactions,
    onBulkLedgerStatusUpdate,
    onBulkLedgerDelete,
    onBulkTransactionStatusUpdate,
    onBulkTransactionDelete,
    onBulkCreateTasks,
    onClearLedgerSelections,
    onClearTransactionSelections,
  }) => {
    return (
      <Tabs
        value={activeTab}
        onValueChange={(value) =>
          onTabChange(
            value as
              | "overview"
              | "ledgers"
              | "transactions"
              | "reports"
              | "cost-center"
          )
        }
        className="w-full"
      >
        <TabsList className="grid w-max grid-cols-5">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="ledgers" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Ledgers ({filteredLedgers.length})
          </TabsTrigger>
          <TabsTrigger value="transactions" className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            Transactions ({filteredTransactions.length})
          </TabsTrigger>
          <TabsTrigger value="cost-center" className="flex items-center gap-2">
            <Building className="h-4 w-4" />
            Cost Center
          </TabsTrigger>
          <TabsTrigger value="reports" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Reports
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          <FinanceOverview />
        </TabsContent>

        <TabsContent value="ledgers" className="mt-6">
          <LedgersList
            searchTerm={searchTerm}
            setSearchTerm={onSearchChange}
            filterStatus={filterStatus}
            setFilterStatus={onFilterStatusChange}
            filterCategory={filterCategory}
            setFilterCategory={onFilterCategoryChange}
            viewMode={viewMode}
            setViewMode={onViewModeChange}
            loading={loading}
            onRefresh={onRefresh}
            columnFilters={columnFilters}
            setColumnFilters={onColumnFiltersChange}
            ledgers={filteredLedgers}
            currentPage={currentPage}
            itemsPerPage={itemsPerPage}
            onPageChange={onPageChange}
            onViewLedger={(ledger) => onLedgerAction("view", ledger)}
            onEditLedger={(ledger) => onLedgerAction("edit", ledger)}
            onDeleteLedger={(ledger) => onLedgerAction("delete", ledger)}
            onOpenLedger={(ledger) => onLedgerAction("openLedger", ledger)}
            onCreateLedger={() => onLedgerAction("create")}
            selectedLedgers={selectedLedgers}
            setSelectedLedgers={setSelectedLedgers}
            onBulkStatusUpdate={onBulkLedgerStatusUpdate}
            onBulkDelete={onBulkLedgerDelete}
            onBulkCreateTasks={() => onBulkCreateTasks("ledgers")}
            onClearSelections={onClearLedgerSelections}
          />
        </TabsContent>

        <TabsContent value="transactions" className="mt-6">
          <FinanceTransactions
            searchTerm={searchTerm}
            setSearchTerm={onSearchChange}
            filterStatus={filterStatus}
            setFilterStatus={onFilterStatusChange}
            viewMode={viewMode}
            setViewMode={onViewModeChange}
            loading={loading}
            onRefresh={onRefresh}
            columnFilters={columnFilters}
            setColumnFilters={onColumnFiltersChange}
            transactions={filteredTransactions}
            currentPage={currentPage}
            itemsPerPage={itemsPerPage}
            onPageChange={onPageChange}
            onViewTransaction={(transaction) =>
              onTransactionAction("view", transaction)
            }
            onEditTransaction={(transaction) =>
              onTransactionAction("edit", transaction)
            }
            onDeleteTransaction={(transaction) =>
              onTransactionAction("delete", transaction)
            }
            onCreateTransaction={() => onTransactionAction("create")}
            selectedTransactions={selectedTransactions}
            setSelectedTransactions={setSelectedTransactions}
            onBulkStatusUpdate={onBulkTransactionStatusUpdate}
            onBulkDelete={onBulkTransactionDelete}
            onBulkCreateTasks={() => onBulkCreateTasks("transactions")}
            onClearSelections={onClearTransactionSelections}
          />
        </TabsContent>

        <TabsContent value="cost-center" className="mt-6">
          <FinanceCostCenter />
        </TabsContent>

        <TabsContent value="reports" className="mt-6">
          <FinanceReports />
        </TabsContent>
      </Tabs>
    );
  }
);

FinanceTabs.displayName = "FinanceTabs";
