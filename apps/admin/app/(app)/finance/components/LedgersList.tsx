"use client";

import { memo, useMemo, useCallback } from "react";
import {
  FileText,
  Plus,
  Trash2,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  CheckSquare,
} from "lucide-react";
import { Button } from "@workspace/ui/components/button";
import { ProtectedCreateButton } from "@/lib/components/RBACWrapper";
import {
  BulkStatusSelector,
  LEDGER_STATUS_OPTIONS,
} from "@/components/ui/bulk-status-selector";
import { Listing } from "@/modules/listing";
import { type LedgerWithStats } from "@/lib/logistics";
import { type ColumnFilter } from "@/components/ui/filter-panel";
import {
  getLedgerTableColumns,
  convertToListingTableColumns,
} from "./LedgerTableColumns";
import { LedgerCards } from "./LedgerCards";
import { LEDGER_CATEGORIES } from "@/lib/logistics";

interface LedgersListProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  filterStatus: string;
  setFilterStatus: (status: string) => void;
  filterCategory: string; // Add category filter
  setFilterCategory: (category: string) => void; // Add category filter handler
  viewMode: "cards" | "table";
  setViewMode: (mode: "cards" | "table") => void;
  loading: boolean;
  onRefresh: () => void;
  columnFilters: ColumnFilter[];
  setColumnFilters: (filters: ColumnFilter[]) => void;
  ledgers: LedgerWithStats[];
  currentPage: number; // Add pagination
  itemsPerPage: number; // Add pagination
  onPageChange: (page: number) => void; // Add pagination handler
  onViewLedger: (ledger: LedgerWithStats) => void;
  onEditLedger: (ledger: LedgerWithStats) => void;
  onDeleteLedger: (ledger: LedgerWithStats) => void;
  onOpenLedger: (ledger: LedgerWithStats) => void;
  onCreateLedger: () => void;
  // Checkbox support
  selectedLedgers: Set<string>;
  setSelectedLedgers: (items: Set<string>) => void;
  onBulkStatusUpdate: (status: string) => void;
  onBulkDelete: () => void;
  onBulkCreateTasks: () => void;
  onClearSelections?: () => void;
}

/**
 * Ledgers List Component
 *
 * Displays ledgers in either card or table view with filtering and search capabilities.
 * Uses the standardized Listing component for consistent UI patterns.
 */
export const LedgersList = memo<LedgersListProps>(
  ({
    searchTerm,
    setSearchTerm,
    filterStatus,
    setFilterStatus,
    filterCategory,
    setFilterCategory,
    viewMode,
    setViewMode,
    loading,
    onRefresh,
    columnFilters,
    setColumnFilters,
    ledgers,
    currentPage,
    itemsPerPage,
    onPageChange,
    onViewLedger,
    onEditLedger,
    onDeleteLedger,
    onOpenLedger,
    onCreateLedger,
    selectedLedgers,
    setSelectedLedgers,
    onBulkStatusUpdate,
    onBulkDelete,
    onBulkCreateTasks,
    onClearSelections,
  }) => {
    // Debug logging
    console.log("LedgersList received:", {
      ledgersCount: ledgers.length,
      loading,
      viewMode,
      filterStatus,
      searchTerm,
      sampleLedger: ledgers[0],
      ledgerKeys: ledgers[0] ? Object.keys(ledgers[0]) : [],
    });
    // Table columns with three-dot dropdown actions (TanStack format for FilterPanel)
    const tanstackTableColumns = useMemo(
      () =>
        getLedgerTableColumns({
          onViewLedger,
          onEditLedger,
          onDeleteLedger,
          onOpenLedger,
        }),
      [onViewLedger, onEditLedger, onDeleteLedger, onOpenLedger]
    );

    // Table columns for Listing.Table component (simple format)
    const listingTableColumns = useMemo(
      () =>
        convertToListingTableColumns({
          onViewLedger,
          onEditLedger,
          onDeleteLedger,
          onOpenLedger,
        }),
      [onViewLedger, onEditLedger, onDeleteLedger, onOpenLedger]
    );

    // Define ledger categories for filtering (ordered by priority)
    const ledgerCategories = useMemo(() => {
      const categories = Object.values(LEDGER_CATEGORIES).map((category) => ({
        key: category.name.toLowerCase(),
        label: category.name,
        count: 0,
      }));

      // Calculate counts
      categories.forEach((category) => {
        category.count = ledgers.filter(
          (ledger) =>
            (ledger.category?.toLowerCase() || "uncategorized") === category.key
        ).length;
      });

      // Defensive constraint: if current filter results in no data, reset to "all"
      const currentCategoryCount =
        categories.find((cat) => cat.key === filterCategory)?.count || 0;
      if (
        filterCategory !== "all" &&
        currentCategoryCount === 0 &&
        ledgers.length > 0
      ) {
        setFilterCategory("all");
      }

      return categories;
    }, [ledgers, filterCategory, setFilterCategory]);

    // Render bulk actions
    const renderBulkActions = useCallback(
      () => (
        <>
          <BulkStatusSelector
            statusOptions={LEDGER_STATUS_OPTIONS}
            onStatusUpdate={onBulkStatusUpdate}
            placeholder="Mark as..."
          />
          <Button
            variant="outline"
            size="sm"
            onClick={onBulkCreateTasks}
            className="gap-2"
            disabled={selectedLedgers.size === 0}
          >
            <CheckSquare size={16} />
            Create Tasks
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={onBulkDelete}
            className="gap-2"
          >
            <Trash2 size={16} />
            Delete
          </Button>
        </>
      ),
      [
        onBulkStatusUpdate,
        onBulkDelete,
        onBulkCreateTasks,
        selectedLedgers.size,
      ]
    );

    // Pagination - calculate based on filtered ledgers
    const totalPages = Math.ceil(ledgers.length / itemsPerPage);

    // Pagination for cards view only (table handles its own pagination)
    const paginatedLedgers = useMemo(() => {
      const start = (currentPage - 1) * itemsPerPage;
      const end = start + itemsPerPage;
      return ledgers.slice(start, end);
    }, [ledgers, currentPage, itemsPerPage]);

    // Empty state for table (React element)
    const tableEmptyState = (
      <div className="text-center py-12">
        <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
          <FileText className="h-6 w-6 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-1">
          No ledgers found
        </h3>
        <p className="text-gray-500 mb-4">
          Get started by creating your first ledger
        </p>
        <button
          onClick={onCreateLedger}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
        >
          Create Ledger
        </button>
      </div>
    );

    return (
      <Listing>
        <Listing.Filters
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          columnFilters={columnFilters}
          onColumnFilterAdd={(filter) =>
            setColumnFilters([...columnFilters, filter])
          }
          onColumnFilterRemove={(index) =>
            setColumnFilters(columnFilters.filter((_, i) => i !== index))
          }
          columns={tanstackTableColumns}
          tableData={ledgers}
          loading={loading}
          onRefresh={onRefresh}
          enableDynamicFilters={true}
          defaultFilterColumn="name"
          autoSelectDefaultColumn={true}
          bulkActions={renderBulkActions()}
          selectedCount={selectedLedgers.size}
          showBulkActions={true}
          onClearSelections={onClearSelections}
        />

        <Listing.Controls
          entity="ledgers"
          length={ledgers.length}
          viewMode={viewMode}
          onViewModeChange={setViewMode}
          categories={ledgerCategories}
          categoryFilter={filterCategory}
          onCategoryFilterChange={setFilterCategory}
          actions={
            <div className="flex items-center gap-2">
              <ProtectedCreateButton entity="ledgers">
                <Button onClick={onCreateLedger}>
                  <Plus size={16} />
                  New Ledger
                </Button>
              </ProtectedCreateButton>
            </div>
          }
        />

        {viewMode === "cards" ? (
          <>
            <LedgerCards
              ledgers={paginatedLedgers}
              loading={loading}
              emptyState={tableEmptyState}
              ledgerCategories={ledgerCategories}
              onViewLedger={onViewLedger}
            />

            {/* Pagination for cards view */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between px-6 py-4 border-t border-gray-200 bg-white rounded-lg">
                <div className="text-sm text-gray-500">
                  Showing {(currentPage - 1) * itemsPerPage + 1} to{" "}
                  {Math.min(currentPage * itemsPerPage, ledgers.length)} of{" "}
                  {ledgers.length} results
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(1)}
                    disabled={currentPage === 1}
                    className="gap-1"
                  >
                    <ChevronsLeft size={16} />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="gap-1"
                  >
                    <ChevronLeft size={16} />
                    Previous
                  </Button>
                  <span className="px-3 py-2 text-sm text-gray-900">
                    Page {currentPage} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="gap-1"
                  >
                    Next
                    <ChevronRight size={16} />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(totalPages)}
                    disabled={currentPage === totalPages}
                    className="gap-1"
                  >
                    <ChevronsRight size={16} />
                  </Button>
                </div>
              </div>
            )}
          </>
        ) : (
          <Listing.Table
            data={ledgers}
            columns={listingTableColumns}
            loading={loading}
            enableCheckboxes={true}
            selectedRowIds={Array.from(selectedLedgers)}
            onSelectionChange={(selectedIds) =>
              setSelectedLedgers(new Set(selectedIds))
            }
            getRowId={(item) => item.id}
            emptyState={tableEmptyState}
            pagination={{
              currentPage,
              totalPages,
              totalItems: ledgers.length,
              itemsPerPage,
              onPageChange,
            }}
          />
        )}
      </Listing>
    );
  }
);

LedgersList.displayName = "LedgersList";
