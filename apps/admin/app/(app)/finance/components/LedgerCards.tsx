"use client";

import { memo } from "react";
import {
  FileText,
  Calendar,
  DollarSign,
  Tag,
  CheckCircle,
  XCircle,
  Clock,
  Building,
  TrendingUp,
  TrendingDown,
  CreditCard,
} from "lucide-react";
import { Badge } from "@workspace/ui/components/badge";
import { Listing } from "@/modules/listing";
import { type LedgerWithStats, LEDGER_CATEGORIES } from "@/lib/logistics";
import { trimming } from "@/lib/utils";

interface LedgerCardsProps {
  ledgers: LedgerWithStats[];
  loading: boolean;
  emptyState: React.ReactNode;
  ledgerCategories: Array<{ key: string; label: string; count: number }>;
  onViewLedger: (ledger: LedgerWithStats) => void;
}

/**
 * Ledger Cards Component
 *
 * Displays ledgers in a card grid layout with animations and actions.
 * Follows the design patterns from other card components in the application.
 */
export const LedgerCards = memo<LedgerCardsProps>(
  ({ ledgers, loading, emptyState, ledgerCategories, onViewLedger }) => {
    // Helper functions for ledger category styling
    const getLedgerCategoryColor = (category: string): string => {
      const categoryData = Object.values(LEDGER_CATEGORIES).find(
        (cat) => cat.name.toUpperCase() === category.toUpperCase()
      );
      return categoryData?.color || "#6b7280";
    };

    const getLedgerCategoryIcon = (category: string) => {
      const iconMap: Record<string, any> = {
        TrendingUp: TrendingUp,
        TrendingDown: TrendingDown,
        Building: Building,
        CreditCard: CreditCard,
        FileText: FileText,
      };

      const categoryData = Object.values(LEDGER_CATEGORIES).find(
        (cat) => cat.name.toUpperCase() === category.toUpperCase()
      );
      return iconMap[categoryData?.icon || "FileText"] || FileText;
    };

    return (
      <Listing.Cards
        data={ledgers}
        loading={loading}
        emptyState={emptyState}
        columns="grid-cols-2"
        groupByCategory={true}
        categories={ledgerCategories.map((category) => ({
          key: category.key,
          name: category.label,
          description: `${category.count} ledgers`,
          color: getLedgerCategoryColor(category.key),
          icon: getLedgerCategoryIcon(category.key),
        }))}
        getCategoryKey={(ledger) =>
          ledger.category?.toLowerCase() || "uncategorized"
        }
        renderCard={(ledger: Ledger, category) => (
          <div className="p-4 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center gap-2">
                <div
                  className="p-1.5 rounded-md"
                  style={{
                    backgroundColor: category?.color
                      ? `${category.color}20`
                      : "#f3f4f6",
                    color: category?.color || "#6b7280",
                  }}
                >
                  {category?.icon ? (
                    <category.icon className="h-3 w-3" />
                  ) : (
                    <FileText className="h-3 w-3" />
                  )}
                </div>
                <h5 className="font-medium text-gray-900 truncate">
                  {trimming(ledger.name, 15)}
                </h5>
              </div>
              <Badge
                className="gap-1"
                style={{
                  backgroundColor: `${getLedgerCategoryColor(ledger.category || "")}20`,
                  color: getLedgerCategoryColor(ledger.category || ""),
                  border: `1px solid ${getLedgerCategoryColor(ledger.category || "")}40`,
                }}
              >
                <Building className="h-1 w-1" />
                <small className="capitalize">
                  {ledger.category || "Uncategorized"}
                </small>
              </Badge>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Status</span>
                <Badge
                  variant="secondary"
                  className={
                    ledger.status === "ACTIVE"
                      ? "bg-green-100 text-green-800"
                      : "bg-gray-100 text-gray-800"
                  }
                >
                  <small className="capitalize">
                    {ledger.status || "PENDING"}
                  </small>
                </Badge>
              </div>

              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Books</span>
                <span className="text-gray-900 font-medium">
                  {ledger.books && ledger.books.length > 0
                    ? `${ledger.books.length} book${ledger.books.length > 1 ? "s" : ""}`
                    : "No books"}
                </span>
              </div>

              <div className="flex flex-row justify-between gap-1">
                <span className="text-xs text-gray-500">
                  {ledger.created_at
                    ? new Date(ledger.created_at).toLocaleDateString()
                    : "No date"}
                </span>
                <small className="text-gray-500 text-xs bg-gray-100 px-2 py-0.5 rounded-md">
                  {ledger.tags && ledger.tags.length > 0
                    ? `${ledger.tags.length} tag${ledger.tags.length > 1 ? "s" : ""}`
                    : "No tags"}
                </small>
              </div>
            </div>
          </div>
        )}
        onItemClick={(ledger) => onViewLedger(ledger)}
      />
    );
  }
);

LedgerCards.displayName = "LedgerCards";
