"use client";

import { memo } from "react";
import { motion } from "framer-motion";
import { RefreshCw, Plus, Loader2 } from "lucide-react";
import { But<PERSON> } from "@workspace/ui/components/button";

interface FinanceManagementHeaderProps {
  loading: boolean;
  shouldShowCreateButton: boolean;
  onRefresh: () => void;
  onCreateLedger: () => void;
}

/**
 * Finance Management Header Component
 *
 * Displays the page title, description, and action buttons.
 * Includes refresh functionality and create ledger button with RBAC.
 */
export const FinanceManagementHeader = memo<FinanceManagementHeaderProps>(
  ({ loading, shouldShowCreateButton, onRefresh, onCreateLedger }) => {
    return (
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-foreground">Finance</h1>
          <p className="text-muted-foreground mt-1">
            Monitor financial performance and manage transactions
          </p>
        </div>

        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            onClick={onRefresh}
            disabled={loading}
            className="gap-2"
          >
            {loading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            Refresh
          </Button>

          {shouldShowCreateButton && (
            <Button onClick={onCreateLedger} className="gap-2">
              <Plus className="h-4 w-4" />
              New Ledger
            </Button>
          )}
        </div>
      </motion.div>
    );
  }
);

FinanceManagementHeader.displayName = "FinanceManagementHeader";
