"use client";

import { memo } from "react";
import { type FinanceManagementState } from "./FinanceManagementContainer";
import { LedgerDialog } from "@/components/finance/ledger-dialog";
import { CreateLedgerDialog } from "@/components/finance/create-ledger-dialog";
import { CreateTransactionDialog } from "@/components/finance/create-transaction-dialog";
import { EditTransactionDialog } from "@/components/finance/edit-transaction-dialog";
import { TransactionDetailDialog } from "@/components/finance/transaction-detail-dialog";
import { DeleteTransactionDialog } from "@/components/finance/delete-transaction-dialog";
import { ledgerService } from "@/lib/logistics";
import { useAppSelector } from "@/store/hooks";

interface FinanceDialogsProps {
  state: FinanceManagementState;
  onClose: (dialogType: string) => void;
  onLedgerMutation: () => void;
  onTransactionMutation?: () => void;
}

/**
 * Finance Dialogs Component
 *
 * Manages all dialog states for the finance management module.
 * Centralizes dialog management for better organization and performance.
 */
export const FinanceDialogs = memo<FinanceDialogsProps>(
  ({ state, onClose, onLedgerMutation, onTransactionMutation }) => {
    const { user: authUser } = useAppSelector((state) => state.auth);

    // Handle ledger deletion
    const handleDeleteLedger = async () => {
      if (!state.selectedLedger || !authUser?.accountId) return;

      try {
        const result = await ledgerService.deleteLedger(
          state.selectedLedger.id
        );
        if (result.success) {
          onLedgerMutation();
          onClose("DeleteLedgerDialog");
        } else {
          console.error("Failed to delete ledger:", result.error);
        }
      } catch (error) {
        console.error("Error deleting ledger:", error);
      }
    };

    return (
      <>
        {/* Create Ledger Dialog */}
        <CreateLedgerDialog
          isOpen={state.isCreateLedgerDialogOpen}
          onClose={() => onClose("CreateLedgerDialog")}
          onLedgerCreated={() => {
            onLedgerMutation();
            onClose("CreateLedgerDialog");
          }}
        />

        {/* View/Edit Ledger Dialog */}
        {state.selectedLedger && (
          <LedgerDialog
            isOpen={
              state.isViewLedgerDialogOpen || state.isEditLedgerDialogOpen
            }
            onClose={() => {
              onClose("ViewLedgerDialog");
              onClose("EditLedgerDialog");
            }}
            ledger={state.selectedLedger}
            onLedgerUpdate={() => {
              onLedgerMutation();
              onClose("ViewLedgerDialog");
              onClose("EditLedgerDialog");
            }}
          />
        )}

        {/* Delete Confirmation Dialog */}
        {state.selectedLedger && state.isDeleteLedgerDialogOpen && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Delete Ledger
              </h3>
              <p className="text-gray-600 mb-6">
                Are you sure you want to delete "{state.selectedLedger.name}"?
                This action cannot be undone.
              </p>
              <div className="flex gap-3 justify-end">
                <button
                  onClick={() => onClose("DeleteLedgerDialog")}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDeleteLedger}
                  className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Transaction Dialogs */}

        {/* Create Transaction Dialog */}
        <CreateTransactionDialog
          isOpen={state.isCreateTransactionDialogOpen}
          onClose={() => onClose("CreateTransactionDialog")}
          ledgerId="" // Will be selected within the dialog
          onTransactionCreated={() => {
            onTransactionMutation?.();
            onClose("CreateTransactionDialog");
          }}
        />

        {/* View Transaction Dialog */}
        {state.selectedTransaction && (
          <TransactionDetailDialog
            isOpen={state.isViewTransactionDialogOpen}
            onClose={() => onClose("ViewTransactionDialog")}
            transaction={state.selectedTransaction}
          />
        )}

        {/* Edit Transaction Dialog */}
        {state.selectedTransaction && (
          <EditTransactionDialog
            isOpen={state.isEditTransactionDialogOpen}
            onClose={() => onClose("EditTransactionDialog")}
            transaction={state.selectedTransaction}
            onTransactionUpdated={() => {
              onTransactionMutation?.();
              onClose("EditTransactionDialog");
            }}
          />
        )}

        {/* Delete Transaction Dialog */}
        {state.selectedTransaction && (
          <DeleteTransactionDialog
            isOpen={state.isDeleteTransactionDialogOpen}
            onClose={() => onClose("DeleteTransactionDialog")}
            transaction={state.selectedTransaction}
            onTransactionDeleted={() => {
              onTransactionMutation?.();
              onClose("DeleteTransactionDialog");
            }}
          />
        )}
      </>
    );
  }
);

FinanceDialogs.displayName = "FinanceDialogs";
