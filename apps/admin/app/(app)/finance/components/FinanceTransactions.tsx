"use client";

import { memo } from "react";
import { type TransactionWithAssociations } from "@/lib/logistics/operations/transactions";
import { type ColumnFilter } from "@/components/ui/filter-panel";
import { TransactionsList } from "./TransactionsList";

interface FinanceTransactionsProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  filterStatus: string;
  setFilterStatus: (status: string) => void;
  viewMode: "cards" | "table";
  setViewMode: (mode: "cards" | "table") => void;
  loading: boolean;
  onRefresh: () => void;
  columnFilters: ColumnFilter[];
  setColumnFilters: (filters: ColumnFilter[]) => void;
  transactions: TransactionWithAssociations[];
  currentPage: number; // Add pagination
  itemsPerPage: number; // Add pagination
  onPageChange: (page: number) => void; // Add pagination handler
  onViewTransaction: (transaction: TransactionWithAssociations) => void;
  onEditTransaction: (transaction: TransactionWithAssociations) => void;
  onDeleteTransaction: (transaction: TransactionWithAssociations) => void;
  onCreateTransaction: () => void;
  // Checkbox support
  selectedTransactions: Set<string>;
  setSelectedTransactions: (items: Set<string>) => void;
  onBulkStatusUpdate: (status: string) => void;
  onBulkDelete: () => void;
  onBulkCreateTasks: () => void;
  onClearSelections?: () => void;
}

/**
 * Finance Transactions Component
 *
 * Displays transaction history and management using the same viewset pattern as ledgers.
 * Uses the standardized TransactionsList component for consistent UI patterns.
 */
export const FinanceTransactions = memo<FinanceTransactionsProps>(
  ({
    searchTerm,
    setSearchTerm,
    filterStatus,
    setFilterStatus,
    viewMode,
    setViewMode,
    loading,
    onRefresh,
    columnFilters,
    setColumnFilters,
    transactions,
    currentPage,
    itemsPerPage,
    onPageChange,
    onViewTransaction,
    onEditTransaction,
    onDeleteTransaction,
    onCreateTransaction,
    selectedTransactions,
    setSelectedTransactions,
    onBulkStatusUpdate,
    onBulkDelete,
    onBulkCreateTasks,
    onClearSelections,
  }) => {
    return (
      <TransactionsList
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        filterStatus={filterStatus}
        setFilterStatus={setFilterStatus}
        viewMode={viewMode}
        setViewMode={setViewMode}
        loading={loading}
        onRefresh={onRefresh}
        columnFilters={columnFilters}
        setColumnFilters={setColumnFilters}
        transactions={transactions}
        currentPage={currentPage}
        itemsPerPage={itemsPerPage}
        onPageChange={onPageChange}
        onViewTransaction={onViewTransaction}
        onEditTransaction={onEditTransaction}
        onDeleteTransaction={onDeleteTransaction}
        onCreateTransaction={onCreateTransaction}
        selectedTransactions={selectedTransactions}
        setSelectedTransactions={setSelectedTransactions}
        onBulkStatusUpdate={onBulkStatusUpdate}
        onBulkDelete={onBulkDelete}
        onBulkCreateTasks={onBulkCreateTasks}
        onClearSelections={onClearSelections}
      />
    );
  }
);

FinanceTransactions.displayName = "FinanceTransactions";
