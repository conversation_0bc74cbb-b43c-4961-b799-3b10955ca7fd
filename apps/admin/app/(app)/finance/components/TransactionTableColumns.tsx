"use client";

import React from "react";
import { redirect } from "next/navigation";
import { type ColumnDef } from "@tanstack/react-table";
import {
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Calendar,
  DollarSign,
  ArrowUpRight,
  ArrowDownLeft,
  ReceiptText,
} from "lucide-react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { type TransactionWithAssociations } from "@/lib/logistics/operations/transactions";
import { abbreviateNumber } from "js-abbreviation-number";

interface TransactionTableColumnsProps {
  onViewTransaction: (transaction: TransactionWithAssociations) => void;
  onEditTransaction: (transaction: TransactionWithAssociations) => void;
  onDeleteTransaction: (transaction: TransactionWithAssociations) => void;
}

// Column format for Listing.Table component
interface ListingTableColumn {
  key: string;
  label: string;
  render?: (item: any, index: number) => React.ReactNode;
  className?: string;
}

/**
 * Transaction Table Columns Configuration
 *
 * Defines the table columns for the transactions table with three-dot dropdown actions.
 * Uses the standardized dropdown menu pattern instead of inline action buttons.
 */
export function getTransactionTableColumns({
  onViewTransaction,
  onEditTransaction,
  onDeleteTransaction,
}: TransactionTableColumnsProps): ColumnDef<TransactionWithAssociations>[] {
  return [
    {
      accessorKey: "name",
      header: "Transaction Name",
      cell: ({ row }) => {
        const transaction = row.original;
        return (
          <div className="flex flex-col">
            <span className="font-medium text-gray-900">
              {transaction.name || "Unnamed Transaction"}
            </span>
            {transaction.context && (
              <span className="text-sm text-gray-500 truncate max-w-xs">
                {transaction.context}
              </span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "amount",
      header: "Type & Amount",
      cell: ({ row }) => {
        const amount = row.getValue("amount") as number;
        const isCredit = amount >= 0;
        const type = isCredit ? "CREDIT" : "DEBIT";
        const Icon = isCredit ? ArrowUpRight : ArrowDownLeft;

        return (
          <div className="flex items-center gap-2">
            <Icon
              className={`h-4 w-4 ${isCredit ? "text-green-600" : "text-red-600"}`}
            />
            <div className="flex flex-col">
              <Badge
                variant={isCredit ? "default" : "destructive"}
                className={
                  isCredit
                    ? "bg-green-100 text-green-800"
                    : "bg-red-100 text-red-800"
                }
              >
                {type}
              </Badge>
              <span
                className={`font-medium text-sm ${isCredit ? "text-green-600" : "text-red-600"}`}
              >
                {abbreviateNumber(amount)}
              </span>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status") as string;
        const statusColors: Record<string, string> = {
          ACTIVE: "bg-green-100 text-green-800",
          PENDING: "bg-yellow-100 text-yellow-800",
          COMPLETED: "bg-blue-100 text-blue-800",
          CANCELLED: "bg-red-100 text-red-800",
          INACTIVE: "bg-gray-100 text-gray-800",
        };

        return (
          <Badge
            variant="secondary"
            className={statusColors[status] || "bg-gray-100 text-gray-800"}
          >
            {status || "PENDING"}
          </Badge>
        );
      },
    },
    {
      accessorKey: "created_at",
      header: "Created",
      cell: ({ row }) => {
        const date = row.getValue("created_at") as string;
        return (
          <div className="flex items-center gap-1 text-sm text-gray-600">
            <Calendar className="h-3 w-3" />
            {date ? new Date(date).toLocaleDateString() : "N/A"}
          </div>
        );
      },
    },
    {
      accessorKey: "tags",
      header: "Tags",
      cell: ({ row }) => {
        const tags = row.getValue("tags") as string[];
        if (!tags || tags.length === 0) {
          return <span className="text-gray-400 text-sm">No tags</span>;
        }

        return (
          <div className="flex flex-wrap gap-1">
            {tags.slice(0, 2).map((tag, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
            {tags.length > 2 && (
              <Badge variant="secondary" className="text-xs">
                +{tags.length - 2}
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "",
      cell: ({ row }) => {
        const transaction = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0 hover:bg-gray-100"
                onClick={(e) => e.stopPropagation()}
              >
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  onViewTransaction(transaction);
                }}
                className="cursor-pointer"
              >
                <ReceiptText className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  onEditTransaction(transaction);
                }}
                className="cursor-pointer"
              >
                <Edit className="mr-2 h-4 w-4" />
                Edit Transaction
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteTransaction(transaction);
                }}
                className="cursor-pointer text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Transaction
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];
}

/**
 * Convert TanStack React Table columns to Listing.Table format
 *
 * The Listing.Table component expects a simpler column format than TanStack React Table.
 * This function converts the TanStack ColumnDef format to the expected format.
 */
export function convertToListingTableColumns({
  onViewTransaction,
  onEditTransaction,
  onDeleteTransaction,
}: TransactionTableColumnsProps): ListingTableColumn[] {
  return [
    {
      key: "ledgers",
      label: "Ledger",
      render: (transaction: TransactionWithAssociations) => {
        const ledger = (transaction as any).ledgers;
        return (
          <div
            className="flex flex-col"
            onClick={() => {
              redirect(`/finance/${ledger?.id}`);
            }}
          >
            <span className="font-medium text-primary hover:underline cursor-pointer">
              {ledger?.name || "No Ledger"}
            </span>
            {transaction.book && (
              <small className="text-sm text-gray-500">
                Book: {transaction.book}
              </small>
            )}
          </div>
        );
      },
    },
    {
      key: "name",
      label: "Transaction Name",
      render: (transaction: TransactionWithAssociations) => (
        <div className="flex flex-col">
          <span className="font-medium text-gray-900">
            {transaction.name || "Unnamed Transaction"}
          </span>
          {transaction.context && (
            <span className="text-sm text-gray-500 truncate max-w-xs">
              {transaction.context}
            </span>
          )}
        </div>
      ),
    },
    {
      key: "type",
      label: "Type",
      render: (transaction: TransactionWithAssociations) => {
        const isCredit = transaction.type === "CREDIT";
        const type = isCredit ? "CREDIT" : "DEBIT";

        return (
          <Badge
            variant={!isCredit ? "default" : "destructive"}
            className={
              !isCredit
                ? "bg-green-50/50 text-green-800 border border-green-200"
                : "bg-red-50/50 text-red-800 border border-red-200"
            }
          >
            {type}
          </Badge>
        );
      },
    },
    {
      key: "amount",
      label: "Amount",
      render: (transaction: TransactionWithAssociations) => {
        const amount = transaction.amount;
        const isCredit = transaction.type === "CREDIT";

        return (
          <span
            className={`font-medium text-sm ${!isCredit ? "text-green-600" : "text-red-600"}`}
          >
            ${abbreviateNumber(amount)}
          </span>
        );
      },
    },
    {
      key: "status",
      label: "Status",
      render: (transaction: TransactionWithAssociations) => {
        const status = transaction.status;
        const statusColors: Record<string, string> = {
          ACTIVE: "bg-green-100 text-green-800",
          PENDING: "bg-yellow-100 text-yellow-800",
          COMPLETED: "bg-blue-100 text-blue-800",
          CANCELLED: "bg-red-100 text-red-800",
          INACTIVE: "bg-gray-100 text-gray-800",
        };

        return (
          <Badge
            variant="secondary"
            className={statusColors[status] || "bg-gray-100 text-gray-800"}
          >
            {status || "PENDING"}
          </Badge>
        );
      },
    },
    {
      key: "created_at",
      label: "Created",
      render: (transaction: TransactionWithAssociations) => {
        const date = transaction.created_at;
        return (
          <div className="flex items-center gap-1 text-sm text-gray-600">
            <Calendar className="h-3 w-3" />
            {date ? new Date(date).toLocaleDateString() : "N/A"}
          </div>
        );
      },
    },
    {
      key: "tags",
      label: "Tags",
      render: (transaction: TransactionWithAssociations) => {
        const tags = transaction.tags;
        if (!tags || tags.length === 0) {
          return <span className="text-gray-400 text-sm">No tags</span>;
        }

        return (
          <div className="flex flex-wrap gap-1">
            {tags.slice(0, 2).map((tag, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
            {tags.length > 2 && (
              <Badge variant="secondary" className="text-xs">
                +{tags.length - 2}
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      key: "actions",
      label: "",
      render: (transaction: TransactionWithAssociations) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="h-8 w-8 p-0 hover:bg-gray-100"
              onClick={(e) => e.stopPropagation()}
            >
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                onViewTransaction(transaction);
              }}
              className="cursor-pointer"
            >
              <ReceiptText className="mr-2 h-4 w-4" />
              View Details
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                onEditTransaction(transaction);
              }}
              className="cursor-pointer"
            >
              <Edit className="mr-2 h-4 w-4" />
              Edit Transaction
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                onDeleteTransaction(transaction);
              }}
              className="cursor-pointer text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete Transaction
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];
}
