"use client";

import { memo, useState } from "react";
import { AnimatedCard, AnimatedCardGrid } from "@/components/animated-card";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from "recharts";
import {
  Bar<PERSON>hart3,
  DollarSign,
  <PERSON><PERSON>dingUp,
  <PERSON><PERSON>hart,
  FileText,
  AlertCircle,
  CheckCircle2,
  Loader2,
} from "lucide-react";
import { AutocompleteOption } from "@/components/ui/filter-panel";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { ChartWrapper } from "@/lib/chart-utils";
import {
  FINANCE_ENTITIES,
  OPERATIONS_ENTITIES,
  fetchEntityInstances,
  type EntityInstance,
} from "@/lib/constants/entity-types";

/**
 * Finance Cost Center Component
 *
 * Displays cost center analysis and management.
 * This is a placeholder component that can be expanded with actual cost center data.
 */

// Colors for charts
const COLORS = [
  "#0088FE",
  "#00C49F",
  "#FFBB28",
  "#FF8042",
  "#8884d8",
  "#82ca9d",
];

export const FinanceCostCenter = memo(() => {
  const [selectedEntityType, setSelectedEntityType] = useState<string>("");
  const [selectedEntityId, setSelectedEntityId] = useState<string>("");
  const [entities, setEntities] = useState<AutocompleteOption[]>([]);
  const [entitiesLoading, setEntitiesLoading] = useState(false);
  const [analysisData, setAnalysisData] = useState<any>(null);
  const [analysisLoading, setAnalysisLoading] = useState(false);
  const [insights, setInsights] = useState<any>(null);
  const [searchQuery, setSearchQuery] = useState<string>("");

  // Fetch entities for selected type using the dynamic fetcher
  const fetchEntitiesForType = async (entityType: string) => {
    setEntitiesLoading(true);
    try {
      const response = await fetchEntityInstances(entityType, { limit: 100 });
      if (response.success) {
        // Convert EntityInstance[] to AutocompleteOption[] format
        const autocompleteOptions: AutocompleteOption[] = response.data.map(
          (entity: EntityInstance) => ({
            id: entity.id,
            label: entity.label,
            description: entity.description,
            metadata: entity.metadata,
          })
        );
        setEntities(autocompleteOptions);
      } else {
        console.error("Error fetching entities:", response.error);
        setEntities([]);
      }
    } catch (error) {
      console.error("Error fetching entities:", error);
      setEntities([]);
    } finally {
      setEntitiesLoading(false);
    }
  };

  const handleEntityTypeChange = (entityType: string) => {
    setSelectedEntityType(entityType);
    setSelectedEntityId("");
    setEntities([]);
    setAnalysisData(null);
    setInsights(null);
    if (entityType) {
      fetchEntitiesForType(entityType);
    }
  };

  const handleEntitySelect = async (option: AutocompleteOption) => {
    setSelectedEntityId(option.id);
    setAnalysisLoading(true);

    try {
      // Import cost center service
      const { costCenterService } = await import("@/lib/logistics/cost-center");

      // Get cost center analysis
      const analysisResult = await costCenterService.getCostCenterAnalysis(
        selectedEntityType,
        option.id
      );

      if (analysisResult.success) {
        setAnalysisData(analysisResult.data);

        // Get insights
        const insightsResult = await costCenterService.getCostCenterInsights(
          selectedEntityType,
          option.id
        );

        if (insightsResult.success) {
          setInsights(insightsResult.data);
        }
      }
    } catch (error) {
      console.error("Error fetching cost center analysis:", error);
    } finally {
      setAnalysisLoading(false);
    }
  };

  const handleAutocompleteSearch = (query: string) => {
    setSearchQuery(query);
    // Could implement server-side search here if needed
  };

  return (
    <div className="space-y-6">
      {/* Cost Center Entity Selector */}
      <div className="bg-gradient-to-r from-zinc-50 to-slate-50 border border-zinc-200 rounded-lg p-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">
              Cost Center Analysis
            </h3>
            <span className="text-sm text-gray-600 font-medium">
              Select entity for detailed insights
            </span>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Entity Type Selection */}
            <div className="w-full flex flex-row items-center gap-8">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Entity Type
                </label>
                <Select
                  value={selectedEntityType}
                  onValueChange={handleEntityTypeChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select entity type" />
                  </SelectTrigger>
                  <SelectContent>
                    {/* Finance entities */}
                    {FINANCE_ENTITIES.map((entity) => (
                      <SelectItem key={entity.value} value={entity.value}>
                        {entity.label}
                      </SelectItem>
                    ))}
                    {/* Operations entities relevant to finance */}
                    {OPERATIONS_ENTITIES.filter((entity) =>
                      [
                        "customers",
                        "suppliers",
                        "batches",
                        "freights",
                        "shipments",
                      ].includes(entity.value)
                    ).map((entity) => (
                      <SelectItem key={entity.value} value={entity.value}>
                        {entity.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Entity Instance Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Entity Instance
                </label>
                <Select
                  value={selectedEntityId}
                  onValueChange={(value) => {
                    const entity = entities.find((e) => e.id === value);
                    if (entity) {
                      handleEntitySelect(entity);
                    }
                  }}
                  disabled={!selectedEntityType || entitiesLoading}
                >
                  <SelectTrigger>
                    <SelectValue
                      placeholder={
                        entitiesLoading
                          ? "Loading..."
                          : !selectedEntityType
                            ? "Select entity type first"
                            : entities.length === 0
                              ? "No entities found"
                              : "Select entity instance"
                      }
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {entities.map((entity) => (
                      <SelectItem key={entity.id} value={entity.id}>
                        <div className="flex flex-row items-center gap-4">
                          <span className="font-medium">{entity.label}</span>
                          {entity.description && (
                            <span className="text-xs text-gray-500">
                              {entity.description}
                            </span>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Selected Entity Info */}
            {selectedEntityId && (
              <div className="bg-gray-100 border border-slate-300 rounded-lg p-4">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm font-medium text-blue-900">
                    Analyzing:{" "}
                    {entities.find((e) => e.id === selectedEntityId)?.label}
                  </span>
                </div>
                <p className="text-xs text-blue-700 mt-1">
                  Financial data and transactions will be filtered for this
                  entity.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Analysis Results */}
      {analysisLoading && (
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2 text-gray-600">
            Analyzing cost center data...
          </span>
        </div>
      )}

      {analysisData && !analysisLoading && (
        <CostCenterDashboard analysisData={analysisData} insights={insights} />
      )}

      {!selectedEntityId && !analysisLoading && (
        <div className="text-center py-12">
          <div className="bg-gray-50 rounded-lg p-8">
            <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Select an Entity for Cost Center Analysis
            </h3>
            <p className="text-gray-600">
              Choose an entity type and specific instance to view detailed
              financial insights, KPIs, and performance analytics.
            </p>
          </div>
        </div>
      )}
    </div>
  );
});

function CostCenterDashboard({
  analysisData,
  insights,
}: {
  analysisData: any;
  insights: any;
}) {
  if (!analysisData) {
    return (
      <div className="space-y-6">
        <CostCenterSkeleton />
      </div>
    );
  }

  const { kpis, transactions, ledgers, invoices, charts, entityInfo } =
    analysisData;

  // Handle null/empty data gracefully
  const hasKpis = kpis && Object.keys(kpis).length > 0;
  const hasTransactions =
    transactions &&
    transactions.transactions &&
    transactions.transactions.length > 0;
  const hasLedgers = ledgers && ledgers.ledgers && ledgers.ledgers.length > 0;
  const hasInvoices =
    invoices && invoices.invoices && invoices.invoices.length > 0;
  const hasCharts = charts && Object.keys(charts).length > 0;

  return (
    <div className="space-y-6">
      {/* Entity Header */}
      <div className="bg-white rounded-lg border shadow-sm p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              {entityInfo?.name || "Unknown Entity"}
            </h2>
            <p className="text-gray-600 capitalize">
              {entityInfo?.type || "Unknown"} Cost Center Analysis
            </p>
          </div>
          {insights && (
            <div className="text-right">
              <div className="text-sm text-gray-600">Performance Score</div>
              <div
                className={`text-2xl font-bold ${
                  insights.performanceScore >= 80
                    ? "text-green-600"
                    : insights.performanceScore >= 60
                      ? "text-yellow-600"
                      : "text-red-600"
                }`}
              >
                {insights.performanceScore || 0}/100
              </div>
            </div>
          )}
        </div>
      </div>

      {/* KPI Cards */}
      {hasKpis ? (
        <AnimatedCardGrid className="grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
          <AnimatedCard className="p-6">
            <div className="flex items-center gap-4">
              <div className="bg-green-500/10 p-3 rounded-full">
                <DollarSign className="h-6 w-6 text-green-500" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Revenue</p>
                <h3 className="text-2xl font-bold text-gray-900">
                  ${(kpis.totalRevenue || 0).toLocaleString()}
                </h3>
                <p className="text-xs text-green-600">Debit transactions</p>
              </div>
            </div>
          </AnimatedCard>

          <AnimatedCard className="p-6">
            <div className="flex items-center gap-4">
              <div className="bg-red-500/10 p-3 rounded-full">
                <TrendingUp className="h-6 w-6 text-red-500" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Expenses</p>
                <h3 className="text-2xl font-bold text-gray-900">
                  ${(kpis.totalExpenses || 0).toLocaleString()}
                </h3>
                <p className="text-xs text-red-600">Credit transactions</p>
              </div>
            </div>
          </AnimatedCard>

          <AnimatedCard className="p-6">
            <div className="flex items-center gap-4">
              <div className="bg-blue-500/10 p-3 rounded-full">
                <PieChart className="h-6 w-6 text-blue-500" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Profit Margin</p>
                <h3
                  className={`text-2xl font-bold ${
                    (kpis.profitMargin || 0) >= 0
                      ? "text-green-600"
                      : "text-red-600"
                  }`}
                >
                  {(kpis.profitMargin || 0).toFixed(1)}%
                </h3>
                <p className="text-xs text-gray-600">Revenue efficiency</p>
              </div>
            </div>
          </AnimatedCard>

          <AnimatedCard className="p-6">
            <div className="flex items-center gap-4">
              <div className="bg-purple-500/10 p-3 rounded-full">
                <BarChart3 className="h-6 w-6 text-purple-500" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Net Surplus</p>
                <h3
                  className={`text-2xl font-bold ${
                    (kpis.netSurplus || 0) >= 0
                      ? "text-green-600"
                      : "text-red-600"
                  }`}
                >
                  ${(kpis.netSurplus || 0).toLocaleString()}
                </h3>
                <p className="text-xs text-gray-600">Revenue - Expenses</p>
              </div>
            </div>
          </AnimatedCard>
        </AnimatedCardGrid>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <KpiCardPlaceholder
            title="Total Revenue"
            icon={DollarSign}
            color="green"
          />
          <KpiCardPlaceholder
            title="Total Expenses"
            icon={TrendingUp}
            color="red"
          />
          <KpiCardPlaceholder
            title="Profit Margin"
            icon={PieChart}
            color="blue"
          />
          <KpiCardPlaceholder
            title="Net Surplus"
            icon={BarChart3}
            color="purple"
          />
        </div>
      )}

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Revenue vs Expenses Comparison - Perpendicular Alignment */}
        <AnimatedCard className="p-6">
          <h3 className="text-lg font-semibold mb-4">Revenue vs Expenses</h3>
          {hasTransactions ? (
            <ChartWrapper
              data={[
                {
                  name: "Revenue (Debit)",
                  value: transactions.totalDebit || 0,
                },
                {
                  name: "Expenses (Credit)",
                  value: transactions.totalCredit || 0,
                },
              ]}
              chartType="bar"
              context="transactions"
              height={300}
            >
              <ResponsiveContainer width="100%" height={300}>
                <BarChart
                  data={[
                    {
                      name: "Revenue (Debit)",
                      value: transactions.totalDebit || 0,
                      color: "#10B981",
                    },
                    {
                      name: "Expenses (Credit)",
                      value: transactions.totalCredit || 0,
                      color: "#EF4444",
                    },
                  ]}
                  layout="horizontal"
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis type="category" dataKey="name" />
                  <Tooltip
                    formatter={(value) => `$${value.toLocaleString()}`}
                  />
                  <Bar dataKey="value" fill="#8884d8">
                    <Cell fill="#10B981" />
                    <Cell fill="#EF4444" />
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </ChartWrapper>
          ) : (
            <div className="h-[300px] flex items-center justify-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
              <div className="text-center">
                <BarChart3 className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500">
                  No transaction data available
                </p>
              </div>
            </div>
          )}
        </AnimatedCard>

        {/* Revenue vs Expenses Chart */}
        <AnimatedCard className="p-6">
          <h3 className="text-lg font-semibold mb-4">Revenue vs Expenses</h3>
          {hasCharts && charts.revenueVsExpenses ? (
            <ChartWrapper
              data={charts.revenueVsExpenses}
              chartType="bar"
              context="revenue"
              height={300}
            >
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={charts.revenueVsExpenses}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip
                    formatter={(value) => `$${value.toLocaleString()}`}
                  />
                  <Bar dataKey="revenue" fill="#10B981" name="Revenue" />
                  <Bar dataKey="expenses" fill="#EF4444" name="Expenses" />
                </BarChart>
              </ResponsiveContainer>
            </ChartWrapper>
          ) : (
            <div className="h-[300px] flex items-center justify-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
              <div className="text-center">
                <BarChart3 className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500">
                  No revenue data available
                </p>
              </div>
            </div>
          )}
        </AnimatedCard>

        {/* Transaction Distribution */}
        <AnimatedCard className="p-6">
          <h3 className="text-lg font-semibold mb-4">
            Transaction Distribution
          </h3>
          {hasCharts && charts.transactionDistribution ? (
            <ChartWrapper
              data={charts.transactionDistribution}
              chartType="pie"
              context="transactions"
              height={300}
            >
              <ResponsiveContainer width="100%" height={300}>
                <RePieChart>
                  <Pie
                    data={charts.transactionDistribution}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, value }) =>
                      `${name}: $${value.toLocaleString()}`
                    }
                  >
                    {charts.transactionDistribution.map(
                      (entry: any, index: number) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={entry.color || COLORS[index % COLORS.length]}
                        />
                      )
                    )}
                  </Pie>
                  <Tooltip
                    formatter={(value) => `$${value.toLocaleString()}`}
                  />
                </RePieChart>
              </ResponsiveContainer>
            </ChartWrapper>
          ) : (
            <div className="h-[300px] flex items-center justify-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
              <div className="text-center">
                <PieChart className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500">
                  No distribution data available
                </p>
              </div>
            </div>
          )}
        </AnimatedCard>
      </div>

      {/* Insights and Recommendations */}
      {insights && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Insights */}
          <AnimatedCard className="p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <CheckCircle2 className="h-5 w-5 text-green-500" />
              Key Insights
            </h3>
            <div className="space-y-2">
              {insights.insights.map((insight: string, index: number) => (
                <div
                  key={index}
                  className="text-sm text-gray-700 bg-green-50 p-3 rounded-lg"
                >
                  {insight}
                </div>
              ))}
            </div>
          </AnimatedCard>

          {/* Recommendations */}
          <AnimatedCard className="p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-blue-500" />
              Recommendations
            </h3>
            <div className="space-y-2">
              {insights.recommendations.map(
                (recommendation: string, index: number) => (
                  <div
                    key={index}
                    className="text-sm text-gray-700 bg-blue-50 p-3 rounded-lg"
                  >
                    {recommendation}
                  </div>
                )
              )}
            </div>
          </AnimatedCard>

          {/* Risk Factors */}
          <AnimatedCard className="p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-red-500" />
              Risk Factors
            </h3>
            <div className="space-y-2">
              {insights.riskFactors.length > 0 ? (
                insights.riskFactors.map((risk: string, index: number) => (
                  <div
                    key={index}
                    className="text-sm text-gray-700 bg-red-50 p-3 rounded-lg"
                  >
                    {risk}
                  </div>
                ))
              ) : (
                <div className="text-sm text-gray-500 bg-gray-50 p-3 rounded-lg">
                  No significant risk factors identified
                </div>
              )}
            </div>
          </AnimatedCard>
        </div>
      )}

      {/* Detailed Lists */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Comprehensive Transactions List */}
        <AnimatedCard className="p-6">
          <h3 className="text-lg font-semibold mb-4">
            All Related Transactions
            <span className="text-sm font-normal text-gray-500 ml-2">
              (Entity, Ledger & Invoice)
            </span>
          </h3>
          {hasTransactions ? (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {transactions.transactions
                .slice(0, 15)
                .map((transaction: any) => (
                  <div
                    key={transaction.id}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="flex-1">
                      <div className="font-medium text-sm">
                        {transaction.name ||
                          transaction.description ||
                          "Transaction"}
                      </div>
                      <div className="flex items-center gap-2 mt-1">
                        <div className="text-xs text-gray-500">
                          {new Date(
                            transaction.created_at
                          ).toLocaleDateString()}
                        </div>
                        {transaction.ledgers?.name && (
                          <>
                            <span className="text-xs text-gray-400">•</span>
                            <div className="text-xs text-blue-600 bg-blue-50 px-2 py-0.5 rounded">
                              {transaction.ledgers.name}
                            </div>
                          </>
                        )}
                        {transaction.associated_table && (
                          <>
                            <span className="text-xs text-gray-400">•</span>
                            <div className="text-xs text-purple-600 bg-purple-50 px-2 py-0.5 rounded capitalize">
                              {transaction.associated_table === "invoices"
                                ? "Invoice Payment"
                                : transaction.associated_table}
                            </div>
                          </>
                        )}
                      </div>
                      {transaction.context && (
                        <div className="text-xs text-gray-400 mt-1 truncate">
                          {transaction.context}
                        </div>
                      )}
                    </div>
                    <div className="flex flex-col items-end">
                      <div
                        className={`font-semibold ${
                          transaction.type === "CREDIT"
                            ? "text-red-600"
                            : "text-green-600"
                        }`}
                      >
                        {transaction.type === "CREDIT" ? "-" : "+"}$
                        {transaction.amount?.toLocaleString() || "0"}
                      </div>
                      <div
                        className={`text-xs px-2 py-0.5 rounded-full ${
                          transaction.status === "COMPLETED"
                            ? "bg-green-100 text-green-800"
                            : transaction.status === "PENDING"
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {transaction.status || "UNKNOWN"}
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          ) : (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="bg-gray-100 rounded-full p-3 w-12 h-12 mx-auto mb-3">
                  <DollarSign className="h-6 w-6 text-gray-400" />
                </div>
                <p className="text-sm text-gray-500 font-medium">
                  No transactions found
                </p>
                <p className="text-xs text-gray-400 mt-1">
                  Transactions will appear here once the entity has financial
                  activity
                </p>
              </div>
            </div>
          )}
        </AnimatedCard>

        {/* Ledgers List */}
        <AnimatedCard className="p-6">
          <h3 className="text-lg font-semibold mb-4">Associated Ledgers</h3>
          {hasLedgers ? (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {ledgers.ledgers.map((ledger: any) => (
                <div key={ledger.id} className="p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <div className="font-medium text-sm">{ledger.name}</div>
                    <div
                      className={`text-xs px-2 py-1 rounded-full ${
                        ledger.status === "ACTIVE"
                          ? "bg-green-100 text-green-800"
                          : "bg-gray-100 text-gray-800"
                      }`}
                    >
                      {ledger.status}
                    </div>
                  </div>
                  <div className="flex justify-between text-xs text-gray-600">
                    <span>
                      Credit: ${(ledger.total_credit || 0).toLocaleString()}
                    </span>
                    <span>
                      Debit: ${(ledger.total_debit || 0).toLocaleString()}
                    </span>
                    <span
                      className={`font-medium ${
                        (ledger.total_credit || 0) -
                          (ledger.total_debit || 0) >=
                        0
                          ? "text-green-600"
                          : "text-red-600"
                      }`}
                    >
                      Net: $
                      {(
                        (ledger.total_credit || 0) - (ledger.total_debit || 0)
                      ).toLocaleString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="bg-gray-100 rounded-full p-3 w-12 h-12 mx-auto mb-3">
                  <BarChart3 className="h-6 w-6 text-gray-400" />
                </div>
                <p className="text-sm text-gray-500 font-medium">
                  No ledgers found
                </p>
                <p className="text-xs text-gray-400 mt-1">
                  Ledgers will be created automatically when financial activity
                  occurs
                </p>
              </div>
            </div>
          )}
        </AnimatedCard>

        {/* Related Invoices List */}
        <AnimatedCard className="p-6">
          <h3 className="text-lg font-semibold mb-4">Related Invoices</h3>
          {hasInvoices ? (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {invoices.invoices.slice(0, 10).map((invoice: any) => (
                <div key={invoice.id} className="p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <div className="font-medium text-sm">
                      {invoice.inv_number ||
                        `Invoice ${invoice.id.slice(0, 8)}`}
                    </div>
                    <div
                      className={`text-xs px-2 py-1 rounded-full ${
                        invoice.status === "PAID"
                          ? "bg-green-100 text-green-800"
                          : invoice.status === "PENDING"
                            ? "bg-yellow-100 text-yellow-800"
                            : invoice.status === "OVERDUE"
                              ? "bg-red-100 text-red-800"
                              : "bg-gray-100 text-gray-800"
                      }`}
                    >
                      {invoice.status || "UNKNOWN"}
                    </div>
                  </div>
                  <div className="flex justify-between items-center text-xs text-gray-600">
                    <span>
                      {invoice.customer?.name ||
                        invoice.supplier?.tracking_number ||
                        "Unknown"}
                    </span>
                    <span className="font-medium text-gray-900">
                      ${(invoice.total || 0).toLocaleString()}
                    </span>
                  </div>
                  {invoice.due_at && (
                    <div className="text-xs text-gray-500 mt-1">
                      Due: {new Date(invoice.due_at).toLocaleDateString()}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="bg-gray-100 rounded-full p-3 w-12 h-12 mx-auto mb-3">
                  <FileText className="h-6 w-6 text-gray-400" />
                </div>
                <p className="text-sm text-gray-500 font-medium">
                  No invoices found
                </p>
                <p className="text-xs text-gray-400 mt-1">
                  Invoices will appear here when created for this entity
                </p>
              </div>
            </div>
          )}
        </AnimatedCard>
      </div>
    </div>
  );
}

// Skeleton and Placeholder Components
function CostCenterSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header Skeleton */}
      <div className="bg-white rounded-lg border shadow-sm p-6">
        <div className="flex items-center justify-between">
          <div>
            <div className="h-8 w-48 bg-gray-200 rounded animate-pulse mb-2"></div>
            <div className="h-4 w-32 bg-gray-200 rounded animate-pulse"></div>
          </div>
          <div className="text-right">
            <div className="h-4 w-24 bg-gray-200 rounded animate-pulse mb-2"></div>
            <div className="h-8 w-16 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* KPI Cards Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <div key={index} className="bg-white rounded-lg border shadow-sm p-6">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gray-200 rounded-full animate-pulse"></div>
              <div className="flex-1">
                <div className="h-4 w-20 bg-gray-200 rounded animate-pulse mb-2"></div>
                <div className="h-6 w-16 bg-gray-200 rounded animate-pulse mb-1"></div>
                <div className="h-3 w-24 bg-gray-200 rounded animate-pulse"></div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Skeleton */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className="bg-white rounded-lg border shadow-sm p-6">
            <div className="h-6 w-32 bg-gray-200 rounded animate-pulse mb-4"></div>
            <div className="h-[300px] bg-gray-100 rounded animate-pulse"></div>
          </div>
        ))}
      </div>
    </div>
  );
}

interface KpiCardPlaceholderProps {
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  color: "green" | "red" | "blue" | "purple";
}

function KpiCardPlaceholder({
  title,
  icon: Icon,
  color,
}: KpiCardPlaceholderProps) {
  const colorClasses = {
    green: "bg-green-500/10 text-green-500",
    red: "bg-red-500/10 text-red-500",
    blue: "bg-blue-500/10 text-blue-500",
    purple: "bg-purple-500/10 text-purple-500",
  };

  return (
    <div className="bg-white rounded-lg border shadow-sm p-6">
      <div className="flex items-center gap-4">
        <div className={`p-3 rounded-full ${colorClasses[color]}`}>
          <Icon className="h-6 w-6" />
        </div>
        <div>
          <p className="text-sm text-gray-600">{title}</p>
          <h3 className="text-2xl font-bold text-gray-400">--</h3>
          <p className="text-xs text-gray-400">No data available</p>
        </div>
      </div>
    </div>
  );
}

FinanceCostCenter.displayName = "FinanceCostCenter";
