"use client";

import { memo } from "react";
import {
  DollarSign,
  Calendar,
  Tag,
  CheckCircle,
  XCircle,
  Clock,
  ArrowUpRight,
  ArrowDownLeft,
  Building,
} from "lucide-react";
import { Badge } from "@workspace/ui/components/badge";
import { Listing } from "@/modules/listing";
import { type TransactionWithAssociations } from "@/lib/logistics/operations/transactions";

interface TransactionCardsProps {
  transactions: TransactionWithAssociations[];
  loading: boolean;
  emptyState: React.ReactNode;
  transactionStatuses: Array<{ key: string; label: string; count: number }>;
  onViewTransaction: (transaction: TransactionWithAssociations) => void;
}

/**
 * Transaction Cards Component
 *
 * Displays transactions in a card grid layout with animations and actions.
 * Follows the design patterns from other card components in the application.
 */
export const TransactionCards = memo<TransactionCardsProps>(
  ({
    transactions,
    loading,
    emptyState,
    transactionStatuses,
    onViewTransaction,
  }) => {
    // Helper functions for transaction styling
    const getTransactionStatusColor = (status: string): string => {
      const colors: Record<string, string> = {
        ACTIVE: "#10b981",
        PENDING: "#f59e0b",
        COMPLETED: "#3b82f6",
        CANCELLED: "#ef4444",
        INACTIVE: "#6b7280",
      };
      return colors[status] || "#6b7280";
    };

    const getTransactionStatusIcon = (status: string) => {
      const icons: Record<string, any> = {
        ACTIVE: CheckCircle,
        PENDING: Clock,
        COMPLETED: CheckCircle,
        CANCELLED: XCircle,
        INACTIVE: Clock,
      };
      return icons[status] || DollarSign;
    };

    const getTransactionTypeColor = (type: string): string => {
      return type === "CREDIT" ? "#10b981" : "#ef4444";
    };

    const getTransactionTypeIcon = (type: string) => {
      return type === "CREDIT" ? ArrowUpRight : ArrowDownLeft;
    };

    return (
      <Listing.Cards
        data={transactions}
        loading={loading}
        emptyState={emptyState}
        columns="grid-cols-3"
        groupByCategory={true}
        categories={transactionStatuses.map((status) => ({
          key: status.key,
          name: status.label,
          description: `${status.count} transactions`,
          color: getTransactionStatusColor(status.key.toUpperCase()),
          icon: getTransactionStatusIcon(status.key.toUpperCase()),
        }))}
        getCategoryKey={(transaction) =>
          transaction.status?.toLowerCase() || "pending"
        }
        renderCard={(transaction) => {
          const StatusIcon = getTransactionStatusIcon(
            transaction.status?.toUpperCase() || "PENDING"
          );
          // Transaction type is determined by amount: positive = CREDIT, negative = DEBIT
          const transactionType = transaction.amount >= 0 ? "CREDIT" : "DEBIT";
          const TypeIcon = getTransactionTypeIcon(transactionType);
          const isCredit = transactionType === "CREDIT";

          return (
            <div
              className="p-4 cursor-pointer border border-gray-200 rounded-lg bg-zinc-50 hover:bg-zinc-100 transition-colors"
              onClick={() => onViewTransaction(transaction)}
            >
              {/* Header */}
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-sm text-gray-900 truncate">
                    {transaction.name || "Unnamed Transaction"}
                  </h3>
                  <p className="text-xs text-gray-500 mt-1 truncate">
                    {transaction.ledger?.name || "No Ledger"}
                  </p>
                </div>
                <div className="flex items-center gap-2 ml-2">
                  <div className="flex items-center gap-1">
                    <TypeIcon
                      className={`h-3 w-3 ${isCredit ? "text-green-600" : "text-red-600"}`}
                    />
                    <Badge
                      variant="secondary"
                      className={`text-xs ${isCredit ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}
                    >
                      {transactionType}
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Amount */}
              <div className="flex items-center gap-1 mb-3">
                <DollarSign className="h-4 w-4 text-gray-400" />
                <span
                  className={`text-lg font-bold ${isCredit ? "text-green-600" : "text-red-600"}`}
                >
                  {transaction.amount?.toLocaleString("en-US", {
                    style: "currency",
                    currency: "USD",
                  }) || "$0.00"}
                </span>
              </div>

              {/* Status */}
              <div className="flex items-center gap-2 mb-3">
                <StatusIcon
                  className="h-4 w-4"
                  style={{
                    color: getTransactionStatusColor(
                      transaction.status?.toUpperCase() || "PENDING"
                    ),
                  }}
                />
                <Badge
                  variant="secondary"
                  style={{
                    backgroundColor: `${getTransactionStatusColor(transaction.status?.toUpperCase() || "PENDING")}20`,
                    color: getTransactionStatusColor(
                      transaction.status?.toUpperCase() || "PENDING"
                    ),
                  }}
                >
                  {transaction.status || "PENDING"}
                </Badge>
              </div>

              {/* Context */}
              {transaction.context && (
                <div className="mb-3">
                  <p className="text-sm text-gray-600 line-clamp-2">
                    {transaction.context}
                  </p>
                </div>
              )}

              {/* Book */}
              {transaction.book && (
                <div className="flex items-center gap-1 mb-3">
                  <Building className="h-3 w-3 text-gray-400" />
                  <span className="text-sm text-gray-600">
                    Book: {transaction.book}
                  </span>
                </div>
              )}

              {/* Tags */}
              {transaction.tags && transaction.tags.length > 0 && (
                <div className="flex items-center gap-1 mb-3">
                  <Tag className="h-3 w-3 text-gray-400" />
                  <div className="flex flex-wrap gap-1">
                    {transaction.tags.slice(0, 2).map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {transaction.tags.length > 2 && (
                      <Badge variant="outline" className="text-xs">
                        +{transaction.tags.length - 2}
                      </Badge>
                    )}
                  </div>
                </div>
              )}

              {/* Associated Entity */}
              {transaction.associated_entity && (
                <div className="mb-3">
                  <p className="text-xs text-gray-500">
                    Related to: {transaction.associated_entity.type} -{" "}
                    {transaction.associated_entity.label}
                  </p>
                </div>
              )}

              {/* Footer */}
              <div className="flex items-center justify-between pt-3 border-t border-gray-100">
                <div className="flex items-center gap-1 text-xs text-gray-500">
                  <Calendar className="h-3 w-3" />
                  {transaction.created_at
                    ? new Date(transaction.created_at).toLocaleDateString()
                    : "N/A"}
                </div>
                <div className="text-xs text-gray-400">
                  ID: {transaction.id.slice(-8)}
                </div>
              </div>
            </div>
          );
        }}
      />
    );
  }
);

TransactionCards.displayName = "TransactionCards";
