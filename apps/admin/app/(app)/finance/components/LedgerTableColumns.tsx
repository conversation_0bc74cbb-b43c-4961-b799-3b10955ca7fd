"use client";

import React from "react";
import { type ColumnDef } from "@tanstack/react-table";
import {
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Calendar,
  DollarSign,
  ExternalLink,
  TrendingUp,
  TrendingDown,
  ReceiptText,
  Activity,
} from "lucide-react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { type LedgerWithStats } from "@/lib/logistics";
import { abbreviateNumber } from "js-abbreviation-number";

interface LedgerTableColumnsProps {
  onViewLedger: (ledger: LedgerWithStats) => void;
  onEditLedger: (ledger: LedgerWithStats) => void;
  onDeleteLedger: (ledger: LedgerWithStats) => void;
  onOpenLedger?: (ledger: LedgerWithStats) => void;
}

// Column format for Listing.Table component
interface ListingTableColumn {
  key: string;
  label: string;
  render?: (item: any, index: number) => React.ReactNode;
  className?: string;
}

/**
 * Ledger Table Columns Configuration
 *
 * Defines the table columns for the ledgers table with three-dot dropdown actions.
 * Uses the standardized dropdown menu pattern instead of inline action buttons.
 */
export function getLedgerTableColumns({
  onViewLedger,
  onEditLedger,
  onDeleteLedger,
  onOpenLedger,
}: LedgerTableColumnsProps): ColumnDef<LedgerWithStats>[] {
  return [
    {
      accessorKey: "name",
      header: "Ledger Name",
      cell: ({ row }) => {
        const ledger = row.original;
        return (
          <div className="flex flex-col">
            <span className="font-medium text-gray-900">
              {ledger.name || "Unnamed Ledger"}
            </span>
            {ledger.code && (
              <span className="text-sm text-gray-500 truncate max-w-xs">
                Code: {ledger.code}
              </span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "category",
      header: "Category",
      cell: ({ row }) => {
        const category = row.getValue("category") as string | null;
        if (!category) {
          return (
            <Badge variant="outline" className="text-xs text-gray-400">
              Uncategorized
            </Badge>
          );
        }

        return (
          <Badge variant="outline" className="text-xs capitalize">
            {category.replace("_", " ")}
          </Badge>
        );
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status") as string;
        const statusConfig = {
          ACTIVE: { variant: "default" as const, label: "Active" },
          INACTIVE: { variant: "secondary" as const, label: "Inactive" },
          CLOSED: { variant: "destructive" as const, label: "Closed" },
        };

        const config = statusConfig[status as keyof typeof statusConfig] || {
          variant: "outline" as const,
          label: status || "Unknown",
        };

        return <Badge variant={config.variant}>{config.label}</Badge>;
      },
    },
    {
      accessorKey: "tags",
      header: "Tags",
      cell: ({ row }) => {
        const tags = row.getValue("tags") as string[] | null;
        if (!tags || tags.length === 0) {
          return <span className="text-gray-400 text-sm">No tags</span>;
        }

        return (
          <div className="flex flex-wrap gap-1">
            {tags.slice(0, 2).map((tag, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
            {tags.length > 2 && (
              <Badge variant="secondary" className="text-xs">
                +{tags.length - 2}
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "created_at",
      header: "Created",
      cell: ({ row }) => {
        const date = row.getValue("created_at") as string;
        return (
          <div className="flex items-center gap-1 text-sm text-gray-600">
            <Calendar className="h-3 w-3" />
            {date ? new Date(date).toLocaleDateString() : "N/A"}
          </div>
        );
      },
    },
    {
      accessorKey: "totalTransactions",
      header: "Transactions",
      cell: ({ row }) => {
        const ledger = row.original;
        const totalTransactions = ledger.totalTransactions || 0;
        return (
          <div className="flex items-center gap-1 text-sm text-gray-600">
            <Activity className="h-3 w-3" />
            {totalTransactions.toLocaleString()}
          </div>
        );
      },
    },
    {
      accessorKey: "totalRevenue",
      header: "Revenue",
      cell: ({ row }) => {
        const ledger = row.original;
        const totalRevenue = ledger.totalRevenue || 0;
        return (
          <div className="flex items-center gap-1 text-sm text-green-600">
            <TrendingUp className="h-3 w-3" />$
            {totalRevenue.toLocaleString(undefined, {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })}
          </div>
        );
      },
    },
    {
      accessorKey: "totalExpenses",
      header: "Expenses",
      cell: ({ row }) => {
        const ledger = row.original;
        const totalExpenses = ledger.totalExpenses || 0;
        return (
          <div className="flex items-center gap-1 text-sm text-red-600">
            <TrendingDown className="h-3 w-3" />$
            {totalExpenses.toLocaleString(undefined, {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })}
          </div>
        );
      },
    },
    {
      accessorKey: "balance",
      header: "Balance",
      cell: ({ row }) => {
        const ledger = row.original;
        const balance = ledger.balance || 0;
        const isPositive = balance >= 0;
        return (
          <div
            className={`flex items-center gap-1 text-sm font-medium ${isPositive ? "text-green-600" : "text-red-600"}`}
          >
            <DollarSign className="h-3 w-3" />
            {isPositive ? "+" : ""}$
            {balance.toLocaleString(undefined, {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "",
      cell: ({ row }) => {
        const ledger = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0 hover:bg-gray-100"
                onClick={(e) => e.stopPropagation()}
              >
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              {onOpenLedger && (
                <>
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation();
                      onOpenLedger(ledger);
                    }}
                    className="cursor-pointer"
                  >
                    <ExternalLink className="mr-2 h-4 w-4" />
                    Open Ledger
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                </>
              )}
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  onViewLedger(ledger);
                }}
                className="cursor-pointer"
              >
                <ReceiptText className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  onEditLedger(ledger);
                }}
                className="cursor-pointer"
              >
                <Edit className="mr-2 h-4 w-4" />
                Edit Ledger
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteLedger(ledger);
                }}
                className="cursor-pointer text-red-600 focus:text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Ledger
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];
}

/**
 * Convert TanStack React Table columns to Listing.Table format
 *
 * The Listing.Table component expects a simpler column format than TanStack React Table.
 * This function converts the TanStack ColumnDef format to the expected format.
 */
export function convertToListingTableColumns({
  onViewLedger,
  onEditLedger,
  onDeleteLedger,
  onOpenLedger,
}: LedgerTableColumnsProps): ListingTableColumn[] {
  return [
    {
      key: "name",
      label: "Ledger Name",
      render: (ledger: LedgerWithStats) => {
        console.log("Rendering ledger name:", {
          ledger,
          name: ledger?.name,
          keys: Object.keys(ledger || {}),
        });
        return (
          <div className="flex flex-col">
            <span className="font-medium text-gray-900">
              {ledger?.name || "Unnamed Ledger"}
            </span>
            {ledger?.code && (
              <span className="text-sm text-gray-500 truncate max-w-xs">
                Code: {ledger.code}
              </span>
            )}
          </div>
        );
      },
    },
    {
      key: "category",
      label: "Category",
      render: (ledger: LedgerWithStats) => {
        const category = ledger.category;
        if (!category) {
          return (
            <Badge variant="outline" className="text-xs text-gray-400">
              Uncategorized
            </Badge>
          );
        }

        return (
          <Badge variant="outline" className="text-xs capitalize">
            {category.replace("_", " ")}
          </Badge>
        );
      },
    },
    {
      key: "status",
      label: "Status",
      render: (ledger: LedgerWithStats) => {
        const status = ledger.status;
        const statusConfig = {
          ACTIVE: { variant: "default" as const, label: "Active" },
          INACTIVE: { variant: "secondary" as const, label: "Inactive" },
          CLOSED: { variant: "destructive" as const, label: "Closed" },
        };

        const config = statusConfig[status as keyof typeof statusConfig] || {
          variant: "outline" as const,
          label: status || "Unknown",
        };

        return <Badge variant={config?.variant}>{config?.label}</Badge>;
      },
    },
    {
      key: "tags",
      label: "Tags",
      render: (ledger: LedgerWithStats) => {
        const tags = ledger.tags;
        if (!tags || tags.length === 0) {
          return <span className="text-gray-400 text-sm">No tags</span>;
        }

        return (
          <div className="flex flex-wrap gap-1">
            {tags.slice(0, 2).map((tag, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
            {tags.length > 2 && (
              <Badge variant="secondary" className="text-xs">
                +{tags.length - 2}
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      key: "created_at",
      label: "Created",
      render: (ledger: LedgerWithStats) => {
        const date = ledger.created_at;
        return (
          <div className="flex items-center gap-1 text-sm text-gray-600">
            <Calendar className="h-3 w-3" />
            {date ? new Date(date).toLocaleDateString() : "N/A"}
          </div>
        );
      },
    },
    {
      key: "totalTransactions",
      label: "Transactions",
      render: (ledger: LedgerWithStats) => {
        const totalTransactions = ledger.totalTransactions || 0;
        return (
          <div className="flex items-center gap-1 text-sm text-gray-600">
            <Activity className="h-3 w-3" />
            {abbreviateNumber(totalTransactions, 1)}
          </div>
        );
      },
    },
    {
      key: "totalRevenue",
      label: "Revenue",
      render: (ledger: LedgerWithStats) => {
        const totalRevenue = ledger.totalRevenue || 0;
        return (
          <div className="flex items-center gap-1 text-sm text-green-600">
            <TrendingUp className="h-3 w-3" />$
            {abbreviateNumber(totalRevenue, 1)}
          </div>
        );
      },
    },
    {
      key: "totalExpenses",
      label: "Expenses",
      render: (ledger: LedgerWithStats) => {
        const totalExpenses = ledger.totalExpenses || 0;
        return (
          <div className="flex items-center gap-1 text-sm text-red-600">
            <TrendingDown className="h-3 w-3" />$
            {abbreviateNumber(totalExpenses, 1)}
          </div>
        );
      },
    },
    {
      key: "balance",
      label: "Balance",
      render: (ledger: LedgerWithStats) => {
        const balance = ledger.balance || 0;
        const isPositive = balance >= 0;
        return (
          <div
            className={`flex items-center gap-1 text-sm font-medium ${isPositive ? "text-green-600" : "text-red-600"}`}
          >
            ${abbreviateNumber(balance, 1)}
          </div>
        );
      },
    },
    {
      key: "actions",
      label: "",
      render: (ledger: LedgerWithStats) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="h-8 w-8 p-0 hover:bg-gray-100"
              onClick={(e) => e.stopPropagation()}
            >
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            {onOpenLedger && (
              <>
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    onOpenLedger(ledger);
                  }}
                  className="cursor-pointer"
                >
                  <ExternalLink className="mr-2 h-4 w-4" />
                  Open Ledger
                </DropdownMenuItem>
                <DropdownMenuSeparator />
              </>
            )}
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                onViewLedger(ledger);
              }}
              className="cursor-pointer"
            >
              <ReceiptText className="mr-2 h-4 w-4" />
              View Details
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                onDeleteLedger(ledger);
              }}
              className="cursor-pointer text-red-600 focus:text-red-600"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete Ledger
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];
}
