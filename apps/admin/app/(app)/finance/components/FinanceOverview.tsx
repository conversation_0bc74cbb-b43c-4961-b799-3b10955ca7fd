"use client";

import { memo, useState, useEffect } from "react";
import {
  Clock,
  Filter,
  Loader2,
  Calendar,
  CreditCard,
  AlertCircle,
  CheckCircle2,
} from "lucide-react";

import { AnimatedCard } from "@/components/animated-card";

import {
  Pie,
  Bar,
  Cell,
  Area,
  XAxis,
  YAxis,
  Legend,
  Tooltip,
  BarChart,
  AreaChart,
  CartesianGrid,
  ResponsiveContainer,
  <PERSON><PERSON>hart as <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "recharts";
import {
  invoiceService,
  transactionService,
  type InvoiceWithRelations,
} from "@/lib/logistics";

import { ChartWrapper } from "@/lib/chart-utils";

// Colors for charts
const COLORS = [
  "#0088FE",
  "#00C49F",
  "#FFBB28",
  "#FF8042",
  "#8884d8",
  "#82ca9d",
];

/**
 * Finance Overview Component
 *
 * Displays financial overview with charts and key metrics.
 * This is a placeholder component that can be expanded with actual charts and data.
 */

export const FinanceOverview = memo(() => {
  const [timeframe, setTimeframe] = useState("month");
  const [loading, setLoading] = useState(true);
  const [revenueData, setRevenueData] = useState<
    Array<{ name: string; value: number }>
  >([]);
  const [expenseData, setExpenseData] = useState<
    Array<{ name: string; value: number }>
  >([]);
  const [expenseTrendData, setExpenseTrendData] = useState<
    Array<{
      name: string;
      operations: number;
      payroll: number;
      equipment: number;
    }>
  >([]);
  const [upcomingPayments, setUpcomingPayments] = useState<
    InvoiceWithRelations[]
  >([]);

  useEffect(() => {
    const fetchOverviewData = async () => {
      try {
        setLoading(true);

        // Fetch revenue data, transaction stats, and upcoming invoices
        // Note: Transaction stats automatically exclude INACTIVE transactions
        const [revenueResult, transactionStatsResult, upcomingInvoicesResult] =
          await Promise.all([
            transactionService.getTransactionStats(), // Excludes INACTIVE transactions
            transactionService.getTransactionStats(), // Excludes INACTIVE transactions
            invoiceService.getUpcomingInvoices(30),
          ]);

        // Process revenue data from transaction stats
        if (revenueResult.success && revenueResult.data) {
          // Create mock revenue data based on transaction stats
          const totalIncome = revenueResult.data.totalIncome || 0;
          const processedRevenueData = [
            { name: "Jan", value: totalIncome * 0.8 },
            { name: "Feb", value: totalIncome * 0.9 },
            { name: "Mar", value: totalIncome * 1.1 },
            { name: "Apr", value: totalIncome * 0.95 },
            { name: "May", value: totalIncome * 1.05 },
            { name: "Jun", value: totalIncome },
          ];
          setRevenueData(processedRevenueData);
        }

        // Process expense data from transaction stats
        if (transactionStatsResult.success && transactionStatsResult.data) {
          // Create mock expense data based on total expenses
          const totalExpenses = transactionStatsResult.data.totalExpenses || 0;
          const processedExpenseData = [
            { name: "Operations", value: totalExpenses * 0.4 },
            { name: "Payroll", value: totalExpenses * 0.35 },
            { name: "Equipment", value: totalExpenses * 0.15 },
            { name: "Marketing", value: totalExpenses * 0.1 },
          ];
          setExpenseData(processedExpenseData);

          // TODO: Fetch real expense trend data from service
          // For now, set empty array to show no data state
          setExpenseTrendData([]);
        }

        // Set upcoming payments
        if (upcomingInvoicesResult.success && upcomingInvoicesResult.data) {
          setUpcomingPayments(upcomingInvoicesResult.data.slice(0, 5)); // Limit to 5 items
        }
      } catch (error) {
        console.error("Error fetching overview data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchOverviewData();
  }, [timeframe]);

  return (
    <div className="grid gap-6 grid-cols-1 lg:grid-cols-3">
      <AnimatedCard className="lg:col-span-2">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              Revenue Overview
            </h3>
            <p className="text-sm text-gray-500">
              Revenue trend for {timeframe}
            </p>
          </div>
          <div className="flex gap-2">
            <button
              className={`text-xs px-3 py-1.5 border rounded-md ${
                timeframe === "year"
                  ? "border-primary bg-primary text-white"
                  : "border-gray-200 bg-white text-gray-700 hover:bg-gray-50"
              }`}
              onClick={() => setTimeframe("year")}
            >
              Year
            </button>
            <button
              className={`text-xs px-3 py-1.5 border rounded-md ${
                timeframe === "quarter"
                  ? "border-primary bg-primary text-white"
                  : "border-gray-200 bg-white text-gray-700 hover:bg-gray-50"
              }`}
              onClick={() => setTimeframe("quarter")}
            >
              Quarter
            </button>
            <button
              className={`text-xs px-3 py-1.5 border rounded-md ${
                timeframe === "month"
                  ? "border-primary bg-primary text-white"
                  : "border-gray-200 bg-white text-gray-700 hover:bg-gray-50"
              }`}
              onClick={() => setTimeframe("month")}
            >
              Month
            </button>
          </div>
        </div>
        <div className="h-80 border-t border-gray-200 pt-4 bg-white">
          {loading ? (
            <div className="flex items-center justify-center h-full">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <ChartWrapper
              data={revenueData}
              chartType="area"
              context="revenue"
              height={280}
            >
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={revenueData}
                  margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                >
                  <defs>
                    <linearGradient
                      id="colorRevenue"
                      x1="0"
                      y1="0"
                      x2="0"
                      y2="1"
                    >
                      <stop offset="5%" stopColor="#0088FE" stopOpacity={0.8} />
                      <stop offset="95%" stopColor="#0088FE" stopOpacity={0} />
                    </linearGradient>
                  </defs>
                  <XAxis dataKey="name" tick={{ fontSize: 12 }} />
                  <YAxis
                    tickFormatter={(value) => `$${value / 1000}k`}
                    tick={{ fontSize: 12 }}
                  />
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <Tooltip
                    formatter={(value) => [
                      `$${value.toLocaleString()}`,
                      "Revenue",
                    ]}
                    labelFormatter={(label) => `${label}`}
                  />
                  <Area
                    type="monotone"
                    dataKey="value"
                    stroke="#0088FE"
                    fillOpacity={1}
                    fill="url(#colorRevenue)"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </ChartWrapper>
          )}
        </div>
      </AnimatedCard>

      <AnimatedCard>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900">
            Expense Distribution
          </h3>
          <button className="text-xs px-3 py-1.5 border border-gray-200 rounded-md bg-white text-gray-700 hover:bg-gray-50 flex items-center gap-1">
            <Filter className="h-3 w-3" /> Filter
          </button>
        </div>
        <div className="h-80 border-t border-gray-200 pt-4 bg-white">
          {loading ? (
            <div className="flex items-center justify-center h-full">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <ChartWrapper
              data={expenseData}
              chartType="pie"
              context="expenses"
              height={280}
            >
              <ResponsiveContainer width="100%" height="100%">
                <RePieChart>
                  <Pie
                    data={expenseData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) =>
                      `${name} ${(percent * 100).toFixed(0)}%`
                    }
                  >
                    {expenseData.map((_, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={COLORS[index % COLORS.length]}
                      />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value) => `$${value.toLocaleString()}`}
                  />
                  <Legend />
                </RePieChart>
              </ResponsiveContainer>
            </ChartWrapper>
          )}
        </div>
      </AnimatedCard>

      <AnimatedCard className="lg:col-span-2">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              Expense Trends
            </h3>
            <p className="text-sm text-gray-500">
              Quarterly expense breakdown by category
            </p>
          </div>
        </div>
        <div className="h-80 border-t border-gray-200 pt-4 bg-white">
          {loading ? (
            <div className="flex items-center justify-center h-full">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <ChartWrapper
              data={expenseTrendData}
              chartType="bar"
              context="expenses"
              height={280}
            >
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={expenseTrendData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="name" />
                  <YAxis tickFormatter={(value) => `$${value / 1000}k`} />
                  <Tooltip
                    formatter={(value) => [`$${value.toLocaleString()}`]}
                  />
                  <Legend />
                  <Bar
                    dataKey="operations"
                    name="Operations"
                    stackId="a"
                    fill="#0088FE"
                  />
                  <Bar
                    dataKey="payroll"
                    name="Payroll"
                    stackId="a"
                    fill="#00C49F"
                  />
                  <Bar
                    dataKey="equipment"
                    name="Equipment"
                    stackId="a"
                    fill="#FFBB28"
                  />
                </BarChart>
              </ResponsiveContainer>
            </ChartWrapper>
          )}
        </div>
      </AnimatedCard>

      <AnimatedCard>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900">
            Upcoming Payments
          </h3>
          <button className="text-xs px-3 py-1.5 border border-gray-200 rounded-md bg-white text-gray-700 hover:bg-gray-50 flex items-center gap-1">
            <Calendar className="h-3 w-3" /> View Calendar
          </button>
        </div>
        <div className="space-y-3 border-t border-gray-200 pt-4">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin text-primary" />
            </div>
          ) : upcomingPayments.length > 0 ? (
            upcomingPayments.map((payment: any) => (
              <div
                key={payment.id}
                className="flex justify-between items-center p-3 border border-gray-100 rounded-lg bg-white hover:bg-gray-50"
              >
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-md bg-blue-50">
                    <CreditCard className="h-4 w-4 text-blue-500" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">
                      {payment.description || "Invoice Payment"}
                    </p>
                    <div className="flex items-center gap-1 mt-1">
                      <span className="text-xs text-gray-500">
                        {payment.invoiceNumber}
                      </span>
                      <span className="text-xs text-gray-500">•</span>
                      <span className="text-xs text-gray-500">
                        Due{" "}
                        {payment.dueDate
                          ? new Date(payment.dueDate).toLocaleDateString()
                          : "N/A"}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex flex-col items-end">
                  <span className="font-medium text-gray-900">
                    ${payment.totalAmount?.toLocaleString()}
                  </span>
                  <span
                    className={`inline-flex items-center gap-1 text-xs px-2 py-0.5 rounded-full ${
                      payment.status === "PENDING"
                        ? "bg-amber-100 text-amber-800"
                        : payment.status === "PAID"
                          ? "bg-green-100 text-green-800"
                          : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {payment.status === "PENDING" ? (
                      <Clock className="h-3 w-3" />
                    ) : payment.status === "PAID" ? (
                      <CheckCircle2 className="h-3 w-3" />
                    ) : (
                      <AlertCircle className="h-3 w-3" />
                    )}
                    {payment.status}
                  </span>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Calendar className="h-8 w-8 mx-auto mb-2 text-gray-300" />
              <p className="text-sm">No upcoming payments</p>
            </div>
          )}
          {upcomingPayments.length > 0 && (
            <button className="w-full mt-2 text-center text-sm text-primary hover:text-primary/80 font-medium">
              View all scheduled payments
            </button>
          )}
        </div>
      </AnimatedCard>
    </div>
  );
});

FinanceOverview.displayName = "FinanceOverview";
