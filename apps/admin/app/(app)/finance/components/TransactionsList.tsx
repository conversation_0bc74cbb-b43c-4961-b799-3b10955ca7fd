"use client";

import { memo, useMemo, useCallback } from "react";
import {
  DollarSign,
  Plus,
  Trash2,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  CheckSquare,
} from "lucide-react";
import { Button } from "@workspace/ui/components/button";
import {
  BulkStatusSelector,
  TRANSACTION_STATUS_OPTIONS,
} from "@/components/ui/bulk-status-selector";
import { Listing } from "@/modules/listing";
import { ProtectedCreateButton } from "@/lib/components/RBACWrapper";
import { type TransactionWithAssociations } from "@/lib/logistics/operations/transactions";
import { type ColumnFilter } from "@/components/ui/filter-panel";
import {
  getTransactionTableColumns,
  convertToListingTableColumns,
} from "./TransactionTableColumns";
import { TransactionCards } from "./TransactionCards";

interface TransactionsListProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  filterStatus: string;
  setFilterStatus: (status: string) => void;
  viewMode: "cards" | "table";
  setViewMode: (mode: "cards" | "table") => void;
  loading: boolean;
  onRefresh: () => void;
  columnFilters: ColumnFilter[];
  setColumnFilters: (filters: ColumnFilter[]) => void;
  transactions: TransactionWithAssociations[];
  currentPage: number; // Add pagination
  itemsPerPage: number; // Add pagination
  onPageChange: (page: number) => void; // Add pagination handler
  onViewTransaction: (transaction: TransactionWithAssociations) => void;
  onEditTransaction: (transaction: TransactionWithAssociations) => void;
  onDeleteTransaction: (transaction: TransactionWithAssociations) => void;
  onCreateTransaction: () => void;
  // Checkbox support
  selectedTransactions: Set<string>;
  setSelectedTransactions: (items: Set<string>) => void;
  onBulkStatusUpdate: (status: string) => void;
  onBulkDelete: () => void;
  onBulkCreateTasks: () => void;
  onClearSelections?: () => void;
}

/**
 * Transactions List Component
 *
 * Displays transactions in either card or table view with filtering and search capabilities.
 * Uses the standardized Listing component for consistent UI patterns.
 */
export const TransactionsList = memo<TransactionsListProps>(
  ({
    searchTerm,
    setSearchTerm,
    filterStatus,
    setFilterStatus,
    viewMode,
    setViewMode,
    loading,
    onRefresh,
    columnFilters,
    setColumnFilters,
    transactions,
    currentPage,
    itemsPerPage,
    onPageChange,
    onViewTransaction,
    onEditTransaction,
    onDeleteTransaction,
    onCreateTransaction,
    selectedTransactions,
    setSelectedTransactions,
    onBulkStatusUpdate,
    onBulkDelete,
    onBulkCreateTasks,
    onClearSelections,
  }) => {
    // Table columns with three-dot dropdown actions (TanStack format for FilterPanel)
    const tanstackTableColumns = useMemo(
      () =>
        getTransactionTableColumns({
          onViewTransaction,
          onEditTransaction,
          onDeleteTransaction,
        }),
      [onViewTransaction, onEditTransaction, onDeleteTransaction]
    );

    // Table columns for Listing.Table component (simple format)
    const listingTableColumns = useMemo(
      () =>
        convertToListingTableColumns({
          onViewTransaction,
          onEditTransaction,
          onDeleteTransaction,
        }),
      [onViewTransaction, onEditTransaction, onDeleteTransaction]
    );

    // Define transaction statuses for filtering (ordered by priority)
    const transactionStatuses = useMemo(() => {
      const statuses = [
        { key: "active", label: "Active", count: 0 },
        { key: "pending", label: "Pending", count: 0 },
        { key: "completed", label: "Completed", count: 0 },
        { key: "cancelled", label: "Cancelled", count: 0 },
        { key: "inactive", label: "Inactive", count: 0 },
      ];

      // Calculate counts
      statuses.forEach((status) => {
        status.count = transactions.filter(
          (transaction) => transaction.status?.toLowerCase() === status.key
        ).length;
      });

      // Defensive constraint: if current filter results in no data, reset to "all"
      const currentCategoryCount =
        statuses.find((cat) => cat.key === filterStatus)?.count || 0;
      if (
        filterStatus !== "all" &&
        currentCategoryCount === 0 &&
        transactions.length > 0
      ) {
        setFilterStatus("all");
      }

      return statuses;
    }, [transactions, filterStatus, setFilterStatus]);

    // Render bulk actions
    const renderBulkActions = useCallback(
      () => (
        <>
          <BulkStatusSelector
            statusOptions={TRANSACTION_STATUS_OPTIONS}
            onStatusUpdate={onBulkStatusUpdate}
            placeholder="Mark as..."
          />
          <Button
            variant="outline"
            size="sm"
            onClick={onBulkCreateTasks}
            className="gap-2"
            disabled={selectedTransactions.size === 0}
          >
            <CheckSquare size={16} />
            Create Tasks
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={onBulkDelete}
            className="gap-2"
          >
            <Trash2 size={16} />
            Delete
          </Button>
        </>
      ),
      [
        onBulkStatusUpdate,
        onBulkDelete,
        onBulkCreateTasks,
        selectedTransactions.size,
      ]
    );

    // Pagination - calculate based on filtered transactions
    const totalPages = Math.ceil(transactions.length / itemsPerPage);

    // Pagination for cards view only (table handles its own pagination)
    const paginatedTransactions = useMemo(() => {
      const start = (currentPage - 1) * itemsPerPage;
      const end = start + itemsPerPage;
      return transactions.slice(start, end);
    }, [transactions, currentPage, itemsPerPage]);

    // Empty state for table (React element)
    const tableEmptyState = (
      <div className="text-center py-12">
        <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
          <DollarSign className="h-6 w-6 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-1">
          No transactions found
        </h3>
        <p className="text-gray-500 mb-4">
          Get started by creating your first transaction
        </p>
        <button
          onClick={onCreateTransaction}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
        >
          Create Transaction
        </button>
      </div>
    );

    return (
      <Listing>
        <Listing.Filters
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          columnFilters={columnFilters}
          onColumnFilterAdd={(filter) =>
            setColumnFilters([...columnFilters, filter])
          }
          onColumnFilterRemove={(index) =>
            setColumnFilters(columnFilters.filter((_, i) => i !== index))
          }
          columns={tanstackTableColumns}
          tableData={transactions}
          loading={loading}
          onRefresh={onRefresh}
          enableDynamicFilters={true}
          defaultFilterColumn="name"
          autoSelectDefaultColumn={true}
          bulkActions={renderBulkActions()}
          selectedCount={selectedTransactions.size}
          showBulkActions={true}
          onClearSelections={onClearSelections}
        />

        <Listing.Controls
          entity="transactions"
          length={transactions.length}
          viewMode={viewMode}
          onViewModeChange={setViewMode}
          categories={transactionStatuses}
          categoryFilter={filterStatus}
          onCategoryFilterChange={setFilterStatus}
          actions={
            <div className="flex items-center gap-2">
              <ProtectedCreateButton entity="transactions">
                <Button onClick={onCreateTransaction}>
                  <Plus size={16} />
                  New Transactions
                </Button>
              </ProtectedCreateButton>
            </div>
          }
        />

        {viewMode === "cards" ? (
          <>
            <TransactionCards
              transactions={paginatedTransactions}
              loading={loading}
              emptyState={tableEmptyState}
              transactionStatuses={transactionStatuses}
              onViewTransaction={onViewTransaction}
            />

            {/* Pagination for cards view */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between px-6 py-4 border-t border-gray-200 bg-white rounded-lg">
                <div className="text-sm text-gray-500">
                  Showing {(currentPage - 1) * itemsPerPage + 1} to{" "}
                  {Math.min(currentPage * itemsPerPage, transactions.length)} of{" "}
                  {transactions.length} results
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(1)}
                    disabled={currentPage === 1}
                    className="gap-1"
                  >
                    <ChevronsLeft size={16} />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="gap-1"
                  >
                    <ChevronLeft size={16} />
                    Previous
                  </Button>
                  <span className="px-3 py-2 text-sm text-gray-900">
                    Page {currentPage} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="gap-1"
                  >
                    Next
                    <ChevronRight size={16} />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(totalPages)}
                    disabled={currentPage === totalPages}
                    className="gap-1"
                  >
                    <ChevronsRight size={16} />
                  </Button>
                </div>
              </div>
            )}
          </>
        ) : (
          <Listing.Table
            data={transactions}
            columns={listingTableColumns}
            loading={loading}
            enableCheckboxes={true}
            selectedRowIds={Array.from(selectedTransactions)}
            onSelectionChange={(selectedIds) =>
              setSelectedTransactions(new Set(selectedIds))
            }
            getRowId={(item) => item.id}
            emptyState={tableEmptyState}
            pagination={{
              currentPage,
              totalPages,
              totalItems: transactions.length,
              itemsPerPage,
              onPageChange,
            }}
          />
        )}
      </Listing>
    );
  }
);

TransactionsList.displayName = "TransactionsList";
