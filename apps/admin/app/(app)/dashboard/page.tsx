"use client";

import { useState, useEffect } from "react";
import {
  Search,
  ArrowUp,
  ArrowDown,
  Package,
  Users,
  FileText,
  Clock,
  Plus,
  UserPlus,
  Bell,
  Calendar,
  Truck,
  Map,
  BarChart2,
  CheckCircle,
  AlertTriangle,
  MoreHorizontal,
  TrendingUp,
  AlertCircle,
  RefreshCw,
  ChevronRight,
  Loader2,
  Shield,
} from "lucide-react";
import { Input } from "@workspace/ui/components/input";
import { Button } from "@workspace/ui/components/button";
import { cn } from "@workspace/ui/lib/utils";
import { motion } from "framer-motion";
import {
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  Tooltip,
  CartesianGrid,
  AreaChart,
  Area,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  Legend,
} from "recharts";
import {
  CargoModal,
  CustomerModal,
  TrackModal,
  InvoiceModal,
  ReportsModal,
  RoutesModal,
} from "@/components/modals";
import {
  customerService,
  cargoService,
  notificationService,
  accountService,
  departmentService,
  type CargoWithRelations,
} from "@/lib/logistics";
import { ChartWrapper, isDataSufficient } from "@/lib/chart-utils";
import { useAppSelector } from "@/store/hooks";
import { NotificationTester } from "@/components/notifications/NotificationTester";
import { redirect } from "next/navigation";

// Types for dashboard data
interface DashboardStats {
  activeShipments: number;
  totalCustomers: number;
  pendingDeliveries: number;
  totalCargos: number;
}

interface CargoDistribution {
  name: string;
  value: number;
}

interface RecentShipment {
  id: string;
  status: string;
  customer: string;
  destination: string;
  date: string;
  tracking_number: string;
}

interface NotificationItem {
  id: string;
  title: string;
  time: string;
  type: "alert" | "info" | "success";
}

const StatCard = ({
  title,
  value,
  percentage,
  icon: Icon,
  trend = "up",
  color = "primary",
  loading = false,
}: {
  title: string;
  value: string | number;
  percentage: number;
  icon?: React.ElementType;
  trend?: "up" | "down";
  color?: "primary" | "blue" | "green" | "amber" | "red";
  loading?: boolean;
}) => {
  const colorClasses = {
    primary: "bg-primary/10 text-primary",
    blue: "bg-blue-500/10 text-blue-500",
    green: "bg-green-500/10 text-green-500",
    amber: "bg-amber-500/10 text-amber-500",
    red: "bg-red-500/10 text-red-500",
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="stat-card bg-background border border-gray-200 rounded-lg p-6 shadow-sm"
    >
      <div className="flex justify-between items-start mb-3">
        <p className="text-sm font-medium text-foreground-tertiary">{title}</p>
        {Icon && (
          <div className={`p-2 rounded-full ${colorClasses[color]}`}>
            {loading ? (
              <Loader2 size={18} className="animate-spin" />
            ) : (
              <Icon size={18} />
            )}
          </div>
        )}
      </div>
      <div className="flex justify-between items-end">
        {loading ? (
          <div className="h-8 w-20 bg-gray-200 rounded animate-pulse" />
        ) : (
          <p className="text-2xl font-bold text-foreground">{value}</p>
        )}
        <div
          className={`flex items-center text-xs font-medium ${
            trend === "up"
              ? "text-green-600 bg-green-100"
              : "text-red-600 bg-red-100"
          } px-2 py-1 rounded-full`}
        >
          {trend === "up" ? (
            <ArrowUp size={12} className="mr-1" />
          ) : (
            <ArrowDown size={12} className="mr-1" />
          )}
          {percentage.toFixed(1)}%
        </div>
      </div>
    </motion.div>
  );
};

const TimeRangeButton = ({
  label,
  active,
  onClick,
}: {
  label: string;
  active: boolean;
  onClick: () => void;
}) => (
  <button
    onClick={onClick}
    className={cn(
      "px-3 py-1.5 text-xs font-medium rounded-md transition-colors",
      active
        ? "bg-primary text-white"
        : "text-foreground-quaternary hover:bg-background-tertiary hover:text-foreground-secondary border border-gray-200"
    )}
  >
    {label}
  </button>
);

const StatusBadge = ({ status }: { status: string }) => {
  const statusStyles: { [key: string]: string } = {
    active: "bg-blue-100 text-blue-800",
    completed: "bg-green-100 text-green-800",
    processing: "bg-amber-100 text-amber-800",
    pending: "bg-orange-100 text-orange-800",
    created: "bg-gray-100 text-gray-800",
    in_transit: "bg-blue-100 text-blue-800",
    delivered: "bg-green-100 text-green-800",
    cancelled: "bg-red-100 text-red-800",
  };

  const normalizedStatus = status.toLowerCase().replace(/[^a-z]/g, "_");
  const displayStatus = status.replace(/_/g, " ").toLowerCase();

  return (
    <span
      className={cn(
        "px-2 py-1 text-xs rounded-full inline-flex items-center",
        statusStyles[normalizedStatus] || "bg-gray-100 text-gray-800"
      )}
    >
      {normalizedStatus === "in_transit" && (
        <Truck size={12} className="mr-1" />
      )}
      {normalizedStatus === "completed" && (
        <CheckCircle size={12} className="mr-1" />
      )}
      {normalizedStatus === "processing" && (
        <RefreshCw size={12} className="mr-1" />
      )}
      {normalizedStatus === "pending" && <Clock size={12} className="mr-1" />}
      {displayStatus}
    </span>
  );
};

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884d8"];

export default function DashboardPage() {
  const [timeRange, setTimeRange] = useState<"12m" | "30d" | "7d">("12m");
  const [loading, setLoading] = useState(true);
  const [dashboardStats, setDashboardStats] = useState<DashboardStats>({
    activeShipments: 0,
    totalCustomers: 0,
    pendingDeliveries: 0,
    totalCargos: 0,
  });
  const [recentShipments, setRecentShipments] = useState<RecentShipment[]>([]);
  const [cargoDistribution, setCargoDistribution] = useState<
    CargoDistribution[]
  >([]);
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [monthlyShipmentsData, setMonthlyShipmentsData] = useState<
    Array<{ name: string; value: number }>
  >([]);
  const [deliveryPerformanceData, setDeliveryPerformanceData] = useState<
    Array<{ name: string; onTime: number; delayed: number }>
  >([]);

  // Get authenticated user from Redux store
  const { user: authUser } = useAppSelector((state) => state.auth);

  // Helper function to get first name from full name
  const getFirstName = (fullName: string) => {
    return fullName.split(" ")[0];
  };

  // Role badge component
  const RoleBadge = ({
    role,
  }: {
    role: { name: string; department: { name: string } };
  }) => (
    <span className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full bg-primary/10 text-primary border border-primary/20">
      <Shield size={12} />
      {role.name}
    </span>
  );

  // Fetch monthly shipments data
  const fetchMonthlyShipmentsData = async () => {
    try {
      // Get cargos from last 12 months grouped by month
      const endDate = new Date();
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - 12);

      const monthlyData: Array<{ name: string; value: number }> = [];
      const monthNames = [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "May",
        "Jun",
        "Jul",
        "Aug",
        "Sep",
        "Oct",
        "Nov",
        "Dec",
      ];

      // Initialize 12 months of data
      for (let i = 11; i >= 0; i--) {
        const date = new Date();
        date.setMonth(date.getMonth() - i);
        monthlyData.push({
          name: monthNames[date.getMonth()] || "Unknown",
          value: 0,
        });
      }

      // For now, use a simplified approach with mock data since we'd need
      // complex date aggregation queries that might not be available in the current service
      // In a real implementation, this would query cargos by created_at date ranges

      const cargoResult = await cargoService.getAllCargosWithRelations({
        limit: 1000,
      });
      if (cargoResult.success && cargoResult.data) {
        // Group cargos by month (simplified - would need more complex logic for real data)
        let cargosByMonth: any = {};

        cargoResult.data.forEach((cargo: CargoWithRelations) => {
          if (cargo.created_at) {
            const date = new Date(cargo.created_at);
            let monthKey: any = monthNames[date.getMonth()];

            cargosByMonth = {
              ...cargosByMonth,
              [monthKey]: (cargosByMonth[monthKey] || 0) + 1,
            };
          }
        });

        // Update monthly data with real counts
        monthlyData.forEach((month) => {
          month.value = cargosByMonth[month.name] || 0;
        });
      }

      setMonthlyShipmentsData(monthlyData);
    } catch (error) {
      console.error("Error fetching monthly shipments data:", error);
      // Set empty data on error
      setMonthlyShipmentsData([]);
    }
  };

  // Fetch delivery performance data
  const fetchDeliveryPerformanceData = async () => {
    try {
      // Get recent cargos and analyze delivery performance
      const recentCargos = await cargoService.getAllCargosWithRelations({
        limit: 500,
      });

      if (recentCargos.success && recentCargos.data) {
        const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
        const performanceData: Array<{
          name: string;
          onTime: number;
          delayed: number;
        }> = [];

        // Initialize 7 days of data
        for (let i = 6; i >= 0; i--) {
          const date = new Date();
          date.setDate(date.getDate() - i);
          performanceData.push({
            name: dayNames[date.getDay()] || "Unknown",
            onTime: 0,
            delayed: 0,
          });
        }

        // Simplified analysis - in real implementation would compare delivery dates vs expected dates
        recentCargos.data.forEach((cargo) => {
          if (cargo.created_at) {
            const date = new Date(cargo.created_at);
            const dayName = dayNames[date.getDay()];
            const dayData = performanceData.find((d) => d.name === dayName);

            if (dayData) {
              // Simplified logic: assume delivered cargos are "on time" and others might be "delayed"
              if (cargo.status === "DELIVERED") {
                dayData.onTime += 1;
              } else if (
                cargo.status === "PENDING" ||
                cargo.status === "PROCESSING"
              ) {
                dayData.delayed += 1;
              }
            }
          }
        });

        setDeliveryPerformanceData(performanceData);
      }
    } catch (error) {
      console.error("Error fetching delivery performance data:", error);
      // Set empty data on error
      setDeliveryPerformanceData([]);
    }
  };

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        // Fetch customer statistics
        const customerStats = await customerService.getCustomerStats();

        // Fetch cargo statistics
        const cargoStats = await cargoService.getCargoStats();

        // Fetch recent cargos as shipments
        const recentCargos = await cargoService.getAllCargosWithRelations({
          limit: 5,
          column: "created_at",
          ascending: false,
        });

        // Fetch notifications
        const notificationsResult =
          await notificationService.getAllNotificationsWithAccounts({
            limit: 4,
            column: "created_at",
            ascending: false,
          });

        // Process and set dashboard statistics
        if (customerStats.success && cargoStats.success) {
          const activeShipments =
            cargoStats.data?.cargosByStatus.find((s) =>
              ["PROCESSING", "IN_TRANSIT", "ACTIVE"].includes(s.status)
            )?.count || 0;

          const pendingDeliveries =
            cargoStats.data?.cargosByStatus.find((s) => s.status === "PENDING")
              ?.count || 0;

          setDashboardStats({
            activeShipments,
            totalCustomers: customerStats.data?.totalCustomers || 0,
            pendingDeliveries,
            totalCargos: cargoStats.data?.totalCargos || 0,
          });

          // Process cargo distribution data
          if (cargoStats.data?.cargosByCategory) {
            const distribution = cargoStats.data.cargosByCategory.map(
              (cat) => ({
                name: cat.category,
                value: cat.count,
              })
            );
            setCargoDistribution(distribution);
          }
        }

        // Process recent shipments
        if (recentCargos.success && recentCargos.data) {
          const shipments = recentCargos.data.map(
            (cargo: CargoWithRelations) => ({
              id: cargo.id,
              status: cargo.status || "Unknown",
              customer: cargo.customers?.name || "Unknown Customer",
              particular: cargo.particular || "Unknown",
              date: cargo.created_at
                ? new Date(cargo.created_at).toLocaleDateString()
                : "Unknown",
              tracking_number: cargo.tracking_number || "",
            })
          );
          setRecentShipments(shipments);
        }

        // Process notifications
        if (notificationsResult.success && notificationsResult.data) {
          const processedNotifications = notificationsResult.data.map(
            (notification) => ({
              id: notification.id,
              title: notification.name || notification.message || "No title",
              time: notification.created_at
                ? getTimeAgo(notification.created_at)
                : "Unknown time",
              type: getNotificationType(notification.name || ""),
            })
          );
          setNotifications(processedNotifications);
        }

        // Fetch monthly shipments data (last 12 months)
        await fetchMonthlyShipmentsData();

        // Fetch delivery performance data (last 7 days)
        await fetchDeliveryPerformanceData();
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Helper function to get time ago
  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60)
    );

    if (diffInMinutes < 60) {
      return `${diffInMinutes} min ago`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)} hour${Math.floor(diffInMinutes / 60) > 1 ? "s" : ""} ago`;
    } else {
      return `${Math.floor(diffInMinutes / 1440)} day${Math.floor(diffInMinutes / 1440) > 1 ? "s" : ""} ago`;
    }
  };

  // Helper function to determine notification type
  const getNotificationType = (title: string): "alert" | "info" | "success" => {
    const lowerTitle = title.toLowerCase();
    if (
      lowerTitle.includes("error") ||
      lowerTitle.includes("failed") ||
      lowerTitle.includes("alert")
    ) {
      return "alert";
    } else if (
      lowerTitle.includes("completed") ||
      lowerTitle.includes("delivered") ||
      lowerTitle.includes("success")
    ) {
      return "success";
    }
    return "info";
  };

  // Handle search
  const handleSearch = async (value: string) => {
    setSearchTerm(value);
    if (value.trim()) {
      // Search cargos by tracking number
      const searchResults = await cargoService.searchCargos(value, {
        limit: 10,
      });

      if (searchResults.success) {
        // Update recent shipments with search results
        const shipments = searchResults.data.map(
          (cargo: CargoWithRelations) => ({
            id: cargo.id,
            status: cargo.status || "Unknown",
            customer: cargo.customers?.name || "Unknown Customer",
            particular: cargo.particular || "Unknown",
            date: cargo.created_at
              ? new Date(cargo.created_at).toLocaleDateString()
              : "Unknown",
            tracking_number: cargo.tracking_number || "",
          })
        );
        setRecentShipments(shipments);
      }
    }
  };

  const today = new Date();
  const formattedDate = today.toLocaleDateString("en-US", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  return (
    <div className="p-6 space-y-6">
      <motion.div
        className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div>
          <div className="flex items-center gap-3 mb-2">
            <h1 className="text-3xl font-bold text-foreground">
              {authUser ? (
                <>Welcome, {getFirstName(authUser.name)}</>
              ) : (
                <span className="flex items-center gap-2">
                  Welcome
                  <Loader2 size={20} className="animate-spin" />
                </span>
              )}
            </h1>
            {authUser?.role && <RoleBadge role={authUser.role} />}
          </div>
          <p className="text-sm text-muted-foreground">{formattedDate}</p>
        </div>
        <div className="flex items-center gap-3">
          <div className="relative w-full md:w-auto md:min-w-[240px]">
            <Search
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"
              size={18}
            />
            <Input
              type="search"
              placeholder="Search shipments, customers..."
              className="pl-10 h-10 border border-gray-200 rounded-md w-full"
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>
        </div>
      </motion.div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Cargos"
          value={dashboardStats.totalCargos}
          percentage={6.2}
          icon={Package}
          color="primary"
          loading={loading}
        />
        <StatCard
          title="Active Shipments"
          value={dashboardStats.activeShipments}
          percentage={4.8}
          icon={Truck}
          color="blue"
          loading={loading}
        />
        <StatCard
          title="Customers"
          value={dashboardStats.totalCustomers.toLocaleString()}
          percentage={0.8}
          icon={Users}
          color="green"
          loading={loading}
        />
        <StatCard
          title="Pending Deliveries"
          value={dashboardStats.pendingDeliveries}
          percentage={2.3}
          trend="down"
          icon={Clock}
          color="amber"
          loading={loading}
        />
      </div>

      <motion.div
        className="lg:col-span-2 bg-background border border-gray-200 rounded-lg shadow-sm p-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-lg font-medium">Monthly Shipments</h2>
          <div className="flex items-center gap-2">
            <TimeRangeButton
              label="12 months"
              active={timeRange === "12m"}
              onClick={() => setTimeRange("12m")}
            />
            <TimeRangeButton
              label="30 days"
              active={timeRange === "30d"}
              onClick={() => setTimeRange("30d")}
            />
            <TimeRangeButton
              label="7 days"
              active={timeRange === "7d"}
              onClick={() => setTimeRange("7d")}
            />
          </div>
        </div>
        <div style={{ width: "100%", height: 280 }}>
          <ChartWrapper
            data={monthlyShipmentsData}
            chartType="area"
            context="shipments"
            height={280}
          >
            <ResponsiveContainer>
              <AreaChart
                data={monthlyShipmentsData}
                margin={{ top: 5, right: 10, left: 0, bottom: 0 }}
              >
                <CartesianGrid
                  strokeDasharray="3 3"
                  stroke="hsl(var(--border))"
                />
                <XAxis
                  dataKey="name"
                  fontSize={11}
                  tickLine={false}
                  axisLine={false}
                  stroke="hsl(var(--muted-foreground))"
                />
                <YAxis
                  fontSize={11}
                  tickLine={false}
                  axisLine={false}
                  stroke="hsl(var(--muted-foreground))"
                  width={30}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: "hsl(var(--background))",
                    borderColor: "hsl(var(--border))",
                    borderRadius: "var(--radius)",
                    fontSize: "12px",
                    boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
                  }}
                  cursor={{ stroke: "hsl(var(--border))", strokeWidth: 1 }}
                />
                <Area
                  type="monotone"
                  dataKey="value"
                  stroke="hsl(var(--primary))"
                  strokeWidth={2}
                  fill="hsl(var(--primary)/0.1)"
                  activeDot={{
                    r: 6,
                    strokeWidth: 0,
                    fill: "hsl(var(--primary))",
                  }}
                />
              </AreaChart>
            </ResponsiveContainer>
          </ChartWrapper>
        </div>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <motion.div
          className="bg-background border border-gray-200 rounded-lg shadow-sm p-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-medium">Cargo Distribution</h2>
            <Button variant="ghost" size="sm" className="text-xs">
              <MoreHorizontal size={16} />
            </Button>
          </div>
          <div style={{ width: "100%", height: 220 }}>
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            ) : (
              <ChartWrapper
                data={cargoDistribution}
                chartType="pie"
                context="cargo"
                height={220}
              >
                <ResponsiveContainer>
                  <PieChart>
                    <Pie
                      data={cargoDistribution}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={80}
                      fill="#8884d8"
                      paddingAngle={3}
                      dataKey="value"
                      label={({ name, percent }) =>
                        `${name} ${(percent * 100).toFixed(0)}%`
                      }
                      labelLine={false}
                    >
                      {cargoDistribution.map((entry, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={COLORS[index % COLORS.length]}
                        />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </ChartWrapper>
            )}
          </div>
        </motion.div>

        <motion.div
          className="lg:col-span-2 bg-background border border-gray-200 rounded-lg shadow-sm p-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-medium">Delivery Performance</h2>
            <div className="flex items-center gap-2 text-xs">
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-green-500 rounded-sm"></div>
                <span>On time</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-3 h-3 bg-red-500 rounded-sm"></div>
                <span>Delayed</span>
              </div>
            </div>
          </div>
          <div style={{ width: "100%", height: 220 }}>
            <ChartWrapper
              data={deliveryPerformanceData}
              chartType="bar"
              context="performance"
              height={220}
            >
              <ResponsiveContainer>
                <BarChart data={deliveryPerformanceData}>
                  <CartesianGrid
                    strokeDasharray="3 3"
                    stroke="hsl(var(--border))"
                  />
                  <XAxis dataKey="name" fontSize={11} />
                  <YAxis fontSize={11} />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: "hsl(var(--background))",
                      borderColor: "hsl(var(--border))",
                      borderRadius: "var(--radius)",
                      fontSize: "12px",
                      boxShadow: "0 2px 10px rgba(0,0,0,0.1)",
                    }}
                  />
                  <Bar dataKey="onTime" fill="#10b981" radius={[4, 4, 0, 0]} />
                  <Bar dataKey="delayed" fill="#ef4444" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </ChartWrapper>
          </div>
        </motion.div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <motion.div
          className="bg-background border border-gray-200 rounded-lg shadow-sm p-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.5 }}
        >
          <div className="flex justify-between items-center mb-5">
            <h2 className="text-lg font-medium">Quick Actions</h2>
          </div>
          <div className="grid grid-cols-1 gap-4">
            <CargoModal />
            <CustomerModal />
            <TrackModal />
            <InvoiceModal />
            <ReportsModal />
          </div>
        </motion.div>

        <motion.div
          className="bg-background border border-gray-200 rounded-lg shadow-sm overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <div className="flex justify-between items-center p-6 border-b border-gray-200">
            <h2 className="text-lg font-medium">Recent Cargo</h2>
            <Button
              onClick={() => {
                redirect(`/cargo-management`);
              }}
              variant="outline"
              size="sm"
              className="text-xs border border-gray-200 rounded-md"
            >
              View All
            </Button>
          </div>
          <div className="p-2">
            {loading ? (
              <div className="space-y-3 p-4">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="animate-pulse flex space-x-4">
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                  </div>
                ))}
              </div>
            ) : recentShipments.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-[300px] w-full">
                <Truck size={32} className="text-gray-300 mb-3" />
                <h3 className="text-sm font-medium text-gray-900 mb-1">
                  No Recent Shipments
                </h3>
                <p className="text-sm text-gray-500 text-center">
                  Start creating shipments to see them appear here.
                </p>
              </div>
            ) : (
              <table className="w-full divide-y divide-gray-200">
                <tbody className="divide-y divide-gray-200">
                  {recentShipments.map((shipment) => (
                    <tr
                      key={shipment.id}
                      className="hover:bg-muted/50"
                      onClick={() => {
                        redirect(`/cargo-management/${shipment.id}`);
                      }}
                    >
                      <td className="px-4 py-3.5 whitespace-nowrap text-sm font-medium">
                        {shipment.tracking_number || shipment.id}
                      </td>
                      <td className="px-4 py-3.5 whitespace-nowrap text-sm">
                        {shipment.particular}
                      </td>
                      <td className="px-4 py-3.5 whitespace-nowrap text-sm text-right text-muted-foreground">
                        {shipment.date}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        </motion.div>

        <motion.div
          className="bg-background border border-gray-200 rounded-lg shadow-sm"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.6 }}
        >
          <div className="flex justify-between items-center p-6 border-b border-gray-200">
            <h2 className="text-lg font-medium">Notifications</h2>
            <Button
              onClick={() => {
                redirect(`/notifications`);
              }}
              variant="outline"
              size="sm"
              className="text-xs border border-gray-200 rounded-md"
            >
              View All
            </Button>
          </div>
          <div className="divide-y divide-gray-200">
            {loading ? (
              <div className="space-y-3 p-6">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="animate-pulse flex space-x-4">
                    <div className="rounded-full bg-gray-200 h-10 w-10"></div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : notifications.length > 0 ? (
              notifications.map((notification) => (
                <div
                  key={notification.id}
                  className="p-6 flex items-start gap-3"
                >
                  <div
                    className={`p-2 rounded-full flex-shrink-0 ${
                      notification.type === "alert"
                        ? "bg-red-100 text-red-600"
                        : notification.type === "success"
                          ? "bg-green-100 text-green-600"
                          : "bg-blue-100 text-blue-600"
                    }`}
                  >
                    {notification.type === "alert" && (
                      <AlertTriangle size={16} />
                    )}
                    {notification.type === "success" && (
                      <CheckCircle size={16} />
                    )}
                    {notification.type === "info" && <Bell size={16} />}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">{notification.title}</p>
                    <p className="text-xs text-muted-foreground">
                      {notification.time}
                    </p>
                  </div>
                  <Button variant="ghost" size="sm" className="p-1">
                    <MoreHorizontal size={16} />
                  </Button>
                </div>
              ))
            ) : (
              <div className="p-6 text-center text-muted-foreground">
                <Bell size={24} className="mx-auto mb-2 opacity-50" />
                <p className="text-sm">No new notifications</p>
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  );
}
