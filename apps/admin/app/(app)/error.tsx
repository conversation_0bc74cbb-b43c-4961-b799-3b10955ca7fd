"use client";

import { useEffect } from "react";
import { motion } from "framer-motion";
import { AlertTriangle } from "lucide-react";
import { AnimatedCard } from "@/components/animated-card";
import { slideUpAnimation, springTransition } from "@/lib/motion-config";

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    // TODO: Implement proper error reporting service
  }, [error]);

  return (
    <div className="flex items-center justify-center min-h-[80vh] p-6">
      <motion.div
        className="w-full max-w-2xl mx-auto"
        variants={slideUpAnimation}
        initial="hidden"
        animate="visible"
        exit="exit"
        transition={springTransition}
      >
        <AnimatedCard className="text-center">
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.2, ...springTransition }}
            className="mb-6 flex justify-center"
          >
            <AlertTriangle size={64} className="text-red-500" />
          </motion.div>

          <motion.h1
            className="text-2xl font-bold mb-2"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.3, ...springTransition }}
          >
            Something went wrong
          </motion.h1>

          <motion.p
            className="text-muted-foreground mb-6"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.4, ...springTransition }}
          >
            {error.message ||
              "An unexpected error occurred. Please try again later."}
          </motion.p>

          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.5, ...springTransition }}
          >
            <motion.button
              onClick={reset}
              className="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-md transition-colors duration-200"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Try again
            </motion.button>
          </motion.div>
        </AnimatedCard>
      </motion.div>
    </div>
  );
}
