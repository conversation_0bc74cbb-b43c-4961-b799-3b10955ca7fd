"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import {
  ArrowLeft,
  ChevronRight,
  AlertCircle,
  CheckCircle,
  Package,
  Truck,
  User,
  Building,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Hash,
  Weight,
  Ruler,
  DollarSign,
  Clock,
  Loader2,
  Shield,
  FileText,
  Box,
  Container,
  Eye,
} from "lucide-react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@workspace/ui/components/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@workspace/ui/components/alert-dialog";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { StatusBadge } from "@/components/status-badge";
import { cn } from "@workspace/ui/lib/utils";
import { PageTransition } from "@/components/page-transition";
import { AnimatedCard } from "@/components/animated-card";
import { useAppSelector } from "@/store/hooks";
import { ProtectedEditButton, withRBAC } from "@/lib/components/RBACWrapper";
import {
  cargoService,
  type CargoWithRelations,
  type DocumentWithAccount,
  StatusEnum as Status,
  DimensionUnitEnum as DimensionUnit,
  WeightUnitEnum as WeightUnit,
} from "@/lib/logistics";
import { releaseDocumentService } from "@/lib/logistics/operations/release-documents";
import { documentService } from "@/lib/logistics/operations/documents";
import {
  DocumentPreviewDialog,
  useDocumentPreview,
} from "@/components/ui/document-preview-dialog";

// Helper function to format dimensions
const formatDimensions = (
  length: number | null,
  width: number | null,
  height: number | null,
  unit: DimensionUnit | string | null
): string => {
  if (!length || !width || !height) return "N/A";
  const unitDisplay =
    unit === "METER_CUBIC" ? "m" : unit === "FEET_CUBIC" ? "ft" : "cm";
  return `${length} × ${width} × ${height} ${unitDisplay}`;
};

// Helper function to format CBM
const formatCBM = (
  cbm: number | null,
  unit: DimensionUnit | string | null
): string => {
  if (!cbm) return "N/A";
  const unitDisplay =
    unit === "METER_CUBIC"
      ? "m³"
      : unit === "FEET_CUBIC"
        ? "ft³"
        : unit === "CENTIMETER_CUBIC"
          ? "cm³"
          : unit === "INCH_CUBIC"
            ? "in³"
            : "m³";
  return `${cbm.toLocaleString()} ${unitDisplay}`;
};

// Helper function to format weight
const formatWeight = (
  weight: number | null,
  unit: WeightUnit | string | null
): string => {
  if (!weight) return "N/A";
  const unitDisplay =
    unit === "KILOGRAM" ? "kg" : unit === "POUND" ? "lbs" : "kg";
  return `${weight.toLocaleString()} ${unitDisplay}`;
};

// Helper function to format date
const formatDate = (dateString: string | null): string => {
  if (!dateString) return "N/A";
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

// Helper function to format datetime
const formatDateTime = (dateString: string | null): string => {
  if (!dateString) return "N/A";
  return new Date(dateString).toLocaleString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};

// Helper function to get status badge variant
const getStatusBadgeVariant = (status: string | null) => {
  switch (status?.toLowerCase()) {
    case "created":
      return "secondary";
    case "processing":
      return "default";
    case "in_transit":
      return "default";
    case "delivered":
      return "default";
    case "released":
      return "default";
    case "cancelled":
      return "destructive";
    default:
      return "secondary";
  }
};

const InfoItem = ({
  label,
  value,
  icon: Icon,
  className,
}: {
  label: string;
  value: string | React.ReactNode;
  icon?: React.ElementType;
  className?: string;
}) => (
  <div className={className}>
    <p className="text-xs text-gray-500 mb-0.5 flex items-center gap-1">
      {Icon && <Icon size={14} className="text-gray-400" />}
      {label}
    </p>
    <p className="text-sm text-gray-900 font-medium">{value}</p>
  </div>
);

function ReleaseAuthorizationDetailPage() {
  const params = useParams();
  const router = useRouter();
  const cargoId = params.id as string;

  const [cargo, setCargo] = useState<CargoWithRelations | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAuthorizationDialogOpen, setIsAuthorizationDialogOpen] =
    useState(false);
  const [isAuthorizing, setIsAuthorizing] = useState(false);
  const [consentChecked, setConsentChecked] = useState(false);
  const [documents, setDocuments] = useState<DocumentWithAccount[]>([]);
  const [documentsLoading, setDocumentsLoading] = useState(false);

  // Document preview hook
  const { isOpen, document, openPreview, closePreview } = useDocumentPreview();

  useEffect(() => {
    const fetchCargoData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch cargo data
        const cargoResult = await cargoService.getCargoWithRelations(cargoId);
        if (!cargoResult.success || !cargoResult.data) {
          setError(cargoResult.error || "Cargo not found");
          return;
        }

        setCargo(cargoResult.data);

        // Fetch documents for this cargo
        setDocumentsLoading(true);
        const documentsResult = await documentService.getDocumentsByEntity(
          "handovers",
          cargoResult.data.id // Fix this to use the handover id
        );

        if (documentsResult.success && documentsResult.data) {
          setDocuments(documentsResult.data);
        } else {
          console.warn("Failed to fetch documents:", documentsResult.error);
          setDocuments([]);
        }
      } catch (error) {
        console.error("Error fetching cargo:", error);
        setError("Failed to fetch cargo information");
      } finally {
        setLoading(false);
        setDocumentsLoading(false);
      }
    };

    if (cargoId) {
      fetchCargoData();
    }
  }, [cargoId]);

  // Function to handle viewing a document
  const handleViewDocument = async (doc: DocumentWithAccount) => {
    try {
      // Get the download URL for the document
      const downloadResult = await documentService.getDocumentDownloadUrl(
        doc.path
      );

      if (downloadResult.success && downloadResult.data) {
        // Determine file type from the document name or path
        const fileExtension = doc.name.split(".").pop()?.toLowerCase() || "pdf";
        const fileType = ["jpg", "jpeg", "png", "gif"].includes(fileExtension)
          ? "image"
          : fileExtension === "pdf"
            ? "pdf"
            : "document";

        openPreview({
          uri: downloadResult.data,
          fileName: doc.name || "Release Authorization Document",
          fileType: fileType,
        });
      } else {
        alert("Failed to load document for preview");
      }
    } catch (error) {
      console.error("Error loading document:", error);
      alert("Failed to load document");
    }
  };

  const handleAuthorizeRelease = async () => {
    if (!cargo || !consentChecked) return;

    setIsAuthorizing(true);
    try {
      const result = await cargoService.updateCargoStatus(cargo.id, "RELEASED");

      if (result.success) {
        // Update local state
        setCargo((prev) => (prev ? { ...prev, status: "RELEASED" } : null));
        setIsAuthorizationDialogOpen(false);
        setConsentChecked(false);

        // Show success message
        alert("Cargo has been successfully released from the bay!");
      } else {
        alert("Error authorizing release: " + result.error);
      }
    } catch (error) {
      console.error("Error authorizing release:", error);
      alert("Failed to authorize cargo release");
    } finally {
      setIsAuthorizing(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto" />
          <p className="text-gray-600">Loading cargo information...</p>
        </div>
      </div>
    );
  }

  if (error || !cargo) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center space-y-4 max-w-md">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto" />
          <div>
            <h1 className="text-xl font-semibold text-gray-900">
              Cargo Not Found
            </h1>
            <p className="text-gray-600 mt-2">
              {error || "The requested cargo could not be found."}
            </p>
          </div>
          <Button
            onClick={() => router.push("/release-authorization")}
            className="gap-2"
          >
            <ArrowLeft size={16} />
            Back to Release Authorization
          </Button>
        </div>
      </div>
    );
  }

  const canAuthorize = cargo.status?.toLowerCase() === "ready_for_pickup";

  return (
    <PageTransition>
      <div className="p-6 md:p-8 lg:p-10 space-y-6 bg-gray-50 min-h-screen">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-4">
          <div>
            <div className="flex items-center text-sm text-gray-500 mb-1">
              <Link
                href="/release-authorization"
                className="hover:text-gray-700 flex items-center gap-1"
              >
                <ArrowLeft size={16} />
                Release Authorization
              </Link>
              <ChevronRight size={16} className="mx-1 text-gray-400" />
              <span className="text-gray-900 font-medium">
                {cargo.tracking_number}
              </span>
            </div>
            <h1 className="text-2xl md:text-3xl font-bold text-gray-900">
              Release Authorization Details
            </h1>
            <p className="text-gray-600 mt-1">
              Cargo: {cargo.particular} • Status: {cargo.status}
            </p>
          </div>

          <div className="flex items-center gap-3">
            <StatusBadge status={cargo.status || "Unknown"} />

            {/* View Document Button */}
            {documents && documents.length > 0 && documents[0] && (
              <Button
                variant="outline"
                className="gap-2"
                onClick={() => handleViewDocument(documents[0]!)}
                disabled={documentsLoading}
              >
                {documentsLoading ? (
                  <Loader2 size={16} className="animate-spin" />
                ) : (
                  <Eye size={16} />
                )}
                View Document
              </Button>
            )}

            {canAuthorize && (
              <ProtectedEditButton entity="handovers">
                <Button
                  className="gap-2 bg-green-600 hover:bg-green-700"
                  onClick={() => setIsAuthorizationDialogOpen(true)}
                >
                  <Shield size={16} />
                  Authorize Release
                </Button>
              </ProtectedEditButton>
            )}
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Cargo Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Cargo Information */}
            <AnimatedCard className="p-6 space-y-4">
              <h2 className="text-lg font-semibold text-gray-900 mb-1 flex items-center gap-2">
                <Package size={18} />
                Cargo Information
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <InfoItem
                  label="Tracking Number"
                  value={cargo.tracking_number}
                  icon={Hash}
                />
                <InfoItem
                  label="Particular"
                  value={cargo.particular}
                  icon={FileText}
                />
                <InfoItem
                  label="Weight"
                  value={formatWeight(cargo.weight_value, cargo.weight_unit)}
                  icon={Weight}
                />
                <InfoItem
                  label="CBM"
                  value={formatCBM(cargo.cbm_value, cargo.cbm_unit)}
                  icon={Box}
                />
                <InfoItem
                  label="Dimensions"
                  value={formatDimensions(
                    cargo.dimension_length,
                    cargo.dimension_width,
                    cargo.dimension_height,
                    cargo.cbm_unit
                  )}
                  icon={Ruler}
                />
                <InfoItem
                  label="Value"
                  value={
                    cargo.total_price
                      ? `$${cargo.total_price.toLocaleString()}`
                      : "N/A"
                  }
                  icon={DollarSign}
                />
                <InfoItem
                  label="Created Date"
                  value={formatDate(cargo.created_at)}
                  icon={Calendar}
                />
                <InfoItem
                  label="Status"
                  value={<StatusBadge status={cargo.status || "Unknown"} />}
                  icon={CheckCircle}
                />
              </div>
            </AnimatedCard>

            {/* Customer Information */}
            {cargo.customers && (
              <AnimatedCard className="p-6 space-y-4">
                <h2 className="text-lg font-semibold text-gray-900 mb-1 flex items-center gap-2">
                  <User size={18} />
                  Customer Information
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <InfoItem
                    label="Company Name"
                    value={cargo.customers.name}
                    icon={Building}
                  />
                  <InfoItem
                    label="Email"
                    value={cargo.customers.email || "N/A"}
                    icon={Mail}
                  />
                  <InfoItem
                    label="Phone"
                    value={cargo.customers.phone || "N/A"}
                    icon={Phone}
                  />
                  <InfoItem
                    label="Location"
                    value={cargo.customers.location || "N/A"}
                    icon={MapPin}
                  />
                </div>
              </AnimatedCard>
            )}

            {/* Freight Information */}
            {cargo.batches?.freights && (
              <AnimatedCard className="p-6 space-y-4">
                <h2 className="text-lg font-semibold text-gray-900 mb-1 flex items-center gap-2">
                  <Truck size={18} />
                  Freight Information
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <InfoItem
                    label="Freight Name"
                    value={cargo.batches.freights.name}
                    icon={FileText}
                  />
                  <InfoItem
                    label="Freight Code"
                    value={cargo.batches.freights.code}
                    icon={Hash}
                  />
                  <InfoItem
                    label="Type"
                    value={cargo.batches.freights.type || "N/A"}
                    icon={Truck}
                  />
                  <InfoItem
                    label="Vehicle"
                    value={cargo.batches.freights.vehicle || "N/A"}
                    icon={Truck}
                  />
                </div>
              </AnimatedCard>
            )}
          </div>

          {/* Right Column - Batch & Shipment Info */}
          <div className="lg:col-span-1 space-y-6">
            {/* Batch Information */}
            {cargo.batches && (
              <AnimatedCard className="p-6 space-y-4">
                <h2 className="text-lg font-semibold text-gray-900 mb-1 flex items-center gap-2">
                  <Container size={18} />
                  Batch Information
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <InfoItem
                    label="Batch Code"
                    value={cargo.batches.code || "N/A"}
                    icon={Hash}
                  />
                  <InfoItem
                    label="Batch Name"
                    value={cargo.batches.name || "N/A"}
                    icon={FileText}
                  />
                  <InfoItem
                    label="Batch Status"
                    value={
                      <StatusBadge status={cargo.batches.status || "Unknown"} />
                    }
                    icon={CheckCircle}
                  />
                  {cargo.batches.freights && (
                    <>
                      <InfoItem
                        label="Freight"
                        value={cargo.batches.freights.name || "N/A"}
                        icon={Truck}
                      />
                      <InfoItem
                        label="Origin"
                        value={cargo.batches.freights.origin || "N/A"}
                        icon={MapPin}
                      />
                      <InfoItem
                        label="Destination"
                        value={cargo.batches.freights.destination || "N/A"}
                        icon={MapPin}
                      />
                    </>
                  )}
                </div>
              </AnimatedCard>
            )}

            {/* Release Authorization Status */}
            <AnimatedCard className="p-6 space-y-4">
              <h2 className="text-lg font-semibold text-gray-900 mb-1 flex items-center gap-2">
                <Shield size={18} />
                Authorization Status
              </h2>
              <div className="space-y-4">
                <InfoItem
                  label="Current Status"
                  value={<StatusBadge status={cargo.status || "Unknown"} />}
                  icon={CheckCircle}
                />
                <InfoItem
                  label="Can Authorize"
                  value={canAuthorize ? "Yes" : "No"}
                  icon={Shield}
                />
                {canAuthorize && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                    <p className="text-sm text-green-800 font-medium">
                      ✅ Ready for Release Authorization
                    </p>
                    <p className="text-xs text-green-600 mt-1">
                      This cargo has been delivered and is ready to be released
                      from the bay.
                    </p>
                  </div>
                )}
                {!canAuthorize && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                    <p className="text-sm text-yellow-800 font-medium">
                      ⏳ Not Ready for Release
                    </p>
                    <p className="text-xs text-yellow-600 mt-1">
                      Cargo must be in "Delivered" status before it can be
                      released.
                    </p>
                  </div>
                )}
              </div>
            </AnimatedCard>

            {/* Documents Section */}
            <AnimatedCard className="p-6 space-y-4">
              <h2 className="text-lg font-semibold text-gray-900 mb-1 flex items-center gap-2">
                <FileText size={18} />
                Documents ({documents.length})
              </h2>
              {documentsLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="flex items-center gap-2">
                    <Loader2 size={16} className="animate-spin" />
                    <span className="text-sm text-gray-500">
                      Loading documents...
                    </span>
                  </div>
                </div>
              ) : documents.length === 0 ? (
                <div className="text-center py-8">
                  <FileText size={24} className="text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">
                    No documents available
                  </p>
                  <p className="text-xs text-gray-400">
                    Documents will appear here when uploaded
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {documents.map((doc) => (
                    <div
                      key={doc.id}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                    >
                      <div className="flex items-center gap-3">
                        <FileText size={16} className="text-gray-500" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {doc.name}
                          </p>
                          <p className="text-xs text-gray-500">
                            {doc.category} • {formatDateTime(doc.created_at)}
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="gap-2"
                        onClick={() => handleViewDocument(doc)}
                      >
                        <Eye size={14} />
                        View
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </AnimatedCard>
          </div>
        </div>

        {/* Authorization Confirmation Dialog */}
        <AlertDialog
          open={isAuthorizationDialogOpen}
          onOpenChange={setIsAuthorizationDialogOpen}
        >
          <AlertDialogContent className="max-w-md">
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-green-600" />
                Authorize Cargo Release
              </AlertDialogTitle>
              <AlertDialogDescription className="space-y-3">
                <p>You are about to authorize the release of cargo:</p>
                <div className="bg-gray-50 rounded-lg p-3 space-y-2">
                  <p className="text-sm font-medium text-gray-900">
                    {cargo.tracking_number} - {cargo.particular}
                  </p>
                  <p className="text-xs text-gray-600">
                    Customer: {cargo.customers?.name || "N/A"}
                  </p>
                </div>
                <div className="flex items-start space-x-2">
                  <Checkbox
                    id="consent"
                    checked={consentChecked}
                    onCheckedChange={(checked) =>
                      setConsentChecked(checked as boolean)
                    }
                  />
                  <label
                    htmlFor="consent"
                    className="text-sm text-gray-700 leading-relaxed cursor-pointer"
                  >
                    I confirm that I have verified the cargo details and
                    authorize its release from the bay. This action cannot be
                    undone.
                  </label>
                </div>
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={isAuthorizing}>
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={handleAuthorizeRelease}
                disabled={!consentChecked || isAuthorizing}
                className="bg-green-600 hover:bg-green-700"
              >
                {isAuthorizing ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Authorizing...
                  </>
                ) : (
                  <>
                    <Shield className="h-4 w-4 mr-2" />
                    Authorize Release
                  </>
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Document Preview Dialog */}
        <DocumentPreviewDialog
          isOpen={isOpen}
          onClose={closePreview}
          document={document}
        />
      </div>
    </PageTransition>
  );
}

// Export the page with RBAC protection
export default withRBAC(
  ReleaseAuthorizationDetailPage,
  "handovers", // Entity
  "view", // Required action
  <div className="p-6 text-center">
    <h1 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h1>
    <p className="text-gray-500">
      You don't have permission to view release authorization details.
    </p>
  </div> // Fallback content
);
