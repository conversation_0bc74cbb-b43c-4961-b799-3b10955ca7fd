"use client";

import { memo, useMemo, useCallback } from "react";
import { Navigation, CheckSquare } from "lucide-react";
import { Button } from "@workspace/ui/components/button";
import { Listing } from "@/modules/listing";
import {
  type ColumnFilter,
  type TableColumn,
} from "@/components/ui/filter-panel";
import {
  type ReleaseAuthorizationState,
  type ReleaseAuthorizationDisplay,
  ITEMS_PER_PAGE,
} from "../types";
import { getReleaseAuthorizationTableColumns } from "./ReleaseAuthorizationTableColumns";
import { ReleaseAuthorizationCardRenderer } from "./ReleaseAuthorizationCardRenderer";

interface ReleaseAuthorizationContentProps {
  state: ReleaseAuthorizationState;
  filteredShipments: ReleaseAuthorizationDisplay[];
  onStateUpdate: (updates: Partial<ReleaseAuthorizationState>) => void;
  onRefresh: () => void;
  onAuthorizationAction: (
    action: string,
    shipment?: ReleaseAuthorizationDisplay
  ) => void;
  onBulkCreateTasks: () => void;
  onClearSelections?: () => void;
}

/**
 * Content component for Release Authorization Management
 *
 * Handles the main listing, filtering, and display of release authorization data.
 * Uses the Listing layout component for consistent UI patterns.
 */
export const ReleaseAuthorizationContent =
  memo<ReleaseAuthorizationContentProps>(
    ({
      state,
      filteredShipments,
      onStateUpdate,
      onRefresh,
      onAuthorizationAction,
      onBulkCreateTasks,
      onClearSelections,
    }) => {
      // Define table columns for dynamic filtering
      const releaseTableColumns: TableColumn[] = [
        {
          key: "trackingNumber",
          label: "Tracking Number",
          type: "string",
          searchable: true,
        },
        {
          key: "batchCode",
          label: "Batch Code",
          type: "string",
          searchable: true,
        },
        {
          key: "releaseCode",
          label: "Release Code",
          type: "string",
          searchable: true,
        },
        {
          key: "customer",
          label: "Customer",
          type: "string",
          searchable: true,
        },
        {
          key: "cargoType",
          label: "Cargo Type",
          type: "enum",
          searchable: true,
        },
        { key: "status", label: "Status", type: "enum", searchable: true },
        {
          key: "qrVerification",
          label: "QR Verified",
          type: "boolean",
          searchable: false,
        },
        {
          key: "biometricVerification",
          label: "Biometric Verified",
          type: "boolean",
          searchable: false,
        },
        {
          key: "arrivalDate",
          label: "Arrival Date",
          type: "date",
          searchable: true,
        },
        { key: "origin", label: "Origin", type: "string", searchable: true },
        {
          key: "destination",
          label: "Destination",
          type: "string",
          searchable: true,
        },
        {
          key: "createdAt",
          label: "Created Date",
          type: "date",
          searchable: true,
        },
        {
          key: "updatedAt",
          label: "Updated Date",
          type: "date",
          searchable: true,
        },
      ];

      // Category definitions for quick filters
      const statusCategories = [
        {
          key: "pending",
          label: "Pending",
          count: filteredShipments.filter((s) => s.status === "pending").length,
        },
        {
          key: "authorized",
          label: "Authorized",
          count: filteredShipments.filter((s) => s.status === "authorized")
            .length,
        },
        {
          key: "hold",
          label: "On Hold",
          count: filteredShipments.filter((s) => s.status === "hold").length,
        },
        {
          key: "verified",
          label: "Verified",
          count: filteredShipments.filter((s) => s.status === "verified")
            .length,
        },
      ];

      // Pagination calculations
      const totalItems = filteredShipments.length;
      const totalPages = Math.ceil(totalItems / ITEMS_PER_PAGE);
      const startIndex = (state.currentPage - 1) * ITEMS_PER_PAGE;
      const endIndex = startIndex + ITEMS_PER_PAGE;
      const paginatedData = filteredShipments.slice(startIndex, endIndex);

      // Table column definitions for Listing.Table
      const tableColumns = useMemo(() => {
        return getReleaseAuthorizationTableColumns({
          onAction: onAuthorizationAction,
        });
      }, [onAuthorizationAction]);

      // Render bulk actions
      const renderBulkActions = useCallback(
        () => (
          <>
            <Button
              variant="outline"
              size="sm"
              onClick={onBulkCreateTasks}
              className="gap-2"
              disabled={state.selectedShipments.size === 0}
            >
              <CheckSquare size={16} />
              Create Tasks
            </Button>
          </>
        ),
        [onBulkCreateTasks, state.selectedShipments.size]
      );

      // Empty state for table
      const tableEmptyState = (
        <div className="text-center py-12">
          <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
            <Navigation className="h-6 w-6 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-1">
            No release authorizations found
          </h3>
          <p className="text-gray-500 mb-4">
            No shipments are currently pending authorization
          </p>
        </div>
      );

      return (
        <Listing>
          <Listing.Filters
            searchTerm={state.searchTerm}
            onSearchChange={(term: string) =>
              onStateUpdate({ searchTerm: term })
            }
            onRefresh={onRefresh}
            loading={state.loading}
            columnFilters={state.columnFilters}
            onColumnFilterAdd={(filter: ColumnFilter) =>
              onStateUpdate({ columnFilters: [...state.columnFilters, filter] })
            }
            onColumnFilterRemove={(index: number) =>
              onStateUpdate({
                columnFilters: state.columnFilters.filter(
                  (_, i) => i !== index
                ),
              })
            }
            columns={releaseTableColumns}
            tableData={state.shipments}
            enableDynamicFilters={true}
            defaultFilterColumn="trackingNumber"
            autoSelectDefaultColumn={true}
            bulkActions={renderBulkActions()}
            selectedCount={state.selectedShipments.size}
            showBulkActions={state.selectedShipments.size > 0}
            onClearSelections={onClearSelections}
          />

          <Listing.Controls
            entity="release authorizations"
            length={filteredShipments.length}
            viewMode={state.viewMode}
            onViewModeChange={(mode: "cards" | "table") =>
              onStateUpdate({ viewMode: mode })
            }
            categoryFilter={state.statusFilter}
            categories={statusCategories}
            onCategoryFilterChange={(filter: string) =>
              onStateUpdate({ statusFilter: filter, currentPage: 1 })
            }
          />

          {state.viewMode === "table" ? (
            <Listing.Table
              data={paginatedData}
              columns={tableColumns}
              loading={state.loading}
              enableCheckboxes={true}
              selectedRowIds={Array.from(state.selectedShipments)}
              onSelectionChange={(selectedIds) =>
                onStateUpdate({ selectedShipments: new Set(selectedIds) })
              }
              getRowId={(item) => item.id}
              emptyState={tableEmptyState}
              pagination={{
                currentPage: state.currentPage,
                totalPages,
                totalItems,
                itemsPerPage: ITEMS_PER_PAGE,
                onPageChange: (page: number) =>
                  onStateUpdate({ currentPage: page }),
              }}
            />
          ) : (
            <Listing.Cards
              data={paginatedData}
              loading={state.loading}
              emptyState={tableEmptyState}
              onItemClick={(shipment) =>
                onAuthorizationAction("view", shipment)
              }
              renderCard={(shipment: ReleaseAuthorizationDisplay) => (
                <ReleaseAuthorizationCardRenderer
                  shipment={shipment}
                  onAction={onAuthorizationAction}
                />
              )}
            />
          )}
        </Listing>
      );
    }
  );

ReleaseAuthorizationContent.displayName = "ReleaseAuthorizationContent";
