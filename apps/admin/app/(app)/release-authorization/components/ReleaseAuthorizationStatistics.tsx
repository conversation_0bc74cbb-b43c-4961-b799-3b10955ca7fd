"use client";

import { memo } from "react";
import { Package, CheckCircle2, <PERSON>, Lock } from "lucide-react";
import { Overview } from "@/modules/layouts/overview";
import { Listing } from "@/modules/listing";
import { StatCard } from "@/components/ui/stat-card";
import { type ReleaseStats } from "../types";

interface ReleaseAuthorizationStatisticsProps {
  releaseStats: ReleaseStats;
  loading: boolean;
}

/**
 * Statistics component for Release Authorization page
 *
 * Displays key metrics in a grid layout using StatCard components.
 * Memoized to prevent unnecessary re-renders.
 */
export const ReleaseAuthorizationStatistics =
  memo<ReleaseAuthorizationStatisticsProps>(({ releaseStats, loading }) => {
    return (
      <Overview.Statistics columns="grid-cols-4">
        <Listing.StatCard
          name="Total Shipments"
          value={releaseStats.totalCargo}
          caption="Active shipments"
          icon={Package}
          color="primary"
          loading={loading}
        />
        <Listing.StatCard
          name="Authorized"
          value={releaseStats.authorized}
          caption="Ready for pickup"
          icon={CheckCircle2}
          color="green"
          loading={loading}
        />
        <Listing.StatCard
          name="Pending"
          value={releaseStats.pendingAuthorization}
          caption="Awaiting approval"
          icon={Clock}
          color="amber"
          loading={loading}
        />
        <Listing.StatCard
          name="On Hold"
          value={releaseStats.onHold}
          caption="Payment issues"
          icon={Lock}
          color="red"
          loading={loading}
        />
      </Overview.Statistics>
    );
  });

ReleaseAuthorizationStatistics.displayName = "ReleaseAuthorizationStatistics";
