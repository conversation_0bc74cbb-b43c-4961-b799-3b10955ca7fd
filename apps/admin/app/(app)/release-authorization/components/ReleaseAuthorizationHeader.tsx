"use client";

import { memo } from "react";
import { RefreshCw } from "lucide-react";
import { Button } from "@workspace/ui/components/button";
import { Overview } from "@/modules/layouts/overview";

interface ReleaseAuthorizationHeaderProps {
  loading: boolean;
  onRefresh: () => void;
}

/**
 * Header component for Release Authorization page
 *
 * Displays the page title, description, and action buttons.
 * Memoized to prevent unnecessary re-renders.
 */
export const ReleaseAuthorizationHeader = memo<ReleaseAuthorizationHeaderProps>(
  ({ loading, onRefresh }) => {
    return (
      <Overview.Header
        title="Release Authorization"
        caption="Manage and approve cargo release authorizations with QR code verification"
        actions={
          <Button
            variant="outline"
            onClick={onRefresh}
            disabled={loading}
            aria-label="Refresh release authorization data"
          >
            <RefreshCw
              className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
        }
      />
    );
  }
);

ReleaseAuthorizationHeader.displayName = "ReleaseAuthorizationHeader";
