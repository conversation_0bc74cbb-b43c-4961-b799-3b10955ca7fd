"use client";

import { useRB<PERSON> } from "@/lib/hooks/useRBAC";
import { useReleaseAuthorization } from "../hooks/useReleaseAuthorization";

// Components
import { Overview } from "@/modules/layouts/overview";
import { ReleaseAuthorizationHeader } from "./ReleaseAuthorizationHeader";
import { ReleaseAuthorizationStatistics } from "./ReleaseAuthorizationStatistics";
import { ReleaseAuthorizationContent } from "./ReleaseAuthorizationContent";
import { AuthorizationModal } from "./AuthorizationModal";
import { BulkTaskCreateDialog } from "@/components/ui/bulk-task-create";

/**
 * Main container component for Release Authorization Management
 *
 * This component manages all the state and business logic for the release authorization page.
 * It follows the container/presenter pattern for better separation of concerns.
 */
export function ReleaseAuthorizationContainer() {
  const { hasPermission } = useRBAC();

  // Use custom hook for all release authorization logic
  const {
    state,
    filteredShipments,
    updateState,
    handleRefresh,
    handleAuthorizationAction,
    handleDialogClose,
    handleMutation,
    handleClearSelections,
    handleBulkCreateTasks,
    isBulkTaskDialogOpen,
    setIsBulkTaskDialogOpen,
    selectedItemsForTasks,
    handleTasksCreated,
  } = useReleaseAuthorization();

  // Check if user has permission to create release authorizations
  const canCreateAuthorization = hasPermission("handovers", "create");

  return (
    <Overview className="p-6 space-y-8">
      <ReleaseAuthorizationHeader
        loading={state.loading}
        onRefresh={handleRefresh}
      />

      <ReleaseAuthorizationStatistics
        releaseStats={state.releaseStats}
        loading={state.loading}
      />

      <Overview.Content>
        <ReleaseAuthorizationContent
          state={state}
          filteredShipments={filteredShipments}
          onStateUpdate={updateState}
          onRefresh={handleRefresh}
          onAuthorizationAction={handleAuthorizationAction}
          onBulkCreateTasks={handleBulkCreateTasks}
          onClearSelections={handleClearSelections}
        />
      </Overview.Content>

      {/* Bulk Task Create Dialog */}
      <BulkTaskCreateDialog
        isOpen={isBulkTaskDialogOpen}
        onClose={() => setIsBulkTaskDialogOpen(false)}
        onTasksCreated={handleTasksCreated}
        selectedItems={selectedItemsForTasks}
        associatedTable="shipments"
        title="Create Tasks for Selected Release Authorizations"
      />

      {/* Authorization Modal */}
      <AuthorizationModal
        isOpen={state.isAuthModalOpen}
        onClose={() => handleDialogClose("authorization")}
        shipment={state.selectedShipment}
        onSuccess={handleMutation}
      />
    </Overview>
  );
}
