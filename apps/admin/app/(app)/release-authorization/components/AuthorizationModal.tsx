"use client";

import { useState, useEffect, memo } from "react";
import { X, CheckCircle, Loader2, FileText, AlertCircle } from "lucide-react";
import { Button } from "@workspace/ui/components/button";
import { Textarea } from "@workspace/ui/components/textarea";
import { motion } from "framer-motion";
import { useAuth } from "@/components/providers/AuthProvider";
import { releaseDocumentService } from "@/lib/logistics/operations/release-documents";
import { createPersonnelInfo } from "@/lib/invoice-templates";
import {
  DocumentPreviewDialog,
  useDocumentPreview,
} from "@/components/ui/document-preview-dialog";
import { DocumentToolbar } from "@/components/ui/document-toolbar";
import { codeGeneratorService } from "@/lib/logistics/operations/code-generator";
import { type ReleaseAuthorizationDisplay, type DocumentData } from "../types";

interface AuthorizationModalProps {
  isOpen: boolean;
  onClose: () => void;
  shipment: ReleaseAuthorizationDisplay | null;
  onSuccess?: () => void;
}

/**
 * Authorization Modal Component
 *
 * Handles the release authorization document generation process.
 * Memoized to prevent unnecessary re-renders.
 */
export const AuthorizationModal = memo<AuthorizationModalProps>(
  ({ isOpen, onClose, shipment, onSuccess }) => {
    const [releaseCode, setReleaseCode] = useState("");
    const [documentGenerated, setDocumentGenerated] = useState(false);
    const [generating, setGenerating] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [comments, setComments] = useState("");
    const [documentData, setDocumentData] = useState<DocumentData | null>(null);

    // Document preview hook
    const {
      isOpen: isPreviewOpen,
      document: previewDocument,
      openPreview,
      closePreview,
    } = useDocumentPreview();

    // Get current user from auth context
    const {
      user: authUser,
      isLoading: authLoading,
      isAuthenticated,
    } = useAuth();

    // Generate release code
    const generateReleaseCode = (cargoTrackingNumber: string) => {
      return codeGeneratorService.generateReleaseCode(cargoTrackingNumber);
    };

    // Initialize modal state
    useEffect(() => {
      if (isOpen && shipment) {
        if (shipment.hasReleaseAuthorization && shipment.releaseCode) {
          setReleaseCode(shipment.releaseCode);
          setDocumentGenerated(shipment.documentGenerated || false);

          if (shipment.documentGenerated) {
            setDocumentData({
              fileName: `Release Authorization - ${shipment.trackingNumber}`,
              documentNumber: shipment.releaseCode,
              qrCodeData: shipment.releaseCode,
            });
          }
        } else {
          setDocumentGenerated(false);
          // Use shipment tracking number as cargo tracking number for release code
          const cargoTrackingNumber = shipment.trackingNumber || "UNKNOWN";
          const generatedCode = generateReleaseCode(cargoTrackingNumber);
          setReleaseCode(generatedCode);
        }
      } else if (!isOpen) {
        // Reset state when modal closes
        setDocumentGenerated(false);
        setGenerating(false);
        setError(null);
        setDocumentData(null);
        setReleaseCode("");
        setComments("");
      }
    }, [isOpen, shipment]);

    // Generate release authorization document
    const generateReleaseDocument = async () => {
      if (!shipment || !authUser) return;

      setGenerating(true);
      setError(null);

      try {
        // Create personnel info from current user
        const personnelInfo = createPersonnelInfo({
          id: authUser.id,
          name: authUser.name,
          role: authUser.role
            ? {
                name: authUser.role.name,
                department: authUser.role.department
                  ? { name: authUser.role.department.name }
                  : undefined,
              }
            : undefined,
        });

        // Generate the release document
        const result = await releaseDocumentService.generateReleaseDocument(
          {
            cargo: shipment,
            personnelInfo,
            additionalData: {
              notes: comments || "Release authorization document generated",
              specialInstructions:
                "Please verify recipient identity before release",
            },
            autoUpdateStatus: true,
          },
          authUser.accountId
        );

        if (!result.success || !result.data) {
          throw new Error(result.error || "Failed to generate document");
        }

        // Store document data
        setDocumentData({
          fileName: result.data.fileName,
          documentNumber: result.data.documentNumber,
          qrCodeData: result.data.releaseCode,
          downloadUrl: result.data.downloadUrl,
          pdfBlob: result.data.pdfBlob,
        });

        setDocumentGenerated(true);

        if (onSuccess) {
          onSuccess();
        }
      } catch (err) {
        console.error("Error generating release document:", err);
        const errorMessage =
          err instanceof Error
            ? err.message
            : "Failed to generate release document";
        setError(errorMessage);
      } finally {
        setGenerating(false);
      }
    };

    // Handle document actions
    const handleDownloadDocument = async () => {
      if (documentData?.pdfBlob) {
        const url = URL.createObjectURL(documentData.pdfBlob);
        const link = document.createElement("a");
        link.href = url;
        link.download = documentData.fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      }
    };

    const handleViewDocument = () => {
      if (documentData?.pdfBlob) {
        const url = URL.createObjectURL(documentData.pdfBlob);
        openPreview({
          uri: url,
          fileName: documentData.fileName,
          fileType: "pdf",
        });
      }
    };

    const handlePrintDocument = () => {
      if (documentData?.pdfBlob) {
        const url = URL.createObjectURL(documentData.pdfBlob);
        const printWindow = window.open(url);
        if (printWindow) {
          printWindow.onload = () => {
            printWindow.print();
            printWindow.close();
            URL.revokeObjectURL(url);
          };
        }
      }
    };

    const handleShare = (method: "whatsapp" | "email" | "copy") => {
      if (!documentData || !shipment) return;

      switch (method) {
        case "whatsapp":
          const whatsappMessage = `Release Authorization Document Generated\n\nDocument: ${documentData.fileName}\nRelease Code: ${releaseCode}\n\nPlease download the PDF document for cargo release authorization.`;
          const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(whatsappMessage)}`;
          window.open(whatsappUrl, "_blank");
          break;
        case "email":
          const subject = `Release Authorization - ${shipment.trackingNumber}`;
          const body = `Dear Team,\n\nA release authorization document has been generated for:\n\nCargo: ${shipment.trackingNumber}\nCustomer: ${shipment.customer}\nRelease Code: ${releaseCode}\n\nDocument: ${documentData.fileName}\n\nBest regards,\n${authUser?.name}`;
          const mailtoUrl = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
          window.open(mailtoUrl);
          break;
        case "copy":
          navigator.clipboard.writeText(releaseCode);
          break;
      }
    };

    if (!isOpen || !shipment) return null;

    if (authLoading) {
      return (
        <div className="fixed inset-0 bg-black/30 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="flex items-center gap-3">
              <Loader2 className="h-5 w-5 animate-spin text-primary" />
              <span className="text-gray-700">Loading user information...</span>
            </div>
          </div>
        </div>
      );
    }

    if (!isAuthenticated || !authUser) {
      return null;
    }

    return (
      <>
        <div className="fixed inset-0 bg-black/30 flex items-center justify-center z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="bg-white rounded-lg shadow-lg w-full max-w-2xl overflow-hidden"
          >
            {/* Header */}
            <div className="p-5 border-b border-gray-200 flex items-center justify-between">
              <h3 className="text-xl font-medium">Release Authorization</h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={20} />
              </button>
            </div>

            <div className="p-6">
              {!documentGenerated ? (
                // Document Generation Form
                <div className="space-y-6">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-2">
                      Cargo Information
                    </h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Tracking Number:</span>
                        <p className="font-medium">{shipment.trackingNumber}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Customer:</span>
                        <p className="font-medium">{shipment.customer}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Type:</span>
                        <p className="font-medium">{shipment.cargoType}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Release Code:</span>
                        <p className="font-medium font-mono">{releaseCode}</p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Comments (Optional)
                    </label>
                    <Textarea
                      value={comments}
                      onChange={(e) => setComments(e.target.value)}
                      placeholder="Add any additional notes or instructions..."
                      rows={3}
                    />
                  </div>

                  {error && (
                    <div className="p-3 bg-red-50 text-red-800 rounded-md border border-red-200 text-sm">
                      <div className="flex gap-2 items-start">
                        <AlertCircle className="h-5 w-5 flex-shrink-0 mt-0.5" />
                        <div>
                          <p className="font-medium">Error</p>
                          <p className="mt-1">{error}</p>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="bg-green-50 p-4 rounded-md border border-green-200">
                    <div className="flex gap-2 items-start">
                      <FileText className="h-5 w-5 text-green-600 flex-shrink-0 mt-0.5" />
                      <div>
                        <p className="font-medium text-green-800">
                          Document Generation
                        </p>
                        <p className="text-sm text-green-700 mt-1">
                          This will generate a release authorization document
                          with QR code. The cargo status will be set to "Pending
                          Authorization" until the QR code is scanned by
                          operations.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end gap-3">
                    <Button variant="outline" onClick={onClose}>
                      Cancel
                    </Button>
                    <Button
                      onClick={generateReleaseDocument}
                      disabled={generating}
                      className="gap-2"
                    >
                      {generating ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <FileText className="h-4 w-4" />
                      )}
                      {generating ? "Generating..." : "Generate Document"}
                    </Button>
                  </div>
                </div>
              ) : (
                // Document Generated - Success State
                <div className="text-center space-y-6">
                  <div className="flex justify-center">
                    <div className="bg-green-100 p-4 rounded-full">
                      <CheckCircle className="h-12 w-12 text-green-600" />
                    </div>
                  </div>

                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      Release Authorization Document Generated
                    </h3>
                    <p className="text-gray-600">
                      The release authorization document has been successfully
                      generated with QR code. The cargo status is now "Pending
                      Authorization" until scanned by operations.
                    </p>
                  </div>

                  {documentData && (
                    <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">Document Number</p>
                          <p className="font-semibold text-gray-900">
                            {documentData.documentNumber}
                          </p>
                        </div>
                        <div>
                          <p className="text-gray-500">Release Code</p>
                          <p className="font-semibold text-gray-900">
                            {releaseCode}
                          </p>
                        </div>
                        <div className="col-span-2">
                          <p className="text-gray-500">File Name</p>
                          <p className="font-semibold text-gray-900">
                            {documentData.fileName}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {documentData && (
                    <DocumentToolbar
                      onView={handleViewDocument}
                      onDownload={handleDownloadDocument}
                      onPrint={handlePrintDocument}
                      onShare={handleShare}
                      fileName={documentData.fileName}
                      className="justify-center"
                    />
                  )}

                  <div className="flex justify-center">
                    <Button onClick={onClose}>Close</Button>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        </div>

        {/* Document Preview Dialog */}
        <DocumentPreviewDialog
          isOpen={isPreviewOpen}
          document={previewDocument}
          onClose={closePreview}
        />
      </>
    );
  }
);

AuthorizationModal.displayName = "AuthorizationModal";
