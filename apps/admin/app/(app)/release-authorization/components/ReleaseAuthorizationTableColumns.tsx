"use client";

import { Fragment } from "react";
import Link from "next/link";
import {
  Plane,
  Anchor,
  Truck,
  Package,
  MoreHorizontal,
  Shield,
  ReceiptText,
  Share,
  UserCheck,
} from "lucide-react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { type ReleaseAuthorizationDisplay } from "../types";
import { getStatusColor, formatDate } from "../utils";

interface ReleaseAuthorizationTableColumnsProps {
  onAction: (action: string, shipment: ReleaseAuthorizationDisplay) => void;
}

/**
 * Table columns configuration for Release Authorization table
 */
export function getReleaseAuthorizationTableColumns({
  onAction,
}: ReleaseAuthorizationTableColumnsProps) {
  return [
    {
      key: "trackingNumber",
      label: "Tracking Number",
      render: (shipment: ReleaseAuthorizationDisplay) => (
        <div className="space-y-1">
          <Link
            href={`/cargo-management/${shipment.id}`}
            className="font-medium text-primary hover:underline text-sm"
          >
            {shipment.trackingNumber}
          </Link>
          {shipment.releaseCode && (
            <p className="text-xs text-gray-500 font-mono">
              Release: {shipment.releaseCode}
            </p>
          )}
        </div>
      ),
      className: "min-w-[180px]",
    },
    {
      key: "batch",
      label: "Batch",
      render: (shipment: ReleaseAuthorizationDisplay) => (
        <div className="text-sm">
          {shipment.batchCode && shipment.batchId ? (
            <Link
              href={`/batch-management/${shipment.batchId}`}
              className="font-mono text-primary hover:underline"
              title="View batch details"
            >
              {shipment.batchCode}
            </Link>
          ) : (
            <span className="text-gray-400 italic">Unassigned</span>
          )}
        </div>
      ),
      className: "min-w-[120px]",
    },
    {
      key: "customer",
      label: "Customer",
      render: (shipment: ReleaseAuthorizationDisplay) => (
        <div className="flex flex-col space-y-1">
          <span className="text-sm text-gray-900">{shipment.customer}</span>
          <small className="text-xs text-gray-400 mt-0.5">
            {shipment.customerPhone || ""}
          </small>
        </div>
      ),
      className: "min-w-[150px]",
    },
    {
      key: "cargoType",
      label: "Type",
      render: (shipment: ReleaseAuthorizationDisplay) => {
        const CargoIcon =
          shipment.cargoType === "AIR"
            ? Plane
            : shipment.cargoType === "SEA"
              ? Anchor
              : shipment.cargoType === "LAND"
                ? Truck
                : Package;

        return (
          <div className="flex items-center gap-2">
            <CargoIcon size={16} className="text-blue-500" />
            <span className="text-sm text-gray-900">{shipment.cargoType}</span>
          </div>
        );
      },
      className: "min-w-[100px]",
    },
    {
      key: "route",
      label: "Route",
      render: (shipment: ReleaseAuthorizationDisplay) => (
        <div className="space-y-1">
          <p className="text-sm font-medium text-gray-900">{shipment.origin}</p>
          <div className="flex items-center gap-2 text-xs text-gray-500">
            <span>→</span>
            <span>{shipment.destination}</span>
          </div>
        </div>
      ),
      className: "min-w-[150px]",
    },
    {
      key: "status",
      label: "Status",
      render: (shipment: ReleaseAuthorizationDisplay) => (
        <div className="flex flex-col space-y-1">
          <Badge
            variant="secondary"
            className={getStatusColor(shipment.status)}
          >
            {shipment.status.charAt(0).toUpperCase() + shipment.status.slice(1)}
          </Badge>
        </div>
      ),
      className: "min-w-[100px]",
    },
    {
      key: "verification",
      label: "Verification",
      render: (shipment: ReleaseAuthorizationDisplay) => (
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <div
              className={`w-2 h-2 rounded-full ${
                shipment.qrVerification ? "bg-green-500" : "bg-gray-300"
              }`}
            />
            <span className="text-xs text-gray-600">QR</span>
          </div>
          <div className="flex items-center gap-2">
            <div
              className={`w-2 h-2 rounded-full ${
                shipment.biometricVerification ? "bg-green-500" : "bg-gray-300"
              }`}
            />
            <span className="text-xs text-gray-600">Bio</span>
          </div>
        </div>
      ),
      className: "min-w-[100px]",
    },
    {
      key: "dates",
      label: "Dates",
      render: (shipment: ReleaseAuthorizationDisplay) => (
        <div className="space-y-1 text-xs text-gray-600">
          <div>
            <span className="text-gray-500">Created:</span>
            <br />
            {formatDate(shipment.createdAt)}
          </div>
          {shipment.authorizedDate && (
            <div>
              <span className="text-gray-500">Authorized:</span>
              <br />
              {formatDate(shipment.authorizedDate)}
            </div>
          )}
        </div>
      ),
      className: "min-w-[120px]",
    },
    {
      key: "shared",
      label: "Shared",
      render: (shipment: ReleaseAuthorizationDisplay) => (
        <div className="flex items-center gap-2">
          <div
            className={`w-2 h-2 rounded-full ${
              shipment.shared ? "bg-green-500" : "bg-gray-300"
            }`}
          />
          <span className="text-xs text-gray-600">
            {shipment.shared ? "Shared" : "Not Shared"}
          </span>
        </div>
      ),
      className: "min-w-[100px]",
    },
    {
      key: "actions",
      label: "Actions",
      render: (shipment: ReleaseAuthorizationDisplay) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="h-8 w-8 p-0 hover:bg-gray-100"
              onClick={(e) => e.stopPropagation()}
            >
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                onAction("view", shipment);
              }}
              className="cursor-pointer"
            >
              <ReceiptText className="mr-2 h-4 w-4" />
              View Details
            </DropdownMenuItem>
            {shipment.status === "pending" && (
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  onAction("authorize", shipment);
                }}
                className="cursor-pointer"
              >
                <Shield className="mr-2 h-4 w-4" />
                {shipment.documentGenerated
                  ? "Preview Document"
                  : "Authorize Release"}
              </DropdownMenuItem>
            )}
            {/* Share release document - only for authorized shipments with documents */}
            {shipment.documentGenerated && !shipment.shared && (
              <Fragment>
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    onAction("share", shipment);
                  }}
                  className="cursor-pointer"
                >
                  <Share className="mr-2 h-4 w-4" />
                  Share with Customer
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    onAction("manually-shared", shipment);
                  }}
                  className="cursor-pointer"
                >
                  <UserCheck className="mr-2 h-4 w-4" />
                  Manually Shared
                </DropdownMenuItem>
              </Fragment>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      ),
      className: "w-[100px]",
    },
  ];
}
