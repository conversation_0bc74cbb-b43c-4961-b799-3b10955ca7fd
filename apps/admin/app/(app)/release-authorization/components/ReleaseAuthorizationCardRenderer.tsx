"use client";

import { memo } from "react";
import Link from "next/link";
import {
  Package,
  MapPin,
  Calendar,
  User,
  MoreHorizontal,
  Eye,
  Shield,
  Share,
  Plane,
  Anchor,
  Truck,
  ReceiptText,
} from "lucide-react";
import { Button } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { type ReleaseAuthorizationDisplay } from "../types";
import { getStatusColor, formatDate } from "../utils";

interface ReleaseAuthorizationCardRendererProps {
  shipment: ReleaseAuthorizationDisplay;
  onAction: (action: string, shipment?: ReleaseAuthorizationDisplay) => void;
}

/**
 * Card renderer component for Release Authorization items
 *
 * Displays shipment information in a card format with action dropdown menu.
 * Follows the established pattern from cargo-management module.
 */
export const ReleaseAuthorizationCardRenderer =
  memo<ReleaseAuthorizationCardRendererProps>(({ shipment, onAction }) => {
    // Get freight type icon
    const getFreightIcon = (freightType: string) => {
      switch (freightType?.toLowerCase()) {
        case "air":
          return Plane;
        case "sea":
          return Anchor;
        case "road":
          return Truck;
        default:
          return Package;
      }
    };

    const FreightIcon = getFreightIcon(shipment.cargoType);

    return (
      <div className="p-4 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-lg transition-all duration-200">
        <div className="space-y-3">
          {/* Header with tracking number and actions */}
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <FreightIcon className="h-4 w-4 text-gray-500" />
                <Link
                  href={`/cargo-management/${shipment.id}`}
                  className="font-medium text-primary hover:underline text-sm"
                >
                  {shipment.trackingNumber}
                </Link>
              </div>
              {shipment.releaseCode && (
                <p className="text-xs text-gray-500 font-mono mt-1">
                  Release: {shipment.releaseCode}
                </p>
              )}
            </div>

            <div className="flex items-center gap-2">
              <div className="flex flex-col space-y-1">
                <Badge
                  variant="secondary"
                  className={`text-xs ${getStatusColor(shipment.status)}`}
                >
                  {shipment.status}
                </Badge>
                {shipment.shared && (
                  <Badge
                    variant="outline"
                    className="text-xs bg-blue-50 text-blue-700 border-blue-200"
                  >
                    Shared
                  </Badge>
                )}
              </div>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuItem
                    onClick={() => onAction("view", shipment)}
                    className="cursor-pointer"
                  >
                    <ReceiptText className="mr-2 h-4 w-4" />
                    View Details
                  </DropdownMenuItem>
                  {shipment.status === "pending" && (
                    <DropdownMenuItem
                      onClick={() => onAction("authorize", shipment)}
                      className="cursor-pointer"
                    >
                      <Shield className="mr-2 h-4 w-4" />
                      {shipment.documentGenerated
                        ? "Preview Document"
                        : "Authorize Release"}
                    </DropdownMenuItem>
                  )}
                  {/* Share release document - only for authorized shipments with documents */}
                  {shipment.documentGenerated && (
                    <DropdownMenuItem
                      onClick={() => {
                        if (!shipment.shared) {
                          onAction("share", shipment);
                        }
                      }}
                      className={`cursor-pointer ${shipment.shared ? "opacity-50" : ""}`}
                      disabled={shipment.shared}
                    >
                      <Share className="mr-2 h-4 w-4" />
                      {shipment.shared
                        ? "Already Shared"
                        : "Share with Customer"}
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Customer and batch information */}
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <User className="h-4 w-4" />
              <span>{shipment.customer}</span>
            </div>

            {shipment.batchCode && (
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Package className="h-4 w-4" />
                <span>Batch: {shipment.batchCode}</span>
              </div>
            )}

            {shipment.origin && shipment.destination && (
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <MapPin className="h-4 w-4" />
                <span>
                  {shipment.origin} → {shipment.destination}
                </span>
              </div>
            )}
          </div>

          {/* Footer with dates and document status */}
          <div className="pt-2 border-t border-gray-100">
            <div className="flex items-center justify-between text-xs text-gray-500">
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                <span>Created: {formatDate(shipment.createdAt)}</span>
              </div>

              {shipment.documentGenerated && (
                <span className="text-green-600 font-medium">
                  Document Ready
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  });

ReleaseAuthorizationCardRenderer.displayName =
  "ReleaseAuthorizationCardRenderer";
