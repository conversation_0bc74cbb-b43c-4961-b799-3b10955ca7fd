"use client";

import { useState, useEffect, useMemo, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  BellRing,
  Archive,
  Trash2,
  Search,
  ListFilter,
  Check,
  AlertTriangle,
  CheckCircle,
  MessageSquare,
  Loader2,
  RefreshCw,
  Package,
  Users,
  FileText,
  Truck,
  Calendar,
  Wifi,
  WifiOff,
  Bell,
  Volume2,
  VolumeX,
} from "lucide-react";
import { cn } from "@workspace/ui/lib/utils";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";

import {
  Ta<PERSON>,
  TabsList,
  TabsTrigger,
  TabsContent,
} from "@workspace/ui/components/tabs";
import { Badge } from "@workspace/ui/components/badge";
import { PageTransition } from "@/components/page-transition";
import { useAppSelector } from "@/store/hooks";
import { type NotificationWithAccount } from "@/lib/logistics";
import { useRealtimeNotifications } from "@/lib/hooks/useRealtimeNotifications";
import { toast } from "sonner";
import { NotificationCard } from "@/components/notifications/NotificationCard";

type NotificationDisplay = {
  id: string;
  read: boolean;
  type: string;
  objectType: string;
  objectId: string;
  message: string;
  timestamp: string;
  icon: React.ElementType;
  iconColor: string;
  rawNotification: NotificationWithAccount;
};

// Helper function to determine notification icon and type
const getNotificationDisplay = (
  notification: NotificationWithAccount
): Omit<NotificationDisplay, "rawNotification"> => {
  const name = notification.name || "";
  const message = notification.message || "";
  // READ = read notifications, UNREAD = unread notifications, INACTIVE = deleted (excluded from queries)
  const isRead =
    notification.status === "READ" || notification.status === ("READ" as any);

  // Determine type and object from name or associated table
  let type = "Notification";
  let objectType = "System";
  let objectId = notification.associated_id || notification.id;
  let icon = Bell;
  let iconColor = "text-blue-600";

  // Parse notification content to determine type
  const lowerName = name.toLowerCase();
  const lowerMessage = message.toLowerCase();

  if (lowerName.includes("delivered") || lowerMessage.includes("delivered")) {
    type = "Delivered";
    objectType = "Cargo";
    icon = CheckCircle;
    iconColor = "text-green-600";
  } else if (
    lowerName.includes("invoice") ||
    lowerMessage.includes("invoice")
  ) {
    type = "Invoice Issued";
    objectType = "Invoice";
    icon = FileText;
    iconColor = "text-blue-600";
  } else if (lowerName.includes("delay") || lowerMessage.includes("delay")) {
    type = "Delay Alert";
    objectType = "Shipment";
    icon = AlertTriangle;
    iconColor = "text-red-600";
  } else if (
    lowerName.includes("maintenance") ||
    lowerMessage.includes("maintenance")
  ) {
    type = "Maintenance Due";
    objectType = "Vehicle";
    icon = AlertTriangle;
    iconColor = "text-amber-600";
  } else if (
    lowerName.includes("status") ||
    lowerMessage.includes("completed")
  ) {
    type = "Status Update";
    objectType = "Staff";
    icon = MessageSquare;
    iconColor = "text-gray-500";
  } else if (lowerName.includes("cargo") || lowerMessage.includes("cargo")) {
    type = "Cargo Update";
    objectType = "Cargo";
    icon = Package;
    iconColor = "text-purple-600";
  } else if (
    lowerName.includes("customer") ||
    lowerMessage.includes("customer")
  ) {
    type = "Customer Update";
    objectType = "Customer";
    icon = Users;
    iconColor = "text-indigo-600";
  } else if (
    lowerName.includes("shipment") ||
    lowerMessage.includes("shipment")
  ) {
    type = "Shipment Update";
    objectType = "Shipment";
    icon = Truck;
    iconColor = "text-blue-600";
  } else if (
    lowerName.includes("schedule") ||
    lowerMessage.includes("schedule")
  ) {
    type = "Schedule Update";
    objectType = "Schedule";
    icon = Calendar;
    iconColor = "text-orange-600";
  }

  // Use associated table if available
  if (notification.associated_table) {
    objectType =
      notification.associated_table.charAt(0).toUpperCase() +
      notification.associated_table.slice(1);
  }

  // Format timestamp
  const timestamp = getTimeAgo(
    notification.created_at || new Date().toISOString()
  );

  return {
    id: notification.id,
    read: isRead,
    type,
    objectType,
    objectId,
    message: message || name,
    timestamp,
    icon,
    iconColor,
  };
};

// Helper function to get time ago
const getTimeAgo = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInMinutes = Math.floor(
    (now.getTime() - date.getTime()) / (1000 * 60)
  );

  if (diffInMinutes < 1) {
    return "Just now";
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes} min${diffInMinutes > 1 ? "s" : ""} ago`;
  } else if (diffInMinutes < 1440) {
    const hours = Math.floor(diffInMinutes / 60);
    return `${hours} hour${hours > 1 ? "s" : ""} ago`;
  } else {
    const days = Math.floor(diffInMinutes / 1440);
    return `${days} day${days > 1 ? "s" : ""} ago`;
  }
};

export default function NotificationsPage() {
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>(
    []
  );
  const [activeTab, setActiveTab] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredNotifications, setFilteredNotifications] = useState<
    NotificationDisplay[]
  >([]);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [newNotificationIds, setNewNotificationIds] = useState<Set<string>>(
    new Set()
  );
  const previousNotificationCount = useRef(0);

  // Get authenticated user from Redux store
  const { user: authUser } = useAppSelector((state) => state.auth);

  // Use realtime notifications hook
  const {
    notifications: realtimeNotifications,
    unreadCount: realtimeUnreadCount,
    isConnected,
    isLoading,
    error: connectionError,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    refreshNotifications,
    playNotificationSound,
  } = useRealtimeNotifications();

  // Convert realtime notifications to display format
  const displayNotifications = useMemo(() => {
    return realtimeNotifications.map((notification) => ({
      ...getNotificationDisplay(notification),
      rawNotification: notification,
    }));
  }, [realtimeNotifications]);

  // Detect new notifications and play sound
  useEffect(() => {
    const currentCount = displayNotifications.length;
    const previousCount = previousNotificationCount.current;

    // If we have more notifications than before, mark new ones and play sound
    if (currentCount > previousCount && previousCount > 0) {
      const newNotifications = displayNotifications.slice(
        0,
        currentCount - previousCount
      );
      const newIds = new Set(newNotifications.map((n) => n.id));

      setNewNotificationIds(newIds);

      // Play sound if enabled using hook function
      if (soundEnabled) {
        playNotificationSound();
      }

      // Show toast for new notifications
      newNotifications.forEach((notification) => {
        toast.info(notification.type, {
          description: notification.message,
          action: {
            label: "View",
            onClick: () => {
              setActiveTab("unread");
            },
          },
        });
      });

      // Clear new notification indicators after 5 seconds
      setTimeout(() => {
        setNewNotificationIds(new Set());
      }, 5000);
    }

    previousNotificationCount.current = currentCount;
  }, [displayNotifications, soundEnabled]);

  // Toggle sound
  const toggleSound = () => {
    setSoundEnabled(!soundEnabled);
    toast.success(soundEnabled ? "Sound disabled" : "Sound enabled");
  };

  // Filter and search notifications
  useEffect(() => {
    let filtered = displayNotifications;

    // Apply search filter
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (notification) =>
          notification.type.toLowerCase().includes(searchLower) ||
          notification.message.toLowerCase().includes(searchLower) ||
          notification.objectType.toLowerCase().includes(searchLower) ||
          notification.objectId.toLowerCase().includes(searchLower)
      );
    }

    setFilteredNotifications(filtered);
  }, [displayNotifications, searchTerm]);

  // Handle selection
  const handleSelect = (id: string, checked: boolean) => {
    setSelectedNotifications((prev) =>
      checked ? [...prev, id] : prev.filter((nid) => nid !== id)
    );
  };

  // Mark selected as read using hook function
  const handleMarkSelectedRead = async () => {
    if (selectedNotifications.length === 0) return;

    try {
      // Use hook's markAsRead for each notification
      await Promise.all(selectedNotifications.map((id) => markAsRead(id)));

      setSelectedNotifications([]);
      toast.success(
        `${selectedNotifications.length} notifications marked as read`
      );
      // Soft refresh the list
      await handleSoftRefresh();
    } catch (error) {
      console.error("Error marking notifications as read:", error);
      toast.error("Failed to mark notifications as read");
    }
  };

  // Archive selected (mark as read for now)
  const handleArchiveSelected = async () => {
    await handleMarkSelectedRead();
  };

  // Mark all notifications as read using hook function
  const handleMarkAllAsRead = async () => {
    if (!authUser?.accountId) return;

    try {
      // Use hook's markAllAsRead function
      await markAllAsRead();

      // Soft refresh the list
      await handleSoftRefresh();
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
      toast.error("Failed to mark all notifications as read");
    }
  };

  // Delete selected using hook function
  const handleDeleteSelected = async () => {
    if (selectedNotifications.length === 0) return;

    try {
      // Use hook's deleteNotification for each notification
      await Promise.all(
        selectedNotifications.map((id) => deleteNotification(id))
      );

      setSelectedNotifications([]);
      toast.success(`${selectedNotifications.length} notifications deleted`);
      // Soft refresh the list
      await handleSoftRefresh();
    } catch (error) {
      console.error("Error deleting notifications:", error);
      toast.error("Failed to delete notifications");
    }
  };

  // Archive single notification using hook function
  const handleArchiveItem = async (id: string) => {
    try {
      // Use hook's markAsRead function
      await markAsRead(id);

      toast.success("Notification archived");
      // Soft refresh the list
      await handleSoftRefresh();
    } catch (error) {
      console.error("Error archiving notification:", error);
      toast.error("Failed to archive notification");
    }
  };

  // Delete single notification using hook function
  const handleDeleteItem = async (id: string) => {
    if (!id) {
      toast.error("Invalid notification ID");
      return;
    }

    try {
      // Use hook's deleteNotification function
      await deleteNotification(id);

      setSelectedNotifications((prev) => prev.filter((nid) => nid !== id));
      toast.success("Notification deleted");
      // Soft refresh the list
      await handleSoftRefresh();
    } catch (error) {
      console.error("Error deleting notification:", error);
      toast.error("Failed to delete notification");
    }
  };

  // Soft refresh function with timeout protection
  const handleSoftRefresh = async () => {
    try {
      // Add timeout protection for soft refresh
      const timeoutPromise = (
        promise: Promise<any>,
        timeoutMs: number = 5000
      ) => {
        return Promise.race([
          promise,
          new Promise((_, reject) =>
            setTimeout(
              () => reject(new Error("Soft refresh timeout")),
              timeoutMs
            )
          ),
        ]);
      };

      // Trigger soft refresh with timeout protection
      await timeoutPromise(Promise.resolve(refreshNotifications()));
    } catch (error) {
      // Don't show error toast for soft refresh failures
    }
  };

  // Mark single notification as read using hook function
  const handleDismissItem = async (id: string) => {
    if (!id) {
      toast.error("Invalid notification ID");
      return;
    }

    try {
      // Use hook's markAsRead function
      await markAsRead(id);

      toast.success("Notification marked as read");
      // Soft refresh the list
      await handleSoftRefresh();
    } catch (error) {
      console.error("Error marking notification as read:", error);
      toast.error("Failed to mark notification as read");
    }
  };

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
  };

  // Render notification list component
  const renderNotificationList = (notifications: NotificationDisplay[]) => (
    <div className="overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center gap-2 text-gray-500">
            <Loader2 className="h-5 w-5 animate-spin" />
            <span>Loading notifications...</span>
          </div>
        </div>
      ) : notifications.length > 0 ? (
        <div className="w-full space-y-1 overflow-y-auto max-h-[calc(100vh-300px)]">
          <AnimatePresence>
            {notifications.map((n, index) => (
              <motion.div
                key={n.id}
                initial={{ opacity: 0, y: 20, scale: 0.95 }}
                animate={{
                  opacity: 1,
                  y: 0,
                  scale: 1,
                  ...(newNotificationIds.has(n.id) && {
                    boxShadow: "0 0 20px rgba(59, 130, 246, 0.5)",
                    borderColor: "rgb(59, 130, 246)",
                  }),
                }}
                exit={{ opacity: 0, y: -20, scale: 0.95 }}
                transition={{
                  duration: 0.3,
                  delay: index * 0.05,
                  type: "spring",
                  stiffness: 300,
                  damping: 30,
                }}
                className={cn(
                  "relative",
                  newNotificationIds.has(n.id) &&
                    "ring-2 ring-blue-500 ring-opacity-50"
                )}
              >
                {/* New notification indicator */}
                {newNotificationIds.has(n.id) && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute -top-2 -right-2 z-10"
                  >
                    <div className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full font-medium shadow-lg">
                      NEW
                    </div>
                  </motion.div>
                )}
                <NotificationCard
                  notification={n}
                  isSelected={selectedNotifications.includes(n.id)}
                  onSelect={handleSelect}
                  onArchive={handleArchiveItem}
                  onDelete={handleDeleteItem}
                  onDismiss={handleDismissItem}
                  index={index}
                />
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      ) : (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
          className="flex flex-col items-center justify-center h-64 text-center p-10"
        >
          <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
            <BellRing size={32} className="text-primary" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-1">
            {!authUser ? "Please sign in" : "All caught up!"}
          </h3>
          <p className="text-sm text-gray-500 max-w-xs">
            {!authUser && "Sign in to view your notifications."}
            {authUser &&
              activeTab === "all" &&
              !searchTerm &&
              "You have no notifications right now."}
            {authUser &&
              activeTab === "unread" &&
              !searchTerm &&
              "You have no unread notifications."}
            {authUser &&
              activeTab === "read" &&
              !searchTerm &&
              "You have no read notifications."}
            {authUser && searchTerm && "No notifications match your search."}
          </p>
        </motion.div>
      )}
    </div>
  );

  return (
    <PageTransition className="p-6 space-y-8 bg-gray-50 min-h-screen">
      {/* Header with Title and Action Buttons */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
        className="flex justify-between items-center"
      >
        {/* Left: Title */}
        <div className="flex items-center gap-4">
          <h1 className="text-2xl font-semibold text-gray-900">
            Notifications
          </h1>
          {/* Connection Status */}
          {isConnected ? (
            <Wifi className="h-5 w-5 text-green-600" />
          ) : (
            <WifiOff className="h-5 w-5 text-red-600" />
          )}
        </div>

        {/* Right: Action Buttons */}
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            className="text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 gap-1.5 px-2"
            onClick={handleMarkSelectedRead}
            disabled={selectedNotifications.length === 0 || isLoading}
          >
            <Check size={16} /> Mark as Read
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 gap-1.5 px-2"
            onClick={handleMarkAllAsRead}
            disabled={isLoading || realtimeUnreadCount === 0}
          >
            <CheckCircle size={14} /> Mark All as Read
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 gap-1.5 px-2"
            onClick={handleArchiveSelected}
            disabled={selectedNotifications.length === 0 || isLoading}
          >
            <Archive size={14} /> Archive
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="text-sm text-gray-600 hover:text-red-600 hover:bg-red-50 gap-1.5 px-2"
            onClick={handleDeleteSelected}
            disabled={selectedNotifications.length === 0 || isLoading}
          >
            <Trash2 size={14} /> Delete
          </Button>

          {/* Sound Toggle */}
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleSound}
            className="h-9 w-9"
            title={soundEnabled ? "Disable sound" : "Enable sound"}
          >
            {soundEnabled ? (
              <Volume2 size={18} className="text-green-600" />
            ) : (
              <VolumeX size={18} className="text-gray-400" />
            )}
          </Button>

          <Button
            variant="ghost"
            size="icon"
            onClick={refreshNotifications}
            disabled={isLoading}
            className="h-9 w-9"
          >
            <RefreshCw size={18} className={cn(isLoading && "animate-spin")} />
          </Button>

          <Button
            variant="outline"
            size="icon"
            className="h-9 w-9 border-gray-200 text-gray-500 hover:bg-gray-100 hover:text-gray-700"
            aria-label="Filter notifications"
          >
            <ListFilter size={18} />
          </Button>

          <div className="relative w-64">
            <Search
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              size={16}
              aria-hidden="true"
            />
            <Input
              type="search"
              placeholder="Search notifications..."
              value={searchTerm}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-9 h-9 text-sm border border-gray-200 rounded-md bg-white"
              aria-label="Search notifications"
            />
          </div>
        </div>
      </motion.div>

      {/* Connection Error */}
      {connectionError && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-3 bg-red-50 border border-red-200 rounded-lg"
        >
          <div className="flex items-center gap-2 text-red-600">
            <WifiOff className="h-4 w-4" />
            <span className="text-sm font-medium">
              Connection Error: {connectionError}
            </span>
          </div>
        </motion.div>
      )}

      {/* Horizontal Tabs */}
      <Tabs
        defaultValue="all"
        value={activeTab}
        onValueChange={setActiveTab}
        className="w-full"
      >
        <TabsList className="grid w-max grid-cols-3">
          <TabsTrigger value="all">
            <span>All Notifications</span>
            <Badge
              variant="secondary"
              className="text-xs bg-gray-100 text-gray-700"
            >
              {displayNotifications.length}
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="unread">
            <BellRing size={14} />
            <span>Unread</span>
            {realtimeUnreadCount > 0 && (
              <Badge variant="destructive" className="text-xs animate-pulse">
                {realtimeUnreadCount}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="read">
            <CheckCircle size={14} />
            <span>Read</span>
            <Badge variant="outline" className="text-xs">
              {displayNotifications.filter((n) => n.read).length}
            </Badge>
          </TabsTrigger>
        </TabsList>

        {/* Tab Content Areas */}
        <TabsContent value="all" className="mt-6 space-y-4">
          {/* Notification List for All */}
          {renderNotificationList(filteredNotifications)}
        </TabsContent>

        <TabsContent value="unread" className="mt-6 space-y-4">
          {/* Notification List for Unread */}
          {renderNotificationList(filteredNotifications.filter((n) => !n.read))}
        </TabsContent>

        <TabsContent value="read" className="mt-6 space-y-4">
          {/* Notification List for Read */}
          {renderNotificationList(filteredNotifications.filter((n) => n.read))}
        </TabsContent>
      </Tabs>
    </PageTransition>
  );
}
