"use client";

import { DashboardLayout } from "@/components/dashboard-layout";
import { AnimatePresence } from "framer-motion";
import { usePathname } from "next/navigation";

export default function AppGroupLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  return (
    <DashboardLayout>
      <AnimatePresence mode="wait">
        <div key={pathname}>{children}</div>
      </AnimatePresence>
    </DashboardLayout>
  );
}
