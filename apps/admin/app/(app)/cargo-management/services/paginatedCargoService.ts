import { cargoService } from "@/lib/logistics/operations/cargos";
import type { CargoWithRelations } from "@/lib/logistics/types/cargo";
import type { QueryParams } from "@/lib/logistics/types/common";

interface PaginatedCargoResponse {
  data: CargoWithRelations[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
  hasNextPage: boolean;
}

interface CargoFilters {
  status?: string;
  customer_id?: string;
  batch_id?: string;
  supplier_id?: string;
  category?: string;
  search?: string;
}

class PaginatedCargoService {
  async fetchCargos(
    page: number = 1,
    pageSize: number = 20,
    filters: CargoFilters = {}
  ): Promise<PaginatedCargoResponse> {
    try {
      const params: QueryParams = {
        page,
        limit: pageSize,
        filters: {
          ...filters,
          // Exclude inactive cargos
          status: filters.status || undefined,
        },
        column: "updated_at",
        ascending: false,
      };

      const response = await cargoService.getAllCargosWithRelations(params);

      if (!response.success) {
        throw new Error(response.error || "Failed to fetch cargos");
      }

      const totalItems = response.count || 0;
      const totalPages = Math.ceil(totalItems / pageSize);
      const hasNextPage = page < totalPages;

      return {
        data: response.data || [],
        totalItems,
        totalPages,
        currentPage: page,
        hasNextPage,
      };
    } catch (error) {
      console.error("Error fetching paginated cargos:", error);
      throw error;
    }
  }

  async fetchCargosByStatus(
    status: string,
    page: number = 1,
    pageSize: number = 20
  ): Promise<PaginatedCargoResponse> {
    return this.fetchCargos(page, pageSize, { status });
  }

  async fetchCargosByCustomer(
    customerId: string,
    page: number = 1,
    pageSize: number = 20
  ): Promise<PaginatedCargoResponse> {
    return this.fetchCargos(page, pageSize, { customer_id: customerId });
  }

  async fetchCargosByBatch(
    batchId: string,
    page: number = 1,
    pageSize: number = 20
  ): Promise<PaginatedCargoResponse> {
    return this.fetchCargos(page, pageSize, { batch_id: batchId });
  }

  async fetchUnassignedCargos(
    page: number = 1,
    pageSize: number = 20
  ): Promise<PaginatedCargoResponse> {
    try {
      const params: QueryParams = {
        page,
        limit: pageSize,
        filters: {
          batch_id: null, // Unassigned cargos
        },
        column: "updated_at",
        ascending: false,
      };

      const response = await cargoService.getUnassignedCargos(params);

      if (!response.success) {
        throw new Error(response.error || "Failed to fetch unassigned cargos");
      }

      const totalItems = response.count || 0;
      const totalPages = Math.ceil(totalItems / pageSize);
      const hasNextPage = page < totalPages;

      return {
        data: response.data || [],
        totalItems,
        totalPages,
        currentPage: page,
        hasNextPage,
      };
    } catch (error) {
      console.error("Error fetching unassigned cargos:", error);
      throw error;
    }
  }

  async searchCargos(
    searchTerm: string,
    page: number = 1,
    pageSize: number = 20,
    additionalFilters: CargoFilters = {}
  ): Promise<PaginatedCargoResponse> {
    return this.fetchCargos(page, pageSize, {
      ...additionalFilters,
      search: searchTerm,
    });
  }
}

export const paginatedCargoService = new PaginatedCargoService();
export type { PaginatedCargoResponse, CargoFilters };
