"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import QRCode from "qrcode";
import {
  Container,
  ChevronRight,
  QrCode,
  MapPin,
  Building,
  User,
  Phone,
  Mail,
  FileText,
  Download,
  AlertCircle,
  CheckCircle,
  Truck,
  PencilLine,
  Box,
  DollarSign,
  Ruler,
  Weight,
  Info,
  Hash,
  Clock,
  Loader2,
  RefreshCw,
  Package,
  ArrowLeft,
  Save,
  Settings,
  History,
  Upload,
  Eye,
  Skull,
  Shield,
  Printer,
  Share2,
  Copy,
} from "lucide-react";
import { Button } from "@workspace/ui/components/button";
import { toast } from "sonner";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@workspace/ui/components/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Tabs,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from "@workspace/ui/components/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import { StatusBadge } from "@/components/status-badge";
import { cn } from "@workspace/ui/lib/utils";
import { PageTransition } from "@/components/page-transition";
import { AnimatedCard } from "@/components/animated-card";
import { useAppSelector } from "@/store/hooks";
import { ProtectedEditButton, withRBAC } from "@/lib/components/RBACWrapper";
import {
  cargoService,
  documentService,
  type CargoWithRelations,
  type CargoTrackingInfo,
  type DocumentWithAccount,
  type DocumentInsert,
  Cargo_Categories,
  CARGO_CATEGORY_LABELS,
} from "@/lib/logistics";
import {
  DocumentPreviewDialog,
  useDocumentPreview,
} from "@/components/ui/document-preview-dialog";
import { parseTrackingNumber } from "@/lib/tracking-generator";
import { EditCargoDialog } from "../components/Dialog/EditCargoDialog";

// Types for timeline tracking history
interface TrackingHistoryItem {
  status: string;
  date: string;
  time: string;
  location: string;
  completed: boolean;
  icon: React.ElementType;
  description?: string;
}

// Helper function to get cargo type display
const getCargoTypeDisplay = (category?: string | null): any => {
  if (!category) return { type: "N/A", icon: Package };

  if (category === Cargo_Categories.SAFE)
    return {
      type: CARGO_CATEGORY_LABELS[Cargo_Categories.SAFE],
      icon: Shield,
    };

  if (category === Cargo_Categories.DANGEROUS)
    return {
      type: CARGO_CATEGORY_LABELS[Cargo_Categories.DANGEROUS],
      icon: Skull,
    };
};

// Helper function to format weight
const formatWeight = (weight: number | null, unit: string | null): string => {
  if (!weight) return "N/A";
  return `${weight.toLocaleString()} ${unit || "kg"}`;
};

// Helper function to format dimensions
const formatDimensions = (
  length: number | null,
  width: number | null,
  height: number | null,
  unit: string | null
): string => {
  if (!length || !width || !height) return "N/A";
  return `${length} x ${width} x ${height} ${unit || "cm"}`;
};

// Helper function to get entity type
const getEntityType = (
  cargo: CargoWithRelations
): "customer" | "supplier" | "unknown" => {
  if (cargo.customers) return "customer";
  if ((cargo as any).suppliers) return "supplier";
  return "unknown";
};

// Helper function to format CBM
const formatCBM = (cbm: number | null): string => {
  if (!cbm) return "N/A";
  return `${cbm.toFixed(3)} m³`;
};

// Helper function to format currency
const formatCurrency = (amount: number | null): string => {
  if (!amount) return "N/A";
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(amount);
};

// Helper function to format date
const formatDate = (dateString: string | null): string => {
  if (!dateString) return "N/A";
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

const InfoItem = ({
  label,
  value,
  icon: Icon,
  className,
}: {
  label: string;
  value: string | React.ReactNode;
  icon?: React.ElementType;
  className?: string;
}) => (
  <div className={className}>
    <div className="text-xs text-gray-500 mb-0.5 flex items-center gap-1">
      {Icon && <Icon size={14} className="text-gray-400" />}
      {label}
    </div>
    <div className="text-sm text-gray-900 font-medium">{value}</div>
  </div>
);

const TrackingNumberDisplay = ({
  trackingNumber,
}: {
  trackingNumber: string;
}) => {
  try {
    const parsed = parseTrackingNumber(trackingNumber);
    const {
      shippingMode,
      locationCode,
      year,
      month,
      batchIndex,
      weightCategory,
    } = parsed;

    return (
      <div className="space-y-1">
        <span className="font-mono text-sm">{trackingNumber}</span>
        <div className="text-xs text-gray-500 space-y-0.5">
          <div>
            Mode: {shippingMode} • Location: {locationCode} • Weight:{" "}
            {weightCategory === "H" ? "Heavy" : "Light"}
          </div>
          <div>
            Year: {year} • Month: {month.toString().padStart(2, "0")} • Batch:{" "}
            {batchIndex.toString().padStart(3, "0")}
          </div>
        </div>
      </div>
    );
  } catch (error) {
    // If parsing fails, just display the tracking number as-is
    return <span className="font-mono">{trackingNumber}</span>;
  }
};

// Helper function to format datetime for timeline
const formatDateTime = (
  dateString: string | null
): { date: string; time: string } => {
  if (!dateString) return { date: "N/A", time: "N/A" };
  const date = new Date(dateString);
  return {
    date: date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }),
    time: date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    }),
  };
};

// Helper function to generate enhanced tracking history from cargo status
const generateEnhancedTrackingHistory = (
  cargo: CargoWithRelations,
  trackingInfo?: CargoTrackingInfo
): TrackingHistoryItem[] => {
  const history: TrackingHistoryItem[] = [];
  const createdDateTime = formatDateTime(cargo.created_at);
  const updatedDateTime = formatDateTime(cargo.updated_at);

  // Always show cargo created
  history.push({
    status: "Cargo Created",
    date: createdDateTime.date,
    time: createdDateTime.time,
    location: cargo.batches?.freights?.origin || "Unknown",
    completed: true,
    icon: Package,
    description: "Cargo shipment was registered in the system",
  });

  // Add status-specific history
  switch (cargo.status) {
    case "PROCESSING":
      history.push({
        status: "Processing",
        date: updatedDateTime.date,
        time: updatedDateTime.time,
        location: cargo.batches?.freights?.origin || "Unknown",
        completed: true,
        icon: Clock,
        description: "Cargo is being processed for shipment",
      });
      break;
    case "IN_TRANSIT":
      history.push({
        status: "In Transit",
        date: updatedDateTime.date,
        time: updatedDateTime.time,
        location: trackingInfo?.currentLocation || "En Route",
        completed: true,
        icon: Truck,
        description: "Cargo is in transit to destination",
      });
      break;
    case "READY_FOR_PICKUP":
      history.push({
        status: "Ready for Pickup",
        date: updatedDateTime.date,
        time: updatedDateTime.time,
        location: cargo.batches?.freights?.destination || "Destination",
        completed: true,
        icon: CheckCircle,
        description: "Cargo has been delivered successfully",
      });
    case "DELIVERED":
      history.push({
        status: "Delivered",
        date: updatedDateTime.date,
        time: updatedDateTime.time,
        location: cargo.batches?.freights?.destination || "Destination",
        completed: true,
        icon: CheckCircle,
        description: "Cargo has been delivered successfully",
      });
      break;
    case "PICKED_UP":
      history.push({
        status: "Picked Up",
        date: updatedDateTime.date,
        time: updatedDateTime.time,
        location: cargo.batches?.freights?.destination || "Destination",
        completed: true,
        icon: CheckCircle,
        description: "Cargo has been picked up by recipient",
      });
      break;
  }

  // Add estimated arrival if available
  if (
    cargo.estimated_arrival &&
    cargo.status !== "PICKED_UP" &&
    cargo.status !== "RELEASED" &&
    cargo.status !== "READY_FOR_PICKUP"
  ) {
    const arrivalDateTime = formatDateTime(cargo.estimated_arrival);
    history.push({
      status: "Expected Delivery",
      date: arrivalDateTime.date,
      time: arrivalDateTime.time,
      location: cargo.batches?.freights?.destination || "Destination",
      completed: false,
      icon: MapPin,
      description: "Estimated delivery date",
    });
  }

  return history;
};

// Enhanced Timeline Item Component
const EnhancedTrackingItem = ({
  item,
  isLast,
}: {
  item: TrackingHistoryItem;
  isLast: boolean;
}) => {
  const IconComponent = item.icon;

  return (
    <li className="relative pb-6">
      {!isLast && (
        <div className="absolute left-[15px] top-8 -bottom-2 w-0.5 bg-gray-200" />
      )}
      <div className="relative flex items-start gap-4">
        <div
          className={cn(
            "w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 mt-1 z-10 border-2",
            item.completed
              ? "bg-primary text-white border-primary shadow-sm"
              : "bg-white border-gray-300 text-gray-400 shadow-sm"
          )}
        >
          <IconComponent size={16} />
        </div>
        <div className="flex-1 pt-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <p className="text-sm font-semibold text-gray-900">{item.status}</p>
            <span className="text-xs text-gray-500 font-medium">
              {item.time}
            </span>
          </div>
          <p className="text-xs text-gray-600 mb-1">
            📍 {item.location} • {item.date}
          </p>
          {item.description && (
            <p className="text-xs text-gray-500 leading-relaxed">
              {item.description}
            </p>
          )}
        </div>
      </div>
    </li>
  );
};

// Track History & Documents Tabulated Component
interface TrackHistoryAndDocumentsProps {
  cargo: CargoWithRelations;
  trackingInfo?: CargoTrackingInfo;
}

export function TrackHistoryAndDocuments({
  cargo,
  trackingInfo,
}: TrackHistoryAndDocumentsProps) {
  const [activeTab, setActiveTab] = useState("history");
  const [documents, setDocuments] = useState<DocumentWithAccount[]>([]);
  const [documentsLoading, setDocumentsLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const {
    isOpen,
    document: previewDocument,
    openPreview,
    closePreview,
  } = useDocumentPreview();

  // Get current user for document uploads
  const { user: authUser } = useAppSelector((state) => state.auth);

  // Generate enhanced timeline history
  const timelineHistory = generateEnhancedTrackingHistory(cargo, trackingInfo);

  // Helper function to get file extension
  const getFileExtension = (fileName: string): string => {
    const extension = fileName.split(".").pop()?.toLowerCase();
    return extension || "pdf";
  };

  // Helper function to format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Fetch documents for this cargo
  useEffect(() => {
    const fetchDocuments = async () => {
      setDocumentsLoading(true);
      try {
        const result = await documentService.getDocumentsByEntity(
          "cargos",
          cargo.id
        );
        if (result.success && result.data) {
          setDocuments(result.data);
        } else {
          console.error("Failed to fetch documents:", result.error);
        }
      } catch (error) {
        console.error("Error fetching documents:", error);
      } finally {
        setDocumentsLoading(false);
      }
    };

    fetchDocuments();
  }, [cargo.id]);

  // Handle file upload
  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Check if user is authenticated
    if (!authUser || !authUser.accountId) {
      alert("You must be logged in to upload documents");
      return;
    }

    setUploading(true);
    try {
      // Upload file to storage
      const uploadResult = await documentService.uploadToStorage({
        content: file,
        fileName: file.name,
        contentType: file.type,
        folder: "cargo-documents",
        metadata: {
          cargoId: cargo.id,
          trackingNumber: cargo.tracking_number,
        },
      });

      if (!uploadResult.success || !uploadResult.data) {
        alert("Failed to upload file: " + uploadResult.error);
        return;
      }

      // Create document record
      const accountId = authUser?.accountId;
      if (!accountId) {
        alert("Unable to determine user account. Please try logging in again.");
        return;
      }

      const documentData: DocumentInsert = {
        name: file.name,
        path: uploadResult.data,
        category: "cargo-document",
        description: `Document for cargo ${cargo.tracking_number || cargo.id}`,
        associated_table: "cargos",
        associated_id: cargo.id,
        account_id: accountId,
        status: "ACTIVE",
      };

      const createResult = await documentService.createDocument(documentData);
      if (createResult.success && createResult.data) {
        // Refresh documents list
        const refreshResult = await documentService.getDocumentsByEntity(
          "cargos",
          cargo.id
        );
        if (refreshResult.success && refreshResult.data) {
          setDocuments(refreshResult.data);
        }
        alert("Document uploaded successfully!");
      } else {
        console.error("Document creation error:", createResult.error);
        alert(
          "Failed to create document record: " +
            (createResult.error || "Unknown error")
        );
      }
    } catch (error) {
      console.error("Upload error:", error);
      alert("Failed to upload document");
    } finally {
      setUploading(false);
      // Reset file input
      if (event.target) {
        event.target.value = "";
      }
    }
  };

  // Handle document view
  const handleViewDocument = async (doc: DocumentWithAccount) => {
    try {
      const downloadResult = await documentService.getDocumentDownloadUrl(
        doc.path
      );

      if (downloadResult.success && downloadResult.data) {
        openPreview({
          uri: downloadResult.data,
          fileName: doc.name,
          fileType: getFileExtension(doc.name),
        });
      } else {
        alert("Failed to load document for preview");
      }
    } catch (error) {
      console.error("Error loading document:", error);
      alert("Failed to load document");
    }
  };

  // Handle document download
  const handleDownloadDocument = async (doc: DocumentWithAccount) => {
    try {
      const downloadResult = await documentService.getDocumentDownloadUrl(
        doc.path
      );
      if (downloadResult.success && downloadResult.data) {
        const link = document.createElement("a");
        link.href = downloadResult.data;
        link.download = doc.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        alert("Failed to download document");
      }
    } catch (error) {
      console.error("Error downloading document:", error);
      alert("Failed to download document");
    }
  };

  const getFileIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case "pdf":
        return <FileText className="h-4 w-4 text-red-500" />;
      case "xlsx":
      case "xls":
        return <FileText className="h-4 w-4 text-green-500" />;
      case "docx":
      case "doc":
        return <FileText className="h-4 w-4 text-blue-500" />;
      case "png":
      case "jpg":
      case "jpeg":
        return <FileText className="h-4 w-4 text-purple-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <AnimatedCard className="mt-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 rounded-none bg-gray-50">
          <TabsTrigger
            value="history"
            className="flex items-center gap-2 data-[state=active]:bg-white"
          >
            <History size={16} />
            Track History ({timelineHistory.length})
          </TabsTrigger>
          <TabsTrigger
            value="documents"
            className="flex items-center gap-2 data-[state=active]:bg-white"
          >
            <Upload size={16} />
            Documents ({documents.length})
          </TabsTrigger>
        </TabsList>

        <div className="h-[1px] bg-gray-200 flex" />

        {/* History Tab */}
        <TabsContent value="history" className="px-2 py-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">
                Cargo Track History
              </h3>
              <div className="text-sm text-gray-500">
                {timelineHistory.length} tracking points
              </div>
            </div>

            {/* Scrollable Timeline Container */}
            <div className="max-h-96 overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
              <div className="relative">
                <ul className="space-y-2">
                  {timelineHistory.map((item, index) => (
                    <EnhancedTrackingItem
                      key={index}
                      item={item}
                      isLast={index === timelineHistory.length - 1}
                    />
                  ))}
                </ul>
              </div>
            </div>

            {/* Timeline Legend */}
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex items-center gap-6 text-xs text-gray-500">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-primary border-2 border-primary"></div>
                  <span>Completed</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full bg-white border-2 border-gray-300"></div>
                  <span>Pending</span>
                </div>
                <div className="flex items-center gap-2">
                  <span>📍</span>
                  <span>Location</span>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        {/* Documents Tab */}
        <TabsContent value="documents" className="px-2 py-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">
                Uploaded Documents & Attachments
              </h3>
              <div className="flex items-center gap-2">
                <div className="relative">
                  <input
                    type="file"
                    id="document-upload"
                    className="hidden"
                    accept=".pdf,.doc,.docx,.xlsx,.xls,.png,.jpg,.jpeg"
                    onChange={handleFileUpload}
                    disabled={uploading}
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    className="gap-2"
                    onClick={() =>
                      document.getElementById("document-upload")?.click()
                    }
                    disabled={uploading}
                  >
                    {uploading ? (
                      <Loader2 size={14} className="animate-spin" />
                    ) : (
                      <Upload size={14} />
                    )}
                    {uploading ? "Uploading..." : "Upload Document"}
                  </Button>
                </div>
                <div className="text-sm text-gray-500">
                  {documents.length} files
                </div>
              </div>
            </div>

            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="font-semibold">
                      Document Name
                    </TableHead>
                    <TableHead className="font-semibold">Type</TableHead>
                    <TableHead className="font-semibold">Size</TableHead>
                    <TableHead className="font-semibold">Uploaded By</TableHead>
                    <TableHead className="font-semibold">Upload Date</TableHead>
                    <TableHead className="font-semibold">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {documentsLoading ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <div className="flex items-center justify-center gap-2">
                          <Loader2 size={16} className="animate-spin" />
                          <span className="text-sm text-gray-500">
                            Loading documents...
                          </span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : documents.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <div className="flex flex-col items-center gap-2">
                          <FileText size={24} className="text-gray-400" />
                          <span className="text-sm text-gray-500">
                            No documents uploaded yet
                          </span>
                          <span className="text-xs text-gray-400">
                            Upload documents using the button above
                          </span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    documents.map((doc) => (
                      <TableRow key={doc.id} className="hover:bg-gray-50">
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getFileIcon(getFileExtension(doc.name))}
                            <span className="text-sm font-medium">
                              {doc.name}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="text-xs bg-gray-100 px-2 py-1 rounded font-mono uppercase">
                            {getFileExtension(doc.name)}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm text-gray-600">
                            {doc.details &&
                            typeof doc.details === "object" &&
                            "size" in doc.details
                              ? formatFileSize(doc.details.size as number)
                              : "N/A"}
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <User size={14} className="text-gray-400" />
                            <span className="text-sm text-gray-600">
                              {(doc.accounts as any)?.users?.name ||
                                doc.accounts?.email ||
                                "Unknown"}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Clock size={14} className="text-gray-400" />
                            <span className="text-sm text-gray-600">
                              {formatDate(doc.created_at)}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => handleViewDocument(doc)}
                              title="View Document"
                            >
                              <Eye size={14} />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => handleDownloadDocument(doc)}
                              title="Download Document"
                            >
                              <Download size={14} />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* Document Preview Dialog */}
      <DocumentPreviewDialog
        isOpen={isOpen}
        onClose={closePreview}
        document={previewDocument}
        title="Document Preview"
      />
    </AnimatedCard>
  );
}

function CargoDetailPage() {
  const params = useParams();
  const router = useRouter();
  const cargoId = params.id as string;

  // Get authenticated user for status updates
  const { user: authUser } = useAppSelector((state) => state.auth);

  const [cargo, setCargo] = useState<CargoWithRelations | null>(null);
  const [trackingInfo, setTrackingInfo] = useState<CargoTrackingInfo | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Status update states
  const [updatingStatus, setUpdatingStatus] = useState(false);
  const [statusUpdateMessage, setStatusUpdateMessage] = useState<string | null>(
    null
  );
  const [selectedStatus, setSelectedStatus] = useState<string>("");
  const [isEditCargoOpen, setIsEditCargoOpen] = useState(false);

  // QR Code dialog state
  const [qrCodeDialogOpen, setQrCodeDialogOpen] = useState(false);

  // Extracted fetchCargoData function for reuse
  const fetchCargoData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch cargo with relations
      const cargoResult = await cargoService.getCargoWithRelations(cargoId);
      if (!cargoResult.success || !cargoResult.data) {
        setError(cargoResult.error || "Cargo not found");
        return;
      }

      setCargo(cargoResult.data);
      setSelectedStatus(cargoResult.data.status || "");

      // Fetch tracking information if tracking number exists
      if (cargoResult.data.tracking_number) {
        const trackingResult = await cargoService.getCargoTrackingInfo(
          cargoResult.data.tracking_number
        );
        if (trackingResult.success && trackingResult.data) {
          setTrackingInfo(trackingResult.data);
        }
      }
    } catch (error) {
      console.error("Error fetching cargo:", error);
      setError("Failed to fetch cargo information");
    } finally {
      setLoading(false);
    }
  };

  // const fetchCargoDocuments = () => {
  //   if (!cargo?.id) return;

  //   setDocumentsLoading(true);
  //   try {
  //     const result = await documentService.getDocumentsByEntity(
  //       "cargos",
  //       cargo.id
  //     );
  //     if (result.success && result.data) {
  //       setDocuments(result.data);
  //     } else {
  //       setDocuments([]);
  //     }
  //   } catch (error) {
  //     console.error("Error fetching documents:", error);
  //     setDocuments([]);
  //   } finally {
  //     setDocumentsLoading(false);
  //   }
  // };

  // Optimized refresh function that only updates cargo data
  const refreshCargoData = async () => {
    await fetchCargoData();
  };

  useEffect(() => {
    if (cargoId) {
      fetchCargoData();
    }
  }, [cargoId]);

  // Handle status update
  const handleStatusUpdate = async () => {
    if (!cargo || !authUser) return;

    setUpdatingStatus(true);
    setStatusUpdateMessage(null);

    try {
      const result = await cargoService.updateCargoStatus(
        cargoId,
        selectedStatus,
        `Status updated to ${selectedStatus} by ${authUser.name}`,
        authUser.id
      );

      if (result.success) {
        // Update local cargo state immediately for responsive UI
        setCargo({ ...cargo, status: selectedStatus as any });

        // Refresh cargo data to ensure all related information is up-to-date
        refreshCargoData();

        if (selectedStatus === "READY_FOR_PICKUP") {
          setStatusUpdateMessage(
            "✅ Status updated to READY FOR PICKUP! Release authorization has been automatically generated."
          );
        } else {
          setStatusUpdateMessage(
            `✅ Status updated to ${selectedStatus} successfully.`
          );
        }

        // Clear message after 5 seconds
        setTimeout(() => setStatusUpdateMessage(null), 5000);
      } else {
        setStatusUpdateMessage(`❌ Failed to update status: ${result.error}`);
      }
    } catch (error) {
      console.error("Error updating status:", error);
      setStatusUpdateMessage("❌ Failed to update cargo status");
    } finally {
      setUpdatingStatus(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto" />
          <p className="text-gray-600">Loading cargo information...</p>
        </div>
      </div>
    );
  }

  if (error || !cargo) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center space-y-4 max-w-md">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto" />
          <div>
            <h1 className="text-xl font-semibold text-gray-900">
              Cargo Not Found
            </h1>
            <p className="text-gray-600 mt-2">
              {error || "The requested cargo could not be found."}
            </p>
          </div>
          <Button
            onClick={() => router.push("/cargo-management")}
            className="gap-2"
          >
            <ArrowLeft size={16} />
            Back to Cargo Management
          </Button>
        </div>
      </div>
    );
  }

  const cargoType = getCargoTypeDisplay((cargo as any)?.type);

  return (
    <PageTransition>
      <div className="p-6 md:p-8 lg:p-10 space-y-6 bg-gray-50 min-h-screen">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-4">
          <div>
            <div className="flex items-center text-sm text-gray-500 mb-1">
              <Link
                href="/cargo-management"
                className="hover:text-gray-700 flex items-center gap-1"
              >
                <ArrowLeft size={16} />
                Cargo Management
              </Link>
              <ChevronRight size={16} className="mx-1 text-gray-400" />
              <span className="text-gray-900 font-medium">
                {cargo.tracking_number || cargo.id}
              </span>
            </div>
            <h1 className="text-3xl font-bold text-gray-900">Cargo Details</h1>
            <p className="text-sm text-gray-600 mt-1 flex items-center gap-2">
              <cargoType.icon size={16} />
              {cargoType.type} • {cargo.batches?.freights?.origin || "Unknown"}{" "}
              to {cargo.batches?.freights?.destination || "Unknown"}
            </p>
          </div>
          <div className="flex items-center gap-3 flex-shrink-0">
            <StatusBadge status={cargo.status || "UNKNOWN"} />
            <Button
              variant="outline"
              size="sm"
              onClick={refreshCargoData}
              className="gap-2"
            >
              <RefreshCw size={16} />
              Refresh
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-6">
            {/* Cargo Information */}
            <AnimatedCard className="p-6">
              <div className="flex justify-between items-center mb-5">
                <h2 className="text-lg font-semibold text-gray-900">
                  Cargo Information
                </h2>
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-1.5"
                  onClick={() => setQrCodeDialogOpen(true)}
                >
                  <QrCode size={16} />
                  QR Code
                </Button>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-x-4 gap-y-5">
                <InfoItem
                  label="Quantity"
                  value={(cargo as any).quantity || "1"}
                  icon={Package}
                />
                <InfoItem
                  label="Type"
                  value={cargoType.type}
                  icon={cargoType.icon}
                />
                <InfoItem
                  label="Created"
                  value={formatDate(cargo.created_at)}
                  icon={Clock}
                />
                <InfoItem
                  label="Tracking Number"
                  value={
                    <TrackingNumberDisplay
                      trackingNumber={cargo.tracking_number || "N/A"}
                    />
                  }
                  icon={Hash}
                />
                <InfoItem
                  label="Assigned Batch"
                  value={cargo.batches?.name || "N/A"}
                  icon={Container}
                />
                {cargo.china_tracking_number && (
                  <InfoItem
                    label="China Tracking Number"
                    value={cargo.china_tracking_number}
                    icon={Hash}
                  />
                )}
                <InfoItem
                  label="Unit Price"
                  value={formatCurrency(cargo.unit_price)}
                  icon={DollarSign}
                />
                <InfoItem
                  label="Total Value"
                  value={formatCurrency(cargo.total_price)}
                  icon={DollarSign}
                />
                <InfoItem
                  label="Volume"
                  value={formatCBM(cargo.cbm_value)}
                  icon={Box}
                />
                <InfoItem
                  label="CTN"
                  value={cargo.ctn || "N/A"}
                  icon={FileText}
                  className="sm:col-span-2 md:col-span-3"
                />
                <InfoItem
                  label="Dimensions"
                  value={formatDimensions(
                    cargo.dimension_length,
                    cargo.dimension_width,
                    cargo.dimension_height,
                    cargo.dimension_unit
                  )}
                  icon={Ruler}
                />
                <InfoItem
                  label="Weight"
                  value={formatWeight(cargo.weight_value, cargo.weight_unit)}
                  icon={Weight}
                />
                <InfoItem
                  label="Origin"
                  value={cargo.batches?.freights?.origin || "N/A"}
                  icon={MapPin}
                />
                <InfoItem
                  label="Destination"
                  value={cargo.batches?.freights?.destination || "N/A"}
                  icon={MapPin}
                />
                <InfoItem
                  label="Description"
                  value={cargo.particular || "N/A"}
                  icon={FileText}
                  className="sm:col-span-2 md:col-span-3"
                />
              </div>
            </AnimatedCard>

            {/* Track History & Documents Tabulated Section */}
            <TrackHistoryAndDocuments
              cargo={cargo}
              trackingInfo={trackingInfo || undefined}
            />
          </div>

          {/* Right Column */}
          <div className="lg:col-span-1 space-y-6">
            {/* Status Management */}
            <AnimatedCard className="p-6 space-y-4">
              <h2 className="text-lg font-semibold text-gray-900 mb-1 flex items-center gap-2">
                <Settings size={18} />
                Status Management
              </h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Update Status
                  </label>
                  <Select
                    value={selectedStatus}
                    onValueChange={setSelectedStatus}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="CREATED">Created</SelectItem>
                      <SelectItem value="LOADED">Loaded</SelectItem>
                      <SelectItem value="IN_TRANSIT">In Transit</SelectItem>
                      <SelectItem value="ARRIVED">Arrived</SelectItem>
                      <SelectItem value="PROCESSING">Processing</SelectItem>
                      <SelectItem value="CLEARED">Cleared</SelectItem>
                      <SelectItem value="UNLOADED">Unloaded</SelectItem>
                      <SelectItem value="READY_FOR_PICKUP">
                        Ready for Pickup
                      </SelectItem>
                      <SelectItem value="PICKED_UP">Picked Up</SelectItem>
                      <SelectItem value="RELEASED">Released</SelectItem>
                      <SelectItem value="DELIVERED">Delivered</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {selectedStatus === "READY_FOR_PICKUP" && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <div className="flex items-start gap-2">
                      <Info
                        size={16}
                        className="text-blue-600 mt-0.5 flex-shrink-0"
                      />
                      <div className="text-sm">
                        <p className="font-medium text-blue-900">
                          Auto Release Authorization
                        </p>
                        <p className="text-blue-700 mt-1">
                          Changing status to "READY FOR PICKUP" will
                          automatically generate a release authorization for
                          pickup.
                        </p>
                      </div>
                    </div>
                  </div>
                )}
                <Button
                  className="w-full gap-2"
                  onClick={handleStatusUpdate}
                  disabled={updatingStatus || selectedStatus === cargo.status}
                >
                  {updatingStatus ? (
                    <Loader2 size={16} className="animate-spin" />
                  ) : (
                    <Save size={16} />
                  )}
                  {updatingStatus ? "Updating..." : "Update Status"}
                </Button>
                {statusUpdateMessage && (
                  <div
                    className={cn(
                      "p-3 rounded-lg text-sm",
                      statusUpdateMessage.startsWith("✅")
                        ? "bg-green-50 border border-green-200 text-green-800"
                        : "bg-red-50 border border-red-200 text-red-800"
                    )}
                  >
                    {statusUpdateMessage}
                  </div>
                )}
              </div>
            </AnimatedCard>

            {/* Entity Information (Customer or Supplier) */}
            <AnimatedCard className="p-6 space-y-4">
              <div className="flex items-center gap-2 mb-1">
                <h2 className="text-lg font-semibold text-gray-900">
                  {getEntityType(cargo) === "customer"
                    ? "Customer"
                    : getEntityType(cargo) === "supplier"
                      ? "Supplier"
                      : "Entity"}{" "}
                  Information
                </h2>
                <span
                  className={`px-2 py-1 text-xs rounded-full font-medium ${
                    getEntityType(cargo) === "customer"
                      ? "bg-blue-100 text-blue-700"
                      : getEntityType(cargo) === "supplier"
                        ? "bg-green-100 text-green-700"
                        : "bg-gray-100 text-gray-700"
                  }`}
                >
                  {getEntityType(cargo) === "customer"
                    ? "Customer"
                    : getEntityType(cargo) === "supplier"
                      ? "Supplier"
                      : "Unknown"}
                </span>
              </div>

              {getEntityType(cargo) === "customer" && cargo.customers ? (
                <>
                  <InfoItem
                    label="Company"
                    value={cargo.customers.name}
                    icon={Building}
                  />
                  <InfoItem
                    label="Email"
                    value={cargo.customers.email || "N/A"}
                    icon={Mail}
                  />
                  <InfoItem
                    label="Phone"
                    value={cargo.customers.phone || "N/A"}
                    icon={Phone}
                  />
                  <InfoItem
                    label="Location"
                    value={cargo.customers.location || "N/A"}
                    icon={MapPin}
                  />
                </>
              ) : getEntityType(cargo) === "supplier" &&
                (cargo as any).suppliers ? (
                <>
                  <InfoItem
                    label="Tracking Number"
                    value={(cargo as any).suppliers.tracking_number || "N/A"}
                    icon={Hash}
                  />
                  <InfoItem
                    label="Phone"
                    value={(cargo as any).suppliers.phone || "N/A"}
                    icon={Phone}
                  />
                  <InfoItem
                    label="Location"
                    value={(cargo as any).suppliers.location || "N/A"}
                    icon={MapPin}
                  />
                </>
              ) : (
                <p className="text-gray-500 text-sm">
                  No{" "}
                  {getEntityType(cargo) === "customer"
                    ? "customer"
                    : getEntityType(cargo) === "supplier"
                      ? "supplier"
                      : "entity"}{" "}
                  information available
                </p>
              )}
            </AnimatedCard>

            {/* Assignee Information */}
            {cargo.users && (
              <AnimatedCard className="p-6 space-y-4">
                <h2 className="text-lg font-semibold text-gray-900 mb-1">
                  Assigned To
                </h2>
                <InfoItem
                  label="Staff Member"
                  value={cargo.users.name}
                  icon={User}
                />
                <InfoItem
                  label="Phone"
                  value={cargo.users.phone || "N/A"}
                  icon={Phone}
                />
                <InfoItem
                  label="Location"
                  value={cargo.users.location || "N/A"}
                  icon={MapPin}
                />
              </AnimatedCard>
            )}

            {/* Actions */}
            <AnimatedCard className="p-6 space-y-3">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Actions
              </h2>
              <ProtectedEditButton entity="cargo">
                <Button
                  className="w-full gap-2 bg-primary hover:bg-primary/90"
                  onClick={() => setIsEditCargoOpen(true)}
                >
                  <PencilLine size={16} />
                  Edit Cargo
                </Button>
              </ProtectedEditButton>
              <Button
                variant="outline"
                className="w-full gap-2"
                onClick={() => window.print()}
              >
                <FileText size={16} />
                Generate Report
              </Button>
              <Button
                variant="outline"
                className="w-full gap-2"
                onClick={() => router.push("/release-authorization")}
              >
                <CheckCircle size={16} />
                View Release Authorizations
              </Button>
              <Button
                variant="outline"
                className="w-full gap-2"
                onClick={() => alert("Issue reporting feature coming soon")}
              >
                <AlertCircle size={16} />
                Report Issue
              </Button>
            </AnimatedCard>
          </div>
        </div>

        <EditCargoDialog
          isOpen={isEditCargoOpen}
          onOpenChange={setIsEditCargoOpen}
          cargo={cargo}
          onSuccess={() => {
            // Optimized refresh - only update cargo data instead of full page reload
            refreshCargoData();
          }}
        />

        {/* QR Code Dialog */}
        <QRCodeDialog
          open={qrCodeDialogOpen}
          cargo={cargo}
          onOpenChange={setQrCodeDialogOpen}
        />
      </div>
    </PageTransition>
  );
}

// QR Code Dialog Component for Cargo Details
interface QRCodeDialogProps {
  open: boolean;
  cargo: CargoWithRelations | null;
  onOpenChange: (open: boolean) => void;
}

function QRCodeDialog({ open, cargo, onOpenChange }: QRCodeDialogProps) {
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState<string>("");
  const [isGenerating, setIsGenerating] = useState(false);

  // Generate QR code when dialog opens and cargo is available
  useEffect(() => {
    if (open && cargo) {
      generateQRCode();
    }
  }, [open, cargo]);

  const generateQRCode = async () => {
    if (!cargo) return;

    setIsGenerating(true);
    try {
      // Create the cargo management URL
      const cargoUrl = `${window.location.origin}/cargo-management/${cargo.id}`;

      // Generate QR code as data URL
      const qrCodeUrl = await QRCode.toDataURL(cargoUrl, {
        width: 256,
        margin: 2,
        color: {
          dark: "#000000",
          light: "#FFFFFF",
        },
      });

      setQrCodeDataUrl(qrCodeUrl);
    } catch (error) {
      console.error("Error generating QR code:", error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handlePrint = () => {
    if (!cargo || !qrCodeDataUrl) return;

    const printWindow = window.open("", "_blank");
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>QR Code - ${cargo.tracking_number}</title>
            <style>
              body {
                font-family: Arial, sans-serif;
                text-align: center;
                padding: 20px;
              }
              .qr-container {
                display: inline-block;
                border: 2px solid #000;
                padding: 20px;
                margin: 20px;
              }
              .tracking-info {
                margin-bottom: 15px;
                font-size: 14px;
              }
              .tracking-number {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 10px;
              }
            </style>
          </head>
          <body>
            <div class="qr-container">
              <div class="tracking-number">${cargo.tracking_number}</div>
              <div class="tracking-info">
                <div><strong>Cargo:</strong> ${cargo.particular}</div>
                <div><strong>Customer:</strong> ${cargo.customers?.name || "N/A"}</div>
                <div><strong>Status:</strong> ${cargo.status}</div>
                <div><strong>Weight:</strong> ${cargo.weight_value || "N/A"} ${cargo.weight_unit || ""}</div>
              </div>
              <img src="${qrCodeDataUrl}" alt="QR Code" style="max-width: 200px;" />
              <div style="margin-top: 10px; font-size: 12px; color: #666;">
                Scan to track shipment
              </div>
            </div>
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
  };

  const handleShare = async () => {
    if (!cargo) return;

    const cargoUrl = `${window.location.origin}/cargo-management/${cargo.id}`;
    const shareData = {
      title: `Cargo Tracking - ${cargo.tracking_number}`,
      text: `Track cargo shipment: ${cargo.particular}`,
      url: cargoUrl,
    };

    try {
      if (navigator.share) {
        await navigator.share(shareData);
      } else {
        // Fallback: copy to clipboard
        await navigator.clipboard.writeText(cargoUrl);
        toast.success("Tracking URL copied to clipboard!");
      }
    } catch (error) {
      console.error("Error sharing:", error);
      toast.error("Failed to share tracking URL");
    }
  };

  const handleCopyUrl = async () => {
    if (!cargo) return;

    const cargoUrl = `${window.location.origin}/cargo-management/${cargo.id}`;
    try {
      await navigator.clipboard.writeText(cargoUrl);
      toast.success("Tracking URL copied to clipboard!");
    } catch (error) {
      console.error("Error copying to clipboard:", error);
      toast.error("Failed to copy URL to clipboard");
    }
  };

  if (!cargo) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <QrCode className="h-5 w-5" />
            QR Code - {cargo.tracking_number}
          </DialogTitle>
          <DialogDescription>
            Generate QR code for cargo tracking and sharing
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Cargo Information */}
          <div className="bg-gray-50 p-4 rounded-lg space-y-2">
            <div className="text-sm">
              <span className="font-medium">Cargo:</span> {cargo.particular}
            </div>
            <div className="text-sm">
              <span className="font-medium">Customer:</span>{" "}
              {cargo.customers?.name || "N/A"}
            </div>
            <div className="text-sm">
              <span className="font-medium">Status:</span> {cargo.status}
            </div>
            <div className="text-sm">
              <span className="font-medium">Weight:</span>{" "}
              {cargo.weight_value || "N/A"} {cargo.weight_unit || ""}
            </div>
          </div>

          {/* QR Code Display */}
          <div className="flex justify-center">
            {isGenerating ? (
              <div className="flex items-center justify-center w-64 h-64 border-2 border-dashed border-gray-300 rounded-lg">
                <div className="text-center">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                  <p className="text-sm text-gray-500">Generating QR Code...</p>
                </div>
              </div>
            ) : qrCodeDataUrl ? (
              <div className="border-2 border-gray-200 p-4 rounded-lg">
                <img
                  src={qrCodeDataUrl}
                  alt="QR Code"
                  className="w-48 h-48 mx-auto"
                />
                <p className="text-xs text-gray-500 text-center mt-2">
                  Scan to track shipment
                </p>
              </div>
            ) : (
              <div className="flex items-center justify-center w-64 h-64 border-2 border-dashed border-gray-300 rounded-lg">
                <p className="text-sm text-gray-500">
                  Failed to generate QR code
                </p>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePrint}
              disabled={!qrCodeDataUrl}
              className="flex-1"
            >
              <Printer className="h-4 w-4 mr-2" />
              Print
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleShare}
              disabled={!cargo}
              className="flex-1"
            >
              <Share2 className="h-4 w-4 mr-2" />
              Share
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopyUrl}
              disabled={!cargo}
              className="flex-1"
            >
              <Copy className="h-4 w-4 mr-2" />
              Copy URL
            </Button>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// Export the page with RBAC protection
export default withRBAC(
  CargoDetailPage,
  "cargo", // Entity
  "view", // Required action
  <div className="p-6 text-center">
    <h1 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h1>
    <p className="text-gray-500">
      You don't have permission to view cargo details.
    </p>
  </div> // Fallback content
);
