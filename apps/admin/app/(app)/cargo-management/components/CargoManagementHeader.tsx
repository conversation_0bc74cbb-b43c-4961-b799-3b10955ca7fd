"use client";

import { memo } from "react";
import { RefreshCw, Plus } from "lucide-react";
import { Button } from "@workspace/ui/components/button";
import { Overview } from "@/modules/layouts/overview";

interface CargoManagementHeaderProps {
  loading: boolean;
  shouldShowCreateButton: (entity: string) => boolean;
  onRefresh: () => void;
  onCreateCargo: () => void;
}

/**
 * Header component for Cargo Management page
 *
 * Displays the page title, description, and action buttons.
 * Memoized to prevent unnecessary re-renders.
 */
export const CargoManagementHeader = memo<CargoManagementHeaderProps>(
  ({ loading, shouldShowCreateButton, onRefresh, onCreateCargo }) => {
    return (
      <Overview.Header
        title="Cargo Management"
        caption="Track and manage cargo shipments, categories, and logistics coordination"
        actions={
          <>
            <Button
              variant="outline"
              onClick={onRefresh}
              disabled={loading}
              aria-label="Refresh cargo data"
            >
              <RefreshCw
                className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`}
              />
              Refresh
            </Button>
            {shouldShowCreateButton("cargo") && (
              <Button
                onClick={onCreateCargo}
                aria-label="Create new cargo"
              >
                <Plus className="h-4 w-4 mr-2" />
                New Cargo
              </Button>
            )}
          </>
        }
      />
    );
  }
);

CargoManagementHeader.displayName = "CargoManagementHeader";
