/**
 * Cargo Management Components
 *
 * Optimized component exports for the cargo management module.
 * All components are memoized for performance and follow React best practices.
 */

export { CargoManagementContainer } from "./CargoManagementContainer";
export { CargoManagementHeader } from "./CargoManagementHeader";
export { CargoStatistics } from "./CargoStatistics";
export { CargoContent } from "./CargoContent";
export { CargoTableColumns } from "./CargoTableColumns";
export { CargoCardRenderer } from "./CargoCardRenderer";

// Dialog components
export { NewCargoDialog, EditCargoDialog, QRCodeDialog } from "./Dialog";

// Re-export types
export type {
  CargoStats,
  CargoManagementState,
  CargoDisplay,
} from "../utils/types";
