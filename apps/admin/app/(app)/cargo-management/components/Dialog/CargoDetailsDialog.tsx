"use client";

import React, { useState, useEffect, Fragment } from "react";
import {
  <PERSON><PERSON>,
  Di<PERSON><PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import { Button } from "@workspace/ui/components/button";
import {
  Package,
  MapPin,
  Building,
  User,
  Phone,
  FileText,
  Ruler,
  Weight,
  Hash,
  Clock,
  Loader2,
  Save,
  Settings,
  Info,
  Eye,
  Download,
  QrCode,
} from "lucide-react";
import { StatusBadge } from "@/components/status-badge";
import { AnimatedCard } from "@/components/animated-card";
import { cn } from "@workspace/ui/lib/utils";
import { useAppSelector } from "@/store/hooks";
import { cargoService } from "@/lib/logistics";
import { parseTrackingNumber } from "@/lib/tracking-generator";
import { type CargoDisplay } from "../../utils/types";
import { toast } from "sonner";

import {
  DocumentPreviewDialog,
  useDocumentPreview,
} from "@/components/ui/document-preview-dialog";
import { documentService } from "@/lib/logistics/operations/documents";
import { type DocumentWithAccount, type DocumentInsert } from "@/lib/logistics";

interface CargoDetailsDialogProps {
  open: boolean;
  cargo: CargoDisplay | null;
  onOpenChange: (open: boolean) => void;
  onCargoUpdate?: () => void;
  onQRCodeAction?: (cargo: CargoDisplay) => void;
}

// Helper function to format CBM
const formatCBM = (cbm: number | string | null): string => {
  if (!cbm) return "N/A";
  const cbmValue = typeof cbm === "string" ? parseFloat(cbm) : cbm;
  if (isNaN(cbmValue)) return "N/A";
  return `${cbmValue.toFixed(3)} m³`;
};

// Helper function to format date
const formatDate = (dateString: string | null): string => {
  if (!dateString) return "N/A";
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

// InfoItem component for displaying cargo details
const InfoItem = ({
  label,
  value,
  icon: Icon,
  className,
}: {
  label: string;
  value: string | React.ReactNode;
  icon?: React.ElementType;
  className?: string;
}) => (
  <div className={className}>
    <div className="text-xs text-gray-500 mb-0.5 flex items-center gap-1">
      {Icon && <Icon size={14} className="text-gray-400" />}
      {label}
    </div>
    <div className="text-sm text-gray-900 font-medium">{value}</div>
  </div>
);

// Tracking number display component
const TrackingNumberDisplay = ({
  trackingNumber,
}: {
  trackingNumber: string;
}) => {
  try {
    const parsed = parseTrackingNumber(trackingNumber);
    const {
      shippingMode,
      locationCode,
      year,
      month,
      batchIndex,
      weightCategory,
    } = parsed;

    return (
      <div className="space-y-1">
        <span className="font-mono text-sm">{trackingNumber}</span>
        <div className="text-xs text-gray-500 space-y-0.5">
          <div>
            Mode: {shippingMode} • Location: {locationCode} • Weight:{" "}
            {weightCategory === "H" ? "Heavy" : "Light"}
          </div>
          <div>
            Year: {year} • Month: {month.toString().padStart(2, "0")} • Batch:{" "}
            {batchIndex.toString().padStart(3, "0")}
          </div>
        </div>
      </div>
    );
  } catch (error) {
    // If parsing fails, just display the tracking number as-is
    return <span className="font-mono">{trackingNumber}</span>;
  }
};

/**
 * Cargo Details Dialog Component
 *
 * Displays detailed cargo information in a dialog format.
 * Includes cargo information, customer/supplier details, and status update functionality.
 * Based on the cargo detail page layout but adapted for dialog use.
 */
export const CargoDetailsDialog: React.FC<CargoDetailsDialogProps> = ({
  open,
  cargo,
  onOpenChange,
  onCargoUpdate,
  onQRCodeAction,
}) => {
  const authUser = useAppSelector((state) => state.auth.user);
  const [selectedStatus, setSelectedStatus] = useState<string>("");
  const [updatingStatus, setUpdatingStatus] = useState(false);
  const [statusUpdateMessage, setStatusUpdateMessage] = useState<string | null>(
    null
  );

  // Document
  const [activeTab, setActiveTab] = useState("history");
  const [documents, setDocuments] = useState<DocumentWithAccount[]>([]);
  const [documentsLoading, setDocumentsLoading] = useState(true);
  const [uploading, setUploading] = useState(false);

  const {
    isOpen,
    document: previewDocument,
    openPreview,
    closePreview,
  } = useDocumentPreview();

  const fetchDocuments = async () => {
    setDocumentsLoading(true);
    try {
      const result = await documentService.getDocumentsByEntity(
        "cargos",
        cargo.id
      );
      if (result.success && result.data) {
        setDocuments(result.data);
      } else {
        console.error("Failed to fetch documents:", result.error);
      }
    } catch (error) {
      console.error("Error fetching documents:", error);
    } finally {
      setDocumentsLoading(false);
    }
  };

  // Initialize selected status when dialog opens or cargo changes
  useEffect(() => {
    if (open && cargo) {
      setSelectedStatus(cargo.status);
      setStatusUpdateMessage(null);
      fetchDocuments();
    }
  }, [open, cargo]);

  if (!cargo) return null;

  // Handle status update
  const handleStatusUpdate = async () => {
    if (!cargo || !authUser) return;

    setUpdatingStatus(true);
    setStatusUpdateMessage(null);

    try {
      const result = await cargoService.updateCargoStatus(
        cargo.id,
        selectedStatus,
        `Status updated to ${selectedStatus} by ${authUser.name}`,
        authUser.id
      );

      if (result.success) {
        if (selectedStatus === "READY_FOR_PICKUP") {
          setStatusUpdateMessage(
            "✅ Status updated to READY FOR PICKUP! Release authorization has been automatically generated."
          );
        } else {
          setStatusUpdateMessage(
            `✅ Status updated to ${selectedStatus} successfully.`
          );
        }

        // Call update callback if provided
        if (onCargoUpdate) {
          onCargoUpdate();
        }

        // Clear message after 5 seconds
        setTimeout(() => setStatusUpdateMessage(null), 5000);
      } else {
        setStatusUpdateMessage(`❌ Failed to update status: ${result.error}`);
      }
    } catch (error) {
      console.error("Error updating status:", error);
      setStatusUpdateMessage("❌ Failed to update status. Please try again.");
    } finally {
      setUpdatingStatus(false);
    }
  };

  // Get cargo type configuration
  const getCargoType = (category: string) => {
    const cargoConfigs: Record<
      string,
      { type: string; icon: any; color: string }
    > = {
      DANGEROUS: { type: "Dangerous", icon: Package, color: "text-red-600" },
      SAFE: { type: "Safe", icon: Package, color: "text-green-600" },
      COPY: { type: "Copy", icon: Package, color: "text-orange-600" },
    };

    return (
      cargoConfigs[category] || {
        type: "Unknown",
        icon: Package,
        color: "text-gray-600",
      }
    );
  };

  const cargoType = getCargoType(cargo.type || "SAFE");

  // Helper function to get file extension
  const getFileExtension = (fileName: string): string => {
    const extension = fileName.split(".").pop()?.toLowerCase();
    return extension || "pdf";
  };

  // Helper function to format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Handle file upload
  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Check if user is authenticated
    if (!authUser || !authUser.accountId) {
      alert("You must be logged in to upload documents");
      return;
    }

    setUploading(true);
    try {
      // Upload file to storage
      const uploadResult = await documentService.uploadToStorage({
        content: file,
        fileName: file.name,
        contentType: file.type,
        folder: "cargo-documents",
        metadata: {
          cargoId: cargo.id,
          trackingNumber: cargo.tracking_number,
        },
      });

      if (!uploadResult.success || !uploadResult.data) {
        alert("Failed to upload file: " + uploadResult.error);
        return;
      }

      // Create document record
      const accountId = authUser?.accountId;
      if (!accountId) {
        alert("Unable to determine user account. Please try logging in again.");
        return;
      }

      const documentData: DocumentInsert = {
        name: file.name,
        path: uploadResult.data,
        category: "cargo-document",
        description: `Document for cargo ${cargo.tracking_number || cargo.id}`,
        associated_table: "cargos",
        associated_id: cargo.id,
        account_id: accountId,
        status: "ACTIVE",
      };

      const createResult = await documentService.createDocument(documentData);
      if (createResult.success && createResult.data) {
        // Refresh documents list
        const refreshResult = await documentService.getDocumentsByEntity(
          "cargos",
          cargo.id
        );
        if (refreshResult.success && refreshResult.data) {
          setDocuments(refreshResult.data);
        }
        alert("Document uploaded successfully!");
      } else {
        console.error("Document creation error:", createResult.error);
        alert(
          "Failed to create document record: " +
            (createResult.error || "Unknown error")
        );
      }
    } catch (error) {
      console.error("Upload error:", error);
      alert("Failed to upload document");
    } finally {
      setUploading(false);
      // Reset file input
      if (event.target) {
        event.target.value = "";
      }
    }
  };

  // Handle document view
  const handleViewDocument = async (doc: DocumentWithAccount) => {
    try {
      const downloadResult = await documentService.getDocumentDownloadUrl(
        doc.path
      );

      if (downloadResult.success && downloadResult.data) {
        openPreview({
          uri: downloadResult.data,
          fileName: doc.name,
          fileType: getFileExtension(doc.name),
        });
      } else {
        alert("Failed to load document for preview");
      }
    } catch (error) {
      console.error("Error loading document:", error);
      alert("Failed to load document");
    }
  };

  // Handle document download
  const handleDownloadDocument = async (doc: DocumentWithAccount) => {
    try {
      const downloadResult = await documentService.getDocumentDownloadUrl(
        doc.path
      );
      if (downloadResult.success && downloadResult.data) {
        const link = document.createElement("a");
        link.href = downloadResult.data;
        link.download = doc.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        alert("Failed to download document");
      }
    } catch (error) {
      console.error("Error downloading document:", error);
      alert("Failed to download document");
    }
  };

  const getFileIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case "pdf":
        return <FileText className="h-4 w-4 text-red-500" />;
      case "xlsx":
      case "xls":
        return <FileText className="h-4 w-4 text-green-500" />;
      case "docx":
      case "doc":
        return <FileText className="h-4 w-4 text-blue-500" />;
      case "png":
      case "jpg":
      case "jpeg":
        return <FileText className="h-4 w-4 text-purple-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <Fragment>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Cargo Details - {cargo.trackingNumber}
            </DialogTitle>
          </DialogHeader>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left Column - Main Information */}
            <div className="lg:col-span-2 space-y-6">
              {/* Cargo Information */}
              <AnimatedCard className="p-6">
                <div className="flex justify-between items-center mb-5">
                  <h2 className="text-lg font-semibold text-gray-900">
                    Cargo Information
                  </h2>
                  <Button
                    variant="outline"
                    size="sm"
                    className="gap-1.5"
                    onClick={() => {
                      if (onQRCodeAction && cargo) {
                        onQRCodeAction(cargo);
                      } else {
                        toast.info("QR Code functionality not available");
                      }
                    }}
                  >
                    <QrCode size={16} />
                    QR Code
                  </Button>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-x-4 gap-y-5">
                  <InfoItem
                    label="Tracking Number"
                    value={
                      <TrackingNumberDisplay
                        trackingNumber={cargo.trackingNumber}
                      />
                    }
                    icon={Hash}
                    className="sm:col-span-2 md:col-span-3"
                  />
                  {cargo.chinaTrackingNumber &&
                    cargo.chinaTrackingNumber !== "N/A" && (
                      <InfoItem
                        label="China Tracking"
                        value={cargo.chinaTrackingNumber}
                        icon={Hash}
                      />
                    )}
                  <InfoItem
                    label="Type"
                    value={cargoType.type}
                    icon={cargoType.icon}
                  />
                  <InfoItem
                    label="Status"
                    value={<StatusBadge status={cargo.status} />}
                    icon={Settings}
                  />
                  <InfoItem
                    label="Created"
                    value={formatDate(cargo.date)}
                    icon={Clock}
                  />
                  <InfoItem
                    label="Updated"
                    value={formatDate(cargo.updatedAt)}
                    icon={Clock}
                  />
                  <InfoItem
                    label="CTN"
                    value={cargo.ctn?.toString() || "N/A"}
                    icon={Package}
                  />
                  <InfoItem
                    label="CBM"
                    value={formatCBM(cargo.cbm)}
                    icon={Ruler}
                  />
                  <InfoItem label="Weight" value={cargo.weight} icon={Weight} />
                  <InfoItem
                    label="Origin"
                    value={cargo.origin || "N/A"}
                    icon={MapPin}
                  />
                  <InfoItem
                    label="Destination"
                    value={cargo.destination || "N/A"}
                    icon={MapPin}
                  />
                  <InfoItem
                    label="Description"
                    value={cargo.particular || "N/A"}
                    icon={FileText}
                    className="sm:col-span-2 md:col-span-3"
                  />
                </div>
              </AnimatedCard>

              {/* Documents */}
              <AnimatedCard>
                <div className="pb-3">
                  <h2 className="text-lg font-semibold text-gray-900">
                    Documents
                  </h2>
                </div>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-gray-50">
                        <TableHead className="font-semibold">Name</TableHead>
                        <TableHead className="font-semibold">Type</TableHead>
                        <TableHead className="font-semibold">Size</TableHead>
                        <TableHead className="font-semibold">
                          Uploaded By
                        </TableHead>
                        <TableHead className="font-semibold">
                          Upload Date
                        </TableHead>
                        <TableHead className="font-semibold">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {documentsLoading ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-8">
                            <div className="flex items-center justify-center gap-2">
                              <Loader2 size={16} className="animate-spin" />
                              <span className="text-sm text-gray-500">
                                Loading documents...
                              </span>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : documents.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-8">
                            <div className="flex flex-col items-center gap-2">
                              <FileText size={24} className="text-gray-400" />
                              <span className="text-sm text-gray-500">
                                No documents uploaded yet
                              </span>
                              <span className="text-xs text-gray-400">
                                Upload documents using the button above
                              </span>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : (
                        documents.map((doc) => (
                          <TableRow key={doc.id} className="hover:bg-gray-50">
                            <TableCell>
                              <div className="flex items-center gap-2">
                                {getFileIcon(getFileExtension(doc.name))}
                                <span className="text-sm font-medium">
                                  {doc.name}
                                </span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <span className="text-xs bg-gray-100 px-2 py-1 rounded font-mono uppercase">
                                {getFileExtension(doc.name)}
                              </span>
                            </TableCell>
                            <TableCell>
                              <span className="text-sm text-gray-600">
                                {doc.details &&
                                typeof doc.details === "object" &&
                                "size" in doc.details
                                  ? formatFileSize(doc.details.size as number)
                                  : "N/A"}
                              </span>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <User size={14} className="text-gray-400" />
                                <span className="text-sm text-gray-600">
                                  {(doc.accounts as any)?.users?.name ||
                                    doc.accounts?.email ||
                                    "Unknown"}
                                </span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Clock size={14} className="text-gray-400" />
                                <span className="text-sm text-gray-600">
                                  {formatDate(doc.created_at)}
                                </span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-1">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0"
                                  onClick={() => handleViewDocument(doc)}
                                  title="View Document"
                                >
                                  <Eye size={14} />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0"
                                  onClick={() => handleDownloadDocument(doc)}
                                  title="Download Document"
                                >
                                  <Download size={14} />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </AnimatedCard>
            </div>

            {/* Right Column - Status Management */}
            <div className="lg:col-span-1 space-y-6">
              {/* Status Management */}
              <AnimatedCard className="p-6 space-y-4">
                <h2 className="text-lg font-semibold text-gray-900 mb-1 flex items-center gap-2">
                  <Settings size={18} />
                  Status Management
                </h2>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Update Status
                    </label>
                    <Select
                      value={selectedStatus}
                      onValueChange={setSelectedStatus}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="CREATED">Created</SelectItem>
                        <SelectItem value="LOADED">Loaded</SelectItem>
                        <SelectItem value="IN_TRANSIT">In Transit</SelectItem>
                        <SelectItem value="ARRIVED">Arrived</SelectItem>
                        <SelectItem value="PROCESSING">Processing</SelectItem>
                        <SelectItem value="CLEARED">Cleared</SelectItem>
                        <SelectItem value="UNLOADED">Unloaded</SelectItem>
                        <SelectItem value="READY_FOR_PICKUP">
                          Ready for Pickup
                        </SelectItem>
                        <SelectItem value="PICKED_UP">Picked Up</SelectItem>
                        <SelectItem value="RELEASED">Released</SelectItem>
                        <SelectItem value="DELIVERED">Delivered</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {selectedStatus === "READY_FOR_PICKUP" && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                      <div className="flex items-start gap-2">
                        <Info
                          size={16}
                          className="text-blue-600 mt-0.5 flex-shrink-0"
                        />
                        <div className="text-sm">
                          <p className="font-medium text-blue-900">
                            Auto Release Authorization
                          </p>
                          <p className="text-blue-700 mt-1">
                            Changing status to "READY FOR PICKUP" will
                            automatically generate a release authorization for
                            pickup.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                  <Button
                    className="w-full gap-2"
                    onClick={handleStatusUpdate}
                    disabled={updatingStatus || selectedStatus === cargo.status}
                  >
                    {updatingStatus ? (
                      <Loader2 size={16} className="animate-spin" />
                    ) : (
                      <Save size={16} />
                    )}
                    {updatingStatus ? "Updating..." : "Update Status"}
                  </Button>
                  {statusUpdateMessage && (
                    <div
                      className={cn(
                        "p-3 rounded-lg text-sm",
                        statusUpdateMessage.startsWith("✅")
                          ? "bg-green-50 border border-green-200 text-green-800"
                          : "bg-red-50 border border-red-200 text-red-800"
                      )}
                    >
                      {statusUpdateMessage}
                    </div>
                  )}
                </div>
              </AnimatedCard>

              {/* Assignee Information */}
              {cargo.assignedTo && (
                <AnimatedCard className="p-6 space-y-4">
                  <h2 className="text-lg font-semibold text-gray-900 mb-1">
                    Assigned To
                  </h2>
                  <InfoItem
                    label="Staff Member"
                    value={cargo.assignedTo}
                    icon={User}
                  />
                </AnimatedCard>
              )}

              {/* Entity Information (Customer or Supplier) */}
              <AnimatedCard className="p-6 space-y-4">
                <div className="flex items-center gap-2 mb-1">
                  <h2 className="text-lg font-semibold text-gray-900">
                    {cargo.entityType === "customer"
                      ? "Customer"
                      : cargo.entityType === "supplier"
                        ? "Supplier"
                        : "Entity"}{" "}
                    Information
                  </h2>
                  <span
                    className={`px-2 py-1 text-xs rounded-full font-medium ${
                      cargo.entityType === "customer"
                        ? "bg-blue-100 text-blue-700"
                        : cargo.entityType === "supplier"
                          ? "bg-green-100 text-green-700"
                          : "bg-gray-100 text-gray-700"
                    }`}
                  >
                    {cargo.entityType === "customer"
                      ? "Customer"
                      : cargo.entityType === "supplier"
                        ? "Supplier"
                        : "Unknown"}
                  </span>
                </div>

                {cargo.entityType === "customer" ? (
                  <>
                    <InfoItem
                      label="Company"
                      value={cargo.customer}
                      icon={Building}
                    />
                    <InfoItem
                      label="Phone"
                      value={cargo.customerPhone || "N/A"}
                      icon={Phone}
                    />
                  </>
                ) : cargo.entityType === "supplier" ? (
                  <>
                    <InfoItem
                      label="Supplier"
                      value={cargo.customer}
                      icon={Building}
                    />
                    <InfoItem
                      label="Phone"
                      value={cargo.customerPhone || "N/A"}
                      icon={Phone}
                    />
                  </>
                ) : (
                  <p className="text-gray-500 text-sm">
                    No entity information available
                  </p>
                )}
              </AnimatedCard>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Document Preview Dialog */}
      <DocumentPreviewDialog
        isOpen={isOpen}
        onClose={closePreview}
        document={previewDocument}
        title="Document Preview"
      />
    </Fragment>
  );
};
