"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Button } from "@workspace/ui/components/button";
import {
  Package,
  MapPin,
  Building,
  User,
  Phone,
  FileText,
  Ruler,
  Weight,
  Hash,
  Clock,
  Loader2,
  Save,
  Settings,
  Info,
  QrCode,
} from "lucide-react";
import { StatusBadge } from "@/components/status-badge";
import { AnimatedCard } from "@/components/animated-card";
import { cn } from "@workspace/ui/lib/utils";
import { useAppSelector } from "@/store/hooks";
import { cargoService } from "@/lib/logistics";
import { parseTrackingNumber } from "@/lib/tracking-generator";
import { type CargoDisplay } from "../../utils/types";
import { toast } from "sonner";

interface CargoDetailsDialogProps {
  open: boolean;
  cargo: CargoDisplay | null;
  onOpenChange: (open: boolean) => void;
  onCargoUpdate?: () => void;
  onQRCodeAction?: (cargo: CargoDisplay) => void;
}

// Helper function to format CBM
const formatCBM = (cbm: number | string | null): string => {
  if (!cbm) return "N/A";
  const cbmValue = typeof cbm === "string" ? parseFloat(cbm) : cbm;
  if (isNaN(cbmValue)) return "N/A";
  return `${cbmValue.toFixed(3)} m³`;
};

// Helper function to format date
const formatDate = (dateString: string | null): string => {
  if (!dateString) return "N/A";
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

// InfoItem component for displaying cargo details
const InfoItem = ({
  label,
  value,
  icon: Icon,
  className,
}: {
  label: string;
  value: string | React.ReactNode;
  icon?: React.ElementType;
  className?: string;
}) => (
  <div className={className}>
    <div className="text-xs text-gray-500 mb-0.5 flex items-center gap-1">
      {Icon && <Icon size={14} className="text-gray-400" />}
      {label}
    </div>
    <div className="text-sm text-gray-900 font-medium">{value}</div>
  </div>
);

// Tracking number display component
const TrackingNumberDisplay = ({
  trackingNumber,
}: {
  trackingNumber: string;
}) => {
  try {
    const parsed = parseTrackingNumber(trackingNumber);
    const {
      shippingMode,
      locationCode,
      year,
      month,
      batchIndex,
      weightCategory,
    } = parsed;

    return (
      <div className="space-y-1">
        <span className="font-mono text-sm">{trackingNumber}</span>
        <div className="text-xs text-gray-500 space-y-0.5">
          <div>
            Mode: {shippingMode} • Location: {locationCode} • Weight:{" "}
            {weightCategory === "H" ? "Heavy" : "Light"}
          </div>
          <div>
            Year: {year} • Month: {month.toString().padStart(2, "0")} • Batch:{" "}
            {batchIndex.toString().padStart(3, "0")}
          </div>
        </div>
      </div>
    );
  } catch (error) {
    // If parsing fails, just display the tracking number as-is
    return <span className="font-mono">{trackingNumber}</span>;
  }
};

/**
 * Cargo Details Dialog Component
 *
 * Displays detailed cargo information in a dialog format.
 * Includes cargo information, customer/supplier details, and status update functionality.
 * Based on the cargo detail page layout but adapted for dialog use.
 */
export const CargoDetailsDialog: React.FC<CargoDetailsDialogProps> = ({
  open,
  cargo,
  onOpenChange,
  onCargoUpdate,
  onQRCodeAction,
}) => {
  const authUser = useAppSelector((state) => state.auth.user);
  const [selectedStatus, setSelectedStatus] = useState<string>("");
  const [updatingStatus, setUpdatingStatus] = useState(false);
  const [statusUpdateMessage, setStatusUpdateMessage] = useState<string | null>(
    null
  );

  // Initialize selected status when dialog opens or cargo changes
  useEffect(() => {
    if (open && cargo) {
      setSelectedStatus(cargo.status);
      setStatusUpdateMessage(null);
    }
  }, [open, cargo]);

  // Handle status update
  const handleStatusUpdate = async () => {
    if (!cargo || !authUser) return;

    setUpdatingStatus(true);
    setStatusUpdateMessage(null);

    try {
      const result = await cargoService.updateCargoStatus(
        cargo.id,
        selectedStatus,
        `Status updated to ${selectedStatus} by ${authUser.name}`,
        authUser.id
      );

      if (result.success) {
        if (selectedStatus === "READY_FOR_PICKUP") {
          setStatusUpdateMessage(
            "✅ Status updated to READY FOR PICKUP! Release authorization has been automatically generated."
          );
        } else {
          setStatusUpdateMessage(
            `✅ Status updated to ${selectedStatus} successfully.`
          );
        }

        // Call update callback if provided
        if (onCargoUpdate) {
          onCargoUpdate();
        }

        // Clear message after 5 seconds
        setTimeout(() => setStatusUpdateMessage(null), 5000);
      } else {
        setStatusUpdateMessage(`❌ Failed to update status: ${result.error}`);
      }
    } catch (error) {
      console.error("Error updating status:", error);
      setStatusUpdateMessage("❌ Failed to update status. Please try again.");
    } finally {
      setUpdatingStatus(false);
    }
  };

  if (!cargo) return null;

  // Get cargo type configuration
  const getCargoType = (category: string) => {
    const cargoConfigs: Record<
      string,
      { type: string; icon: any; color: string }
    > = {
      DANGEROUS: { type: "Dangerous", icon: Package, color: "text-red-600" },
      SAFE: { type: "Safe", icon: Package, color: "text-green-600" },
      COPY: { type: "Copy", icon: Package, color: "text-orange-600" },
    };

    return (
      cargoConfigs[category] || {
        type: "Unknown",
        icon: Package,
        color: "text-gray-600",
      }
    );
  };

  const cargoType = getCargoType(cargo.type || "SAFE");

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Cargo Details - {cargo.trackingNumber}
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Main Information */}
          <div className="lg:col-span-2 space-y-6">
            {/* Cargo Information */}
            <AnimatedCard className="p-6">
              <div className="flex justify-between items-center mb-5">
                <h2 className="text-lg font-semibold text-gray-900">
                  Cargo Information
                </h2>
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-1.5"
                  onClick={() => {
                    if (onQRCodeAction && cargo) {
                      onQRCodeAction(cargo);
                    } else {
                      toast.info("QR Code functionality not available");
                    }
                  }}
                >
                  <QrCode size={16} />
                  QR Code
                </Button>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-x-4 gap-y-5">
                <InfoItem
                  label="Tracking Number"
                  value={
                    <TrackingNumberDisplay
                      trackingNumber={cargo.trackingNumber}
                    />
                  }
                  icon={Hash}
                  className="sm:col-span-2 md:col-span-3"
                />
                {cargo.chinaTrackingNumber &&
                  cargo.chinaTrackingNumber !== "N/A" && (
                    <InfoItem
                      label="China Tracking"
                      value={cargo.chinaTrackingNumber}
                      icon={Hash}
                    />
                  )}
                <InfoItem
                  label="Type"
                  value={cargoType.type}
                  icon={cargoType.icon}
                />
                <InfoItem
                  label="Status"
                  value={<StatusBadge status={cargo.status} />}
                  icon={Settings}
                />
                <InfoItem
                  label="Created"
                  value={formatDate(cargo.date)}
                  icon={Clock}
                />
                <InfoItem
                  label="Updated"
                  value={formatDate(cargo.updatedAt)}
                  icon={Clock}
                />
                <InfoItem
                  label="CTN"
                  value={cargo.ctn?.toString() || "N/A"}
                  icon={Package}
                />
                <InfoItem
                  label="CBM"
                  value={formatCBM(cargo.cbm)}
                  icon={Ruler}
                />
                <InfoItem label="Weight" value={cargo.weight} icon={Weight} />
                <InfoItem
                  label="Origin"
                  value={cargo.origin || "N/A"}
                  icon={MapPin}
                />
                <InfoItem
                  label="Destination"
                  value={cargo.destination || "N/A"}
                  icon={MapPin}
                />
                <InfoItem
                  label="Description"
                  value={cargo.particular || "N/A"}
                  icon={FileText}
                  className="sm:col-span-2 md:col-span-3"
                />
              </div>
            </AnimatedCard>

            {/* Entity Information (Customer or Supplier) */}
            <AnimatedCard className="p-6 space-y-4">
              <div className="flex items-center gap-2 mb-1">
                <h2 className="text-lg font-semibold text-gray-900">
                  {cargo.entityType === "customer"
                    ? "Customer"
                    : cargo.entityType === "supplier"
                      ? "Supplier"
                      : "Entity"}{" "}
                  Information
                </h2>
                <span
                  className={`px-2 py-1 text-xs rounded-full font-medium ${
                    cargo.entityType === "customer"
                      ? "bg-blue-100 text-blue-700"
                      : cargo.entityType === "supplier"
                        ? "bg-green-100 text-green-700"
                        : "bg-gray-100 text-gray-700"
                  }`}
                >
                  {cargo.entityType === "customer"
                    ? "Customer"
                    : cargo.entityType === "supplier"
                      ? "Supplier"
                      : "Unknown"}
                </span>
              </div>

              {cargo.entityType === "customer" ? (
                <>
                  <InfoItem
                    label="Company"
                    value={cargo.customer}
                    icon={Building}
                  />
                  <InfoItem
                    label="Phone"
                    value={cargo.customerPhone || "N/A"}
                    icon={Phone}
                  />
                </>
              ) : cargo.entityType === "supplier" ? (
                <>
                  <InfoItem
                    label="Supplier"
                    value={cargo.customer}
                    icon={Building}
                  />
                  <InfoItem
                    label="Phone"
                    value={cargo.customerPhone || "N/A"}
                    icon={Phone}
                  />
                </>
              ) : (
                <p className="text-gray-500 text-sm">
                  No entity information available
                </p>
              )}
            </AnimatedCard>
          </div>

          {/* Right Column - Status Management */}
          <div className="lg:col-span-1 space-y-6">
            {/* Status Management */}
            <AnimatedCard className="p-6 space-y-4">
              <h2 className="text-lg font-semibold text-gray-900 mb-1 flex items-center gap-2">
                <Settings size={18} />
                Status Management
              </h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Update Status
                  </label>
                  <Select
                    value={selectedStatus}
                    onValueChange={setSelectedStatus}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="CREATED">Created</SelectItem>
                      <SelectItem value="LOADED">Loaded</SelectItem>
                      <SelectItem value="IN_TRANSIT">In Transit</SelectItem>
                      <SelectItem value="ARRIVED">Arrived</SelectItem>
                      <SelectItem value="PROCESSING">Processing</SelectItem>
                      <SelectItem value="CLEARED">Cleared</SelectItem>
                      <SelectItem value="UNLOADED">Unloaded</SelectItem>
                      <SelectItem value="READY_FOR_PICKUP">
                        Ready for Pickup
                      </SelectItem>
                      <SelectItem value="PICKED_UP">Picked Up</SelectItem>
                      <SelectItem value="RELEASED">Released</SelectItem>
                      <SelectItem value="DELIVERED">Delivered</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {selectedStatus === "READY_FOR_PICKUP" && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <div className="flex items-start gap-2">
                      <Info
                        size={16}
                        className="text-blue-600 mt-0.5 flex-shrink-0"
                      />
                      <div className="text-sm">
                        <p className="font-medium text-blue-900">
                          Auto Release Authorization
                        </p>
                        <p className="text-blue-700 mt-1">
                          Changing status to "READY FOR PICKUP" will
                          automatically generate a release authorization for
                          pickup.
                        </p>
                      </div>
                    </div>
                  </div>
                )}
                <Button
                  className="w-full gap-2"
                  onClick={handleStatusUpdate}
                  disabled={updatingStatus || selectedStatus === cargo.status}
                >
                  {updatingStatus ? (
                    <Loader2 size={16} className="animate-spin" />
                  ) : (
                    <Save size={16} />
                  )}
                  {updatingStatus ? "Updating..." : "Update Status"}
                </Button>
                {statusUpdateMessage && (
                  <div
                    className={cn(
                      "p-3 rounded-lg text-sm",
                      statusUpdateMessage.startsWith("✅")
                        ? "bg-green-50 border border-green-200 text-green-800"
                        : "bg-red-50 border border-red-200 text-red-800"
                    )}
                  >
                    {statusUpdateMessage}
                  </div>
                )}
              </div>
            </AnimatedCard>

            {/* Assignee Information */}
            {cargo.assignedTo && (
              <AnimatedCard className="p-6 space-y-4">
                <h2 className="text-lg font-semibold text-gray-900 mb-1">
                  Assigned To
                </h2>
                <InfoItem
                  label="Staff Member"
                  value={cargo.assignedTo}
                  icon={User}
                />
              </AnimatedCard>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
