"use client";

import { memo } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { NewCargoForm } from "../../forms/NewCargoForm";

interface NewCargoDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

/**
 * New Cargo Dialog Component
 *
 * A dialog wrapper for the cargo creation form.
 * Memoized to prevent unnecessary re-renders.
 */
export const NewCargoDialog = memo<NewCargoDialogProps>(
  ({ open, onOpenChange, onSuccess }) => {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              Create New Cargo
            </DialogTitle>
            <DialogDescription>
              Add a new cargo item to the system. Fill in the details below to
              create a cargo entry.
            </DialogDescription>
          </DialogHeader>

          <NewCargoForm onOpenChange={onOpenChange} onSuccess={onSuccess} />
        </DialogContent>
      </Dialog>
    );
  }
);

NewCargoDialog.displayName = "NewCargoDialog";
