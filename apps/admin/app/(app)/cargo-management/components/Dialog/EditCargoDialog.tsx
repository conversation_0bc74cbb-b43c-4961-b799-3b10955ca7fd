"use client";

import React from "react";
import {
  Dialog,
  Dialog<PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
} from "@workspace/ui/components/dialog";
import { EditCargoForm } from "../../forms/EditCargoForm";
import { type CargoWithRelations } from "@/lib/logistics";

interface EditCargoDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  cargo: CargoWithRelations | null;
  onSuccess: () => void;
}

export const EditCargoDialog: React.FC<EditCargoDialogProps> = ({
  isOpen,
  onOpenChange,
  cargo,
  onSuccess,
}) => {
  const handleSuccess = () => {
    onOpenChange(false);
    onSuccess();
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Cargo</DialogTitle>
          <DialogDescription>
            Edit the cargo details and update associated information
          </DialogDescription>
        </DialogHeader>

        <EditCargoForm
          cargo={cargo}
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </DialogContent>
    </Dialog>
  );
};
