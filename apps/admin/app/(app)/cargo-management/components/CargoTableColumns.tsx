"use client";

import React from "react";

import {
  Qr<PERSON>ode,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  User,
  SquareArrowOutUpRight as GotoIcon,
} from "lucide-react";
import { Button } from "@workspace/ui/components/button";
import { StatusBadge } from "@/components/status-badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { type CargoDisplay } from "../utils/types";

interface CargoTableColumnsProps {
  onAction: (action: string, cargo?: CargoDisplay) => void;
}

/**
 * Table columns configuration for Cargo Management
 *
 * Defines the structure and rendering of table columns for cargo data.
 * Memoized to prevent unnecessary re-renders.
 */

export const CargoTableColumns = ({
  onAction,
}: {
  onAction: (action: string, cargo?: CargoDisplay, actionData?: any) => void;
}) => {
  return [
    {
      key: "date",
      label: "Created",
      render: (cargo: CargoDisplay) => (
        <span className="text-sm text-gray-500">{cargo.date}</span>
      ),
      className: "min-w-[100px]",
    },
    {
      key: "updatedAt",
      label: "Updated",
      render: (cargo: CargoDisplay) => (
        <span className="text-sm text-gray-500">{cargo.updatedAt}</span>
      ),
      className: "min-w-[100px]",
    },
    {
      key: "trackingNumber",
      label: "Tracking Number",
      render: (cargo: CargoDisplay) => (
        <div
          className="font-mono text-sm text-nowrap"
          onClick={() => onAction("view", cargo)}
        >
          <div className="font-medium text-primary hover:underline cursor-pointer">
            {cargo.trackingNumber}
          </div>
          {cargo.chinaTrackingNumber !== "N/A" && (
            <div className="text-xs text-gray-500">
              China: {cargo.chinaTrackingNumber}
            </div>
          )}
        </div>
      ),
      className: "min-w-[180px]",
    },
    {
      key: "batch",
      label: "Batch Number",
      render: (cargo: CargoDisplay) => (
        <div
          className="font-medium text-primary hover:underline cursor-pointer"
          onClick={() => onAction("batch", cargo, { batchId: cargo.batchId })}
        >
          {cargo.batchCode}
        </div>
      ),
    },
    {
      key: "customer",
      label: "Customer/Supplier",
      render: (cargo: CargoDisplay) => (
        <div className="text-sm">
          <span className="text-gray-900">{cargo.customer}</span>
          <div className="text-xs text-gray-500 capitalize">
            {cargo.entityType}
            {cargo.customerPhone && (
              <div className="text-xs text-gray-400 mt-0.5">
                {cargo.customerPhone}
              </div>
            )}
          </div>
        </div>
      ),
      className: "min-w-[150px]",
    },
    {
      key: "assignedTo",
      label: (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4" />
          <span>Assigned</span>
        </div>
      ),
      render: (cargo: CargoDisplay) => (
        <div className="text-sm">
          <span
            className={`text-blue-700 ${cargo.assignedTo === "Unassigned" ? "text-gray-400 italic" : ""} capitalize`}
          >
            {cargo.assignedTo}
          </span>
        </div>
      ),
      className: "min-w-[120px]",
    },
    {
      key: "particular",
      label: "Particular",
      render: (cargo: CargoDisplay) => (
        <p className="inline-flex text-wrap">{cargo.particular}</p>
      ),
      className: "min-w-[120px]",
    },
    {
      key: "type",
      label: "Type",
      render: (cargo: CargoDisplay) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/5 text-primary border border-primary/25 text-nowrap">
          {cargo.type}
        </span>
      ),
      className: "min-w-[120px]",
    },
    {
      key: "status",
      label: "Status",
      render: (cargo: CargoDisplay) => <StatusBadge status={cargo.status} />,
      className: "min-w-[120px]",
    },
    {
      key: "weight",
      label: "Weight",
      render: (cargo: CargoDisplay) => (
        <span className="text-sm text-gray-900">{cargo.weight}</span>
      ),
      className: "min-w-[100px]",
    },
    {
      key: "cbm",
      label: "CBM",
      render: (cargo: CargoDisplay) => (
        <span className="text-sm text-gray-900">{cargo.cbm}</span>
      ),
      className: "min-w-[100px]",
    },
    {
      key: "ctn",
      label: "CTN",
      render: (cargo: CargoDisplay) => (
        <span className="text-sm text-gray-900 font-mono">{cargo.ctn}</span>
      ),
      className: "min-w-[120px]",
    },
    {
      key: "dimensions",
      label: "Dimensions",
      render: (cargo: CargoDisplay) => (
        <span className="text-sm text-gray-900">{cargo.dimensions}</span>
      ),
      className: "min-w-[140px]",
    },
    {
      key: "unitPrice",
      label: "Unit Price",
      render: (cargo: CargoDisplay) => (
        <span className="text-sm text-gray-900 font-mono">
          {cargo.unitPrice}
        </span>
      ),
      className: "min-w-[100px]",
    },
    {
      key: "totalPrice",
      label: "Total Price",
      render: (cargo: CargoDisplay) => (
        <span className="text-sm text-gray-900 font-mono font-medium">
          {cargo.totalPrice}
        </span>
      ),
      className: "min-w-[110px]",
    },

    {
      key: "invoiceStatus",
      label: "Invoice",
      render: (cargo: CargoDisplay) => (
        <span
          className={`text-sm px-2 py-1 rounded-full text-xs font-medium ${
            cargo.invoiceStatus === "PAID"
              ? "bg-green-100 text-green-800"
              : cargo.invoiceStatus === "PENDING"
                ? "bg-yellow-100 text-yellow-800"
                : cargo.invoiceStatus === "OVERDUE"
                  ? "bg-red-100 text-red-800"
                  : "bg-gray-100 text-gray-800"
          }`}
        >
          {cargo.invoiceStatus}
        </span>
      ),
      className: "min-w-[100px]",
    },
    {
      key: "actions",
      label: "Actions",
      render: (cargo: CargoDisplay) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onAction("qr-code", cargo)}
            className="h-8 w-8 p-0"
          >
            <QrCode className="h-4 w-4" />
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onAction("view", cargo)}>
                <GotoIcon className="h-4 w-4 mr-2" />
                Go To Cargo
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onAction("view-details", cargo)}>
                <Eye className="h-4 w-4 mr-2" />
                View Summary
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onAction("edit", cargo)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onAction("delete", cargo)}
                className="text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
      className: "w-[100px]",
    },
  ];
};
