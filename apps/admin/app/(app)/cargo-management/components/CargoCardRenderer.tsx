"use client";

import { memo } from "react";
import Link from "next/link";
import {
  Package,
  MapPin,
  Calendar,
  QrCode,
  MoreHorizontal,
  Edit,
  Trash2,
  User,
} from "lucide-react";
import { Button } from "@workspace/ui/components/button";
import { StatusBadge } from "@/components/status-badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { type CargoDisplay } from "../utils/types";

interface CargoCardRendererProps {
  cargo: CargoDisplay;
  onAction: (action: string, cargo?: CargoDisplay) => void;
}

/**
 * Card renderer component for Cargo Management
 *
 * Renders individual cargo items in card format.
 * Memoized to prevent unnecessary re-renders.
 */
export const CargoCardRenderer = memo<CargoCardRendererProps>(
  ({ cargo, onAction }) => {
    return (
      <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow">
        {/* Header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-2">
            <Package className="h-5 w-5 text-blue-600" />
            <div>
              <h3 className="font-medium text-gray-900 font-mono text-sm">
                {cargo.trackingNumber}
              </h3>
              {cargo.chinaTrackingNumber !== "N/A" && (
                <p className="text-xs text-gray-500">
                  China: {cargo.chinaTrackingNumber}
                </p>
              )}
            </div>
          </div>

          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onAction("qr-code", cargo)}
              className="h-8 w-8 p-0"
            >
              <QrCode className="h-4 w-4" />
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onAction("edit", cargo)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onAction("delete", cargo)}
                  className="text-red-600"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Type and Status */}
        <div className="flex items-center justify-between mb-3">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {cargo.type}
          </span>
          <StatusBadge status={cargo.status} />
        </div>

        {/* Route */}
        <div className="flex items-center gap-2 mb-3 text-sm text-gray-600">
          <MapPin className="h-4 w-4" />
          <span>
            {cargo.origin} → {cargo.destination}
          </span>
        </div>

        {/* Weight */}
        <div className="mb-3">
          <span className="text-sm text-gray-600">Weight: </span>
          <span className="text-sm font-medium text-gray-900">
            {cargo.weight}
          </span>
        </div>

        {/* Customer/Supplier */}
        <div className="mb-3">
          <span className="text-sm text-gray-600">
            {cargo.entityType === "customer" ? "Customer" : "Supplier"}:{" "}
          </span>
          {cargo.customerId ? (
            <Link
              href={`/${cargo.entityType === "customer" ? "customers" : "suppliers"}/${cargo.customerId}`}
              className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
            >
              {cargo.customer}
            </Link>
          ) : (
            <span className="text-sm text-gray-900">{cargo.customer}</span>
          )}
          {cargo.customerPhone && (
            <div className="text-xs text-gray-400 mt-1">
              {cargo.customerPhone}
            </div>
          )}
        </div>

        {/* Assigned To */}
        <div className="flex items-center gap-2 mb-3">
          <User className="h-4 w-4 text-gray-400" />
          <span className="text-sm text-gray-600">Assigned: </span>
          <span
            className={`text-sm ${cargo.assignedTo === "Unassigned" ? "text-gray-400 italic" : "text-gray-900 font-medium"}`}
          >
            {cargo.assignedTo}
          </span>
        </div>

        {/* Batch Code */}
        {cargo.batchCode && (
          <div className="mb-3">
            <span className="text-sm text-gray-600">Batch: </span>
            <span className="text-sm font-medium text-gray-900">
              {cargo.batchCode}
            </span>
          </div>
        )}

        {/* Description */}
        {cargo.particular && (
          <div className="mb-3">
            <span className="text-sm text-gray-600">Description: </span>
            <span className="text-sm text-gray-900">{cargo.particular}</span>
          </div>
        )}

        {/* Footer */}
        <div className="flex items-center justify-between pt-3 border-t border-gray-100">
          <div className="flex items-center gap-1 text-xs text-gray-500">
            <Calendar className="h-3 w-3" />
            {cargo.date}
          </div>
        </div>
      </div>
    );
  }
);

CargoCardRenderer.displayName = "CargoCardRenderer";

export default CargoCardRenderer;
