"use client";

import { memo } from "react";
import { Calendar as CalendarIcon } from "lucide-react";
import { Input } from "@workspace/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { cn } from "@workspace/ui/lib/utils";
import { trimming } from "@/lib/utils";

interface DateInputProps extends React.ComponentProps<typeof Input> {
  placeholder?: string;
  className?: string;
}

/**
 * Date Input Component
 *
 * Input field with calendar icon for date selection.
 * Memoized to prevent unnecessary re-renders.
 */
export const DateInput = memo<DateInputProps>(
  ({ placeholder, className, ...props }) => (
    <div className="relative">
      <Input
        type="text"
        placeholder={placeholder}
        className={cn(
          "pl-10 py-2.5 text-sm border border-gray-200 rounded-lg w-full bg-white text-gray-900 shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-colors",
          className
        )}
        {...props}
      />
      <CalendarIcon
        className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500"
        aria-hidden="true"
      />
    </div>
  )
);

DateInput.displayName = "DateInput";

interface InputFieldProps extends React.InputHTMLAttributes<HTMLInputElement> {
  id: string;
  label: string;
  icon?: React.ElementType;
}

/**
 * Input Field Component
 *
 * Labeled input field with optional icon.
 * Memoized to prevent unnecessary re-renders.
 */
export const InputField = memo<InputFieldProps>(
  ({ id, label, icon: Icon, ...props }) => (
    <div>
      <label
        htmlFor={id}
        className="block text-sm font-medium text-gray-700 mb-1.5"
      >
        {label}
      </label>
      <div className="relative">
        {Icon && (
          <Icon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
        )}
        <Input
          id={id}
          className={cn(
            "w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white text-gray-900 shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary",
            Icon ? "pl-10" : ""
          )}
          {...props}
        />
      </div>
    </div>
  )
);

InputField.displayName = "InputField";

interface SelectFieldProps {
  id: string;
  label: string;
  placeholder?: string;
  defaultValue?: string;
  value?: string;
  options: (string | { value: string; label: string })[];
  onChange?: (value: string) => void;
  required?: boolean;
  disabled?: boolean;
}

/**
 * Select Field Component
 *
 * Labeled select field with options.
 * Memoized to prevent unnecessary re-renders.
 */
export const SelectField = memo<SelectFieldProps>(
  ({
    id,
    label,
    placeholder,
    defaultValue,
    value,
    options,
    onChange,
    required,
    disabled,
  }) => (
    <div>
      <label
        htmlFor={id}
        className="block text-sm font-medium text-gray-700 mb-1.5"
      >
        {label}
      </label>
      <Select
        defaultValue={defaultValue}
        value={value}
        onValueChange={onChange}
        required={required}
        disabled={disabled}
      >
        <SelectTrigger
          id={id}
          className="w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white text-gray-900 shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary data-[placeholder]:text-gray-500 h-10"
        >
          <SelectValue placeholder={placeholder ?? "Select..."} />
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => {
            const value =
              typeof option === "string"
                ? option === "All Types" || option === "All Statuses"
                  ? "all"
                  : option.toLowerCase().replace(/\s+/g, "-")
                : option.value;
            const displayLabel =
              typeof option === "string" ? option : option.label;
            return (
              <SelectItem key={value} value={value} className="text-sm text-wrap">
                {trimming(displayLabel, 20)}
              </SelectItem>
            );
          })}
        </SelectContent>
      </Select>
    </div>
  )
);

SelectField.displayName = "SelectField";
