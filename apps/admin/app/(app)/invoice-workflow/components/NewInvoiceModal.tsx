"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  X,
  ChevronsRight,
  ChevronsLeft,
  Save,
  Plus,
  User,
  Building,
  Calendar,
  DollarSign,
  FileText,
  ClipboardList,
} from "lucide-react";

interface NewInvoiceModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function NewInvoiceModal({ isOpen, onClose }: NewInvoiceModalProps) {
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState({
    clientInfo: {
      name: "",
      email: "",
      company: "",
      address: "",
      phone: "",
    },
    invoiceDetails: {
      invoiceNumber: `INV-${Math.floor(Math.random() * 1000)
        .toString()
        .padStart(4, "0")}`,
      issueDate: new Date().toISOString().split("T")[0],
      dueDate: new Date(new Date().setDate(new Date().getDate() + 30))
        .toISOString()
        .split("T")[0],
      reference: "",
      status: "draft",
    },
    items: [{ id: 1, description: "", quantity: 1, unitPrice: 0, total: 0 }],
    notes: "",
    terms: "Payment is due within 30 days of receipt of this invoice.",
    subtotal: 0,
    total: 0,
  });

  // Close modal when Escape key is pressed
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose();
    };
    window.addEventListener("keydown", handleEscape);
    return () => window.removeEventListener("keydown", handleEscape);
  }, [onClose]);

  // Prevent scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    }
    return () => {
      document.body.style.overflow = "auto";
    };
  }, [isOpen]);

  const nextStep = () => {
    if (step < 4) setStep(step + 1);
  };

  const prevStep = () => {
    if (step > 1) setStep(step - 1);
  };

  const handleChange = (section: string, field: string, value: any) => {
    setFormData((prev) => {
      if (section === "") {
        // Handle root-level properties
        return {
          ...prev,
          [field]: value,
        };
      }

      // Handle nested properties
      return {
        ...prev,
        [section]: {
          ...(prev[section as keyof typeof prev] as Record<string, any>),
          [field]: value,
        },
      };
    });
  };

  const addItem = () => {
    const newItem = {
      id: formData.items.length + 1,
      description: "",
      quantity: 1,
      unitPrice: 0,
      total: 0,
    };

    setFormData((prev) => ({
      ...prev,
      items: [...prev.items, newItem],
    }));
  };

  const removeItem = (id: number) => {
    setFormData((prev) => ({
      ...prev,
      items: prev.items.filter((item) => item.id !== id),
    }));
  };

  const updateItem = (id: number, field: string, value: any) => {
    setFormData((prev) => {
      const updatedItems = prev.items.map((item) => {
        if (item.id === id) {
          const updatedItem = { ...item, [field]: value };

          // Recalculate total if quantity or unitPrice changes
          if (field === "quantity" || field === "unitPrice") {
            updatedItem.total = updatedItem.quantity * updatedItem.unitPrice;
          }

          return updatedItem;
        }
        return item;
      });

      // Recalculate invoice totals
      const subtotal = updatedItems.reduce(
        (sum, item) => sum + (item.total || 0),
        0
      );

      return {
        ...prev,
        items: updatedItems,
        subtotal,
        total: subtotal,
      };
    });
  };

  const handleSubmit = () => {
    // Submit the form data
    console.log("Submitting invoice:", formData);

    // Close the modal
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Modal */}
      <motion.div
        className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col overflow-hidden"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        transition={{ duration: 0.2 }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            Create New Invoice
          </h2>
          <button
            onClick={onClose}
            className="p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Step indicators */}
        <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full ${
                  step >= 1
                    ? "bg-primary text-white"
                    : "bg-gray-200 text-gray-600"
                } mr-2`}
              >
                <User className="h-4 w-4" />
              </div>
              <span
                className={
                  step >= 1 ? "text-gray-900 font-medium" : "text-gray-500"
                }
              >
                Client
              </span>
            </div>
            <div className="h-0.5 w-12 bg-gray-200 mx-2" />
            <div className="flex items-center">
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full ${
                  step >= 2
                    ? "bg-primary text-white"
                    : "bg-gray-200 text-gray-600"
                } mr-2`}
              >
                <FileText className="h-4 w-4" />
              </div>
              <span
                className={
                  step >= 2 ? "text-gray-900 font-medium" : "text-gray-500"
                }
              >
                Details
              </span>
            </div>
            <div className="h-0.5 w-12 bg-gray-200 mx-2" />
            <div className="flex items-center">
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full ${
                  step >= 3
                    ? "bg-primary text-white"
                    : "bg-gray-200 text-gray-600"
                } mr-2`}
              >
                <ClipboardList className="h-4 w-4" />
              </div>
              <span
                className={
                  step >= 3 ? "text-gray-900 font-medium" : "text-gray-500"
                }
              >
                Items
              </span>
            </div>
            <div className="h-0.5 w-12 bg-gray-200 mx-2" />
            <div className="flex items-center">
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full ${
                  step >= 4
                    ? "bg-primary text-white"
                    : "bg-gray-200 text-gray-600"
                } mr-2`}
              >
                <DollarSign className="h-4 w-4" />
              </div>
              <span
                className={
                  step >= 4 ? "text-gray-900 font-medium" : "text-gray-500"
                }
              >
                Review
              </span>
            </div>
          </div>
        </div>

        {/* Form content - scrollable */}
        <div className="flex-1 overflow-y-auto px-6 py-4">
          <AnimatePresence mode="wait">
            {step === 1 && (
              <motion.div
                key="step1"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
                className="space-y-4"
              >
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Client Information
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Client Name *
                    </label>
                    <input
                      type="text"
                      value={formData.clientInfo.name}
                      onChange={(e) =>
                        handleChange("clientInfo", "name", e.target.value)
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
                      placeholder="John Doe"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      value={formData.clientInfo.email}
                      onChange={(e) =>
                        handleChange("clientInfo", "email", e.target.value)
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Company
                    </label>
                    <input
                      type="text"
                      value={formData.clientInfo.company}
                      onChange={(e) =>
                        handleChange("clientInfo", "company", e.target.value)
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
                      placeholder="Company Name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      value={formData.clientInfo.phone}
                      onChange={(e) =>
                        handleChange("clientInfo", "phone", e.target.value)
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
                      placeholder="+****************"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Billing Address
                  </label>
                  <textarea
                    value={formData.clientInfo.address}
                    onChange={(e) =>
                      handleChange("clientInfo", "address", e.target.value)
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
                    placeholder="123 Main St, City, State, ZIP"
                    rows={3}
                  />
                </div>
              </motion.div>
            )}

            {step === 2 && (
              <motion.div
                key="step2"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
                className="space-y-4"
              >
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Invoice Details
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Invoice Number *
                    </label>
                    <input
                      type="text"
                      value={formData.invoiceDetails.invoiceNumber}
                      onChange={(e) =>
                        handleChange(
                          "invoiceDetails",
                          "invoiceNumber",
                          e.target.value
                        )
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Reference (Optional)
                    </label>
                    <input
                      type="text"
                      value={formData.invoiceDetails.reference}
                      onChange={(e) =>
                        handleChange(
                          "invoiceDetails",
                          "reference",
                          e.target.value
                        )
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
                      placeholder="PO-12345"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Issue Date *
                    </label>
                    <input
                      type="date"
                      value={formData.invoiceDetails.issueDate}
                      onChange={(e) =>
                        handleChange(
                          "invoiceDetails",
                          "issueDate",
                          e.target.value
                        )
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Due Date *
                    </label>
                    <input
                      type="date"
                      value={formData.invoiceDetails.dueDate}
                      onChange={(e) =>
                        handleChange(
                          "invoiceDetails",
                          "dueDate",
                          e.target.value
                        )
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Terms and Conditions
                  </label>
                  <textarea
                    value={formData.terms}
                    onChange={(e) => handleChange("", "terms", e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
                    rows={3}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Notes (Optional)
                  </label>
                  <textarea
                    value={formData.notes}
                    onChange={(e) => handleChange("", "notes", e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
                    placeholder="Additional information for the client..."
                    rows={2}
                  />
                </div>
              </motion.div>
            )}

            {step === 3 && (
              <motion.div
                key="step3"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
                className="space-y-4"
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    Line Items
                  </h3>
                  <button
                    onClick={addItem}
                    className="inline-flex items-center px-3 py-1.5 bg-primary/10 text-primary rounded-md hover:bg-primary/20 transition-colors text-sm"
                  >
                    <Plus className="w-4 h-4 mr-1" />
                    Add Item
                  </button>
                </div>

                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Description
                        </th>
                        <th className="px-2 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Qty
                        </th>
                        <th className="px-2 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Unit Price
                        </th>
                        <th className="px-2 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total
                        </th>
                        <th className="px-2 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"></th>
                      </tr>
                    </thead>
                    <tbody>
                      {formData.items.map((item, index) => (
                        <tr key={item.id} className="border-b border-gray-200">
                          <td className="px-2 py-2">
                            <input
                              type="text"
                              value={item.description}
                              onChange={(e) =>
                                updateItem(
                                  item.id,
                                  "description",
                                  e.target.value
                                )
                              }
                              className="w-full px-2 py-1.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
                              placeholder="Item description"
                            />
                          </td>
                          <td className="px-2 py-2">
                            <input
                              type="number"
                              value={item.quantity}
                              onChange={(e) =>
                                updateItem(
                                  item.id,
                                  "quantity",
                                  parseInt(e.target.value) || 0
                                )
                              }
                              className="w-20 px-2 py-1.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary text-right"
                              min="1"
                            />
                          </td>
                          <td className="px-2 py-2">
                            <input
                              type="number"
                              value={item.unitPrice}
                              onChange={(e) =>
                                updateItem(
                                  item.id,
                                  "unitPrice",
                                  parseFloat(e.target.value) || 0
                                )
                              }
                              className="w-24 px-2 py-1.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary text-right"
                              min="0"
                              step="0.01"
                            />
                          </td>
                          <td className="px-2 py-2 text-right font-medium">
                            ${(item.quantity * item.unitPrice).toFixed(2)}
                          </td>
                          <td className="px-2 py-2 text-center">
                            {formData.items.length > 1 && (
                              <button
                                onClick={() => removeItem(item.id)}
                                className="text-red-500 hover:text-red-700"
                              >
                                <X className="w-4 h-4" />
                              </button>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                <div className="flex justify-end mt-4">
                  <div className="w-64 space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Subtotal:</span>
                      <span className="font-medium">
                        ${formData.subtotal.toFixed(2)}
                      </span>
                    </div>

                    <div className="flex justify-between pt-2 border-t border-gray-200">
                      <span className="text-gray-800 font-medium">Total:</span>
                      <span className="font-bold text-primary">
                        ${formData.total.toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {step === 4 && (
              <motion.div
                key="step4"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
              >
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Review Invoice
                </h3>

                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
                  <div className="flex justify-between mb-4">
                    <div>
                      <h4 className="font-bold text-gray-900">
                        Invoice #{formData.invoiceDetails.invoiceNumber}
                      </h4>
                      <p className="text-sm text-gray-600">
                        Issued:{" "}
                        {formData.invoiceDetails.issueDate
                          ? new Date(
                              formData.invoiceDetails.issueDate
                            ).toLocaleDateString()
                          : ""}
                      </p>
                      <p className="text-sm text-gray-600">
                        Due:{" "}
                        {formData.invoiceDetails.dueDate
                          ? new Date(
                              formData.invoiceDetails.dueDate
                            ).toLocaleDateString()
                          : ""}
                      </p>
                    </div>
                    <div className="text-right">
                      <h4 className="font-bold text-gray-900">Total Amount:</h4>
                      <p className="text-2xl font-bold text-primary">
                        ${formData.total.toFixed(2)}
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    <div>
                      <h5 className="font-medium text-gray-900 mb-1">From:</h5>
                      <p className="text-sm text-gray-700">Your Company Name</p>
                      <p className="text-sm text-gray-700">123 Business St</p>
                      <p className="text-sm text-gray-700">City, State ZIP</p>
                      <p className="text-sm text-gray-700">
                        <EMAIL>
                      </p>
                    </div>
                    <div>
                      <h5 className="font-medium text-gray-900 mb-1">
                        Bill To:
                      </h5>
                      <p className="text-sm text-gray-700">
                        {formData.clientInfo.name}
                      </p>
                      <p className="text-sm text-gray-700">
                        {formData.clientInfo.company}
                      </p>
                      <p className="text-sm text-gray-700 whitespace-pre-line">
                        {formData.clientInfo.address}
                      </p>
                      <p className="text-sm text-gray-700">
                        {formData.clientInfo.email}
                      </p>
                    </div>
                  </div>

                  <div className="mb-4">
                    <h5 className="font-medium text-gray-900 mb-2">
                      Invoice Items:
                    </h5>
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead>
                          <tr className="bg-gray-100">
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Description
                            </th>
                            <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Qty
                            </th>
                            <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Unit Price
                            </th>
                            <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Total
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {formData.items.map((item, index) => (
                            <tr
                              key={index}
                              className="border-b border-gray-200"
                            >
                              <td className="px-4 py-2 text-sm">
                                {item.description || "—"}
                              </td>
                              <td className="px-4 py-2 text-sm text-right">
                                {item.quantity}
                              </td>
                              <td className="px-4 py-2 text-sm text-right">
                                ${item.unitPrice.toFixed(2)}
                              </td>
                              <td className="px-4 py-2 text-sm text-right font-medium">
                                ${(item.quantity * item.unitPrice).toFixed(2)}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                        <tfoot>
                          <tr>
                            <td colSpan={3}></td>
                            <td className="px-4 py-2 text-sm text-right font-medium">
                              Subtotal:
                            </td>
                            <td className="px-4 py-2 text-sm text-right font-medium">
                              ${formData.subtotal.toFixed(2)}
                            </td>
                          </tr>

                          <tr className="bg-gray-50">
                            <td colSpan={3}></td>
                            <td className="px-4 py-2 text-right font-bold">
                              Total:
                            </td>
                            <td className="px-4 py-2 text-right font-bold">
                              ${formData.total.toFixed(2)}
                            </td>
                          </tr>
                        </tfoot>
                      </table>
                    </div>
                  </div>

                  {formData.notes && (
                    <div className="mb-4">
                      <h5 className="font-medium text-gray-900 mb-1">Notes:</h5>
                      <p className="text-sm text-gray-700 whitespace-pre-line">
                        {formData.notes}
                      </p>
                    </div>
                  )}

                  <div>
                    <h5 className="font-medium text-gray-900 mb-1">
                      Terms and Conditions:
                    </h5>
                    <p className="text-sm text-gray-700 whitespace-pre-line">
                      {formData.terms}
                    </p>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
          <div>
            {step > 1 && (
              <button
                onClick={prevStep}
                className="flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md transition-colors"
              >
                <ChevronsLeft className="h-4 w-4 mr-1" />
                Previous
              </button>
            )}
          </div>
          <div>
            {step < 4 ? (
              <button
                onClick={nextStep}
                className="flex items-center px-4 py-2 bg-primary hover:bg-primary/90 text-white rounded-md transition-colors"
              >
                Next
                <ChevronsRight className="h-4 w-4 ml-1" />
              </button>
            ) : (
              <button
                onClick={handleSubmit}
                className="flex items-center px-4 py-2 bg-primary hover:bg-primary/90 text-white rounded-md transition-colors"
              >
                Create Invoice
                <Save className="h-4 w-4 ml-1" />
              </button>
            )}
          </div>
        </div>
      </motion.div>
    </div>
  );
}
