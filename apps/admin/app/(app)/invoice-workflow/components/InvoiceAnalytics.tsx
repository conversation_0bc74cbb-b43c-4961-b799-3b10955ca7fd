"use client";

import { memo, useMemo } from "react";
import { motion } from "framer-motion";
import {
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  Tooltip,
  CartesianGrid,
  AreaChart,
  Area,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
} from "recharts";
import { AnimatedCard, AnimatedCardGrid } from "@/components/animated-card";
import { type InvoiceWithRelations } from "@/lib/logistics";
import { ChartWrapper, isDataSufficient } from "@/lib/chart-utils";

interface InvoiceAnalyticsProps {
  invoices: InvoiceWithRelations[];
  loading: boolean;
}

/**
 * Analytics component for Invoice Management
 *
 * Displays charts and analytics for invoice data.
 * Memoized to prevent unnecessary re-renders.
 */
export const InvoiceAnalytics = memo<InvoiceAnalyticsProps>(
  ({ invoices, loading }) => {
    // Prepare monthly invoice data
    const monthlyData = useMemo(() => {
      const monthlyMap = new Map();
      
      invoices.forEach((invoice) => {
        const date = new Date(invoice.created_at);
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        const monthName = date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
        
        if (!monthlyMap.has(monthKey)) {
          monthlyMap.set(monthKey, {
            name: monthName,
            invoices: 0,
            amount: 0,
          });
        }
        
        const data = monthlyMap.get(monthKey);
        data.invoices += 1;
        data.amount += invoice.total || 0;
      });
      
      return Array.from(monthlyMap.values()).sort((a, b) => a.name.localeCompare(b.name));
    }, [invoices]);

    // Prepare status distribution data
    const statusData = useMemo(() => {
      const statusMap = new Map();
      
      invoices.forEach((invoice) => {
        const status = invoice.status || 'DRAFT';
        if (!statusMap.has(status)) {
          statusMap.set(status, { name: status, value: 0, amount: 0 });
        }
        
        const data = statusMap.get(status);
        data.value += 1;
        data.amount += invoice.total || 0;
      });
      
      return Array.from(statusMap.values());
    }, [invoices]);

    // Chart colors
    const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

    if (loading) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      );
    }

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="space-y-6"
      >
        <AnimatedCardGrid className="grid-cols-1 lg:grid-cols-2">
          {/* Monthly Invoice Trend */}
          <AnimatedCard className="p-6">
            <h3 className="text-lg font-semibold mb-4">Monthly Invoice Trend</h3>
            <ChartWrapper
              data={monthlyData}
              height={300}
              emptyMessage="No invoice data available for trend analysis"
            >
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={monthlyData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip 
                    formatter={(value, name) => [
                      name === 'amount' ? `$${value.toLocaleString()}` : value,
                      name === 'amount' ? 'Amount' : 'Invoices'
                    ]}
                  />
                  <Area
                    type="monotone"
                    dataKey="invoices"
                    stackId="1"
                    stroke="#8884d8"
                    fill="#8884d8"
                    fillOpacity={0.6}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </ChartWrapper>
          </AnimatedCard>

          {/* Status Distribution */}
          <AnimatedCard className="p-6">
            <h3 className="text-lg font-semibold mb-4">Invoice Status Distribution</h3>
            <ChartWrapper
              data={statusData}
              height={300}
              emptyMessage="No status data available"
            >
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={statusData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {statusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [value, 'Count']} />
                </PieChart>
              </ResponsiveContainer>
            </ChartWrapper>
          </AnimatedCard>
        </AnimatedCardGrid>

        {/* Monthly Revenue */}
        <AnimatedCard className="p-6">
          <h3 className="text-lg font-semibold mb-4">Monthly Revenue</h3>
          <ChartWrapper
            data={monthlyData}
            height={400}
            emptyMessage="No revenue data available"
          >
            <ResponsiveContainer width="100%" height={400}>
              <BarChart data={monthlyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, 'Revenue']} />
                <Bar dataKey="amount" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </ChartWrapper>
        </AnimatedCard>
      </motion.div>
    );
  }
);

InvoiceAnalytics.displayName = "InvoiceAnalytics";
