"use client";

import { memo } from "react";
import { motion } from "framer-motion";
import {
  FileText,
  Calendar,
  DollarSign,
  User,
  Eye,
  Edit,
  Download,
  Share,
  Workflow,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  Package,
} from "lucide-react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import { Card, CardContent, CardHeader } from "@workspace/ui/components/card";
import { type InvoiceWithRelations } from "@/lib/logistics";
import { trimming } from "@/lib/utils";

interface InvoiceCardsProps {
  invoices: InvoiceWithRelations[];
  loading: boolean;
  emptyState: {
    icon: any;
    title: string;
    description: string;
    action: {
      label: string;
      onClick: () => void;
    };
  };
  onViewInvoice: (invoice: InvoiceWithRelations) => void;
  onEditInvoice: (invoice: InvoiceWithRelations) => void;
  onDownloadInvoice: (invoice: InvoiceWithRelations) => void;
  onShareInvoice: (invoice: InvoiceWithRelations) => void;
}

/**
 * Invoice Cards Component
 *
 * Displays invoices in a card grid layout with animations and actions.
 * Follows the design patterns from other card components in the application.
 */
export const InvoiceCards = memo<InvoiceCardsProps>(
  ({
    invoices,
    loading,
    emptyState,
    onViewInvoice,
    onEditInvoice,
    onDownloadInvoice,
    onShareInvoice,
  }) => {
    // Animation variants
    const containerVariants = {
      hidden: { opacity: 0 },
      visible: {
        opacity: 1,
        transition: {
          staggerChildren: 0.1,
          delayChildren: 0.2,
        },
      },
    };

    const cardVariants = {
      hidden: { opacity: 0, y: 20, scale: 0.95 },
      visible: {
        opacity: 1,
        y: 0,
        scale: 1,
        transition: {
          duration: 0.3,
          ease: "easeOut",
        },
      },
    };

    // Status configuration
    const getStatusConfig = (status: string) => {
      switch (status) {
        case "PAID":
          return {
            icon: CheckCircle,
            variant: "default" as const,
            color: "text-green-600",
          };
        case "PENDING":
        case "DRAFT":
          return {
            icon: Clock,
            variant: "secondary" as const,
            color: "text-yellow-600",
          };
        case "OVERDUE":
          return {
            icon: AlertCircle,
            variant: "destructive" as const,
            color: "text-red-600",
          };
        case "CLOSED":
          return {
            icon: XCircle,
            variant: "secondary" as const,
            color: "text-gray-600",
          };
        default:
          return {
            icon: FileText,
            variant: "outline" as const,
            color: "text-gray-600",
          };
      }
    };

    // Format currency
    const formatCurrency = (amount: number, currency = "USD") => {
      return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency,
      }).format(amount);
    };

    // Format date
    const formatDate = (dateString: string) => {
      return new Date(dateString).toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    };

    // Loading state
    if (loading) {
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {Array.from({ length: 8 }).map((_, index) => (
            <Card key={index} className="animate-pulse">
              <CardHeader className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="h-3 bg-gray-200 rounded"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                <div className="flex gap-2">
                  <div className="h-8 bg-gray-200 rounded flex-1"></div>
                  <div className="h-8 bg-gray-200 rounded flex-1"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      );
    }

    // Empty state
    if (invoices.length === 0) {
      const EmptyIcon = emptyState.icon;
      return (
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <EmptyIcon className="h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {emptyState.title}
          </h3>
          <p className="text-gray-500 mb-6 max-w-sm">
            {emptyState.description}
          </p>
          <Button onClick={emptyState.action.onClick}>
            {emptyState.action.label}
          </Button>
        </div>
      );
    }

    return (
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"
      >
        {invoices.map((invoice) => {
          const statusConfig = getStatusConfig(invoice.status || "DRAFT");
          const StatusIcon = statusConfig.icon;
          const isOverdue =
            invoice.due_at &&
            new Date(invoice.due_at) < new Date() &&
            invoice.status !== "PAID";

          return (
            <motion.div
              key={invoice.id}
              variants={cardVariants}
              whileHover={{ y: -2, scale: 1.01 }}
              whileTap={{ scale: 0.98 }}
            >
              <Card className="h-full cursor-pointer hover:shadow-lg transition-all duration-200">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="font-semibold text-sm text-gray-900 truncate">
                        {trimming(invoice.inv_number || "", 20).toUpperCase() ||
                          "Draft Invoice"}
                      </h3>
                      <p className="text-xs text-gray-500 mt-1">
                        {invoice.customer?.name || "No Customer"}
                      </p>
                    </div>
                    <Badge variant={statusConfig.variant} className="ml-2">
                      <StatusIcon className="h-3 w-3 mr-1" />
                      {invoice.status || "DRAFT"}
                    </Badge>
                  </div>
                </CardHeader>

                <CardContent className="pt-0 space-y-3">
                  {/* Amount */}
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-gray-400" />
                    <span className="text-sm font-medium">
                      {formatCurrency(invoice.total || 0)}
                    </span>
                  </div>

                  {/* Due Date */}
                  {invoice.due_at && (
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span
                        className={`text-sm ${isOverdue ? "text-red-600 font-medium" : "text-gray-600"}`}
                      >
                        Due {formatDate(invoice.due_at)}
                      </span>
                    </div>
                  )}

                  {/* Customer */}
                  {invoice.customer && (
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600 truncate">
                        {invoice.customer.email || invoice.customer.name}
                      </span>
                    </div>
                  )}

                  {/* Batch Code */}
                  {invoice.batch?.code && (
                    <div className="flex items-center gap-2">
                      <Package className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-primary font-medium truncate">
                        {invoice.batch.code}
                      </span>
                    </div>
                  )}

                  {/* Actions */}
                  <div className="flex gap-1 pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={(e) => {
                        e.stopPropagation();
                        onViewInvoice(invoice);
                      }}
                    >
                      <Eye className="h-3 w-3 mr-1" />
                      View
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      disabled={
                        invoice.status === "PAID" ||
                        invoice.status === "CLOSED" ||
                        invoice.type === "AUTOMATED"
                      }
                      onClick={(e) => {
                        e.stopPropagation();
                        onEditInvoice(invoice);
                      }}
                    >
                      {invoice.type === "AUTOMATED" ? (
                        <Workflow className="h-3 w-3 mr-1" />
                      ) : (
                        <Edit className="h-3 w-3 mr-1" />
                      )}
                      {invoice.type === "AUTOMATED" ? "Auto" : "Edit"}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        onDownloadInvoice(invoice);
                      }}
                    >
                      <Download className="h-3 w-3" />
                    </Button>
                    {/* Share button - only show if customer has email */}
                    {invoice.customer?.email && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          onShareInvoice(invoice);
                        }}
                      >
                        <Share className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </motion.div>
    );
  }
);

InvoiceCards.displayName = "InvoiceCards";
