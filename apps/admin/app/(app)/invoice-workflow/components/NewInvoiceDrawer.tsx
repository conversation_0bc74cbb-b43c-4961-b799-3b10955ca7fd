"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetDescription,
  She<PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
  She<PERSON><PERSON>ooter,
  SheetClose,
} from "@workspace/ui/components/sheet";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import {
  X,
  ChevronsRight,
  ChevronsLeft,
  Save,
  Plus,
  User,
  Building,
  Calendar,
  DollarSign,
  FileText,
  ClipboardList,
  Loader2,
  AlertCircle,
  CheckCircle,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { invoiceService, customerService } from "@/lib/logistics";
import type { Customer } from "@/lib/logistics/types";
import type { InvoiceInsert } from "@/lib/logistics/operations/invoices";

interface NewInvoiceDrawerProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onInvoiceCreated?: () => void; // Callback to refresh the invoice list
}

interface FormErrors {
  customerInfo?: Record<string, string>;
  invoiceDetails?: Record<string, string>;
  items?: string;
  general?: string;
}

// Factor unit options
const FACTOR_UNIT_OPTIONS = [
  { value: "CBM", label: "CBM" },
  { value: "Weight", label: "Weight" },
  { value: "CTN", label: "CTN" },
] as const;

export function NewInvoiceDrawer({
  isOpen,
  onOpenChange,
  onInvoiceCreated,
}: NewInvoiceDrawerProps) {
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [successMessage, setSuccessMessage] = useState("");
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string>("");

  const [formData, setFormData] = useState({
    customerInfo: {
      name: "",
      email: "",
      company: "",
      address: "",
      phone: "",
    },
    invoiceDetails: {
      invoiceNumber: `INV-${Math.floor(Math.random() * 1000)
        .toString()
        .padStart(4, "0")}`,
      issueDate: new Date().toISOString().split("T")[0],
      dueDate: new Date(new Date().setDate(new Date().getDate() + 30))
        .toISOString()
        .split("T")[0],
      reference: "",
      status: "DRAFT" as const,
    },
    items: [
      {
        id: 1,
        description: "",
        quantity: 1,
        factor_unit: "CBM",
        factor_value: 1,
        unitPrice: 0,
        total: 0,
      },
    ],
    notes: "",
    terms: "Payment is due within 30 days of receipt of this invoice.",
    subtotal: 0,
    total: 0,
  });

  // Load customers when component mounts
  useEffect(() => {
    const loadCustomers = async () => {
      try {
        const result = await customerService.getCustomersForSelection();
        if (result.success && result.data) {
          setCustomers(result.data as Customer[]);
        }
      } catch (error) {
        console.error("Failed to load customers:", error);
      }
    };

    loadCustomers();
  }, []);

  // Reset form and step when drawer opens
  useEffect(() => {
    if (isOpen) {
      setStep(1);
      setErrors({});
      setSuccessMessage("");
      setLoading(false);
      setSelectedCustomerId("");
      // Generate new invoice number
      setFormData((prev) => ({
        ...prev,
        invoiceDetails: {
          ...prev.invoiceDetails,
          invoiceNumber: `INV-${Math.floor(Math.random() * 10000)
            .toString()
            .padStart(4, "0")}`,
        },
      }));
    }
  }, [isOpen]);

  // Validation functions
  const validateStep1 = () => {
    const customerErrors: Record<string, string> = {};

    if (!selectedCustomerId) {
      customerErrors.customer = "Please select a customer";
    }

    setErrors((prev) => ({ ...prev, customerInfo: customerErrors }));
    return Object.keys(customerErrors).length === 0;
  };

  const validateStep2 = () => {
    const detailErrors: Record<string, string> = {};

    if (!formData.invoiceDetails.invoiceNumber.trim()) {
      detailErrors.invoiceNumber = "Invoice number is required";
    }

    if (!formData.invoiceDetails.issueDate) {
      detailErrors.issueDate = "Issue date is required";
    }

    if (!formData.invoiceDetails.dueDate) {
      detailErrors.dueDate = "Due date is required";
    } else if (
      formData.invoiceDetails.issueDate &&
      new Date(formData.invoiceDetails.dueDate) <=
        new Date(formData.invoiceDetails.issueDate)
    ) {
      detailErrors.dueDate = "Due date must be after issue date";
    }

    setErrors((prev) => ({ ...prev, invoiceDetails: detailErrors }));
    return Object.keys(detailErrors).length === 0;
  };

  const validateStep3 = () => {
    const validItems = formData.items.filter(
      (item) =>
        item.description.trim() && item.quantity > 0 && item.unitPrice >= 0
    );

    if (validItems.length === 0) {
      setErrors((prev) => ({
        ...prev,
        items: "At least one valid item is required",
      }));
      return false;
    }

    setErrors((prev) => ({ ...prev, items: undefined }));
    return true;
  };

  const nextStep = () => {
    let isValid = false;

    switch (step) {
      case 1:
        isValid = validateStep1();
        break;
      case 2:
        isValid = validateStep2();
        break;
      case 3:
        isValid = validateStep3();
        break;
      default:
        isValid = true;
    }

    if (isValid && step < 4) {
      setStep(step + 1);
    }
  };

  const prevStep = () => {
    if (step > 1) setStep(step - 1);
  };

  const handleChange = (section: string, field: string, value: any) => {
    setFormData((prev) => {
      if (section === "") {
        // Handle root-level properties
        return {
          ...prev,
          [field]: value,
        };
      }

      // Handle nested properties
      return {
        ...prev,
        [section]: {
          ...(prev[section as keyof typeof prev] as Record<string, any>),
          [field]: value,
        },
      };
    });

    // Clear errors for the field being changed
    if (section === "customerInfo" || section === "invoiceDetails") {
      setErrors((prev) => {
        const currentSectionErrors = prev[section as keyof FormErrors] as
          | Record<string, string>
          | undefined;
        return {
          ...prev,
          [section]: currentSectionErrors
            ? { ...currentSectionErrors, [field]: undefined }
            : {},
        };
      });
    }
  };

  const addItem = () => {
    const newItem = {
      id: formData.items.length + 1,
      description: "",
      quantity: 1,
      factor_unit: "Quantity",
      factor_value: 1,
      unitPrice: 0,
      total: 0,
    };

    setFormData((prev) => ({
      ...prev,
      items: [...prev.items, newItem],
    }));
  };

  const removeItem = (id: number) => {
    setFormData((prev) => ({
      ...prev,
      items: prev.items.filter((item) => item.id !== id),
    }));
  };

  const updateItem = (id: number, field: string, value: any) => {
    setFormData((prev) => {
      const updatedItems = prev.items.map((item) => {
        if (item.id === id) {
          const updatedItem = { ...item, [field]: value };

          // Recalculate total if any factor or unitPrice changes
          if (
            field === "quantity" ||
            field === "factor_unit" ||
            field === "factor_value" ||
            field === "unitPrice"
          ) {
            // Total = factor_value * unitPrice
            // factor_value is inclusive of quantity, factor_unit is just a label/type
            updatedItem.total =
              (updatedItem.quantity || 1) *
              (updatedItem.factor_value || 1) *
              (updatedItem.unitPrice || 0);
          }

          return updatedItem;
        }
        return item;
      });

      // Recalculate invoice totals
      const subtotal = updatedItems.reduce(
        (sum, item) => sum + (item.total || 0),
        0
      );

      return {
        ...prev,
        items: updatedItems,
        subtotal,
        total: subtotal,
      };
    });

    // Clear items error when user starts adding valid items
    setErrors((prev) => ({ ...prev, items: undefined }));
  };

  const getSelectedCustomer = (): {
    success: boolean;
    customerId?: string;
    error?: string;
  } => {
    if (!selectedCustomerId) {
      return { success: false, error: "No customer selected" };
    }

    return { success: true, customerId: selectedCustomerId };
  };

  const handleSubmit = async () => {
    // Final validation
    if (!validateStep1() || !validateStep2() || !validateStep3()) {
      setErrors((prev) => ({
        ...prev,
        general: "Please fix the errors above before submitting",
      }));
      return;
    }

    setLoading(true);
    setErrors({});
    setSuccessMessage("");

    try {
      // Step 1: Get selected customer
      const customerResult = getSelectedCustomer();
      if (!customerResult.success) {
        throw new Error(customerResult.error || "Failed to process customer");
      }

      // Step 2: Prepare invoice data
      const invoiceItems = formData.items
        .filter((item) => item.description.trim())
        .map((item) => ({
          description: `${item.description} - (${item.factor_unit}): ${item.factor_value}`,
          quantity: item.quantity,
          factor_unit: item.factor_unit,
          factor_value: item.factor_value,
          unitPrice: item.unitPrice,
          total: item.total,
        }));

      // Create invoice description with items details (for logging/debugging)
      const itemsDescription = invoiceItems
        .map(
          (item) =>
            `${item.description} (Qty: ${item.quantity}, Unit Price: $${item.unitPrice.toFixed(2)})`
        )
        .join("; ");

      const total = formData.items.reduce((sum, item) => sum + item.total, 0);
      console.log("Creating invoice with items:", itemsDescription);

      const invoiceData: InvoiceInsert = {
        inv_number: formData.invoiceDetails.invoiceNumber,
        notes: formData.notes || null,
        terms_and_conditions: formData.terms || null,
        due_at: formData.invoiceDetails.dueDate || null,
        customer_id: customerResult.customerId!,
        billing_address: formData.customerInfo.address || null,
        line_items: invoiceItems, // Store line items as JSONB
        total: total,
      };

      // Step 3: Create invoice
      const createInvoiceResult =
        await invoiceService.createInvoice(invoiceData);

      if (!createInvoiceResult.success) {
        // Check if the error is about missing table
        if (
          createInvoiceResult.error?.includes(
            'relation "invoices" does not exist'
          ) ||
          createInvoiceResult.error?.includes('table "invoices"') ||
          createInvoiceResult.error?.includes("invoices")
        ) {
          throw new Error(
            "The invoices table does not exist in your Supabase database. Please create the invoices table with the new schema:\n\nCREATE TABLE invoices (\n  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n  due_at TIMESTAMP,\n  inv_number VARCHAR,\n  terms_and_conditions VARCHAR,\n  notes VARCHAR,\n  line_item JSONB,\n  customer_id UUID REFERENCES customers(id),\n  billing_address VARCHAR,\n  created_at TIMESTAMPTZ DEFAULT NOW(),\n  updated_at TIMESTAMPTZ DEFAULT NOW()\n);"
          );
        }
        throw new Error(
          createInvoiceResult.error || "Failed to create invoice"
        );
      }

      // Success! Handle the new response structure
      const invoiceResult = createInvoiceResult.data;
      let successMsg = `Invoice ${formData.invoiceDetails.invoiceNumber} created successfully!`;

      if (invoiceResult?.document) {
        successMsg += ` Document generated: ${invoiceResult.document.fileName}`;
      }

      setSuccessMessage(successMsg);

      // Call callback to refresh the invoice list
      if (onInvoiceCreated) {
        onInvoiceCreated();
      }

      // Close drawer after a short delay to show success message
      setTimeout(() => {
        onOpenChange(false);
      }, 2000);
    } catch (error: any) {
      // Provide more specific error messages
      if (
        error.message.includes("invoices table does not exist") ||
        error.message.includes("CREATE TABLE invoices")
      ) {
        setErrors({
          general: `Database Setup Required: ${error.message}`,
        });
      } else {
        setErrors({
          general:
            error.message || "Failed to create invoice. Please try again.",
        });
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent
        side="right"
        className="w-full min-w-[35rem] p-4 bg-white overflow-hidden"
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <SheetHeader className="px-4 py-3 border-b border-gray-200">
            <SheetTitle className="text-lg font-medium text-gray-900">
              New Invoice
            </SheetTitle>
          </SheetHeader>

          {/* Success/Error Messages */}
          {(successMessage || errors.general) && (
            <div className="px-4 py-3 bg-gray-50 border-b border-gray-200">
              {successMessage && (
                <div className="flex items-center gap-2 text-green-700 bg-green-50 px-3 py-2 rounded-md border border-green-200">
                  <CheckCircle className="h-4 w-4 flex-shrink-0" />
                  <span className="text-sm">{successMessage}</span>
                </div>
              )}
              {errors.general && (
                <div className="text-red-700 bg-red-50 px-3 py-2 rounded-md border border-red-200">
                  <div className="flex items-start gap-2">
                    <AlertCircle className="h-4 w-4 flex-shrink-0 mt-0.5" />
                    <div className="text-sm">
                      {errors.general.includes("CREATE TABLE") ? (
                        <div>
                          <p className="font-semibold mb-2">
                            Database Setup Required
                          </p>
                          <p className="mb-2">
                            The invoices table doesn't exist. Please run this
                            SQL in your Supabase database:
                          </p>
                          <pre className="bg-gray-800 text-green-400 p-2 rounded text-xs overflow-x-auto whitespace-pre-wrap">
                            {`CREATE TABLE invoices (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  due_at TIMESTAMP,
  inv_number VARCHAR,
  terms_and_conditions VARCHAR,
  notes VARCHAR,
  line_items JSONB,
  customer_id UUID REFERENCES customers(id),
  billing_address VARCHAR,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);`}
                          </pre>
                        </div>
                      ) : (
                        <span>{errors.general}</span>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Step indicators */}
          <div className="px-4 py-3 bg-gray-50 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div
                  className={`flex items-center justify-center w-7 h-7 rounded-full ${
                    step >= 1
                      ? "bg-primary text-white"
                      : "bg-gray-200 text-gray-600"
                  }`}
                >
                  <span className="text-xs font-medium">1</span>
                </div>
              </div>
              <div className="h-0.5 w-10 bg-gray-200" />
              <div className="flex items-center">
                <div
                  className={`flex items-center justify-center w-7 h-7 rounded-full ${
                    step >= 2
                      ? "bg-primary text-white"
                      : "bg-gray-200 text-gray-600"
                  }`}
                >
                  <span className="text-xs font-medium">2</span>
                </div>
              </div>
              <div className="h-0.5 w-10 bg-gray-200" />
              <div className="flex items-center">
                <div
                  className={`flex items-center justify-center w-7 h-7 rounded-full ${
                    step >= 3
                      ? "bg-primary text-white"
                      : "bg-gray-200 text-gray-600"
                  }`}
                >
                  <span className="text-xs font-medium">3</span>
                </div>
              </div>
              <div className="h-0.5 w-10 bg-gray-200" />
              <div className="flex items-center">
                <div
                  className={`flex items-center justify-center w-7 h-7 rounded-full ${
                    step >= 4
                      ? "bg-primary text-white"
                      : "bg-gray-200 text-gray-600"
                  }`}
                >
                  <span className="text-xs font-medium">4</span>
                </div>
              </div>
            </div>
            <div className="flex justify-between mt-1 px-1 text-xs">
              <span
                className={
                  step >= 1 ? "text-primary font-medium" : "text-gray-500"
                }
              >
                Client
              </span>
              <span
                className={
                  step >= 2 ? "text-primary font-medium" : "text-gray-500"
                }
              >
                Details
              </span>
              <span
                className={
                  step >= 3 ? "text-primary font-medium" : "text-gray-500"
                }
              >
                Items
              </span>
              <span
                className={
                  step >= 4 ? "text-primary font-medium" : "text-gray-500"
                }
              >
                Review
              </span>
            </div>
          </div>

          {/* Form content - scrollable */}
          <div className="flex-1 overflow-y-auto py-3">
            <AnimatePresence mode="wait">
              {step === 1 && (
                <motion.div
                  key="step1"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.2 }}
                  className="space-y-4"
                >
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Client Information
                  </h3>

                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <Label className="block text-sm font-medium text-gray-700 mb-1">
                        Select Customer *
                      </Label>
                      <Select
                        value={selectedCustomerId}
                        onValueChange={(value) => {
                          setSelectedCustomerId(value);
                          // Update billing address from selected customer
                          const selectedCustomer = customers.find(
                            (c) => c.id === value
                          );
                          if (selectedCustomer) {
                            handleChange(
                              "customerInfo",
                              "address",
                              selectedCustomer.location || ""
                            );
                          }
                        }}
                      >
                        <SelectTrigger
                          className={`w-full bg-white text-gray-900 ${
                            errors.clientInfo?.customer
                              ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                              : ""
                          }`}
                        >
                          <SelectValue placeholder="Choose a customer..." />
                        </SelectTrigger>
                        <SelectContent className="max-h-60">
                          {customers.map((customer) => (
                            <SelectItem key={customer.id} value={customer.id}>
                              <div className="flex flex-row items-center gap-2">
                                <span className="font-medium">
                                  {customer.name}
                                </span>
                                <span className="text-sm text-gray-500">
                                  {customer?.email
                                    ? `(${customer.email})`
                                    : "No email"}
                                </span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.customerInfo?.customer && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.customerInfo.customer}
                        </p>
                      )}
                    </div>

                    {selectedCustomerId && (
                      <div className="p-3 bg-gray-50 rounded-md border">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">
                          Selected Customer
                        </h4>
                        {(() => {
                          const customer = customers.find(
                            (c) => c.id === selectedCustomerId
                          );
                          return customer ? (
                            <div className="text-sm text-gray-600 space-y-1">
                              <p>
                                <span className="font-medium">Name:</span>{" "}
                                {customer.name}
                              </p>
                              <p>
                                <span className="font-medium">Email:</span>{" "}
                                {customer.email}
                              </p>
                              {customer.phone && (
                                <p>
                                  <span className="font-medium">Phone:</span>{" "}
                                  {customer.phone}
                                </p>
                              )}
                              {customer.location && (
                                <p>
                                  <span className="font-medium">Location:</span>{" "}
                                  {customer.location}
                                </p>
                              )}
                            </div>
                          ) : null;
                        })()}
                      </div>
                    )}
                  </div>

                  <div>
                    <Label className="block text-sm font-medium text-gray-700 mb-1">
                      Billing Address
                    </Label>
                    <textarea
                      value={formData.customerInfo.address}
                      onChange={(e) =>
                        handleChange("customerInfo", "address", e.target.value)
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary bg-white text-gray-900"
                      placeholder="123 Main St, City, State, ZIP"
                      rows={3}
                    />
                  </div>
                </motion.div>
              )}

              {step === 2 && (
                <motion.div
                  key="step2"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.2 }}
                  className="space-y-4"
                >
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Invoice Details
                  </h3>

                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <Label className="block text-sm font-medium text-gray-700 mb-1">
                        Invoice Number *
                      </Label>
                      <Input
                        type="text"
                        value={formData.invoiceDetails.invoiceNumber}
                        onChange={(e) =>
                          handleChange(
                            "invoiceDetails",
                            "invoiceNumber",
                            e.target.value
                          )
                        }
                        className={`w-full bg-white text-gray-900 ${
                          errors.invoiceDetails?.invoiceNumber
                            ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                            : ""
                        }`}
                        required
                      />
                      {errors.invoiceDetails?.invoiceNumber && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.invoiceDetails.invoiceNumber}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label className="block text-sm font-medium text-gray-700 mb-1">
                        Reference (Optional)
                      </Label>
                      <Input
                        type="text"
                        value={formData.invoiceDetails.reference}
                        onChange={(e) =>
                          handleChange(
                            "invoiceDetails",
                            "reference",
                            e.target.value
                          )
                        }
                        className="w-full bg-white text-gray-900"
                        placeholder="PO-12345"
                      />
                    </div>

                    <div>
                      <Label className="block text-sm font-medium text-gray-700 mb-1">
                        Issue Date *
                      </Label>
                      <Input
                        type="date"
                        value={formData.invoiceDetails.issueDate}
                        onChange={(e) =>
                          handleChange(
                            "invoiceDetails",
                            "issueDate",
                            e.target.value
                          )
                        }
                        className={`w-full bg-white text-gray-900 ${
                          errors.invoiceDetails?.issueDate
                            ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                            : ""
                        }`}
                        required
                      />
                      {errors.invoiceDetails?.issueDate && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.invoiceDetails.issueDate}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label className="block text-sm font-medium text-gray-700 mb-1">
                        Due Date *
                      </Label>
                      <Input
                        type="date"
                        value={formData.invoiceDetails.dueDate}
                        onChange={(e) =>
                          handleChange(
                            "invoiceDetails",
                            "dueDate",
                            e.target.value
                          )
                        }
                        className={`w-full bg-white text-gray-900 ${
                          errors.invoiceDetails?.dueDate
                            ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                            : ""
                        }`}
                        required
                      />
                      {errors.invoiceDetails?.dueDate && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.invoiceDetails.dueDate}
                        </p>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label className="block text-sm font-medium text-gray-700 mb-1">
                      Terms and Conditions
                    </Label>
                    <textarea
                      value={formData.terms}
                      onChange={(e) =>
                        handleChange("", "terms", e.target.value)
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary bg-white text-gray-900"
                      rows={3}
                    />
                  </div>

                  <div>
                    <Label className="block text-sm font-medium text-gray-700 mb-1">
                      Notes (Optional)
                    </Label>
                    <textarea
                      value={formData.notes}
                      onChange={(e) =>
                        handleChange("", "notes", e.target.value)
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary bg-white text-gray-900"
                      placeholder="Additional information for the client..."
                      rows={2}
                    />
                  </div>
                </motion.div>
              )}

              {step === 3 && (
                <motion.div
                  key="step3"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.2 }}
                  className="space-y-4"
                >
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-lg font-medium text-gray-900">
                      Line Items
                    </h3>
                    <Button
                      onClick={addItem}
                      variant="outline"
                      className="inline-flex items-center gap-1 bg-primary/10 text-primary hover:bg-primary/20 py-1 px-2 h-auto"
                      size="sm"
                      disabled={loading}
                    >
                      <Plus className="w-3 h-3" />
                      Add
                    </Button>
                  </div>

                  {errors.items && (
                    <div className="text-red-600 bg-red-50 border border-red-200 rounded-md p-2 text-sm">
                      {errors.items}
                    </div>
                  )}

                  <div className="space-y-3">
                    {formData.items.map((item) => (
                      <div
                        key={item.id}
                        className="border border-gray-200 rounded-md p-2 bg-white"
                      >
                        <div className="flex justify-between items-center mb-2">
                          <label className="text-xs font-medium text-gray-700">
                            Description
                          </label>
                          {formData.items.length > 1 && (
                            <Button
                              onClick={() => removeItem(item.id)}
                              variant="ghost"
                              size="sm"
                              className="text-red-500 hover:text-red-700 hover:bg-red-50 p-0.5 h-auto"
                              disabled={loading}
                            >
                              <X className="w-3 h-3" />
                            </Button>
                          )}
                        </div>
                        <Input
                          type="text"
                          value={item.description}
                          onChange={(e) =>
                            updateItem(item.id, "description", e.target.value)
                          }
                          className="w-full mb-2 bg-white text-gray-900"
                          placeholder="Item description"
                          disabled={loading}
                        />
                        <div className="grid grid-cols-5 gap-2">
                          <div>
                            <label className="text-xs font-medium text-gray-700 mb-1 block">
                              Quantity
                            </label>
                            <Input
                              type="number"
                              value={item.quantity}
                              onChange={(e) =>
                                updateItem(
                                  item.id,
                                  "quantity",
                                  parseFloat(e.target.value) || 1
                                )
                              }
                              className="w-full bg-white text-gray-900 text-right"
                              min="0"
                              step="1"
                              disabled={loading}
                            />
                          </div>
                          <div>
                            <label className="text-xs font-medium text-gray-700 mb-1 block">
                              Factor Unit
                            </label>
                            <Select
                              value={item.factor_unit}
                              onValueChange={(value) =>
                                updateItem(item.id, "factor_unit", value)
                              }
                              disabled={loading}
                            >
                              <SelectTrigger className="w-full bg-white text-gray-900">
                                <SelectValue placeholder="Select unit" />
                              </SelectTrigger>
                              <SelectContent>
                                {FACTOR_UNIT_OPTIONS.map((option) => (
                                  <SelectItem
                                    key={option.value}
                                    value={option.value}
                                  >
                                    {option.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          <div>
                            <label className="text-xs font-medium text-gray-700 mb-1 block">
                              Factor Value
                            </label>
                            <Input
                              type="number"
                              value={item.factor_value}
                              onChange={(e) =>
                                updateItem(
                                  item.id,
                                  "factor_value",
                                  parseFloat(e.target.value) || 1
                                )
                              }
                              className="w-full bg-white text-gray-900 text-right"
                              min="0"
                              step="0.01"
                              disabled={loading}
                            />
                          </div>
                          <div>
                            <label className="text-xs font-medium text-gray-700 mb-1 block">
                              Unit Price
                            </label>
                            <Input
                              type="number"
                              value={item.unitPrice}
                              onChange={(e) =>
                                updateItem(
                                  item.id,
                                  "unitPrice",
                                  parseFloat(e.target.value) || 0
                                )
                              }
                              className="w-full bg-white text-gray-900 text-right"
                              min="0"
                              step="0.01"
                              disabled={loading}
                            />
                          </div>
                          <div>
                            <label className="text-xs font-medium text-gray-700 mb-1 block">
                              Total
                            </label>
                            <div className="w-full bg-gray-50 border border-gray-300 rounded-md px-3 py-2 text-right text-sm text-gray-700">
                              ${item.total.toFixed(2)}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="rounded-md border border-gray-200 bg-gray-50 p-3 mt-3">
                    <div className="space-y-1">
                      <div className="flex justify-between pt-1 border-t border-gray-200 text-sm">
                        <span className="text-gray-800 font-medium">
                          Total:
                        </span>
                        <span className="font-bold text-primary">
                          ${formData.total.toFixed(2)}
                        </span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              {step === 4 && (
                <motion.div
                  key="step4"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Review Invoice
                  </h3>

                  <div className="bg-gray-50 p-3 rounded-lg border border-gray-200 mb-4">
                    <div className="mb-3">
                      <h4 className="font-medium text-gray-900">
                        Invoice #{formData.invoiceDetails.invoiceNumber}
                      </h4>
                      <p className="text-xs text-gray-600">
                        Issued:{" "}
                        {formData.invoiceDetails.issueDate
                          ? new Date(
                              formData.invoiceDetails.issueDate
                            ).toLocaleDateString()
                          : ""}
                      </p>
                      <p className="text-xs text-gray-600">
                        Due:{" "}
                        {formData.invoiceDetails.dueDate
                          ? new Date(
                              formData.invoiceDetails.dueDate
                            ).toLocaleDateString()
                          : ""}
                      </p>
                      <div className="mt-2">
                        <span className="text-base font-bold text-primary">
                          ${formData.total.toFixed(2)}
                        </span>
                        <span className="text-xs text-gray-600 ml-1">
                          total
                        </span>
                      </div>
                    </div>

                    <div className="border-t border-gray-200 pt-3 mb-3">
                      <h5 className="text-sm font-medium text-gray-900 mb-1">
                        From
                      </h5>
                      <p className="text-xs text-gray-700">Shamwaa Logistics</p>
                      <p className="text-xs text-gray-700">
                        123 Shipping Lane, Port City, FL
                      </p>
                    </div>

                    <div className="border-t border-gray-200 pt-3 mb-3">
                      <h5 className="text-sm font-medium text-gray-900 mb-1">
                        Bill To
                      </h5>
                      <p className="text-xs text-gray-700">
                        {formData.customerInfo.name}
                      </p>
                      {formData.customerInfo.company && (
                        <p className="text-xs text-gray-700">
                          {formData.customerInfo.company}
                        </p>
                      )}
                      {formData.customerInfo.address && (
                        <p className="text-xs text-gray-700 whitespace-pre-line">
                          {formData.customerInfo.address}
                        </p>
                      )}
                    </div>

                    <div className="border-t border-gray-200 pt-3">
                      <h5 className="text-sm font-medium text-gray-900 mb-2">
                        Invoice Items
                      </h5>
                      <div className="space-y-2 mb-2">
                        {formData.items.map((item, index) => (
                          <div
                            key={index}
                            className="flex justify-between text-xs"
                          >
                            <div className="flex-1">
                              {item.description || "—"}
                            </div>
                            <div className="flex-none w-12 text-right">
                              ${(item.quantity * item.unitPrice).toFixed(2)}
                            </div>
                          </div>
                        ))}
                      </div>

                      <div className="border-t border-gray-200 pt-2">
                        <div className="flex justify-between text-sm font-medium mt-1">
                          <span>Total:</span>
                          <span className="text-primary">
                            ${formData.total.toFixed(2)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {formData.notes && (
                    <div className="border-t border-gray-200 pt-3 mb-3">
                      <h5 className="text-sm font-medium text-gray-900 mb-1">
                        Notes
                      </h5>
                      <p className="text-xs text-gray-700">{formData.notes}</p>
                    </div>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Footer */}
          <SheetFooter className="p-4 border-t border-gray-200 flex flex-row items-center justify-between">
            <div>
              {step > 1 && !loading && (
                <Button
                  onClick={prevStep}
                  variant="outline"
                  className="flex items-center gap-1"
                  disabled={loading}
                >
                  <ChevronsLeft className="h-4 w-4" />
                  Previous
                </Button>
              )}
            </div>
            <div>
              {step < 4 ? (
                <Button
                  onClick={nextStep}
                  className="flex items-center gap-1"
                  disabled={loading}
                >
                  Next
                  <ChevronsRight className="h-4 w-4" />
                </Button>
              ) : (
                <Button
                  onClick={handleSubmit}
                  className="flex items-center gap-1"
                  disabled={loading || !!successMessage}
                >
                  {loading ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      Create Invoice
                      <Save className="h-4 w-4" />
                    </>
                  )}
                </Button>
              )}
            </div>
          </SheetFooter>
        </div>
      </SheetContent>
    </Sheet>
  );
}
