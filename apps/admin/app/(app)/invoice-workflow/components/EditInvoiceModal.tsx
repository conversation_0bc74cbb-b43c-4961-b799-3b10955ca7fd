"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  X,
  ChevronsRight,
  ChevronsLeft,
  Save,
  Plus,
  User,
  DollarSign,
  FileText,
  ClipboardList,
  Loader2,
} from "lucide-react";
import { DEFAULT_COMPANY_CONFIG } from "@/lib/utils";
import { Input } from "@workspace/ui/components/input";
import { Textarea } from "@workspace/ui/components/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Button } from "@workspace/ui/components/button";
import {
  invoiceService,
  type InvoiceWithRelations,
} from "@/lib/logistics/operations/invoices";

// Factor unit options
const FACTOR_UNIT_OPTIONS = [
  { value: "CBM", label: "CBM" },
  { value: "Weight", label: "Weight" },
  { value: "CTN", label: "CTN" },
] as const;

interface Invoice {
  id: string;
  inv_number: string;
  customer_id: string;
  customer: {
    id: string;
    name: string;
    email: string | null;
    phone: string | null;
    location: string | null;
  };
  created_at: string;
  due_at: string | null;
  line_items: any[] | null;
  billing_address: string | null;
  terms_and_conditions: string | null;
  notes: string | null;
  status: string;
  type?: string | null; // Invoice type field
  totalAmount: number;
}

interface EditInvoiceModalProps {
  isOpen: boolean;
  onClose: () => void;
  invoice: InvoiceWithRelations;
  onInvoiceUpdated?: () => void;
  onStatusChanged?: () => void; // Optimized callback for status changes only
  onPaymentLedgerRequired?: (invoice: InvoiceWithRelations) => void; // Callback for payment ledger dialog
}

export function EditInvoiceModal({
  isOpen,
  onClose,
  invoice,
  onInvoiceUpdated,
  onStatusChanged,
  onPaymentLedgerRequired,
}: EditInvoiceModalProps) {
  const [step, setStep] = useState(1);
  const [saving, setSaving] = useState(false);

  // Parse line items from the database
  const parseLineItems = (lineItems: any) => {
    if (!lineItems || !Array.isArray(lineItems)) return [];

    return lineItems.map((item: any) => ({
      description: item.description || "",
      quantity: item.quantity || 1,
      factor_unit: item.factor_unit || "Quantity",
      factor_value: item.factor_value || 1,
      unitPrice: item.unitPrice || 0,
      total: item.total || 0,
    }));
  };

  const lineItems = parseLineItems(invoice.line_items);

  // Calculate subtotal, tax, and total using factor_value (quantity is inclusive)
  const subtotal = lineItems.reduce(
    (sum: number, item: any) =>
      sum +
      (item.quantity || 1) * (item.factor_value || 1) * (item.unitPrice || 0),
    0
  );

  const total = subtotal;

  // Generate expanded invoice data (in a real app this would come from API)
  const [formData, setFormData] = useState({
    customerInfo: {
      name: invoice.customer?.name || "",
      email: invoice.customer?.email || "",
      company: invoice.customer?.name || "",
      address: invoice.billing_address || "",
      phone: invoice.customer?.phone || "",
    },
    invoiceDetails: {
      invoiceNumber: invoice.inv_number || invoice.id,
      issueDate: invoice.created_at
        ? new Date(invoice.created_at).toISOString().split("T")[0]
        : "",
      dueDate: invoice.due_at
        ? new Date(invoice.due_at).toISOString().split("T")[0]
        : "",
      reference: "PO-12345",
      status: invoice.status,
    },
    items:
      lineItems.length > 0
        ? lineItems.map((item, index) => ({ ...item, id: index + 1 }))
        : [
            {
              id: 1,
              description: "Default Item",
              quantity: 1,
              factor_unit: "Quantity",
              factor_value: 1,
              unitPrice: 0,
              total: 0,
            },
          ],
    notes: invoice.notes || "",
    terms: invoice.terms_and_conditions || "",
    subtotal: subtotal,
    total: total,
  });

  // Calculate totals on component mount
  useEffect(() => {
    calculateTotals();
  }, []);

  // Close modal when Escape key is pressed
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose();
    };
    window.addEventListener("keydown", handleEscape);
    return () => window.removeEventListener("keydown", handleEscape);
  }, [onClose]);

  // Prevent scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    }
    return () => {
      document.body.style.overflow = "auto";
    };
  }, [isOpen]);

  const calculateTotals = () => {
    setFormData((prev) => {
      const subtotal = prev.items.reduce(
        (sum, item) =>
          sum +
          (item.quantity || 1) *
            (item.factor_value || 1) *
            (item.unitPrice || 0),
        0
      );
      return {
        ...prev,
        subtotal,
        total: subtotal,
      };
    });
  };

  const nextStep = () => {
    if (step < 4) setStep(step + 1);
  };

  const prevStep = () => {
    if (step > 1) setStep(step - 1);
  };

  const handleChange = (section: string, field: string, value: any) => {
    setFormData((prev) => {
      if (section === "") {
        // Handle root-level properties
        return {
          ...prev,
          [field]: value,
        };
      }

      // Handle nested properties
      return {
        ...prev,
        [section]: {
          ...(prev[section as keyof typeof prev] as Record<string, any>),
          [field]: value,
        },
      };
    });
  };

  const addItem = () => {
    const newItem = {
      id: formData.items.length + 1,
      description: "",
      quantity: 1,
      factor_unit: "CBM",
      factor_value: 1,
      unitPrice: 0,
      total: 0,
    };

    setFormData((prev) => ({
      ...prev,
      items: [...prev.items, newItem],
    }));
  };

  const removeItem = (id: number) => {
    setFormData((prev) => {
      const updatedItems = prev.items.filter((item) => item.id !== id);
      return {
        ...prev,
        items: updatedItems,
      };
    });

    // Recalculate totals after removing an item
    setTimeout(calculateTotals, 0);
  };

  const updateItem = (id: number, field: string, value: any) => {
    setFormData((prev) => {
      const updatedItems = prev.items.map((item) => {
        if (item.id === id) {
          const updatedItem = { ...item, [field]: value };

          // Recalculate total if factor_value or unitPrice changes (quantity is inclusive in factor_value)
          if (
            field === "quantity" ||
            field === "factor_value" ||
            field === "unitPrice"
          ) {
            updatedItem.total =
              (updatedItem.quantity || 1) *
              (updatedItem.factor_value || 1) *
              (updatedItem.unitPrice || 0);
          }

          return updatedItem;
        }
        return item;
      });

      return {
        ...prev,
        items: updatedItems,
      };
    });

    // Recalculate totals after updating an item
    setTimeout(calculateTotals, 0);
  };

  const handleSubmit = async () => {
    try {
      setSaving(true);

      // Check if status is changing to determine which callback to use
      const originalStatus = invoice.status?.toLowerCase() || "";
      const newStatus = (formData.invoiceDetails.status || "").toLowerCase();
      const isStatusChanging = originalStatus !== newStatus;

      // Prepare the update data
      const updateData = {
        inv_number: formData.invoiceDetails.invoiceNumber,
        due_at: formData.invoiceDetails.dueDate
          ? new Date(formData.invoiceDetails.dueDate).toISOString()
          : null,
        billing_address: formData.customerInfo.address,
        notes: formData.notes,
        terms_and_conditions: formData.terms,
        line_items: parseLineItems(formData.items),
        status: (formData.invoiceDetails.status || "").toUpperCase(),
        total: formData.total,
      };

      // Update the invoice
      const result = await invoiceService.updateInvoice(invoice.id, updateData);

      if (result.success) {
        // Check for validation errors in the error field
        if (result.error && result.error.startsWith("VALIDATION_ERROR:")) {
          const validationMessage = result.error.replace(
            "VALIDATION_ERROR: ",
            ""
          );
          alert(`⚠️ Payment Validation Failed\n\n${validationMessage}`);
          return;
        }

        console.log("Invoice updated successfully:", result.data);

        // Check if this is a NORMAL type invoice being marked as PAID
        const invoiceType = invoice.type?.toUpperCase() || "NORMAL";
        const isPaidStatusChange =
          newStatus === "paid" && originalStatus !== "paid";

        if (
          invoiceType === "NORMAL" &&
          isPaidStatusChange &&
          onPaymentLedgerRequired
        ) {
          console.log(
            "🏦 NORMAL invoice marked as paid - triggering ledger dialog"
          );
          onPaymentLedgerRequired(invoice);
        }

        // Use optimized callback for status changes, full refresh for other changes
        if (isStatusChanging && onStatusChanged) {
          onStatusChanged();
        } else if (onInvoiceUpdated) {
          onInvoiceUpdated();
        }

        // Close the modal
        onClose();
      } else {
        console.error("Failed to update invoice:", result.error);
        alert("Failed to update invoice. Please try again.");
      }
    } catch (error) {
      console.error("Error updating invoice:", error);
      alert("An error occurred while updating the invoice. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Modal */}
      <motion.div
        className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col overflow-hidden"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        transition={{ duration: 0.2 }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            Edit Invoice #{invoice.id}
          </h2>
          <Button
            onClick={onClose}
            variant="ghost"
            size="icon"
            className="rounded-full"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Step indicators */}
        <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full ${
                  step >= 1
                    ? "bg-primary text-white"
                    : "bg-gray-200 text-gray-600"
                } mr-2`}
              >
                <User className="h-4 w-4" />
              </div>
              <span
                className={
                  step >= 1 ? "text-gray-900 font-medium" : "text-gray-500"
                }
              >
                Customer
              </span>
            </div>
            <div className="h-0.5 w-12 bg-gray-200 mx-2" />
            <div className="flex items-center">
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full ${
                  step >= 2
                    ? "bg-primary text-white"
                    : "bg-gray-200 text-gray-600"
                } mr-2`}
              >
                <FileText className="h-4 w-4" />
              </div>
              <span
                className={
                  step >= 2 ? "text-gray-900 font-medium" : "text-gray-500"
                }
              >
                Details
              </span>
            </div>
            <div className="h-0.5 w-12 bg-gray-200 mx-2" />
            <div className="flex items-center">
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full ${
                  step >= 3
                    ? "bg-primary text-white"
                    : "bg-gray-200 text-gray-600"
                } mr-2`}
              >
                <ClipboardList className="h-4 w-4" />
              </div>
              <span
                className={
                  step >= 3 ? "text-gray-900 font-medium" : "text-gray-500"
                }
              >
                Items
              </span>
            </div>
            <div className="h-0.5 w-12 bg-gray-200 mx-2" />
            <div className="flex items-center">
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full ${
                  step >= 4
                    ? "bg-primary text-white"
                    : "bg-gray-200 text-gray-600"
                } mr-2`}
              >
                <DollarSign className="h-4 w-4" />
              </div>
              <span
                className={
                  step >= 4 ? "text-gray-900 font-medium" : "text-gray-500"
                }
              >
                Review
              </span>
            </div>
          </div>
        </div>

        {/* Form content - scrollable */}
        <div className="flex-1 overflow-y-auto px-6 py-4">
          <AnimatePresence mode="wait">
            {step === 1 && (
              <motion.div
                key="step1"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
                className="space-y-4"
              >
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Customer Information
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Customer Name *
                    </label>
                    <Input
                      type="text"
                      value={formData.customerInfo.name}
                      onChange={(e) =>
                        handleChange("customerInfo", "name", e.target.value)
                      }
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Email Address *
                    </label>
                    <Input
                      type="email"
                      value={formData.customerInfo.email}
                      onChange={(e) =>
                        handleChange("customerInfo", "email", e.target.value)
                      }
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Company
                    </label>
                    <Input
                      type="text"
                      value={formData.customerInfo.company}
                      onChange={(e) =>
                        handleChange("customerInfo", "company", e.target.value)
                      }
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Phone Number
                    </label>
                    <Input
                      type="tel"
                      value={formData.customerInfo.phone}
                      onChange={(e) =>
                        handleChange("customerInfo", "phone", e.target.value)
                      }
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Billing Address
                  </label>
                  <Textarea
                    value={formData.customerInfo.address}
                    onChange={(e) =>
                      handleChange("customerInfo", "address", e.target.value)
                    }
                    rows={3}
                  />
                </div>
              </motion.div>
            )}

            {step === 2 && (
              <motion.div
                key="step2"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
                className="space-y-4"
              >
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Invoice Details
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Invoice Number *
                    </label>
                    <Input
                      type="text"
                      value={formData.invoiceDetails.invoiceNumber}
                      onChange={(e) =>
                        handleChange(
                          "invoiceDetails",
                          "invoiceNumber",
                          e.target.value
                        )
                      }
                      required
                      readOnly
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Reference (Optional)
                    </label>
                    <Input
                      type="text"
                      value={formData.invoiceDetails.reference}
                      onChange={(e) =>
                        handleChange(
                          "invoiceDetails",
                          "reference",
                          e.target.value
                        )
                      }
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Issue Date *
                    </label>
                    <Input
                      type="date"
                      value={formData.invoiceDetails.issueDate}
                      onChange={(e) =>
                        handleChange(
                          "invoiceDetails",
                          "issueDate",
                          e.target.value
                        )
                      }
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Due Date *
                    </label>
                    <Input
                      type="date"
                      value={formData.invoiceDetails.dueDate}
                      onChange={(e) =>
                        handleChange(
                          "invoiceDetails",
                          "dueDate",
                          e.target.value
                        )
                      }
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Status
                    </label>
                    <Select
                      value={formData.invoiceDetails.status || ""}
                      onValueChange={(value) =>
                        handleChange("invoiceDetails", "status", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="draft">Draft</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="paid">Paid</SelectItem>
                        <SelectItem value="overdue">Overdue</SelectItem>
                        <SelectItem value="cancelled">Cancelled</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Terms and Conditions
                  </label>
                  <Textarea
                    value={formData.terms}
                    onChange={(e) => handleChange("", "terms", e.target.value)}
                    rows={3}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Notes (Optional)
                  </label>
                  <Textarea
                    value={formData.notes}
                    onChange={(e) => handleChange("", "notes", e.target.value)}
                    rows={2}
                  />
                </div>
              </motion.div>
            )}

            {step === 3 && (
              <motion.div
                key="step3"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
                className="space-y-4"
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    Line Items
                  </h3>
                  <Button
                    onClick={addItem}
                    variant="outline"
                    size="sm"
                    className="bg-primary/10 text-primary hover:bg-primary/20"
                  >
                    <Plus className="w-4 h-4 mr-1" />
                    Add Item
                  </Button>
                </div>

                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Description
                        </th>
                        <th className="px-2 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Quantity
                        </th>
                        <th className="px-2 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Factor Unit
                        </th>
                        <th className="px-2 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Factor Value
                        </th>
                        <th className="px-2 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Unit Price
                        </th>
                        <th className="px-2 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total
                        </th>
                        <th className="px-2 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"></th>
                      </tr>
                    </thead>
                    <tbody>
                      {formData.items.map((item, index) => (
                        <tr key={index} className="border-b border-gray-200">
                          <td className="px-2 py-2">
                            <Input
                              type="text"
                              value={item.description}
                              onChange={(e) =>
                                updateItem(
                                  item.id,
                                  "description",
                                  e.target.value
                                )
                              }
                              className="w-full px-2 py-1.5"
                            />
                          </td>
                          <td>
                            <Input
                              type="number"
                              value={item.quantity}
                              onChange={(e) =>
                                updateItem(
                                  item.id,
                                  "quantity",
                                  parseFloat(e.target.value) || 1
                                )
                              }
                              className="w-full bg-white text-gray-900 text-right"
                              min="0"
                              step="1"
                              // disabled={loading}
                            />
                          </td>
                          <td className="px-2 py-2">
                            <Select
                              value={item.factor_unit}
                              onValueChange={(value) =>
                                updateItem(item.id, "factor_unit", value)
                              }
                            >
                              <SelectTrigger className="w-24 px-2 py-1.5">
                                <SelectValue placeholder="Unit" />
                              </SelectTrigger>
                              <SelectContent>
                                {FACTOR_UNIT_OPTIONS.map((option) => (
                                  <SelectItem
                                    key={option.value}
                                    value={option.value}
                                  >
                                    {option.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </td>
                          <td className="px-2 py-2">
                            <Input
                              type="number"
                              value={item.factor_value}
                              onChange={(e) =>
                                updateItem(
                                  item.id,
                                  "factor_value",
                                  parseFloat(e.target.value) || 1
                                )
                              }
                              className="w-20 px-2 py-1.5 text-right"
                              min="0"
                              step="0.01"
                            />
                          </td>
                          <td className="px-2 py-2">
                            <Input
                              type="number"
                              value={item.unitPrice}
                              onChange={(e) =>
                                updateItem(
                                  item.id,
                                  "unitPrice",
                                  parseFloat(e.target.value) || 0
                                )
                              }
                              className="w-24 px-2 py-1.5 text-right"
                              min="0"
                              step="0.01"
                            />
                          </td>
                          <td className="px-2 py-2 text-right font-medium">
                            $
                            {(
                              (item.quantity || 1) *
                              (item.factor_value || 1) *
                              (item.unitPrice || 0)
                            ).toFixed(2)}
                          </td>
                          <td className="px-2 py-2 text-center">
                            {formData.items.length > 1 && (
                              <Button
                                onClick={() => removeItem(item.id)}
                                variant="ghost"
                                size="icon"
                                className="text-red-500 hover:text-red-700 h-8 w-8"
                              >
                                <X className="w-4 h-4" />
                              </Button>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                <div className="flex justify-end mt-4">
                  <div className="w-64 space-y-2">
                    <div className="flex justify-between pt-2 border-t border-gray-200">
                      <span className="text-gray-800 font-medium">Total:</span>
                      <span className="font-bold text-primary">
                        ${formData.total.toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}

            {step === 4 && (
              <motion.div
                key="step4"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
              >
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Review Invoice
                </h3>

                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
                  <div className="flex justify-between mb-4">
                    <div>
                      <h4 className="font-bold text-gray-900">
                        Invoice #{formData.invoiceDetails.invoiceNumber}
                      </h4>
                      <p className="text-sm text-gray-600">
                        Issued:{" "}
                        {formData.invoiceDetails.issueDate
                          ? new Date(
                              formData.invoiceDetails.issueDate
                            ).toLocaleDateString()
                          : "Not set"}
                      </p>
                      <p className="text-sm text-gray-600">
                        Due:{" "}
                        {formData.invoiceDetails.dueDate
                          ? new Date(
                              formData.invoiceDetails.dueDate
                            ).toLocaleDateString()
                          : "Not set"}
                      </p>
                      <p className="text-sm text-gray-600">
                        Status:{" "}
                        <span
                          className={
                            formData.invoiceDetails.status === "paid"
                              ? "text-green-600 font-medium"
                              : formData.invoiceDetails.status === "pending"
                                ? "text-amber-600 font-medium"
                                : "text-red-600 font-medium"
                          }
                        >
                          {(formData.invoiceDetails.status || "")
                            .charAt(0)
                            .toUpperCase() +
                            (formData.invoiceDetails.status || "").slice(1)}
                        </span>
                      </p>
                    </div>
                    <div className="text-right">
                      <h4 className="font-bold text-gray-900">Total Amount:</h4>
                      <p className="text-2xl font-bold text-primary">
                        ${formData.total.toFixed(2)}
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    <div>
                      <h5 className="font-medium text-gray-900 mb-1">From:</h5>
                      {Object.entries(DEFAULT_COMPANY_CONFIG).map(
                        ([key, value], index) => (
                          <p key={index} className="text-sm text-gray-700">
                            {key}: {value}
                          </p>
                        )
                      )}
                    </div>
                    <div>
                      <h5 className="font-medium text-gray-900 mb-1">
                        Bill To:
                      </h5>
                      <p className="text-sm text-gray-700">
                        {formData.customerInfo.name}
                      </p>
                      <p className="text-sm text-gray-700">
                        {formData.customerInfo.company}
                      </p>
                      <p className="text-sm text-gray-700 whitespace-pre-line">
                        {formData.customerInfo.address}
                      </p>
                      <p className="text-sm text-gray-700">
                        {formData.customerInfo.email}
                      </p>
                    </div>
                  </div>

                  <div className="mb-4">
                    <h5 className="font-medium text-gray-900 mb-2">
                      Invoice Items:
                    </h5>
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead>
                          <tr className="bg-gray-100">
                            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Description
                            </th>
                            <th></th>
                            <th></th>
                            <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Qty
                            </th>
                            <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Unit Price
                            </th>
                            <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Total
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {formData.items.map((item, index) => (
                            <tr
                              key={index}
                              className="border-b border-gray-200"
                            >
                              <td className="px-4 py-2 text-sm">
                                {item.description || "—"}
                              </td>
                              <td></td>
                              <td></td>
                              <td className="px-4 py-2 text-sm text-right">
                                {item.quantity}
                              </td>
                              <td className="px-4 py-2 text-sm text-right">
                                ${item.unitPrice.toFixed(2)}
                              </td>
                              <td className="px-4 py-2 text-sm text-right font-medium">
                                $
                                {(
                                  (item.quantity || 1) *
                                  (item.factor_value || 1) *
                                  (item.unitPrice || 0)
                                ).toFixed(2)}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                        <tfoot>
                          <tr className="bg-gray-50">
                            <td colSpan={4}></td>
                            <td className="px-4 py-2 text-right font-bold">
                              Total:
                            </td>
                            <td className="px-4 py-2 text-right font-bold">
                              ${formData.total.toFixed(2)}
                            </td>
                          </tr>
                        </tfoot>
                      </table>
                    </div>
                  </div>

                  {formData.notes && (
                    <div className="mb-4">
                      <h5 className="font-medium text-gray-900 mb-1">Notes:</h5>
                      <p className="text-sm text-gray-700 whitespace-pre-line">
                        {formData.notes}
                      </p>
                    </div>
                  )}

                  <div>
                    <h5 className="font-medium text-gray-900 mb-1">
                      Terms and Conditions:
                    </h5>
                    <p className="text-sm text-gray-700 whitespace-pre-line">
                      {formData.terms}
                    </p>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
          <div>
            {step > 1 && (
              <Button
                onClick={prevStep}
                variant="outline"
                className="flex items-center"
              >
                <ChevronsLeft className="h-4 w-4 mr-1" />
                Previous
              </Button>
            )}
          </div>
          <div>
            {step < 4 ? (
              <Button onClick={nextStep} className="flex items-center">
                Next
                <ChevronsRight className="h-4 w-4 ml-1" />
              </Button>
            ) : (
              <Button
                onClick={handleSubmit}
                className="flex items-center"
                disabled={saving}
              >
                {saving ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    Save Changes
                    <Save className="h-4 w-4 ml-1" />
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </motion.div>
    </div>
  );
}
