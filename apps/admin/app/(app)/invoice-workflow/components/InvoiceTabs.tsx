"use client";

import { memo } from "react";
import { BarChart3, FileText } from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON>Trigger,
} from "@workspace/ui/components/tabs";
import { type InvoiceWithRelations } from "@/lib/logistics";
import { type ColumnFilter } from "@/components/ui/filter-panel";
import { InvoicesList } from "./InvoicesList";
import { InvoiceAnalytics } from "./InvoiceAnalytics";

interface InvoiceTabsProps {
  activeTab: "overview" | "analytics";
  viewMode: "cards" | "table";
  searchTerm: string;
  filterStatus: string;
  columnFilters: ColumnFilter[];
  loading: boolean;
  filteredInvoices: InvoiceWithRelations[];
  onTabChange: (tab: "overview" | "analytics") => void;
  onViewModeChange: (mode: "cards" | "table") => void;
  onSearchChange: (term: string) => void;
  onFilterStatusChange: (status: string) => void;
  onColumnFiltersChange: (filters: ColumnFilter[]) => void;
  onRefresh: () => void;
  onInvoiceAction: (action: string, invoice?: InvoiceWithRelations) => void;
  // Checkbox support
  selectedItems: Set<string>;
  setSelectedItems: (items: Set<string>) => void;
  onBulkStatusUpdate: (status: string) => void;
  onBulkDelete: () => void;
  onBulkCreateTasks: () => void;
  onClearSelections?: () => void;
  // Pagination support - simplified like cargo management
  currentPage: number;
  onPageChange: (page: number) => void;
}

/**
 * Tabs component for Invoice Management
 *
 * Handles the tabbed interface for "Overview" and "Analytics" views.
 * Each tab contains appropriate content for invoice management.
 */
export const InvoiceTabs = memo<InvoiceTabsProps>(
  ({
    activeTab,
    viewMode,
    searchTerm,
    filterStatus,
    columnFilters,
    loading,
    filteredInvoices,
    onTabChange,
    onViewModeChange,
    onSearchChange,
    onFilterStatusChange,
    onColumnFiltersChange,
    onRefresh,
    onInvoiceAction,
    selectedItems,
    setSelectedItems,
    onBulkStatusUpdate,
    onBulkDelete,
    onBulkCreateTasks,
    onClearSelections,
    currentPage,
    onPageChange,
  }) => {
    return (
      <Tabs
        value={activeTab}
        onValueChange={(value) =>
          onTabChange(value as "overview" | "analytics")
        }
        className="w-full"
      >
        <TabsList className="grid w-max grid-cols-2">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Overview ({filteredInvoices.length})
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          <InvoicesList
            searchTerm={searchTerm}
            setSearchTerm={onSearchChange}
            filterStatus={filterStatus}
            setFilterStatus={onFilterStatusChange}
            viewMode={viewMode}
            setViewMode={onViewModeChange}
            loading={loading}
            onRefresh={onRefresh}
            columnFilters={columnFilters}
            setColumnFilters={onColumnFiltersChange}
            invoices={filteredInvoices}
            onViewInvoice={(invoice) => onInvoiceAction("view", invoice)}
            onEditInvoice={(invoice) => onInvoiceAction("edit", invoice)}
            onDownloadInvoice={(invoice) =>
              onInvoiceAction("download", invoice)
            }
            onShareInvoice={(invoice) => onInvoiceAction("share", invoice)}
            onCreateInvoice={() => onInvoiceAction("create")}
            selectedItems={selectedItems}
            setSelectedItems={setSelectedItems}
            onBulkStatusUpdate={onBulkStatusUpdate}
            onBulkDelete={onBulkDelete}
            onBulkCreateTasks={onBulkCreateTasks}
            onClearSelections={onClearSelections}
            currentPage={currentPage}
            onPageChange={onPageChange}
          />
        </TabsContent>

        <TabsContent value="analytics" className="mt-6">
          <InvoiceAnalytics invoices={filteredInvoices} loading={loading} />
        </TabsContent>
      </Tabs>
    );
  }
);

InvoiceTabs.displayName = "InvoiceTabs";
