"use client";

import { useState, useEffect } from "react";
import { Loader2, CreditCard, Edit3, Check, X, XCircle } from "lucide-react";
import { DEFAULT_COMPANY_CONFIG } from "@/lib/utils";
import { type InvoiceWithRelations } from "@/lib/logistics/operations/invoices";
import { useInvoiceManagement } from "../hooks/useInvoiceManagement";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";

interface ViewInvoiceModalProps {
  isOpen: boolean;
  onClose: () => void;
  invoice: InvoiceWithRelations;
  onInvoiceUpdated?: () => void; // Callback to refresh invoice data
  onStatusChanged?: () => void; // Optimized callback for status changes only
  onPaymentLedgerRequired?: (invoice: InvoiceWithRelations) => void; // Callback for payment ledger dialog
}

export function ViewInvoiceModal({
  isOpen,
  onClose,
  invoice,
  onInvoiceUpdated,
  onStatusChanged,
  onPaymentLedgerRequired,
}: ViewInvoiceModalProps) {
  // Use the invoice management hook for functions and loading states
  const {
    state,
    handleMarkAsPaid,
    handleUpdateConversionRate,
    handleCloseInvoice,
  } = useInvoiceManagement();

  // Local state for editing
  const [isEditingRate, setIsEditingRate] = useState(false);
  const [customRate, setCustomRate] = useState<string>(
    invoice?.currency_conv_rate?.toString() || "2622"
  );

  const handleCancelRateEdit = () => {
    setCustomRate(invoice?.currency_conv_rate?.toString() || "2622");
    setIsEditingRate(false);
  };

  // Update local state when invoice prop changes (after refresh)
  useEffect(() => {
    if (invoice?.currency_conv_rate) {
      setCustomRate(invoice.currency_conv_rate.toString());
    }
  }, [invoice?.currency_conv_rate]);

  // Show empty state if no invoice
  if (!invoice) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-md">
          <div className="flex flex-col items-center py-8">
            <p className="text-gray-600 mb-4">No invoice found</p>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90"
            >
              Close
            </button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  // Parse line items from the database
  const parseLineItems = (lineItems: any) => {
    if (!lineItems || !Array.isArray(lineItems)) return [];

    return lineItems.map((item: any) => ({
      tracking_number: item.tracking_number || "",
      china_tracking_number: item.china_tracking_number || "",
      description: item.description || "",
      quantity: item.quantity || 0,
      weight_value: item.weight_value || 0,
      weight_unit: item.weight_unit || "KILOGRAMS",
      cbm_value: item.cbm_value || 0,
      cbm_unit: item.cbm_unit || "METER_CUBIC",
      ctn: item.ctn || 0,
      factor_unit: item.factor_unit,
      factor_value: item.factor_value,
      unitPrice: item.unitPrice || 0,
      total: item.total || 0,
    }));
  };

  const lineItems = parseLineItems(invoice.line_items);

  // Calculate invoice status based on due date
  const getInvoiceStatus = () => {
    if (!invoice.due_at) return "pending";
    const dueDate = new Date(invoice.due_at);
    const now = new Date();
    return dueDate < now ? "overdue" : "pending";
  };

  // Determine entity type and details using soft recursion pattern
  const getEntityDetails = () => {
    // Soft recursion: Check customer first, then supplier, with fallback
    if (invoice.customer) {
      return {
        type: "customer" as const,
        name: invoice.customer.name || "Unknown Customer",
        email: invoice.customer.email || "",
        address: invoice.customer.location || "",
        phone: invoice.customer.phone || "",
        identifier: invoice.customer.id,
      };
    } else if (invoice.supplier) {
      return {
        type: "supplier" as const,
        name: `Supplier ${invoice.supplier.tracking_number || invoice.supplier.id}`,
        email: "N/A", // Suppliers don't have email in current schema
        address: invoice.supplier.location || "",
        phone: invoice.supplier.phone || "",
        identifier: invoice.supplier.id,
      };
    } else {
      // Fallback when neither customer nor supplier is available
      return {
        type: "unknown" as const,
        name: "N/A",
        email: "N/A",
        address: "N/A",
        phone: "N/A",
        identifier: "N/A",
      };
    }
  };

  const entityDetails = getEntityDetails();

  // Prepare expanded invoice data from fetched data
  const expandedInvoice = {
    id: invoice.inv_number || invoice.id,
    reference: invoice.inv_number,
    entityDetails,
    items: lineItems,
    notes:
      invoice.notes ||
      "Thank you for your business. We appreciate your prompt payment.",
    terms:
      invoice.terms_and_conditions ||
      "Payment is due within 30 days of receipt of this invoice. Late payments are subject to a 2% monthly fee.",
    issueDate: invoice.created_at,
    dueDate: invoice.due_at,
    status: invoice.status || getInvoiceStatus(),
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[90vh] p-0 overflow-y-auto">
        {/* Header */}
        <DialogHeader className="flex flex-row items-center justify-between pl-6 pr-12 py-4 border-b border-gray-200">
          <div className="flex items-center">
            <DialogTitle className="text-xl font-semibold text-gray-900">
              Invoice
            </DialogTitle>
            <span
              className={`ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
              ${
                expandedInvoice.status === "paid" || invoice.status === "PAID"
                  ? "bg-green-100 text-green-800"
                  : invoice.status === "CLOSED"
                    ? "bg-gray-100 text-gray-800"
                    : invoice.status === "PARTIAL_PAYMENT"
                      ? "bg-blue-100 text-blue-800"
                      : expandedInvoice.status === "pending"
                        ? "bg-amber-100 text-amber-800"
                        : "bg-red-100 text-red-800"
              }`}
            >
              {(invoice.status === "PAID"
                ? "paid"
                : invoice.status === "CLOSED"
                  ? "closed"
                  : invoice.status === "PARTIAL_PAYMENT"
                    ? "partial payment"
                    : expandedInvoice.status
              )
                .charAt(0)
                .toUpperCase() +
                (invoice.status === "PAID"
                  ? "paid"
                  : invoice.status === "CLOSED"
                    ? "closed"
                    : invoice.status === "PARTIAL_PAYMENT"
                      ? "partial payment"
                      : expandedInvoice.status
                ).slice(1)}
            </span>

            {/* Conversion Rate Section */}
            <div className="ml-4 flex items-center gap-2">
              <span className="text-sm text-gray-600">USD/TZS Rate:</span>
              {isEditingRate &&
              invoice.status !== "PAID" &&
              invoice.status !== "CLOSED" &&
              invoice.status !== "OVERDUE" ? (
                <div className="flex items-center gap-1">
                  <Input
                    type="number"
                    value={customRate}
                    onChange={(e) => setCustomRate(e.target.value)}
                    className="w-20 h-7 text-xs"
                    step="0.01"
                    min="0"
                  />
                  <Button
                    size="sm"
                    onClick={() =>
                      handleUpdateConversionRate(
                        invoice,
                        customRate,
                        onInvoiceUpdated,
                        setIsEditingRate
                      )
                    }
                    disabled={state.updatingRate}
                    className="h-7 w-7 p-0"
                  >
                    {state.updatingRate ? (
                      <Loader2 className="h-3 w-3 animate-spin" />
                    ) : (
                      <Check className="h-3 w-3" />
                    )}
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleCancelRateEdit}
                    className="h-7 w-7 p-0"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              ) : (
                <div className="flex items-center gap-1">
                  <span className="text-sm font-medium text-gray-900">
                    {(customRate || 2622).toLocaleString()}
                  </span>
                  {/* Hide edit button for closed/paid/overdue invoices */}
                  {invoice.status !== "PAID" &&
                    invoice.status !== "CLOSED" &&
                    invoice.status !== "OVERDUE" && (
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => setIsEditingRate(true)}
                        className="h-6 w-6 p-0 hover:bg-gray-100"
                      >
                        <Edit3 className="h-3 w-3" />
                      </Button>
                    )}
                </div>
              )}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="flex items-center gap-2">
            {/* Hide actions for closed, paid, or overdue invoices */}
            {invoice.status !== "PAID" &&
              invoice.status !== "CLOSED" &&
              invoice.status !== "OVERDUE" && (
                <>
                  <Button
                    onClick={() =>
                      handleMarkAsPaid(
                        invoice,
                        onPaymentLedgerRequired,
                        onStatusChanged,
                        onInvoiceUpdated,
                        onClose
                      )
                    }
                    disabled={state.markingAsPaid}
                    size="sm"
                    className="gap-2"
                  >
                    {state.markingAsPaid ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <CreditCard className="h-4 w-4" />
                    )}
                    {state.markingAsPaid
                      ? "Marking as Paid..."
                      : "Mark as Paid"}
                  </Button>

                  {/* Close Invoice Button - only show if there's a remaining balance */}
                  {(invoice.total || 0) > 0 && (
                    <Button
                      onClick={() =>
                        handleCloseInvoice(invoice, onStatusChanged, onClose)
                      }
                      disabled={state.closingInvoice}
                      size="sm"
                      variant="outline"
                      className="gap-2"
                    >
                      {state.closingInvoice ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <XCircle className="h-4 w-4" />
                      )}
                      {state.closingInvoice ? "Closing..." : "Close Invoice"}
                    </Button>
                  )}
                </>
              )}
          </div>
        </DialogHeader>

        {/* Invoice content - scrollable */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="max-w-4xl mx-auto">
            <div className="flex justify-between items-start mb-8">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Reference:{" "}
                </h1>
                {expandedInvoice.reference && (
                  <h3 className="text-xl font-normal text-gray-900">
                    {expandedInvoice.reference}
                  </h3>
                )}
              </div>
              <div className="text-right">
                <div className="text-gray-600">
                  <p>
                    Issue Date:{" "}
                    {expandedInvoice.issueDate
                      ? new Date(expandedInvoice.issueDate).toLocaleDateString()
                      : "N/A"}
                  </p>
                  <p>
                    Due Date:{" "}
                    {expandedInvoice.dueDate
                      ? new Date(expandedInvoice.dueDate).toLocaleDateString()
                      : "N/A"}
                  </p>
                </div>
                <div className="mt-4 bg-gray-100 p-3 rounded-md">
                  <p className="text-sm text-gray-600">Amount Due:</p>
                  <p className="text-2xl font-bold text-gray-900">
                    ${invoice?.total?.toLocaleString() || 0}
                  </p>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              <div>
                <h3 className="text-gray-600 text-sm font-semibold uppercase mb-2">
                  {expandedInvoice.entityDetails.type === "customer"
                    ? "Bill To"
                    : expandedInvoice.entityDetails.type === "supplier"
                      ? "Supplier"
                      : "Entity"}
                </h3>
                <div
                  className={`border-l-4 pl-4 ${
                    expandedInvoice.entityDetails.type === "customer"
                      ? "border-blue-300"
                      : expandedInvoice.entityDetails.type === "supplier"
                        ? "border-orange-300"
                        : "border-gray-300"
                  }`}
                >
                  <div className="flex items-center gap-2 mb-1">
                    <p className="text-gray-900 font-medium">
                      {expandedInvoice.entityDetails.name}
                    </p>
                    <span
                      className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                        expandedInvoice.entityDetails.type === "customer"
                          ? "bg-blue-100 text-blue-800"
                          : expandedInvoice.entityDetails.type === "supplier"
                            ? "bg-orange-100 text-orange-800"
                            : "bg-gray-100 text-gray-800"
                      }`}
                    >
                      {expandedInvoice.entityDetails.type
                        .charAt(0)
                        .toUpperCase() +
                        expandedInvoice.entityDetails.type.slice(1)}
                    </span>
                  </div>
                  {expandedInvoice.entityDetails.address !== "N/A" && (
                    <p className="text-gray-600 whitespace-pre-line">
                      {expandedInvoice.entityDetails.address}
                    </p>
                  )}
                  {expandedInvoice.entityDetails.email !== "N/A" && (
                    <p className="text-gray-600 mt-2">
                      {expandedInvoice.entityDetails.email}
                    </p>
                  )}
                  {expandedInvoice.entityDetails.phone !== "N/A" && (
                    <p className="text-gray-600">
                      {expandedInvoice.entityDetails.phone}
                    </p>
                  )}
                  {expandedInvoice.entityDetails.type === "unknown" && (
                    <p className="text-gray-500 italic text-sm">
                      No entity information available
                    </p>
                  )}
                </div>
              </div>
            </div>

            <div className="mb-8">
              <h3 className="text-gray-600 text-sm font-semibold uppercase mb-4">
                Invoice Items
              </h3>
              <div className="overflow-x-auto border border-gray-200 rounded-lg">
                <table className="w-full">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">
                        #
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        track no #
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        china track no #
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Description
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        CTN
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        CBM
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Weight
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Unit Price
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {expandedInvoice.items.map((item, index) => (
                      <tr key={index}>
                        <td className="px-4 py-3 text-sm text-gray-500 font-mono">
                          {index + 1}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {item.tracking_number}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {item.china_tracking_number}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {item.description}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900 text-right">
                          {(item as any).ctn || 0}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900 text-right">
                          {(item as any).cbm_value?.toFixed(2) || "0.00"} m³
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900 text-right">
                          {(item as any).weight_value?.toFixed(2) || "0.00"} kg
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900 text-right">
                          ${item.unitPrice.toLocaleString()}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900 font-medium text-right">
                          ${item.total.toLocaleString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            <div className="flex justify-end mb-8">
              <div className="w-80 space-y-3">
                <div className="flex justify-between pt-3 border-t border-gray-200">
                  <span className="text-gray-900 font-medium">
                    Total (USD):
                  </span>
                  <span className="font-bold text-primary">
                    ${invoice?.total?.toLocaleString() || 0}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-900 font-medium">
                    Total (TZS):
                  </span>
                  <span className="font-bold text-green-600">
                    TZS{" "}
                    {(
                      (invoice?.total || 0) * (parseFloat(customRate) || 2622)
                    ).toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between text-sm text-gray-500 pt-2 border-t border-gray-100">
                  <span>Exchange Rate:</span>
                  <span>
                    1 USD = {(customRate || 2622).toLocaleString()} TZS
                  </span>
                </div>
              </div>
            </div>

            {expandedInvoice.notes && (
              <div className="mb-6">
                <h3 className="text-gray-600 text-sm font-semibold uppercase mb-2">
                  Notes
                </h3>
                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <p className="text-gray-700">{expandedInvoice.notes}</p>
                </div>
              </div>
            )}

            <div className="mb-4">
              <h3 className="text-gray-600 text-sm font-semibold uppercase mb-2">
                Terms & Conditions
              </h3>
              <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <p className="text-gray-700">{expandedInvoice.terms}</p>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
