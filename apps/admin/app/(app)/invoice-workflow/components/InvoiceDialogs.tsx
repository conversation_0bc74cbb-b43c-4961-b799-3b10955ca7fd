"use client";

import { memo } from "react";
import { type InvoiceManagementState } from "./InvoiceManagementContainer";
import {
  NewInvoiceDrawer,
  ViewInvoiceModal,
  EditInvoiceModal,
  InvoicePaymentLedgerDialog,
} from "./index";

interface InvoiceDialogsProps {
  state: InvoiceManagementState;
  onDialogClose: (dialogType: string) => void;
  onInvoiceMutation: () => void;
}

/**
 * Dialogs component for Invoice Management
 *
 * Manages all dialog states and renders the appropriate dialogs.
 * Memoized to prevent unnecessary re-renders.
 */
export const InvoiceDialogs = memo<InvoiceDialogsProps>(
  ({ state, onDialogClose, onInvoiceMutation }) => {
    return (
      <>
        {/* New Invoice Drawer */}
        <NewInvoiceDrawer
          isOpen={state.isNewInvoiceDrawerOpen}
          onOpenChange={(open) => {
            if (!open) onDialogClose("NewInvoiceDrawer");
          }}
          onInvoiceCreated={onInvoiceMutation}
        />

        {/* View Invoice Modal */}
        {state.selectedInvoice && (
          <ViewInvoiceModal
            isOpen={state.isViewInvoiceModalOpen}
            onClose={() => onDialogClose("ViewInvoiceModal")}
            invoice={state.selectedInvoice}
            onInvoiceUpdated={onInvoiceMutation}
            onStatusChanged={onInvoiceMutation}
            onPaymentLedgerRequired={(invoice) => {
              onDialogClose("ViewInvoiceModal");
              // Set selected invoice and open payment ledger dialog
              // This would need to be handled by the parent component
            }}
          />
        )}

        {/* Edit Invoice Modal */}
        {state.selectedInvoice && (
          <EditInvoiceModal
            isOpen={state.isEditInvoiceModalOpen}
            onClose={() => onDialogClose("EditInvoiceModal")}
            invoice={state.selectedInvoice}
            onInvoiceUpdated={onInvoiceMutation}
          />
        )}

        {/* Payment Ledger Dialog */}
        {state.selectedInvoice && (
          <InvoicePaymentLedgerDialog
            isOpen={state.isPaymentLedgerDialogOpen}
            onClose={() => onDialogClose("PaymentLedgerDialog")}
            invoice={state.selectedInvoice}
            onTransactionCreated={onInvoiceMutation}
          />
        )}
      </>
    );
  }
);

InvoiceDialogs.displayName = "InvoiceDialogs";
