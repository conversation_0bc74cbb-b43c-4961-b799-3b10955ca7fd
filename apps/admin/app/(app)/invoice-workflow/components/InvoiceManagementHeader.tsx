"use client";

import { memo } from "react";
import { RefreshCw, Plus } from "lucide-react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Overview } from "@/modules/layouts/overview";

interface InvoiceManagementHeaderProps {
  loading: boolean;
  shouldShowCreateButton: boolean;
  onRefresh: () => void;
  onCreateInvoice: () => void;
}

/**
 * Header component for Invoice Management page
 *
 * Displays the page title, description, and action buttons.
 * Memoized to prevent unnecessary re-renders.
 */
export const InvoiceManagementHeader = memo<InvoiceManagementHeaderProps>(
  ({
    loading,
    shouldShowCreateButton,
    onRefresh,
    onCreateInvoice,
  }) => {
    return (
      <Overview.Header
        title="Invoice Management"
        caption="Create, track, and manage invoices, payments, and billing workflows"
        actions={
          <>
            <Button
              variant="outline"
              onClick={onRefresh}
              disabled={loading}
              aria-label="Refresh invoices"
            >
              <RefreshCw
                className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`}
              />
              Refresh
            </Button>
            
            {shouldShowCreateButton && (
              <Button
                onClick={onCreateInvoice}
                aria-label="Create new invoice"
              >
                <Plus className="h-4 w-4 mr-2" />
                New Invoice
              </Button>
            )}
          </>
        }
      />
    );
  }
);

InvoiceManagementHeader.displayName = "InvoiceManagementHeader";
