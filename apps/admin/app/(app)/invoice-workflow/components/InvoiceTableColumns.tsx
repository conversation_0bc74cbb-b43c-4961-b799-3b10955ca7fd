"use client";

import {
  <PERSON>,
  <PERSON>,
  Download,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>hare,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "lucide-react";
import { redirect } from "next/navigation";
import { Button } from "@workspace/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { TableCell } from "@workspace/ui/components/table";
import { type InvoiceWithRelations } from "@/lib/logistics";

interface InvoiceTableColumnsProps {
  onViewInvoice: (invoice: InvoiceWithRelations) => void;
  onEditInvoice: (invoice: InvoiceWithRelations) => void;
  onDownloadInvoice: (invoice: InvoiceWithRelations) => void;
  onShareInvoice: (invoice: InvoiceWithRelations) => void;
}

// Column format for Listing.Table component
interface ListingTableColumn {
  key: string;
  label: string;
  render?: (item: any, index: number) => React.ReactNode;
  className?: string;
}

/**
 * Get table columns configuration for Invoice Management
 *
 * Returns the structure and rendering of invoice table columns.
 */
export function getInvoiceTableColumns({
  onViewInvoice,
  onEditInvoice,
  onDownloadInvoice,
  onShareInvoice,
}: InvoiceTableColumnsProps): ListingTableColumn[] {
  const hideEdit = (invoice: InvoiceWithRelations) => {
    if (!invoice) return false;

    const type = invoice.type || "";
    const status = invoice.status || "";

    return (
      ["PAID", "CLOSED", "OVERDUE", "PENDING"].includes(status) &&
      type === "AUTOMATED"
    );
  };

  const columns: ListingTableColumn[] = [
    {
      key: "inv_number",
      label: "Invoice Number",
      render: (invoice: InvoiceWithRelations) => (
        <TableCell className="font-medium flex flex-col space-y-1">
          <span>{invoice.inv_number || `INV-${invoice.id.slice(0, 8)}`}</span>
          <small className="text-muted-foreground capitalize bg-gray-50 border border-gray-200 rounded-full w-max px-2">
            {invoice.type?.toLocaleLowerCase()}
          </small>
        </TableCell>
      ),
    },
    {
      key: "customer",
      label: "Customer",
      render: (invoice: InvoiceWithRelations) => (
        <TableCell>
          <div className="flex flex-col">
            <span className="font-medium">
              {invoice.customer?.name || "Unknown Customer"}
            </span>
            <small className="text-xs text-gray-400">
              {invoice.customer?.email || invoice.customer?.phone}
            </small>
          </div>
        </TableCell>
      ),
    },
    {
      key: "batch",
      label: "Batch Code",
      render: (invoice: InvoiceWithRelations) => (
        <TableCell>
          {invoice.batches?.code ? (
            <div
              className="font-medium text-primary hover:underline cursor-pointer"
              onClick={() => {
                // Navigate to batch management page
                redirect(`/batch-management/${invoice.batches?.id}`);
              }}
            >
              {invoice.batches.code}
            </div>
          ) : (
            <span className="text-gray-400 text-sm">No batch</span>
          )}
        </TableCell>
      ),
      className: "min-w-[140px]",
    },
    {
      key: "total",
      label: "Amount",
      render: (invoice: InvoiceWithRelations) => (
        <TableCell className="font-medium">
          ${(invoice.total || 0).toLocaleString()}
        </TableCell>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (invoice: InvoiceWithRelations) => (
        <TableCell>
          <span
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              invoice.status === "PAID"
                ? "bg-green-100 text-green-800"
                : invoice.status === "CLOSED"
                  ? "bg-gray-100 text-gray-800"
                  : invoice.status === "OVERDUE"
                    ? "bg-red-100 text-red-800"
                    : invoice.status === "PARTIAL_PAYMENT"
                      ? "bg-blue-100 text-blue-800"
                      : "bg-amber-100 text-amber-800"
            }`}
          >
            {invoice.status || "DRAFT"}
          </span>
        </TableCell>
      ),
    },
    {
      key: "expiry",
      label: "Expiry",
      render: (invoice: InvoiceWithRelations) => {
        if (!invoice.due_at) {
          return (
            <TableCell>
              <div className="text-sm">
                <span className="text-gray-400">No expiry date</span>
              </div>
            </TableCell>
          );
        }

        const expiryDate = new Date(invoice.due_at);
        const today = new Date();
        const timeDiff = expiryDate.getTime() - today.getTime();
        const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

        // Determine if overdue and styling
        const isOverdue =
          expiryDate < today &&
          invoice.status !== "PAID" &&
          invoice.status !== "CLOSED";

        return (
          <TableCell>
            <div className="text-sm">
              <div className="font-medium">
                {expiryDate.toLocaleDateString()}
              </div>
              <div
                className={`text-xs ${
                  isOverdue ? "text-red-500" : "text-gray-500"
                }`}
              >
                {isOverdue
                  ? "Overdue"
                  : daysDiff <= 0
                    ? "Due today"
                    : `${daysDiff} days left`}
              </div>
            </div>
          </TableCell>
        );
      },
    },
    {
      key: "created_at",
      label: "Created",
      render: (invoice: InvoiceWithRelations) => (
        <TableCell>
          {new Date(invoice.created_at).toLocaleDateString()}
        </TableCell>
      ),
    },
    {
      key: "shared",
      label: "Shared",
      render: (invoice: InvoiceWithRelations) => (
        <TableCell>
          <div className="flex items-center gap-2">
            <div
              className={`w-2 h-2 rounded-full ${
                (invoice as any).shared ? "bg-green-500" : "bg-gray-300"
              }`}
            />
            <span className="text-xs text-gray-600">
              {(invoice as any).shared ? "Shared" : "Not Shared"}
            </span>
          </div>
        </TableCell>
      ),
      className: "min-w-[100px]",
    },
    {
      key: "actions",
      label: "Actions",
      render: (invoice: InvoiceWithRelations) => (
        <TableCell>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onViewInvoice(invoice)}>
                <Eye className="mr-2 h-4 w-4" />
                View
              </DropdownMenuItem>
              {/* Hide Edit for OVERDUE, PAID, and CLOSED invoices */}
              {!hideEdit(invoice) && (
                <DropdownMenuItem onClick={() => onEditInvoice(invoice)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
              )}
              <DropdownMenuItem onClick={() => onDownloadInvoice(invoice)}>
                <Download className="mr-2 h-4 w-4" />
                Download
              </DropdownMenuItem>
              {/* Share invoice - only for invoices with customer email */}

              <DropdownMenuItem onClick={() => onShareInvoice(invoice)}>
                <Share className="mr-2 h-4 w-4" />
                Share with Customer
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onShareInvoice(invoice)}>
                <UserCheck className="mr-2 h-4 w-4" />
                Manually Shared
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </TableCell>
      ),
    },
  ];

  return columns;
}
