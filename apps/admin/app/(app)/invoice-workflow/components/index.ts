/**
 * Invoice Management Components
 *
 * Optimized component exports for the invoice management module.
 * All components are memoized for performance and follow React best practices.
 */

// Legacy components (existing)
export { NewInvoiceDrawer } from "./NewInvoiceDrawer";
export { ViewInvoiceModal } from "./ViewInvoiceModal";
export { EditInvoiceModal } from "./EditInvoiceModal";
export { InvoicePaymentLedgerDialog } from "./InvoicePaymentLedgerDialog";

// New modular components
export { InvoiceManagementContainer } from "./InvoiceManagementContainer";
export { InvoiceManagementHeader } from "./InvoiceManagementHeader";
export { InvoiceStatistics } from "./InvoiceStatistics";
export { InvoiceTabs } from "./InvoiceTabs";
export { InvoicesList } from "./InvoicesList";
export { InvoiceCards } from "./InvoiceCards";
export { InvoiceTableColumns } from "./InvoiceTableColumns";
export { InvoiceAnalytics } from "./InvoiceAnalytics";
export { InvoiceDialogs } from "./InvoiceDialogs";

// Re-export types
export type {
  InvoiceStats,
  InvoiceManagementState,
} from "./InvoiceManagementContainer";
