"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Building,
  DollarSign,
  Plus,
  Loader2,
  Check,
  ArrowRight,
  AlertCircle,
} from "lucide-react";
import { AnimatedCard } from "@/components/animated-card";
import {
  ledgerService,
  transactionService,
  type Ledger,
  type InvoiceWithRelations,
} from "@/lib/logistics";
import { useAppSelector } from "@/store/hooks";
import { showToast } from "@/lib/utils";

interface InvoicePaymentLedgerDialogProps {
  isOpen: boolean;
  onClose: () => void;
  invoice: InvoiceWithRelations;
  onTransactionCreated?: () => void;
}

export function InvoicePaymentLedgerDialog({
  isOpen,
  onClose,
  invoice,
  onTransactionCreated,
}: InvoicePaymentLedgerDialogProps) {
  const router = useRouter();
  const { user: authUser } = useAppSelector((state) => state.auth);

  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [ledgers, setLedgers] = useState<Ledger[]>([]);
  const [selectedLedgerId, setSelectedLedgerId] = useState<string>("");
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newLedgerName, setNewLedgerName] = useState("");

  // Calculate invoice total
  const invoiceTotal = invoice.total || invoice.subtotal || 0;

  useEffect(() => {
    if (isOpen && authUser?.accountId) {
      fetchLedgers();
    }
  }, [isOpen, authUser]);

  const fetchLedgers = async () => {
    try {
      setLoading(true);
      const result = await ledgerService.getAllLedgers({ limit: 100 });

      if (result.success && result.data) {
        // Filter ledgers that have "Sales Revenue" in their books or are suitable for revenue
        const suitableLedgers = result.data.filter(
          (ledger: Ledger) =>
            ledger.books?.includes("Sales Revenue") ||
            ledger.name.toLowerCase().includes("revenue") ||
            ledger.name.toLowerCase().includes("sales") ||
            ledger.tags?.includes("revenue")
        );

        setLedgers(suitableLedgers);

        // Auto-select the first suitable ledger if available
        if (suitableLedgers.length > 0) {
          setSelectedLedgerId(suitableLedgers[0].id);
        }
      }
    } catch (error) {
      console.error("Error fetching ledgers:", error);
      showToast("Failed to fetch ledgers", "error");
    } finally {
      setLoading(false);
    }
  };

  const handleCreateLedger = async () => {
    if (!newLedgerName.trim() || !authUser?.accountId) return;

    try {
      setCreating(true);

      const ledgerData = {
        name: newLedgerName.trim(),
        books: ["Sales Revenue"],
        status: "ACTIVE" as any,
        tags: ["revenue", "sales", "automated"],
        associated_table: "invoices",
        associated_id: invoice.id,
        account_id: authUser.accountId,
      };

      const result = await ledgerService.createLedger(ledgerData);

      if (result.success && result.data) {
        setLedgers((prev) => [result.data!, ...prev]);
        setSelectedLedgerId(result.data.id);
        setShowCreateForm(false);
        setNewLedgerName("");
        showToast("Ledger created successfully", "success");
      } else {
        showToast(result.error || "Failed to create ledger", "error");
      }
    } catch (error) {
      console.error("Error creating ledger:", error);
      showToast("Failed to create ledger", "error");
    } finally {
      setCreating(false);
    }
  };

  const handleSubmit = async () => {
    if (!selectedLedgerId || !authUser?.accountId) return;

    try {
      setCreating(true);

      // Create transaction for the invoice payment
      const transactionData = {
        name: `Invoice Payment - ${invoice.inv_number || invoice.id}`,
        amount: invoiceTotal,
        type: "DEBIT" as any, // Revenue is a credit
        context: `Payment received for invoice ${invoice.inv_number || invoice.id}${invoice.customer ? ` from ${invoice.customer.name}` : ""}`,
        status: "COMPLETED" as any,
        book: "Sales Revenue",
        tags: ["invoice-payment", "revenue", "automated"],
        ledger_id: selectedLedgerId,
        account_id: authUser.accountId,
      };

      const result =
        await transactionService.createTransaction(transactionData);

      if (result.success) {
        showToast("Transaction logged successfully", "success");
        onTransactionCreated?.();

        // Redirect to the ledger page
        router.push(`/finance/${selectedLedgerId}`);
        onClose();
      } else {
        showToast(result.error || "Failed to create transaction", "error");
      }
    } catch (error) {
      console.error("Error creating transaction:", error);
      showToast("Failed to create transaction", "error");
    } finally {
      setCreating(false);
    }
  };

  const resetForm = () => {
    setSelectedLedgerId("");
    setShowCreateForm(false);
    setNewLedgerName("");
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5 text-green-600" />
            Log Invoice Payment
          </DialogTitle>
          <p className="text-sm text-gray-500 mt-1">
            Select or create a ledger to log this invoice payment as a
            transaction under "Sales Revenue"
          </p>
        </DialogHeader>

        <div className="space-y-6">
          {/* Invoice Summary */}
          <AnimatedCard className="p-4 bg-green-50 border-green-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-green-900">
                  Invoice {invoice.inv_number || invoice.id}
                </h3>
                <p className="text-sm text-green-700">
                  {invoice.customer?.name || "Unknown Customer"}
                </p>
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-green-900">
                  ${invoiceTotal.toLocaleString()}
                </p>
                <Badge
                  variant="outline"
                  className="text-green-700 border-green-300"
                >
                  Payment Received
                </Badge>
              </div>
            </div>
          </AnimatedCard>

          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading ledgers...</span>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Ledger Selection */}
              {!showCreateForm && (
                <div className="space-y-3">
                  <Label htmlFor="ledger-select">Select Ledger</Label>
                  {ledgers.length > 0 ? (
                    <Select
                      value={selectedLedgerId}
                      onValueChange={setSelectedLedgerId}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a ledger for this transaction" />
                      </SelectTrigger>
                      <SelectContent>
                        {ledgers.map((ledger) => (
                          <SelectItem key={ledger.id} value={ledger.id}>
                            <div className="flex items-center gap-2">
                              <Building className="h-4 w-4" />
                              <span>{ledger.name}</span>
                              {ledger.books?.includes("Sales Revenue") && (
                                <Badge variant="secondary" className="text-xs">
                                  Sales Revenue
                                </Badge>
                              )}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  ) : (
                    <div className="text-center py-4 text-gray-500">
                      <AlertCircle className="h-8 w-8 mx-auto mb-2" />
                      <p>No suitable ledgers found</p>
                    </div>
                  )}

                  <Button
                    variant="outline"
                    onClick={() => setShowCreateForm(true)}
                    className="w-full gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    Create New Ledger
                  </Button>
                </div>
              )}

              {/* Create Ledger Form */}
              {showCreateForm && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  className="space-y-3 border rounded-lg p-4"
                >
                  <Label htmlFor="ledger-name">New Ledger Name</Label>
                  <Input
                    id="ledger-name"
                    value={newLedgerName}
                    onChange={(e) => setNewLedgerName(e.target.value)}
                    placeholder="e.g., Customer Payments Q4 2024"
                  />
                  <div className="flex gap-2">
                    <Button
                      onClick={handleCreateLedger}
                      disabled={!newLedgerName.trim() || creating}
                      className="gap-2"
                    >
                      {creating ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Check className="h-4 w-4" />
                      )}
                      Create Ledger
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setShowCreateForm(false)}
                    >
                      Cancel
                    </Button>
                  </div>
                </motion.div>
              )}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={!selectedLedgerId || creating}
              className="gap-2"
            >
              {creating ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <ArrowRight className="h-4 w-4" />
              )}
              Log Payment & View Ledger
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
