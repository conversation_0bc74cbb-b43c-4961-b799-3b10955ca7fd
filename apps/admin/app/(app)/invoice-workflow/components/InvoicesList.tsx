"use client";

import { memo, useMemo, useCallback } from "react";
import { FileText, Plus, Trash2, CheckSquare } from "lucide-react";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  BulkStatusSelector,
  INVOICE_STATUS_OPTIONS,
} from "@/components/ui/bulk-status-selector";
import { Listing } from "@/modules/listing";
import { ProtectedCreateButton } from "@/lib/components/RBACWrapper";
import { type InvoiceWithRelations } from "@/lib/logistics";
import { type ColumnFilter } from "@/components/ui/filter-panel";
import { getInvoiceTableColumns } from "./InvoiceTableColumns";
import { InvoiceCards } from "./InvoiceCards";
import { ITEMS_PER_PAGE } from "../hooks/useInvoiceManagement";

interface InvoicesListProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  filterStatus: string;
  setFilterStatus: (status: string) => void;
  viewMode: "cards" | "table";
  setViewMode: (mode: "cards" | "table") => void;
  loading: boolean;
  onRefresh: () => void;
  columnFilters: ColumnFilter[];
  setColumnFilters: (filters: ColumnFilter[]) => void;
  invoices: InvoiceWithRelations[];
  onViewInvoice: (invoice: InvoiceWithRelations) => void;
  onEditInvoice: (invoice: InvoiceWithRelations) => void;
  onDownloadInvoice: (invoice: InvoiceWithRelations) => void;
  onShareInvoice: (invoice: InvoiceWithRelations) => void;
  onCreateInvoice: () => void;
  // Checkbox support
  selectedItems: Set<string>;
  setSelectedItems: (items: Set<string>) => void;
  onBulkStatusUpdate: (status: string) => void;
  onBulkDelete: () => void;
  onBulkCreateTasks: () => void;
  onClearSelections?: () => void;
  // Pagination support - simplified like cargo management
  currentPage: number;
  onPageChange: (page: number) => void;
}

/**
 * Invoices list component with filtering, search, and table view
 *
 * Supports comprehensive filtering capabilities and table view.
 * Memoized for performance optimization.
 */
export const InvoicesList = memo<InvoicesListProps>(
  ({
    searchTerm,
    setSearchTerm,
    filterStatus,
    setFilterStatus,
    viewMode,
    setViewMode,
    loading,
    onRefresh,
    columnFilters,
    setColumnFilters,
    invoices,
    onViewInvoice,
    onEditInvoice,
    onDownloadInvoice,
    onShareInvoice,
    onCreateInvoice,
    selectedItems,
    setSelectedItems,
    onBulkStatusUpdate,
    onBulkDelete,
    onBulkCreateTasks,
    onClearSelections,
    currentPage,
    onPageChange,
  }) => {
    // Table columns configuration
    const tableColumns = getInvoiceTableColumns({
      onViewInvoice,
      onEditInvoice,
      onDownloadInvoice,
      onShareInvoice,
    });

    // Status filter options with defensive constraints
    const statusCategories = useMemo(() => {
      const categories = [
        {
          key: "paid",
          label: "Paid",
          count: invoices.filter((inv) => inv.status === "PAID").length,
        },
        {
          key: "pending",
          label: "Pending",
          count: invoices.filter(
            (inv) => inv.status === "PENDING" || inv.status === "DRAFT"
          ).length,
        },
        {
          key: "overdue",
          label: "Overdue",
          count: invoices.filter((inv) => {
            // Include invoices with OVERDUE status or those that are past due
            if (inv.status === "OVERDUE") return true;
            if (!inv.due_at) return false;
            const dueDate = new Date(inv.due_at);
            const today = new Date();
            return (
              dueDate < today &&
              inv.status !== "PAID" &&
              inv.status !== "CLOSED"
            );
          }).length,
        },
        {
          key: "closed",
          label: "Closed",
          count: invoices.filter((inv) => inv.status === "CLOSED").length,
        },
      ];

      // Add defensive constraint: if current filter results in no data,
      // and it's not "all", reset to "all"
      const currentCategoryCount =
        categories.find((cat) => cat.key === filterStatus)?.count || 0;
      if (
        filterStatus !== "all" &&
        currentCategoryCount === 0 &&
        invoices.length > 0
      ) {
        // Reset to "all" if current filter has no results but there are invoices
        setFilterStatus("all");
      }

      return categories;
    }, [invoices, filterStatus, setFilterStatus]);

    // Render bulk actions
    const renderBulkActions = useCallback(
      () => (
        <>
          <BulkStatusSelector
            statusOptions={INVOICE_STATUS_OPTIONS}
            onStatusUpdate={onBulkStatusUpdate}
            placeholder="Mark as..."
            onAfterUpdate={onRefresh}
          />
          <Button
            variant="outline"
            size="sm"
            onClick={onBulkCreateTasks}
            className="gap-2"
            disabled={selectedItems.size === 0}
          >
            <CheckSquare size={16} />
            Create Tasks
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={onBulkDelete}
            className="gap-2"
          >
            <Trash2 size={16} />
            Delete
          </Button>
        </>
      ),
      [
        onBulkStatusUpdate,
        onBulkDelete,
        onBulkCreateTasks,
        onRefresh,
        selectedItems.size,
      ]
    );

    // Empty state configuration for cards
    const emptyStateConfig = {
      icon: FileText,
      title: "No invoices found",
      description: "Get started by creating your first invoice",
      action: {
        label: "Create Invoice",
        onClick: onCreateInvoice,
      },
    };

    // Empty state for table (React element)
    const tableEmptyState = (
      <div className="text-center py-12">
        <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
          <FileText className="h-6 w-6 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-1">
          No invoices found
        </h3>
        <p className="text-gray-500 mb-4">
          Get started by creating your first invoice
        </p>
        <button
          onClick={onCreateInvoice}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
        >
          Create Invoice
        </button>
      </div>
    );

    // Use the filtered invoices passed from the parent (filtering is now done at hook level)
    const filteredInvoices = invoices;

    // Pagination (for cards view only - table handles its own pagination)
    const totalPages = Math.ceil(filteredInvoices.length / ITEMS_PER_PAGE);
    const paginatedInvoices = useMemo(() => {
      const start = (currentPage - 1) * ITEMS_PER_PAGE;
      const end = start + ITEMS_PER_PAGE;
      return filteredInvoices.slice(start, end);
    }, [filteredInvoices, currentPage]);

    return (
      <Listing>
        <Listing.Filters
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          columnFilters={columnFilters}
          onColumnFilterAdd={(filter) =>
            setColumnFilters([...columnFilters, filter])
          }
          onColumnFilterRemove={(index) =>
            setColumnFilters(columnFilters.filter((_, i) => i !== index))
          }
          columns={tableColumns}
          tableData={filteredInvoices}
          loading={loading}
          onRefresh={onRefresh}
          enableDynamicFilters={true}
          defaultFilterColumn="inv_number"
          autoSelectDefaultColumn={true}
          bulkActions={renderBulkActions()}
          selectedCount={selectedItems.size}
          showBulkActions={true}
          onClearSelections={onClearSelections}
        />

        <Listing.Controls
          entity="invoices"
          length={filteredInvoices.length}
          viewMode={viewMode}
          onViewModeChange={setViewMode}
          categories={statusCategories}
          categoryFilter={filterStatus}
          onCategoryFilterChange={setFilterStatus}
          actions={
            <div className="flex items-center gap-2">
              <ProtectedCreateButton entity="invoices">
                <Button onClick={onCreateInvoice}>
                  <Plus size={16} />
                  New Invoice
                </Button>
              </ProtectedCreateButton>
            </div>
          }
        />

        {viewMode === "cards" ? (
          <InvoiceCards
            invoices={paginatedInvoices}
            loading={loading}
            emptyState={emptyStateConfig}
            onViewInvoice={onViewInvoice}
            onEditInvoice={onEditInvoice}
            onDownloadInvoice={onDownloadInvoice}
            onShareInvoice={onShareInvoice}
          />
        ) : (
          <Listing.Table
            data={filteredInvoices}
            columns={tableColumns}
            loading={loading}
            enableCheckboxes={true}
            selectedRowIds={Array.from(selectedItems)}
            onSelectionChange={(selectedIds) =>
              setSelectedItems(new Set(selectedIds))
            }
            getRowId={(item) => item.id}
            emptyState={tableEmptyState}
            pagination={{
              currentPage,
              totalPages,
              totalItems: filteredInvoices.length,
              itemsPerPage: ITEMS_PER_PAGE,
              onPageChange,
            }}
          />
        )}
      </Listing>
    );
  }
);

InvoicesList.displayName = "InvoicesList";
