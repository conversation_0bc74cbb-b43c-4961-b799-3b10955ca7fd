"use client";

import { memo, useMemo } from "react";
import {
  <PERSON>Text,
  CheckCircle,
  Clock,
  AlertTriangle,
  DollarSign,
  TrendingUp,
  BarChart3,
  CheckCircle2,
} from "lucide-react";
import { Listing } from "@/modules/listing";
import { type InvoiceWithRelations } from "@/lib/logistics";

interface InvoiceStatisticsProps {
  invoices: InvoiceWithRelations[];
  filteredInvoices: InvoiceWithRelations[];
  hasActiveFilters: boolean;
  loading: boolean;
}

/**
 * Statistics component for Invoice Management
 *
 * Displays key metrics and KPIs for invoices in a grid layout.
 * Calculates stats dynamically based on filtered invoices (similar to finance page pattern).
 * Memoized to prevent unnecessary re-renders when parent state changes.
 */
export const InvoiceStatistics = memo<InvoiceStatisticsProps>(
  ({ invoices, filteredInvoices, hasActiveFilters, loading }) => {
    // Calculate filtered stats based on the currently filtered invoices
    const filteredStats = useMemo(() => {
      const paidInvoices = filteredInvoices.filter(
        (inv) => inv.status === "PAID"
      );
      const pendingInvoices = filteredInvoices.filter(
        (inv) => inv.status === "PENDING" || inv.status === "DRAFT"
      );
      const overdueInvoices = filteredInvoices.filter((inv) => {
        // Include invoices with OVERDUE status or those that are past due
        if (inv.status === "OVERDUE") return true;
        if (!inv.due_at) return false;
        const dueDate = new Date(inv.due_at);
        const today = new Date();
        return (
          dueDate < today && inv.status !== "PAID" && inv.status !== "CLOSED"
        );
      });

      const paidAmount = paidInvoices.reduce(
        (sum, inv) => sum + (inv.total || 0),
        0
      );
      const pendingAmount = pendingInvoices.reduce(
        (sum, inv) => sum + (inv.total || 0),
        0
      );
      const overdueAmount = overdueInvoices.reduce(
        (sum, inv) => sum + (inv.total || 0),
        0
      );

      const totalInvoices = filteredInvoices.length;
      const averageInvoiceValue =
        totalInvoices > 0
          ? (paidAmount + pendingAmount + overdueAmount) / totalInvoices
          : 0;

      const collectionRate =
        totalInvoices > 0 ? (paidInvoices.length / totalInvoices) * 100 : 0;

      return {
        totalInvoices,
        paidInvoicesCount: paidInvoices.length,
        pendingInvoicesCount: pendingInvoices.length,
        overdueInvoicesCount: overdueInvoices.length,
        paidAmount,
        pendingAmount,
        overdueAmount,
        averageInvoiceValue,
        collectionRate,
      };
    }, [filteredInvoices]);

    return (
      <Listing.Statistics columns="grid-cols-5">
        <Listing.StatCard
          icon={FileText}
          name="Total Invoices"
          value={filteredStats.totalInvoices}
          valueType="number"
          caption={
            hasActiveFilters ? (
              <span className="text-xs text-blue-600 flex items-center gap-1">
                • Filtered Results
              </span>
            ) : (
              <span className="text-xs text-blue-600 flex items-center gap-1">
                <CheckCircle2 className="h-3 w-3" />
                All invoices
              </span>
            )
          }
          color="primary"
          loading={loading}
        />

        <Listing.StatCard
          icon={CheckCircle}
          name="Paid"
          value={filteredStats.paidAmount}
          valueType="currency"
          caption={
            hasActiveFilters ? (
              <span className="text-xs text-blue-600 flex items-center gap-1">
                • Filtered Results
              </span>
            ) : (
              <span className="text-xs text-green-600 flex items-center gap-1">
                <CheckCircle2 className="h-3 w-3" />(
                {filteredStats.paidInvoicesCount}) Paid invoices
              </span>
            )
          }
          color="green"
          loading={loading}
        />

        <Listing.StatCard
          icon={Clock}
          name="Pending"
          value={filteredStats.pendingAmount}
          valueType="currency"
          caption={
            hasActiveFilters ? (
              <span className="text-xs text-blue-600 flex items-center gap-1">
                • Filtered Results
              </span>
            ) : (
              <span className="text-xs text-amber-600 flex items-center gap-1">
                <CheckCircle2 className="h-3 w-3" />(
                {filteredStats.pendingInvoicesCount}) Pending invoices
              </span>
            )
          }
          color="amber"
          loading={loading}
        />

        <Listing.StatCard
          icon={AlertTriangle}
          name="Overdue"
          value={filteredStats.overdueAmount}
          valueType="currency"
          caption={
            hasActiveFilters ? (
              <span className="text-xs text-blue-600 flex items-center gap-1">
                • Filtered Results
              </span>
            ) : (
              <span className="text-xs text-red-600 flex items-center gap-1">
                <CheckCircle2 className="h-3 w-3" />(
                {filteredStats.overdueInvoicesCount}) Overdue invoices
              </span>
            )
          }
          color="red"
          loading={loading}
        />

        <Listing.StatCard
          icon={DollarSign}
          name="Avg Value"
          value={filteredStats.averageInvoiceValue}
          valueType="currency"
          caption={
            hasActiveFilters ? (
              <span className="text-xs text-blue-600 flex items-center gap-1">
                • Filtered Results
              </span>
            ) : (
              <span className="text-xs text-purple-600 flex items-center gap-1">
                <CheckCircle2 className="h-3 w-3" />
                {filteredStats.collectionRate.toFixed(1)}% collected
              </span>
            )
          }
          color="purple"
          loading={loading}
        />
      </Listing.Statistics>
    );
  }
);

InvoiceStatistics.displayName = "InvoiceStatistics";
