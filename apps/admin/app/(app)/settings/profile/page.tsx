"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  User,
  Mail,
  Phone,
  Globe,
  Building,
  MapPin,
  Clock,
  Calendar,
  Shield,
  ChevronRight,
  Loader2,
  Upload,
  Camera,
  RefreshCw,
} from "lucide-react";
import { Input } from "@workspace/ui/components/input";
import { Button } from "@workspace/ui/components/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { AnimatedCard } from "@/components/animated-card";
import { PageTransition } from "@/components/page-transition";
import { useAppSelector } from "@/store/hooks";
import {
  userService,
  accountService,
  departmentService,
  roleService,
  type UserWithAccount,
  type Role,
  type Department,
} from "@/lib/logistics";
import {
  fadeAnimation,
  slideUpAnimation,
  springTransition,
  easeTransition,
} from "@/lib/motion-config";

interface UserProfile extends UserWithAccount {
  preferences?: {
    language?: string;
    timezone?: string;
    dateFormat?: string;
    theme?: string;
    notifications?: boolean;
  };
}

export default function ProfilePage() {
  const [activeTab, setActiveTab] = useState("personal");
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Get authenticated user
  const { user: authUser } = useAppSelector((state) => state.auth);

  // Fetch user profile and related data
  const fetchProfileData = async (refresh = false) => {
    if (!authUser?.id) return;

    try {
      if (refresh) setRefreshing(true);
      else setLoading(true);

      // Fetch user profile with account details
      const userResult = await userService.getUserWithAccount(authUser.id);
      if (userResult.success && userResult.data) {
        setUserProfile(userResult.data as UserProfile);
      }

      // Fetch departments for company info
      const deptResult = await departmentService.getAll({ limit: 100 });
      if (deptResult.success) {
        setDepartments(deptResult.data);
      }
    } catch (error) {
      console.error("Error fetching profile data:", error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    if (authUser) {
      fetchProfileData();
    }
  }, [authUser]);

  if (!authUser) {
    return (
      <PageTransition className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <h2 className="text-lg font-medium text-gray-900 mb-2">
              Authentication Required
            </h2>
            <p className="text-gray-500">
              Please sign in to access profile settings.
            </p>
          </div>
        </div>
      </PageTransition>
    );
  }

  return (
    <div className="space-y-6">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex justify-between items-center"
      >
        <div>
          <h2 className="text-xl font-medium text-gray-900">Profile</h2>
          <p className="text-sm text-gray-500 mt-1">
            Update your photo and personal details here.
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => fetchProfileData(true)}
            disabled={refreshing}
            className="flex items-center gap-1.5"
          >
            <RefreshCw size={16} className={refreshing ? "animate-spin" : ""} />
            Refresh
          </Button>
        </div>
      </motion.div>

      {/* Tab navigation */}
      <div className="flex border-b border-gray-200">
        <button
          className={`px-4 py-2 font-medium text-sm ${
            activeTab === "personal"
              ? "text-primary border-b-2 border-primary"
              : "text-gray-600 hover:text-gray-900"
          }`}
          onClick={() => setActiveTab("personal")}
        >
          Personal Info
        </button>
        <button
          className={`px-4 py-2 font-medium text-sm ${
            activeTab === "company"
              ? "text-primary border-b-2 border-primary"
              : "text-gray-600 hover:text-gray-900"
          }`}
          onClick={() => setActiveTab("company")}
        >
          Company Details
        </button>
        <button
          className={`px-4 py-2 font-medium text-sm ${
            activeTab === "preferences"
              ? "text-primary border-b-2 border-primary"
              : "text-gray-600 hover:text-gray-900"
          }`}
          onClick={() => setActiveTab("preferences")}
        >
          Preferences
        </button>
      </div>

      <div className="mt-2">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
          </div>
        ) : (
          <>
            {activeTab === "personal" && (
              <PersonalInfoTab
                userProfile={userProfile}
                saving={saving}
                setSaving={setSaving}
                setUserProfile={setUserProfile}
                errors={errors}
                setErrors={setErrors}
              />
            )}
            {activeTab === "company" && (
              <CompanyDetailsTab
                userProfile={userProfile}
                departments={departments}
                saving={saving}
                setSaving={setSaving}
                setUserProfile={setUserProfile}
                errors={errors}
                setErrors={setErrors}
              />
            )}
            {activeTab === "preferences" && (
              <PreferencesTab
                userProfile={userProfile}
                saving={saving}
                setSaving={setSaving}
                setUserProfile={setUserProfile}
                errors={errors}
                setErrors={setErrors}
              />
            )}
          </>
        )}
      </div>
    </div>
  );
}

interface TabProps {
  userProfile: UserProfile | null;
  saving: string | null;
  setSaving: (value: string | null) => void;
  setUserProfile: (profile: UserProfile | null) => void;
  errors: Record<string, string>;
  setErrors: (errors: Record<string, string>) => void;
}

function PersonalInfoTab({
  userProfile,
  saving,
  setSaving,
  setUserProfile,
  errors,
  setErrors,
}: TabProps) {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    location: "",
  });

  useEffect(() => {
    if (userProfile) {
      setFormData({
        name: userProfile.name || "",
        email: userProfile.accounts?.email || "",
        phone: userProfile.phone || "",
        location: userProfile.location || "",
      });
    }
  }, [userProfile]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    if (formData.phone && formData.phone.length > 20) {
      newErrors.phone = "Phone number is too long";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm() || !userProfile) return;

    setSaving("personal");
    try {
      // Update user profile
      const userUpdateResult = await userService.updateProfile(userProfile.id, {
        name: formData.name.trim(),
        phone: formData.phone.trim() || null,
        location: formData.location.trim() || null,
      });

      if (!userUpdateResult.success) {
        setErrors({
          general: userUpdateResult.error || "Failed to update profile",
        });
        return;
      }

      // Update account email if changed
      if (
        userProfile.accounts?.email !== formData.email &&
        userProfile.accounts?.id
      ) {
        const accountUpdateResult = await accountService.update(
          userProfile.accounts.id,
          {
            email: formData.email.toLowerCase().trim(),
          }
        );

        if (!accountUpdateResult.success) {
          setErrors({
            email: accountUpdateResult.error || "Failed to update email",
          });
          return;
        }
      }

      // Update local state
      setUserProfile({
        ...userProfile,
        ...userUpdateResult.data,
        accounts: userProfile.accounts
          ? {
              ...userProfile.accounts,
              email: formData.email.toLowerCase().trim(),
            }
          : undefined,
      });

      setErrors({});
      // Success feedback could be added here
    } catch (error) {
      console.error("Error saving profile:", error);
      setErrors({ general: "Failed to save changes" });
    } finally {
      setSaving(null);
    }
  };

  return (
    <AnimatedCard>
      <motion.div
        variants={slideUpAnimation}
        initial="hidden"
        animate="visible"
        transition={springTransition}
      >
        <div className="flex justify-between items-start mb-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              Personal Information
            </h3>
            <p className="text-sm text-gray-500">
              Update your basic profile details
            </p>
          </div>
        </div>

        {/* Profile Photo Section */}
        <div className="flex items-center gap-6 pt-4 pb-6 border-t border-gray-200">
          <div className="relative">
            <div className="flex-shrink-0 w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center">
              {userProfile?.avatar ? (
                <img
                  src={userProfile.avatar}
                  alt="Profile"
                  className="w-16 h-16 rounded-full object-cover"
                />
              ) : (
                <User className="w-8 h-8 text-gray-400" />
              )}
            </div>
            <button className="absolute -bottom-1 -right-1 p-1.5 bg-primary text-white rounded-full hover:bg-primary/90 transition-colors">
              <Camera className="w-3 h-3" />
            </button>
          </div>
          <div className="flex-1">
            <h4 className="text-sm font-medium text-gray-900">Profile Photo</h4>
            <p className="text-xs text-gray-500 mt-1">
              Upload a new profile picture
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" className="text-gray-700 text-sm">
              <Upload className="w-4 h-4 mr-2" />
              Upload
            </Button>
          </div>
        </div>

        {errors.general && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-800 text-sm">
            {errors.general}
          </div>
        )}

        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label
                htmlFor="name"
                className="block text-sm font-medium text-gray-700 mb-1.5"
              >
                Full Name *
              </label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, name: e.target.value }))
                }
                className={`w-full p-2.5 text-sm border rounded-lg bg-white ${
                  errors.name
                    ? "border-red-300 focus:border-red-500"
                    : "border-gray-200"
                }`}
                placeholder="Enter your full name"
              />
              {errors.name && (
                <p className="mt-1 text-xs text-red-600">{errors.name}</p>
              )}
            </div>
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 mb-1.5"
              >
                Email Address *
              </label>
              <div className="relative">
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, email: e.target.value }))
                  }
                  className={`pl-10 w-full p-2.5 text-sm border rounded-lg bg-white ${
                    errors.email
                      ? "border-red-300 focus:border-red-500"
                      : "border-gray-200"
                  }`}
                  placeholder="Enter your email address"
                />
              </div>
              {errors.email && (
                <p className="mt-1 text-xs text-red-600">{errors.email}</p>
              )}
            </div>
          </div>

          <div>
            <label
              htmlFor="phone"
              className="block text-sm font-medium text-gray-700 mb-1.5"
            >
              Phone Number
            </label>
            <div className="relative">
              <Input
                id="phone"
                value={formData.phone}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, phone: e.target.value }))
                }
                className={`pl-10 w-full p-2.5 text-sm border rounded-lg bg-white ${
                  errors.phone
                    ? "border-red-300 focus:border-red-500"
                    : "border-gray-200"
                }`}
                placeholder="Enter your phone number"
              />
            </div>
            {errors.phone && (
              <p className="mt-1 text-xs text-red-600">{errors.phone}</p>
            )}
          </div>

          <div>
            <label
              htmlFor="location"
              className="block text-sm font-medium text-gray-700 mb-1.5"
            >
              Location
            </label>
            <div className="relative">
              <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
              <textarea
                id="location"
                rows={3}
                value={formData.location}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, location: e.target.value }))
                }
                placeholder="Enter your location"
                className="pl-10 w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white resize-none"
              />
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-3 pt-6 mt-6 border-t border-gray-200">
          <Button
            variant="outline"
            className="text-gray-700 text-sm"
            onClick={() => {
              if (userProfile) {
                setFormData({
                  name: userProfile.name || "",
                  email: userProfile.accounts?.email || "",
                  phone: userProfile.phone || "",
                  location: userProfile.location || "",
                });
                setErrors({});
              }
            }}
            disabled={saving === "personal"}
          >
            Discard
          </Button>
          <Button
            className="bg-primary text-white text-sm"
            onClick={handleSave}
            disabled={saving === "personal"}
          >
            {saving === "personal" ? (
              <Loader2 size={16} className="animate-spin mr-2" />
            ) : null}
            {saving === "personal" ? "Saving..." : "Apply Changes"}
          </Button>
        </div>
      </motion.div>
    </AnimatedCard>
  );
}

interface CompanyTabProps extends TabProps {
  departments: Department[];
}

function CompanyDetailsTab({
  userProfile,
  departments,
  saving,
  setSaving,
  setUserProfile,
  errors,
  setErrors,
}: CompanyTabProps) {
  // Company details would be stored in user preferences or a separate company table
  // For now, we'll use basic user role and department information

  return (
    <AnimatedCard>
      <motion.div
        variants={slideUpAnimation}
        initial="hidden"
        animate="visible"
        transition={springTransition}
      >
        <div className="flex justify-between items-start mb-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              Company Information
            </h3>
            <p className="text-sm text-gray-500">
              View your company and role details
            </p>
          </div>
        </div>

        <div className="space-y-6 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1.5">
                Role
              </label>
              <div className="relative">
                <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                <Input
                  value={
                    userProfile?.accounts?.roles?.name || "No role assigned"
                  }
                  className="pl-10 w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-gray-50"
                  disabled
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1.5">
                Department
              </label>
              <div className="relative">
                <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                <Input
                  value={
                    userProfile?.accounts?.roles?.departments?.name ||
                    "No department assigned"
                  }
                  className="pl-10 w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-gray-50"
                  disabled
                />
              </div>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1.5">
              Account Status
            </label>
            <div className="flex items-center gap-2">
              <span
                className={`inline-flex items-center px-2 py-1 text-xs rounded-full ${
                  userProfile?.accounts?.status === "ACTIVE"
                    ? "bg-green-100 text-green-800"
                    : "bg-gray-100 text-gray-800"
                }`}
              >
                {userProfile?.accounts?.status || "Unknown"}
              </span>
            </div>
          </div>

          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="text-sm font-medium text-blue-900">
              Need to update company details?
            </h4>
            <p className="text-xs text-blue-800 mt-1">
              Contact your administrator to update role assignments or company
              information.
            </p>
          </div>
        </div>
      </motion.div>
    </AnimatedCard>
  );
}

function PreferencesTab({
  userProfile,
  saving,
  setSaving,
  setUserProfile,
  errors,
  setErrors,
}: TabProps) {
  const [preferences, setPreferences] = useState({
    language: "English",
    timezone: "Africa/Dar_es_Salaam",
    dateFormat: "DD/MM/YYYY",
    theme: "light",
  });

  useEffect(() => {
    if (userProfile?.preferences) {
      setPreferences((prev) => ({
        ...prev,
        ...userProfile.preferences,
      }));
    }
  }, [userProfile]);

  const handleSavePreferences = async () => {
    if (!userProfile) return;

    setSaving("preferences");
    try {
      // In a real implementation, you might have a separate preferences table
      // For now, we'll just update the user's metadata or use a JSON field
      const updatedProfile = {
        ...userProfile,
        preferences,
      };

      setUserProfile(updatedProfile);
      setErrors({});
      // Success feedback could be added here
    } catch (error) {
      console.error("Error saving preferences:", error);
      setErrors({ general: "Failed to save preferences" });
    } finally {
      setSaving(null);
    }
  };

  return (
    <div className="space-y-6">
      <AnimatedCard>
        <motion.div
          variants={slideUpAnimation}
          initial="hidden"
          animate="visible"
          transition={springTransition}
        >
          <div className="flex justify-between items-start mb-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                Time & Language
              </h3>
              <p className="text-sm text-gray-500">
                Set your time zone and language preferences
              </p>
            </div>
          </div>

          <div className="space-y-6 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1.5">
                  Language
                </label>
                <div className="relative">
                  <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                  <Select
                    value={preferences.language}
                    onValueChange={(value) =>
                      setPreferences((prev) => ({ ...prev, language: value }))
                    }
                  >
                    <SelectTrigger className="pl-10 w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="English">English</SelectItem>
                      <SelectItem value="French">French</SelectItem>
                      <SelectItem value="Spanish">Spanish</SelectItem>
                      <SelectItem value="Swahili">Swahili</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1.5">
                  Time Zone
                </label>
                <div className="relative">
                  <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                  <Select
                    value={preferences.timezone}
                    onValueChange={(value) =>
                      setPreferences((prev) => ({ ...prev, timezone: value }))
                    }
                  >
                    <SelectTrigger className="pl-10 w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Africa/Dar_es_Salaam">
                        Africa/Dar es Salaam (GMT+3)
                      </SelectItem>
                      <SelectItem value="UTC">UTC/GMT</SelectItem>
                      <SelectItem value="America/New_York">
                        America/New York (GMT-5)
                      </SelectItem>
                      <SelectItem value="Europe/London">
                        Europe/London (GMT+0)
                      </SelectItem>
                      <SelectItem value="Asia/Singapore">
                        Asia/Singapore (GMT+8)
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1.5">
                Date Format
              </label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                <Select
                  value={preferences.dateFormat}
                  onValueChange={(value) =>
                    setPreferences((prev) => ({ ...prev, dateFormat: value }))
                  }
                >
                  <SelectTrigger className="pl-10 w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                    <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                    <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-3 pt-6 mt-6 border-t border-gray-200">
            <Button variant="outline" className="text-gray-700 text-sm">
              Discard
            </Button>
            <Button
              className="bg-primary text-white text-sm"
              onClick={handleSavePreferences}
              disabled={saving === "preferences"}
            >
              {saving === "preferences" ? (
                <Loader2 size={16} className="animate-spin mr-2" />
              ) : null}
              {saving === "preferences" ? "Saving..." : "Apply Changes"}
            </Button>
          </div>
        </motion.div>
      </AnimatedCard>

      <AnimatedCard>
        <motion.div
          variants={slideUpAnimation}
          initial="hidden"
          animate="visible"
          transition={springTransition}
        >
          <div className="flex justify-between items-start mb-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                Privacy & Sessions
              </h3>
              <p className="text-sm text-gray-500">
                Manage your privacy settings and active sessions
              </p>
            </div>
          </div>

          <div className="space-y-4 pt-4 border-t border-gray-200">
            <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-md bg-blue-50">
                  <Shield className="h-4 w-4 text-blue-500" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    Two-Factor Authentication
                  </p>
                  <p className="text-xs text-gray-500">
                    Add an extra layer of security to your account
                  </p>
                </div>
              </div>
              <ChevronRight className="h-5 w-5 text-gray-400" />
            </div>

            <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-md bg-green-50">
                  <Clock className="h-4 w-4 text-green-500" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    Session Management
                  </p>
                  <p className="text-xs text-gray-500">
                    View and manage your active sessions
                  </p>
                </div>
              </div>
              <ChevronRight className="h-5 w-5 text-gray-400" />
            </div>
          </div>
        </motion.div>
      </AnimatedCard>
    </div>
  );
}
