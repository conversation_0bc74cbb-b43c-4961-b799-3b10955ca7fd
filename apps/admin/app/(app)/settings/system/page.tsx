"use client";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Button } from "@workspace/ui/components/button";

const SelectField = ({
  id,
  label,
  placeholder,
  defaultValue,
  options,
  className = "",
  description,
}: {
  id: string;
  label: string;
  placeholder?: string;
  defaultValue?: string;
  options: string[];
  className?: string;
  description?: string;
}) => (
  <div className={className}>
    <label
      htmlFor={id}
      className="block text-sm font-medium text-foreground-secondary mb-1.5"
    >
      {label}
    </label>
    {description && (
      <p className="text-xs text-foreground-quaternary mb-1.5">{description}</p>
    )}
    <Select defaultValue={defaultValue}>
      <SelectTrigger id={id} className="select-base">
        <SelectValue placeholder={placeholder ?? "Select..."} />
      </SelectTrigger>
      <SelectContent>
        {options.map((option) => (
          <SelectItem key={option} value={option}>
            {option}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  </div>
);

export default function SystemPage() {
  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-xl font-semibold text-foreground">
          System Settings
        </h2>
        <p className="text-sm text-foreground-tertiary mt-1">
          Update system-wide configurations.
        </p>
      </div>
      <div className="space-y-6 pt-6 border-t border-border">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <SelectField
            id="timeZone"
            label="Time Zone"
            defaultValue="Africa/Nairobi (GMT +3)"
            options={[
              "Africa/Nairobi (GMT +3)",
              "America/New_York (GMT -4)",
              "Europe/London (GMT +1)",
            ]}
          />
          <SelectField
            id="dateFormat"
            label="Date Format"
            defaultValue="DD/MM/YYYY"
            options={["DD/MM/YYYY", "MM/DD/YYYY", "YYYY-MM-DD"]}
          />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <SelectField
            id="currency"
            label="Currency"
            defaultValue="USD ($)"
            options={["USD ($)", "EUR (€)", "KES (KSh)"]}
          />
          <SelectField
            id="numberFormat"
            label="Format"
            defaultValue="10.00"
            options={["10.00", "10,00"]}
          />
        </div>
        <div>
          <SelectField
            id="autoLogout"
            label="Auto-logout after inactivity"
            defaultValue="15 minutes"
            description="Automatically log out users after period of inactivity"
            options={[
              "5 minutes",
              "15 minutes",
              "30 minutes",
              "1 hour",
              "Never",
            ]}
            className="max-w-[250px]"
          />
        </div>
      </div>
      <div className="flex justify-end gap-3 pt-8 border-t border-border">
        <Button variant="outline" className="button-outline button-md">
          Discard
        </Button>
        <Button className="button-primary button-md">Apply Changes</Button>
      </div>
    </div>
  );
}
