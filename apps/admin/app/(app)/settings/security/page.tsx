"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Shield,
  Lock,
  Key,
  AlertTriangle,
  LogOut,
  Smartphone,
  MapPin,
  Clock,
  AlertCircle,
  ChevronRight,
  Loader2,
  RefreshCw,
  Mail,
  Phone,
  Check,
  X,
} from "lucide-react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Switch } from "@workspace/ui/components/switch";
import { cn } from "@workspace/ui/lib/utils";
import { AnimatedCard } from "@/components/animated-card";
import { PageTransition } from "@/components/page-transition";
import { useAppSelector } from "@/store/hooks";
import {
  accountService,
  logService,
  type AccountWithUserAndRole,
  type Log,
} from "@/lib/logistics";
import {
  fadeAnimation,
  slideUpAnimation,
  springTransition,
  easeTransition,
} from "@/lib/motion-config";

interface SecuritySettings {
  twoFactorEnabled: boolean;
  emailNotifications: boolean;
  loginAlerts: boolean;
  sessionTimeout: number;
}

interface UserSession {
  id: string;
  deviceName: string;
  location: string;
  ipAddress: string;
  lastActive: string;
  isCurrent: boolean;
  createdAt: string;
}

export default function SecurityPage() {
  const [activeTab, setActiveTab] = useState("password");
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [userAccount, setUserAccount] = useState<AccountWithUserAndRole | null>(
    null
  );
  const [securitySettings, setSecuritySettings] = useState<SecuritySettings>({
    twoFactorEnabled: false,
    emailNotifications: true,
    loginAlerts: true,
    sessionTimeout: 30,
  });
  const [userSessions, setUserSessions] = useState<UserSession[]>([]);
  const [securityLogs, setSecurityLogs] = useState<Log[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Get authenticated user
  const { user: authUser, session } = useAppSelector((state) => state.auth);

  // Fetch security data
  const fetchSecurityData = async (refresh = false) => {
    if (!authUser?.id || !session?.account_id) return;

    try {
      if (refresh) setRefreshing(true);
      else setLoading(true);

      // Fetch user account details
      const accountResult = await accountService.getAccountWithUserAndRole(
        session.account_id
      );
      if (accountResult.success && accountResult.data) {
        setUserAccount(accountResult.data);
      }

      // Fetch security-related logs
      const logsResult = await logService.getLogsByAccount(session.account_id, {
        limit: 10,
        column: "created_at",
        ascending: false,
      });
      if (logsResult.success) {
        setSecurityLogs(logsResult.data);
      }

      // Mock sessions data (in real implementation, this would come from a sessions table)
      setUserSessions([
        {
          id: "1",
          deviceName: "Windows PC - Chrome",
          location: "Dar es Salaam, Tanzania",
          ipAddress: "*************",
          lastActive: new Date().toISOString(),
          isCurrent: true,
          createdAt: new Date(Date.now() - ********).toISOString(),
        },
        {
          id: "2",
          deviceName: "iPhone 13 - Safari",
          location: "Dar es Salaam, Tanzania",
          ipAddress: "*************",
          lastActive: new Date(Date.now() - 3600000).toISOString(),
          isCurrent: false,
          createdAt: new Date(Date.now() - *********).toISOString(),
        },
      ]);
    } catch (error) {
      console.error("Error fetching security data:", error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    if (authUser && session) {
      fetchSecurityData();
    }
  }, [authUser, session]);

  if (!authUser) {
    return (
      <PageTransition className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <h2 className="text-lg font-medium text-gray-900 mb-2">
              Authentication Required
            </h2>
            <p className="text-gray-500">
              Please sign in to access security settings.
            </p>
          </div>
        </div>
      </PageTransition>
    );
  }

  return (
    <div className="space-y-6">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex justify-between items-center"
      >
        <div>
          <h2 className="text-xl font-medium text-gray-900">Security</h2>
          <p className="text-sm text-gray-500 mt-1">
            Update and keep an eye on your security details
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => fetchSecurityData(true)}
            disabled={refreshing}
            className="flex items-center gap-1.5"
          >
            <RefreshCw size={16} className={refreshing ? "animate-spin" : ""} />
            Refresh
          </Button>
        </div>
      </motion.div>

      {/* Tab navigation */}
      <div className="flex border-b border-gray-200">
        <button
          className={`px-4 py-2 font-medium text-sm ${
            activeTab === "password"
              ? "text-primary border-b-2 border-primary"
              : "text-gray-600 hover:text-gray-900"
          }`}
          onClick={() => setActiveTab("password")}
        >
          Password
        </button>
        <button
          className={`px-4 py-2 font-medium text-sm ${
            activeTab === "authentication"
              ? "text-primary border-b-2 border-primary"
              : "text-gray-600 hover:text-gray-900"
          }`}
          onClick={() => setActiveTab("authentication")}
        >
          Authentication
        </button>
        <button
          className={`px-4 py-2 font-medium text-sm ${
            activeTab === "sessions"
              ? "text-primary border-b-2 border-primary"
              : "text-gray-600 hover:text-gray-900"
          }`}
          onClick={() => setActiveTab("sessions")}
        >
          Sessions
        </button>
      </div>

      <div className="mt-2">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
          </div>
        ) : (
          <>
            {activeTab === "password" && (
              <PasswordTab
                userAccount={userAccount}
                saving={saving}
                setSaving={setSaving}
                errors={errors}
                setErrors={setErrors}
              />
            )}
            {activeTab === "authentication" && (
              <AuthenticationTab
                securitySettings={securitySettings}
                setSecuritySettings={setSecuritySettings}
                saving={saving}
                setSaving={setSaving}
                errors={errors}
                setErrors={setErrors}
              />
            )}
            {activeTab === "sessions" && (
              <SessionsTab
                userSessions={userSessions}
                securityLogs={securityLogs}
                userAccount={userAccount}
                saving={saving}
                setSaving={setSaving}
              />
            )}
          </>
        )}
      </div>
    </div>
  );
}

interface PasswordTabProps {
  userAccount: AccountWithUserAndRole | null;
  saving: string | null;
  setSaving: (value: string | null) => void;
  errors: Record<string, string>;
  setErrors: (errors: Record<string, string>) => void;
}

function PasswordTab({
  userAccount,
  saving,
  setSaving,
  errors,
  setErrors,
}: PasswordTabProps) {
  const [passwordForm, setPasswordForm] = useState<{
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  }>({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });
  const [passwordStrength, setPasswordStrength] = useState<{
    score: number;
    requirements: { met: boolean; text: string }[];
  }>({
    score: 0,
    requirements: [],
  });

  const checkPasswordStrength = (password: string) => {
    const requirements = [
      { met: password.length >= 8, text: "At least 8 characters" },
      { met: /[A-Z]/.test(password), text: "At least one uppercase letter" },
      { met: /[a-z]/.test(password), text: "At least one lowercase letter" },
      { met: /\d/.test(password), text: "At least one number" },
      {
        met: /[!@#$%^&*(),.?":{}|<>]/.test(password),
        text: "At least one special character",
      },
    ];

    const score = requirements.filter((req) => req.met).length;
    setPasswordStrength({ score, requirements });
  };

  useEffect(() => {
    if (passwordForm.newPassword) {
      checkPasswordStrength(passwordForm.newPassword);
    }
  }, [passwordForm.newPassword]);

  const validatePasswordForm = () => {
    const newErrors: Record<string, string> = {};

    if (!passwordForm.currentPassword) {
      newErrors.currentPassword = "Current password is required";
    }

    if (!passwordForm.newPassword) {
      newErrors.newPassword = "New password is required";
    } else if (passwordStrength.score < 5) {
      newErrors.newPassword = "Password does not meet all requirements";
    }

    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handlePasswordChange = async () => {
    if (
      !validatePasswordForm() ||
      !userAccount ||
      !passwordForm.newPassword ||
      !passwordForm.currentPassword
    )
      return;

    setSaving("password");
    try {
      // Verify current password and update with new hashed password
      const result = await accountService.changePassword(
        userAccount.id,
        passwordForm.currentPassword,
        passwordForm.newPassword
      );

      if (result.success) {
        setPasswordForm({
          currentPassword: "",
          newPassword: "",
          confirmPassword: "",
        });
        setErrors({});
        // Success feedback could be added here
        console.log("✅ Password changed successfully");
      } else {
        setErrors({ general: result.error || "Failed to change password" });
      }
    } catch (error) {
      console.error("Error changing password:", error);
      setErrors({ general: "Failed to change password" });
    } finally {
      setSaving(null);
    }
  };

  return (
    <AnimatedCard>
      <motion.div
        variants={slideUpAnimation}
        initial="hidden"
        animate="visible"
        transition={springTransition}
      >
        <div className="flex justify-between items-start mb-6">
          <div className="flex gap-3 items-center">
            <div className="p-2 rounded-md bg-blue-50">
              <Lock className="h-5 w-5 text-blue-500" />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                Update Password
              </h3>
              <p className="text-sm text-gray-500">
                Ensure your account is using a strong password
              </p>
            </div>
          </div>
        </div>

        {errors.general && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-800 text-sm">
            {errors.general}
          </div>
        )}

        <div className="space-y-6 pt-4 border-t border-gray-200">
          <div>
            <label
              htmlFor="current-password"
              className="flex text-sm font-medium text-gray-700 mb-1.5"
            >
              Current password
              <span className="text-red-500 ml-0.5">*</span>
            </label>
            <div className="relative">
              <Key className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
              <Input
                id="current-password"
                type="password"
                value={passwordForm.currentPassword}
                onChange={(e) =>
                  setPasswordForm((prev) => ({
                    ...prev,
                    currentPassword: e.target.value,
                  }))
                }
                className={`pl-10 w-full p-2.5 text-sm border rounded-lg bg-white ${
                  errors.currentPassword
                    ? "border-red-300 focus:border-red-500"
                    : "border-gray-200"
                }`}
                placeholder="Enter current password"
              />
            </div>
            {errors.currentPassword && (
              <p className="mt-1 text-xs text-red-600">
                {errors.currentPassword}
              </p>
            )}
          </div>

          <div>
            <label
              htmlFor="new-password"
              className="flex text-sm font-medium text-gray-700 mb-1.5"
            >
              New password <span className="text-red-500 ml-0.5">*</span>
            </label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
              <Input
                id="new-password"
                type="password"
                value={passwordForm.newPassword}
                onChange={(e) =>
                  setPasswordForm((prev) => ({
                    ...prev,
                    newPassword: e.target.value,
                  }))
                }
                className={`pl-10 w-full p-2.5 text-sm border rounded-lg bg-white ${
                  errors.newPassword
                    ? "border-red-300 focus:border-red-500"
                    : "border-gray-200"
                }`}
                placeholder="Enter new password"
              />
            </div>
            {passwordForm.newPassword && (
              <div className="mt-3 p-3 bg-gray-50 border border-gray-200 rounded-md">
                <div className="flex items-center gap-2 mb-2">
                  <p className="text-xs text-gray-700 font-medium">
                    Password strength:
                  </p>
                  <div className="flex gap-1">
                    {[1, 2, 3, 4, 5].map((level) => (
                      <div
                        key={level}
                        className={`w-6 h-1 rounded ${
                          passwordStrength.score >= level
                            ? passwordStrength.score <= 2
                              ? "bg-red-500"
                              : passwordStrength.score <= 4
                                ? "bg-yellow-500"
                                : "bg-green-500"
                            : "bg-gray-200"
                        }`}
                      />
                    ))}
                  </div>
                </div>
                <ul className="text-xs text-gray-600 space-y-1">
                  {passwordStrength.requirements.map((req, index) => (
                    <li key={index} className="flex items-center gap-2">
                      {req.met ? (
                        <Check className="h-3 w-3 text-green-500" />
                      ) : (
                        <X className="h-3 w-3 text-red-500" />
                      )}
                      <span
                        className={req.met ? "text-green-700" : "text-red-700"}
                      >
                        {req.text}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
            {errors.newPassword && (
              <p className="mt-1 text-xs text-red-600">{errors.newPassword}</p>
            )}
          </div>

          <div>
            <label
              htmlFor="confirm-password"
              className="flex text-sm font-medium text-gray-700 mb-1.5"
            >
              Confirm new password
              <span className="text-red-500 ml-0.5">*</span>
            </label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
              <Input
                id="confirm-password"
                type="password"
                value={passwordForm.confirmPassword}
                onChange={(e) =>
                  setPasswordForm((prev) => ({
                    ...prev,
                    confirmPassword: e.target.value,
                  }))
                }
                className={`pl-10 w-full p-2.5 text-sm border rounded-lg bg-white ${
                  errors.confirmPassword
                    ? "border-red-300 focus:border-red-500"
                    : "border-gray-200"
                }`}
                placeholder="Confirm new password"
              />
            </div>
            {errors.confirmPassword && (
              <p className="mt-1 text-xs text-red-600">
                {errors.confirmPassword}
              </p>
            )}
          </div>
        </div>

        <div className="flex justify-end gap-3 pt-6 mt-6 border-t border-gray-200">
          <Button
            variant="outline"
            className="text-gray-700 text-sm"
            onClick={() => {
              setPasswordForm({
                currentPassword: "",
                newPassword: "",
                confirmPassword: "",
              });
              setErrors({});
            }}
            disabled={saving === "password"}
          >
            Discard
          </Button>
          <Button
            className="bg-primary text-white text-sm"
            onClick={handlePasswordChange}
            disabled={saving === "password" || passwordStrength.score < 5}
          >
            {saving === "password" ? (
              <Loader2 size={16} className="animate-spin mr-2" />
            ) : null}
            {saving === "password" ? "Updating..." : "Update Password"}
          </Button>
        </div>
      </motion.div>
    </AnimatedCard>
  );
}

interface AuthenticationTabProps {
  securitySettings: SecuritySettings;
  setSecuritySettings: (settings: SecuritySettings) => void;
  saving: string | null;
  setSaving: (value: string | null) => void;
  errors: Record<string, string>;
  setErrors: (errors: Record<string, string>) => void;
}

function AuthenticationTab({
  securitySettings,
  setSecuritySettings,
  saving,
  setSaving,
  errors,
  setErrors,
}: AuthenticationTabProps) {
  const handleToggle2FA = async (enabled: boolean) => {
    setSaving("2fa");
    try {
      // In a real implementation, this would involve setting up TOTP or SMS
      setSecuritySettings({
        ...securitySettings,
        twoFactorEnabled: enabled,
      });
      setErrors({});
    } catch (error) {
      console.error("Error toggling 2FA:", error);
      setErrors({ "2fa": "Failed to update two-factor authentication" });
    } finally {
      setSaving(null);
    }
  };

  return (
    <div className="space-y-6">
      <AnimatedCard>
        <motion.div
          variants={slideUpAnimation}
          initial="hidden"
          animate="visible"
          transition={springTransition}
        >
          <div className="flex justify-between items-start mb-6">
            <div className="flex gap-3 items-center">
              <div className="p-2 rounded-md bg-green-50">
                <Shield className="h-5 w-5 text-green-500" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900">
                  Two-Factor Authentication
                </h3>
                <p className="text-sm text-gray-500">
                  Add an extra layer of security to your account
                </p>
              </div>
            </div>
          </div>

          <div className="pt-4 border-t border-gray-200">
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex gap-3 items-center">
                <Switch
                  id="twoFactor"
                  checked={securitySettings.twoFactorEnabled}
                  onCheckedChange={handleToggle2FA}
                  disabled={saving === "2fa"}
                />
                <div>
                  <label
                    htmlFor="twoFactor"
                    className="text-sm font-medium text-gray-900"
                  >
                    Enable two factor authentication
                  </label>
                  <p className="text-xs text-gray-500 mt-1">
                    When enabled, you'll be required to enter a verification
                    code in addition to your password when signing in.
                  </p>
                </div>
              </div>
              {saving === "2fa" && (
                <Loader2 size={16} className="animate-spin text-gray-400" />
              )}
            </div>

            {securitySettings.twoFactorEnabled && (
              <div className="mt-6 space-y-4">
                <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-md bg-blue-50">
                      <Smartphone className="h-4 w-4 text-blue-500" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        Authenticator App
                      </p>
                      <p className="text-xs text-gray-500">
                        Use an authentication app to get verification codes
                      </p>
                    </div>
                  </div>
                  <ChevronRight className="h-5 w-5 text-gray-400" />
                </div>

                <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-md bg-purple-50">
                      <Mail className="h-4 w-4 text-purple-500" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        Email Authentication
                      </p>
                      <p className="text-xs text-gray-500">
                        Receive verification codes via email
                      </p>
                    </div>
                  </div>
                  <ChevronRight className="h-5 w-5 text-gray-400" />
                </div>

                <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-md bg-amber-50">
                      <Phone className="h-4 w-4 text-amber-500" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        SMS Authentication
                      </p>
                      <p className="text-xs text-gray-500">
                        Receive verification codes via SMS
                      </p>
                    </div>
                  </div>
                  <ChevronRight className="h-5 w-5 text-gray-400" />
                </div>
              </div>
            )}
          </div>
        </motion.div>
      </AnimatedCard>

      <AnimatedCard>
        <motion.div
          variants={slideUpAnimation}
          initial="hidden"
          animate="visible"
          transition={springTransition}
        >
          <div className="flex justify-between items-start mb-6">
            <div className="flex gap-3 items-center">
              <div className="p-2 rounded-md bg-red-50">
                <AlertTriangle className="h-5 w-5 text-red-500" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900">
                  Account Recovery
                </h3>
                <p className="text-sm text-gray-500">
                  Set up recovery methods for your account
                </p>
              </div>
            </div>
          </div>

          <div className="pt-4 border-t border-gray-200 space-y-4">
            <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-md bg-blue-50">
                  <Mail className="h-4 w-4 text-blue-500" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    Recovery Email
                  </p>
                  <p className="text-xs text-gray-500">
                    <EMAIL>
                  </p>
                </div>
              </div>
              <button className="text-primary hover:text-primary/80 text-sm">
                Change
              </button>
            </div>

            <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-md bg-green-50">
                  <Phone className="h-4 w-4 text-green-500" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    Recovery Phone
                  </p>
                  <p className="text-xs text-gray-500">+255 *** **** 1234</p>
                </div>
              </div>
              <button className="text-primary hover:text-primary/80 text-sm">
                Change
              </button>
            </div>
          </div>
        </motion.div>
      </AnimatedCard>
    </div>
  );
}

interface SessionsTabProps {
  userSessions: UserSession[];
  securityLogs: Log[];
  userAccount: AccountWithUserAndRole | null;
  saving: string | null;
  setSaving: (value: string | null) => void;
}

function SessionsTab({
  userSessions,
  securityLogs,
  userAccount,
  saving,
  setSaving,
}: SessionsTabProps) {
  const handleLogoutSession = async (sessionId: string) => {
    setSaving(`session-${sessionId}`);
    try {
      // In a real implementation, you'd call an API to terminate the session
      await new Promise((resolve) => setTimeout(resolve, 1000)); // Simulate API call
      // Remove session from local state
      // setUserSessions(prev => prev.filter(s => s.id !== sessionId));
    } catch (error) {
      console.error("Error terminating session:", error);
    } finally {
      setSaving(null);
    }
  };

  const handleLogoutAllSessions = async () => {
    setSaving("all-sessions");
    try {
      // In a real implementation, you'd call an API to terminate all sessions
      await new Promise((resolve) => setTimeout(resolve, 1500)); // Simulate API call
    } catch (error) {
      console.error("Error terminating all sessions:", error);
    } finally {
      setSaving(null);
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 60) return `${diffMins} minutes ago`;
    if (diffHours < 24) return `${diffHours} hours ago`;
    return `${diffDays} days ago`;
  };

  return (
    <AnimatedCard>
      <motion.div
        variants={slideUpAnimation}
        initial="hidden"
        animate="visible"
        transition={springTransition}
      >
        <div className="flex justify-between items-start mb-6">
          <div className="flex gap-3 items-center">
            <div className="p-2 rounded-md bg-blue-50">
              <Smartphone className="h-5 w-5 text-blue-500" />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                Active Sessions
              </h3>
              <p className="text-sm text-gray-500">
                We'll alert you via{" "}
                <span className="font-medium">{userAccount?.email}</span> if
                there is any unusual activity
              </p>
            </div>
          </div>
          <Button
            variant="outline"
            onClick={handleLogoutAllSessions}
            disabled={saving === "all-sessions"}
            className="flex items-center text-sm text-red-600 hover:text-red-700 gap-1"
          >
            {saving === "all-sessions" ? (
              <Loader2 size={16} className="animate-spin mr-1" />
            ) : (
              <LogOut className="h-4 w-4 mr-1" />
            )}
            {saving === "all-sessions"
              ? "Logging out..."
              : "Log out of all sessions"}
          </Button>
        </div>

        <div className="space-y-4 pt-4 border-t border-gray-200">
          {userSessions.map((session) => (
            <div
              key={session.id}
              className={`p-4 border rounded-lg ${session.isCurrent ? "border-green-200 bg-green-50" : "border-gray-200 hover:bg-gray-50"}`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div
                    className={`p-2 rounded-md ${session.isCurrent ? "bg-green-100" : "bg-gray-100"}`}
                  >
                    <Smartphone
                      className={`h-4 w-4 ${session.isCurrent ? "text-green-600" : "text-gray-600"}`}
                    />
                  </div>
                  <div>
                    <div className="flex items-center gap-2">
                      <p className="text-sm font-medium text-gray-900">
                        {session.deviceName}
                      </p>
                      {session.isCurrent && (
                        <span className="px-1.5 py-0.5 text-xs rounded bg-green-100 text-green-800">
                          Current
                        </span>
                      )}
                    </div>
                    <div className="flex items-center gap-2 mt-1 text-xs text-gray-500">
                      <div className="flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        <span>{session.location}</span>
                      </div>
                      <span>•</span>
                      <span>{session.ipAddress}</span>
                      <span>•</span>
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span>{formatTimeAgo(session.lastActive)}</span>
                      </div>
                    </div>
                  </div>
                </div>
                {!session.isCurrent && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleLogoutSession(session.id)}
                    disabled={saving === `session-${session.id}`}
                    className="text-sm text-red-600 hover:text-red-700"
                  >
                    {saving === `session-${session.id}` ? (
                      <Loader2 size={14} className="animate-spin" />
                    ) : (
                      "Terminate"
                    )}
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>

        {securityLogs.length > 0 && (
          <div className="mt-8 pt-6 border-t border-gray-200">
            <h4 className="text-sm font-medium text-gray-900 mb-4">
              Recent Security Activity
            </h4>
            <div className="space-y-3">
              {securityLogs.slice(0, 5).map((log: any, index: number) => (
                <div
                  key={log.id}
                  className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg"
                >
                  <div className="p-1.5 rounded-full bg-gray-200">
                    <Shield className="h-3 w-3 text-gray-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-xs font-medium text-gray-900">
                      {log.event}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatTimeAgo(log.created_at)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </motion.div>
    </AnimatedCard>
  );
}
