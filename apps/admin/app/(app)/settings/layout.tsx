"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { Input } from "@workspace/ui/components/input";
import { Search } from "lucide-react";
import { cn } from "@workspace/ui/lib/utils";

interface SettingsLayoutProps {
  children: React.ReactNode;
}

const settingsNavItems = [
  { name: "Profile", href: "/settings/profile" },
  { name: "Security", href: "/settings/security" },
  { name: "Notifications", href: "/settings/notifications" },
  { name: "Company", href: "/settings/company" },
  { name: "System", href: "/settings/system" },
  { name: "About", href: "/settings/about" },
];

export default function SettingsLayout({ children }: SettingsLayoutProps) {
  const pathname = usePathname();
  const currentPath = pathname === "/settings" ? "/settings/profile" : pathname;

  return (
    <div className="p-6 md:p-10 max-w-6xl mx-auto">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-heading font-bold text-foreground">
          Settings
        </h1>
        <div className="relative w-full max-w-xs">
          <Search
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground-quaternary"
            size={18}
          />
          <Input
            type="search"
            placeholder="Search"
            className="pl-10 h-10 input-base"
          />
        </div>
      </div>
      <div className="flex flex-col md:flex-row gap-10">
        <nav className="flex flex-row md:flex-col md:w-48 flex-shrink-0 gap-1">
          {settingsNavItems.map((item) => (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                "px-4 py-2.5 text-sm font-medium rounded-lg transition-colors duration-150",
                currentPath === item.href
                  ? "bg-settings-active-nav-bg text-settings-active-nav-text"
                  : "text-foreground-secondary hover:bg-background-tertiary hover:text-foreground"
              )}
            >
              {item.name}
            </Link>
          ))}
        </nav>
        <div className="flex-1 min-w-0 bg-background p-6 md:p-8 rounded-lg shadow">
          {children}
        </div>
      </div>
    </div>
  );
}
