"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { 
  Building, 
  Mail, 
  Phone, 
  Globe, 
  MapPin,
  FileText,
  Loader2,
  RefreshCw,
  Upload,
  Camera
} from "lucide-react";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { AnimatedCard } from "@/components/animated-card";
import { PageTransition } from "@/components/page-transition";
import { useAppSelector } from "@/store/hooks";
import {
  userService,
  departmentService,
  type UserWithAccount,
  type Department
} from "@/lib/logistics";
import {
  slideUpAnimation,
  springTransition
} from "@/lib/motion-config";

interface CompanyProfile {
  name: string;
  industry: string;
  email: string;
  phone: string;
  address: string;
  website: string;
  taxId: string;
  description?: string;
  employeeCount?: string;
  foundedYear?: string;
  logo?: string;
}

const industryOptions = [
  "Logistics & Transportation",
  "Shipping & Maritime",
  "Supply Chain Management", 
  "Freight Forwarding",
  "Warehousing & Distribution",
  "Air Cargo",
  "Rail Transport",
  "Trucking & Road Transport",
  "Import/Export",
  "3PL Services"
];

const employeeCountOptions = [
  "1-10 employees",
  "11-50 employees", 
  "51-200 employees",
  "201-500 employees",
  "501-1000 employees",
  "1000+ employees"
];

export default function CompanyPage() {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [userProfile, setUserProfile] = useState<UserWithAccount | null>(null);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [companyProfile, setCompanyProfile] = useState<CompanyProfile>({
    name: '',
    industry: '',
    email: '',
    phone: '',
    address: '',
    website: '',
    taxId: '',
    description: '',
    employeeCount: '',
    foundedYear: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Get authenticated user
  const { user: authUser } = useAppSelector((state) => state.auth);

  // Fetch company data
  const fetchCompanyData = async (refresh = false) => {
    if (!authUser?.id) return;

    try {
      if (refresh) setRefreshing(true);
      else setLoading(true);

      // Fetch user profile
      const userResult = await userService.getUserWithAccount(authUser.id);
      if (userResult.success && userResult.data) {
        setUserProfile(userResult.data);
      }

      // Fetch departments
      const deptResult = await departmentService.getAll({ limit: 100 });
      if (deptResult.success) {
        setDepartments(deptResult.data);
      }

      // In a real implementation, you would fetch company profile from a company table
      // For now, we'll use default data that could be stored in user metadata or a company table
      setCompanyProfile({
        name: 'Shamwaa Logistics',
        industry: 'Logistics & Transportation',
        email: userResult.data?.accounts?.email || '',
        phone: userResult.data?.phone || '',
        address: userResult.data?.location || '123 Business Park, Road, Nairobi, Kenya',
        website: 'www.shamwaa.com',
        taxId: 'KE123456789',
        description: 'Leading logistics company providing comprehensive transportation and supply chain solutions across East Africa.',
        employeeCount: '51-200 employees',
        foundedYear: '2018'
      });

    } catch (error) {
      console.error('Error fetching company data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    if (authUser) {
      fetchCompanyData();
    }
  }, [authUser]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!companyProfile.name.trim()) {
      newErrors.name = 'Company name is required';
    }

    if (!companyProfile.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(companyProfile.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (companyProfile.website && !companyProfile.website.match(/^https?:\/\/.+/)) {
      if (!companyProfile.website.startsWith('www.') && !companyProfile.website.includes('.')) {
        newErrors.website = 'Please enter a valid website URL';
      }
    }

    if (companyProfile.foundedYear && (
      parseInt(companyProfile.foundedYear) < 1800 || 
      parseInt(companyProfile.foundedYear) > new Date().getFullYear()
    )) {
      newErrors.foundedYear = 'Please enter a valid year';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    setSaving(true);
    try {
      // In a real implementation, you would save company profile to database
      // This might involve:
      // 1. Creating/updating a company table record
      // 2. Updating user profile with company association
      // 3. Potentially updating user contact information
      
      // For now, we'll just simulate the API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Update user profile fields that overlap
      if (userProfile) {
        const userUpdateData: any = {};
        if (companyProfile.phone !== userProfile.phone) {
          userUpdateData.phone = companyProfile.phone;
        }
        if (companyProfile.address !== userProfile.location) {
          userUpdateData.location = companyProfile.address;
        }

        if (Object.keys(userUpdateData).length > 0) {
          await userService.updateProfile(userProfile.id, userUpdateData);
        }
      }

      setErrors({});
      // Success feedback could be added here
    } catch (error) {
      console.error('Error saving company profile:', error);
      setErrors({ general: 'Failed to save company profile' });
    } finally {
      setSaving(false);
    }
  };

  const handleDiscard = () => {
    // Reset to original values
    fetchCompanyData();
    setErrors({});
  };

  if (!authUser) {
    return (
      <PageTransition className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <h2 className="text-lg font-medium text-gray-900 mb-2">Authentication Required</h2>
            <p className="text-gray-500">Please sign in to access company settings.</p>
          </div>
        </div>
      </PageTransition>
    );
  }

  return (
    <div className="space-y-8">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex justify-between items-center"
      >
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Company</h2>
          <p className="text-sm text-gray-500 mt-1">
            Update company details here.
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => fetchCompanyData(true)}
            disabled={refreshing}
            className="flex items-center gap-1.5"
          >
            <RefreshCw size={16} className={refreshing ? "animate-spin" : ""} />
            Refresh
          </Button>
        </div>
      </motion.div>

      {errors.general && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-800 text-sm">
          {errors.general}
        </div>
      )}

      {loading ? (
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        </div>
      ) : (
        <AnimatedCard>
          <motion.div
            variants={slideUpAnimation}
            initial="hidden"
            animate="visible"
            transition={springTransition}
          >
            {/* Company Logo Section */}
            <div className="flex items-center gap-6 pt-6 border-t border-gray-200 mb-8">
              <div className="relative">
                <div className="flex-shrink-0 w-16 h-16 rounded-lg bg-gray-100 flex items-center justify-center">
                  {companyProfile.logo ? (
                    <img 
                      src={companyProfile.logo} 
                      alt="Company Logo" 
                      className="w-16 h-16 rounded-lg object-cover"
                    />
                  ) : (
                    <Building className="w-8 h-8 text-gray-400" />
                  )}
                </div>
                <button className="absolute -bottom-1 -right-1 p-1.5 bg-primary text-white rounded-full hover:bg-primary/90 transition-colors">
                  <Camera className="w-3 h-3" />
                </button>
              </div>
              <div className="flex-1">
                <h4 className="text-sm font-medium text-gray-900">Company Logo</h4>
                <p className="text-xs text-gray-500 mt-1">Upload your company logo (recommended size: 256x256px)</p>
              </div>
              <div className="flex items-center gap-3">
                <Button variant="outline" className="text-gray-700 text-sm">
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Logo
                </Button>
              </div>
            </div>

            <div className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="companyName" className="block text-sm font-medium text-gray-700 mb-1.5">
                    Company Name *
                  </label>
                  <div className="relative">
                    <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                    <Input
                      id="companyName"
                      value={companyProfile.name}
                      onChange={(e) => setCompanyProfile(prev => ({ ...prev, name: e.target.value }))}
                      className={`pl-10 w-full p-2.5 text-sm border rounded-lg bg-white ${
                        errors.name ? 'border-red-300 focus:border-red-500' : 'border-gray-200'
                      }`}
                      placeholder="Enter company name"
                    />
                  </div>
                  {errors.name && (
                    <p className="mt-1 text-xs text-red-600">{errors.name}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="industry" className="block text-sm font-medium text-gray-700 mb-1.5">
                    Industry *
                  </label>
                  <Select
                    value={companyProfile.industry}
                    onValueChange={(value) => setCompanyProfile(prev => ({ ...prev, industry: value }))}
                  >
                    <SelectTrigger className="w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white">
                      <SelectValue placeholder="Select industry" />
                    </SelectTrigger>
                    <SelectContent>
                      {industryOptions.map((industry) => (
                        <SelectItem key={industry} value={industry}>
                          {industry}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Contact Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1.5">
                    Email Address *
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                    <Input
                      id="email"
                      type="email"
                      value={companyProfile.email}
                      onChange={(e) => setCompanyProfile(prev => ({ ...prev, email: e.target.value }))}
                      className={`pl-10 w-full p-2.5 text-sm border rounded-lg bg-white ${
                        errors.email ? 'border-red-300 focus:border-red-500' : 'border-gray-200'
                      }`}
                      placeholder="Enter email address"
                    />
                  </div>
                  {errors.email && (
                    <p className="mt-1 text-xs text-red-600">{errors.email}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1.5">
                    Phone Number
                  </label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                    <Input
                      id="phone"
                      value={companyProfile.phone}
                      onChange={(e) => setCompanyProfile(prev => ({ ...prev, phone: e.target.value }))}
                      className="pl-10 w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white"
                      placeholder="Enter phone number"
                    />
                  </div>
                </div>
              </div>

              {/* Address and Website */}
              <div>
                <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1.5">
                  Address
                </label>
                <div className="relative">
                  <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                  <textarea
                    id="address"
                    rows={3}
                    value={companyProfile.address}
                    onChange={(e) => setCompanyProfile(prev => ({ ...prev, address: e.target.value }))}
                    placeholder="Enter company address"
                    className="pl-10 w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white resize-none"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-1.5">
                    Website
                  </label>
                  <div className="relative">
                    <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                    <Input
                      id="website"
                      value={companyProfile.website}
                      onChange={(e) => setCompanyProfile(prev => ({ ...prev, website: e.target.value }))}
                      className={`pl-10 w-full p-2.5 text-sm border rounded-lg bg-white ${
                        errors.website ? 'border-red-300 focus:border-red-500' : 'border-gray-200'
                      }`}
                      placeholder="www.company.com"
                    />
                  </div>
                  {errors.website && (
                    <p className="mt-1 text-xs text-red-600">{errors.website}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="taxId" className="block text-sm font-medium text-gray-700 mb-1.5">
                    Tax ID / VAT Number
                  </label>
                  <div className="relative">
                    <FileText className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                    <Input
                      id="taxId"
                      value={companyProfile.taxId}
                      onChange={(e) => setCompanyProfile(prev => ({ ...prev, taxId: e.target.value }))}
                      className="pl-10 w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white"
                      placeholder="Enter tax ID"
                    />
                  </div>
                </div>
              </div>

              {/* Additional Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="employeeCount" className="block text-sm font-medium text-gray-700 mb-1.5">
                    Company Size
                  </label>
                  <Select
                    value={companyProfile.employeeCount || ''}
                    onValueChange={(value) => setCompanyProfile(prev => ({ ...prev, employeeCount: value }))}
                  >
                    <SelectTrigger className="w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white">
                      <SelectValue placeholder="Select company size" />
                    </SelectTrigger>
                    <SelectContent>
                      {employeeCountOptions.map((option) => (
                        <SelectItem key={option} value={option}>
                          {option}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label htmlFor="foundedYear" className="block text-sm font-medium text-gray-700 mb-1.5">
                    Founded Year
                  </label>
                  <Input
                    id="foundedYear"
                    type="number"
                    min="1800"
                    max={new Date().getFullYear()}
                    value={companyProfile.foundedYear}
                    onChange={(e) => setCompanyProfile(prev => ({ ...prev, foundedYear: e.target.value }))}
                    className={`w-full p-2.5 text-sm border rounded-lg bg-white ${
                      errors.foundedYear ? 'border-red-300 focus:border-red-500' : 'border-gray-200'
                    }`}
                    placeholder="e.g. 2018"
                  />
                  {errors.foundedYear && (
                    <p className="mt-1 text-xs text-red-600">{errors.foundedYear}</p>
                  )}
                </div>
              </div>

              {/* Description */}
              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1.5">
                  Company Description
                </label>
                <textarea
                  id="description"
                  rows={4}
                  value={companyProfile.description}
                  onChange={(e) => setCompanyProfile(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Brief description of your company and services"
                  className="w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white resize-none"
                />
                <p className="text-xs text-gray-500 mt-1">
                  {companyProfile.description?.length || 0}/500 characters
                </p>
              </div>

              {/* Department Information */}
              <div className="border-t border-gray-200 pt-6">
                <h4 className="text-sm font-medium text-gray-900 mb-4">Department Structure</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {departments.length > 0 ? (
                    departments.map((dept, index) => (
                      <div key={dept.id} className="p-3 border border-gray-200 rounded-lg bg-gray-50">
                        <div className="flex items-center gap-2">
                          <Building className="h-4 w-4 text-gray-500" />
                          <span className="text-sm font-medium text-gray-900">{dept.name}</span>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">Active department</p>
                      </div>
                    ))
                  ) : (
                    <div className="col-span-3 text-center py-4">
                      <Building className="h-8 w-8 text-gray-300 mx-auto mb-2" />
                      <p className="text-sm text-gray-500">No departments configured</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-3 pt-8 border-t border-gray-200">
              <Button 
                variant="outline" 
                className="text-gray-700 text-sm"
                onClick={handleDiscard}
                disabled={saving}
              >
                Discard
              </Button>
              <Button 
                className="bg-primary text-white text-sm"
                onClick={handleSave}
                disabled={saving}
              >
                {saving ? <Loader2 size={16} className="animate-spin mr-2" /> : null}
                {saving ? 'Saving...' : 'Apply Changes'}
              </Button>
            </div>
          </motion.div>
        </AnimatedCard>
      )}
    </div>
  );
}
