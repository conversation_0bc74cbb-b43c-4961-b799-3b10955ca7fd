"use client";

import React from "react";
import { motion } from "framer-motion";
import {
  Package,
  Truck,
  Globe,
  Shield,
  Zap,
  Users,
  BarChart3,
  ExternalLink,
  Copyright,
  Code,
  Heart,
  Clock,
  Database,
  Cloud,
} from "lucide-react";
import { Badge } from "@workspace/ui/components/badge";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Separator } from "@workspace/ui/components/separator";

const About = () => {
  const year = new Date().getFullYear();

  const features = [
    {
      icon: Package,
      title: "Real-Time Cargo Tracking",
      description:
        "GPS-based monitoring with live dashboard updates and complete transparency throughout the shipping process.",
    },
    {
      icon: Shield,
      title: "Automated Cargo Handover",
      description:
        "Digital handover process using QR codes and biometric authentication for secure cargo transfers.",
    },
    {
      icon: BarChart3,
      title: "Finance & CRM Integration",
      description:
        "Synchronized business functions with automated financial tracking and real-time customer engagement.",
    },
    {
      icon: Database,
      title: "Efficient Data Management",
      description:
        "Centralized database system with automated data entry and comprehensive audit capabilities.",
    },
    {
      icon: Cloud,
      title: "Cloud-First Architecture",
      description:
        "High availability, automatic updates, and seamless remote access with disaster recovery.",
    },
    {
      icon: Users,
      title: "Role-Based Access Control",
      description:
        "Comprehensive permission system ensuring secure access to features based on user roles.",
    },
  ];

  const specifications = [
    { label: "Version", value: "0.0.1" },
    { label: "Architecture", value: "Progressive Web Application (PWA)" },
  ];

  return (
    <div className="min-h-screen">
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center space-y-4"
        >
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-primary rounded-xl">
              <Truck className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900">
              Shamwaa Logistics
            </h1>
          </div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            A comprehensive cloud-based logistics management system designed to
            streamline cargo operations, enhance transparency, and improve
            operational efficiency.
          </p>
          <div className="flex items-center justify-center gap-2">
            <Badge variant="secondary" className="gap-1">
              <Clock className="h-3 w-3" />
              Version 0.0.1
            </Badge>
            <Badge variant="outline" className="gap-1">
              <Globe className="h-3 w-3" />
              Progressive Web App
            </Badge>
          </div>
        </motion.div>

        {/* Main Features */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-primary" />
                Key Features & Capabilities
              </CardTitle>
              <CardDescription>
                Comprehensive logistics management with advanced automation and
                real-time tracking
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
                {features.map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: 0.1 * index }}
                    className="p-4 border rounded-lg hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start gap-3">
                      <div className="p-2 bg-primary/10 rounded-lg">
                        <feature.icon className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-2">
                          {feature.title}
                        </h3>
                        <p className="text-sm text-gray-600">
                          {feature.description}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Technical Specifications */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Code className="h-5 w-5 text-primary" />
                Technical Specifications
              </CardTitle>
              <CardDescription>
                Built with modern technologies for scalability and performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {specifications.map((spec, index) => (
                  <div
                    key={index}
                    className="flex justify-start gap-2 items-start py-2"
                  >
                    <span className="font-medium text-gray-700">
                      {spec.label}:
                    </span>
                    <span className="text-gray-600">{spec.value}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Credits & Copyright */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="grid grid-cols-1 md:grid-cols-2 gap-6"
        >
          {/* Credits */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Heart className="h-5 w-5 text-red-500" />
                Credits
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center space-y-3">
                <p className="text-gray-600">Built with passion by</p>
                <Button
                  variant="outline"
                  className="gap-2"
                  onClick={() => window.open("https://underscor.io", "_blank")}
                >
                  <ExternalLink className="h-4 w-4" />
                  underscor.io
                </Button>
                <p className="text-sm text-gray-500">
                  Professional software development and digital solutions
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Copyright */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Copyright className="h-5 w-5 text-primary" />
                Copyright & Legal
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-start gap-2">
                  <Copyright className="h-4 w-4 text-gray-500 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {year} Shamwaa Logistics Management System
                    </p>
                    <p className="text-xs text-gray-600">
                      All rights reserved under company acts of rights
                    </p>
                  </div>
                </div>
                <Separator />
                <p className="text-xs text-gray-500">
                  This software and its documentation are protected by copyright
                  law. Unauthorized reproduction or distribution is prohibited.
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Footer */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center py-6"
        >
          <p className="text-sm text-gray-500">
            Empowering logistics operations with cutting-edge technology
          </p>
        </motion.div>
      </div>
    </div>
  );
};

export default About;
