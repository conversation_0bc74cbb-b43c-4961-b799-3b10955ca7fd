"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { 
  Bell, 
  Mail, 
  Smartphone, 
  Settings as SettingsIcon,
  Loader2,
  RefreshCw,
  Check,
  X
} from "lucide-react";
import { Button } from "@workspace/ui/components/button";
import { Switch } from "@workspace/ui/components/switch";
import { AnimatedCard } from "@/components/animated-card";
import { PageTransition } from "@/components/page-transition";
import { useAppSelector } from "@/store/hooks";
import {
  userService,
  type UserWithAccount
} from "@/lib/logistics";
import {
  slideUpAnimation,
  springTransition
} from "@/lib/motion-config";

interface NotificationSettings {
  // Email notifications
  cargoStatusEmail: boolean;
  invoiceNotificationsEmail: boolean;
  customerUpdatesEmail: boolean;
  systemAlertsEmail: boolean;
  securityAlertsEmail: boolean;
  marketingEmail: boolean;
  
  // Browser notifications  
  cargoStatusBrowser: boolean;
  invoiceNotificationsBrowser: boolean;
  customerUpdatesBrowser: boolean;
  systemAlertsBrowser: boolean;
  securityAlertsBrowser: boolean;
  
  // SMS notifications
  urgentAlertsSms: boolean;
  securityAlertsSms: boolean;
  
  // General settings
  notificationFrequency: 'immediate' | 'hourly' | 'daily' | 'weekly';
  quietHours: boolean;
  quietHoursStart: string;
  quietHoursEnd: string;
}

interface NotificationCategory {
  id: string;
  title: string;
  description: string;
  emailKey: keyof NotificationSettings;
  browserKey?: keyof NotificationSettings;
  smsKey?: keyof NotificationSettings;
  icon: React.ElementType;
  color: string;
}

const notificationCategories: NotificationCategory[] = [
  {
    id: 'cargo-status',
    title: 'Cargo Status Updates',
    description: 'Receive notifications when cargo status changes',
    emailKey: 'cargoStatusEmail',
    browserKey: 'cargoStatusBrowser',
    icon: Bell,
    color: 'blue'
  },
  {
    id: 'invoices',
    title: 'Invoice Notifications',
    description: 'Receive notifications about invoices and payments',
    emailKey: 'invoiceNotificationsEmail',
    browserKey: 'invoiceNotificationsBrowser',
    icon: Mail,
    color: 'green'
  },
  {
    id: 'customers',
    title: 'Customer Updates',
    description: 'Receive notifications about customer activities',
    emailKey: 'customerUpdatesEmail',
    browserKey: 'customerUpdatesBrowser',
    icon: Bell,
    color: 'purple'
  },
  {
    id: 'system',
    title: 'System Alerts',
    description: 'Important system notifications and updates',
    emailKey: 'systemAlertsEmail',
    browserKey: 'systemAlertsBrowser',
    icon: SettingsIcon,
    color: 'orange'
  },
  {
    id: 'security',
    title: 'Security Alerts',
    description: 'Critical security notifications and login alerts',
    emailKey: 'securityAlertsEmail',
    browserKey: 'securityAlertsBrowser',
    smsKey: 'securityAlertsSms',
    icon: Bell,
    color: 'red'
  }
];

export default function NotificationsSettingsPage() {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [userProfile, setUserProfile] = useState<UserWithAccount | null>(null);
  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    // Email notifications - enabled by default
    cargoStatusEmail: true,
    invoiceNotificationsEmail: true,
    customerUpdatesEmail: true,
    systemAlertsEmail: true,
    securityAlertsEmail: true,
    marketingEmail: false,
    
    // Browser notifications - disabled by default
    cargoStatusBrowser: false,
    invoiceNotificationsBrowser: false,
    customerUpdatesBrowser: false,
    systemAlertsBrowser: false,
    securityAlertsBrowser: true,
    
    // SMS notifications - only for urgent items
    urgentAlertsSms: false,
    securityAlertsSms: false,
    
    // General settings
    notificationFrequency: 'immediate',
    quietHours: false,
    quietHoursStart: '22:00',
    quietHoursEnd: '07:00'
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Get authenticated user
  const { user: authUser } = useAppSelector((state) => state.auth);

  // Fetch notification settings
  const fetchNotificationSettings = async (refresh = false) => {
    if (!authUser?.id) return;

    try {
      if (refresh) setRefreshing(true);
      else setLoading(true);

      // Fetch user profile (notification preferences would be stored here or in a separate table)
      const userResult = await userService.getUserWithAccount(authUser.id);
      if (userResult.success && userResult.data) {
        setUserProfile(userResult.data);
        
        // In a real implementation, you would fetch notification preferences from a user_preferences table
        // For now, we'll use the default settings with some customization based on user data
        
        // You could store preferences in user metadata or a separate preferences table
        // const preferences = userResult.data.preferences || {};
        // setNotificationSettings(prev => ({ ...prev, ...preferences }));
      }
    } catch (error) {
      console.error('Error fetching notification settings:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    if (authUser) {
      fetchNotificationSettings();
    }
  }, [authUser]);

  const handleToggle = async (key: keyof NotificationSettings, value: boolean) => {
    setSaving(key);
    try {
      // Update local state immediately for better UX
      setNotificationSettings(prev => ({ ...prev, [key]: value }));
      
      // In a real implementation, you would save to database here
      // await userService.updateNotificationPreferences(authUser.id, { [key]: value });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setErrors({});
    } catch (error) {
      console.error('Error updating notification setting:', error);
      setErrors({ [key]: 'Failed to update notification setting' });
      // Revert on error
      setNotificationSettings(prev => ({ ...prev, [key]: !value }));
    } finally {
      setSaving(null);
    }
  };

  const handleBulkSave = async () => {
    if (!authUser) return;

    setSaving('bulk');
    try {
      // In a real implementation, you would save all preferences to database
      // await userService.updateNotificationPreferences(authUser.id, notificationSettings);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setErrors({});
      // Success feedback could be added here
    } catch (error) {
      console.error('Error saving notification settings:', error);
      setErrors({ general: 'Failed to save notification settings' });
    } finally {
      setSaving(null);
    }
  };

  if (!authUser) {
    return (
      <PageTransition className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <h2 className="text-lg font-medium text-gray-900 mb-2">Authentication Required</h2>
            <p className="text-gray-500">Please sign in to access notification settings.</p>
          </div>
        </div>
      </PageTransition>
    );
  }

  return (
    <div className="space-y-6">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex justify-between items-center"
      >
        <div>
          <h2 className="text-xl font-semibold text-gray-900">
            Notification Settings
          </h2>
          <p className="text-sm text-gray-500 mt-1">
            We may still send you important notifications about your account
            outside of your notification settings.
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => fetchNotificationSettings(true)}
            disabled={refreshing}
            className="flex items-center gap-1.5"
          >
            <RefreshCw size={16} className={refreshing ? "animate-spin" : ""} />
            Refresh
          </Button>
        </div>
      </motion.div>

      {errors.general && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-800 text-sm">
          {errors.general}
        </div>
      )}

      {loading ? (
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        </div>
      ) : (
        <div className="space-y-6">
          {/* Notification Categories */}
          <AnimatedCard>
            <motion.div
              variants={slideUpAnimation}
              initial="hidden"
              animate="visible"
              transition={springTransition}
            >
              <div className="flex justify-between items-center mb-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Notification Preferences</h3>
                  <p className="text-sm text-gray-500">Choose how you want to be notified</p>
                </div>
              </div>

              <div className="space-y-1">
                {/* Header */}
                <div className="grid grid-cols-1 md:grid-cols-12 gap-4 p-4 border-b border-gray-200 bg-gray-50 rounded-t-lg">
                  <div className="md:col-span-6">
                    <span className="text-sm font-medium text-gray-700">Notification Type</span>
                  </div>
                  <div className="md:col-span-2 text-center">
                    <div className="flex items-center justify-center gap-1">
                      <Mail className="h-4 w-4 text-gray-500" />
                      <span className="text-sm font-medium text-gray-700">Email</span>
                    </div>
                  </div>
                  <div className="md:col-span-2 text-center">
                    <div className="flex items-center justify-center gap-1">
                      <Bell className="h-4 w-4 text-gray-500" />
                      <span className="text-sm font-medium text-gray-700">Browser</span>
                    </div>
                  </div>
                  <div className="md:col-span-2 text-center">
                    <div className="flex items-center justify-center gap-1">
                      <Smartphone className="h-4 w-4 text-gray-500" />
                      <span className="text-sm font-medium text-gray-700">SMS</span>
                    </div>
                  </div>
                </div>

                {/* Notification rows */}
                {notificationCategories.map((category, index) => (
                  <NotificationRow
                    key={category.id}
                    category={category}
                    settings={notificationSettings}
                    onToggle={handleToggle}
                    saving={saving}
                    errors={errors}
                    index={index}
                  />
                ))}
              </div>
            </motion.div>
          </AnimatedCard>

          {/* Additional Settings */}
          <AnimatedCard>
            <motion.div
              variants={slideUpAnimation}
              initial="hidden"
              animate="visible"
              transition={springTransition}
            >
              <div className="flex justify-between items-center mb-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Additional Settings</h3>
                  <p className="text-sm text-gray-500">Configure additional notification preferences</p>
                </div>
              </div>

              <div className="space-y-6 border-t border-gray-200 pt-6">
                {/* Marketing emails */}
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900">Marketing Emails</p>
                    <p className="text-xs text-gray-500">Receive updates about new features and products</p>
                  </div>
                  <Switch
                    checked={notificationSettings.marketingEmail}
                    onCheckedChange={(value) => handleToggle('marketingEmail', value)}
                    disabled={saving === 'marketingEmail'}
                  />
                </div>

                {/* Quiet hours */}
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900">Quiet Hours</p>
                    <p className="text-xs text-gray-500">Reduce notifications during specified hours</p>
                  </div>
                  <Switch
                    checked={notificationSettings.quietHours}
                    onCheckedChange={(value) => handleToggle('quietHours', value)}
                    disabled={saving === 'quietHours'}
                  />
                </div>

                {notificationSettings.quietHours && (
                  <div className="ml-4 p-4 bg-gray-50 rounded-lg border">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Start Time
                        </label>
                        <input
                          type="time"
                          value={notificationSettings.quietHoursStart}
                          onChange={(e) => setNotificationSettings(prev => ({ 
                            ...prev, 
                            quietHoursStart: e.target.value 
                          }))}
                          className="w-full p-2 text-sm border border-gray-200 rounded-lg"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          End Time
                        </label>
                        <input
                          type="time"
                          value={notificationSettings.quietHoursEnd}
                          onChange={(e) => setNotificationSettings(prev => ({ 
                            ...prev, 
                            quietHoursEnd: e.target.value 
                          }))}
                          className="w-full p-2 text-sm border border-gray-200 rounded-lg"
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div className="flex justify-end gap-3 pt-6 mt-6 border-t border-gray-200">
                <Button variant="outline" className="text-gray-700 text-sm">
                  Reset to Defaults
                </Button>
                <Button 
                  className="bg-primary text-white text-sm"
                  onClick={handleBulkSave}
                  disabled={saving === 'bulk'}
                >
                  {saving === 'bulk' ? <Loader2 size={16} className="animate-spin mr-2" /> : null}
                  {saving === 'bulk' ? 'Saving...' : 'Save All Changes'}
                </Button>
              </div>
            </motion.div>
          </AnimatedCard>
        </div>
      )}
    </div>
  );
}

interface NotificationRowProps {
  category: NotificationCategory;
  settings: NotificationSettings;
  onToggle: (key: keyof NotificationSettings, value: boolean) => void;
  saving: string | null;
  errors: Record<string, string>;
  index: number;
}

function NotificationRow({ 
  category, 
  settings, 
  onToggle, 
  saving, 
  errors, 
  index 
}: NotificationRowProps) {
  const IconComponent = category.icon;
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.05 }}
      className="grid grid-cols-1 md:grid-cols-12 gap-4 p-4 border-b border-gray-100 hover:bg-gray-50 transition-colors"
    >
      <div className="md:col-span-6 flex items-center gap-3">
        <div className={`p-2 rounded-md bg-${category.color}-50`}>
          <IconComponent className={`h-4 w-4 text-${category.color}-500`} />
        </div>
        <div>
          <p className="text-sm font-medium text-gray-900">{category.title}</p>
          <p className="text-xs text-gray-500">{category.description}</p>
          {errors[category.emailKey] && (
            <p className="text-xs text-red-600 mt-1">{errors[category.emailKey]}</p>
          )}
        </div>
      </div>

      {/* Email toggle */}
      <div className="md:col-span-2 flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Switch
            checked={settings[category.emailKey] as boolean}
            onCheckedChange={(value) => onToggle(category.emailKey, value)}
            disabled={saving === category.emailKey}
          />
          {saving === category.emailKey && <Loader2 size={14} className="animate-spin text-gray-400" />}
        </div>
      </div>

      {/* Browser toggle */}
      <div className="md:col-span-2 flex items-center justify-center">
        {category.browserKey ? (
          <div className="flex items-center gap-2">
            <Switch
              checked={settings[category.browserKey] as boolean}
              onCheckedChange={(value) => onToggle(category.browserKey!, value)}
              disabled={saving === category.browserKey}
            />
            {saving === category.browserKey && <Loader2 size={14} className="animate-spin text-gray-400" />}
          </div>
        ) : (
          <span className="text-xs text-gray-400">N/A</span>
        )}
      </div>

      {/* SMS toggle */}
      <div className="md:col-span-2 flex items-center justify-center">
        {category.smsKey ? (
          <div className="flex items-center gap-2">
            <Switch
              checked={settings[category.smsKey] as boolean}
              onCheckedChange={(value) => onToggle(category.smsKey!, value)}
              disabled={saving === category.smsKey}
            />
            {saving === category.smsKey && <Loader2 size={14} className="animate-spin text-gray-400" />}
          </div>
        ) : (
          <span className="text-xs text-gray-400">N/A</span>
        )}
      </div>
    </motion.div>
  );
}
