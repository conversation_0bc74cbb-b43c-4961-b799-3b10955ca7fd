"use client";

import { memo } from "react";
import { Building, UserCheck, Truck, Package, Calendar } from "lucide-react";
import { Listing } from "@/modules/listing";

interface Statistic {
  title: string;
  value: number;
  change: string;
  trend: "up" | "down" | "neutral";
  icon: string;
}

interface CustomerRelationsStatisticsProps {
  statistics: Statistic[];
  loading: boolean;
}

/**
 * Customer Relations Statistics Component
 *
 * Displays key metrics in a 5-column grid using StatCards.
 * Follows the cargo-management statistics pattern for consistency.
 */
export const CustomerRelationsStatistics =
  memo<CustomerRelationsStatisticsProps>(({ statistics, loading }) => {
    const getIcon = (iconName: string) => {
      const iconMap = {
        users: Building,
        "user-check": UserCheck,
        truck: Truck,
        package: Package,
        calendar: Calendar,
      };

      return iconMap[iconName as keyof typeof iconMap] || Building;
    };

    const getStatCardColor = (
      iconName: string
    ): "primary" | "blue" | "green" | "amber" | "red" | "purple" => {
      const colorMap = {
        users: "primary" as const,
        "user-check": "green" as const,
        truck: "blue" as const,
        package: "purple" as const,
        calendar: "amber" as const,
      };

      return colorMap[iconName as keyof typeof colorMap] || "primary";
    };

    return statistics.map((stat, index) => {
      const Icon = getIcon(stat.icon);
      const color = getStatCardColor(stat.icon);

      return (
        <Listing.StatCard
          key={index}
          icon={Icon}
          name={stat.title}
          value={stat.value}
          caption={stat.change}
          color={color}
          loading={loading}
        />
      );
    });
  });

CustomerRelationsStatistics.displayName = "CustomerRelationsStatistics";
