"use client";

import { memo, useState } from "react";
import {
  Mail,
  Phone,
  MapPin,
  User,
  Edit,
  Trash2,
  UserPlus,
  Search,
} from "lucide-react";
import { Listing } from "@/modules/listing";
import { Button } from "@workspace/ui/components/button";
import {
  ProtectedCreateButton,
  ProtectedEditButton,
  ProtectedDeleteButton,
} from "@/lib/components/RBACWrapper";
import type { Customer } from "../../types";
import { transformCustomerForDisplay } from "../../types";

interface CustomersTabProps {
  customers: Customer[];
  loading: boolean;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  clientFilter: string;
  setClientFilter: (filter: string) => void;
  viewMode: "cards" | "table";
  onViewModeChange: (mode: "cards" | "table") => void;
  onRefresh: () => void;
  onAddCustomer: () => void;
  onEditCustomer: (customer: Customer) => void;
  onDeleteCustomer: (customer: Customer) => void;
}

/**
 * Customers Tab Component
 *
 * Displays the customer directory with search, filtering, and CRUD operations.
 * Uses the Listing.Table pattern for consistency.
 */
export const CustomersTab = memo<CustomersTabProps>(
  ({
    customers,
    loading,
    searchTerm,
    setSearchTerm,
    clientFilter,
    setClientFilter,
    viewMode,
    onViewModeChange,
    onRefresh,
    onAddCustomer,
    onEditCustomer,
    onDeleteCustomer,
  }) => {
    // Checkbox selection state
    const [selectedCustomers, setSelectedCustomers] = useState<Set<string>>(
      new Set()
    );

    // Pagination state
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 10;

    // Filter customers based on filter and search term
    const filteredCustomers = customers.filter((customer) => {
      const matchesFilter =
        clientFilter === "all" ||
        (customer.status &&
          customer.status.toLowerCase() === clientFilter.toLowerCase());
      const matchesSearch =
        !searchTerm ||
        customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (customer.code &&
          customer.code.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (customer.email &&
          customer.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (customer.phone &&
          customer.phone.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (customer.location &&
          customer.location.toLowerCase().includes(searchTerm.toLowerCase()));

      return matchesFilter && matchesSearch;
    });

    // Define table columns for Listing.Table
    const columns = [
      {
        key: "customer",
        label: "Customer",
        render: (customer: Customer) => {
          const displayCustomer = transformCustomerForDisplay(customer);
          return (
            <div className="flex items-center gap-3">
              <div className="bg-primary/10 p-2 rounded-md">
                <User className="h-4 w-4 text-primary" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">
                  {displayCustomer.name}
                </p>
                <p className="text-xs text-gray-500">
                  ID: {displayCustomer.code.slice(0, 8)}...
                </p>
              </div>
            </div>
          );
        },
      },
      {
        key: "code",
        label: "Code",
        render: (customer: Customer) => {
          const displayCustomer = transformCustomerForDisplay(customer);
          return (
            <span className="text-sm font-mono text-gray-700 bg-gray-100 px-2 py-1 rounded text-xs">
              {displayCustomer.code}
            </span>
          );
        },
      },
      {
        key: "contact",
        label: "Contact",
        render: (customer: Customer) => {
          const displayCustomer = transformCustomerForDisplay(customer);
          return (
            <div className="space-y-1">
              <div className="flex items-center gap-1">
                <Mail className="h-3 w-3 text-gray-400" />
                <span className="text-xs text-gray-600">
                  {displayCustomer.email}
                </span>
              </div>
              {displayCustomer.phone !== "No phone" && (
                <div className="flex items-center gap-1">
                  <Phone className="h-3 w-3 text-gray-400" />
                  <span className="text-xs text-gray-600">
                    {displayCustomer.phone}
                  </span>
                </div>
              )}
            </div>
          );
        },
      },
      {
        key: "location",
        label: "Location",
        render: (customer: Customer) => {
          const displayCustomer = transformCustomerForDisplay(customer);
          return (
            <div className="flex items-center gap-1">
              <MapPin className="h-3 w-3 text-gray-400" />
              <span className="text-sm text-gray-700">
                {displayCustomer.location}
              </span>
            </div>
          );
        },
      },
      {
        key: "status",
        label: "Status",
        className: "text-center",
        render: (customer: Customer) => {
          const displayCustomer = transformCustomerForDisplay(customer);
          return (
            <span
              className={`inline-flex items-center px-2 py-1 text-xs rounded-full ${
                displayCustomer.status === "ACTIVE"
                  ? "bg-green-100 text-green-800"
                  : displayCustomer.status === "INACTIVE"
                    ? "bg-red-100 text-red-800"
                    : "bg-gray-100 text-gray-800"
              }`}
            >
              {displayCustomer.status}
            </span>
          );
        },
      },
      {
        key: "created",
        label: "Created",
        className: "text-right",
        render: (customer: Customer) => {
          const displayCustomer = transformCustomerForDisplay(customer);
          return (
            <span className="text-sm text-gray-500">
              {displayCustomer.created_at
                ? new Date(displayCustomer.created_at).toLocaleDateString()
                : "Unknown"}
            </span>
          );
        },
      },
      {
        key: "actions",
        label: "",
        className: "text-right w-[50px]",
        render: (customer: Customer) => (
          <div className="flex gap-1">
            <ProtectedEditButton entity="customers">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onEditCustomer(customer);
                }}
                className="p-1 text-gray-400 hover:text-gray-600"
                title="Edit"
              >
                <Edit size={16} />
              </button>
            </ProtectedEditButton>
            <ProtectedDeleteButton entity="customers">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteCustomer(customer);
                }}
                className="p-1 text-gray-400 hover:text-gray-600"
                title="Delete"
              >
                <Trash2 size={16} />
              </button>
            </ProtectedDeleteButton>
          </div>
        ),
      },
    ];

    return (
      <Listing className="pt-4">
        <Listing.Filters
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          onRefresh={onRefresh}
          loading={loading}
        />

        <Listing.Controls
          entity="customers"
          length={filteredCustomers.length}
          viewMode={viewMode}
          onViewModeChange={onViewModeChange}
          categoryFilter={clientFilter}
          onCategoryFilterChange={setClientFilter}
          categories={[
            { key: "active", label: "Active" },
            { key: "inactive", label: "Inactive" },
          ]}
          actions={
            <div className="flex items-center gap-2">
              <ProtectedCreateButton entity="customers">
                <Button onClick={onAddCustomer}>
                  <UserPlus size={16} />
                  Add Customer
                </Button>
              </ProtectedCreateButton>
            </div>
          }
        />

        {viewMode === "cards" ? (
          <Listing.Cards
            data={filteredCustomers}
            loading={loading}
            columns="grid-cols-3"
            renderCard={(customer: Customer) => (
              <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow">
                {/* Header */}
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <div className="bg-primary/10 p-2 rounded-md">
                      <User className="h-4 w-4 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">
                        {transformCustomerForDisplay(customer).name}
                      </h3>
                      <p className="text-xs text-gray-500">
                        ID:{" "}
                        {transformCustomerForDisplay(customer).code.slice(0, 8)}
                        ...
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <ProtectedEditButton entity="customers">
                      <Edit
                        size={16}
                        color="gray"
                        onClick={(e) => {
                          e.stopPropagation();
                          onEditCustomer(customer);
                        }}
                      />
                    </ProtectedEditButton>
                    <ProtectedDeleteButton entity="customers">
                      <Trash2
                        size={16}
                        color="gray"
                        onClick={(e) => {
                          e.stopPropagation();
                          onDeleteCustomer(customer);
                        }}
                      />
                    </ProtectedDeleteButton>
                  </div>
                </div>

                {/* Status */}
                <div className="mb-3">
                  <span
                    className={`inline-flex items-center px-2 py-1 text-xs rounded-full ${
                      transformCustomerForDisplay(customer).status === "ACTIVE"
                        ? "bg-green-100 text-green-800"
                        : transformCustomerForDisplay(customer).status ===
                            "INACTIVE"
                          ? "bg-red-100 text-red-800"
                          : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {transformCustomerForDisplay(customer).status}
                  </span>
                </div>

                {/* Contact Info */}
                <div className="space-y-2 mb-3">
                  <div className="flex items-center gap-1">
                    <Mail className="h-3 w-3 text-gray-400" />
                    <span className="text-xs text-gray-600">
                      {transformCustomerForDisplay(customer).email}
                    </span>
                  </div>
                  {transformCustomerForDisplay(customer).phone !==
                    "No phone" && (
                    <div className="flex items-center gap-1">
                      <Phone className="h-3 w-3 text-gray-400" />
                      <span className="text-xs text-gray-600">
                        {transformCustomerForDisplay(customer).phone}
                      </span>
                    </div>
                  )}
                  <div className="flex items-center gap-1">
                    <MapPin className="h-3 w-3 text-gray-400" />
                    <span className="text-xs text-gray-600">
                      {transformCustomerForDisplay(customer).location}
                    </span>
                  </div>
                </div>

                {/* Created Date */}
                <div className="text-xs text-gray-500">
                  Created:{" "}
                  {transformCustomerForDisplay(customer).created_at
                    ? new Date(
                        transformCustomerForDisplay(customer).created_at
                      ).toLocaleDateString()
                    : "Unknown"}
                </div>
              </div>
            )}
            emptyState={
              <div className="py-12 text-center">
                <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
                  <Search className="h-6 w-6 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-1">
                  No customers found
                </h3>
                <p className="text-sm text-gray-500">
                  {searchTerm
                    ? "No customers match your search criteria."
                    : "No customers available."}
                </p>
              </div>
            }
          />
        ) : (
          <Listing.Table
            data={filteredCustomers}
            columns={columns}
            loading={loading}
            enableCheckboxes={true}
            selectedRowIds={Array.from(selectedCustomers)}
            onSelectionChange={(selectedIds) =>
              setSelectedCustomers(new Set(selectedIds))
            }
            getRowId={(item) => item.id}
            pagination={{
              currentPage,
              totalPages: Math.ceil(filteredCustomers.length / itemsPerPage),
              totalItems: filteredCustomers.length,
              itemsPerPage,
              onPageChange: setCurrentPage,
            }}
            emptyState={
              <div className="py-12 text-center">
                <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
                  <Search className="h-6 w-6 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-1">
                  No customers found
                </h3>
                <p className="text-sm text-gray-500">
                  {searchTerm
                    ? "No customers match your search criteria."
                    : "No customers available."}
                </p>
              </div>
            }
          />
        )}
      </Listing>
    );
  }
);

CustomersTab.displayName = "CustomersTab";
