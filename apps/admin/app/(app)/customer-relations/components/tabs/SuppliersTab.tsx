"use client";

import { memo, useState } from "react";
import {
  Search,
  UserPlus,
  Phone,
  MapPin,
  FileText,
  Edit,
  Trash2,
} from "lucide-react";
import { Listing } from "@/modules/listing";
import { Button } from "@workspace/ui/components/button";
import {
  ProtectedEditButton,
  ProtectedDeleteButton,
  ProtectedCreateButton,
} from "@/lib/components/RBACWrapper";
import type { Supplier } from "../../types";
import { transformSupplierForDisplay } from "../../types";

interface SuppliersTabProps {
  suppliers: Supplier[];
  loading: boolean;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  supplierFilter: string;
  setSupplierFilter: (filter: string) => void;
  viewMode: "cards" | "table";
  onViewModeChange: (mode: "cards" | "table") => void;
  onRefresh: () => void;
  onAddSupplier: () => void;
  onEditSupplier: (supplier: Supplier) => void;
  onDeleteSupplier: (supplier: Supplier) => void;
}

/**
 * Suppliers Tab Component
 *
 * Displays the supplier directory with search, filtering, and CRUD operations.
 * Uses the Listing.Table pattern for consistency.
 */
export const SuppliersTab = memo<SuppliersTabProps>(
  ({
    suppliers,
    loading,
    searchTerm,
    setSearchTerm,
    supplierFilter,
    setSupplierFilter,
    viewMode,
    onViewModeChange,
    onRefresh,
    onAddSupplier,
    onEditSupplier,
    onDeleteSupplier,
  }) => {
    // Checkbox selection state
    const [selectedSuppliers, setSelectedSuppliers] = useState<Set<string>>(
      new Set()
    );

    // Pagination state
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 10;

    // Filter suppliers based on filter and search term
    const filteredSuppliers = suppliers.filter((supplier) => {
      const matchesFilter =
        supplierFilter === "all" ||
        (supplier.status &&
          supplier.status.toLowerCase() === supplierFilter.toLowerCase());
      const matchesSearch =
        !searchTerm ||
        supplier.tracking_number
          ?.toLowerCase()
          .includes(searchTerm.toLowerCase()) ||
        supplier.phone?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        supplier.location?.toLowerCase().includes(searchTerm.toLowerCase());
      return matchesFilter && matchesSearch;
    });

    // Define table columns for Listing.Table
    const columns = [
      {
        key: "phone",
        label: "Phone",
        render: (supplier: Supplier) => {
          const displaySupplier = transformSupplierForDisplay(supplier);
          return displaySupplier.phone ? (
            <div className="flex items-center gap-1">
              <Phone className="h-3 w-3 text-gray-400" />
              <span className="text-sm text-gray-700">
                {displaySupplier.phone}
              </span>
            </div>
          ) : (
            <span className="text-sm text-gray-400">-</span>
          );
        },
      },
      {
        key: "location",
        label: "Location",
        render: (supplier: Supplier) => {
          const displaySupplier = transformSupplierForDisplay(supplier);
          return displaySupplier.location ? (
            <div className="flex items-center gap-1">
              <MapPin className="h-3 w-3 text-gray-400" />
              <span className="text-sm text-gray-700">
                {displaySupplier.location}
              </span>
            </div>
          ) : (
            <span className="text-sm text-gray-400">-</span>
          );
        },
      },
      {
        key: "tracking_number",
        label: "Tracking Number",
        render: (supplier: Supplier) => {
          const displaySupplier = transformSupplierForDisplay(supplier);
          return displaySupplier.tracking_number ? (
            <div className="flex items-center gap-1">
              <FileText className="h-3 w-3 text-gray-400" />
              <span className="text-sm text-gray-700">
                {displaySupplier.tracking_number}
              </span>
            </div>
          ) : (
            <span className="text-sm text-gray-400">-</span>
          );
        },
      },
      {
        key: "status",
        label: "Status",
        className: "text-center",
        render: (supplier: Supplier) => {
          const displaySupplier = transformSupplierForDisplay(supplier);
          return (
            <span
              className={`inline-flex items-center px-2 py-1 text-xs rounded-full ${
                displaySupplier.status === "ACTIVE"
                  ? "bg-green-100 text-green-800"
                  : displaySupplier.status === "INACTIVE"
                    ? "bg-red-100 text-red-800"
                    : displaySupplier.status === "PENDING"
                      ? "bg-yellow-100 text-yellow-800"
                      : "bg-gray-100 text-gray-800"
              }`}
            >
              {displaySupplier.status}
            </span>
          );
        },
      },
      {
        key: "created",
        label: "Created",
        className: "text-right",
        render: (supplier: Supplier) => {
          const displaySupplier = transformSupplierForDisplay(supplier);
          return (
            <span className="text-sm text-gray-500">
              {displaySupplier.created_at
                ? new Date(displaySupplier.created_at).toLocaleDateString()
                : "Unknown"}
            </span>
          );
        },
      },
      {
        key: "actions",
        label: "",
        className: "text-right w-[50px]",
        render: (supplier: Supplier) => (
          <div className="flex gap-1">
            <ProtectedEditButton entity="suppliers">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onEditSupplier(supplier);
                }}
                className="p-1 text-gray-400 hover:text-gray-600"
                title="Edit"
              >
                <Edit size={16} />
              </button>
            </ProtectedEditButton>
            <ProtectedDeleteButton entity="suppliers">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteSupplier(supplier);
                }}
                className="p-1 text-gray-400 hover:text-gray-600"
                title="Delete"
              >
                <Trash2 size={16} />
              </button>
            </ProtectedDeleteButton>
          </div>
        ),
      },
    ];

    return (
      <Listing className="pt-4">
        <Listing.Filters
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          onRefresh={onRefresh}
          loading={loading}
        />

        <Listing.Controls
          entity="suppliers"
          length={filteredSuppliers.length}
          viewMode={viewMode}
          onViewModeChange={onViewModeChange}
          categoryFilter={supplierFilter}
          onCategoryFilterChange={setSupplierFilter}
          categories={[
            { key: "active", label: "Active" },
            { key: "inactive", label: "Inactive" },
            { key: "pending", label: "Pending" },
          ]}
          actions={
            <div className="flex items-center gap-2">
              <ProtectedCreateButton entity="customers">
                <Button onClick={onAddSupplier}>
                  <UserPlus size={16} />
                  Add Supplier
                </Button>
              </ProtectedCreateButton>
            </div>
          }
        />

        {viewMode === "cards" ? (
          <Listing.Cards
            data={filteredSuppliers}
            loading={loading}
            columns="grid-cols-3"
            renderCard={(supplier: Supplier) => (
              <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow">
                {/* Header */}
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <div className="bg-primary/10 p-2 rounded-md">
                      <FileText className="h-4 w-4 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">
                        {transformSupplierForDisplay(supplier)
                          .tracking_number || "No tracking"}
                      </h3>
                      <p className="text-xs text-gray-500">
                        Supplier ID: {supplier.id.slice(0, 8)}...
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-1">
                    <ProtectedEditButton entity="suppliers">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onEditSupplier(supplier);
                        }}
                        className="p-1 text-gray-400 hover:text-gray-600"
                        title="Edit"
                      >
                        <Edit size={16} />
                      </button>
                    </ProtectedEditButton>
                    <ProtectedDeleteButton entity="suppliers">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onDeleteSupplier(supplier);
                        }}
                        className="p-1 text-gray-400 hover:text-gray-600"
                        title="Delete"
                      >
                        <Trash2 size={16} />
                      </button>
                    </ProtectedDeleteButton>
                  </div>
                </div>

                {/* Status */}
                <div className="mb-3">
                  <span
                    className={`inline-flex items-center px-2 py-1 text-xs rounded-full ${
                      transformSupplierForDisplay(supplier).status === "ACTIVE"
                        ? "bg-green-100 text-green-800"
                        : transformSupplierForDisplay(supplier).status ===
                            "INACTIVE"
                          ? "bg-red-100 text-red-800"
                          : transformSupplierForDisplay(supplier).status ===
                              "PENDING"
                            ? "bg-yellow-100 text-yellow-800"
                            : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {transformSupplierForDisplay(supplier).status}
                  </span>
                </div>

                {/* Contact Info */}
                <div className="space-y-2 mb-3">
                  {transformSupplierForDisplay(supplier).phone && (
                    <div className="flex items-center gap-1">
                      <Phone className="h-3 w-3 text-gray-400" />
                      <span className="text-xs text-gray-600">
                        {transformSupplierForDisplay(supplier).phone}
                      </span>
                    </div>
                  )}
                  {transformSupplierForDisplay(supplier).location && (
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3 text-gray-400" />
                      <span className="text-xs text-gray-600">
                        {transformSupplierForDisplay(supplier).location}
                      </span>
                    </div>
                  )}
                </div>

                {/* Created Date */}
                <div className="text-xs text-gray-500">
                  Created:{" "}
                  {transformSupplierForDisplay(supplier).created_at
                    ? new Date(
                        transformSupplierForDisplay(supplier).created_at
                      ).toLocaleDateString()
                    : "Unknown"}
                </div>
              </div>
            )}
            emptyState={
              <div className="py-12 text-center">
                <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
                  <Search className="h-6 w-6 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-1">
                  No suppliers found
                </h3>
                <p className="text-sm text-gray-500">
                  {searchTerm
                    ? "No suppliers match your search criteria."
                    : "No suppliers available."}
                </p>
              </div>
            }
          />
        ) : (
          <Listing.Table
            data={filteredSuppliers}
            columns={columns}
            loading={loading}
            enableCheckboxes={true}
            selectedRowIds={Array.from(selectedSuppliers)}
            onSelectionChange={(selectedIds) =>
              setSelectedSuppliers(new Set(selectedIds))
            }
            getRowId={(item) => item.id}
            pagination={{
              currentPage,
              totalPages: Math.ceil(filteredSuppliers.length / itemsPerPage),
              totalItems: filteredSuppliers.length,
              itemsPerPage,
              onPageChange: setCurrentPage,
            }}
            emptyState={
              <div className="py-12 text-center">
                <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
                  <Search className="h-6 w-6 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-1">
                  No suppliers found
                </h3>
                <p className="text-sm text-gray-500">
                  {searchTerm
                    ? "No suppliers match your search criteria."
                    : "No suppliers available."}
                </p>
              </div>
            }
          />
        )}
      </Listing>
    );
  }
);

SuppliersTab.displayName = "SuppliersTab";
