"use client";

import { memo, useState } from "react";
import { motion } from "framer-motion";
import {
  Loader2,
  Filter,
  Eye,
  User,
  Phone,
  MapPin,
  FileText,
} from "lucide-react";
import { AnimatedCard } from "@/components/animated-card";
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart as RePieChart,
  Pie,
  Cell,
  Legend,
  BarChart as ReBarChart,
  Bar,
} from "recharts";
import type {
  Customer,
  Supplier,
  CustomerStats,
  SupplierStats,
} from "../../types";

// Colors for charts
const COLORS = [
  "#0088FE",
  "#00C49F",
  "#FFBB28",
  "#FF8042",
  "#8884d8",
  "#82ca9d",
];

interface OverviewTabProps {
  customers: Customer[];
  customerStats: CustomerStats;
  suppliers: Supplier[];
  supplierStats: SupplierStats;
  loading: boolean;
}

/**
 * Overview Tab Component
 *
 * Displays analytics and overview information for customer relations.
 * Shows charts, recent customers, and key metrics.
 */
export const OverviewTab = memo<OverviewTabProps>(
  ({ customers, customerStats, suppliers, supplierStats, loading }) => {
    const [timeframe, setTimeframe] = useState("year");

    // Generate chart data from real customers
    const getChartData = () => {
      if (loading || customers.length === 0) {
        return {
          acquisitionData: [],
          statusData: [],
          locationData: [],
        };
      }

      // Group customers by month for acquisition chart
      const monthlyData = customers.reduce((acc: any, customer) => {
        if (customer.created_at) {
          const date = new Date(customer.created_at);
          const monthKey = date.toLocaleDateString("en-US", {
            month: "short",
            year: "numeric",
          });
          acc[monthKey] = (acc[monthKey] || 0) + 1;
        }
        return acc;
      }, {});

      const acquisitionData = Object.entries(monthlyData)
        .slice(-12) // Last 12 months
        .map(([name, value]) => ({ name, value }));

      // Group by status
      const statusCounts = customers.reduce((acc: any, customer) => {
        const status = customer.status || "Unknown";
        acc[status] = (acc[status] || 0) + 1;
        return acc;
      }, {});

      const statusData = Object.entries(statusCounts).map(([name, value]) => ({
        name,
        value,
      }));

      // Group by location (simplified - by country/region)
      const locationCounts = customers.reduce((acc: any, customer) => {
        const location =
          customer.location?.split(",").pop()?.trim() || "Unknown";
        acc[location] = (acc[location] || 0) + 1;
        return acc;
      }, {});

      const locationData = Object.entries(locationCounts)
        .slice(0, 5) // Top 5 locations
        .map(([name, value]) => ({ name, value }));

      return { acquisitionData, statusData, locationData };
    };

    const { acquisitionData, statusData, locationData } = getChartData();

    return (
      <div className="grid gap-6 grid-cols-1 lg:grid-cols-3 pt-4">
        <AnimatedCard className="lg:col-span-2">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                Customer Acquisition
              </h3>
              <p className="text-sm text-gray-500">
                New customers added over time
              </p>
            </div>
            <div className="flex gap-2">
              <button
                className={`text-xs px-3 py-1.5 border rounded-md ${
                  timeframe === "year"
                    ? "border-primary bg-primary text-white"
                    : "border-gray-200 bg-white text-gray-700 hover:bg-gray-50"
                }`}
                onClick={() => setTimeframe("year")}
              >
                Year
              </button>
              <button
                className={`text-xs px-3 py-1.5 border rounded-md ${
                  timeframe === "quarter"
                    ? "border-primary bg-primary text-white"
                    : "border-gray-200 bg-white text-gray-700 hover:bg-gray-50"
                }`}
                onClick={() => setTimeframe("quarter")}
              >
                Quarter
              </button>
              <button
                className={`text-xs px-3 py-1.5 border rounded-md ${
                  timeframe === "month"
                    ? "border-primary bg-primary text-white"
                    : "border-gray-200 bg-white text-gray-700 hover:bg-gray-50"
                }`}
                onClick={() => setTimeframe("month")}
              >
                Month
              </button>
            </div>
          </div>
          <div className="h-80 border-t border-gray-200 pt-4 bg-white">
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={acquisitionData}
                  margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                >
                  <defs>
                    <linearGradient
                      id="colorCustomers"
                      x1="0"
                      y1="0"
                      x2="0"
                      y2="1"
                    >
                      <stop offset="5%" stopColor="#0088FE" stopOpacity={0.8} />
                      <stop offset="95%" stopColor="#0088FE" stopOpacity={0} />
                    </linearGradient>
                  </defs>
                  <XAxis dataKey="name" tick={{ fontSize: 12 }} />
                  <YAxis tick={{ fontSize: 12 }} />
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <Tooltip
                    formatter={(value) => [
                      `${value} customers`,
                      "New Customers",
                    ]}
                    labelFormatter={(label) => label}
                  />
                  <Area
                    type="monotone"
                    dataKey="value"
                    stroke="#0088FE"
                    fillOpacity={1}
                    fill="url(#colorCustomers)"
                  />
                </AreaChart>
              </ResponsiveContainer>
            )}
          </div>
        </AnimatedCard>

        <AnimatedCard>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              Customer Status
            </h3>
            <button className="text-xs px-3 py-1.5 border border-gray-200 rounded-md bg-white text-gray-700 hover:bg-gray-50 flex items-center gap-1">
              <Filter className="h-3 w-3" /> Filter
            </button>
          </div>
          <div className="h-80 border-t border-gray-200 pt-4 bg-white">
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <RePieChart>
                  <Pie
                    data={statusData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) =>
                      `${name} ${(percent * 100).toFixed(0)}%`
                    }
                  >
                    {statusData.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={COLORS[index % COLORS.length]}
                      />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`${value} customers`]} />
                  <Legend />
                </RePieChart>
              </ResponsiveContainer>
            )}
          </div>
        </AnimatedCard>

        <AnimatedCard className="lg:col-span-2">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                Top Locations
              </h3>
              <p className="text-sm text-gray-500">
                Customer distribution by location
              </p>
            </div>
          </div>
          <div className="h-80 border-t border-gray-200 pt-4 bg-white">
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                <ReBarChart
                  data={locationData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`${value} customers`]} />
                  <Bar dataKey="value" name="Customers" fill="#0088FE" />
                </ReBarChart>
              </ResponsiveContainer>
            )}
          </div>
        </AnimatedCard>

        <AnimatedCard>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              Recent Customers
            </h3>
            <button className="text-xs px-3 py-1.5 border border-gray-200 rounded-md bg-white text-gray-700 hover:bg-gray-50 flex items-center gap-1">
              <Eye className="h-3 w-3" /> View All
            </button>
          </div>
          <div className="space-y-3 border-t border-gray-200 pt-4">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
              </div>
            ) : (
              customers.slice(0, 5).map((customer, i) => (
                <motion.div
                  key={customer.id}
                  className="flex justify-between items-center p-3 border border-gray-100 rounded-lg bg-white hover:bg-gray-50"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.2, delay: i * 0.1 }}
                >
                  <div className="flex items-center gap-3">
                    <div className="bg-primary/10 p-2 rounded-md">
                      <User className="h-4 w-4 text-primary" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">
                        {customer.name}
                      </p>
                      <div className="flex items-center gap-1 mt-1">
                        <span className="text-xs text-gray-500">
                          {customer.email}
                        </span>
                        {customer.location && (
                          <>
                            <span className="text-xs text-gray-500">•</span>
                            <span className="text-xs text-gray-500">
                              {customer.location}
                            </span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <span
                      className={`inline-flex items-center px-2 py-1 text-xs rounded-full ${
                        customer.status === "ACTIVE"
                          ? "bg-green-100 text-green-800"
                          : customer.status === "INACTIVE"
                            ? "bg-red-100 text-red-800"
                            : "bg-gray-100 text-gray-800"
                      }`}
                    >
                      {customer.status || "Unknown"}
                    </span>
                  </div>
                </motion.div>
              ))
            )}
            {!loading && customers.length === 0 && (
              <div className="text-center py-8">
                <p className="text-gray-500">No customers found</p>
              </div>
            )}
          </div>
        </AnimatedCard>
      </div>
    );
  }
);

OverviewTab.displayName = "OverviewTab";
