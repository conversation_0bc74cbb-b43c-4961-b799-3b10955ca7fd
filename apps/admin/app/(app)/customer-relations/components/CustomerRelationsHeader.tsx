"use client";

import { memo } from "react";
import { RefreshCw, UserPlus } from "lucide-react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { ProtectedCreateButton } from "@/lib/components/RBACWrapper";
import { cn } from "@workspace/ui/lib/utils";

interface CustomerRelationsHeaderProps {
  refreshing: boolean;
  onRefresh: () => void;
  onAddCustomer: () => void;
}

/**
 * Customer Relations Header Component
 *
 * Displays the page title, description, and primary actions.
 * Follows the cargo-management header pattern for consistency.
 */
export const CustomerRelationsHeader = memo<CustomerRelationsHeaderProps>(({
  refreshing,
  onRefresh,
  onAddCustomer,
}) => {
  return (
    <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
      <div>
        <h1 className="text-3xl font-bold text-foreground">
          Customer Relations
        </h1>
        <p className="text-muted-foreground mt-1">
          Manage clients and maintain strong business relationships
        </p>
      </div>
      
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          onClick={onRefresh}
          disabled={refreshing}
          className="flex items-center gap-1.5 px-3.5 py-2.5 text-sm border border-gray-200 text-gray-700 rounded-lg shadow-sm hover:bg-gray-50 transition-colors"
        >
          <RefreshCw
            size={16}
            className={cn(refreshing && "animate-spin")}
          />
          Refresh
        </Button>
        
        <ProtectedCreateButton entity="customers">
          <Button
            onClick={onAddCustomer}
            className="flex items-center gap-1.5 px-3.5 py-2.5 bg-primary text-white rounded-lg text-sm shadow-sm hover:bg-primary/90 transition-colors"
          >
            <UserPlus size={16} />
            Add Customer
          </Button>
        </ProtectedCreateButton>
      </div>
    </div>
  );
});

CustomerRelationsHeader.displayName = "CustomerRelationsHeader";
