"use client";

import { memo } from "react";
import {
  Loader2,
  AlertCircle,
  FileText,
  Phone,
  MapPin,
  Truck,
  Shield,
} from "lucide-react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@workspace/ui/components/dialog";
import { cn } from "@workspace/ui/lib/utils";
import type { Supplier, SupplierFormData, StatusEnum } from "../../types";

interface SupplierModalsProps {
  showAddModal: boolean;
  setShowAddModal: (show: boolean) => void;
  showEditModal: boolean;
  setShowEditModal: (show: boolean) => void;
  editingSupplier: Supplier | null;
  setEditingSupplier: (supplier: Supplier | null) => void;
  formData: SupplierFormData;
  setFormData: (data: SupplierFormData) => void;
  formLoading: boolean;
  formErrors: Partial<Record<keyof SupplierFormData, string>>;
  handleCreateSupplier: (data: SupplierFormData) => Promise<void>;
  handleUpdateSupplier: (id: string, data: SupplierFormData) => Promise<void>;
  resetSupplierForm: () => void;
}

/**
 * Supplier Modals Component
 *
 * Handles both add and edit supplier modals.
 * Follows the cargo-management modal pattern for consistency.
 */
export const SupplierModals = memo<SupplierModalsProps>(
  ({
    showAddModal,
    setShowAddModal,
    showEditModal,
    setShowEditModal,
    editingSupplier,
    setEditingSupplier,
    formData,
    setFormData,
    formLoading,
    formErrors,
    handleCreateSupplier,
    handleUpdateSupplier,
    resetSupplierForm,
  }) => {
    return (
      <>
        {/* Add Supplier Modal */}
        <Dialog
          open={showAddModal}
          onOpenChange={(open) => {
            setShowAddModal(open);
            if (!open && resetSupplierForm) {
              resetSupplierForm();
            }
          }}
        >
          <DialogContent className="sm:max-w-lg p-0 mx-auto">
            <DialogHeader className="px-4 py-3 border-b border-gray-100">
              <DialogTitle className="text-lg font-medium text-gray-900 text-center flex items-center justify-center gap-2">
                <Truck className="h-5 w-5 text-primary" />
                Add Supplier
              </DialogTitle>
            </DialogHeader>
            <form
              onSubmit={async (e) => {
                e.preventDefault();
                await handleCreateSupplier(formData);
              }}
              className="p-4"
            >
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5 flex items-center gap-1">
                    <FileText className="h-4 w-4 text-gray-500" />
                    Tracking Number *
                  </label>
                  <Input
                    placeholder="Enter supplier tracking number"
                    value={formData.tracking_number}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        tracking_number: e.target.value,
                      })
                    }
                    className={cn(
                      "w-full h-9 text-sm border rounded-md bg-white text-gray-900 focus:ring-1 focus:ring-primary/50 focus:border-primary",
                      formErrors.tracking_number
                        ? "border-red-300"
                        : "border-gray-200"
                    )}
                    required
                  />
                  {formErrors.tracking_number && (
                    <p className="mt-1 text-xs text-red-600 flex items-center gap-1">
                      <AlertCircle size={12} />
                      {formErrors.tracking_number}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5 flex items-center gap-1">
                    <Phone className="h-4 w-4 text-gray-500" />
                    Phone Number
                  </label>
                  <Input
                    type="tel"
                    placeholder="+****************"
                    value={formData.phone}
                    onChange={(e) =>
                      setFormData({ ...formData, phone: e.target.value })
                    }
                    className="w-full h-9 text-sm border border-gray-200 rounded-md bg-white text-gray-900 focus:ring-1 focus:ring-primary/50 focus:border-primary"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5 flex items-center gap-1">
                    <MapPin className="h-4 w-4 text-gray-500" />
                    Location
                  </label>
                  <Input
                    placeholder="City, Country"
                    value={formData.location}
                    onChange={(e) =>
                      setFormData({ ...formData, location: e.target.value })
                    }
                    className="w-full h-9 text-sm border border-gray-200 rounded-md bg-white text-gray-900 focus:ring-1 focus:ring-primary/50 focus:border-primary"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5 flex items-center gap-1">
                    <Shield className="h-4 w-4 text-gray-500" />
                    Status
                  </label>
                  <Select
                    value={formData.status}
                    onValueChange={(value: StatusEnum) =>
                      setFormData({ ...formData, status: value })
                    }
                  >
                    <SelectTrigger className="w-full h-9 text-sm border border-gray-200 rounded-md bg-white text-gray-900 focus:ring-1 focus:ring-primary/50 focus:border-primary">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ACTIVE">Active</SelectItem>
                      <SelectItem value="INACTIVE">Inactive</SelectItem>
                      <SelectItem value="PENDING">Pending</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="flex gap-2 mt-4">
                <DialogClose asChild>
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1 h-8 text-xs"
                  >
                    Cancel
                  </Button>
                </DialogClose>
                <Button
                  type="submit"
                  disabled={formLoading}
                  className="flex-1 h-8 text-xs bg-primary text-white hover:bg-primary/90 disabled:opacity-50"
                >
                  {formLoading ? (
                    <Loader2 size={14} className="animate-spin" />
                  ) : (
                    "Add Supplier"
                  )}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>

        {/* Edit Supplier Modal */}
        <Dialog
          open={showEditModal}
          onOpenChange={(open) => {
            setShowEditModal(open);
            if (!open) {
              setEditingSupplier(null);
              if (resetSupplierForm) {
                resetSupplierForm();
              }
            }
          }}
        >
          <DialogContent className="sm:max-w-lg p-0 mx-auto">
            <DialogHeader className="px-4 py-3 border-b border-gray-100">
              <DialogTitle className="text-lg font-medium text-gray-900 text-center flex items-center justify-center gap-2">
                <Truck className="h-5 w-5 text-primary" />
                Edit Supplier
              </DialogTitle>
            </DialogHeader>
            <form
              onSubmit={async (e) => {
                e.preventDefault();
                if (!editingSupplier) return;
                await handleUpdateSupplier(editingSupplier.id, formData);
              }}
              className="p-4"
            >
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5 flex items-center gap-1">
                    <FileText className="h-4 w-4 text-gray-500" />
                    Tracking Number *
                  </label>
                  <Input
                    placeholder="Enter supplier tracking number"
                    value={formData.tracking_number}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        tracking_number: e.target.value,
                      })
                    }
                    className={cn(
                      "w-full h-9 text-sm border rounded-md bg-white text-gray-900 focus:ring-1 focus:ring-primary/50 focus:border-primary",
                      formErrors.tracking_number
                        ? "border-red-300"
                        : "border-gray-200"
                    )}
                    required
                  />
                  {formErrors.tracking_number && (
                    <p className="mt-1 text-xs text-red-600 flex items-center gap-1">
                      <AlertCircle size={12} />
                      {formErrors.tracking_number}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5 flex items-center gap-1">
                    <Phone className="h-4 w-4 text-gray-500" />
                    Phone Number
                  </label>
                  <Input
                    type="tel"
                    placeholder="+****************"
                    value={formData.phone}
                    onChange={(e) =>
                      setFormData({ ...formData, phone: e.target.value })
                    }
                    className="w-full h-9 text-sm border border-gray-200 rounded-md bg-white text-gray-900 focus:ring-1 focus:ring-primary/50 focus:border-primary"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5 flex items-center gap-1">
                    <MapPin className="h-4 w-4 text-gray-500" />
                    Location
                  </label>
                  <Input
                    placeholder="City, Country"
                    value={formData.location}
                    onChange={(e) =>
                      setFormData({ ...formData, location: e.target.value })
                    }
                    className="w-full h-9 text-sm border border-gray-200 rounded-md bg-white text-gray-900 focus:ring-1 focus:ring-primary/50 focus:border-primary"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1.5 flex items-center gap-1">
                    <Shield className="h-4 w-4 text-gray-500" />
                    Status
                  </label>
                  <Select
                    value={formData.status}
                    onValueChange={(value: StatusEnum) =>
                      setFormData({ ...formData, status: value })
                    }
                  >
                    <SelectTrigger className="w-full h-9 text-sm border border-gray-200 rounded-md bg-white text-gray-900 focus:ring-1 focus:ring-primary/50 focus:border-primary">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ACTIVE">Active</SelectItem>
                      <SelectItem value="INACTIVE">Inactive</SelectItem>
                      <SelectItem value="PENDING">Pending</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="flex gap-2 mt-4">
                <DialogClose asChild>
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1 h-8 text-xs"
                  >
                    Cancel
                  </Button>
                </DialogClose>
                <Button
                  type="submit"
                  disabled={formLoading}
                  className="flex-1 h-8 text-xs bg-primary text-white hover:bg-primary/90 disabled:opacity-50"
                >
                  {formLoading ? (
                    <Loader2 size={14} className="animate-spin" />
                  ) : (
                    "Update"
                  )}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </>
    );
  }
);

SupplierModals.displayName = "SupplierModals";
