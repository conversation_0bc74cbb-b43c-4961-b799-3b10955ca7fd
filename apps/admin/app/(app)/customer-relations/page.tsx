"use client";

import { memo } from "react";
import { withRBAC } from "@/lib/components/RBACWrapper";
import { CustomerRelationsContainer } from "./components/CustomerRelationsContainer";

/**
 * Customer Relations Page Component
 *
 * This is the main page component for customer relations management.
 * It's wrapped with RBAC for permission control and uses a container component
 * for better separation of concerns and performance optimization.
 */
const CustomerRelationsPage = memo(() => {
  return <CustomerRelationsContainer />;
});

CustomerRelationsPage.displayName = "CustomerRelationsPage";

export default withRBAC(
  CustomerRelationsPage,
  "customers", // Entity
  "view", // Required action
  <div className="p-6 text-center">
    <h1 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h1>
    <p className="text-gray-500">
      You don't have permission to view customer relations.
    </p>
  </div> // Fallback content
);
