"use client";

import Link from "next/link";
import { motion } from "framer-motion";
import { Search, ArrowLeft } from "lucide-react";
import { AnimatedCard } from "@/components/animated-card";
import { slideUpAnimation, springTransition } from "@/lib/motion-config";

export default function NotFound() {
  return (
    <div className="flex items-center justify-center min-h-[80vh] p-6">
      <motion.div
        className="w-full max-w-2xl mx-auto"
        variants={slideUpAnimation}
        initial="hidden"
        animate="visible"
        exit="exit"
        transition={springTransition}
      >
        <AnimatedCard className="text-center">
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.2, ...springTransition }}
            className="mb-6 flex justify-center"
          >
            <div className="w-24 h-24 rounded-full bg-neutral-100 dark:bg-neutral-800 flex items-center justify-center">
              <Search size={48} className="text-neutral-500" />
            </div>
          </motion.div>
          
          <motion.h1 
            className="text-3xl font-bold mb-2"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.3, ...springTransition }}
          >
            404
          </motion.h1>
          
          <motion.p 
            className="text-xl font-medium mb-2"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.4, ...springTransition }}
          >
            Page Not Found
          </motion.p>
          
          <motion.p 
            className="text-muted-foreground mb-6"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.5, ...springTransition }}
          >
            The page you are looking for doesn't exist or has been moved.
          </motion.p>
          
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.6, ...springTransition }}
          >
            <Link href="/dashboard">
              <motion.button
                className="flex items-center gap-2 bg-primary hover:bg-primary/90 text-white px-5 py-2.5 rounded-md transition-colors duration-200 mx-auto"
                whileHover={{ scale: 1.05, x: -5 }}
                whileTap={{ scale: 0.95 }}
              >
                <ArrowLeft size={16} />
                Back to Dashboard
              </motion.button>
            </Link>
          </motion.div>
        </AnimatedCard>
      </motion.div>
    </div>
  );
} 