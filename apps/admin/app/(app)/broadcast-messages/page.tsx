"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  MessageSquare,
  Mail,
  Send,
  Users,
  AlertCircle,
  Clock,
  CheckCircle,
  Search,
  Filter,
  Smartphone,
  RefreshCw,
  PlusCircle,
  Trash,
  FileText,
  Copy,
  Edit,
  MoreHorizontal,
  X,
  BarChart,
  TrendingUp,
  ArrowUpRight,
  Eye,
  Plus,
  ChevronDown,
  Loader2,
} from "lucide-react";
import { AnimatedCard, AnimatedCardGrid } from "@/components/animated-card";
import { PageTransition } from "@/components/page-transition";
import { useAppSelector } from "@/store/hooks";
import {
  notificationService,
  customerService,
  userService,
  type Notification,
  type Customer,
  type User,
  type NotificationInsert,
} from "@/lib/logistics";
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Toolt<PERSON>,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from "recharts";

// Mock data for message metrics chart
const messageMetricsData = [
  { name: "<PERSON>", sent: 180, opened: 142, clicked: 87 },
  { name: "Feb", sent: 220, opened: 165, clicked: 95 },
  { name: "Mar", sent: 195, opened: 153, clicked: 78 },
  { name: "Apr", sent: 240, opened: 178, clicked: 110 },
  { name: "May", sent: 285, opened: 230, clicked: 132 },
  { name: "Jun", sent: 205, opened: 168, clicked: 90 },
  { name: "Jul", sent: 195, opened: 150, clicked: 85 },
  { name: "Aug", sent: 250, opened: 190, clicked: 105 },
  { name: "Sep", sent: 230, opened: 172, clicked: 98 },
  { name: "Oct", sent: 260, opened: 208, clicked: 118 },
  { name: "Nov", sent: 275, opened: 225, clicked: 142 },
  { name: "Dec", sent: 208, opened: 174, clicked: 95 },
];

// Mock data for message types distribution
const messageTypesData = [
  { name: "Email", value: 156 },
  { name: "SMS", value: 92 },
  { name: "In-App", value: 68 },
];

// Mock data for recipient groups
const recipientGroupsData = [
  { name: "Enterprise Clients", value: 42 },
  { name: "Mid-Market Clients", value: 86 },
  { name: "SMB Clients", value: 124 },
  { name: "Staff Members", value: 64 },
];

// Mock data for engagement by hour
const hourlyEngagementData = [
  { hour: "12 AM", rate: 15 },
  { hour: "2 AM", rate: 8 },
  { hour: "4 AM", rate: 5 },
  { hour: "6 AM", rate: 12 },
  { hour: "8 AM", rate: 32 },
  { hour: "10 AM", rate: 68 },
  { hour: "12 PM", rate: 72 },
  { hour: "2 PM", rate: 85 },
  { hour: "4 PM", rate: 78 },
  { hour: "6 PM", rate: 65 },
  { hour: "8 PM", rate: 45 },
  { hour: "10 PM", rate: 25 },
];

// Colors for charts
const COLORS = [
  "#0088FE",
  "#00C49F",
  "#FFBB28",
  "#FF8042",
  "#8884d8",
  "#82ca9d",
];

// Message statistics interface
interface MessageStats {
  totalMessages: number;
  deliveredMessages: number;
  scheduledMessages: number;
  totalRecipients: number;
}

export default function BroadcastMessagesPage() {
  const [activeTab, setActiveTab] = useState("overview");
  const [searchTerm, setSearchTerm] = useState("");
  const [messageFilter, setMessageFilter] = useState("all");
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Data states
  const [messageStats, setMessageStats] = useState<MessageStats>({
    totalMessages: 0,
    deliveredMessages: 0,
    scheduledMessages: 0,
    totalRecipients: 0,
  });
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [users, setUsers] = useState<User[]>([]);

  // Get authenticated user
  const { user: authUser } = useAppSelector((state) => state.auth);

  // Fetch broadcast data
  const fetchBroadcastData = async (refresh = false) => {
    if (!authUser) return;

    try {
      if (refresh) setRefreshing(true);
      else setLoading(true);

      // Fetch all data in parallel
      const [notificationsResult, customersResult, usersResult] =
        await Promise.all([
          notificationService.getAllNotifications({ limit: 1000 }),
          customerService.getActiveCustomers({ limit: 1000 }),
          userService.getActiveUsers({ limit: 1000 }),
        ]);

      if (notificationsResult.success) {
        setNotifications(notificationsResult.data);
        calculateMessageStats(notificationsResult.data);
      }

      if (customersResult.success) setCustomers(customersResult.data);
      if (usersResult.success) setUsers(usersResult.data);
    } catch (error) {
      // Handle error silently or with proper error reporting
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Calculate message statistics
  const calculateMessageStats = (notifications: Notification[]) => {
    const delivered = notifications.filter((n) => n.read_at).length;
    const scheduled = notifications.filter(
      (n) => n.created_at && new Date(n.created_at) > new Date()
    ).length;

    setMessageStats({
      totalMessages: notifications.length,
      deliveredMessages: delivered,
      scheduledMessages: scheduled,
      totalRecipients: customers.length + users.length,
    });
  };

  useEffect(() => {
    if (authUser) {
      fetchBroadcastData();
    }
  }, [authUser]);

  useEffect(() => {
    if (notifications.length > 0) {
      calculateMessageStats(notifications);
    }
  }, [notifications, customers, users]);

  if (!authUser) {
    return (
      <PageTransition className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <h2 className="text-lg font-medium text-gray-900 mb-2">
              Authentication Required
            </h2>
            <p className="text-gray-500">
              Please sign in to access broadcast messages.
            </p>
          </div>
        </div>
      </PageTransition>
    );
  }

  return (
    <PageTransition className="p-6">
      <div className="flex flex-col gap-6">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="flex justify-between items-center"
        >
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              Broadcast Messages
            </h1>
            <p className="text-muted-foreground mt-1">
              Create and send messages to multiple customers or staff
            </p>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => fetchBroadcastData(true)}
              disabled={refreshing}
              className="flex items-center gap-1.5 px-3.5 py-2.5 bg-white border border-gray-200 text-gray-700 rounded-lg text-sm shadow-sm hover:bg-gray-50 transition-colors"
            >
              <RefreshCw
                size={16}
                className={refreshing ? "animate-spin" : ""}
              />
              Refresh
            </button>
            <button className="flex items-center gap-1.5 px-3.5 py-2.5 bg-primary text-white rounded-lg text-sm shadow-sm hover:bg-primary/90 transition-colors">
              <PlusCircle size={16} />
              New Message
            </button>
          </div>
        </motion.div>

        <AnimatedCardGrid className="grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
          <AnimatedCard className="flex items-center gap-4">
            <div className="bg-primary/10 p-3 rounded-full">
              <MessageSquare className="h-6 w-6 text-primary" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Total Messages</p>
              <h3 className="text-2xl font-bold">
                {loading ? (
                  <div className="h-8 w-16 bg-gray-200 rounded animate-pulse"></div>
                ) : (
                  messageStats.totalMessages
                )}
              </h3>
              <p className="text-xs text-gray-500">All time</p>
            </div>
          </AnimatedCard>

          <AnimatedCard className="flex items-center gap-4">
            <div className="bg-green-500/10 p-3 rounded-full">
              <CheckCircle className="h-6 w-6 text-green-500" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Delivered</p>
              <h3 className="text-2xl font-bold">
                {loading ? (
                  <div className="h-8 w-16 bg-gray-200 rounded animate-pulse"></div>
                ) : (
                  messageStats.deliveredMessages
                )}
              </h3>
              <p className="text-xs text-green-600 flex items-center gap-1">
                <ArrowUpRight className="h-3 w-3" />
                {messageStats.totalMessages > 0
                  ? Math.round(
                      (messageStats.deliveredMessages /
                        messageStats.totalMessages) *
                        100
                    )
                  : 0}
                %
              </p>
            </div>
          </AnimatedCard>

          <AnimatedCard className="flex items-center gap-4">
            <div className="bg-amber-500/10 p-3 rounded-full">
              <Clock className="h-6 w-6 text-amber-500" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Scheduled</p>
              <h3 className="text-2xl font-bold">
                {loading ? (
                  <div className="h-8 w-16 bg-gray-200 rounded animate-pulse"></div>
                ) : (
                  messageStats.scheduledMessages
                )}
              </h3>
              <p className="text-xs text-amber-600">Upcoming</p>
            </div>
          </AnimatedCard>

          <AnimatedCard className="flex items-center gap-4">
            <div className="bg-blue-500/10 p-3 rounded-full">
              <Users className="h-6 w-6 text-blue-500" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Recipients</p>
              <h3 className="text-2xl font-bold">
                {loading ? (
                  <div className="h-8 w-16 bg-gray-200 rounded animate-pulse"></div>
                ) : (
                  messageStats.totalRecipients
                )}
              </h3>
              <p className="text-xs text-gray-500">Active contacts</p>
            </div>
          </AnimatedCard>
        </AnimatedCardGrid>

        {/* Tab navigation */}
        <div className="flex border-b border-gray-200">
          <button
            className={`px-4 py-2 font-medium text-sm ${
              activeTab === "overview"
                ? "text-primary border-b-2 border-primary"
                : "text-gray-600 hover:text-gray-900"
            }`}
            onClick={() => setActiveTab("overview")}
          >
            Overview
          </button>
          <button
            className={`px-4 py-2 font-medium text-sm ${
              activeTab === "send"
                ? "text-primary border-b-2 border-primary"
                : "text-gray-600 hover:text-gray-900"
            }`}
            onClick={() => setActiveTab("send")}
          >
            Send
          </button>
          <button
            className={`px-4 py-2 font-medium text-sm ${
              activeTab === "messages"
                ? "text-primary border-b-2 border-primary"
                : "text-gray-600 hover:text-gray-900"
            }`}
            onClick={() => setActiveTab("messages")}
          >
            Message History
          </button>
          <button
            className={`px-4 py-2 font-medium text-sm ${
              activeTab === "templates"
                ? "text-primary border-b-2 border-primary"
                : "text-gray-600 hover:text-gray-900"
            }`}
            onClick={() => setActiveTab("templates")}
          >
            Templates
          </button>
          <button
            className={`px-4 py-2 font-medium text-sm ${
              activeTab === "scheduled"
                ? "text-primary border-b-2 border-primary"
                : "text-gray-600 hover:text-gray-900"
            }`}
            onClick={() => setActiveTab("scheduled")}
          >
            Scheduled
          </button>
        </div>

        {/* Tab content */}
        <div className="mt-2">
          {activeTab === "overview" && (
            <Overview
              loading={loading}
              notifications={notifications}
              customers={customers}
              users={users}
            />
          )}
          {activeTab === "send" && (
            <SendCommunication
              customers={customers}
              users={users}
              onSend={fetchBroadcastData}
            />
          )}
          {activeTab === "messages" && (
            <MessageHistory
              notifications={notifications}
              loading={loading}
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              messageFilter={messageFilter}
              setMessageFilter={setMessageFilter}
            />
          )}
          {activeTab === "templates" && <Templates />}
          {activeTab === "scheduled" && (
            <ScheduledMessages
              notifications={notifications.filter(
                (n) => n.created_at && new Date(n.created_at) > new Date()
              )}
              loading={loading}
            />
          )}
        </div>
      </div>
    </PageTransition>
  );
}

function Overview() {
  const [timeframe, setTimeframe] = useState("month");

  return (
    <div className="grid gap-6 grid-cols-1 lg:grid-cols-3">
      <AnimatedCard className="lg:col-span-2">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              Message Performance
            </h3>
            <p className="text-sm text-gray-500">
              Monthly message metrics for 2023
            </p>
          </div>
          <div className="flex gap-2">
            <button
              className={`text-xs px-3 py-1.5 border rounded-md ${
                timeframe === "year"
                  ? "border-primary bg-primary text-white"
                  : "border-gray-200 bg-white text-gray-700 hover:bg-gray-50"
              }`}
              onClick={() => setTimeframe("year")}
            >
              Year
            </button>
            <button
              className={`text-xs px-3 py-1.5 border rounded-md ${
                timeframe === "quarter"
                  ? "border-primary bg-primary text-white"
                  : "border-gray-200 bg-white text-gray-700 hover:bg-gray-50"
              }`}
              onClick={() => setTimeframe("quarter")}
            >
              Quarter
            </button>
            <button
              className={`text-xs px-3 py-1.5 border rounded-md ${
                timeframe === "month"
                  ? "border-primary bg-primary text-white"
                  : "border-gray-200 bg-white text-gray-700 hover:bg-gray-50"
              }`}
              onClick={() => setTimeframe("month")}
            >
              Month
            </button>
          </div>
        </div>
        <div className="h-80 border-t border-gray-200 pt-4 bg-white">
          <ResponsiveContainer width="100%" height="100%">
            <ReBarChart
              data={messageMetricsData}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" vertical={false} />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="sent" name="Sent" fill="#0088FE" />
              <Bar dataKey="opened" name="Opened" fill="#00C49F" />
              <Bar dataKey="clicked" name="Clicked" fill="#FFBB28" />
            </ReBarChart>
          </ResponsiveContainer>
        </div>
      </AnimatedCard>

      <AnimatedCard>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900">Message Types</h3>
          <button className="text-xs px-3 py-1.5 border border-gray-200 rounded-md bg-white text-gray-700 hover:bg-gray-50 flex items-center gap-1">
            <Filter className="h-3 w-3" /> Filter
          </button>
        </div>
        <div className="h-80 border-t border-gray-200 pt-4 bg-white">
          <ResponsiveContainer width="100%" height="100%">
            <RePieChart>
              <Pie
                data={messageTypesData}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
                label={({ name, percent }) =>
                  `${name} ${(percent * 100).toFixed(0)}%`
                }
              >
                {messageTypesData.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={COLORS[index % COLORS.length]}
                  />
                ))}
              </Pie>
              <Tooltip formatter={(value) => `${value} messages`} />
              <Legend />
            </RePieChart>
          </ResponsiveContainer>
        </div>
      </AnimatedCard>

      <AnimatedCard className="lg:col-span-2">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              Engagement by Hour
            </h3>
            <p className="text-sm text-gray-500">Open rate by time of day</p>
          </div>
        </div>
        <div className="h-80 border-t border-gray-200 pt-4 bg-white">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart
              data={hourlyEngagementData}
              margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
            >
              <defs>
                <linearGradient
                  id="colorEngagement"
                  x1="0"
                  y1="0"
                  x2="0"
                  y2="1"
                >
                  <stop offset="5%" stopColor="#0088FE" stopOpacity={0.8} />
                  <stop offset="95%" stopColor="#0088FE" stopOpacity={0} />
                </linearGradient>
              </defs>
              <XAxis dataKey="hour" tick={{ fontSize: 12 }} />
              <YAxis
                tickFormatter={(value) => `${value}%`}
                tick={{ fontSize: 12 }}
              />
              <CartesianGrid strokeDasharray="3 3" vertical={false} />
              <Tooltip formatter={(value) => [`${value}%`, "Open Rate"]} />
              <Area
                type="monotone"
                dataKey="rate"
                stroke="#0088FE"
                fillOpacity={1}
                fill="url(#colorEngagement)"
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </AnimatedCard>

      <AnimatedCard>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900">
            Recipient Groups
          </h3>
          <button className="text-xs px-3 py-1.5 border border-gray-200 rounded-md bg-white text-gray-700 hover:bg-gray-50 flex items-center gap-1">
            <Plus className="h-3 w-3" /> Add Group
          </button>
        </div>
        <div className="space-y-3 border-t border-gray-200 pt-4">
          {recipientGroupsData.map((group, i) => (
            <div
              key={i}
              className="flex justify-between items-center p-3 border border-gray-100 rounded-lg bg-white hover:bg-gray-50"
            >
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-md bg-blue-50">
                  <Users className="h-4 w-4 text-blue-500" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">{group.name}</p>
                  <p className="text-xs text-gray-500">
                    {group.value} recipients
                  </p>
                </div>
              </div>
              <div className="flex gap-2">
                <button
                  className="p-1 text-gray-500 hover:text-gray-700"
                  title="View"
                >
                  <Eye className="h-4 w-4" />
                </button>
                <button
                  className="p-1 text-gray-500 hover:text-gray-700"
                  title="Send Message"
                >
                  <Send className="h-4 w-4" />
                </button>
              </div>
            </div>
          ))}
          <button className="w-full mt-2 text-center text-sm text-primary hover:text-primary/80 font-medium">
            View all recipient groups
          </button>
        </div>
      </AnimatedCard>
    </div>
  );
}

function SendCommunication() {
  const [messageType, setMessageType] = useState("email");
  const [selectedTemplate, setSelectedTemplate] = useState("");
  const [recipientMode, setRecipientMode] = useState("groups"); // "groups" or "individuals"
  const [recipientType, setRecipientType] = useState("all");
  const [selectedIndividuals, setSelectedIndividuals] = useState([]);
  const [recipientSearch, setRecipientSearch] = useState("");
  const [isScheduled, setIsScheduled] = useState(false);
  const [scheduleDate, setScheduleDate] = useState("");
  const [scheduleTime, setScheduleTime] = useState("");

  // Mock data for templates
  const templateOptions = [
    { id: "1", name: "Welcome Message", type: "email" },
    { id: "2", name: "Shipping Update", type: "email" },
    { id: "3", name: "Monthly Newsletter", type: "email" },
    { id: "4", name: "Service Notification", type: "sms" },
    { id: "5", name: "Payment Reminder", type: "sms" },
    { id: "6", name: "Feature Update", type: "in-app" },
  ];

  // Mock data for recipient groups
  const recipientGroups = [
    { id: "all", name: "All Clients", count: 2156 },
    { id: "enterprise", name: "Enterprise Clients", count: 42 },
    { id: "midmarket", name: "Mid-Market Clients", count: 86 },
    { id: "smb", name: "SMB Clients", count: 124 },
    { id: "staff", name: "Staff Members", count: 64 },
  ];

  // Mock data for individual recipients
  const individualRecipients = [
    {
      id: "1",
      name: "John Doe",
      email: "<EMAIL>",
      phone: "+1234567890",
      type: "client",
    },
    {
      id: "2",
      name: "Jane Smith",
      email: "<EMAIL>",
      phone: "+1234567891",
      type: "client",
    },
    {
      id: "3",
      name: "Robert Johnson",
      email: "<EMAIL>",
      phone: "+1234567892",
      type: "client",
    },
    {
      id: "4",
      name: "Emily Davis",
      email: "<EMAIL>",
      phone: "+1234567893",
      type: "staff",
    },
    {
      id: "5",
      name: "Michael Wilson",
      email: "<EMAIL>",
      phone: "+1234567894",
      type: "client",
    },
    {
      id: "6",
      name: "Sarah Brown",
      email: "<EMAIL>",
      phone: "+1234567895",
      type: "staff",
    },
    {
      id: "7",
      name: "David Miller",
      email: "<EMAIL>",
      phone: "+1234567896",
      type: "client",
    },
    {
      id: "8",
      name: "Jessica Martinez",
      email: "<EMAIL>",
      phone: "+1234567897",
      type: "client",
    },
  ];

  // Filter templates based on selected message type
  const filteredTemplates = templateOptions.filter(
    (template) => template.type === messageType
  );

  // Filter individual recipients based on search term
  const filteredRecipients = individualRecipients.filter(
    (recipient) =>
      recipient.name.toLowerCase().includes(recipientSearch.toLowerCase()) ||
      recipient.email.toLowerCase().includes(recipientSearch.toLowerCase()) ||
      recipient.phone.includes(recipientSearch)
  );

  // Add an individual recipient to the selected list
  const addRecipient = (recipient) => {
    if (!selectedIndividuals.some((r) => r.id === recipient.id)) {
      setSelectedIndividuals([...selectedIndividuals, recipient]);
    }
  };

  // Remove an individual recipient from the selected list
  const removeRecipient = (recipientId) => {
    setSelectedIndividuals(
      selectedIndividuals.filter((r) => r.id !== recipientId)
    );
  };

  // Get the total count of recipients
  const getTotalRecipients = () => {
    if (recipientMode === "groups") {
      return (
        recipientGroups.find((group) => group.id === recipientType)?.count || 0
      );
    } else {
      return selectedIndividuals.length;
    }
  };

  return (
    <AnimatedCard>
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-medium text-gray-900">
          Send Communication
        </h3>
        <div className="flex items-center gap-2">
          <button className="flex items-center gap-1.5 px-3.5 py-2 border border-gray-200 text-gray-700 rounded-lg text-sm hover:bg-gray-50 transition-colors">
            <Eye className="h-4 w-4" />
            Preview
          </button>
          <button
            className="flex items-center gap-1.5 px-3.5 py-2 bg-primary text-white rounded-lg text-sm shadow-sm hover:bg-primary/90 transition-colors"
            disabled={
              recipientMode === "individuals" &&
              selectedIndividuals.length === 0
            }
          >
            <Send className="h-4 w-4" />
            Send Now
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          {/* Message Type Selection */}
          <div className="bg-white p-4 border border-gray-200 rounded-lg">
            <h4 className="text-sm font-medium text-gray-700 mb-3">
              Message Type
            </h4>
            <div className="flex flex-wrap gap-2">
              <button
                className={`px-4 py-2 text-sm rounded-md border ${
                  messageType === "email"
                    ? "bg-primary text-white border-primary"
                    : "bg-white text-gray-700 border-gray-200 hover:bg-gray-50"
                }`}
                onClick={() => setMessageType("email")}
              >
                <Mail className="h-4 w-4 inline-block mr-2" />
                Email
              </button>
              <button
                className={`px-4 py-2 text-sm rounded-md border ${
                  messageType === "sms"
                    ? "bg-primary text-white border-primary"
                    : "bg-white text-gray-700 border-gray-200 hover:bg-gray-50"
                }`}
                onClick={() => setMessageType("sms")}
              >
                <Smartphone className="h-4 w-4 inline-block mr-2" />
                SMS
              </button>
              <button
                className={`px-4 py-2 text-sm rounded-md border ${
                  messageType === "in-app"
                    ? "bg-primary text-white border-primary"
                    : "bg-white text-gray-700 border-gray-200 hover:bg-gray-50"
                }`}
                onClick={() => setMessageType("in-app")}
              >
                <MessageSquare className="h-4 w-4 inline-block mr-2" />
                In-App
              </button>
            </div>
          </div>

          {/* Template Selection */}
          <div className="bg-white p-4 border border-gray-200 rounded-lg">
            <div className="flex justify-between items-center mb-3">
              <h4 className="text-sm font-medium text-gray-700">Template</h4>
              <button className="text-xs text-primary hover:text-primary/80">
                Create New Template
              </button>
            </div>

            <select
              value={selectedTemplate}
              onChange={(e) => setSelectedTemplate(e.target.value)}
              className="w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary"
            >
              <option value="">Select a template</option>
              {filteredTemplates.map((template) => (
                <option key={template.id} value={template.id}>
                  {template.name}
                </option>
              ))}
            </select>
          </div>

          {/* Message Content */}
          <div className="bg-white p-4 border border-gray-200 rounded-lg">
            <h4 className="text-sm font-medium text-gray-700 mb-3">
              Message Content
            </h4>

            {messageType === "email" && (
              <div className="mb-4">
                <label
                  htmlFor="subject"
                  className="block text-sm text-gray-600 mb-1"
                >
                  Subject Line
                </label>
                <input
                  type="text"
                  id="subject"
                  placeholder="Enter subject line"
                  className="w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary"
                />
              </div>
            )}

            <div>
              <label
                htmlFor="message"
                className="block text-sm text-gray-600 mb-1"
              >
                Message
              </label>
              <textarea
                id="message"
                rows={8}
                placeholder="Enter your message here..."
                className="w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary"
              ></textarea>
            </div>
          </div>

          {/* Schedule Options */}
          <div className="bg-white p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center mb-3">
              <input
                type="checkbox"
                id="schedule"
                checked={isScheduled}
                onChange={() => setIsScheduled(!isScheduled)}
                className="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary"
              />
              <label
                htmlFor="schedule"
                className="ml-2 text-sm font-medium text-gray-700"
              >
                Schedule for Later
              </label>
            </div>

            {isScheduled && (
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-3">
                <div>
                  <label
                    htmlFor="scheduleDate"
                    className="block text-sm text-gray-600 mb-1"
                  >
                    Date
                  </label>
                  <input
                    type="date"
                    id="scheduleDate"
                    value={scheduleDate}
                    onChange={(e) => setScheduleDate(e.target.value)}
                    className="w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary"
                  />
                </div>
                <div>
                  <label
                    htmlFor="scheduleTime"
                    className="block text-sm text-gray-600 mb-1"
                  >
                    Time
                  </label>
                  <input
                    type="time"
                    id="scheduleTime"
                    value={scheduleTime}
                    onChange={(e) => setScheduleTime(e.target.value)}
                    className="w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary"
                  />
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="space-y-6">
          {/* Recipients */}
          <div className="bg-white p-4 border border-gray-200 rounded-lg">
            <div className="flex justify-between items-center mb-3">
              <h4 className="text-sm font-medium text-gray-700">Recipients</h4>
              <div className="flex bg-gray-100 rounded-md p-0.5">
                <button
                  className={`px-3 py-1.5 text-xs rounded ${
                    recipientMode === "groups"
                      ? "bg-white text-gray-800 shadow-sm"
                      : "text-gray-600 hover:text-gray-800"
                  }`}
                  onClick={() => setRecipientMode("groups")}
                >
                  Groups
                </button>
                <button
                  className={`px-3 py-1.5 text-xs rounded ${
                    recipientMode === "individuals"
                      ? "bg-white text-gray-800 shadow-sm"
                      : "text-gray-600 hover:text-gray-800"
                  }`}
                  onClick={() => setRecipientMode("individuals")}
                >
                  Individuals
                </button>
              </div>
            </div>

            {recipientMode === "groups" ? (
              <div className="space-y-2">
                {recipientGroups.map((group) => (
                  <div key={group.id} className="flex items-center">
                    <input
                      type="radio"
                      id={`group-${group.id}`}
                      name="recipientGroup"
                      value={group.id}
                      checked={recipientType === group.id}
                      onChange={() => setRecipientType(group.id)}
                      className="h-4 w-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <label
                      htmlFor={`group-${group.id}`}
                      className="ml-2 block text-sm text-gray-700"
                    >
                      {group.name}{" "}
                      <span className="text-gray-500">({group.count})</span>
                    </label>
                  </div>
                ))}

                <div className="mt-4 pt-4 border-t border-gray-200">
                  <button className="flex items-center gap-1.5 text-sm text-primary hover:text-primary/80">
                    <Plus className="h-3.5 w-3.5" />
                    Create New Group
                  </button>
                </div>
              </div>
            ) : (
              <div>
                <div className="relative mb-4">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                  <input
                    type="text"
                    placeholder="Search by name, email, or phone..."
                    value={recipientSearch}
                    onChange={(e) => setRecipientSearch(e.target.value)}
                    className="pl-10 pr-4 py-2.5 text-sm border border-gray-200 rounded-lg w-full bg-white text-gray-900 shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary"
                  />
                </div>

                {/* Search results */}
                {recipientSearch && (
                  <div className="max-h-60 overflow-y-auto mb-4 border border-gray-200 rounded-lg">
                    {filteredRecipients.length > 0 ? (
                      filteredRecipients.map((recipient) => (
                        <div
                          key={recipient.id}
                          className="p-2.5 border-b border-gray-100 last:border-0 hover:bg-gray-50 cursor-pointer"
                          onClick={() => addRecipient(recipient)}
                        >
                          <div className="flex justify-between items-center">
                            <div>
                              <p className="font-medium text-sm text-gray-900">
                                {recipient.name}
                              </p>
                              <p className="text-xs text-gray-500">
                                {messageType === "email"
                                  ? recipient.email
                                  : recipient.phone}
                              </p>
                            </div>
                            <button
                              className="p-1 text-gray-400 hover:text-primary"
                              onClick={(e) => {
                                e.stopPropagation();
                                addRecipient(recipient);
                              }}
                            >
                              <Plus className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="p-4 text-center text-sm text-gray-500">
                        No recipients found matching "{recipientSearch}"
                      </div>
                    )}
                  </div>
                )}

                {/* Selected individuals */}
                <div className="mb-2 flex justify-between items-center">
                  <h5 className="text-xs font-medium text-gray-500">
                    Selected Recipients ({selectedIndividuals.length})
                  </h5>
                  {selectedIndividuals.length > 0 && (
                    <button
                      className="text-xs text-gray-500 hover:text-gray-700"
                      onClick={() => setSelectedIndividuals([])}
                    >
                      Clear All
                    </button>
                  )}
                </div>

                <div className="max-h-60 overflow-y-auto">
                  {selectedIndividuals.length > 0 ? (
                    <div className="space-y-2">
                      {selectedIndividuals.map((recipient) => (
                        <div
                          key={recipient.id}
                          className="flex justify-between items-center p-2 bg-gray-50 rounded-md"
                        >
                          <div>
                            <p className="text-sm font-medium text-gray-900">
                              {recipient.name}
                            </p>
                            <p className="text-xs text-gray-500">
                              {messageType === "email"
                                ? recipient.email
                                : recipient.phone}
                            </p>
                          </div>
                          <button
                            className="p-1 text-gray-400 hover:text-red-500"
                            onClick={() => removeRecipient(recipient.id)}
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-6 text-sm text-gray-500">
                      No recipients selected. Search and select recipients
                      above.
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Delivery Options */}
          <div className="bg-white p-4 border border-gray-200 rounded-lg">
            <h4 className="text-sm font-medium text-gray-700 mb-3">
              Delivery Options
            </h4>

            <div className="space-y-3">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="trackOpens"
                  defaultChecked
                  className="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary"
                />
                <label
                  htmlFor="trackOpens"
                  className="ml-2 block text-sm text-gray-700"
                >
                  Track Opens
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="trackClicks"
                  defaultChecked
                  className="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary"
                />
                <label
                  htmlFor="trackClicks"
                  className="ml-2 block text-sm text-gray-700"
                >
                  Track Clicks
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="sendCopy"
                  className="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary"
                />
                <label
                  htmlFor="sendCopy"
                  className="ml-2 block text-sm text-gray-700"
                >
                  Send Copy to Myself
                </label>
              </div>
            </div>
          </div>

          {/* Summary */}
          <div className="bg-white p-4 border border-gray-200 rounded-lg">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Summary</h4>

            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-500">Message Type:</span>
                <span className="text-gray-900 capitalize">{messageType}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Recipient Mode:</span>
                <span className="text-gray-900 capitalize">
                  {recipientMode}
                </span>
              </div>
              {recipientMode === "groups" && (
                <div className="flex justify-between">
                  <span className="text-gray-500">Recipient Group:</span>
                  <span className="text-gray-900">
                    {recipientGroups.find((group) => group.id === recipientType)
                      ?.name || "None selected"}
                  </span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-gray-500">Total Recipients:</span>
                <span className="text-gray-900">{getTotalRecipients()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Delivery:</span>
                <span className="text-gray-900">
                  {isScheduled ? "Scheduled" : "Immediate"}
                </span>
              </div>

              {isScheduled && scheduleDate && scheduleTime && (
                <div className="flex justify-between">
                  <span className="text-gray-500">Scheduled for:</span>
                  <span className="text-gray-900">{`${scheduleDate} at ${scheduleTime}`}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </AnimatedCard>
  );
}

function MessageHistory() {
  const [messageFilterStatus, setMessageFilterStatus] = useState("all");
  const [messageSearchTerm, setMessageSearchTerm] = useState("");

  // Mock message data
  const messages = [
    {
      subject: "Important: New Safety Regulations",
      type: "Email",
      recipients: "All Clients",
      sentDate: "May 15, 2023",
      status: "delivered",
      openRate: "68%",
    },
    {
      subject: "System Maintenance Notice",
      type: "Email",
      recipients: "All Users",
      sentDate: "May 10, 2023",
      status: "delivered",
      openRate: "72%",
    },
    {
      subject: "Holiday Schedule Update",
      type: "SMS",
      recipients: "Active Clients",
      sentDate: "May 05, 2023",
      status: "delivered",
      openRate: "94%",
    },
    {
      subject: "New Feature Release",
      type: "In-App",
      recipients: "All Users",
      sentDate: "Apr 28, 2023",
      status: "delivered",
      openRate: "45%",
    },
    {
      subject: "Partnership Announcement",
      type: "Email",
      recipients: "Enterprise Clients",
      sentDate: "Apr 20, 2023",
      status: "delivered",
      openRate: "81%",
    },
  ];

  // Filter messages based on status and search term
  const filteredMessages = messages.filter((message) => {
    const matchesStatus =
      messageFilterStatus === "all" ||
      message.type.toLowerCase() === messageFilterStatus.toLowerCase();
    const matchesSearch =
      !messageSearchTerm ||
      message.subject.toLowerCase().includes(messageSearchTerm.toLowerCase()) ||
      message.recipients
        .toLowerCase()
        .includes(messageSearchTerm.toLowerCase());

    return matchesStatus && matchesSearch;
  });

  return (
    <AnimatedCard>
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-medium text-gray-900">Message History</h3>
          <span className="bg-primary/10 text-primary text-xs px-2 py-1 rounded-full">
            {messages.length} messages
          </span>
        </div>

        <div className="flex flex-wrap items-center gap-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
            <input
              type="text"
              placeholder="Search messages..."
              value={messageSearchTerm}
              onChange={(e) => setMessageSearchTerm(e.target.value)}
              className="pl-10 pr-10 py-2.5 text-sm border border-gray-200 rounded-lg w-full sm:w-64 bg-white text-gray-900 shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-colors"
            />
            {messageSearchTerm && (
              <button
                onClick={() => setMessageSearchTerm("")}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 hover:text-gray-600"
              >
                <X size={16} />
              </button>
            )}
          </div>

          <button className="flex items-center gap-1.5 px-3.5 py-2.5 bg-white border border-gray-200 text-gray-700 rounded-lg text-sm shadow-sm hover:bg-gray-50 transition-colors">
            <RefreshCw className="h-4 w-4" />
            Refresh
          </button>
        </div>
      </div>

      <div className="flex flex-wrap gap-2 mb-4">
        <button
          className={`px-3 py-1.5 text-sm rounded-md border ${
            messageFilterStatus === "all"
              ? "bg-primary text-white border-primary"
              : "bg-white text-gray-700 border-gray-200 hover:bg-gray-50"
          }`}
          onClick={() => setMessageFilterStatus("all")}
        >
          All
        </button>
        <button
          className={`px-3 py-1.5 text-sm rounded-md border ${
            messageFilterStatus === "email"
              ? "bg-primary text-white border-primary"
              : "bg-white text-gray-700 border-gray-200 hover:bg-gray-50"
          }`}
          onClick={() => setMessageFilterStatus("email")}
        >
          Email
        </button>
        <button
          className={`px-3 py-1.5 text-sm rounded-md border ${
            messageFilterStatus === "sms"
              ? "bg-primary text-white border-primary"
              : "bg-white text-gray-700 border-gray-200 hover:bg-gray-50"
          }`}
          onClick={() => setMessageFilterStatus("sms")}
        >
          SMS
        </button>
        <button
          className={`px-3 py-1.5 text-sm rounded-md border ${
            messageFilterStatus === "in-app"
              ? "bg-primary text-white border-primary"
              : "bg-white text-gray-700 border-gray-200 hover:bg-gray-50"
          }`}
          onClick={() => setMessageFilterStatus("in-app")}
        >
          In-App
        </button>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="pb-2 text-left font-medium text-gray-500 text-xs">
                Subject
              </th>
              <th className="pb-2 text-left font-medium text-gray-500 text-xs">
                Type
              </th>
              <th className="pb-2 text-left font-medium text-gray-500 text-xs">
                Recipients
              </th>
              <th className="pb-2 text-left font-medium text-gray-500 text-xs">
                Sent Date
              </th>
              <th className="pb-2 text-center font-medium text-gray-500 text-xs">
                Status
              </th>
              <th className="pb-2 text-center font-medium text-gray-500 text-xs">
                Open Rate
              </th>
              <th className="pb-2 text-right font-medium text-gray-500 text-xs">
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {filteredMessages.length > 0 ? (
              filteredMessages.map((message, i) => (
                <motion.tr
                  key={i}
                  className="border-b border-gray-200"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: i * 0.05 }}
                >
                  <td className="py-3 text-sm font-medium text-gray-900">
                    {message.subject}
                  </td>
                  <td className="py-3 text-sm">
                    <span
                      className={`inline-flex px-2 py-1 text-xs rounded-full ${
                        message.type === "Email"
                          ? "bg-blue-100 text-blue-800"
                          : message.type === "SMS"
                            ? "bg-purple-100 text-purple-800"
                            : "bg-green-100 text-green-800"
                      }`}
                    >
                      {message.type}
                    </span>
                  </td>
                  <td className="py-3 text-sm text-gray-700">
                    {message.recipients}
                  </td>
                  <td className="py-3 text-sm text-gray-700">
                    {message.sentDate}
                  </td>
                  <td className="py-3 text-center">
                    <span className="inline-flex items-center px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Delivered
                    </span>
                  </td>
                  <td className="py-3 text-center font-medium text-gray-900">
                    {message.openRate}
                  </td>
                  <td className="py-3 text-right">
                    <div className="flex justify-end gap-2">
                      <button
                        className="p-1 text-gray-500 hover:text-gray-700"
                        title="View"
                      >
                        <FileText className="h-4 w-4" />
                      </button>
                      <button
                        className="p-1 text-gray-500 hover:text-gray-700"
                        title="Duplicate"
                      >
                        <Copy className="h-4 w-4" />
                      </button>
                      <button
                        className="p-1 text-gray-500 hover:text-gray-700"
                        title="More"
                      >
                        <MoreHorizontal className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </motion.tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className="py-8 text-center">
                  <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
                    <Search className="h-6 w-6 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-1">
                    No messages found
                  </h3>
                  <p className="text-gray-500">
                    No messages match your search criteria. Try adjusting your
                    filters.
                  </p>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      <div className="flex justify-between items-center mt-4 pt-4 border-t border-gray-200">
        <div className="text-sm text-gray-600">
          Showing{" "}
          <span className="font-medium text-gray-900">
            {filteredMessages.length}
          </span>{" "}
          of{" "}
          <span className="font-medium text-gray-900">{messages.length}</span>{" "}
          messages
        </div>
        <div className="flex gap-1">
          <button className="px-3 py-1 text-sm border border-gray-200 rounded-md bg-gray-100 text-gray-600 hover:bg-gray-200">
            Previous
          </button>
          <button className="px-3 py-1 text-sm border border-primary rounded-md bg-primary text-white">
            1
          </button>
          <button className="px-3 py-1 text-sm border border-gray-200 rounded-md bg-gray-100 text-gray-600 hover:bg-gray-200">
            Next
          </button>
        </div>
      </div>
    </AnimatedCard>
  );
}

function Templates() {
  const [templateSearchTerm, setTemplateSearchTerm] = useState("");

  // Mock templates data
  const templates = [
    { id: 1, name: "Shipping Update", type: "Email", lastUsed: "May 10, 2023" },
    {
      id: 2,
      name: "Regulatory Changes",
      type: "Email",
      lastUsed: "Apr 28, 2023",
    },
    { id: 3, name: "Holiday Schedule", type: "SMS", lastUsed: "Dec 15, 2022" },
    {
      id: 4,
      name: "Maintenance Notification",
      type: "Email",
      lastUsed: "May 02, 2023",
    },
    {
      id: 5,
      name: "Welcome Message",
      type: "In-App",
      lastUsed: "May 12, 2023",
    },
    { id: 6, name: "Payment Reminder", type: "SMS", lastUsed: "May 05, 2023" },
  ];

  // Filter templates based on search term
  const filteredTemplates = templates.filter((template) => {
    const matchesSearch =
      !templateSearchTerm ||
      template.name.toLowerCase().includes(templateSearchTerm.toLowerCase()) ||
      template.type.toLowerCase().includes(templateSearchTerm.toLowerCase());

    return matchesSearch;
  });

  return (
    <AnimatedCard>
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-medium text-gray-900">
            Message Templates
          </h3>
          <span className="bg-primary/10 text-primary text-xs px-2 py-1 rounded-full">
            {templates.length} templates
          </span>
        </div>

        <div className="flex flex-wrap items-center gap-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
            <input
              type="text"
              placeholder="Search templates..."
              value={templateSearchTerm}
              onChange={(e) => setTemplateSearchTerm(e.target.value)}
              className="pl-10 pr-10 py-2.5 text-sm border border-gray-200 rounded-lg w-full sm:w-64 bg-white text-gray-900 shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-colors"
            />
            {templateSearchTerm && (
              <button
                onClick={() => setTemplateSearchTerm("")}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 hover:text-gray-600"
              >
                <X size={16} />
              </button>
            )}
          </div>

          <button className="flex items-center gap-1.5 px-3.5 py-2.5 bg-primary text-white rounded-lg text-sm shadow-sm hover:bg-primary/90 transition-colors">
            <PlusCircle className="h-4 w-4" />
            Create Template
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredTemplates.length > 0 ? (
          filteredTemplates.map((template, i) => (
            <motion.div
              key={i}
              className="border border-gray-200 rounded-lg p-4 bg-white hover:bg-gray-50 transition-colors"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: i * 0.05 }}
            >
              <div className="flex justify-between mb-2">
                <h4 className="font-medium text-gray-900">{template.name}</h4>
                <span
                  className={`inline-flex px-2 py-1 text-xs rounded-full ${
                    template.type === "Email"
                      ? "bg-blue-100 text-blue-800"
                      : template.type === "SMS"
                        ? "bg-purple-100 text-purple-800"
                        : "bg-green-100 text-green-800"
                  }`}
                >
                  {template.type}
                </span>
              </div>
              <p className="text-xs text-gray-500 mb-4">
                Last used: {template.lastUsed}
              </p>
              <div className="flex justify-end gap-2 mt-2">
                <button
                  className="p-1 text-gray-500 hover:text-gray-700"
                  title="Edit"
                >
                  <Edit className="h-4 w-4" />
                </button>
                <button
                  className="p-1 text-gray-500 hover:text-gray-700"
                  title="Duplicate"
                >
                  <Copy className="h-4 w-4" />
                </button>
                <button
                  className="p-1 text-gray-500 hover:text-gray-700"
                  title="Delete"
                >
                  <Trash className="h-4 w-4" />
                </button>
              </div>
            </motion.div>
          ))
        ) : (
          <div className="col-span-3 py-12 text-center">
            <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
              <Search className="h-6 w-6 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-1">
              No templates found
            </h3>
            <p className="text-gray-500">
              No templates match your search criteria. Try adjusting your search
              terms.
            </p>
          </div>
        )}
      </div>

      <div className="flex justify-between items-center mt-6 pt-4 border-t border-gray-200">
        <div className="text-sm text-gray-600">
          Showing{" "}
          <span className="font-medium text-gray-900">
            {filteredTemplates.length}
          </span>{" "}
          of{" "}
          <span className="font-medium text-gray-900">{templates.length}</span>{" "}
          templates
        </div>
        <button className="text-sm text-primary hover:text-primary/80 font-medium">
          Import Templates
        </button>
      </div>
    </AnimatedCard>
  );
}

function ScheduledMessages() {
  const [scheduledSearchTerm, setScheduledSearchTerm] = useState("");

  // Mock scheduled messages data
  const scheduledMessages = [
    {
      subject: "Monthly Logistics Update",
      type: "Email",
      recipients: "All Clients",
      scheduledDate: "Jun 01, 2023 - 09:00 AM",
      status: "scheduled",
    },
    {
      subject: "System Upgrade Notice",
      type: "Email",
      recipients: "All Users",
      scheduledDate: "May 28, 2023 - 10:30 PM",
      status: "scheduled",
    },
    {
      subject: "Price Adjustment Notice",
      type: "Email",
      recipients: "Enterprise Clients",
      scheduledDate: "May 25, 2023 - 02:00 PM",
      status: "scheduled",
    },
    {
      subject: "Reminder: Document Submission",
      type: "SMS",
      recipients: "Selected Clients",
      scheduledDate: "May 22, 2023 - 11:00 AM",
      status: "scheduled",
    },
  ];

  // Filter scheduled messages based on search term
  const filteredScheduledMessages = scheduledMessages.filter((message) => {
    const matchesSearch =
      !scheduledSearchTerm ||
      message.subject
        .toLowerCase()
        .includes(scheduledSearchTerm.toLowerCase()) ||
      message.recipients
        .toLowerCase()
        .includes(scheduledSearchTerm.toLowerCase());

    return matchesSearch;
  });

  return (
    <AnimatedCard>
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-medium text-gray-900">
            Scheduled Messages
          </h3>
          <span className="bg-primary/10 text-primary text-xs px-2 py-1 rounded-full">
            {scheduledMessages.length} scheduled
          </span>
        </div>

        <div className="flex flex-wrap items-center gap-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
            <input
              type="text"
              placeholder="Search scheduled..."
              value={scheduledSearchTerm}
              onChange={(e) => setScheduledSearchTerm(e.target.value)}
              className="pl-10 pr-10 py-2.5 text-sm border border-gray-200 rounded-lg w-full sm:w-64 bg-white text-gray-900 shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-colors"
            />
            {scheduledSearchTerm && (
              <button
                onClick={() => setScheduledSearchTerm("")}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 hover:text-gray-600"
              >
                <X size={16} />
              </button>
            )}
          </div>

          <button className="flex items-center gap-1.5 px-3.5 py-2.5 bg-primary text-white rounded-lg text-sm shadow-sm hover:bg-primary/90 transition-colors">
            <Plus className="h-4 w-4" />
            Schedule Message
          </button>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="pb-2 text-left font-medium text-gray-500 text-xs">
                Subject
              </th>
              <th className="pb-2 text-left font-medium text-gray-500 text-xs">
                Type
              </th>
              <th className="pb-2 text-left font-medium text-gray-500 text-xs">
                Recipients
              </th>
              <th className="pb-2 text-left font-medium text-gray-500 text-xs">
                Scheduled For
              </th>
              <th className="pb-2 text-center font-medium text-gray-500 text-xs">
                Status
              </th>
              <th className="pb-2 text-right font-medium text-gray-500 text-xs">
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {filteredScheduledMessages.length > 0 ? (
              filteredScheduledMessages.map((message, i) => (
                <motion.tr
                  key={i}
                  className="border-b border-gray-200"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: i * 0.05 }}
                >
                  <td className="py-3 text-sm font-medium text-gray-900">
                    {message.subject}
                  </td>
                  <td className="py-3 text-sm">
                    <span
                      className={`inline-flex px-2 py-1 text-xs rounded-full ${
                        message.type === "Email"
                          ? "bg-blue-100 text-blue-800"
                          : message.type === "SMS"
                            ? "bg-purple-100 text-purple-800"
                            : "bg-green-100 text-green-800"
                      }`}
                    >
                      {message.type}
                    </span>
                  </td>
                  <td className="py-3 text-sm text-gray-700">
                    {message.recipients}
                  </td>
                  <td className="py-3 text-sm text-gray-700">
                    {message.scheduledDate}
                  </td>
                  <td className="py-3 text-center">
                    <span className="inline-flex items-center px-2 py-1 bg-amber-100 text-amber-800 text-xs rounded-full">
                      <Clock className="h-3 w-3 mr-1" />
                      Scheduled
                    </span>
                  </td>
                  <td className="py-3 text-right">
                    <div className="flex justify-end gap-2">
                      <button
                        className="p-1 text-gray-500 hover:text-gray-700"
                        title="Edit"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        className="p-1 text-gray-500 hover:text-gray-700"
                        title="Send Now"
                      >
                        <Send className="h-4 w-4" />
                      </button>
                      <button
                        className="p-1 text-gray-500 hover:text-gray-700"
                        title="Cancel"
                      >
                        <Trash className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </motion.tr>
              ))
            ) : (
              <tr>
                <td colSpan={6} className="py-8 text-center">
                  <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
                    <Clock className="h-6 w-6 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-1">
                    No scheduled messages
                  </h3>
                  <p className="text-gray-500">
                    You don't have any messages scheduled for delivery.
                  </p>
                  <button className="mt-4 px-4 py-2 bg-primary text-white rounded-md text-sm font-medium">
                    Schedule Message
                  </button>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {filteredScheduledMessages.length > 0 && (
        <div className="flex justify-between items-center mt-4 pt-4 border-t border-gray-200">
          <div className="text-sm text-gray-600">
            Showing{" "}
            <span className="font-medium text-gray-900">
              {filteredScheduledMessages.length}
            </span>{" "}
            of{" "}
            <span className="font-medium text-gray-900">
              {scheduledMessages.length}
            </span>{" "}
            scheduled messages
          </div>
          <div className="flex gap-1">
            <button className="px-3 py-1 text-sm border border-gray-200 rounded-md bg-gray-100 text-gray-600 hover:bg-gray-200">
              Previous
            </button>
            <button className="px-3 py-1 text-sm border border-primary rounded-md bg-primary text-white">
              1
            </button>
            <button className="px-3 py-1 text-sm border border-gray-200 rounded-md bg-gray-100 text-gray-600 hover:bg-gray-200">
              Next
            </button>
          </div>
        </div>
      )}
    </AnimatedCard>
  );
}
