"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON>, <PERSON>, <PERSON>Off, Loader2 } from "lucide-react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
  DialogClose,
} from "@workspace/ui/components/dialog";
import { InputField } from "./FormComponents";
import { slideUpAnimation, springTransition } from "@/lib/motion-config";

interface ChangePasswordFormProps {
  employeeName: string;
  onClose: () => void;
  onSubmit: (data: { newPassword: string }) => Promise<void>;
}

export function ChangePasswordForm({
  employeeName,
  onClose,
  onSubmit,
}: ChangePasswordFormProps) {
  const [formData, setFormData] = useState({
    newPassword: "",
    confirmPassword: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showPassword, setShowPassword] = useState(false);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.newPassword) {
      newErrors.newPassword = "New password is required";
    } else if (formData.newPassword.length < 8) {
      newErrors.newPassword = "Password must be at least 8 characters";
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = "Please confirm the password";
    } else if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      await onSubmit({ newPassword: formData.newPassword });
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md p-0">
        <DialogHeader className="p-6 pb-4">
          <DialogTitle className="text-xl font-semibold text-gray-900">
            Change Password
          </DialogTitle>
          <p className="text-sm text-gray-600 mt-1">
            Changing password for <strong>{employeeName}</strong>
          </p>
        </DialogHeader>

        <motion.div
          className="px-6 pb-6"
          variants={slideUpAnimation}
          initial="hidden"
          animate="visible"
          transition={springTransition}
        >
          <form onSubmit={handleSubmit} className="grid gap-5 py-4">
            <div className="relative">
              <InputField
                id="newPassword"
                label="New Password"
                icon={Key}
                type={showPassword ? "text" : "password"}
                required
                value={formData.newPassword}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    newPassword: e.target.value,
                  }))
                }
                placeholder="Enter new password"
                error={errors.newPassword}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-8 text-gray-400 hover:text-gray-600"
              >
                {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
              </button>
            </div>

            <InputField
              id="confirmPassword"
              label="Confirm Password"
              icon={Key}
              type={showPassword ? "text" : "password"}
              required
              value={formData.confirmPassword}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  confirmPassword: e.target.value,
                }))
              }
              placeholder="Confirm new password"
              error={errors.confirmPassword}
            />

            <DialogFooter className="mt-4 pt-4 border-t border-gray-200 flex gap-2">
              <DialogClose asChild>
                <Button
                  type="button"
                  variant="outline"
                  className="flex-1"
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
              </DialogClose>
              <Button
                type="submit"
                className="flex-1"
                disabled={
                  isSubmitting ||
                  !formData.newPassword ||
                  !formData.confirmPassword
                }
              >
                {isSubmitting ? (
                  <Loader2 size={16} className="animate-spin mr-2" />
                ) : null}
                {isSubmitting ? "Updating..." : "Change Password"}
              </Button>
            </DialogFooter>
          </form>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
