"use client";

import { motion } from "framer-motion";
import { Loader2 } from "lucide-react";
import { Input } from "@workspace/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { cn } from "@workspace/ui/lib/utils";
import {
  fadeAnimation,
  easeTransition,
} from "@/lib/motion-config";

// Animated Input Field Component
export const InputField = ({
  id,
  label,
  icon: Icon,
  error,
  ...props
}: {
  id: string;
  label: string;
  icon?: React.ElementType;
  error?: string;
} & React.InputHTMLAttributes<HTMLInputElement>) => (
  <motion.div
    variants={fadeAnimation}
    initial="hidden"
    animate="visible"
    transition={easeTransition}
  >
    <label
      htmlFor={id}
      className="block text-sm font-medium text-gray-700 mb-1.5"
    >
      {label}
    </label>
    <div className="relative">
      {Icon && (
        <Icon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
      )}
      <Input
        id={id}
        className={cn(
          "w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white text-gray-900 shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all duration-200",
          Icon ? "pl-10" : "",
          error ? "border-red-300 focus:border-red-500 focus:ring-red-200" : ""
        )}
        {...props}
      />
    </div>
    {error && (
      <motion.p
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="mt-1 text-xs text-red-600"
      >
        {error}
      </motion.p>
    )}
  </motion.div>
);

// Animated Select Field Component
export const SelectField = ({
  id,
  label,
  placeholder,
  value,
  options,
  onChange,
  error,
  loading,
}: {
  id: string;
  label: string;
  placeholder?: string;
  value?: string;
  options: { value: string; label: string }[];
  onChange?: (value: string) => void;
  error?: string;
  loading?: boolean;
}) => (
  <motion.div
    variants={fadeAnimation}
    initial="hidden"
    animate="visible"
    transition={easeTransition}
  >
    <label
      htmlFor={id}
      className="block text-sm font-medium text-gray-700 mb-1.5"
    >
      {label}{" "}
      {loading && <Loader2 size={12} className="inline animate-spin ml-1" />}
    </label>
    <Select value={value} onValueChange={onChange} disabled={loading}>
      <SelectTrigger
        id={id}
        className={cn(
          "w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white text-gray-900 shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary data-[placeholder]:text-gray-500 h-10 transition-all duration-200",
          error ? "border-red-300 focus:border-red-500 focus:ring-red-200" : ""
        )}
      >
        <SelectValue placeholder={placeholder ?? "Select..."} />
      </SelectTrigger>
      <SelectContent>
        {options.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
    {error && (
      <motion.p
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="mt-1 text-xs text-red-600"
      >
        {error}
      </motion.p>
    )}
  </motion.div>
);
