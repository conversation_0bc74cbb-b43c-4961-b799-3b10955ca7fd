"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Building, Loader2 } from "lucide-react";
import { Button } from "@workspace/ui/components/button";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
  DialogClose,
} from "@workspace/ui/components/dialog";
import { InputField } from "./FormComponents";
import { slideUpAnimation, springTransition } from "@/lib/motion-config";
import type { Department } from "@/lib/logistics";

interface EditDepartmentFormProps {
  department: Department;
  onClose: () => void;
  onSubmit: (data: { name: string }) => Promise<void>;
}

export function EditDepartmentForm({
  department,
  onClose,
  onSubmit,
}: EditDepartmentFormProps) {
  const [name, setName] = useState(department.name);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");

  const validateForm = () => {
    if (!name.trim()) {
      setError("Department name is required");
      return false;
    }
    if (name.trim().length < 2) {
      setError("Department name must be at least 2 characters");
      return false;
    }
    setError("");
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      await onSubmit({ name: name.trim() });
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md p-0">
        <DialogHeader className="p-6 pb-4">
          <DialogTitle className="text-xl font-semibold text-gray-900">
            Edit Department
          </DialogTitle>
        </DialogHeader>

        <motion.div
          className="px-6 pb-6"
          variants={slideUpAnimation}
          initial="hidden"
          animate="visible"
          transition={springTransition}
        >
          <form onSubmit={handleSubmit} className="grid gap-5 py-4">
            <InputField
              id="editDepartmentName"
              label="Department Name"
              icon={Building}
              required
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter department name"
              error={error}
            />

            <DialogFooter className="mt-4 pt-4 border-t border-gray-200 flex gap-2">
              <DialogClose asChild>
                <Button
                  type="button"
                  variant="outline"
                  className="flex-1"
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
              </DialogClose>
              <Button
                type="submit"
                className="flex-1"
                disabled={isSubmitting || !name.trim()}
              >
                {isSubmitting ? (
                  <Loader2 size={16} className="animate-spin mr-2" />
                ) : null}
                {isSubmitting ? "Updating..." : "Update Department"}
              </Button>
            </DialogFooter>
          </form>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
