"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { UserCircle, Mail, Phone, Loader2 } from "lucide-react";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
  DialogClose,
} from "@workspace/ui/components/dialog";
import { InputField, SelectField } from "./FormComponents";
import { slideUpAnimation, springTransition } from "@/lib/motion-config";
import type { Department, Role } from "@/lib/logistics";

interface AddEmployeeFormProps {
  departments: Department[];
  roles: Role[];
  onClose: () => void;
  onSubmit: (data: {
    name: string;
    email: string;
    phone?: string;
    roleId: string;
  }) => Promise<void>;
}

export function AddEmployeeForm({
  departments,
  roles,
  onClose,
  onSubmit,
}: AddEmployeeFormProps) {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    roleId: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    if (!formData.roleId) {
      newErrors.roleId = "Role selection is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const roleOptions = roles.map((role) => ({
    value: role.id,
    label: role.name,
  }));

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md p-0">
        <DialogHeader className="p-6 pb-4">
          <DialogTitle className="text-xl font-semibold text-gray-900">
            Add New Employee
          </DialogTitle>
        </DialogHeader>

        <motion.div
          className="px-6 pb-6 max-h-[70vh] overflow-y-auto"
          variants={slideUpAnimation}
          initial="hidden"
          animate="visible"
          transition={springTransition}
        >
          <form onSubmit={handleSubmit} className="grid gap-5 py-4">
            <InputField
              id="employeeName"
              label="Full Name"
              icon={UserCircle}
              required
              value={formData.name}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, name: e.target.value }))
              }
              placeholder="Enter full name"
              error={errors.name}
            />

            <InputField
              id="employeeEmail"
              label="Email Address"
              icon={Mail}
              type="email"
              required
              value={formData.email}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, email: e.target.value }))
              }
              placeholder="Enter email address"
              error={errors.email}
            />

            <InputField
              id="employeePhone"
              label="Phone Number"
              icon={Phone}
              type="tel"
              value={formData.phone}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, phone: e.target.value }))
              }
              placeholder="Enter phone number"
              error={errors.phone}
            />

            <SelectField
              id="employeeRole"
              label="Role"
              placeholder="Select a role"
              value={formData.roleId}
              options={roleOptions}
              onChange={(value) =>
                setFormData((prev) => ({ ...prev, roleId: value }))
              }
              error={errors.roleId}
            />

            <DialogFooter className="mt-4 pt-4 border-t border-gray-200 flex gap-2">
              <DialogClose asChild>
                <Button
                  type="button"
                  variant="outline"
                  className="flex-1"
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
              </DialogClose>
              <Button
                type="submit"
                className="flex-1"
                disabled={
                  isSubmitting ||
                  !formData.name ||
                  !formData.email ||
                  !formData.roleId
                }
              >
                {isSubmitting ? (
                  <Loader2 size={16} className="animate-spin mr-2" />
                ) : null}
                {isSubmitting ? "Adding..." : "Add Employee"}
              </Button>
            </DialogFooter>
          </form>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
