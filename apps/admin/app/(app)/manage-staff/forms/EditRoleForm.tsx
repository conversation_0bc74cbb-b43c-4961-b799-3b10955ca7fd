"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Loader2 } from "lucide-react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
  DialogClose,
} from "@workspace/ui/components/dialog";
import { InputField, SelectField } from "./FormComponents";
import { slideUpAnimation, springTransition } from "@/lib/motion-config";
import type { Role, Department } from "@/lib/logistics";

interface EditRoleFormProps {
  role: Role;
  departments: Department[];
  onClose: () => void;
  onSubmit: (data: { name: string; departmentId: string }) => Promise<void>;
}

export function EditRoleForm({
  role,
  departments,
  onClose,
  onSubmit,
}: EditRoleFormProps) {
  const [formData, setFormData] = useState({
    name: role.name,
    departmentId: role.department_id || "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Role name is required";
    }

    if (!formData.departmentId) {
      newErrors.departmentId = "Department selection is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const departmentOptions = departments.map((dept) => ({
    value: dept.id,
    label: dept.name,
  }));

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md p-0">
        <DialogHeader className="p-6 pb-4">
          <DialogTitle className="text-xl font-semibold text-gray-900">
            Edit Role
          </DialogTitle>
        </DialogHeader>

        <motion.div
          className="px-6 pb-6"
          variants={slideUpAnimation}
          initial="hidden"
          animate="visible"
          transition={springTransition}
        >
          <form onSubmit={handleSubmit} className="grid gap-5 py-4">
            <InputField
              id="editRoleName"
              label="Role Name"
              icon={BadgeCheck}
              required
              value={formData.name}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, name: e.target.value }))
              }
              placeholder="Enter role name"
              error={errors.name}
            />

            <SelectField
              id="editRoleDepartment"
              label="Department"
              placeholder="Select a department"
              value={formData.departmentId}
              options={departmentOptions}
              onChange={(value) =>
                setFormData((prev) => ({ ...prev, departmentId: value }))
              }
              error={errors.departmentId}
            />

            <DialogFooter className="mt-4 pt-4 border-t border-gray-200 flex gap-2">
              <DialogClose asChild>
                <Button
                  type="button"
                  variant="outline"
                  className="flex-1"
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
              </DialogClose>
              <Button
                type="submit"
                className="flex-1"
                disabled={
                  isSubmitting ||
                  !formData.name.trim() ||
                  !formData.departmentId
                }
              >
                {isSubmitting ? (
                  <Loader2 size={16} className="animate-spin mr-2" />
                ) : null}
                {isSubmitting ? "Updating..." : "Update Role"}
              </Button>
            </DialogFooter>
          </form>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
