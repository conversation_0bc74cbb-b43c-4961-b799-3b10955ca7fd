"use client";

import { memo } from "react";
import { motion } from "framer-motion";
import {
  Download,
  Filter,
  Clock,
  UserPlus,
  Edit,
  UserCog,
  Trash,
  Building,
  Loader2,
} from "lucide-react";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, Too<PERSON><PERSON> } from "recharts";
import { Listing } from "@/modules/listing";
import type { Department } from "@/lib/logistics";
import { AnimatedCard } from "@/components/animated-card";

// Colors for charts
const COLORS = [
  "#0088FE",
  "#00C49F",
  "#FFBB28",
  "#FF8042",
  "#8884d8",
  "#82ca9d",
];

// Activity interface
interface Activity {
  id: string;
  action: string;
  user: string;
  time: string;
  type: "add" | "update" | "permission" | "delete";
}

interface StaffOverviewTabProps {
  loading: boolean;
  departmentData: { name: string; value: number; color: string }[];
  recentActivities: Activity[];
  departments: Department[];
  onRefresh: () => void;
  refreshing: boolean;
}

/**
 * Staff Overview Tab Component
 *
 * Displays overview information including department distribution,
 * recent activities, and department summary using the Listing module.
 */
export const StaffOverviewTab = memo<StaffOverviewTabProps>(
  ({
    loading,
    departmentData,
    recentActivities,
    departments,
    onRefresh,
    refreshing,
  }) => {
    return (
      <div className="space-y-6">
        {/* Department Distribution and Recent Activities */}
        <div className="grid gap-6 grid-cols-1 lg:grid-cols-3">
          {/* Department Distribution Chart */}
          <AnimatedCard className="lg:col-span-2">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-lg font-medium text-gray-900">
                  Department Distribution
                </h3>
                <p className="text-sm text-gray-500">
                  Staff allocation across departments
                </p>
              </div>
              <button className="text-xs px-3 py-1.5 border border-gray-200 rounded-md bg-white text-gray-700 hover:bg-gray-50 flex items-center gap-1">
                <Download className="h-3 w-3" /> Export
              </button>
            </div>
            <div className="h-80 border-t border-gray-200 pt-4 bg-white">
              {loading ? (
                <div className="flex items-center justify-center h-full">
                  <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
                </div>
              ) : departmentData.length > 0 ? (
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={departmentData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={120}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) =>
                        `${name} ${(percent * 100).toFixed(0)}%`
                      }
                    >
                      {departmentData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => `${value} employees`} />
                  </PieChart>
                </ResponsiveContainer>
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  No department data available
                </div>
              )}
            </div>
          </AnimatedCard>

          {/* Recent Activities */}
          <AnimatedCard>
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                Recent Activities
              </h3>
              <button className="text-xs px-3 py-1.5 border border-gray-200 rounded-md bg-white text-gray-700 hover:bg-gray-50 flex items-center gap-1">
                <Filter className="h-3 w-3" /> Filter
              </button>
            </div>
            <div className="space-y-3 border-t border-gray-200 pt-4">
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
                </div>
              ) : recentActivities.length > 0 ? (
                recentActivities.map((activity) => (
                  <motion.div
                    key={activity.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="flex justify-between items-center p-3 border border-gray-100 rounded-lg bg-white hover:bg-gray-50"
                  >
                    <div className="flex items-center gap-3">
                      <div
                        className={`p-2 rounded-md ${
                          activity.type === "add"
                            ? "bg-green-100 text-green-600"
                            : activity.type === "update"
                              ? "bg-blue-100 text-blue-600"
                              : activity.type === "permission"
                                ? "bg-amber-100 text-amber-600"
                                : "bg-red-100 text-red-600"
                        }`}
                      >
                        {activity.type === "add" ? (
                          <UserPlus className="h-4 w-4" />
                        ) : activity.type === "update" ? (
                          <Edit className="h-4 w-4" />
                        ) : activity.type === "permission" ? (
                          <UserCog className="h-4 w-4" />
                        ) : (
                          <Trash className="h-4 w-4" />
                        )}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">
                          {activity.action}
                        </p>
                        <div className="flex items-center gap-1 mt-1">
                          <span className="text-xs text-gray-500">
                            {activity.user}
                          </span>
                          <span className="text-xs text-gray-500">•</span>
                          <span className="text-xs text-gray-500">
                            {activity.time}
                          </span>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))
              ) : (
                <div className="text-center py-8">
                  <Clock className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                  <p className="text-gray-500">No recent activities</p>
                </div>
              )}
              {!loading && recentActivities.length > 0 && (
                <button className="w-full mt-2 text-center text-sm text-primary hover:text-primary/80 font-medium">
                  View all activities
                </button>
              )}
            </div>
          </AnimatedCard>
        </div>

        {/* Department Summary */}
        <AnimatedCard>
          <div className="flex justify-between items-center mb-4">
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                Department Summary
              </h3>
              <p className="text-sm text-gray-500">
                Quick overview of all departments
              </p>
            </div>
          </div>
          <div className="border-t border-gray-200 pt-4 bg-white">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
              </div>
            ) : departments.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                {departments.map((dept, i) => {
                  const employeeCount =
                    departmentData.find((d) => d.name === dept.name)?.value ||
                    0;
                  return (
                    <motion.div
                      key={dept.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: i * 0.1 }}
                      className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
                    >
                      <div
                        className="h-2 rounded-t-md -mt-4 -mx-4 mb-3"
                        style={{ backgroundColor: COLORS[i % COLORS.length] }}
                      />
                      <h4 className="font-medium text-gray-900">{dept.name}</h4>
                      <div className="flex items-center justify-between mt-2">
                        <div>
                          <p className="text-xs text-gray-500">Employees</p>
                          <p className="font-medium">{employeeCount}</p>
                        </div>
                        <div className="text-right">
                          <p className="text-xs text-gray-500">Status</p>
                          <p className="font-medium text-green-600">Active</p>
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-8">
                <Building className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                <p className="text-gray-500">No departments configured</p>
              </div>
            )}
          </div>
        </AnimatedCard>
      </div>
    );
  }
);

StaffOverviewTab.displayName = "StaffOverviewTab";
