"use client";

import { memo } from "react";
import { motion } from "framer-motion";
import {
  Plus,
  Shield,
  Edit,
  Trash,
  Settings,
  Loader2,
  Building,
  Users,
  MoreHorizontal,
} from "lucide-react";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { Listing } from "@/modules/listing";
import {
  ProtectedCreateButton,
  ProtectedEditButton,
  ProtectedDeleteButton,
} from "@/lib/components/RBACWrapper";
import type { Role, Department, UserWithAccount } from "@/lib/logistics";

interface RolesPermissionsTabProps {
  roles: Role[];
  departments: Department[];
  employees: UserWithAccount[];
  loading: boolean;
  viewMode: "cards" | "table";
  onViewModeChange: (mode: "cards" | "table") => void;
  currentPage: number;
  onPageChange: (page: number) => void;
  itemsPerPage: number;
  roleFilter: string;
  setRoleFilter: (filter: string) => void;
  onRefresh: () => void;
  onAddRole: () => void;
  onEditRole: (role: Role) => void;
  onManagePermissions: (role: Role) => void;
  onDeleteRole: (role: Role) => void;
  operationLoading: string | null;
  refreshing: boolean;
}

/**
 * Roles & Permissions Tab Component
 *
 * Displays and manages role data with permissions management
 * using the Listing module with card-based layout.
 */
export const RolesPermissionsTab = memo<RolesPermissionsTabProps>(
  ({
    roles,
    roleFilter,
    setRoleFilter,
    departments,
    employees,
    loading,
    viewMode,
    onViewModeChange,
    currentPage,
    onPageChange,
    itemsPerPage,
    onRefresh,
    onAddRole,
    onEditRole,
    onManagePermissions,
    onDeleteRole,
    operationLoading,
    refreshing,
  }) => {
    // Helper function to get department name
    const getDepartmentName = (role: Role) => {
      const department = departments.find((d) => d.id === role.department_id);
      return department?.name || "No department";
    };

    // Helper function to calculate user count per role
    const getUserCountForRole = (roleId: string) => {
      return employees.filter(
        (employee) =>
          employee.accounts?.role_id === roleId &&
          employee.accounts?.status === "ACTIVE"
      ).length;
    };

    const categories = [
      { key: "active", label: "Active" },
      { key: "inactive", label: "Inactive" },
    ];

    return (
      <Listing>
        <Listing.Controls
          entity="roles"
          length={roles.length}
          viewMode={viewMode}
          categoryFilter={roleFilter}
          onCategoryFilterChange={setRoleFilter}
          categories={categories}
          onViewModeChange={onViewModeChange}
          actions={
            <ProtectedCreateButton entity="roles">
              <Button onClick={onAddRole}>
                <Plus size={16} />
                Add Role
              </Button>
            </ProtectedCreateButton>
          }
        />

        {viewMode === "cards" ? (
          <Listing.Cards
            data={roles}
            loading={loading}
            columns="grid-cols-3"
            renderCard={(role: Role, index: number) => (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow h-full min-h-[18em] max-h-[19em] flex flex-col justify-between items-start"
              >
                {/* Header */}
                <div className="w-full flex justify-between items-start mb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-3 rounded-lg bg-purple-50">
                      <Shield className="h-6 w-6 text-purple-500" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">
                        {role.name}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {getDepartmentName(role)}
                      </p>
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <button
                        className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-50"
                        title="More actions"
                      >
                        <MoreHorizontal size={16} />
                      </button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() => onManagePermissions(role)}
                        disabled={operationLoading === `permissions-${role.id}`}
                      >
                        {operationLoading === `permissions-${role.id}` ? (
                          <Loader2 size={16} className="animate-spin" />
                        ) : (
                          <Settings size={16} />
                        )}
                        Manage Permissions
                      </DropdownMenuItem>
                      <ProtectedEditButton entity="roles">
                        <DropdownMenuItem
                          onClick={() => onEditRole(role)}
                          disabled={operationLoading === `edit-${role.id}`}
                        >
                          {operationLoading === `edit-${role.id}` ? (
                            <Loader2 size={16} className="animate-spin" />
                          ) : (
                            <Edit size={16} />
                          )}
                          Edit Role
                        </DropdownMenuItem>
                      </ProtectedEditButton>
                      <ProtectedDeleteButton entity="roles">
                        <DropdownMenuItem
                          variant="destructive"
                          onClick={() => onDeleteRole(role)}
                          disabled={operationLoading === `delete-${role.id}`}
                        >
                          {operationLoading === `delete-${role.id}` ? (
                            <Loader2 size={16} className="animate-spin" />
                          ) : (
                            <Trash size={16} />
                          )}
                          Delete Role
                        </DropdownMenuItem>
                      </ProtectedDeleteButton>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                {/* Status */}
                <div className="mb-4">
                  <span
                    className={`inline-flex items-center px-2 py-1 text-xs rounded-full ${
                      role.status === "ACTIVE"
                        ? "bg-green-100 text-green-800"
                        : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {role.status}
                  </span>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-100">
                  <div>
                    <p className="text-xs text-gray-500">Users</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {getUserCountForRole(role.id)}
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Department</p>
                    <p className="text-sm font-medium text-gray-900">
                      {getDepartmentName(role)}
                    </p>
                  </div>
                </div>

                {/* Created Date */}
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <p className="text-xs text-gray-500">
                    Created:{" "}
                    {role.created_at
                      ? new Date(role.created_at).toLocaleDateString()
                      : "Unknown"}
                  </p>
                </div>
              </motion.div>
            )}
            emptyState={
              <div className="py-12 text-center">
                <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
                  <Shield className="h-6 w-6 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-1">
                  No roles found
                </h3>
                <p className="text-sm text-gray-500">
                  Get started by creating your first role.
                </p>
                <ProtectedCreateButton entity="roles">
                  <Button onClick={onAddRole} className="mt-4">
                    <Plus size={16} />
                    Add Role
                  </Button>
                </ProtectedCreateButton>
              </div>
            }
          />
        ) : (
          <Listing.Table
            data={roles}
            loading={loading}
            pagination={{
              currentPage,
              totalPages: Math.ceil(roles.length / itemsPerPage),
              totalItems: roles.length,
              itemsPerPage,
              onPageChange: onPageChange,
            }}
            columns={[
              {
                key: "name",
                label: "Role Name",
                render: (role: Role) => (
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-purple-50">
                      <Shield className="h-4 w-4 text-purple-500" />
                    </div>
                    <span className="font-medium">{role.name}</span>
                  </div>
                ),
              },
              {
                key: "department",
                label: "Department",
                render: (role: Role) => getDepartmentName(role),
              },
              {
                key: "status",
                label: "Status",
                render: (role: Role) => (
                  <span
                    className={`inline-flex items-center px-2 py-1 text-xs rounded-full ${
                      role.status === "ACTIVE"
                        ? "bg-green-100 text-green-800"
                        : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {role.status}
                  </span>
                ),
              },
              {
                key: "created_at",
                label: "Created",
                render: (role: Role) =>
                  role.created_at
                    ? new Date(role.created_at).toLocaleDateString()
                    : "Unknown",
              },
              {
                key: "actions",
                label: "Actions",
                className: "w-32",
                render: (role: Role) => (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <button
                        className="p-1 text-gray-400 hover:text-gray-600 rounded"
                        title="More actions"
                      >
                        <MoreHorizontal size={14} />
                      </button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() => onManagePermissions(role)}
                        disabled={operationLoading === `permissions-${role.id}`}
                      >
                        {operationLoading === `permissions-${role.id}` ? (
                          <Loader2 size={14} className="animate-spin" />
                        ) : (
                          <Settings size={14} />
                        )}
                        Manage Permissions
                      </DropdownMenuItem>
                      <ProtectedEditButton entity="roles">
                        <DropdownMenuItem
                          onClick={() => onEditRole(role)}
                          disabled={operationLoading === `edit-${role.id}`}
                        >
                          {operationLoading === `edit-${role.id}` ? (
                            <Loader2 size={14} className="animate-spin" />
                          ) : (
                            <Edit size={14} />
                          )}
                          Edit Role
                        </DropdownMenuItem>
                      </ProtectedEditButton>
                      <ProtectedDeleteButton entity="roles">
                        <DropdownMenuItem
                          variant="destructive"
                          onClick={() => onDeleteRole(role)}
                          disabled={operationLoading === `delete-${role.id}`}
                        >
                          {operationLoading === `delete-${role.id}` ? (
                            <Loader2 size={14} className="animate-spin" />
                          ) : (
                            <Trash size={14} />
                          )}
                          Delete Role
                        </DropdownMenuItem>
                      </ProtectedDeleteButton>
                    </DropdownMenuContent>
                  </DropdownMenu>
                ),
              },
            ]}
            emptyState={
              <div className="py-12 text-center">
                <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
                  <Shield className="h-6 w-6 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-1">
                  No roles found
                </h3>
                <p className="text-sm text-gray-500">
                  Get started by creating your first role.
                </p>
                <ProtectedCreateButton entity="roles">
                  <Button onClick={onAddRole} className="mt-4">
                    <Plus size={16} />
                    Add Role
                  </Button>
                </ProtectedCreateButton>
              </div>
            }
          />
        )}
      </Listing>
    );
  }
);

RolesPermissionsTab.displayName = "RolesPermissionsTab";
