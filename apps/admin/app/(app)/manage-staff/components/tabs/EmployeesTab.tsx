"use client";

import { memo } from "react";
import { motion } from "framer-motion";
import {
  UserPlus,
  Ellipsis,
  Search,
  Edit,
  Trash,
  Key,
  UserCheck,
  UserX,
  Loader2,
  Mail,
  Phone,
  User,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@workspace/ui/components/dropdown-menu";
import { Button } from "@workspace/ui/components/button";
import { Listing } from "@/modules/listing";
import {
  ProtectedCreateButton,
  ProtectedEditButton,
  ProtectedDeleteButton,
} from "@/lib/components/RBACWrapper";
import type { UserWithAccount } from "@/lib/logistics";

// Employee Card Component
interface EmployeeCardProps {
  employee: UserWithAccount;
  index: number;
  onEdit: (employee: UserWithAccount) => void;
  onDelete: (employee: UserWithAccount) => void;
  onChangePassword: (employee: UserWithAccount) => void;
  operationLoading: string | null;
}

const EmployeeCard = memo<EmployeeCardProps>(
  ({
    employee,
    index,
    onEdit,
    onDelete,
    onChangePassword,
    operationLoading,
  }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.05 }}
      className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow h-full min-h-[20em] max-h-[21em] flex flex-col justify-between items-start"
    >
      {/* Header */}
      <div className="w-full flex justify-between items-start">
        <div className="flex items-center gap-3">
          <div className="p-3 rounded-lg bg-blue-50">
            <User className="h-6 w-6 text-blue-500" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">{employee.name}</h3>
            <p className="text-sm text-gray-500">
              {employee.accounts?.roles?.name || "No role assigned"}
            </p>
          </div>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <Ellipsis className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <ProtectedEditButton entity="users">
              <DropdownMenuItem onClick={() => onEdit(employee)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
            </ProtectedEditButton>
            <DropdownMenuItem onClick={() => onChangePassword(employee)}>
              <Key className="mr-2 h-4 w-4" />
              Change Password
            </DropdownMenuItem>
            <ProtectedDeleteButton entity="users">
              <DropdownMenuItem onClick={() => onDelete(employee)}>
                <Trash className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </ProtectedDeleteButton>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Status */}
      <div className="mb-4">
        <span
          className={`inline-flex items-center px-2 py-1 text-xs rounded-full ${
            employee.accounts?.status === "ACTIVE"
              ? "bg-green-100 text-green-800"
              : employee.accounts?.status === "PENDING"
                ? "bg-yellow-100 text-yellow-800"
                : "bg-gray-100 text-gray-800"
          }`}
        >
          {employee.accounts?.status || "Unknown"}
        </span>
      </div>

      {/* Contact Info */}
      <div className="space-y-2 mb-4">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Mail className="h-4 w-4" />
          <span>{employee.accounts?.email || "No email"}</span>
        </div>
        {employee.phone && (
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Phone className="h-4 w-4" />
            <span>{employee.phone}</span>
          </div>
        )}
      </div>

      {/* Department */}
      <div className="pt-4 border-t border-gray-100">
        <p className="text-xs text-gray-500">Department</p>
        <p className="text-sm font-medium text-gray-900">
          {employee.accounts?.roles?.departments?.name || "Unassigned"}
        </p>
      </div>
    </motion.div>
  )
);

EmployeeCard.displayName = "EmployeeCard";

interface EmployeesTabProps {
  employees: UserWithAccount[];
  loading: boolean;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  employeeFilter: string;
  setEmployeeFilter: (filter: string) => void;
  viewMode: "cards" | "table";
  onViewModeChange: (mode: "cards" | "table") => void;
  currentPage: number;
  onPageChange: (page: number) => void;
  itemsPerPage: number;
  onRefresh: () => void;
  onAddEmployee: () => void;
  onEditEmployee: (employee: UserWithAccount) => void;
  onDeleteEmployee: (employee: UserWithAccount) => void;
  onChangePassword: (employee: UserWithAccount) => void;
  onToggleStatus: (employee: UserWithAccount) => void;
  operationLoading: string | null;
  refreshing: boolean;
}

/**
 * Employees Tab Component
 *
 * Displays and manages employee data using the Listing module
 * with table view, search, filtering, and CRUD operations.
 */
export const EmployeesTab = memo<EmployeesTabProps>(
  ({
    employees,
    loading,
    searchTerm,
    setSearchTerm,
    employeeFilter,
    setEmployeeFilter,
    viewMode,
    onViewModeChange,
    currentPage,
    onPageChange,
    itemsPerPage,
    onRefresh,
    onAddEmployee,
    onEditEmployee,
    onDeleteEmployee,
    onChangePassword,
    onToggleStatus,
    operationLoading,
    refreshing,
  }) => {
    const columns = [
      {
        key: "name",
        label: "Name",
        render: (employee: UserWithAccount) => (
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-800 font-medium text-sm">
              {employee.name
                ? employee.name
                    .split(" ")
                    .map((n) => n[0])
                    .join("")
                    .toUpperCase()
                : "U"}
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900">
                {employee.name || "Unknown"}
              </p>
              <p className="text-xs text-gray-500">
                {employee.accounts?.email || "No email"}
              </p>
            </div>
          </div>
        ),
      },
      {
        key: "role",
        label: "Role",
        render: (employee: UserWithAccount) => (
          <span className="text-sm text-gray-700">
            {employee.accounts?.roles?.name || "No role"}
          </span>
        ),
      },
      {
        key: "department",
        label: "Department",
        render: (employee: UserWithAccount) => (
          <span className="text-sm text-gray-700">
            {employee.accounts?.roles?.departments?.name || "Unassigned"}
          </span>
        ),
      },
      {
        key: "status",
        label: "Status",
        align: "center" as const,
        render: (employee: UserWithAccount) => (
          <span
            className={`inline-flex items-center px-2 py-1 text-xs rounded-full ${
              employee.accounts?.status === "ACTIVE"
                ? "bg-green-100 text-green-800"
                : employee.accounts?.status === "PENDING"
                  ? "bg-amber-100 text-amber-800"
                  : "bg-gray-100 text-gray-800"
            }`}
          >
            {employee.accounts?.status || "Unknown"}
          </span>
        ),
      },
      {
        key: "actions",
        label: "Actions",
        align: "right" as const,
        render: (employee: UserWithAccount) => (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button
                className="p-1 text-gray-400 hover:text-gray-600"
                title="More Actions"
              >
                <Ellipsis size={16} />
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <ProtectedEditButton entity="users">
                <DropdownMenuItem
                  onClick={() => onEditEmployee(employee)}
                  className="cursor-pointer"
                >
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Employee
                </DropdownMenuItem>
              </ProtectedEditButton>
              <DropdownMenuItem
                onClick={() => onChangePassword(employee)}
                className="cursor-pointer"
              >
                <Key className="mr-2 h-4 w-4" />
                Change Password
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onDeleteEmployee(employee)}
                className="cursor-pointer text-red-600"
              >
                <Trash className="mr-2 h-4 w-4" />
                Delete Employee
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        ),
      },
    ];

    const categories = [
      { key: "active", label: "Active" },
      { key: "inactive", label: "Inactive" },
    ];

    return (
      <Listing>
        <Listing.Filters
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          onRefresh={onRefresh}
          loading={refreshing}
        />

        <Listing.Controls
          entity="employees"
          length={employees.length}
          viewMode={viewMode}
          onViewModeChange={onViewModeChange}
          categoryFilter={employeeFilter}
          onCategoryFilterChange={setEmployeeFilter}
          categories={categories}
          actions={
            <ProtectedCreateButton entity="users">
              <Button onClick={onAddEmployee}>
                <UserPlus size={16} />
                Add Employee
              </Button>
            </ProtectedCreateButton>
          }
        />

        {viewMode === "cards" ? (
          <Listing.Cards
            data={employees}
            loading={loading}
            columns="grid-cols-3"
            renderCard={(employee: UserWithAccount, index: number) => (
              <EmployeeCard
                key={employee.id}
                employee={employee}
                index={index}
                onEdit={onEditEmployee}
                onDelete={onDeleteEmployee}
                onChangePassword={onChangePassword}
                operationLoading={operationLoading}
              />
            )}
            emptyState={
              <div className="py-12 text-center">
                <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
                  <Search className="h-6 w-6 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-1">
                  No employees found
                </h3>
                <p className="text-sm text-gray-500">
                  {searchTerm
                    ? "No employees match your search criteria."
                    : "No employees available."}
                </p>
              </div>
            }
          />
        ) : (
          <Listing.Table
            data={employees}
            columns={columns}
            loading={loading}
            pagination={{
              currentPage,
              totalPages: Math.ceil(employees.length / itemsPerPage),
              totalItems: employees.length,
              itemsPerPage,
              onPageChange: onPageChange,
            }}
            emptyState={
              <div className="py-12 text-center">
                <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
                  <Search className="h-6 w-6 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-1">
                  No employees found
                </h3>
                <p className="text-sm text-gray-500">
                  {searchTerm
                    ? "No employees match your search criteria."
                    : "No employees available."}
                </p>
              </div>
            }
          />
        )}
      </Listing>
    );
  }
);

EmployeesTab.displayName = "EmployeesTab";
