"use client";

import { memo } from "react";
import { motion } from "framer-motion";
import {
  Plus,
  Building,
  Edit,
  Trash,
  Loader2,
  Search,
  MoreHorizontal,
} from "lucide-react";
import { Button } from "@workspace/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { Listing } from "@/modules/listing";
import {
  ProtectedCreateButton,
  ProtectedEditButton,
  ProtectedDeleteButton,
} from "@/lib/components/RBACWrapper";
import type { Department, UserWithAccount, Role } from "@/lib/logistics";

interface DepartmentsTabProps {
  departments: Department[];
  employees: UserWithAccount[];
  roles: Role[];
  loading: boolean;
  viewMode: "cards" | "table";
  onViewModeChange: (mode: "cards" | "table") => void;
  currentPage: number;
  onPageChange: (page: number) => void;
  itemsPerPage: number;
  departmentFilter: string;
  setDepartmentFilter: (filter: string) => void;
  onRefresh: () => void;
  onAddDepartment: () => void;
  onEditDepartment: (department: Department) => void;
  operationLoading: string | null;
  refreshing: boolean;
}

/**
 * Departments Tab Component
 *
 * Displays and manages department data using the Listing module
 * with card-based layout for better visual organization.
 */
export const DepartmentsTab = memo<DepartmentsTabProps>(
  ({
    departments,
    employees,
    roles,
    departmentFilter,
    setDepartmentFilter,
    loading,
    viewMode,
    onViewModeChange,
    currentPage,
    onPageChange,
    itemsPerPage,
    onRefresh,
    onAddDepartment,
    onEditDepartment,
    operationLoading,
    refreshing,
  }) => {
    // Helper functions to calculate counts
    const getEmployeeCountForDepartment = (departmentId: string) => {
      return employees.filter(
        (employee) =>
          employee.accounts?.roles?.departments?.id === departmentId &&
          employee.accounts?.status === "ACTIVE"
      ).length;
    };

    const getRoleCountForDepartment = (departmentId: string) => {
      return roles.filter(
        (role) =>
          role.department_id === departmentId && role.status === "ACTIVE"
      ).length;
    };

    const categories = [
      { key: "active", label: "Active" },
      { key: "inactive", label: "Inactive" },
    ];

    return (
      <Listing>
        <Listing.Controls
          entity="departments"
          categoryFilter={departmentFilter}
          onCategoryFilterChange={setDepartmentFilter}
          categories={categories}
          length={departments.length}
          viewMode={viewMode}
          onViewModeChange={onViewModeChange}
          actions={
            <ProtectedCreateButton entity="departments">
              <Button onClick={onAddDepartment}>
                <Plus size={16} />
                Add Department
              </Button>
            </ProtectedCreateButton>
          }
        />

        {viewMode === "cards" ? (
          <Listing.Cards
            data={departments}
            loading={loading}
            columns="grid-cols-3"
            renderCard={(department: Department, index: number) => (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow h-full min-h-[17em] max-h-[18em] flex flex-col justify-between items-start"
              >
                {/* Header */}
                <div className="w-full flex justify-between items-start mb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-3 rounded-lg bg-blue-50">
                      <Building className="h-6 w-6 text-blue-500" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">
                        {department.name}
                      </h3>
                      <p className="text-sm text-gray-500">Department</p>
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <button
                        className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-50"
                        title="More actions"
                      >
                        <MoreHorizontal size={16} />
                      </button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <ProtectedEditButton entity="departments">
                        <DropdownMenuItem
                          onClick={() => onEditDepartment(department)}
                          disabled={
                            operationLoading === `edit-${department.id}`
                          }
                        >
                          {operationLoading === `edit-${department.id}` ? (
                            <Loader2 size={16} className="animate-spin" />
                          ) : (
                            <Edit size={16} />
                          )}
                          Edit Department
                        </DropdownMenuItem>
                      </ProtectedEditButton>
                      <ProtectedDeleteButton entity="departments">
                        <DropdownMenuItem
                          variant="destructive"
                          disabled={
                            operationLoading === `delete-${department.id}`
                          }
                        >
                          {operationLoading === `delete-${department.id}` ? (
                            <Loader2 size={16} className="animate-spin" />
                          ) : (
                            <Trash size={16} />
                          )}
                          Delete Department
                        </DropdownMenuItem>
                      </ProtectedDeleteButton>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                {/* Status */}
                <div className="mb-4">
                  <span
                    className={`inline-flex items-center px-2 py-1 text-xs rounded-full ${
                      department.status === "ACTIVE"
                        ? "bg-green-100 text-green-800"
                        : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {department.status}
                  </span>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-100">
                  <div>
                    <p className="text-xs text-gray-500">Employees</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {getEmployeeCountForDepartment(department.id)}
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Roles</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {getRoleCountForDepartment(department.id)}
                    </p>
                  </div>
                </div>

                {/* Created Date */}
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <p className="text-xs text-gray-500">
                    Created:{" "}
                    {department.created_at
                      ? new Date(department.created_at).toLocaleDateString()
                      : "Unknown"}
                  </p>
                </div>
              </motion.div>
            )}
            emptyState={
              <div className="py-12 text-center">
                <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
                  <Building className="h-6 w-6 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-1">
                  No departments found
                </h3>
                <p className="text-sm text-gray-500">
                  Get started by creating your first department.
                </p>
                <ProtectedCreateButton entity="departments">
                  <Button onClick={onAddDepartment} className="mt-4">
                    <Plus size={16} />
                    Add Department
                  </Button>
                </ProtectedCreateButton>
              </div>
            }
          />
        ) : (
          <Listing.Table
            data={departments}
            loading={loading}
            pagination={{
              currentPage,
              totalPages: Math.ceil(departments.length / itemsPerPage),
              totalItems: departments.length,
              itemsPerPage,
              onPageChange: onPageChange,
            }}
            columns={[
              {
                key: "name",
                label: "Department Name",
                render: (department: Department) => (
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-blue-50">
                      <Building className="h-4 w-4 text-blue-500" />
                    </div>
                    <span className="font-medium">{department.name}</span>
                  </div>
                ),
              },
              {
                key: "status",
                label: "Status",
                render: (department: Department) => (
                  <span
                    className={`inline-flex items-center px-2 py-1 text-xs rounded-full ${
                      department.status === "ACTIVE"
                        ? "bg-green-100 text-green-800"
                        : "bg-gray-100 text-gray-800"
                    }`}
                  >
                    {department.status}
                  </span>
                ),
              },
              {
                key: "created_at",
                label: "Created",
                render: (department: Department) =>
                  department.created_at
                    ? new Date(department.created_at).toLocaleDateString()
                    : "Unknown",
              },
              {
                key: "actions",
                label: "Actions",
                className: "w-24",
                render: (department: Department) => (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <button
                        className="p-1 text-gray-400 hover:text-gray-600 rounded"
                        title="More actions"
                      >
                        <MoreHorizontal size={14} />
                      </button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <ProtectedEditButton entity="departments">
                        <DropdownMenuItem
                          onClick={() => onEditDepartment(department)}
                          disabled={
                            operationLoading === `edit-${department.id}`
                          }
                        >
                          {operationLoading === `edit-${department.id}` ? (
                            <Loader2 size={14} className="animate-spin" />
                          ) : (
                            <Edit size={14} />
                          )}
                          Edit Department
                        </DropdownMenuItem>
                      </ProtectedEditButton>
                      <ProtectedDeleteButton entity="departments">
                        <DropdownMenuItem
                          variant="destructive"
                          disabled={
                            operationLoading === `delete-${department.id}`
                          }
                        >
                          {operationLoading === `delete-${department.id}` ? (
                            <Loader2 size={14} className="animate-spin" />
                          ) : (
                            <Trash size={14} />
                          )}
                          Delete Department
                        </DropdownMenuItem>
                      </ProtectedDeleteButton>
                    </DropdownMenuContent>
                  </DropdownMenu>
                ),
              },
            ]}
            emptyState={
              <div className="py-12 text-center">
                <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
                  <Building className="h-6 w-6 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-1">
                  No departments found
                </h3>
                <p className="text-sm text-gray-500">
                  Get started by creating your first department.
                </p>
                <ProtectedCreateButton entity="departments">
                  <Button onClick={onAddDepartment} className="mt-4">
                    <Plus size={16} />
                    Add Department
                  </Button>
                </ProtectedCreateButton>
              </div>
            }
          />
        )}
      </Listing>
    );
  }
);

DepartmentsTab.displayName = "DepartmentsTab";
