"use client";

import { memo } from "react";
import { AlertTriangle } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@workspace/ui/components/alert-dialog";
import { PermissionsModal } from "./PermissionsModal";

// Import form components
import {
  AddEmployeeForm,
  EditEmployeeForm,
  AddDepartmentForm,
  EditDepartmentForm,
  AddRoleForm,
  EditRoleForm,
  ChangePasswordForm,
} from "../../forms";

import type { UserWithAccount, Department, Role } from "@/lib/logistics";

interface StaffDialogsProps {
  // Modal states
  showAddEmployeeModal: boolean;
  showEditEmployeeModal: boolean;
  showAddDepartmentModal: boolean;
  showEditDepartmentModal: boolean;
  showAddRoleModal: boolean;
  showEditRoleModal: boolean;
  showDeleteConfirmModal: boolean;
  showChangePasswordModal: boolean;
  showPermissionsModal: boolean;

  // Data
  departments: Department[];
  roles: Role[];
  editingEmployee: UserWithAccount | null;
  editingDepartment: Department | null;
  editingRole: Role | null;
  deletingEmployee: UserWithAccount | null;
  changingPasswordEmployee: UserWithAccount | null;
  permissionsRole: Role | null;

  // Handlers
  onCloseModal: (modalType: string) => void;
  onAddEmployee: (data: {
    name: string;
    email: string;
    phone?: string;
    roleId: string;
  }) => Promise<void>;
  onEditEmployee: (data: {
    name: string;
    phone?: string;
    roleId: string;
  }) => Promise<void>;
  onAddDepartment: (data: { name: string }) => Promise<void>;
  onEditDepartment: (data: { name: string }) => Promise<void>;
  onAddRole: (data: { name: string; departmentId: string }) => Promise<void>;
  onEditRole: (data: { name: string; departmentId: string }) => Promise<void>;
  onDeleteEmployee: () => Promise<void>;
  onChangePassword: (data: { newPassword: string }) => Promise<void>;
  onPermissionsSave: () => Promise<void>;
}

/**
 * Staff Dialogs Component
 *
 * Manages all dialog states and renders the appropriate dialog content.
 * Follows the freight-management pattern for modal organization.
 */
export const StaffDialogs = memo<StaffDialogsProps>(
  ({
    // Modal states
    showAddEmployeeModal,
    showEditEmployeeModal,
    showAddDepartmentModal,
    showEditDepartmentModal,
    showAddRoleModal,
    showEditRoleModal,
    showDeleteConfirmModal,
    showChangePasswordModal,
    showPermissionsModal,

    // Data
    departments,
    roles,
    editingEmployee,
    editingDepartment,
    editingRole,
    deletingEmployee,
    changingPasswordEmployee,
    permissionsRole,

    // Handlers
    onCloseModal,
    onAddEmployee,
    onEditEmployee,
    onAddDepartment,
    onEditDepartment,
    onAddRole,
    onEditRole,
    onDeleteEmployee,
    onChangePassword,
    onPermissionsSave,
  }) => {
    return (
      <>
        {/* Add Employee Modal */}
        {showAddEmployeeModal && (
          <AddEmployeeForm
            departments={departments}
            roles={roles}
            onClose={() => onCloseModal("addEmployee")}
            onSubmit={onAddEmployee}
          />
        )}

        {/* Edit Employee Modal */}
        {showEditEmployeeModal && editingEmployee && (
          <EditEmployeeForm
            employee={editingEmployee}
            roles={roles}
            onClose={() => onCloseModal("editEmployee")}
            onSubmit={onEditEmployee}
          />
        )}

        {/* Add Department Modal */}
        {showAddDepartmentModal && (
          <AddDepartmentForm
            onClose={() => onCloseModal("addDepartment")}
            onSubmit={onAddDepartment}
          />
        )}

        {/* Edit Department Modal */}
        {showEditDepartmentModal && editingDepartment && (
          <EditDepartmentForm
            department={editingDepartment}
            onClose={() => onCloseModal("editDepartment")}
            onSubmit={onEditDepartment}
          />
        )}

        {/* Add Role Modal */}
        {showAddRoleModal && (
          <AddRoleForm
            departments={departments}
            onClose={() => onCloseModal("addRole")}
            onSubmit={onAddRole}
          />
        )}

        {/* Edit Role Modal */}
        {showEditRoleModal && editingRole && (
          <EditRoleForm
            role={editingRole}
            departments={departments}
            onClose={() => onCloseModal("editRole")}
            onSubmit={onEditRole}
          />
        )}

        {/* Delete Confirmation Modal */}
        {showDeleteConfirmModal && deletingEmployee && (
          <AlertDialog
            open={true}
            onOpenChange={() => onCloseModal("deleteConfirm")}
          >
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle className="flex items-center gap-3">
                  <div className="p-2 rounded-full bg-red-100">
                    <AlertTriangle className="h-6 w-6 text-red-600" />
                  </div>
                  Confirm Deactivation
                </AlertDialogTitle>
                <AlertDialogDescription className="text-left">
                  Are you sure you want to deactivate{" "}
                  <strong>{deletingEmployee.name}</strong>? This will remove
                  their access to the system but preserve their data for audit
                  purposes.
                  <div className="mt-3 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                    <p className="text-amber-800 text-xs">
                      <strong>Note:</strong> This action can be reversed later
                      by reactivating the employee account.
                    </p>
                  </div>
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={onDeleteEmployee}
                  className="bg-red-600 hover:bg-red-700"
                >
                  Deactivate Employee
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        )}

        {/* Change Password Modal */}
        {showChangePasswordModal && changingPasswordEmployee && (
          <ChangePasswordForm
            employeeName={changingPasswordEmployee.name || "Unknown"}
            onClose={() => onCloseModal("changePassword")}
            onSubmit={onChangePassword}
          />
        )}

        {/* Permissions Modal */}
        {showPermissionsModal && permissionsRole && (
          <PermissionsModal
            isOpen={true}
            onClose={() => onCloseModal("permissions")}
            role={permissionsRole}
            onSave={onPermissionsSave}
          />
        )}
      </>
    );
  }
);

StaffDialogs.displayName = "StaffDialogs";
