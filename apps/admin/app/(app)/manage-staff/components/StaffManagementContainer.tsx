"use client";

import { memo } from "react";
import { motion } from "framer-motion";
import { Users, Building, Shield, BarChart, RefreshCw } from "lucide-react";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@workspace/ui/components/tabs";
import { Overview } from "@/modules/layouts/overview";
import { Listing } from "@/modules/listing";
import { Button } from "@workspace/ui/components/button";
import { useStaffManagement } from "../hooks/useStaffManagement";
import { StaffOverviewTab } from "./tabs/StaffOverviewTab";
import { EmployeesTab } from "./tabs/EmployeesTab";
import { DepartmentsTab } from "./tabs/DepartmentsTab";
import { RolesPermissionsTab } from "./tabs/RolesPermissionsTab";
import { StaffDialogs } from "./modals";

/**
 * Staff Management Container Component
 *
 * This container component manages the staff management functionality
 * using the standardized Overview layout and Tabs components.
 * It follows the patterns established in cargo-management and finance modules.
 */
export const StaffManagementContainer = memo(() => {
  const {
    // Tab state
    activeTab,
    setActiveTab,

    // Loading states
    loading,
    refreshing,

    // Data
    employees,
    departments,
    roles,
    staffStats,
    recentActivities,

    // Handlers
    handleRefresh,
    handleToggleStatus,
    handleDeleteRole,
    handleCloseModal,
    handlePermissionsSave,

    // Modal trigger handlers
    handleOpenAddEmployeeModal,
    handleOpenEditEmployeeModal,
    handleOpenAddDepartmentModal,
    handleOpenEditDepartmentModal,
    handleOpenAddRoleModal,
    handleOpenEditRoleModal,
    handleOpenDeleteConfirmModal,
    handleOpenChangePasswordModal,
    handleOpenPermissionsModal,

    // Form event handlers
    handleAddEmployeeFormSubmit,
    handleEditEmployeeFormSubmit,
    handleAddDepartmentFormSubmit,
    handleEditDepartmentFormSubmit,
    handleAddRoleFormSubmit,
    handleEditRoleFormSubmit,
    handleChangePasswordFormSubmit,
    handleDeleteEmployeeConfirm,

    // Search and filters
    searchTerm,
    setSearchTerm,
    employeeFilter,
    setEmployeeFilter,
    departmentFilter,
    setDepartmentFilter,
    roleFilter,
    setRoleFilter,

    // View mode states
    employeesViewMode,
    setEmployeesViewMode,
    departmentsViewMode,
    setDepartmentsViewMode,
    rolesViewMode,
    setRolesViewMode,

    // Pagination states
    employeesCurrentPage,
    setEmployeesCurrentPage,
    departmentsCurrentPage,
    setDepartmentsCurrentPage,
    rolesCurrentPage,
    setRolesCurrentPage,
    itemsPerPage,

    // Modal states
    showAddEmployeeModal,
    setShowAddEmployeeModal,
    showAddDepartmentModal,
    setShowAddDepartmentModal,
    showAddRoleModal,
    setShowAddRoleModal,
    showEditEmployeeModal,
    setShowEditEmployeeModal,
    showEditDepartmentModal,
    setShowEditDepartmentModal,
    showEditRoleModal,
    setShowEditRoleModal,
    showDeleteConfirmModal,
    setShowDeleteConfirmModal,
    showChangePasswordModal,
    setShowChangePasswordModal,
    showPermissionsModal,
    setShowPermissionsModal,

    // Edit states
    editingEmployee,
    setEditingEmployee,
    editingDepartment,
    setEditingDepartment,
    editingRole,
    setEditingRole,
    deletingEmployee,
    setDeletingEmployee,
    changingPasswordEmployee,
    setChangingPasswordEmployee,
    permissionsRole,
    setPermissionsRole,

    // Operation loading
    operationLoading,
  } = useStaffManagement();

  return (
    <Overview className="p-6 space-y-12">
      <Overview.Header
        title="Staff Management"
        caption="Manage employees, departments, roles, and permissions"
        actions={
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={loading}
            aria-label="Refresh staff data"
          >
            <RefreshCw
              className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
        }
      />

      <Overview.Statistics>
        <Listing.StatCard
          icon={Users}
          name="Total Staff"
          value={staffStats.totalStaff}
          valueType="number"
          caption={
            <span className="text-xs text-primary flex items-center gap-1">
              <RefreshCw className="h-3 w-3" /> Real-time data
            </span>
          }
          color="primary"
          loading={loading}
        />
        <Listing.StatCard
          icon={Users}
          name="Active Staff"
          value={staffStats.activeStaff}
          valueType="number"
          caption={
            <span className="text-xs text-green-600 flex items-center gap-1">
              <Users className="h-3 w-3" /> Currently active
            </span>
          }
          color="green"
          loading={loading}
        />
        <Listing.StatCard
          icon={Users}
          name="Pending Staff"
          value={staffStats.pendingStaff}
          valueType="number"
          caption={
            <span className="text-xs text-amber-600 flex items-center gap-1">
              <Users className="h-3 w-3" /> Awaiting activation
            </span>
          }
          color="amber"
          loading={loading}
        />
        <Listing.StatCard
          icon={Building}
          name="Departments"
          value={departments.filter((d) => d.status === "ACTIVE").length}
          valueType="number"
          caption={
            <span className="text-xs text-blue-600 flex items-center gap-1">
              <Building className="h-3 w-3" /> Active departments
            </span>
          }
          color="blue"
          loading={loading}
        />
      </Overview.Statistics>

      <Overview.Content>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-max grid-cols-4">
            <TabsTrigger value="overview">
              <BarChart size={16} />
              Overview
            </TabsTrigger>
            <TabsTrigger value="employees">
              <Users size={16} />
              Staff
            </TabsTrigger>
            <TabsTrigger value="departments">
              <Building size={16} />
              Departments
            </TabsTrigger>
            <TabsTrigger value="roles">
              <Shield size={16} />
              Roles & Permissions
            </TabsTrigger>
          </TabsList>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="mt-6"
          >
            <TabsContent value="overview" className="mt-0">
              <StaffOverviewTab
                loading={loading}
                departmentData={staffStats.departmentData}
                recentActivities={recentActivities}
                departments={departments}
                onRefresh={handleRefresh}
                refreshing={refreshing}
              />
            </TabsContent>

            <TabsContent value="employees" className="mt-0">
              <EmployeesTab
                employees={employees}
                loading={loading}
                searchTerm={searchTerm}
                setSearchTerm={setSearchTerm}
                employeeFilter={employeeFilter}
                setEmployeeFilter={setEmployeeFilter}
                viewMode={employeesViewMode}
                onViewModeChange={setEmployeesViewMode}
                currentPage={employeesCurrentPage}
                onPageChange={setEmployeesCurrentPage}
                itemsPerPage={itemsPerPage}
                onRefresh={handleRefresh}
                onAddEmployee={handleOpenAddEmployeeModal}
                onEditEmployee={handleOpenEditEmployeeModal}
                onDeleteEmployee={handleOpenDeleteConfirmModal}
                onChangePassword={handleOpenChangePasswordModal}
                onToggleStatus={handleToggleStatus}
                operationLoading={operationLoading}
                refreshing={refreshing}
              />
            </TabsContent>

            <TabsContent value="departments" className="mt-0">
              <DepartmentsTab
                departments={departments}
                employees={employees}
                roles={roles}
                loading={loading}
                viewMode={departmentsViewMode}
                onViewModeChange={setDepartmentsViewMode}
                currentPage={departmentsCurrentPage}
                onPageChange={setDepartmentsCurrentPage}
                itemsPerPage={itemsPerPage}
                departmentFilter={departmentFilter}
                setDepartmentFilter={setDepartmentFilter}
                onRefresh={handleRefresh}
                onAddDepartment={handleOpenAddDepartmentModal}
                onEditDepartment={handleOpenEditDepartmentModal}
                operationLoading={operationLoading}
                refreshing={refreshing}
              />
            </TabsContent>

            <TabsContent value="roles" className="mt-0">
              <RolesPermissionsTab
                roles={roles}
                departments={departments}
                employees={employees}
                loading={loading}
                viewMode={rolesViewMode}
                onViewModeChange={setRolesViewMode}
                currentPage={rolesCurrentPage}
                onPageChange={setRolesCurrentPage}
                itemsPerPage={itemsPerPage}
                roleFilter={roleFilter}
                setRoleFilter={setRoleFilter}
                onRefresh={handleRefresh}
                onAddRole={handleOpenAddRoleModal}
                onEditRole={handleOpenEditRoleModal}
                onManagePermissions={handleOpenPermissionsModal}
                onDeleteRole={handleDeleteRole}
                operationLoading={operationLoading}
                refreshing={refreshing}
              />
            </TabsContent>
          </motion.div>
        </Tabs>
      </Overview.Content>

      {/* Staff Management Dialogs */}
      <StaffDialogs
        // Modal states
        showAddEmployeeModal={showAddEmployeeModal}
        showEditEmployeeModal={showEditEmployeeModal}
        showAddDepartmentModal={showAddDepartmentModal}
        showEditDepartmentModal={showEditDepartmentModal}
        showAddRoleModal={showAddRoleModal}
        showEditRoleModal={showEditRoleModal}
        showDeleteConfirmModal={showDeleteConfirmModal}
        showChangePasswordModal={showChangePasswordModal}
        showPermissionsModal={showPermissionsModal}
        // Data
        departments={departments}
        roles={roles}
        editingEmployee={editingEmployee}
        editingDepartment={editingDepartment}
        editingRole={editingRole}
        deletingEmployee={deletingEmployee}
        changingPasswordEmployee={changingPasswordEmployee}
        permissionsRole={permissionsRole}
        // Handlers
        onCloseModal={handleCloseModal}
        onAddEmployee={handleAddEmployeeFormSubmit}
        onEditEmployee={handleEditEmployeeFormSubmit}
        onAddDepartment={handleAddDepartmentFormSubmit}
        onEditDepartment={handleEditDepartmentFormSubmit}
        onAddRole={handleAddRoleFormSubmit}
        onEditRole={handleEditRoleFormSubmit}
        onDeleteEmployee={handleDeleteEmployeeConfirm}
        onChangePassword={handleChangePasswordFormSubmit}
        onPermissionsSave={handlePermissionsSave}
      />
    </Overview>
  );
});

StaffManagementContainer.displayName = "StaffManagementContainer";
