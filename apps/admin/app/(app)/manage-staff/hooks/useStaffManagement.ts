import { useState, useEffect, useCallback } from "react";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { refreshUserPermissions } from "@/store/slices/authSlice";
import {
  userService,
  departmentService,
  roleService,
  accountService,
  logService,
  type User,
  type UserWithAccount,
  type Department,
  type Role,
  type Account,
  type Log,
} from "@/lib/logistics";
import { showToast } from "@/lib/utils";

// Staff statistics interface
interface StaffStats {
  totalStaff: number;
  activeStaff: number;
  pendingStaff: number;
  newThisMonth: number;
  departmentData: { name: string; value: number; color: string }[];
}

// Activity interface
interface Activity {
  id: string;
  action: string;
  user: string;
  time: string;
  type: "add" | "update" | "permission" | "delete";
}

// Colors for charts
const COLORS = [
  "#0088FE",
  "#00C49F",
  "#FFBB28",
  "#FF8042",
  "#8884d8",
  "#82ca9d",
];

/**
 * Custom hook for managing staff management state and operations
 *
 * This hook centralizes all staff management logic including:
 * - Data fetching and state management
 * - CRUD operations for employees, departments, and roles
 * - Search and filtering
 * - Modal state management
 * - Loading states
 */
export const useStaffManagement = () => {
  // Tab state
  const [activeTab, setActiveTab] = useState("overview");

  // Loading states
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [operationLoading, setOperationLoading] = useState<string | null>(null);

  // Modal states
  const [showAddEmployeeModal, setShowAddEmployeeModal] = useState(false);
  const [showAddDepartmentModal, setShowAddDepartmentModal] = useState(false);
  const [showAddRoleModal, setShowAddRoleModal] = useState(false);
  const [showEditEmployeeModal, setShowEditEmployeeModal] = useState(false);
  const [showEditDepartmentModal, setShowEditDepartmentModal] = useState(false);
  const [showEditRoleModal, setShowEditRoleModal] = useState(false);
  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false);
  const [showChangePasswordModal, setShowChangePasswordModal] = useState(false);
  const [showPermissionsModal, setShowPermissionsModal] = useState(false);

  // Edit states
  const [editingEmployee, setEditingEmployee] =
    useState<UserWithAccount | null>(null);
  const [editingDepartment, setEditingDepartment] = useState<Department | null>(
    null
  );
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [deletingEmployee, setDeletingEmployee] =
    useState<UserWithAccount | null>(null);
  const [changingPasswordEmployee, setChangingPasswordEmployee] =
    useState<UserWithAccount | null>(null);
  const [permissionsRole, setPermissionsRole] = useState<Role | null>(null);

  // Data states
  const [staffStats, setStaffStats] = useState<StaffStats>({
    totalStaff: 0,
    activeStaff: 0,
    pendingStaff: 0,
    newThisMonth: 0,
    departmentData: [],
  });
  const [employees, setEmployees] = useState<UserWithAccount[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [recentActivities, setRecentActivities] = useState<Activity[]>([]);

  // Search and filter states
  const [searchTerm, setSearchTerm] = useState("");
  const [employeeFilter, setEmployeeFilter] = useState("all");
  const [departmentFilter, setDepartmentFilter] = useState("all");
  const [roleFilter, setRoleFilter] = useState("all");

  // View mode states
  const [employeesViewMode, setEmployeesViewMode] = useState<"cards" | "table">(
    "table"
  );
  const [departmentsViewMode, setDepartmentsViewMode] = useState<
    "cards" | "table"
  >("table");
  const [rolesViewMode, setRolesViewMode] = useState<"cards" | "table">(
    "table"
  );

  // Pagination states
  const [employeesCurrentPage, setEmployeesCurrentPage] = useState(1);
  const [departmentsCurrentPage, setDepartmentsCurrentPage] = useState(1);
  const [rolesCurrentPage, setRolesCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Get authenticated user and dispatch
  const { user: authUser } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();

  // Fetch all staff data
  const fetchStaffData = useCallback(
    async (refresh = false) => {
      if (!authUser) return;

      try {
        if (refresh) setRefreshing(true);
        else setLoading(true);

        // Fetch all data in parallel
        const [usersResult, departmentsResult, rolesResult, logsResult] =
          await Promise.all([
            userService.getAllUsersWithAccounts({ limit: 1000 }),
            departmentService.getAll({ limit: 100 }),
            roleService.getAllRolesWithDepartments({ limit: 100 }),
            logService.getRecentLogs(20),
          ]);

        if (usersResult.success) {
          setEmployees(usersResult.data);
          calculateStaffStats(usersResult.data, departmentsResult.data || []);
        } else {
          showToast(`Failed to fetch employees: ${usersResult.error}`, "error");
        }

        if (departmentsResult.success) {
          setDepartments(departmentsResult.data);
        } else {
          showToast(
            `Failed to fetch departments: ${departmentsResult.error}`,
            "error"
          );
        }

        if (rolesResult.success) {
          setRoles(rolesResult.data);
        } else {
          showToast(`Failed to fetch roles: ${rolesResult.error}`, "error");
        }

        if (logsResult.success) {
          // Transform logs to activities
          const activities: Activity[] = logsResult.data
            .slice(0, 5)
            .map((log: any) => ({
              id: log.id,
              action: log.event || "System activity",
              user: log.accounts?.users?.name || "Unknown User",
              time: getRelativeTime(log.created_at || ""),
              type: getActivityType(log.event || ""),
            }));
          setRecentActivities(activities);
        }
      } catch (error) {
        console.error("Error fetching staff data:", error);
        showToast("Failed to fetch staff data. Please try again.", "error");
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    },
    [authUser]
  );

  // Calculate staff statistics
  const calculateStaffStats = useCallback(
    (users: UserWithAccount[], departments: Department[]) => {
      const activeUsers = users.filter((u) => u.accounts?.status === "ACTIVE");
      const pendingUsers = users.filter(
        (u) => u.accounts?.status === "PENDING"
      );

      // Calculate new users this month
      const thisMonth = new Date();
      thisMonth.setDate(1);
      const newThisMonth = users.filter(
        (u) => u.created_at && new Date(u.created_at) >= thisMonth
      ).length;

      // Calculate department distribution
      const departmentCounts: { [key: string]: number } = {};
      users.forEach((user) => {
        if (user.accounts?.roles?.departments?.name) {
          const deptName = user.accounts.roles.departments.name;
          departmentCounts[deptName] = (departmentCounts[deptName] || 0) + 1;
        } else {
          departmentCounts["Unassigned"] =
            (departmentCounts["Unassigned"] || 0) + 1;
        }
      });

      const departmentData = Object.entries(departmentCounts).map(
        ([name, value], index) => ({
          name,
          value,
          color: COLORS[index % COLORS.length] || "#6b7280",
        })
      );

      setStaffStats({
        totalStaff: users.length,
        activeStaff: activeUsers.length,
        pendingStaff: pendingUsers.length,
        newThisMonth,
        departmentData,
      });
    },
    []
  );

  // Get relative time
  const getRelativeTime = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 60) return `${diffMins} minutes ago`;
    if (diffHours < 24) return `${diffHours} hours ago`;
    return `${diffDays} days ago`;
  };

  // Get activity type from action
  const getActivityType = (action: string): Activity["type"] => {
    if (
      action.toLowerCase().includes("create") ||
      action.toLowerCase().includes("add")
    )
      return "add";
    if (
      action.toLowerCase().includes("update") ||
      action.toLowerCase().includes("edit")
    )
      return "update";
    if (
      action.toLowerCase().includes("delete") ||
      action.toLowerCase().includes("remove")
    )
      return "delete";
    if (
      action.toLowerCase().includes("permission") ||
      action.toLowerCase().includes("role")
    )
      return "permission";
    return "update";
  };

  // Filter employees
  const filteredEmployees = employees.filter((employee) => {
    const matchesFilter =
      employeeFilter === "all" ||
      (employee.accounts?.status &&
        employee.accounts.status.toLowerCase() ===
          employeeFilter.toLowerCase()) ||
      (employee.accounts?.roles?.departments?.name &&
        employee.accounts.roles.departments.name.toLowerCase() ===
          employeeFilter.toLowerCase());

    const matchesSearch =
      !searchTerm ||
      employee.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.accounts?.email
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      employee.accounts?.roles?.name
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase());

    return matchesFilter && matchesSearch;
  });

  // Filter departments
  const filteredDepartments = departments.filter((department) => {
    const matchesFilter =
      departmentFilter === "all" ||
      (department.status &&
        department.status.toLowerCase() === departmentFilter.toLowerCase());

    const matchesSearch =
      !searchTerm ||
      department.name?.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesFilter && matchesSearch;
  });

  // Filter roles
  const filteredRoles = roles.filter((role) => {
    const matchesFilter =
      roleFilter === "all" ||
      (role.status && role.status.toLowerCase() === roleFilter.toLowerCase());

    const matchesSearch =
      !searchTerm ||
      role.name?.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesFilter && matchesSearch;
  });

  // Handlers
  const handleRefresh = useCallback(() => {
    fetchStaffData(true);
  }, [fetchStaffData]);

  const handleAddEmployee = useCallback(
    async (employeeData: {
      name: string;
      email: string;
      phone?: string;
      roleId: string;
    }) => {
      try {
        setOperationLoading("add-employee");
        await accountService.createNewStaff({
          name: employeeData.name,
          phone: employeeData.phone,
          email: employeeData.email,
          roleId: employeeData.roleId,
        });
        await fetchStaffData(true);
        setShowAddEmployeeModal(false);
        showToast("Employee added successfully!", "success");
      } catch (error) {
        console.error("Error adding employee:", error);
        const message =
          error instanceof Error ? error.message : "Failed to add employee";
        showToast(message, "error");
      } finally {
        setOperationLoading(null);
      }
    },
    [fetchStaffData]
  );

  // Add Department function
  const handleAddDepartment = useCallback(
    async (departmentData: { name: string }) => {
      try {
        setOperationLoading("add-department");

        const result = await departmentService.create({
          name: departmentData.name,
          status: "ACTIVE",
        });

        if (!result.success) {
          throw new Error(result.error || "Failed to create department");
        }

        // Refresh data
        await fetchStaffData(true);
        setShowAddDepartmentModal(false);
        showToast("Department added successfully!", "success");
      } catch (error) {
        console.error("Error adding department:", error);
        const message =
          error instanceof Error ? error.message : "Failed to add department";
        showToast(message, "error");
      } finally {
        setOperationLoading(null);
      }
    },
    [fetchStaffData]
  );

  // Add Role function
  const handleAddRole = useCallback(
    async (roleData: { name: string; departmentId: string }) => {
      try {
        setOperationLoading("add-role");

        const result = await roleService.create({
          name: roleData.name,
          department_id: roleData.departmentId,
          status: "ACTIVE",
        });

        if (!result.success) {
          throw new Error(result.error || "Failed to create role");
        }

        // Refresh data
        await fetchStaffData(true);
        setShowAddRoleModal(false);
        showToast("Role added successfully!", "success");
      } catch (error) {
        console.error("Error adding role:", error);
        const message =
          error instanceof Error ? error.message : "Failed to add role";
        showToast(message, "error");
      } finally {
        setOperationLoading(null);
      }
    },
    [fetchStaffData]
  );

  // Update Employee function
  const handleEditEmployee = useCallback(
    async (employeeData: { name: string; phone?: string; roleId: string }) => {
      if (!editingEmployee) return;

      try {
        setOperationLoading("update-employee");

        // Update user
        const userResult = await userService.update(editingEmployee.id, {
          name: employeeData.name,
          phone: employeeData.phone,
        });

        if (!userResult.success) {
          throw new Error(userResult.error || "Failed to update user");
        }

        // Update account if needed
        if (
          editingEmployee.accounts?.id &&
          editingEmployee.accounts.role_id !== employeeData.roleId
        ) {
          const accountResult = await accountService.update(
            editingEmployee.accounts.id,
            {
              role_id: employeeData.roleId,
            }
          );

          if (!accountResult.success) {
            throw new Error(accountResult.error || "Failed to update account");
          }
        }

        // Refresh data
        await fetchStaffData(true);
        setShowEditEmployeeModal(false);
        setEditingEmployee(null);
        showToast("Employee updated successfully!", "success");
      } catch (error) {
        console.error("Error updating employee:", error);
        const message =
          error instanceof Error ? error.message : "Failed to update employee";
        showToast(message, "error");
      } finally {
        setOperationLoading(null);
      }
    },
    [editingEmployee, fetchStaffData]
  );

  // Update Department function
  const handleEditDepartment = useCallback(
    async (departmentData: { name: string }) => {
      if (!editingDepartment) return;

      try {
        setOperationLoading("update-department");

        const result = await departmentService.update(editingDepartment.id, {
          name: departmentData.name,
        });

        if (!result.success) {
          throw new Error(result.error || "Failed to update department");
        }

        // Refresh data
        await fetchStaffData(true);
        setShowEditDepartmentModal(false);
        setEditingDepartment(null);
        showToast("Department updated successfully!", "success");
      } catch (error) {
        console.error("Error updating department:", error);
        const message =
          error instanceof Error
            ? error.message
            : "Failed to update department";
        showToast(message, "error");
      } finally {
        setOperationLoading(null);
      }
    },
    [editingDepartment, fetchStaffData]
  );

  // Update Role function
  const handleEditRole = useCallback(
    async (roleData: { name: string; departmentId: string }) => {
      if (!editingRole) return;

      try {
        setOperationLoading("update-role");

        const result = await roleService.update(editingRole.id, {
          name: roleData.name,
          department_id: roleData.departmentId,
        });

        if (!result.success) {
          throw new Error(result.error || "Failed to update role");
        }

        // Refresh data
        await fetchStaffData(true);
        setShowEditRoleModal(false);
        setEditingRole(null);
        showToast("Role updated successfully!", "success");
      } catch (error) {
        console.error("Error updating role:", error);
        const message =
          error instanceof Error ? error.message : "Failed to update role";
        showToast(message, "error");
      } finally {
        setOperationLoading(null);
      }
    },
    [editingRole, fetchStaffData]
  );

  // Confirm Delete Employee
  const handleDeleteEmployee = useCallback(async () => {
    if (!deletingEmployee?.accounts?.id) {
      showToast("Employee account not found", "error");
      return;
    }

    try {
      setOperationLoading(`delete-${deletingEmployee.id}`);

      // Deactivate the account instead of hard deletion
      const result = await accountService.removeStaffAccount(
        deletingEmployee.id,
        deletingEmployee.accounts.id
      );

      if (!result.success) {
        throw new Error(result.error || "Failed to deactivate employee");
      }

      // Log the activity
      await logService.create({
        event: "Employee deactivated",
        message: `${deletingEmployee.name} has been deactivated`,
        account_id: authUser?.accountId || null,
        associated_table: "users",
        associated_id: deletingEmployee.id,
      });

      // Close modal and refresh data
      setShowDeleteConfirmModal(false);
      setDeletingEmployee(null);
      await fetchStaffData(true);
      showToast("Employee deactivated successfully!", "success");
    } catch (error) {
      console.error("Error deactivating employee:", error);
      const message =
        error instanceof Error
          ? error.message
          : "Failed to deactivate employee";
      showToast(message, "error");
    } finally {
      setOperationLoading(null);
    }
  }, [deletingEmployee, authUser?.accountId, fetchStaffData]);

  // Change Password function
  const handleChangePassword = useCallback(
    async (passwordData: { newPassword: string }) => {
      if (!changingPasswordEmployee?.accounts?.id) return;

      try {
        setOperationLoading("change-password");

        const result = await accountService.adminChangePassword(
          changingPasswordEmployee.accounts.id,
          passwordData.newPassword
        );

        if (!result.success) {
          throw new Error(result.error || "Failed to update password");
        }

        setShowChangePasswordModal(false);
        setChangingPasswordEmployee(null);
        showToast("Password updated successfully!", "success");
      } catch (error) {
        console.error("Error updating password:", error);
        const message =
          error instanceof Error ? error.message : "Failed to update password";
        showToast(message, "error");
      } finally {
        setOperationLoading(null);
      }
    },
    [changingPasswordEmployee]
  );

  // Toggle employee status
  const handleToggleStatus = useCallback(
    async (employee: UserWithAccount) => {
      if (!employee.accounts?.id) {
        showToast("Employee account not found", "error");
        return;
      }

      try {
        setOperationLoading(`toggle-${employee.id}`);

        const newStatus =
          employee.accounts.status === "ACTIVE" ? "INACTIVE" : "ACTIVE";

        // Only update the accounts table - this controls access to the system
        const result = await accountService.update(employee.accounts.id, {
          status: newStatus,
        });

        if (!result.success) {
          throw new Error(result.error || "Failed to update employee status");
        }

        // Log the activity
        await logService.create({
          event: `Employee ${newStatus.toLowerCase()}`,
          message: `${employee.name} has been ${newStatus.toLowerCase()}`,
          account_id: authUser?.accountId || null,
          associated_table: "users",
          associated_id: employee.id,
        });

        // Refresh data
        await fetchStaffData(true);
        showToast(
          `Employee ${newStatus.toLowerCase()} successfully!`,
          "success"
        );
      } catch (error) {
        console.error("Error toggling employee status:", error);
        const message =
          error instanceof Error
            ? error.message
            : "Failed to update employee status";
        showToast(message, "error");
      } finally {
        setOperationLoading(null);
      }
    },
    [authUser?.accountId, fetchStaffData]
  );

  // Manage permissions function
  const handleManagePermissions = useCallback((role: Role) => {
    setPermissionsRole(role);
    setShowPermissionsModal(true);
  }, []);

  // Delete role function (soft delete - set status to INACTIVE)
  const handleDeleteRole = useCallback(
    async (role: Role) => {
      const confirmDelete = window.confirm(
        `Are you sure you want to delete the role "${role.name}"? This will set it to INACTIVE status.`
      );
      if (!confirmDelete) return;

      try {
        setOperationLoading(`delete-${role.id}`);

        const result = await roleService.update(role.id, {
          status: "INACTIVE",
        });

        if (result.success) {
          showToast(`Role "${role.name}" has been deactivated`, "success");
          await fetchStaffData(); // Refresh data
        } else {
          showToast(`Failed to delete role: ${result.error}`, "error");
        }
      } catch (error) {
        console.error("Error deleting role:", error);
        showToast("An error occurred while deleting the role", "error");
      } finally {
        setOperationLoading(null);
      }
    },
    [roleService, fetchStaffData, showToast]
  );

  // Modal trigger handlers for systematic prop drill down
  const handleOpenAddEmployeeModal = useCallback(() => {
    setShowAddEmployeeModal(true);
  }, []);

  const handleOpenEditEmployeeModal = useCallback(
    (employee: UserWithAccount) => {
      setEditingEmployee(employee);
      setShowEditEmployeeModal(true);
    },
    []
  );

  const handleOpenAddDepartmentModal = useCallback(() => {
    setShowAddDepartmentModal(true);
  }, []);

  const handleOpenEditDepartmentModal = useCallback(
    (department: Department) => {
      setEditingDepartment(department);
      setShowEditDepartmentModal(true);
    },
    []
  );

  const handleOpenAddRoleModal = useCallback(() => {
    setShowAddRoleModal(true);
  }, []);

  const handleOpenEditRoleModal = useCallback((role: Role) => {
    setEditingRole(role);
    setShowEditRoleModal(true);
  }, []);

  const handleOpenDeleteConfirmModal = useCallback(
    (employee: UserWithAccount) => {
      setDeletingEmployee(employee);
      setShowDeleteConfirmModal(true);
    },
    []
  );

  const handleOpenChangePasswordModal = useCallback(
    (employee: UserWithAccount) => {
      setChangingPasswordEmployee(employee);
      setShowChangePasswordModal(true);
    },
    []
  );

  const handleOpenPermissionsModal = useCallback((role: Role) => {
    setPermissionsRole(role);
    setShowPermissionsModal(true);
  }, []);

  // Form event handlers that align with form submission patterns
  const handleAddEmployeeFormSubmit = useCallback(
    async (data: {
      name: string;
      email: string;
      phone?: string;
      roleId: string;
    }) => {
      await handleAddEmployee(data);
    },
    [handleAddEmployee]
  );

  const handleEditEmployeeFormSubmit = useCallback(
    async (data: { name: string; phone?: string; roleId: string }) => {
      await handleEditEmployee(data);
    },
    [handleEditEmployee]
  );

  const handleAddDepartmentFormSubmit = useCallback(
    async (data: { name: string }) => {
      await handleAddDepartment(data);
    },
    [handleAddDepartment]
  );

  const handleEditDepartmentFormSubmit = useCallback(
    async (data: { name: string }) => {
      await handleEditDepartment(data);
    },
    [handleEditDepartment]
  );

  const handleAddRoleFormSubmit = useCallback(
    async (data: { name: string; departmentId: string }) => {
      await handleAddRole(data);
    },
    [handleAddRole]
  );

  const handleEditRoleFormSubmit = useCallback(
    async (data: { name: string; departmentId: string }) => {
      await handleEditRole(data);
    },
    [handleEditRole]
  );

  const handleChangePasswordFormSubmit = useCallback(
    async (data: { newPassword: string }) => {
      await handleChangePassword(data);
    },
    [handleChangePassword]
  );

  const handleDeleteEmployeeConfirm = useCallback(async () => {
    await handleDeleteEmployee();
  }, [handleDeleteEmployee]);

  // Close modal handler
  const handleCloseModal = useCallback((modalType: string) => {
    switch (modalType) {
      case "addEmployee":
        setShowAddEmployeeModal(false);
        setEditingEmployee(null);
        break;
      case "editEmployee":
        setShowEditEmployeeModal(false);
        setEditingEmployee(null);
        break;
      case "addDepartment":
        setShowAddDepartmentModal(false);
        break;
      case "editDepartment":
        setShowEditDepartmentModal(false);
        setEditingDepartment(null);
        break;
      case "addRole":
        setShowAddRoleModal(false);
        break;
      case "editRole":
        setShowEditRoleModal(false);
        setEditingRole(null);
        break;
      case "deleteConfirm":
        setShowDeleteConfirmModal(false);
        setDeletingEmployee(null);
        break;
      case "changePassword":
        setShowChangePasswordModal(false);
        setChangingPasswordEmployee(null);
        break;
      case "permissions":
        setShowPermissionsModal(false);
        setPermissionsRole(null);
        break;
    }
  }, []);

  // Permissions save handler
  const handlePermissionsSave = useCallback(async () => {
    console.log("Permissions saved successfully");
    showToast("Permissions updated successfully", "success");
    setShowPermissionsModal(false);
    setPermissionsRole(null);
    // Refresh data to reflect any changes
    await fetchStaffData();
  }, [fetchStaffData, showToast]);

  // Reset pagination when filters change
  useEffect(() => {
    setEmployeesCurrentPage(1);
  }, [searchTerm, employeeFilter]);

  useEffect(() => {
    setDepartmentsCurrentPage(1);
  }, [searchTerm, departmentFilter]);

  useEffect(() => {
    setRolesCurrentPage(1);
  }, [searchTerm, roleFilter]);

  // Initialize data on mount
  useEffect(() => {
    if (authUser) {
      fetchStaffData();
    }
  }, [authUser, fetchStaffData]);

  return {
    // Tab state
    activeTab,
    setActiveTab,

    // Loading states
    loading,
    refreshing,
    operationLoading,

    // Data
    employees: filteredEmployees,
    departments: filteredDepartments,
    roles: filteredRoles,
    staffStats,
    recentActivities,

    // Handlers
    handleRefresh,
    handleAddEmployee,
    handleEditEmployee,
    handleDeleteEmployee,
    handleAddDepartment,
    handleEditDepartment,
    handleAddRole,
    handleEditRole,
    handleManagePermissions,
    handleDeleteRole,
    handleChangePassword,
    handleToggleStatus,
    handleCloseModal,
    handlePermissionsSave,

    // Modal trigger handlers
    handleOpenAddEmployeeModal,
    handleOpenEditEmployeeModal,
    handleOpenAddDepartmentModal,
    handleOpenEditDepartmentModal,
    handleOpenAddRoleModal,
    handleOpenEditRoleModal,
    handleOpenDeleteConfirmModal,
    handleOpenChangePasswordModal,
    handleOpenPermissionsModal,

    // Form event handlers
    handleAddEmployeeFormSubmit,
    handleEditEmployeeFormSubmit,
    handleAddDepartmentFormSubmit,
    handleEditDepartmentFormSubmit,
    handleAddRoleFormSubmit,
    handleEditRoleFormSubmit,
    handleChangePasswordFormSubmit,
    handleDeleteEmployeeConfirm,

    // Search and filters
    searchTerm,
    setSearchTerm,
    employeeFilter,
    setEmployeeFilter,
    departmentFilter,
    setDepartmentFilter,
    roleFilter,
    setRoleFilter,

    // View mode states
    employeesViewMode,
    setEmployeesViewMode,
    departmentsViewMode,
    setDepartmentsViewMode,
    rolesViewMode,
    setRolesViewMode,

    // Pagination states
    employeesCurrentPage,
    setEmployeesCurrentPage,
    departmentsCurrentPage,
    setDepartmentsCurrentPage,
    rolesCurrentPage,
    setRolesCurrentPage,
    itemsPerPage,

    // Modal states
    showAddEmployeeModal,
    setShowAddEmployeeModal,
    showAddDepartmentModal,
    setShowAddDepartmentModal,
    showAddRoleModal,
    setShowAddRoleModal,
    showEditEmployeeModal,
    setShowEditEmployeeModal,
    showEditDepartmentModal,
    setShowEditDepartmentModal,
    showEditRoleModal,
    setShowEditRoleModal,
    showDeleteConfirmModal,
    setShowDeleteConfirmModal,
    showChangePasswordModal,
    setShowChangePasswordModal,
    showPermissionsModal,
    setShowPermissionsModal,

    // Edit states
    editingEmployee,
    setEditingEmployee,
    editingDepartment,
    setEditingDepartment,
    editingRole,
    setEditingRole,
    deletingEmployee,
    setDeletingEmployee,
    changingPasswordEmployee,
    setChangingPasswordEmployee,
    permissionsRole,
    setPermissionsRole,
  };
};
