"use client";

import { memo } from "react";
import { Package, Calendar, Hash } from "lucide-react";
import { Badge } from "@workspace/ui/components/badge";
import { Listing } from "@/modules/listing";
import { type BatchDisplay } from "./BatchManagementContainer";

interface BatchCardRendererProps {
  data: BatchDisplay[];
  loading: boolean;
  emptyState: React.ReactNode;
  onItemClick: (item: any) => void;
}

/**
 * Card renderer for batch management items
 *
 * Renders items in a card layout with type-specific styling.
 * Optimized with memoization to prevent unnecessary re-renders.
 */
export const BatchCardRenderer = memo<BatchCardRendererProps>(
  ({ data, loading, emptyState, onItemClick }) => {
    // Helper functions for styling
    const getTypeColor = (type: string): string => {
      const colors: Record<string, string> = {
        STANDARD: "#6b7280",
        EXPRESS: "#f59e0b",
        FRAGILE: "#ef4444",
        HAZARDOUS: "#eab308",
      };
      return colors[type] || "#6b7280";
    };

    const getTypeIcon = () => {
      return Package; // All batches use Package icon
    };

    const getStatusColor = (status: string): string => {
      const colors: Record<string, string> = {
        ACTIVE: "#10b981",
        INACTIVE: "#6b7280",
        COMPLETED: "#3b82f6",
        CANCELLED: "#ef4444",
      };
      return colors[status] || "#6b7280";
    };

    // No categories needed for batch-only view
    const categories: any[] = [];

    return (
      <Listing.Cards
        data={data}
        loading={loading}
        emptyState={emptyState}
        columns="grid-cols-3"
        groupByCategory={false}
        categories={categories}
        getCategoryKey={() => ""}
        renderCard={(item: BatchDisplay) => {
          const typeColor = getTypeColor(item.type);
          const TypeIcon = getTypeIcon();

          return (
            <div className="p-4 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-2">
                  <div
                    className="p-1.5 rounded-md"
                    style={{
                      backgroundColor: `${typeColor}20`,
                      color: typeColor,
                    }}
                  >
                    <TypeIcon className="h-4 w-4" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 text-sm">
                      {item.name}
                    </h3>
                    <div className="flex items-center gap-1 text-xs text-gray-500">
                      <Hash className="h-3 w-3" />
                      {item.code}
                    </div>
                  </div>
                </div>

                {/* Type Badge */}
                <Badge
                  style={{
                    backgroundColor: `${typeColor}20`,
                    color: typeColor,
                  }}
                >
                  {item.type}
                </Badge>
              </div>

              {/* Content based on type */}
              <div className="space-y-2 mb-3">
                <div className="text-sm text-gray-600">
                  <span className="font-medium">Freight:</span> {item.freight}
                </div>
                <div className="text-sm text-gray-600">
                  <span className="font-medium">Weight:</span> {item.weight}
                </div>
                <div className="text-sm text-gray-600">
                  <span className="font-medium">CBM:</span> {item.volume}
                </div>
                {item.status && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-gray-600">
                      Status:
                    </span>
                    <Badge
                      style={{
                        backgroundColor: `${getStatusColor(item.status)}20`,
                        color: getStatusColor(item.status),
                      }}
                    >
                      {item.status}
                    </Badge>
                  </div>
                )}
              </div>

              {/* Footer */}
              <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                <div className="flex items-center gap-1 text-xs text-gray-500">
                  <Calendar className="h-3 w-3" />
                  {item.date}
                </div>
              </div>
            </div>
          );
        }}
        onItemClick={onItemClick}
      />
    );
  }
);

BatchCardRenderer.displayName = "BatchCardRenderer";
