"use client";

import { memo } from "react";
import {
  Package,
  CheckCircle,
  Clock,
  TrendingUp,
  Activity,
  Target,
} from "lucide-react";
import { Overview } from "@/modules/layouts/overview";
import { Listing } from "@/modules/listing";
import { type BatchStats } from "./BatchManagementContainer";

interface BatchStatisticsProps {
  batchStats: BatchStats;
  loading: boolean;
}

/**
 * Statistics component for Freight Management
 *
 * Displays key metrics and KPIs for freights and batches using StatCard components
 * from the listing module for consistency. Memoized to prevent unnecessary re-renders.
 */
export const BatchStatistics = memo<BatchStatisticsProps>(
  ({ batchStats, loading }) => {
    return (
      <Overview.Statistics>
        <Listing.StatCard
          icon={Package}
          name="Total Batches"
          value={batchStats.totalBatches}
          valueType="number"
          color="purple"
          loading={loading}
          caption={
            <div className="flex items-center gap-1 text-purple-600">
              <Activity className="h-3 w-3" />
              All batches
            </div>
          }
        />

        <Listing.StatCard
          icon={CheckCircle}
          name="Active Batches"
          value={batchStats.activeBatches}
          valueType="number"
          color="green"
          loading={loading}
          caption={
            <div className="flex items-center gap-1 text-green-600">
              <Clock className="h-3 w-3" />
              In progress
            </div>
          }
        />

        <Listing.StatCard
          icon={Target}
          name="Completed Batches"
          value={batchStats.completedBatches}
          valueType="number"
          color="amber"
          loading={loading}
          caption={
            <div className="flex items-center gap-1 text-orange-600">
              <CheckCircle className="h-3 w-3" />
              Delivered
            </div>
          }
        />

        <Listing.StatCard
          icon={Activity}
          name="Total Capacity"
          value={batchStats.totalCapacity}
          valueType="number"
          color="blue"
          loading={loading}
          caption={
            <div className="flex items-center gap-1 text-blue-600">
              <Target className="h-3 w-3" />
              CBM
            </div>
          }
        />

        <Listing.StatCard
          icon={TrendingUp}
          name="Utilization Rate"
          value={batchStats.utilizationRate}
          valueType="percent"
          color="purple"
          loading={loading}
          caption={
            <div className="flex items-center gap-1 text-purple-600">
              <Activity className="h-3 w-3" />
              Efficiency
            </div>
          }
        />
      </Overview.Statistics>
    );
  }
);

BatchStatistics.displayName = "BatchStatistics";
