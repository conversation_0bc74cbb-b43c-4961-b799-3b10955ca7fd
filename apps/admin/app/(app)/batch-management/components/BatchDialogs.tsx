"use client";

import { memo } from "react";
import { Package, Trash2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@workspace/ui/components/alert-dialog";
import { EditBatchDialog } from "../forms/EditBatchDialog";
import { type BatchManagementState } from "./BatchManagementContainer";
import { CargoDetailsDialog } from "../../cargo-management/components/Dialog/CargoDetailsDialog";

// Import form components
import { NewBatchForm } from "../forms/NewBatchForm";

interface BatchDialogsProps {
  state: BatchManagementState;
  onClose: (dialogType: string) => void;
  onBatchMutation: () => Promise<void>;
  onDeleteBatch: (batch: any) => Promise<void>;
}

/**
 * Dialogs component for Batch Management
 *
 * Manages all dialog states and renders the appropriate dialog content.
 * Memoized to prevent unnecessary re-renders.
 */
export const BatchDialogs = memo<BatchDialogsProps>(
  ({ state, onClose, onBatchMutation, onDeleteBatch }) => {
    return (
      <>
        {/* New Batch Dialog */}
        <Dialog
          open={state.isNewBatchOpen}
          onOpenChange={(open) => !open && onClose("newBatch")}
        >
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Create New Batch
              </DialogTitle>
            </DialogHeader>
            <NewBatchForm
              onOpenChange={(open) => !open && onClose("newBatch")}
              onSuccess={onBatchMutation}
            />
          </DialogContent>
        </Dialog>

        {/* Edit Batch Dialog */}
        {state.selectedBatchForEdit && (
          <EditBatchDialog
            isOpen={state.isEditBatchDialogOpen}
            onClose={() => onClose("editBatch")}
            batch={state.selectedBatchForEdit}
            onBatchUpdated={onBatchMutation}
            freights={[]}
          />
        )}

        {/* Delete Batch Dialog */}
        {state.selectedBatchForDelete && (
          <AlertDialog
            open={state.isDeleteBatchDialogOpen}
            onOpenChange={(open) => !open && onClose("deleteBatch")}
          >
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle className="flex items-center gap-2 text-red-600">
                  <Trash2 size={20} />
                  Delete Batch
                </AlertDialogTitle>
                <AlertDialogDescription className="space-y-2">
                  <p>
                    Are you sure you want to delete the batch{" "}
                    <span className="font-semibold text-gray-900">
                      "{state.selectedBatchForDelete.name}" (
                      {state.selectedBatchForDelete.code})
                    </span>
                    ?
                  </p>
                  <div className="bg-red-50 border border-red-200 rounded-lg p-3 mt-3">
                    <div className="flex">
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800">
                          This action cannot be undone
                        </h3>
                        <div className="mt-2 text-sm text-red-700">
                          <ul className="list-disc pl-5 space-y-1">
                            <li>• All assigned cargo will be unlinked</li>
                            <li>• Invoice records will be affected</li>
                            <li>
                              • Historical data will be permanently removed
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={() => onDeleteBatch(state.selectedBatchForDelete)}
                  className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
                >
                  <Trash2 size={16} className="mr-2" />
                  Delete Batch
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        )}
      </>
    );
  }
);

BatchDialogs.displayName = "BatchDialogs";
