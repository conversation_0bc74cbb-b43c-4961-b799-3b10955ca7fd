"use client";

import { memo, useMemo, useCallback, Fragment } from "react";
import {
  Package,
  Plus,
  Download,
  Trash2,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  CheckSquare,
} from "lucide-react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import {
  BulkStatusSelector,
  FREIGHT_STATUS_OPTIONS,
} from "@/components/ui/bulk-status-selector";
import { Listing } from "@/modules/listing";
import { BatchTableColumns } from "./BatchTableColumns";
import { BatchCardRenderer } from "./BatchCardRenderer";

import { type BatchDisplay } from "./BatchManagementContainer";

interface BatchListProps {
  data: BatchDisplay[];
  loading: boolean;
  viewMode: "cards" | "table";
  searchTerm: string;
  categoryFilter: string;
  currentPage: number;
  itemsPerPage: number;
  listType: "batches";
  onViewModeChange: (mode: "cards" | "table") => void;
  onSearchChange: (term: string) => void;
  onCategoryFilterChange: (filter: string) => void;
  onPageChange: (page: number) => void;
  onRefresh: () => void;
  onItemAction: (action: string, item?: any) => void;
  // Checkbox support
  selectedItems: Set<string>;
  setSelectedItems: (items: Set<string>) => void;
  // Bulk actions
  onBulkStatusUpdate: (status: string) => void;
  onBulkDelete: () => void;
  onBulkCreateTasks: () => void;
  onClearSelections?: () => void;
}

/**
 * List component for batch management items
 *
 * Handles both card and table views with filtering, pagination, and actions.
 * Optimized with memoization to prevent unnecessary re-renders.
 */
export const BatchList = memo<BatchListProps>(
  ({
    data,
    loading,
    viewMode,
    searchTerm,
    categoryFilter,
    currentPage,
    itemsPerPage,
    listType,
    onViewModeChange,
    onSearchChange,
    onCategoryFilterChange,
    onPageChange,
    onRefresh,
    onItemAction,
    selectedItems,
    setSelectedItems,
    onBulkStatusUpdate,
    onBulkDelete,
    onBulkCreateTasks,
    onClearSelections,
  }) => {
    // Apply category filtering based on data.type key
    const filteredData = useMemo(() => {
      if (!categoryFilter || categoryFilter === "all") {
        return data;
      }

      return data.filter((item) => {
        return item.type?.toLowerCase() === categoryFilter.toLowerCase();
      });
    }, [data, categoryFilter]);

    // Calculate pagination info for display
    const totalPages = Math.ceil(filteredData.length / itemsPerPage);

    // Pagination for cards view only (table handles its own pagination)
    const paginatedData = useMemo(() => {
      const start = (currentPage - 1) * itemsPerPage;
      const end = start + itemsPerPage;
      return filteredData.slice(start, end);
    }, [filteredData, currentPage, itemsPerPage]);

    // Note: Bulk action handlers are now passed as props from the hook

    // Render bulk actions
    const renderBulkActions = useCallback(
      () => (
        <>
          <BulkStatusSelector
            statusOptions={FREIGHT_STATUS_OPTIONS}
            onStatusUpdate={onBulkStatusUpdate}
            placeholder="Mark as..."
          />
          <Button
            variant="outline"
            size="sm"
            onClick={onBulkCreateTasks}
            className="gap-2"
            disabled={selectedItems.size === 0}
          >
            <CheckSquare size={16} />
            Create Tasks
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={onBulkDelete}
            className="gap-2"
          >
            <Trash2 size={16} />
            Delete
          </Button>
        </>
      ),
      [onBulkStatusUpdate, onBulkDelete, onBulkCreateTasks, selectedItems.size]
    );

    // Get categories for batches
    const categories = useMemo(() => {
      const batchTypes = Array.from(new Set(data.map((b) => b.type)));
      return batchTypes.map((type) => ({
        key: type.toLowerCase(),
        label: type.toLowerCase(),
        count: data.filter((b) => b.type === type).length,
      }));
    }, [data]);

    // Table columns configuration
    const tableColumns = useMemo(() => {
      return BatchTableColumns({
        onViewItem: (item: any) => onItemAction("view", item),
        onEditItem: (item: any) => onItemAction("edit", item),
        onDeleteItem: (item: any) => onItemAction("delete", item),
      });
    }, [onItemAction]);

    // Empty state
    const emptyState = (
      <div className="text-center py-12">
        <div className="mx-auto h-12 w-12 text-gray-400 mb-4">
          <Package className="h-12 w-12" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          No {listType} found
        </h3>
        <p className="text-gray-500 mb-4">
          {searchTerm
            ? `No ${listType} match your search criteria.`
            : `Get started by creating your first ${listType.slice(0, -1)}.`}
        </p>

        <Button
          onClick={() => onItemAction(listType)}
          className="capitalize"
          hidden={!searchTerm}
        >
          <Plus className="h-4 w-4 mr-2" />
          New {listType}
        </Button>
      </div>
    );

    return (
      <Listing className="space-y-6">
        <Listing.Filters
          searchTerm={searchTerm}
          onSearchChange={onSearchChange}
          onRefresh={onRefresh}
          loading={loading}
          bulkActions={renderBulkActions()}
          selectedCount={selectedItems.size}
          showBulkActions={true}
          onClearSelections={onClearSelections}
        />

        <Listing.Controls
          entity={listType}
          length={filteredData.length}
          viewMode={viewMode}
          onViewModeChange={onViewModeChange}
          categories={categories}
          categoryFilter={categoryFilter}
          onCategoryFilterChange={onCategoryFilterChange}
          actions={
            <div className="inline-block">
              <Button variant="outline" size="sm" className="mr-4">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button
                size="sm"
                onClick={() => onItemAction("create", listType)}
                className="capitalize"
              >
                <Plus className="h-4 w-4 mr-2" />
                New {listType}
              </Button>
            </div>
          }
        />

        {viewMode === "cards" ? (
          <Fragment>
            <BatchCardRenderer
              data={paginatedData}
              loading={loading}
              emptyState={emptyState}
              onItemClick={(item: any) => onItemAction("view", item)}
            />

            {/* Pagination for cards view */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between px-6 py-4 border-t border-gray-200 bg-white rounded-lg">
                <div className="text-sm text-gray-500">
                  Showing {(currentPage - 1) * itemsPerPage + 1} to{" "}
                  {Math.min(currentPage * itemsPerPage, filteredData.length)} of{" "}
                  {filteredData.length} results
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(1)}
                    disabled={currentPage === 1}
                    className="gap-1"
                  >
                    <ChevronsLeft size={16} />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="gap-1"
                  >
                    <ChevronLeft size={16} />
                    Previous
                  </Button>
                  <span className="px-3 py-2 text-sm text-gray-900">
                    Page {currentPage} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="gap-1"
                  >
                    Next
                    <ChevronRight size={16} />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onPageChange(totalPages)}
                    disabled={currentPage === totalPages}
                    className="gap-1"
                  >
                    <ChevronsRight size={16} />
                  </Button>
                </div>
              </div>
            )}
          </Fragment>
        ) : (
          <Listing.Table
            data={filteredData}
            columns={tableColumns}
            loading={loading}
            enableCheckboxes={true}
            selectedRowIds={Array.from(selectedItems)}
            onSelectionChange={(selectedIds) =>
              setSelectedItems(new Set(selectedIds))
            }
            getRowId={(item) => item.id}
            emptyState={emptyState}
            pagination={{
              currentPage,
              totalPages,
              totalItems: filteredData.length,
              itemsPerPage,
              onPageChange,
            }}
          />
        )}
      </Listing>
    );
  }
);

BatchList.displayName = "BatchList";
