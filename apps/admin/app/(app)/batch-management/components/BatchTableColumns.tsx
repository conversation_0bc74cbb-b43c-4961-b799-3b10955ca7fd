"use client";

import { Package, Eye, Edit, Trash2, MoreHorizontal } from "lucide-react";
import { Badge } from "@workspace/ui/components/badge";
import { Button } from "@workspace/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { type BatchDisplay } from "./BatchManagementContainer";

interface BatchTableColumnsProps {
  onViewItem: (item: any) => void;
  onEditItem: (item: any) => void;
  onDeleteItem: (item: any) => void;
}

/**
 * Table columns configuration for batch management
 *
 * Returns an array of column definitions for batch data.
 * Includes type badges, status indicators, and action buttons.
 */
export function BatchTableColumns({
  onViewItem,
  onEditItem,
  onDeleteItem,
}: BatchTableColumnsProps) {
  // Helper function for type badges
  const getTypeBadge = (type: string) => {
    const batchConfigs: Record<string, { color: string; icon: any }> = {
      SAFE: { color: "bg-green-100 text-green-800", icon: Package },
      COPY: { color: "bg-orange-100 text-orange-800", icon: Package },
      DANGEROUS: { color: "bg-red-100 text-red-800", icon: Package },
    };

    const config = batchConfigs[type] || {
      color: "bg-gray-100 text-gray-800",
      icon: Package,
    };

    const IconComponent = config.icon;

    return (
      <Badge className={config.color}>
        <IconComponent className="h-3 w-3 mr-1" />
        {type}
      </Badge>
    );
  };

  // Helper function for status badges
  const getStatusBadge = (status: string) => {
    const statusConfigs: Record<string, string> = {
      ACTIVE: "bg-green-100 text-green-800",
      INACTIVE: "bg-gray-100 text-gray-800",
      COMPLETED: "bg-blue-100 text-blue-800",
      CANCELLED: "bg-red-100 text-red-800",
    };

    return (
      <Badge className={statusConfigs[status] || "bg-gray-100 text-gray-800"}>
        {status}
      </Badge>
    );
  };

  // Base columns for all list types
  const baseColumns = [
    {
      key: "code",
      label: "Code",
      render: (item: any) => (
        <div
          className="font-mono text-sm text-primary hover:underline cursor-pointer"
          onClick={() => {
            onViewItem(item);
          }}
        >
          {item.code}
        </div>
      ),
    },
    {
      key: "name",
      label: "Name",
      render: (item: any) => (
        <div>
          <div className="font-medium text-gray-900">{item.name}</div>
        </div>
      ),
    },
  ];

  // Batch-specific columns
  const batchColumns = [
    {
      key: "type",
      label: "Type",
      render: (item: BatchDisplay) => getTypeBadge(item.type),
    },
    {
      key: "weight",
      label: "Weight",
      render: (item: BatchDisplay) => (
        <div className="text-sm text-gray-900">{item.weight}</div>
      ),
    },
    {
      key: "volume",
      label: "Volume",
      render: (item: BatchDisplay) => (
        <div className="text-sm text-gray-900">{item.volume}</div>
      ),
    },
    {
      key: "assignedCargo",
      label: "Assigned Cargo",
      render: (item: BatchDisplay) => (
        <div className="text-sm text-gray-900 font-medium">
          {item.assignedCargo}
        </div>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (item: BatchDisplay) => getStatusBadge(item.status),
    },
  ];

  // Common columns
  const commonColumns = [
    {
      key: "date",
      label: "Created",
      render: (item: any) => (
        <div className="text-sm text-gray-500">{item.date}</div>
      ),
    },
    {
      key: "actions",
      label: "Actions",
      render: (item: any) => (
        <div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onViewItem(item)}>
                <Eye className="h-4 w-4 mr-2" />
                View
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onEditItem(item)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onDeleteItem(item)}
                className="text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
    },
  ];

  return [...baseColumns, ...batchColumns, ...commonColumns];
}
