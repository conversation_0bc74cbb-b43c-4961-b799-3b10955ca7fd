"use client";

import { memo } from "react";
import { RefreshCw, Package } from "lucide-react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Overview } from "@/modules/layouts/overview";

interface BatchManagementHeaderProps {
  loading: boolean;
  shouldShowCreateButton: (entity: string) => boolean;
  onRefresh: () => void;
  onCreateBatch: () => void;
}

/**
 * Header component for Batch Management page
 *
 * Displays the page title, description, and action buttons.
 * Memoized to prevent unnecessary re-renders.
 */
export const BatchManagementHeader = memo<BatchManagementHeaderProps>(
  ({ loading, shouldShowCreateButton, onRefresh, onCreateBatch }) => {
    return (
      <Overview.Header
        title="Batch Management"
        caption="Manage batch operations and logistics coordination"
        actions={
          <>
            <Button
              variant="outline"
              onClick={onRefresh}
              disabled={loading}
              aria-label="Refresh batch data"
            >
              <RefreshCw
                className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`}
              />
              Refresh
            </Button>

            {shouldShowCreateButton("batches") && (
              <Button
                onClick={onCreateBatch}
                disabled={loading}
                aria-label="Create new batch"
              >
                <Package className="h-4 w-4 mr-2" />
                New Batch
              </Button>
            )}
          </>
        }
      />
    );
  }
);

BatchManagementHeader.displayName = "BatchManagementHeader";
