"use client";

import { useState, useEffect, useMemo, useCallback } from "react";
import { useRouter } from "next/navigation";
import { useAppSelector } from "@/store/hooks";
import { batchService, type BatchWithRelations } from "@/lib/logistics";
import { showToast } from "@/lib/utils";
import { toast } from "sonner";

import {
  type BatchStats,
  type BatchManagementState,
  type BatchDisplay,
} from "../components/BatchManagementContainer";
import {
  getDimensionUnitAbbreviation,
  getWeightUnitAbbreviation,
} from "@/lib/utils/unit-mappings";

/**
 * Custom hook for batch management logic
 *
 * Encapsulates all business logic, state management, and side effects
 * for the batch management feature. Follows React best practices with
 * proper memoization and optimization.
 */
export function useBatchManagement() {
  const router = useRouter();
  const { user: authUser } = useAppSelector((state) => state.auth);

  // Main state
  const [state, setState] = useState<BatchManagementState>({
    loading: true,
    refreshing: false,
    viewMode: "table",
    searchTerm: "",
    categoryFilter: "all",
    currentPage: 1,
    columnFilters: [],
    batchStats: {
      totalBatches: 0,
      activeBatches: 0,
      completedBatches: 0,
      totalCapacity: 0,
      utilizationRate: 0,
    },
    batchData: [],
    batches: [],

    // Dialog states
    isNewBatchOpen: false,
    isEditBatchDialogOpen: false,
    selectedBatchForEdit: null,
    isDeleteBatchDialogOpen: false,
    selectedBatchForDelete: null,

    // Checkbox state
    selectedItems: new Set<string>(),

    // Bulk task creation state
    isBulkTaskDialogOpen: false,
  });

  // Helper function to update state
  const updateState = useCallback((updates: Partial<BatchManagementState>) => {
    setState((prev) => ({ ...prev, ...updates }));
  }, []);

  // Clear selections handler
  const handleClearSelections = useCallback(() => {
    updateState({ selectedItems: new Set() });
    showToast("Batch selections cleared", "success");
  }, [updateState]);

  // Load batch data
  const loadData = useCallback(
    async (refresh = false) => {
      if (!authUser) return;

      try {
        if (refresh) setState((prev) => ({ ...prev, refreshing: true }));
        else setState((prev) => ({ ...prev, loading: true }));

        // Get all batches with cargo data
        const batchesResult = await batchService.getAllBatchesWithCargo({
          limit: 100,
        });

        if (batchesResult.success) {
          const batches = batchesResult.data || [];

          // Transform data for display
          const batchData: BatchDisplay[] = batches.map((batch) => ({
            id: batch.id,
            code: batch.code,
            name: batch.name,
            type: batch.type || "STANDARD",
            weight: `${batch.weight || 0} ${getWeightUnitAbbreviation(batch.weight_unit) || "KG"}`,
            volume: `${(batch as any).cbm_value || 0} ${getDimensionUnitAbbreviation(batch.cbm_unit) || "M3"}`,
            assignedCargo: (batch as any).cargos?.length || 0, // Count of assigned cargo
            status: batch.status || "ACTIVE",
            date: batch.created_at
              ? new Date(batch.created_at).toLocaleDateString()
              : "N/A",
          }));

          // Calculate statistics
          const batchStats: BatchStats = {
            totalBatches: batches.length,
            activeBatches: batches.filter((b) => b.status === "ACTIVE").length,
            completedBatches: batches.filter((b) => b.status === "COMPLETED")
              .length,
            totalCapacity: batches.reduce(
              (sum, b) => sum + ((b as any).cbm_value || 0),
              0
            ),
            utilizationRate: batches.length > 0 ? 85 : 0, // Placeholder calculation
          };

          setState((prev) => ({
            ...prev,
            batches,
            batchData,
            batchStats,
            loading: false,
            refreshing: false,
          }));
        } else {
          throw new Error("Failed to load batch data");
        }
      } catch (error) {
        console.error("Error loading batch management data:", error);
        showToast("Failed to load batch data", "error");
        setState((prev) => ({ ...prev, loading: false, refreshing: false }));
      }
    },
    [authUser]
  );

  // Initial data load
  useEffect(() => {
    loadData();
  }, [loadData]);

  // Refresh handler
  const handleRefresh = useCallback(() => {
    loadData(true);
  }, [loadData]);

  // Action handlers
  const handleBatchAction = useCallback(
    (action: string, batch?: BatchWithRelations | any) => {
      switch (action) {
        case "create":
        case "batches":
          updateState({ isNewBatchOpen: true });
          break;
        case "edit":
          if (batch?.id) {
            // Find the full batch object from state
            const fullBatch = state.batches.find((b) => b.id === batch.id);
            if (fullBatch) {
              updateState({
                isEditBatchDialogOpen: true,
                selectedBatchForEdit: fullBatch,
              });
            } else {
              // Fallback to using the provided batch object
              updateState({
                isEditBatchDialogOpen: true,
                selectedBatchForEdit: batch,
              });
            }
          }
          break;
        case "view":
          if (batch?.id) {
            router.push(`/batch-management/${batch.id}`);
          }
          break;
        case "delete":
          if (batch?.id) {
            // Find the full batch object from state
            const fullBatch = state.batches.find((b) => b.id === batch.id);
            if (fullBatch) {
              updateState({
                isDeleteBatchDialogOpen: true,
                selectedBatchForDelete: fullBatch,
              });
            } else {
              // Fallback to direct delete if batch not found in state
              handleDeleteBatch(batch);
            }
          }
          break;
      }
    },
    [updateState, router, state.batches]
  );

  const handleDialogClose = useCallback(
    (dialogType: string) => {
      switch (dialogType) {
        case "newBatch":
          updateState({ isNewBatchOpen: false });
          break;
        case "editBatch":
          updateState({
            isEditBatchDialogOpen: false,
            selectedBatchForEdit: null,
          });
          break;
        case "deleteBatch":
          updateState({
            isDeleteBatchDialogOpen: false,
            selectedBatchForDelete: null,
          });
          break;
      }
    },
    [updateState]
  );

  const handleBatchMutation = useCallback(() => {
    loadData(true);
  }, [loadData]);

  // Bulk actions handlers
  const handleBulkStatusUpdate = useCallback(
    async (status: string) => {
      const selectedIds = Array.from(state.selectedItems);
      if (selectedIds.length === 0) {
        console.warn("No items selected for bulk status update");
        return;
      }

      try {
        // Update batches
        const batchIds = selectedIds.filter((id) =>
          state.batches.some((b) => b.id === id)
        );

        const updatePromises = batchIds.map((batchId) =>
          batchService.updateStatus(batchId, status as any)
        );

        const updateResults = await Promise.all(updatePromises);
        const successfulUpdates = updateResults.filter(
          (result) => result.success
        ).length;

        if (successfulUpdates > 0) {
          showToast(
            `Successfully updated ${successfulUpdates} batch(es) to ${status}`,
            "success"
          );

          // Clear selection after successful action
          setState((prev) => ({ ...prev, selectedItems: new Set() }));

          // Refresh data to show updated statuses
          await loadData(true);
        } else {
          showToast("Failed to update any batches", "error");
        }
      } catch (error) {
        console.error("Error in bulk status update:", error);
        showToast("Failed to update batch status", "error");
      }
    },
    [state.selectedItems, state.batches, loadData]
  );

  const handleBulkDelete = useCallback(async () => {
    const selectedIds = Array.from(state.selectedItems);
    if (selectedIds.length === 0) {
      console.warn("No items selected for bulk delete");
      return;
    }

    if (
      !confirm(
        `Are you sure you want to delete ${selectedIds.length} batch(es)?`
      )
    ) {
      return;
    }

    try {
      // Delete batches
      const batchIds = selectedIds.filter((id) =>
        state.batches.some((b) => b.id === id)
      );

      const deletePromises = batchIds.map((batchId) =>
        batchService.delete(batchId)
      );

      const deleteResults = await Promise.all(deletePromises);
      const successfulDeletes = deleteResults.filter(
        (result) => result.success
      ).length;

      if (successfulDeletes > 0) {
        showToast(
          `Successfully deleted ${successfulDeletes} batch(es)`,
          "success"
        );

        // Clear selection and refresh data
        setState((prev) => ({ ...prev, selectedItems: new Set() }));
        loadData(true);
      } else {
        showToast("Failed to delete any batches", "error");
      }
    } catch (error) {
      console.error("Error in bulk delete:", error);
      showToast("Failed to delete batches", "error");
    }
  }, [state.selectedItems, state.batches, loadData]);

  // Delete handlers
  const handleDeleteBatch = useCallback(
    async (batch: any) => {
      if (!confirm(`Are you sure you want to delete batch "${batch.name}"?`)) {
        return;
      }

      try {
        const result = await batchService.delete(batch.id);
        if (result.success) {
          showToast("Batch deleted successfully", "success");
          loadData(true);
        } else {
          showToast("Failed to delete batch", "error");
        }
      } catch (error) {
        console.error("Error deleting batch:", error);
        showToast("Failed to delete batch", "error");
      }
    },
    [loadData]
  );

  // Filtered data based on search term
  const filteredBatches = useMemo(() => {
    return state.batchData.filter((batch) => {
      if (state.searchTerm) {
        const searchLower = state.searchTerm.toLowerCase();
        return (
          batch.name.toLowerCase().includes(searchLower) ||
          batch.code.toLowerCase().includes(searchLower) ||
          batch.type.toLowerCase().includes(searchLower)
        );
      }
      return true;
    });
  }, [state.batchData, state.searchTerm]);

  // Bulk task creation handlers
  const handleBulkCreateTasks = useCallback(() => {
    if (state.selectedItems.size === 0) {
      toast.error("Please select batch items to create tasks for");
      return;
    }
    updateState({ isBulkTaskDialogOpen: true });
  }, [state.selectedItems.size, updateState]);

  const setIsBulkTaskDialogOpen = useCallback(
    (isOpen: boolean) => {
      updateState({ isBulkTaskDialogOpen: isOpen });
    },
    [updateState]
  );

  const selectedItemsForTasks = Array.from(state.selectedItems).map(
    (batchId) => {
      const batch = state.batchData.find((b) => b.id === batchId);
      return {
        id: batchId,
        name: batch?.name || "Unknown Batch",
        identifier: batch?.code,
      };
    }
  );

  const handleTasksCreated = useCallback(() => {
    // Clear selections and refresh data
    updateState({ selectedItems: new Set() });
    loadData(true);
  }, [updateState, loadData]);

  return {
    state,
    filteredBatches,
    handleRefresh,
    handleBatchAction,
    handleDialogClose,
    handleBatchMutation,
    handleBulkStatusUpdate,
    handleBulkDelete,
    handleClearSelections,
    updateState,
    handleBulkCreateTasks,
    isBulkTaskDialogOpen: state.isBulkTaskDialogOpen,
    setIsBulkTaskDialogOpen,
    selectedItemsForTasks,
    handleTasksCreated,
  };
}
