"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Loader2 } from "lucide-react";
import { useAppSelector } from "@/store/hooks";
import {
  batchService,
  type BatchInsert,
  BatchTypeEnum as BatchType,
  WeightUnitEnum as WeightUnit,
  DimensionUnitEnum as DimensionUnit,
} from "@/lib/logistics";
import { codeGeneratorService } from "@/lib/logistics/operations/code-generator";
import { showToast } from "@/lib/utils";
import { cn } from "@workspace/ui/lib/utils";
import { type FreightDisplay } from "../components/BatchManagementContainer";

interface NewBatchFormProps {
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
  freights?: FreightDisplay[];
  freightId?: string;
}

// Input Field Component
const InputField = ({
  id,
  label,
  icon: Icon,
  ...props
}: {
  id: string;
  label: string;
  icon?: React.ElementType;
} & React.InputHTMLAttributes<HTMLInputElement>) => (
  <div>
    <label
      htmlFor={id}
      className="block text-sm font-medium text-gray-700 mb-1.5"
    >
      {label}
    </label>
    <div className="relative">
      {Icon && (
        <Icon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
      )}
      <Input
        id={id}
        className={cn(
          "w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white text-gray-900 shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary",
          Icon ? "pl-10" : ""
        )}
        {...props}
      />
    </div>
  </div>
);

// Select Field Component
const SelectField = ({
  id,
  label,
  placeholder,
  defaultValue,
  value,
  options,
  onChange,
}: {
  id: string;
  label: string;
  placeholder?: string;
  defaultValue?: string;
  value?: string;
  options: (string | { value: string; label: string })[];
  onChange?: (value: string) => void;
}) => (
  <div>
    <label
      htmlFor={id}
      className="block text-sm font-medium text-gray-700 mb-1.5"
    >
      {label}
    </label>
    <Select defaultValue={defaultValue} value={value} onValueChange={onChange}>
      <SelectTrigger
        id={id}
        className="w-full p-2.5 text-sm border border-gray-200 rounded-lg bg-white text-gray-900 shadow-sm focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary data-[placeholder]:text-gray-500 h-10"
      >
        <SelectValue placeholder={placeholder ?? "Select..."} />
      </SelectTrigger>
      <SelectContent>
        {options.map((option) => {
          const value =
            typeof option === "string"
              ? option === "All Types" || option === "All Statuses"
                ? "all"
                : option.toLowerCase().replace(/\s+/g, "-")
              : option.value;
          const displayLabel =
            typeof option === "string" ? option : option.label;
          return (
            <SelectItem key={value} value={value}>
              {displayLabel}
            </SelectItem>
          );
        })}
      </SelectContent>
    </Select>
  </div>
);

export const NewBatchForm = ({
  onOpenChange,
  onSuccess,
  freights = [],
  freightId,
}: NewBatchFormProps) => {
  const { user: authUser } = useAppSelector((state) => state.auth);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    code: "",
    type: "CONTAINER" as BatchType,
    category: "SAFE" as "DANGEROUS" | "SAFE",
    freightId: freightId || "", // Optional
    freightType: "" as "AIR" | "SEA" | "", // Mandatory - required for code generation
    billOfLading: "",
    length: "",
    width: "",
    height: "",
    cbmUnit: "METER_CUBIC" as DimensionUnit,
    cbmValue: "",
    weight: "",
    weightUnit: "KILOGRAMS" as WeightUnit,
    currencyConvRate: "", // USD to TZS conversion rate
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  // Function to apply dimension preset
  const applyDimensionPreset = (
    preset: (typeof dimensionPresets.CONTAINER)[0]
  ) => {
    const cbmValue = (preset.length * preset.width * preset.height).toFixed(3);

    setFormData((prev) => ({
      ...prev,
      length: preset.length.toString(),
      width: preset.width.toString(),
      height: preset.height.toString(),
      weight: preset.weight.toString(),
      weightUnit: preset.weightUnit,
      cbmUnit: preset.cbmUnit,
      cbmValue: cbmValue,
    }));
  };

  // Auto-calculate CBM when dimensions change
  useEffect(() => {
    const length = parseFloat(formData.length) || 0;
    const width = parseFloat(formData.width) || 0;
    const height = parseFloat(formData.height) || 0;

    if (length > 0 && width > 0 && height > 0) {
      const cbm = length * width * height;
      setFormData((prev) => ({ ...prev, cbmValue: cbm.toFixed(3) }));
    } else {
      setFormData((prev) => ({ ...prev, cbmValue: "" }));
    }
  }, [formData.length, formData.width, formData.height]);

  // Auto-generate batch code when freight type is selected
  const generateBatchCodeWithIndex = async () => {
    if (formData.freightType && authUser) {
      try {
        // Get next index for this freight type/year/month combination
        const { data: existingBatches } = await batchService.getAll({
          filters: {
            account_id: authUser.accountId,
          },
        });

        if (existingBatches) {
          // Extract existing batch codes for sequential generation
          const existingBatchCodes = existingBatches
            .map((batch) => batch.code)
            .filter((code): code is string => !!code);

          // Generate new code with sequential index using centralized service
          const newCode = codeGeneratorService.generateSequentialBatchCode(
            {
              freightName: "", // Not needed in new format
              shippingMode: formData.freightType,
              origin: "", // Not needed in new format
            },
            existingBatchCodes
          );

          setFormData((prev) => ({ ...prev, code: newCode }));
        }
      } catch (error) {
        console.error("Error generating batch code:", error);
      }
    }
  };

  // Auto-generate code when freight type changes
  useEffect(() => {
    if (formData.freightType) {
      generateBatchCodeWithIndex();
    } else {
      setFormData((prev) => ({ ...prev, code: "" }));
    }
  }, [formData.freightType]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!authUser?.accountId) {
      showToast("Authentication required", "error");
      return;
    }

    // Validate mandatory fields
    if (!formData.freightType) {
      showToast("Freight type is required", "error");
      return;
    }

    if (!formData.name.trim()) {
      showToast("Batch name is required", "error");
      return;
    }

    if (!formData.code.trim()) {
      showToast("Batch code is required", "error");
      return;
    }

    setLoading(true);

    try {
      const batchData: BatchInsert = {
        name: formData.name,
        code: formData.code,
        type: formData.type,
        category: formData.category,
        freight_id: formData.freightId || null,
        freight_type: formData.freightType,
        bill_of_lading: formData.billOfLading || null,
        length: parseFloat(formData.length) || 0,
        width: parseFloat(formData.width) || 0,
        height: parseFloat(formData.height) || 0,
        cbm_unit: formData.cbmUnit,
        cbm_value: parseFloat(formData.cbmValue) || 0,
        weight: parseFloat(formData.weight) || 0,
        weight_unit: formData.weightUnit,
        currency_conv_rate: formData.currencyConvRate
          ? parseFloat(formData.currencyConvRate)
          : null,
        account_id: authUser.accountId,
        status: "ACTIVE",
      };

      const result = await batchService.createBatch(batchData);

      if (result.success) {
        showToast("Batch created successfully!", "success");
        onSuccess();
        onOpenChange(false);

        // Reset form
        setFormData({
          name: "",
          code: "",
          type: "CONTAINER" as BatchType,
          category: "SAFE" as "DANGEROUS" | "SAFE",
          freightId: "",
          freightType: "" as "AIR" | "SEA" | "", // Reset to empty - user must select again
          billOfLading: "",
          length: "",
          width: "",
          height: "",
          cbmUnit: "METER_CUBIC" as DimensionUnit,
          cbmValue: "",
          weight: "",
          weightUnit: "KILOGRAMS" as WeightUnit,
          currencyConvRate: "",
        });
      } else {
        showToast(
          "Error creating batch: " + (result.error || "Failed to create batch"),
          "error"
        );
      }
    } catch (error: any) {
      console.error("Error creating batch:", error);
      showToast(
        "Failed to create batch: " + (error.message || "Unknown error"),
        "error"
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <form
      onSubmit={handleSubmit}
      className="overflow-y-scroll max-h-[70vh] space-y-6 py-4"
    >
      {/* Freight Type - Primary Field */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
        <SelectField
          id="freightType"
          label="Freight Type (Required)"
          placeholder="Select freight type to generate batch code"
          value={formData.freightType}
          options={[
            { value: "AIR", label: "Air Freight" },
            { value: "SEA", label: "Sea Freight" },
          ]}
          onChange={(value) => handleInputChange("freightType", value)}
        />
        <div>
          <InputField
            id="code"
            label="Batch Code (Auto-generated)"
            placeholder={
              formData.freightType
                ? "Auto-generated from freight type"
                : "Select freight type first"
            }
            value={formData.code}
            onChange={(e) => handleInputChange("code", e.target.value)}
            required
            readOnly
            className={cn(
              "bg-gray-50",
              !formData.freightType && "text-gray-400"
            )}
          />
          {formData.freightType && (
            <p className="text-xs text-gray-500 mt-1">
              Format: BCH/S{formData.freightType === "AIR" ? "A" : "S"}/
              {new Date().getFullYear().toString().slice(-2)}
              {(new Date().getMonth() + 1).toString().padStart(2, "0")}/XXX
            </p>
          )}
        </div>
      </div>

      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
        <InputField
          id="name"
          label="Batch Name *"
          placeholder="e.g: Electronics Container Batch"
          value={formData.name}
          onChange={(e) => handleInputChange("name", e.target.value)}
          required
        />
        <InputField
          id="billOfLading"
          label={`${formData.freightType === "AIR" ? "Air Way Bill (AWB)" : "Bill of Lading (BIL)"}`}
          placeholder="e.g: BIL-2024-001"
          value={formData.billOfLading}
          onChange={(e) => handleInputChange("billOfLading", e.target.value)}
        />
      </div>

      {/* Type and Category */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
        <SelectField
          id="type"
          label="Batch Type"
          placeholder="Select batch type"
          value={formData.type}
          options={[
            { value: "CONTAINER", label: "Container" },
            { value: "PALLET", label: "Pallet" },
            { value: "BULK", label: "Bulk" },
            { value: "MIXED", label: "Mixed" },
          ]}
          onChange={(value) => handleInputChange("type", value)}
        />

        <SelectField
          id="category"
          label="Category"
          placeholder="Select cargo category"
          value={formData.category}
          options={[
            { value: "SAFE", label: "Safe Goods" },
            { value: "DANGEROUS", label: "Dangerous Goods" },
          ]}
          onChange={(value) => handleInputChange("category", value)}
        />
      </div>

      {/* Freight Assignment */}
      {freights.length > 0 && (
        <SelectField
          id="freightId"
          label="Assign to Freight (Optional)"
          placeholder="Select freight (optional)"
          value={formData.freightId}
          options={[
            { value: "", label: "No freight assignment" },
            ...freights.map((freight) => ({
              value: freight.id,
              label: `${freight.name} (${freight.code})`,
            })),
          ]}
          onChange={(value) => handleInputChange("freightId", value)}
        />
      )}

      {/* Dimension Presets */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Dimension Presets</h3>
        <p className="text-sm text-gray-600">
          Click on a preset to automatically populate dimensions and weight
          based on common standards.
        </p>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
          {dimensionPresets[formData.type].map((preset, index) => (
            <button
              key={index}
              type="button"
              onClick={() => applyDimensionPreset(preset)}
              className="p-4 text-left border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 group"
            >
              <div className="font-medium text-gray-900 text-sm group-hover:text-blue-700 mb-1">
                {preset.name}
              </div>
              <div className="text-xs text-gray-500 mb-2">
                {preset.description}
              </div>
              <div className="flex justify-between text-xs text-gray-400">
                <span>
                  Weight: {preset.weight}
                  {preset.weightUnit === "POUNDS"
                    ? "lb"
                    : preset.weightUnit === "KILOGRAMS"
                      ? "kg"
                      : preset.weightUnit === "TONS"
                        ? "t"
                        : "kg"}
                </span>
                <span>
                  Volume:{" "}
                  {(preset.length * preset.width * preset.height).toFixed(2)}
                  {preset.cbmUnit === "FEET_CUBIC"
                    ? "ft³"
                    : preset.cbmUnit === "METER_CUBIC"
                      ? "m³"
                      : preset.cbmUnit === "CENTIMETER_CUBIC"
                        ? "cm³"
                        : "m³"}
                </span>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Weight */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
        <SelectField
          id="weightUnit"
          label="Weight Unit"
          placeholder="Select weight unit"
          value={formData.weightUnit}
          options={[
            { value: "KILOGRAMS", label: "Kilograms (kg)" },
            { value: "POUNDS", label: "Pounds (lb)" },
            { value: "GRAMS", label: "Grams (g)" },
            { value: "TONS", label: "Tons (t)" },
            { value: "OUNCES", label: "Ounces (oz)" },
            { value: "SHORT_TON", label: "Short Ton" },
            { value: "LONG_TON", label: "Long Ton" },
          ]}
          onChange={(value) => handleInputChange("weightUnit", value)}
        />
        <InputField
          id="weight"
          label={`Weight (${formData.weightUnit.toLowerCase()})`}
          type="number"
          placeholder="e.g: 1500"
          value={formData.weight}
          onChange={(e) => handleInputChange("weight", e.target.value)}
          required
        />
      </div>

      {/* Dimensions */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Dimensions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
          <InputField
            id="length"
            label="Length"
            type="number"
            step="0.01"
            placeholder="e.g: 12.0"
            value={formData.length}
            onChange={(e) => handleInputChange("length", e.target.value)}
            required
          />
          <InputField
            id="width"
            label="Width"
            type="number"
            step="0.01"
            placeholder="e.g: 2.4"
            value={formData.width}
            onChange={(e) => handleInputChange("width", e.target.value)}
            required
          />
          <InputField
            id="height"
            label="Height"
            type="number"
            step="0.01"
            placeholder="e.g: 2.6"
            value={formData.height}
            onChange={(e) => handleInputChange("height", e.target.value)}
            required
          />
        </div>
      </div>

      {/* CBM Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
        <SelectField
          id="cbmUnit"
          label="CBM Unit"
          placeholder="Select CBM unit"
          value={formData.cbmUnit}
          options={[
            { value: "METER_CUBIC", label: "Cubic Meters (m³)" },
            { value: "FEET_CUBIC", label: "Cubic Feet (ft³)" },
            { value: "CENTIMETER_CUBIC", label: "Cubic Centimeters (cm³)" },
            { value: "INCH_CUBIC", label: "Cubic Inches (in³)" },
          ]}
          onChange={(value) => handleInputChange("cbmUnit", value)}
        />
        <InputField
          id="cbmValue"
          label="CBM Value"
          type="number"
          step="0.01"
          placeholder="Auto-calculated"
          value={formData.cbmValue}
          onChange={(e) => handleInputChange("cbmValue", e.target.value)}
          required
          readOnly
        />
      </div>

      {/* Currency Conversion Rate */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
        <InputField
          id="currencyConvRate"
          label="Currency Conversion Rate (USD to TZS)"
          type="number"
          step="0.01"
          placeholder="e.g: 2500.00"
          value={formData.currencyConvRate}
          onChange={(e) =>
            handleInputChange("currencyConvRate", e.target.value)
          }
        />
        <div className="flex items-end">
          <div className="text-sm text-gray-500 p-3 bg-gray-50 rounded-lg">
            <p className="font-medium">Currency Conversion</p>
            <p className="text-xs mt-1">
              This rate will be used for converting USD amounts to Tanzanian
              Shillings (TZS) in invoices and financial calculations for this
              batch.
            </p>
          </div>
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end gap-3 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => onOpenChange(false)}
          disabled={loading}
        >
          Cancel
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating...
            </>
          ) : (
            "Create Batch"
          )}
        </Button>
      </div>
    </form>
  );
};

// Dimension presets based on batch type and common standards
const dimensionPresets = {
  CONTAINER: [
    {
      name: "20ft Standard Container",
      length: 6.1,
      width: 2.44,
      height: 2.59,
      weight: 2300,
      weightUnit: "KILOGRAMS" as WeightUnit,
      cbmUnit: "METER_CUBIC" as DimensionUnit,
      description: "6.1m x 2.44m x 2.59m",
    },
    {
      name: "40ft Standard Container",
      length: 12.2,
      width: 2.44,
      height: 2.59,
      weight: 3800,
      weightUnit: "KILOGRAMS" as WeightUnit,
      cbmUnit: "METER_CUBIC" as DimensionUnit,
      description: "12.2m x 2.44m x 2.59m",
    },
    {
      name: "40ft High Cube Container",
      length: 12.2,
      width: 2.44,
      height: 2.9,
      weight: 4200,
      weightUnit: "KILOGRAMS" as WeightUnit,
      cbmUnit: "METER_CUBIC" as DimensionUnit,
      description: "12.2m x 2.44m x 2.9m",
    },
    {
      name: "45ft High Cube Container",
      length: 13.7,
      width: 2.44,
      height: 2.9,
      weight: 4800,
      weightUnit: "KILOGRAMS" as WeightUnit,
      cbmUnit: "METER_CUBIC" as DimensionUnit,
      description: "13.7m x 2.44m x 2.9m",
    },
  ],
  PALLET: [
    {
      name: "Euro Pallet (EPAL)",
      length: 1.2,
      width: 0.8,
      height: 1.8,
      weight: 25,
      weightUnit: "KILOGRAMS" as WeightUnit,
      cbmUnit: "METER_CUBIC" as DimensionUnit,
      description: "1.2m x 0.8m x 1.8m",
    },
    {
      name: "Standard US Pallet",
      length: 1.22,
      width: 1.02,
      height: 1.8,
      weight: 30,
      weightUnit: "KILOGRAMS" as WeightUnit,
      cbmUnit: "METER_CUBIC" as DimensionUnit,
      description: "1.22m x 1.02m x 1.8m",
    },
    {
      name: "UK Standard Pallet",
      length: 1.2,
      width: 1.0,
      height: 1.8,
      weight: 28,
      weightUnit: "KILOGRAMS" as WeightUnit,
      cbmUnit: "METER_CUBIC" as DimensionUnit,
      description: "1.2m x 1m x 1.8m",
    },
  ],
  BULK: [
    {
      name: "Small Bulk Load",
      length: 2.0,
      width: 2.0,
      height: 2.0,
      weight: 1000,
      weightUnit: "KILOGRAMS" as WeightUnit,
      cbmUnit: "METER_CUBIC" as DimensionUnit,
      description: "2m x 2m x 2m",
    },
    {
      name: "Medium Bulk Load",
      length: 3.0,
      width: 3.0,
      height: 3.0,
      weight: 2500,
      weightUnit: "KILOGRAMS" as WeightUnit,
      cbmUnit: "METER_CUBIC" as DimensionUnit,
      description: "3m x 3m x 3m",
    },
    {
      name: "Large Bulk Load",
      length: 5.0,
      width: 3.0,
      height: 2.5,
      weight: 5000,
      weightUnit: "KILOGRAMS" as WeightUnit,
      cbmUnit: "METER_CUBIC" as DimensionUnit,
      description: "5m x 3m x 2.5m",
    },
  ],
  MIXED: [
    {
      name: "Mixed Small",
      length: 2.5,
      width: 2.0,
      height: 2.0,
      weight: 800,
      weightUnit: "KILOGRAMS" as WeightUnit,
      cbmUnit: "METER_CUBIC" as DimensionUnit,
      description: "2.5m x 2m x 2m",
    },
    {
      name: "Mixed Medium",
      length: 4.0,
      width: 2.5,
      height: 2.5,
      weight: 1500,
      weightUnit: "KILOGRAMS" as WeightUnit,
      cbmUnit: "METER_CUBIC" as DimensionUnit,
      description: "4m x 2.5m x 2.5m",
    },
    {
      name: "Mixed Large",
      length: 6.0,
      width: 3.0,
      height: 2.8,
      weight: 3000,
      weightUnit: "KILOGRAMS" as WeightUnit,
      cbmUnit: "METER_CUBIC" as DimensionUnit,
      description: "6m x 3m x 2.8m",
    },
  ],
};
