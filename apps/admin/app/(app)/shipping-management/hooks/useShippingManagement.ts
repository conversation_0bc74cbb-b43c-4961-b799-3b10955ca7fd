"use client";

import { redirect } from "next/navigation";
import { useState, useEffect, useMemo, useCallback } from "react";
import { useAppSelector } from "@/store/hooks";
import {
  freightService,
  shipmentService,
  type FreightWithRelations,
} from "@/lib/logistics";

import {
  type ShippingStats,
  type ShippingManagementState,
  type ShipmentDisplay,
  type TrackingMetadata,
  type TrackingLocation,
} from "../components/ShippingManagementContainer";
import { toast } from "sonner";

/**
 * Custom hook for shipping management logic
 *
 * Encapsulates all business logic, state management, and side effects
 * for the shipping management feature. Follows React best practices with
 * proper memoization and optimization.
 */
export function useShippingManagement() {
  const { user: authUser } = useAppSelector((state) => state.auth);

  // State management
  const [state, setState] = useState<ShippingManagementState>({
    searchTerm: "",
    statusFilter: "all",
    typeFilter: "all",
    loading: true,
    refreshing: false,
    shipments: [],
    shippingStats: {
      totalShipments: 0,
      activeShipments: 0,
      onTimeDeliveries: 0,
      delayedShipments: 0,
      upcomingDepartures: 0,
      completedToday: 0,
    },
    isNewShipmentOpen: false,
    selectedShipment: null,
    columnFilters: [],
    selectedShipments: new Set<string>(),
    isBulkTaskDialogOpen: false,
  });

  // Additional state for freights (needed for form)
  const [freights, setFreights] = useState<FreightWithRelations[]>([]);

  // Calculate shipping stats
  const calculateShippingStats = useCallback(
    (shipments: ShipmentDisplay[]): ShippingStats => {
      const todayFormatted = formatDate(new Date().toISOString());

      return {
        totalShipments: shipments.length,
        activeShipments: shipments.filter((s) => s.status === "IN_TRANSIT")
          .length,
        onTimeDeliveries: shipments.filter(
          (s) => s.status === "DELIVERED" && !s.delay
        ).length,
        delayedShipments: shipments.filter((s) => s.delay && s.delay > 0)
          .length,
        upcomingDepartures: shipments.filter(
          (s) =>
            s.status === "SCHEDULED" &&
            new Date(s.departureDateTime) >= new Date()
        ).length,
        completedToday: shipments.filter(
          (s) =>
            s.status === "DELIVERED" &&
            formatDate(s.arrivalDateTime) === todayFormatted
        ).length,
      };
    },
    []
  );

  // Helper function to format date for comparison
  const formatDate = useCallback((dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  }, []);

  // Transform shipment data from database to display format
  const transformShipmentToDisplay = useCallback(
    (
      shipment: any,
      freightMap: Map<string, FreightWithRelations>
    ): ShipmentDisplay => {
      // Get freight information - either from relation or map lookup
      const freight = shipment.freights || freightMap.get(shipment.freight_id);

      // Get batch information with enhanced mapping
      const batch = shipment.batches;
      const batchInfo = batch
        ? {
            id: batch.id,
            name: batch.name || "Unnamed Batch",
            code: batch.code || "No Code",
            type: batch.type || "GENERAL",
            freightType: batch.freight_type || freight?.type || "GENERAL",
          }
        : null;

      const batchDisplay = batchInfo
        ? `${batchInfo.code} (${batchInfo.name})`
        : "No Batch Assigned";

      // Calculate progress based on departure and arrival times
      const now = new Date();
      const departure = shipment.departure
        ? new Date(shipment.departure)
        : null;
      const arrival = shipment.arrival ? new Date(shipment.arrival) : null;
      const estimatedArrival = shipment.estimated_time_of_arrival
        ? new Date(shipment.estimated_time_of_arrival)
        : arrival;

      let progress = 0;
      let status = "SCHEDULED";

      if (departure && arrival) {
        if (now >= arrival) {
          progress = 100;
          status = "DELIVERED";
        } else if (now >= departure) {
          const totalJourneyMs = arrival.getTime() - departure.getTime();
          const elapsedMs = now.getTime() - departure.getTime();
          progress = Math.min(
            95,
            Math.max(5, (elapsedMs / totalJourneyMs) * 100)
          );
          status = "IN_TRANSIT";
        }
      }

      // Determine if delayed
      const isDelayed =
        estimatedArrival && now > estimatedArrival && status !== "DELIVERED";
      if (isDelayed) {
        status = "DELAYED";
      }

      // Calculate delay in minutes
      const delay =
        isDelayed && estimatedArrival
          ? Math.floor(
              (now.getTime() - estimatedArrival.getTime()) / (1000 * 60)
            )
          : undefined;

      // Extract tracking metadata and route information
      const trackingMetadata = shipment.metadata as TrackingMetadata | null;
      const routeLocations = trackingMetadata?.locations || [];
      const hasRealTimeTracking = !!(
        trackingMetadata && routeLocations.length > 0
      );

      // Use shipment-specific locations, fallback to freight locations, then tracking data
      let shipmentDeparting =
        shipment.departing || freight?.origin || "Unknown Origin";
      let shipmentDestination =
        shipment.destination || freight?.destination || "Unknown Destination";

      // Update origin and destination from tracking data if available
      if (hasRealTimeTracking && routeLocations.length >= 2) {
        const trackingOrigin = routeLocations[0];
        const trackingDestination = routeLocations[1];

        // Use tracking data locations if they exist, otherwise fallback to existing
        shipmentDeparting = trackingOrigin?.name || shipmentDeparting;
        shipmentDestination = trackingDestination?.name || shipmentDestination;
      }

      // Determine current location based on tracking data or progress
      let currentLocation = "Unknown Location";

      if (hasRealTimeTracking && trackingMetadata) {
        // Use real-time tracking data to determine current location
        if (routeLocations.length >= 2) {
          const origin = routeLocations[0];
          const destination = routeLocations[1];

          // Use the shipping status from metadata to determine current location
          switch (trackingMetadata.metadata.shippingStatus) {
            case "IN_TRANSIT":
              currentLocation = `En route from ${origin?.name || "Unknown"} to ${destination?.name || "Unknown"}`;
              break;
            case "DELIVERED":
            case "ARRIVED":
              currentLocation = destination?.name || "Delivered";
              break;
            default:
              currentLocation = origin?.name || "At Origin";
          }
        }
      } else if (shipmentDeparting && shipmentDestination) {
        // Fallback to progress-based location
        currentLocation =
          progress > 50
            ? `En route to ${shipmentDestination}`
            : shipmentDeparting;
      }

      // Override status with tracking data if available
      if (hasRealTimeTracking && trackingMetadata) {
        const trackingStatus = trackingMetadata.metadata.shippingStatus;
        switch (trackingStatus) {
          case "IN_TRANSIT":
            status = "IN_TRANSIT";
            break;
          case "DELIVERED":
          case "ARRIVED":
            status = "DELIVERED";
            progress = 100;
            break;
          case "SCHEDULED":
            status = "SCHEDULED";
            break;
          default:
            // Keep calculated status
            break;
        }
      }

      return {
        id: shipment.id,
        name: `Shipment to ${shipmentDestination}`,
        trackingNumber: shipment.tracking_number || "N/A",
        billOfLading: (shipment as any).bill_of_lading || undefined,
        freight: freight?.name || "Unknown Freight",
        freightType: freight?.type || batchInfo?.freightType || "GENERAL",
        batch: batchDisplay,
        batchInfo: batchInfo, // Add detailed batch info for enhanced display
        freightInfo: freight
          ? {
              id: freight.id,
              name: freight.name,
              code: freight.code,
              type: freight.type,
              origin: freight.origin,
              destination: freight.destination,
            }
          : null,
        origin: shipmentDeparting,
        destination: shipmentDestination,
        departureDateTime: shipment.departure || new Date().toISOString(),
        arrivalDateTime:
          shipment.arrival ||
          shipment.estimated_time_of_arrival ||
          new Date().toISOString(),
        estimatedTimeElapsed: departure
          ? status === "SCHEDULED"
            ? "Not started"
            : calculateTimeElapsed(shipment.departure)
          : "Not started",
        estimatedTimeOfArrival: estimatedArrival
          ? status === "DELIVERED"
            ? "Delivered"
            : calculateETA(
                shipment.departure || new Date().toISOString(),
                estimatedArrival.toISOString()
              )
          : "N/A",
        status,
        currentLocation,
        progress: Math.round(progress),
        delay,
        // Tracking metadata and route information
        trackingMetadata,
        routeLocations,
        hasRealTimeTracking,
      };
    },
    []
  );

  // Helper function to calculate time elapsed
  const calculateTimeElapsed = useCallback(
    (departureDateTime: string): string => {
      const departure = new Date(departureDateTime);
      const now = new Date();
      const diffMs = now.getTime() - departure.getTime();

      if (diffMs < 0) return "Not started";

      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

      if (diffHours > 0) {
        return `${diffHours}h ${diffMinutes}m`;
      }
      return `${diffMinutes}m`;
    },
    []
  );

  // Helper function to calculate ETA
  const calculateETA = useCallback(
    (departureDateTime: string, arrivalDateTime: string): string => {
      const departure = new Date(departureDateTime);
      const arrival = new Date(arrivalDateTime);
      const now = new Date();

      if (now >= arrival) {
        return "Arrived";
      }

      if (now < departure) {
        return (
          formatDateTime(arrival.toISOString()).date +
          " " +
          formatDateTime(arrival.toISOString()).time
        );
      }

      const remainingMs = arrival.getTime() - now.getTime();
      const remainingHours = Math.floor(remainingMs / (1000 * 60 * 60));
      const remainingMinutes = Math.floor(
        (remainingMs % (1000 * 60 * 60)) / (1000 * 60)
      );

      if (remainingHours > 0) {
        return `${remainingHours}h ${remainingMinutes}m`;
      }
      return `${remainingMinutes}m`;
    },
    []
  );

  // Helper function to format datetime
  const formatDateTime = useCallback(
    (dateString: string | null): { date: string; time: string } => {
      if (!dateString) return { date: "N/A", time: "N/A" };
      const date = new Date(dateString);
      return {
        date: date.toLocaleDateString("en-US", {
          year: "numeric",
          month: "short",
          day: "numeric",
        }),
        time: date.toLocaleTimeString("en-US", {
          hour: "2-digit",
          minute: "2-digit",
        }),
      };
    },
    []
  );

  // Data fetching
  const fetchShippingData = useCallback(
    async (refresh = false) => {
      if (!authUser) return;

      try {
        if (refresh) setState((prev) => ({ ...prev, refreshing: true }));
        else setState((prev) => ({ ...prev, loading: true }));

        const [freightsResult, shipmentsResult] = await Promise.all([
          freightService.getAll({ limit: 100 }),
          shipmentService.getAll({ limit: 100 }),
        ]);

        if (freightsResult.success && freightsResult.data) {
          setFreights(freightsResult.data);
        }

        if (shipmentsResult.success && shipmentsResult.data) {
          // Create freight map for quick lookup
          const freightMap = new Map<string, FreightWithRelations>(
            freightsResult.data?.map((freight: FreightWithRelations) => [
              freight.id,
              freight,
            ]) || []
          );

          // Transform shipments to display format with enhanced batch/freight mapping
          const transformedShipments = shipmentsResult.data.map(
            (shipment: any) => transformShipmentToDisplay(shipment, freightMap)
          );

          const stats = calculateShippingStats(transformedShipments);
          setState((prev) => ({
            ...prev,
            shipments: transformedShipments,
            shippingStats: stats,
          }));
        } else {
          setState((prev) => ({ ...prev, shipments: [] }));
        }
      } catch (error) {
        console.error("Error fetching shipping data:", error);
        setState((prev) => ({ ...prev, shipments: [] }));
      } finally {
        setState((prev) => ({ ...prev, loading: false, refreshing: false }));
      }
    },
    [authUser, calculateShippingStats, transformShipmentToDisplay]
  );

  // Effects
  useEffect(() => {
    if (authUser) {
      fetchShippingData();
    } else {
      setState((prev) => ({ ...prev, loading: false }));
    }
  }, [authUser, fetchShippingData]);

  // Recalculate stats when filtered shipments change
  useEffect(() => {
    const stats = calculateShippingStats(state.shipments);
    setState((prev) => ({ ...prev, shippingStats: stats }));
  }, [state.shipments, calculateShippingStats]);

  // Filter shipments based on search, status, type, and column filters
  const filteredShipments = useMemo(() => {
    return state.shipments.filter((shipment) => {
      const matchesSearch =
        shipment.name.toLowerCase().includes(state.searchTerm.toLowerCase()) ||
        shipment.trackingNumber
          .toLowerCase()
          .includes(state.searchTerm.toLowerCase()) ||
        shipment.freight
          .toLowerCase()
          .includes(state.searchTerm.toLowerCase()) ||
        shipment.batch.toLowerCase().includes(state.searchTerm.toLowerCase()) ||
        shipment.origin
          .toLowerCase()
          .includes(state.searchTerm.toLowerCase()) ||
        shipment.destination
          .toLowerCase()
          .includes(state.searchTerm.toLowerCase());

      const matchesStatus =
        state.statusFilter === "all" ||
        shipment.status.toLowerCase() === state.statusFilter.toLowerCase();

      const matchesType =
        state.typeFilter === "all" ||
        shipment.freightType.toLowerCase() === state.typeFilter.toLowerCase();

      // Apply column filters
      const matchesColumnFilters = state.columnFilters.every((filter) => {
        const shipmentValue = shipment[filter.column as keyof ShipmentDisplay];
        return matchesFilter(shipmentValue, filter.value);
      });

      return (
        matchesSearch && matchesStatus && matchesType && matchesColumnFilters
      );
    });
  }, [
    state.shipments,
    state.searchTerm,
    state.statusFilter,
    state.typeFilter,
    state.columnFilters,
  ]);

  // Helper function to check if a value matches a filter
  const matchesFilter = useCallback(
    (value: any, filterValue: string): boolean => {
      if (value == null) return false;

      if (Array.isArray(value)) {
        return value.some((item: any) =>
          String(item).toLowerCase().includes(filterValue.toLowerCase())
        );
      } else if (typeof value === "string") {
        return value.toLowerCase().includes(filterValue.toLowerCase());
      } else if (typeof value === "number") {
        return String(value) === filterValue;
      } else if (value instanceof Date) {
        const dateStr = value.toISOString().split("T")[0];
        return dateStr === filterValue;
      } else {
        return String(value).toLowerCase().includes(filterValue.toLowerCase());
      }
    },
    []
  );

  // Action handlers
  const handleRefresh = useCallback(() => {
    fetchShippingData(true);
  }, [fetchShippingData]);

  const handleShipmentAction = useCallback(
    (action: string, shipment?: ShipmentDisplay) => {
      switch (action) {
        case "create":
          setState((prev) => ({ ...prev, isNewShipmentOpen: true }));
          break;
        case "view":
          if (shipment) redirect(`/shipping-management/${shipment.id}`);
          break;
        case "edit":
        case "delete":
          setState((prev) => ({
            ...prev,
            selectedShipment: shipment || null,
            [`is${action.charAt(0).toUpperCase() + action.slice(1)}DialogOpen`]:
              true,
          }));
          break;
      }
    },
    []
  );

  const handleDialogClose = useCallback((dialogType: string) => {
    setState((prev) => ({
      ...prev,
      [`is${dialogType}Open`]: false,
      selectedShipment: null,
    }));
  }, []);

  const handleShipmentMutation = useCallback(() => {
    fetchShippingData(true);
  }, [fetchShippingData]);

  // Bulk actions handlers
  const handleBulkStatusUpdate = useCallback(
    async (status: string) => {
      const selectedIds = Array.from(state.selectedShipments);
      if (selectedIds.length === 0) {
        console.warn("No shipments selected for bulk status update");
        return;
      }

      try {
        // Update each selected shipment's status
        const updatePromises = selectedIds.map((shipmentId) =>
          shipmentService.update(shipmentId, { status: status as any })
        );

        const updateResults = await Promise.all(updatePromises);
        const successfulUpdates = updateResults.filter(
          (result) => result.success
        ).length;
        const failedUpdates = updateResults.length - successfulUpdates;

        if (successfulUpdates > 0) {
          console.log(
            `Successfully updated ${successfulUpdates} shipment(s) to ${status}`,
            failedUpdates > 0 ? `${failedUpdates} update(s) failed` : ""
          );

          // Clear selection after successful action
          setState((prev) => ({ ...prev, selectedShipments: new Set() }));

          // Refresh data to show updated statuses
          fetchShippingData(true);
        } else {
          console.error("Failed to update any shipment status");
        }
      } catch (error) {
        console.error("Error in bulk status update:", error);
      }
    },
    [state.selectedShipments, fetchShippingData]
  );

  const handleBulkDelete = useCallback(() => {
    const selectedIds = Array.from(state.selectedShipments);
    if (selectedIds.length === 0) return;

    console.log(`Deleting ${selectedIds.length} shipments:`, selectedIds);
    // TODO: Implement bulk delete logic
    setState((prev) => ({ ...prev, selectedShipments: new Set() })); // Clear selection after action
  }, [state.selectedShipments]);

  // State update handlers
  const updateState = useCallback(
    (updates: Partial<ShippingManagementState>) => {
      setState((prev) => ({ ...prev, ...updates }));
    },
    []
  );

  // Clear selections handler
  const handleClearSelections = useCallback(() => {
    setState((prev) => ({ ...prev, selectedShipments: new Set() }));
    toast.success("Shipment selections cleared");
  }, []);

  // Bulk task creation handlers
  const handleBulkCreateTasks = useCallback(() => {
    if (state.selectedShipments.size === 0) {
      toast.error("Please select shipments to create tasks for");
      return;
    }
    setState((prev) => ({ ...prev, isBulkTaskDialogOpen: true }));
  }, [state.selectedShipments.size]);

  const setIsBulkTaskDialogOpen = useCallback((isOpen: boolean) => {
    setState((prev) => ({ ...prev, isBulkTaskDialogOpen: isOpen }));
  }, []);

  const selectedItemsForTasks = Array.from(state.selectedShipments).map(
    (shipmentId) => {
      const shipment = state.shipments.find((s) => s.id === shipmentId);
      return {
        id: shipmentId,
        name: shipment?.freight || "Unknown Shipment",
        identifier: shipment?.id,
      };
    }
  );

  const handleTasksCreated = useCallback(() => {
    // Clear selections and refresh data
    setState((prev) => ({ ...prev, selectedShipments: new Set() }));
    fetchShippingData(true);
  }, [fetchShippingData]);

  return {
    state,
    freights,
    filteredShipments,
    handleRefresh,
    handleShipmentAction,
    handleDialogClose,
    handleShipmentMutation,
    handleBulkStatusUpdate,
    handleBulkDelete,
    handleClearSelections,
    updateState,
    handleBulkCreateTasks,
    isBulkTaskDialogOpen: state.isBulkTaskDialogOpen,
    setIsBulkTaskDialogOpen,
    selectedItemsForTasks,
    handleTasksCreated,
  };
}
