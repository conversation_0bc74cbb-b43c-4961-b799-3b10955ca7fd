"use client";

import { memo, useState, useEffect, useCallback } from "react";
import {
  Loader2,
  Navigation,
  Upload,
  X,
  FileText,
  Package,
  Plane,
  Ship,
  Truck,
} from "lucide-react";
import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";

import {
  Combobox,
  ComboboxTrigger,
  ComboboxContent,
  ComboboxCommand,
  ComboboxInput,
  ComboboxList,
  ComboboxEmpty,
  ComboboxGroup,
  ComboboxItem,
} from "@workspace/ui/components/combobox";
import { useAppSelector } from "@/store/hooks";
import {
  freightService,
  shipmentService,
  batchService,
  documentService,
  type FreightWithRelations,
  type DocumentInsert,
  type StatusEnum,
} from "@/lib/logistics";
import {
  DocumentPreviewDialog,
  useDocumentPreview,
} from "@/components/ui/document-preview-dialog";
import { toast } from "sonner";

interface NewShipmentFormProps {
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

// Helper function to get freight type icon
const getFreightTypeIcon = (type: string) => {
  switch (type?.toLowerCase()) {
    case "air":
      return Plane;
    case "sea":
      return Ship;
    case "land":
    case "road":
      return Truck;
    default:
      return Package;
  }
};

/**
 * New Shipment Form Component
 *
 * Simplified form for creating new shipments with freight and batch selection,
 * bill of lading, freight reference ID, and document upload capabilities.
 * Integrates with realtime tracking through freight selection.
 */
export const NewShipmentForm = memo<NewShipmentFormProps>(
  ({ onOpenChange, onSuccess }) => {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [freights, setFreights] = useState<FreightWithRelations[]>([]);
    const [filteredFreights, setFilteredFreights] = useState<
      FreightWithRelations[]
    >([]);
    const [batches, setBatches] = useState<any[]>([]);
    const [loadingBatches, setLoadingBatches] = useState(false);
    const [attachedFiles, setAttachedFiles] = useState<File[]>([]);
    const [freightSearchTerm, setFreightSearchTerm] = useState("");
    const [isFreightComboboxOpen, setIsFreightComboboxOpen] = useState(false);
    const [isBatchComboboxOpen, setIsBatchComboboxOpen] = useState(false);
    const [selectedFreight, setSelectedFreight] =
      useState<FreightWithRelations | null>(null);

    // Get auth user for document uploads
    const authUser = useAppSelector((state) => state.auth.user);

    // Document preview hook
    const {
      isOpen: isPreviewOpen,
      document: previewDocument,
      closePreview,
    } = useDocumentPreview();

    const [formData, setFormData] = useState({
      freight_id: "",
      batch_id: "none",
      bill_of_lading: "",
      freight_reference_id: "",
    });

    // Load freights on component mount
    const loadFreights = async () => {
      try {
        const result = await freightService.getAll({ limit: 100 });
        console.log("Freights result:", result.data);

        if (result.success && result.data) {
          setFreights(result.data);
          setFilteredFreights(result.data);
        }
      } catch (error) {
        console.error("Error loading freights:", error);
      }
    };

    useEffect(() => {
      loadFreights();
    }, []);

    // Filter freights based on search term
    const filterFreights = useCallback(
      (searchTerm: string) => {
        if (!searchTerm.trim()) {
          setFilteredFreights(freights);
          return;
        }

        const filtered = freights.filter(
          (freight: any) =>
            freight.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            freight.scac_codes.toLowerCase().includes(searchTerm.toLowerCase())
        );
        setFilteredFreights(filtered);
      },
      [freights]
    );

    // Update filtered freights when search term changes
    useEffect(() => {
      filterFreights(freightSearchTerm);
    }, [freightSearchTerm, filterFreights]);

    // Load all batches on component mount
    const loadBatches = async () => {
      setLoadingBatches(true);
      try {
        const result = await batchService.getAll({ limit: 100 });
        if (result.success && result.data) {
          setBatches(result.data);
        } else {
          setBatches([]);
        }
      } catch (error) {
        console.error("Error loading batches:", error);
        setBatches([]);
      } finally {
        setLoadingBatches(false);
      }
    };

    useEffect(() => {
      loadBatches();
    }, []);

    const handleInputChange = (field: string, value: string) => {
      setFormData((prev) => ({ ...prev, [field]: value }));
    };

    const handleFreightSelect = (freight: FreightWithRelations) => {
      setSelectedFreight(freight);
      setFormData((prev) => ({ ...prev, freight_id: freight.id }));
      setFreightSearchTerm(`${freight.name} - ${freight.scac_codes}`);
      setIsFreightComboboxOpen(false);
    };

    const handleFreightClear = () => {
      setSelectedFreight(null);
      setFormData((prev) => ({ ...prev, freight_id: "" }));
      setFreightSearchTerm("");
    };

    const handleBatchSelect = (batchId: string) => {
      handleInputChange("batch_id", batchId);
      setIsBatchComboboxOpen(false);
    };

    const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(event.target.files || []);
      setAttachedFiles((prev) => [...prev, ...files]);
    };

    const removeFile = (index: number) => {
      setAttachedFiles((prev) => prev.filter((_, i) => i !== index));
    };

    const getFileTypeIcon = (fileName: string) => {
      const extension = fileName.split(".").pop()?.toLowerCase();
      switch (extension) {
        case "pdf":
          return <FileText className="h-4 w-4 text-red-500" />;
        case "doc":
        case "docx":
          return <FileText className="h-4 w-4 text-blue-500" />;
        case "xls":
        case "xlsx":
          return <FileText className="h-4 w-4 text-green-500" />;
        case "jpg":
        case "jpeg":
        case "png":
        case "gif":
          return <FileText className="h-4 w-4 text-purple-500" />;
        default:
          return <FileText className="h-4 w-4 text-gray-500" />;
      }
    };

    // Types for tracking data
    interface SinayApiResponse {
      metadata: {
        shipmentType: string;
        shipmentNumber: string;
        sealine: string;
        sealineName: string;
        shippingStatus: string;
        updatedAt: string;
        warnings: string[];
      };
      locations: Array<{
        name: string;
        state: string;
        country: string;
        countryCode: string;
        locode: string;
        coordinates: {
          lat: number;
          lng: number;
        };
        timezone: string;
      }>;
      routeData: {
        routeSegments: Array<{
          path: Array<{ lat: number; lng: number }>;
          routeType: string;
        }>;
        coordinates: {
          lat: number;
          lng: number;
        };
        ais: {
          status: string;
          data: any;
        };
      };
      route: any;
      vessels: any[];
      containers: any[];
    }

    // Function to get shipment tracking data from Sinay API
    const getShipmentTrackingData = async (
      scacCode: string,
      billOfLading: string
    ): Promise<SinayApiResponse | null> => {
      try {
        // Validate required parameters
        if (!billOfLading) {
          console.warn("No bill of lading provided for tracking");
          return null;
        }

        if (!scacCode) {
          console.warn("No SCAC code available for freight, skipping tracking");
          return null;
        }

        // Build query parameters
        const params = new URLSearchParams({
          shipmentNumber: billOfLading,
          sealine: scacCode,
          shipmentType: "BL",
          route: "true",
          ais: "true",
        });

        const options = {
          method: "GET",
          headers: {
            accept: "application/json",
            API_KEY: "ccba8297-dd22-4e61-acf7-88367527a0db",
          },
        };

        const response = await fetch(
          `https://api.sinay.ai/container-tracking/api/v2/shipment?${params.toString()}`,
          options
        );

        if (!response.ok) {
          return null;
        }

        const data: SinayApiResponse = await response.json();

        // Log successful API response for debugging
        console.log("✅ Sinay API response received:", {
          shipmentNumber: billOfLading,
          sealine: scacCode,
          status: data.metadata?.shippingStatus,
          locations: data.locations?.length || 0,
        });

        return data;
      } catch (error: any) {
        console.error("❌ Failed to fetch shipment tracking data:", {
          error: error.message,
          shipmentNumber: billOfLading,
          scacCode,
        });
        return null;
      }
    };

    // Function to determine shipment status based on tracking data
    const determineShipmentStatus = (
      trackingData: SinayApiResponse
    ): "ACTIVE" | "IN_TRANSIT" | "DELIVERED" => {
      if (!trackingData.locations || trackingData.locations.length < 2) {
        // Map external status to our internal status
        const externalStatus = trackingData.metadata.shippingStatus;
        if (externalStatus === "IN_TRANSIT") return "IN_TRANSIT";
        if (externalStatus === "DELIVERED") return "DELIVERED";
        return "ACTIVE";
      }

      // Compare destination coordinates with current coordinates
      const destination = trackingData.locations[1]; // locations[1] is destination
      const currentCoords = trackingData.routeData.coordinates;

      if (destination && currentCoords) {
        const latDiff = Math.abs(
          destination.coordinates.lat - currentCoords.lat
        );
        const lngDiff = Math.abs(
          destination.coordinates.lng - currentCoords.lng
        );

        // If coordinates are very close (within 0.01 degrees), consider as delivered
        if (latDiff < 0.01 && lngDiff < 0.01) {
          return "DELIVERED";
        }
      }

      // Map external status to our internal status
      const externalStatus = trackingData.metadata.shippingStatus;
      if (externalStatus === "IN_TRANSIT") return "IN_TRANSIT";
      if (externalStatus === "DELIVERED") return "DELIVERED";
      return "ACTIVE";
    };

    const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault();

      // Basic validation - at least one of freight or batch must be selected
      if (
        !formData.freight_id &&
        (!formData.batch_id || formData.batch_id === "none")
      ) {
        toast.error("Please select either a freight or batch");
        return;
      }

      if (!authUser?.accountId) {
        toast.error("User session not found. Please log in again.");
        return;
      }

      setIsSubmitting(true);
      try {
        // Get SCAC code from selected freight
        const scacCode = selectedFreight?.scac_codes || "";

        // Fetch tracking data if we have both SCAC code and bill of lading
        let trackingData: SinayApiResponse | null = null;
        let shipmentStatus: "ACTIVE" | "IN_TRANSIT" | "DELIVERED" = "ACTIVE"; // Default status
        let metadata: any = null;

        if (scacCode && formData.bill_of_lading) {
          trackingData = await getShipmentTrackingData(
            scacCode,
            formData.bill_of_lading
          );

          if (trackingData) {
            // Determine shipment status
            shipmentStatus = determineShipmentStatus(trackingData);

            // Prepare metadata in the required format
            metadata = {
              metadata: trackingData.metadata,
              locations: trackingData.locations,
            };

            // Create shipment using the shipment service
            const result = await shipmentService.createShipment({
              account_id: authUser.accountId,
              freight_id: formData.freight_id || null,
              batch_id:
                formData.batch_id === "none" ? null : formData.batch_id || null,
              bill_of_lading: formData.bill_of_lading || null,
              freight_reference_id: formData.freight_reference_id || null,
              status: shipmentStatus,
              metadata: metadata,
            });

            if (result.success && result.data) {
              // Upload documents if any
              if (attachedFiles.length > 0 && authUser?.accountId) {
                let uploadedCount = 0;
                let failedCount = 0;

                for (const file of attachedFiles) {
                  try {
                    // Upload file to storage
                    const uploadResult = await documentService.uploadToStorage({
                      content: file,
                      fileName: file.name,
                      contentType: file.type,
                      folder: "shipment-documents",
                      metadata: {
                        shipmentId: result.data.id,
                      },
                    });

                    if (uploadResult.success && uploadResult.data) {
                      // Create document record
                      const documentData: DocumentInsert = {
                        name: file.name,
                        path: uploadResult.data,
                        category: "shipment",
                        description: `Document attached to shipment: ${result.data.tracking_number}`,
                        associated_table: "shipments",
                        associated_id: result.data.id,
                        account_id: authUser.accountId,
                        status: "ACTIVE" as StatusEnum,
                      };

                      const createResult =
                        await documentService.createDocument(documentData);
                      if (createResult.success) {
                        uploadedCount++;
                      } else {
                        failedCount++;
                        console.warn(
                          `Failed to create document record for ${file.name}:`,
                          createResult.error
                        );
                      }
                    } else {
                      failedCount++;
                      console.warn(
                        `Failed to upload ${file.name}:`,
                        uploadResult.error
                      );
                    }
                  } catch (error) {
                    failedCount++;
                    console.error(`Error uploading ${file.name}:`, error);
                  }
                }

                if (failedCount > 0) {
                  toast.warning(
                    `Shipment created successfully. ${uploadedCount} document(s) uploaded, ${failedCount} failed. Tracking Number: ${result.data.tracking_number}`
                  );
                } else {
                  toast.success(
                    `Shipment created successfully with ${uploadedCount} document(s). Tracking Number: ${result.data.tracking_number}`
                  );
                }
              } else {
                toast.success(
                  `Shipment created successfully! Tracking Number: ${result.data.tracking_number}`
                );
              }

              onSuccess();
              onOpenChange(false);

              // Reset form
              setFormData({
                freight_id: "",
                batch_id: "none",
                bill_of_lading: "",
                freight_reference_id: "",
              });
              setSelectedFreight(null);
              setFreightSearchTerm("");
              setAttachedFiles([]);
            } else {
              toast.error(`Failed to create shipment: ${result.error}`);
            }
          } else {
            toast.error(`Failed to fetch shipment tracking data`);
          }
        }
      } catch (error: any) {
        console.error("Error creating shipment:", error);
        toast.error("An error occurred while creating the shipment");
      } finally {
        setIsSubmitting(false);
      }
    };

    return (
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Freight Selection with Autocomplete */}
          <div className="space-y-2">
            <Label htmlFor="freight_id" className="text-sm font-medium">
              Select Freight (Optional)
            </Label>
            <Combobox
              open={isFreightComboboxOpen}
              onOpenChange={setIsFreightComboboxOpen}
            >
              <ComboboxTrigger>
                {selectedFreight ? (
                  <div className="flex items-center gap-2">
                    {(() => {
                      const Icon = getFreightTypeIcon(
                        selectedFreight.type || "SEA"
                      );
                      return <Icon size={14} className="text-blue-500" />;
                    })()}
                    <span>
                      {selectedFreight.name} - {selectedFreight.scac_codes}
                    </span>
                  </div>
                ) : (
                  "Search and select freight..."
                )}
              </ComboboxTrigger>
              <ComboboxContent>
                <ComboboxCommand>
                  <ComboboxInput
                    placeholder="Search freights by name, SCAC codes, or type..."
                    value={freightSearchTerm}
                    onValueChange={setFreightSearchTerm}
                  />
                  <ComboboxList>
                    <ComboboxEmpty>No freight found.</ComboboxEmpty>
                    <ComboboxGroup>
                      {selectedFreight && (
                        <ComboboxItem
                          onSelect={handleFreightClear}
                          className="text-red-600"
                        >
                          <X className="mr-2 h-4 w-4" />
                          Clear selection
                        </ComboboxItem>
                      )}
                      {filteredFreights.map((freight) => {
                        const Icon = getFreightTypeIcon(freight.type || "SEA");
                        return (
                          <ComboboxItem
                            key={freight.id}
                            onSelect={() => handleFreightSelect(freight)}
                          >
                            <Icon className="mr-2 h-4 w-4 text-blue-500" />
                            <div className="flex flex-col">
                              <span className="font-medium">
                                {freight.name}
                              </span>
                              <span className="text-sm text-gray-500">
                                {freight.scac_codes} • {freight.type} •{" "}
                              </span>
                            </div>
                          </ComboboxItem>
                        );
                      })}
                    </ComboboxGroup>
                  </ComboboxList>
                </ComboboxCommand>
              </ComboboxContent>
            </Combobox>
          </div>

          {/* Batch Selection */}
          <div className="space-y-2">
            <Label htmlFor="batch_id" className="text-sm font-medium">
              Select Batch (Optional)
            </Label>
            <Combobox
              open={isBatchComboboxOpen}
              onOpenChange={setIsBatchComboboxOpen}
            >
              <ComboboxTrigger>
                {formData.batch_id && formData.batch_id !== "none"
                  ? (() => {
                      const selectedBatch = batches.find(
                        (b) => b.id === formData.batch_id
                      );
                      return selectedBatch ? (
                        <div className="flex items-center gap-2">
                          <Package size={14} className="text-green-500" />
                          <span>
                            {selectedBatch.code ||
                              selectedBatch.name ||
                              `Batch ${selectedBatch.id}`}
                          </span>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <Package size={14} className="text-gray-400" />
                          <span>No Batch</span>
                        </div>
                      );
                    })()
                  : loadingBatches
                    ? "Loading batches..."
                    : "Choose a batch for this shipment"}
              </ComboboxTrigger>
              <ComboboxContent>
                <ComboboxCommand>
                  <ComboboxInput placeholder="Search batches..." />
                  <ComboboxList className="max-h-[200px]">
                    <ComboboxEmpty>No batches found.</ComboboxEmpty>
                    <ComboboxGroup>
                      <ComboboxItem
                        value="none"
                        onSelect={() => handleBatchSelect("none")}
                      >
                        <Package size={14} className="text-gray-400 mr-2" />
                        <span>No Batch</span>
                      </ComboboxItem>
                      {batches.length === 0 && !loadingBatches ? (
                        <ComboboxItem value="no-batches" disabled>
                          <Package size={14} className="text-gray-400 mr-2" />
                          <span className="text-gray-500">
                            No batches available
                          </span>
                        </ComboboxItem>
                      ) : (
                        batches.map((batch) => (
                          <ComboboxItem
                            key={batch.id}
                            value={batch.id}
                            onSelect={() => handleBatchSelect(batch.id)}
                          >
                            <Package
                              size={14}
                              className="text-green-500 mr-2"
                            />
                            <span>
                              {batch.code || batch.name || `Batch ${batch.id}`}
                            </span>
                          </ComboboxItem>
                        ))
                      )}
                    </ComboboxGroup>
                  </ComboboxList>
                </ComboboxCommand>
              </ComboboxContent>
            </Combobox>
          </div>
        </div>

        {/* Shipment Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Bill of Lading */}
          <div className="space-y-2">
            <Label htmlFor="bill_of_lading" className="text-sm font-medium">
              Bill of Lading (BIL)
            </Label>
            <Input
              id="bill_of_lading"
              placeholder="e.g: BIL-2024-001"
              value={formData.bill_of_lading}
              onChange={(e) =>
                handleInputChange("bill_of_lading", e.target.value)
              }
            />
            <p className="text-xs text-gray-500">
              Optional freight logging reference number
            </p>
          </div>

          {/* Reference ID */}
          <div className="space-y-2">
            <Label
              htmlFor="freight_reference_id"
              className="text-sm font-medium"
            >
              Freight Reference ID
            </Label>
            <Input
              id="freight_reference_id"
              placeholder="e.g: Container/Crate ID"
              value={formData.freight_reference_id}
              onChange={(e) =>
                handleInputChange("freight_reference_id", e.target.value)
              }
            />
            <p className="text-xs text-gray-500">
              Optional freight reference identifier for tracking
            </p>
          </div>
        </div>

        {/* Document Support */}
        <div className="space-y-4">
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              Attach Documents (Optional)
            </Label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <input
                type="file"
                multiple
                accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.gif"
                onChange={handleFileUpload}
                className="hidden"
                id="file-upload"
              />
              <label
                htmlFor="file-upload"
                className="cursor-pointer flex flex-col items-center gap-2"
              >
                <Upload className="h-8 w-8 text-gray-400" />
                <span className="text-sm text-gray-600">
                  Click to upload or drag and drop
                </span>
                <span className="text-xs text-gray-500">
                  PDF, DOC, XLS, Images (Max 10MB each)
                </span>
              </label>
            </div>
          </div>

          {/* File List */}
          {attachedFiles.length > 0 && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">Attached Files</Label>
              <div className="space-y-2">
                {attachedFiles.map((file, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 bg-gray-50 rounded border"
                  >
                    <div className="flex items-center gap-2 flex-1">
                      {getFileTypeIcon(file.name)}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {file.name}
                        </p>
                        <p className="text-xs text-gray-500">
                          {(file.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(index)}
                      className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-end gap-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting} className="gap-2">
            {isSubmitting ? (
              <>
                <Loader2 size={16} className="animate-spin" />
                Creating...
              </>
            ) : (
              <>
                <Navigation size={16} />
                Create Shipment
              </>
            )}
          </Button>
        </div>

        {/* Document Preview Dialog */}
        <DocumentPreviewDialog
          isOpen={isPreviewOpen}
          onClose={closePreview}
          document={previewDocument}
        />
      </form>
    );
  }
);

NewShipmentForm.displayName = "NewShipmentForm";
