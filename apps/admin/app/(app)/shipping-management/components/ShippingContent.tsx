"use client";

import { memo, useState, useCallback } from "react";
import {
  Plane,
  Ship,
  Truck,
  Package,
  Navigation,
  Trash2,
  CheckSquare,
} from "lucide-react";
import { Button } from "@workspace/ui/components/button";
import { Listing } from "@/modules/listing";
import { type ShipmentDisplay } from "./ShippingManagementContainer";
import {
  type ColumnFilter,
  type TableColumn,
} from "@/components/ui/filter-panel";

interface ShippingContentProps {
  searchTerm: string;
  statusFilter: string;
  typeFilter: string;
  columnFilters: ColumnFilter[];
  loading: boolean;
  filteredShipments: ShipmentDisplay[];
  onSearchChange: (term: string) => void;
  onStatusFilterChange: (filter: string) => void;
  onTypeFilterChange: (filter: string) => void;
  onColumnFiltersChange: (filters: ColumnFilter[]) => void;
  onRefresh: () => void;
  onShipmentAction: (action: string, shipment?: ShipmentDisplay) => void;
  // Checkbox support
  selectedShipments: Set<string>;
  setSelectedShipments: (items: Set<string>) => void;
  onBulkStatusUpdate: (status: string) => void;
  onBulkDelete: () => void;
  onBulkCreateTasks: () => void;
  onClearSelections?: () => void;
}

/**
 * Content component for Shipping Management
 *
 * Uses Listing components for consistent filtering and display.
 * Memoized to prevent unnecessary re-renders.
 */
export const ShippingContent = memo<ShippingContentProps>(
  ({
    searchTerm,
    statusFilter,
    typeFilter,
    columnFilters,
    loading,
    filteredShipments,
    onSearchChange,
    onStatusFilterChange,
    onTypeFilterChange,
    onColumnFiltersChange,
    onRefresh,
    onShipmentAction,
    selectedShipments,
    setSelectedShipments,
    onBulkStatusUpdate,
    onBulkDelete,
    onBulkCreateTasks,
    onClearSelections,
  }) => {
    // View mode state
    const [viewMode, setViewMode] = useState<"cards" | "table">("table");
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 10;

    // Define table columns for dynamic filtering
    const shipmentTableColumns: TableColumn[] = [
      { key: "name", label: "Name", type: "string", searchable: true },
      {
        key: "trackingNumber",
        label: "Tracking Number",
        type: "string",
        searchable: true,
      },
      { key: "freight", label: "Freight", type: "string", searchable: true },
      {
        key: "freightType",
        label: "Freight Type",
        type: "enum",
        searchable: true,
      },
      { key: "batch", label: "Batch", type: "string", searchable: true },
      { key: "origin", label: "Origin", type: "string", searchable: true },
      {
        key: "destination",
        label: "Destination",
        type: "string",
        searchable: true,
      },
      { key: "status", label: "Status", type: "enum", searchable: true },
      {
        key: "currentLocation",
        label: "Current Location",
        type: "string",
        searchable: true,
      },
      {
        key: "departureDateTime",
        label: "Departure",
        type: "date",
        searchable: true,
      },
      {
        key: "arrivalDateTime",
        label: "Arrival",
        type: "date",
        searchable: true,
      },
    ];

    // Category definitions for quick filters
    const statusCategories = [
      {
        key: "active",
        label: "Active",
        count: filteredShipments.filter(
          (s) => s.status.toLowerCase() === "active"
        ).length,
      },
      {
        key: "scheduled",
        label: "Scheduled",
        count: filteredShipments.filter(
          (s) => s.status.toLowerCase() === "scheduled"
        ).length,
      },
      {
        key: "in_transit",
        label: "In Transit",
        count: filteredShipments.filter(
          (s) => s.status.toLowerCase() === "in_transit"
        ).length,
      },
      {
        key: "completed",
        label: "Completed",
        count: filteredShipments.filter(
          (s) => s.status.toLowerCase() === "completed"
        ).length,
      },
      {
        key: "delayed",
        label: "Delayed",
        count: filteredShipments.filter(
          (s) => s.status.toLowerCase() === "delayed"
        ).length,
      },
    ];

    // Pagination calculations
    const totalItems = filteredShipments.length;
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedData = filteredShipments.slice(startIndex, endIndex);

    // Table column definitions for Listing.Table
    const tableColumns = [
      {
        key: "name",
        label: "Shipment",
        render: (shipment: ShipmentDisplay) => (
          <div className="space-y-1">
            <p className="font-medium text-gray-900 text-sm">{shipment.name}</p>
            <p
              className="text-xs text-primary hover:underline font-mono"
              onClick={() => onShipmentAction("view", shipment)}
            >
              {shipment.trackingNumber}
            </p>
            {shipment.billOfLading && (
              <p className="text-xs text-gray-500">
                BIL: {shipment.billOfLading}
              </p>
            )}
          </div>
        ),
      },
      {
        key: "freight",
        label: "Freight/Batch",
        render: (shipment: ShipmentDisplay) => {
          const FreightIcon =
            shipment.freightType.toLowerCase() === "air"
              ? Plane
              : shipment.freightType.toLowerCase() === "sea"
                ? Ship
                : shipment.freightType.toLowerCase() === "land"
                  ? Truck
                  : Package;

          const hasBatch = shipment.batchInfo;
          const hasFreight = shipment.freightInfo;

          return (
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <FreightIcon
                  size={16}
                  className="text-blue-500 flex-shrink-0"
                />
                <div className="text-sm">
                  {hasBatch ? (
                    <div>
                      <div className="font-medium text-gray-900">
                        {shipment.batchInfo?.code}
                      </div>
                      <div className="text-xs text-gray-500">
                        {shipment.batchInfo?.name}
                      </div>
                    </div>
                  ) : hasFreight ? (
                    <div>
                      <div className="font-medium text-gray-900">
                        {shipment.freightInfo?.code || shipment.freight}
                      </div>
                      <div className="text-xs text-gray-500">
                        {shipment.freightInfo?.name}
                      </div>
                    </div>
                  ) : (
                    <div className="font-medium text-gray-900">
                      {shipment.freight}
                    </div>
                  )}
                </div>
              </div>
              {hasBatch && hasFreight && (
                <div className="text-xs text-gray-400 pl-5">
                  via {shipment.freightInfo?.name}
                </div>
              )}
            </div>
          );
        },
      },
      {
        key: "route",
        label: "Route",
        render: (shipment: ShipmentDisplay) => {
          const hasTracking =
            shipment.hasRealTimeTracking &&
            shipment.routeLocations &&
            shipment.routeLocations.length >= 2;

          if (hasTracking) {
            const origin = shipment.routeLocations![0];
            const destination = shipment.routeLocations![1];

            return (
              <div className="space-y-1">
                <div className="flex items-center gap-1">
                  <p className="text-sm font-medium text-gray-900">
                    {origin?.name || "Unknown Origin"}
                  </p>
                  <span className="text-xs bg-blue-100 text-blue-800 px-1 rounded">
                    {origin?.countryCode || "??"}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-xs text-gray-500">
                  <span>→</span>
                  <span>{destination?.name || "Unknown Destination"}</span>
                  <span className="text-xs bg-green-100 text-green-800 px-1 rounded">
                    {destination?.countryCode || "??"}
                  </span>
                </div>
                <div className="text-xs text-blue-600 font-medium">
                  📡 Live Tracking
                </div>
              </div>
            );
          }

          // Fallback to basic route display
          return (
            <div className="space-y-1">
              <p className="text-sm font-medium text-gray-900">
                {shipment.origin}
              </p>
              <div className="flex items-center gap-2 text-xs text-gray-500">
                <span>→</span>
                <span>{shipment.destination}</span>
              </div>
            </div>
          );
        },
      },
      {
        key: "status",
        label: "Status",
        render: (shipment: ShipmentDisplay) => {
          const hasTracking =
            shipment.hasRealTimeTracking && shipment.trackingMetadata;
          const trackingStatus = hasTracking
            ? shipment.trackingMetadata?.metadata.shippingStatus
            : null;

          return (
            <div className="space-y-2">
              <div className="flex flex-col gap-1">
                <span
                  className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    shipment.status.toLowerCase() === "active"
                      ? "bg-blue-100 text-blue-800"
                      : shipment.status.toLowerCase() === "in_transit"
                        ? "bg-yellow-100 text-yellow-800"
                        : shipment.status.toLowerCase() === "delivered"
                          ? "bg-green-100 text-green-800"
                          : shipment.status.toLowerCase() === "delayed"
                            ? "bg-red-100 text-red-800"
                            : "bg-gray-100 text-gray-800"
                  }`}
                >
                  {shipment.status}
                </span>

                {hasTracking && trackingStatus && (
                  <div className="text-xs text-gray-600">
                    <span className="font-medium">API:</span> {trackingStatus}
                  </div>
                )}

                {hasTracking &&
                  shipment.trackingMetadata?.metadata.updatedAt && (
                    <div className="text-xs text-gray-500">
                      Updated:{" "}
                      {new Date(
                        shipment.trackingMetadata.metadata.updatedAt
                      ).toLocaleDateString()}
                    </div>
                  )}
              </div>

              {shipment.delay && shipment.delay > 0 && (
                <p className="text-red-600 text-xs font-medium bg-red-50 px-2 py-1 rounded">
                  ⚠️ +{shipment.delay}min delay
                </p>
              )}
            </div>
          );
        },
      },
    ];

    // Render bulk actions
    const renderBulkActions = useCallback(
      () => (
        <>
          <Button
            variant="outline"
            size="sm"
            onClick={onBulkCreateTasks}
            className="gap-2"
            disabled={selectedShipments.size === 0}
          >
            <CheckSquare size={16} />
            Create Tasks
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={onBulkDelete}
            className="gap-2"
          >
            <Trash2 size={16} />
            Delete
          </Button>
        </>
      ),
      [onBulkDelete, onBulkCreateTasks, selectedShipments.size]
    );

    // Empty state for table (React element)
    const tableEmptyState = (
      <div className="text-center py-12">
        <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
          <Navigation className="h-6 w-6 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-1">
          No shipments found
        </h3>
        <p className="text-gray-500 mb-4">
          Get started by creating your first shipment
        </p>
      </div>
    );

    return (
      <div className="space-y-6">
        <Listing.Filters
          searchTerm={searchTerm}
          onSearchChange={onSearchChange}
          onRefresh={onRefresh}
          loading={loading}
          columnFilters={columnFilters}
          onColumnFilterAdd={(filter) =>
            onColumnFiltersChange([...columnFilters, filter])
          }
          onColumnFilterRemove={(index) =>
            onColumnFiltersChange(columnFilters.filter((_, i) => i !== index))
          }
          columns={shipmentTableColumns}
          tableData={filteredShipments}
          enableDynamicFilters={true}
          defaultFilterColumn="name"
          autoSelectDefaultColumn={true}
          bulkActions={renderBulkActions()}
          selectedCount={selectedShipments.size}
          showBulkActions={true}
          onClearSelections={onClearSelections}
        />

        <Listing.Controls
          entity="shipments"
          length={filteredShipments.length}
          viewMode={viewMode}
          onViewModeChange={setViewMode}
          categoryFilter={statusFilter}
          categories={statusCategories}
          onCategoryFilterChange={onStatusFilterChange}
        />

        {viewMode === "table" ? (
          <Listing.Table
            data={paginatedData}
            columns={tableColumns}
            loading={loading}
            enableCheckboxes={true}
            selectedRowIds={Array.from(selectedShipments)}
            onSelectionChange={(selectedIds) =>
              setSelectedShipments(new Set(selectedIds))
            }
            getRowId={(item) => item.id}
            emptyState={tableEmptyState}
            onRowClick={(shipment) => onShipmentAction("view", shipment)}
            pagination={{
              currentPage,
              totalPages,
              totalItems,
              itemsPerPage,
              onPageChange: setCurrentPage,
            }}
          />
        ) : (
          <Listing.Cards
            data={paginatedData}
            loading={loading}
            emptyState={tableEmptyState}
            onItemClick={(shipment) => onShipmentAction("view", shipment)}
            renderCard={(shipment: ShipmentDisplay) => {
              const FreightIcon =
                shipment.freightType.toLowerCase() === "air"
                  ? Plane
                  : shipment.freightType.toLowerCase() === "sea"
                    ? Ship
                    : shipment.freightType.toLowerCase() === "land"
                      ? Truck
                      : Package;

              return (
                <div className="p-4 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-lg transition-all duration-200">
                  <div className="space-y-3">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="font-medium text-gray-900">
                          {shipment.name}
                        </h3>
                        <p className="text-sm text-gray-500">
                          {shipment.trackingNumber}
                        </p>
                      </div>
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          shipment.status.toLowerCase() === "active"
                            ? "bg-blue-100 text-blue-800"
                            : shipment.status.toLowerCase() === "completed"
                              ? "bg-green-100 text-green-800"
                              : shipment.status.toLowerCase() === "delayed"
                                ? "bg-red-100 text-red-800"
                                : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {shipment.status}
                      </span>
                    </div>

                    {/* Batch/Freight Information */}
                    <div className="flex items-center gap-2 text-sm">
                      <FreightIcon size={16} className="text-blue-500" />
                      <div>
                        {shipment.batchInfo ? (
                          <div>
                            <span className="font-medium text-gray-900">
                              {shipment.batchInfo.code}
                            </span>
                            <span className="text-gray-500 ml-1">
                              ({shipment.batchInfo.name})
                            </span>
                            {shipment.freightInfo && (
                              <div className="text-xs text-gray-400">
                                via {shipment.freightInfo.name}
                              </div>
                            )}
                          </div>
                        ) : shipment.freightInfo ? (
                          <div>
                            <span className="font-medium text-gray-900">
                              {shipment.freightInfo.code}
                            </span>
                            <span className="text-gray-500 ml-1">
                              ({shipment.freightInfo.name})
                            </span>
                          </div>
                        ) : (
                          <span className="text-gray-600">
                            {shipment.freight}
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="text-sm text-gray-600">
                      {/* Route Information */}
                      {shipment.hasRealTimeTracking &&
                      shipment.routeLocations &&
                      shipment.routeLocations.length >= 2 ? (
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <span>
                              {shipment.routeLocations[0]?.name ||
                                shipment.origin}
                            </span>
                            <span className="text-xs bg-blue-100 text-blue-800 px-1 rounded">
                              {shipment.routeLocations[0]?.countryCode}
                            </span>
                            <span>→</span>
                            <span>
                              {shipment.routeLocations[1]?.name ||
                                shipment.destination}
                            </span>
                            <span className="text-xs bg-green-100 text-green-800 px-1 rounded">
                              {shipment.routeLocations[1]?.countryCode}
                            </span>
                          </div>
                          <div className="text-xs text-blue-600 font-medium">
                            📡 Live Tracking Active
                          </div>
                        </div>
                      ) : (
                        <p>
                          {shipment.origin} → {shipment.destination}
                        </p>
                      )}
                      <p>Progress: {shipment.progress}%</p>

                      {/* Current Location */}
                      <p className="text-xs text-gray-500 mt-1">
                        📍 {shipment.currentLocation}
                      </p>
                    </div>
                  </div>
                </div>
              );
            }}
          />
        )}
      </div>
    );
  }
);

ShippingContent.displayName = "ShippingContent";
