"use client";

import { memo } from "react";
import { RefreshCw, Plus } from "lucide-react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Overview } from "@/modules/layouts/overview";

interface ShippingManagementHeaderProps {
  loading: boolean;
  shouldShowCreateButton: (entity: string) => boolean;
  onRefresh: () => void;
  onCreateShipment: () => void;
}

/**
 * Header component for Shipping Management page
 * 
 * Displays the page title, description, and action buttons.
 * Memoized to prevent unnecessary re-renders.
 */
export const ShippingManagementHeader = memo<ShippingManagementHeaderProps>(({
  loading,
  shouldShowCreateButton,
  onRefresh,
  onCreateShipment,
}) => {
  return (
    <Overview.Header
      title="Shipment Management"
      caption="Track and manage shipments with integrated scheduling and progress monitoring"
      actions={
        <>
          <Button
            variant="outline"
            onClick={onRefresh}
            disabled={loading}
            aria-label="Refresh shipments"
          >
            <RefreshCw
              className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
          {shouldShowCreateButton("shipments") && (
            <Button
              onClick={onCreateShipment}
              aria-label="Create new shipment"
            >
              <Plus className="h-4 w-4 mr-2" />
              New Shipment
            </Button>
          )}
        </>
      }
    />
  );
});

ShippingManagementHeader.displayName = "ShippingManagementHeader";
