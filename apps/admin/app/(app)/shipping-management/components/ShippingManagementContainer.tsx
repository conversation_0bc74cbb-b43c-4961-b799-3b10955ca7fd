"use client";

import { useRB<PERSON> } from "@/lib/hooks/useRBAC";
import { useShippingManagement } from "../hooks/useShippingManagement";
import { type ColumnFilter } from "@/components/ui/filter-panel";

// Types for tracking metadata
export interface TrackingLocation {
  name: string;
  state: string;
  country: string;
  countryCode: string;
  locode: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  timezone: string;
}

export interface TrackingMetadata {
  metadata: {
    shipmentType: string;
    shipmentNumber: string;
    sealine: string;
    sealineName: string;
    shippingStatus: string;
    updatedAt: string;
    warnings: string[];
  };
  locations: TrackingLocation[];
}

// Types for unified shipment data
export interface ShipmentDisplay {
  id: string;
  name: string;
  trackingNumber: string;
  billOfLading?: string;
  freight: string;
  freightType: string;
  batch: string;
  origin: string;
  destination: string;
  departureDateTime: string;
  arrivalDateTime: string;
  estimatedTimeElapsed: string; // calculated from departure to current time
  estimatedTimeOfArrival: string; // calculated ETA
  status: string;
  currentLocation: string;
  progress: number; // percentage
  delay?: number; // in minutes
  notes?: string;
  // Enhanced batch/freight information
  batchInfo?: {
    id: string;
    name: string;
    code: string;
    type: string;
    freightType: string;
  } | null;
  freightInfo?: {
    id: string;
    name: string;
    code: string;
    type: string;
    origin: string;
    destination: string;
  } | null;
  // Tracking metadata from Sinay API
  trackingMetadata?: TrackingMetadata | null;
  // Route information derived from metadata
  routeLocations?: TrackingLocation[];
  hasRealTimeTracking?: boolean;
}

// Components
import { Overview } from "@/modules/layouts/overview";
import { ShippingManagementHeader } from "./ShippingManagementHeader";
import { ShippingStatistics } from "./ShippingStatistics";
import { ShippingContent } from "./ShippingContent";
import { ShippingDialogs } from "./ShippingDialogs";
import { BulkTaskCreateDialog } from "@/components/ui/bulk-task-create";

// Types
export interface ShippingStats {
  totalShipments: number;
  activeShipments: number;
  onTimeDeliveries: number;
  delayedShipments: number;
  upcomingDepartures: number;
  completedToday: number;
}

export interface ShippingManagementState {
  // View state
  searchTerm: string;
  statusFilter: string;
  typeFilter: string;
  loading: boolean;
  refreshing: boolean;

  // Data state
  shipments: ShipmentDisplay[];
  shippingStats: ShippingStats;

  // Dialog state
  isNewShipmentOpen: boolean;
  selectedShipment: ShipmentDisplay | null;

  // Filter state
  columnFilters: ColumnFilter[];

  // Checkbox state
  selectedShipments: Set<string>;

  // Bulk task creation state
  isBulkTaskDialogOpen: boolean;
}

/**
 * Main container component for Shipping Management
 *
 * This component manages all the state and business logic for the shipping management page.
 * It follows the container/presenter pattern for better separation of concerns.
 */
export function ShippingManagementContainer() {
  const { shouldShowCreateButton } = useRBAC();

  // Wrapper function to handle the type mismatch
  const handleShouldShowCreateButton = (entity: string) => {
    return shouldShowCreateButton(entity as any);
  };

  // Use custom hook for all shipping management logic
  const {
    state,
    freights,
    filteredShipments,
    handleRefresh,
    handleShipmentAction,
    handleDialogClose,
    handleShipmentMutation,
    handleBulkStatusUpdate,
    handleBulkDelete,
    handleClearSelections,
    updateState,
    handleBulkCreateTasks,
    isBulkTaskDialogOpen,
    setIsBulkTaskDialogOpen,
    selectedItemsForTasks,
    handleTasksCreated,
  } = useShippingManagement();

  return (
    <Overview className="p-6 space-y-8">
      <ShippingManagementHeader
        loading={state.loading}
        shouldShowCreateButton={handleShouldShowCreateButton}
        onRefresh={handleRefresh}
        onCreateShipment={() => handleShipmentAction("create")}
      />

      <ShippingStatistics
        shippingStats={state.shippingStats}
        shipments={state.shipments}
        loading={state.loading}
      />

      <Overview.Content>
        <ShippingContent
          searchTerm={state.searchTerm}
          statusFilter={state.statusFilter}
          typeFilter={state.typeFilter}
          columnFilters={state.columnFilters}
          loading={state.loading}
          filteredShipments={filteredShipments}
          onSearchChange={(term: string) => updateState({ searchTerm: term })}
          onStatusFilterChange={(filter: string) =>
            updateState({ statusFilter: filter })
          }
          onTypeFilterChange={(filter: string) =>
            updateState({ typeFilter: filter })
          }
          onColumnFiltersChange={(filters: ColumnFilter[]) =>
            updateState({ columnFilters: filters })
          }
          onRefresh={handleRefresh}
          onShipmentAction={handleShipmentAction}
          selectedShipments={state.selectedShipments}
          setSelectedShipments={(items: Set<string>) =>
            updateState({ selectedShipments: items })
          }
          onBulkStatusUpdate={handleBulkStatusUpdate}
          onBulkDelete={handleBulkDelete}
          onBulkCreateTasks={handleBulkCreateTasks}
          onClearSelections={handleClearSelections}
        />
      </Overview.Content>

      {/* Bulk Task Create Dialog */}
      <BulkTaskCreateDialog
        isOpen={isBulkTaskDialogOpen}
        onClose={() => setIsBulkTaskDialogOpen(false)}
        onTasksCreated={handleTasksCreated}
        selectedItems={selectedItemsForTasks}
        associatedTable="shipments"
        title="Create Tasks for Selected Shipments"
      />

      <ShippingDialogs
        isNewShipmentOpen={state.isNewShipmentOpen}
        selectedShipment={state.selectedShipment}
        freights={freights}
        onDialogClose={handleDialogClose}
        onShipmentMutation={handleShipmentMutation}
      />
    </Overview>
  );
}
