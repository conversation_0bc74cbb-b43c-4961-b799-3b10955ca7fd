"use client";

import { memo } from "react";
import { motion } from "framer-motion";
import {
  Navigation,
  CheckCircle,
  AlertTriangle,
  Clock,
  Timer,
  TrendingUp,
  Package,
} from "lucide-react";
import { Listing } from "@/modules/listing";
import {
  type ShippingStats,
  type ShipmentDisplay,
} from "./ShippingManagementContainer";

interface ShippingStatisticsProps {
  shippingStats: ShippingStats;
  shipments: ShipmentDisplay[];
  loading: boolean;
}

/**
 * Statistics component for Shipping Management
 *
 * Displays key shipping metrics in a grid of stat cards.
 * Memoized to prevent unnecessary re-renders.
 */
export const ShippingStatistics = memo<ShippingStatisticsProps>(
  ({ shippingStats, shipments, loading }) => {
    // Calculate additional metrics
    const activeShipments = shipments.filter(
      (s) =>
        s.status.toLowerCase() === "active" ||
        s.status.toLowerCase() === "in_transit"
    ).length;

    const statCards = [
      {
        icon: Navigation,
        name: "Total Shipments",
        value: shippingStats.totalShipments,
        caption: (
          <span className="text-xs text-blue-600 flex items-center gap-1">
            <Package className="h-3 w-3" /> All shipments
          </span>
        ),
        color: "primary" as const,
      },
      {
        icon: Clock,
        name: "Active Shipments",
        value: activeShipments,
        caption: (
          <span className="text-xs text-blue-600 flex items-center gap-1">
            <Timer className="h-3 w-3" /> In transit
          </span>
        ),
        color: "blue" as const,
      },
      {
        icon: CheckCircle,
        name: "On-Time Deliveries",
        value: shippingStats.onTimeDeliveries,
        caption: (
          <span className="text-xs text-green-600 flex items-center gap-1">
            <TrendingUp className="h-3 w-3" /> Delivered on time
          </span>
        ),
        color: "green" as const,
      },
      {
        icon: AlertTriangle,
        name: "Delayed Shipments",
        value: shippingStats.delayedShipments,
        caption: (
          <span className="text-xs text-red-600 flex items-center gap-1">
            <AlertTriangle className="h-3 w-3" /> Behind schedule
          </span>
        ),
        color: "red" as const,
      },
      {
        icon: Timer,
        name: "Completed Today",
        value: shippingStats.completedToday,
        caption: (
          <span className="text-xs text-purple-600 flex items-center gap-1">
            <CheckCircle className="h-3 w-3" /> Today's deliveries
          </span>
        ),
        color: "purple" as const,
      },
    ];

    return (
      <Listing.Statistics columns="grid-cols-5">
        {statCards.map((stat, index) => (
          <motion.div
            key={stat.name}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{
              duration: 0.3,
              delay: index * 0.1,
              ease: "easeOut",
            }}
          >
            <Listing.StatCard
              icon={stat.icon}
              name={stat.name}
              value={loading ? "..." : stat.value.toString()}
              valueType="number"
              caption={stat.caption}
              color={stat.color}
              loading={loading}
            />
          </motion.div>
        ))}
      </Listing.Statistics>
    );
  }
);

ShippingStatistics.displayName = "ShippingStatistics";
