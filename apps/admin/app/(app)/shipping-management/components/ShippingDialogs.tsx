"use client";

import { memo } from "react";
import { Navigation } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { NewShipmentForm } from "./NewShipmentForm";
import { type ShipmentDisplay } from "./ShippingManagementContainer";
import { type FreightWithRelations } from "@/lib/logistics";

interface ShippingDialogsProps {
  isNewShipmentOpen: boolean;
  selectedShipment: ShipmentDisplay | null;
  freights: FreightWithRelations[];
  onDialogClose: (dialogType: string) => void;
  onShipmentMutation: () => void;
}

/**
 * Dialogs component for Shipping Management
 *
 * Contains all dialog components for the shipping management page.
 * Memoized to prevent unnecessary re-renders.
 */
export const ShippingDialogs = memo<ShippingDialogsProps>(
  ({
    isNewShipmentOpen,
    selectedShipment,
    freights,
    onDialogClose,
    onShipmentMutation,
  }) => {
    return (
      <>
        {/* New Shipment Dialog */}
        <Dialog
          open={isNewShipmentOpen}
          onOpenChange={(open) => !open && onDialogClose("NewShipment")}
        >
          <DialogContent className="sm:max-w-3xl p-0">
            <DialogHeader className="p-6 pb-4">
              <DialogTitle className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                <Navigation size={20} className="text-primary" />
                Create New Shipment
              </DialogTitle>
            </DialogHeader>
            <div className="px-6 pb-6 max-h-[70vh] overflow-y-auto">
              <NewShipmentForm
                onOpenChange={(open: boolean) => {
                  if (!open) {
                    onDialogClose("NewShipment");
                  }
                }}
                onSuccess={onShipmentMutation}
              />
            </div>
          </DialogContent>
        </Dialog>

        {/* Additional dialogs can be added here as needed */}
        {/* Edit Shipment Dialog */}
        {/* View Shipment Dialog */}
        {/* Delete Shipment Dialog */}
      </>
    );
  }
);

ShippingDialogs.displayName = "ShippingDialogs";
