"use client";

import { memo } from "react";
import { withRBAC } from "@/lib/components/RBACWrapper";
import { ShippingManagementContainer } from "./components/ShippingManagementContainer";

/**
 * Shipping Management Page Component
 *
 * This is the main page component for shipping management.
 * It's wrapped with RBAC for permission control and uses a container component
 * for better separation of concerns and performance optimization.
 */
const ShippingManagementPage = memo(() => {
  return <ShippingManagementContainer />;
});

ShippingManagementPage.displayName = "ShippingManagementPage";

export default withRBAC(ShippingManagementPage, "shipments", "view");
