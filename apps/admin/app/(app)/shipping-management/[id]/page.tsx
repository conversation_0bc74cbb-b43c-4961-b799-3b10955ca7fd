"use client";

import React, { useState, useEffect, useMemo } from "react";
import Link from "next/link";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import {
  ChevronRight,
  AlertCircle,
  Plane,
  Hash,
  Loader2,
  RefreshCw,
  Package,
  ArrowLeft,
  Ship,
  Container,
  MapPin,
  Navigation,
  User,
  Users,
  Map,
  Gauge,
  Globe,
  FileText,
  Trash2,
  Download,
  Eye,
  Upload,
} from "lucide-react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Badge } from "@workspace/ui/components/badge";
import { Label } from "@workspace/ui/components/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@workspace/ui/components/tabs";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Input } from "@workspace/ui/components/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@workspace/ui/components/table";
import { cn } from "@workspace/ui/lib/utils";
import { PageTransition } from "@/components/page-transition";
import { AnimatedCard } from "@/components/animated-card";
import {
  shipmentService,
  batchService,
  freightService,
  documentService,
  type ShipmentWithRelations,
  type FreightWithRelations,
  type DocumentWithAccount,
} from "@/lib/logistics";
import { Constants } from "@/lib/types/supabase";
import {
  formatWeight,
  formatVolume,
  formatCurrency,
} from "@/lib/utils/unit-mappings";
import {
  FilterPanel,
  type ColumnFilter,
  type TableColumn,
} from "@/components/ui/filter-panel";
import {
  DocumentPreviewDialog,
  useDocumentPreview,
} from "@/components/ui/document-preview-dialog";
import { motion, AnimatePresence } from "framer-motion";
import GoogleMapReact from "google-map-react";
import {
  SHIPPING_LOCATIONS,
  formatLocationDisplay,
} from "@/lib/data/shipping-locations";
import { toast } from "sonner";
import { useAppSelector } from "@/store/hooks";

// Types for shipment data
interface ShipmentDetails {
  id: string;
  name: string;
  trackingNumber: string;
  billOfLading?: string;
  freight: {
    id: string;
    name: string;
    code: string;
    type: string;
    origin: string;
    destination: string;
  };
  batches: BatchInfo[];
  selectedBatchId?: string;
  cargo: CargoItem[];
  schedule: {
    departureDateTime: string;
    arrivalDateTime: string;
    estimatedTimeElapsed: string;
    estimatedTimeOfArrival: string;
  };
  status: string;
  progress: number;
  currentLocation: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  createdBy: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
  customers: Customer[];
  tracking: TrackingEvent[];
  notes?: string;
  created_at: string;
  updated_at: string;
  // Tracking metadata from Sinay API
  trackingMetadata?: {
    metadata: {
      shipmentType: string;
      shipmentNumber: string;
      sealine: string;
      sealineName: string;
      shippingStatus: string;
      updatedAt: string;
      warnings: string[];
    };
    locations: Array<{
      name: string;
      state: string;
      country: string;
      countryCode: string;
      locode: string;
      coordinates: {
        lat: number;
        lng: number;
      };
      timezone: string;
    }>;
  } | null;
  hasRealTimeTracking?: boolean;
  routeLocations?: Array<{
    name: string;
    state: string;
    country: string;
    countryCode: string;
    locode: string;
    coordinates: {
      lat: number;
      lng: number;
    };
    timezone: string;
  }>;
}

interface BatchInfo {
  id: string;
  name: string;
  bill_of_lading: string;
  code: string;
  type: string;
  weight: number;
  weightUnit: string;
  cargo: CargoItem[];
  customers: Customer[];
  value: number;
  currency: string;
  status: string;
}

interface CargoItem {
  id: string;
  name: string;
  description: string;
  quantity: number;
  weight: number;
  weightUnit: string;
  dimensions: {
    length: number;
    width: number;
    height: number;
    unit: string;
  };
  cbm: {
    value: number;
    unit: string;
  };
  value: number;
  currency: string;
  batchId: string;
}

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  company: string;
  type: "SENDER" | "RECEIVER";
}

interface TrackingEvent {
  id: string;
  timestamp: string;
  location: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  event: string;
  description: string;
  status: string;
}

// Sinay API Response Types
interface SinayLocation {
  name: string;
  state: string;
  country: string;
  countryCode: string;
  locode: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  timezone: string;
}

interface SinayRoutePoint {
  location: SinayLocation;
  date: string;
  actual: boolean;
  predictiveEta: string | null;
}

interface SinayVessel {
  name: string;
  imo: number;
  callSign: string;
  mmsi: number;
  flag: string;
}

interface SinayContainerEvent {
  location: SinayLocation;
  facility: any;
  description: string;
  eventType: string;
  eventCode: string;
  status: string;
  date: string;
  isActual: boolean;
  isAdditionalEvent: boolean;
  routeType: string;
  transportType: string | null;
  vessel: SinayVessel | null;
  voyage: string | null;
}

interface SinayContainer {
  number: string;
  isoCode: string;
  sizeType: string;
  status: string;
  events: SinayContainerEvent[];
}

interface SinayRouteSegment {
  path: Array<{ lat: number; lng: number }>;
  routeType: string;
}

interface SinayApiResponse {
  metadata: {
    shipmentType: string;
    shipmentNumber: string;
    sealine: string;
    sealineName: string;
    shippingStatus: string;
    updatedAt: string;
    warnings: string[];
  };
  locations: SinayLocation[];
  route: {
    prepol: SinayRoutePoint;
    pol: SinayRoutePoint;
    pod: SinayRoutePoint;
    postpod: SinayRoutePoint;
  };
  vessels: SinayVessel[];
  facilities: any[];
  containers: SinayContainer[];
  routeData: {
    routeSegments: SinayRouteSegment[];
    coordinates: {
      lat: number;
      lng: number;
    };
    ais: {
      status: string;
      data: any;
    };
  };
}

// Enhanced tracking data combining local and external sources
interface EnhancedTrackingData {
  currentLocation: {
    name: string;
    coordinates: { lat: number; lng: number };
    country: string;
    timezone: string;
  };
  route: {
    origin: SinayLocation;
    destination: SinayLocation;
    currentProgress: number; // percentage
  };
  vessel: SinayVessel | null;
  timeline: SinayContainerEvent[];
  routePath: Array<{ lat: number; lng: number }>;
  status: string;
  lastUpdated: string;
}

// Helper function to get freight type icon
const getFreightTypeIcon = (type: string) => {
  switch (type?.toLowerCase()) {
    case "air":
      return Plane;
    case "sea":
      return Ship;
    default:
      return Container;
  }
};

// Vessel Marker Component for Google Maps
interface VesselMarkerProps {
  type: string;
  lat: number;
  lng: number;
  currentLocation: string;
}

const VesselMarker: React.FC<VesselMarkerProps> = ({
  type,
  currentLocation,
}) => {
  const [showTooltip, setShowTooltip] = useState(false);

  return (
    <div className="relative">
      {/* Ship Icon */}
      <motion.div
        initial={{ scale: 0, rotate: -180 }}
        animate={{
          scale: 1,
          rotate: 0,
          x: [0, 3, -2, 0],
          y: [0, -2, 2, 0],
        }}
        transition={{
          scale: { duration: 0.8, ease: "easeOut" },
          rotate: { duration: 1, ease: "easeOut" },
          x: { duration: 4, repeat: Infinity, ease: "easeInOut" },
          y: { duration: 3, repeat: Infinity, ease: "easeInOut" },
        }}
        className="relative cursor-pointer"
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
      >
        {/* Main Ship Icon */}
        <motion.div
          animate={{ rotate: [0, 2, -2, 0] }}
          transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          className="relative bg-white rounded-full p-5 shadow-xl border-2 border-cyan-500 hover:border-cyan-600 transition-colors"
        >
          {type === "AIR" ? (
            <Plane
              size={28}
              className="text-cyan-600 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
            />
          ) : (
            <Ship
              size={28}
              className="text-cyan-600 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
            />
          )}
        </motion.div>

        {/* Pulsing Ring */}
        <motion.div
          animate={{ scale: [1, 2, 1], opacity: [0.8, 0, 0.8] }}
          transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          className="absolute inset-0 bg-blue-500 rounded-full"
        />

        {/* Secondary Pulse Ring */}
        <motion.div
          animate={{ scale: [1, 1.5, 1], opacity: [0.5, 0, 0.5] }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 0.5,
          }}
          className="absolute inset-0 bg-green-400 rounded-full"
        />

        {/* Location Label */}
        <AnimatePresence>
          {(showTooltip || currentLocation.length < 20) && (
            <motion.div
              initial={{ opacity: 0, y: -4, scale: 0.8 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -5, scale: 0.8 }}
              transition={{ duration: 0.2 }}
              className="absolute -top-10 z-20"
            >
              <div className="bg-gradient-to-r from-red-500 to-red-600 text-white px-2 py-1 rounded-lg text-xs font-medium whitespace-nowrap shadow-xl border border-red-400 max-w-48 truncate">
                <div className="flex items-center gap-1">
                  <MapPin size={12} />
                  {currentLocation}
                </div>
              </div>
              <div className="w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-red-500 mx-auto" />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Speed Indicator */}
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
          className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white shadow-lg"
        >
          <div className="w-1 h-1 bg-white rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
        </motion.div>
      </motion.div>

      {/* Wake Effect */}
      <motion.div
        animate={{
          scaleX: [0.8, 1.2, 0.8],
          opacity: [0.3, 0.6, 0.3],
        }}
        transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
        className="absolute top-8 left-1/2 transform -translate-x-1/2 w-8 h-2 bg-blue-200 rounded-full blur-sm"
      />
    </div>
  );
};

// Waypoint Marker Component
interface WaypointMarkerProps {
  lat: number;
  lng: number;
  index: number;
  isFirst?: boolean;
  isLast?: boolean;
}

const WaypointMarker: React.FC<WaypointMarkerProps> = ({
  index,
  isFirst = false,
  isLast = false,
}) => {
  return (
    <div className="relative">
      <motion.div
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ delay: index * 0.1, duration: 0.3 }}
        className={`w-3 h-3 rounded-full border-2 border-white shadow-lg ${
          isFirst ? "bg-green-500" : isLast ? "bg-red-500" : "bg-blue-500"
        }`}
      />
      {(isFirst || isLast) && (
        <motion.div
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: index * 0.1 + 0.2, duration: 0.3 }}
          className="absolute -top-8 left-1/2 transform -translate-x-1/2"
        >
          <div
            className={`px-2 py-1 rounded text-xs font-medium text-white shadow-lg ${
              isFirst ? "bg-green-600" : "bg-red-600"
            }`}
          >
            {isFirst ? "Start" : "End"}
          </div>
          <div
            className={`w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent mx-auto ${
              isFirst ? "border-t-green-600" : "border-t-red-600"
            }`}
          />
        </motion.div>
      )}
    </div>
  );
};

// Google Maps Component
interface GoogleMapComponentProps {
  vesselType: string;
  shipLocation: {
    lat: number;
    lng: number;
  };
  currentLocation: string;
  routePath?: Array<{ lat: number; lng: number }>;
}

const GoogleMapComponent: React.FC<GoogleMapComponentProps> = ({
  vesselType,
  shipLocation,
  currentLocation,
  routePath = [],
}) => {
  // Sample route path for waypoint markers (show max 10 waypoints for performance)
  const sampleRouteForWaypoints = (
    path: Array<{ lat: number; lng: number }>
  ) => {
    if (path.length <= 10) return path;

    const sampledPath = [path[0]]; // Always include first point
    const step = Math.floor((path.length - 2) / 8); // Sample 8 points in between

    for (let i = step; i < path.length - 1; i += step) {
      sampledPath.push(path[i]);
    }

    sampledPath.push(path[path.length - 1]); // Always include last point
    return sampledPath;
  };

  const waypointPath = sampleRouteForWaypoints(routePath);

  const defaultProps = {
    center: {
      lat: shipLocation.lat || 25.2048, // Default to Dubai if no coordinates
      lng: shipLocation.lng || 55.2708,
    },
    zoom: 3,
  };

  const mapOptions = {
    styles: [
      {
        featureType: "water",
        elementType: "geometry",
        stylers: [{ color: "#e9e9e9" }, { lightness: 17 }],
      },
      {
        featureType: "landscape",
        elementType: "geometry",
        stylers: [{ color: "#f5f5f5" }, { lightness: 20 }],
      },
      {
        featureType: "road.highway",
        elementType: "geometry.fill",
        stylers: [{ color: "#ffffff" }, { lightness: 17 }],
      },
      {
        featureType: "road.highway",
        elementType: "geometry.stroke",
        stylers: [{ color: "#ffffff" }, { lightness: 29 }, { weight: 0.2 }],
      },
      {
        featureType: "road.arterial",
        elementType: "geometry",
        stylers: [{ color: "#ffffff" }, { lightness: 18 }],
      },
      {
        featureType: "road.local",
        elementType: "geometry",
        stylers: [{ color: "#ffffff" }, { lightness: 16 }],
      },
      {
        featureType: "poi",
        elementType: "geometry",
        stylers: [{ color: "#f5f5f5" }, { lightness: 21 }],
      },
      {
        featureType: "poi.park",
        elementType: "geometry",
        stylers: [{ color: "#dedede" }, { lightness: 21 }],
      },
      {
        elementType: "labels.text.stroke",
        stylers: [
          { visibility: "on" },
          { color: "#ffffff" },
          { lightness: 16 },
        ],
      },
      {
        elementType: "labels.text.fill",
        stylers: [{ saturation: 36 }, { color: "#333333" }, { lightness: 40 }],
      },
      {
        elementType: "labels.icon",
        stylers: [{ visibility: "off" }],
      },
      {
        featureType: "transit",
        elementType: "geometry",
        stylers: [{ color: "#f2f2f2" }, { lightness: 19 }],
      },
      {
        featureType: "administrative",
        elementType: "geometry.fill",
        stylers: [{ color: "#fefefe" }, { lightness: 20 }],
      },
      {
        featureType: "administrative",
        elementType: "geometry.stroke",
        stylers: [{ color: "#fefefe" }, { lightness: 17 }, { weight: 1.2 }],
      },
    ],
    disableDefaultUI: false,
    zoomControl: true,
    mapTypeControl: false,
    scaleControl: true,
    streetViewControl: false,
    rotateControl: false,
    fullscreenControl: true,
  };

  return (
    <div className="w-full h-full relative">
      <GoogleMapReact
        bootstrapURLKeys={{
          key: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || "",
        }}
        defaultCenter={defaultProps.center}
        defaultZoom={defaultProps.zoom}
        options={mapOptions}
      >
        {/* Vessel Marker */}
        <VesselMarker
          type={vesselType}
          lat={shipLocation.lat || 25.2048}
          lng={shipLocation.lng || 55.2708}
          currentLocation={currentLocation}
        />

        {/* Route Waypoint Markers */}
        {waypointPath &&
          waypointPath.length > 0 &&
          waypointPath
            .filter((point) => point && point.lat && point.lng)
            .map((point, index) => (
              <WaypointMarker
                key={`waypoint-${index}`}
                lat={point!.lat}
                lng={point!.lng}
                index={index}
                isFirst={index === 0}
                isLast={index === waypointPath.length - 1}
              />
            ))}
      </GoogleMapReact>

      {/* Coordinates Display */}
      <div className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg">
        <div className="text-xs text-gray-600 mb-1">Current Position</div>
        <div className="font-mono text-sm font-medium text-gray-900">
          {(shipLocation.lat || 25.2048).toFixed(4)}°N,{" "}
          {(shipLocation.lng || 55.2708).toFixed(4)}°E
        </div>
      </div>

      {/* Live Indicator */}
      <div className="absolute top-4 left-4 flex items-center gap-2 bg-gradient-to-r from-green-500 to-green-600 text-white px-2 py-1 rounded-full shadow-lg z-10 border border-green-400">
        <motion.div
          animate={{ scale: [1, 1.3, 1] }}
          transition={{ duration: 1, repeat: Infinity }}
          className="w-2 h-2 bg-white rounded-full"
        />
        <span className="text-sm font-medium">LIVE TRACKING</span>
      </div>

      {/* Speed and Status Indicator */}
      <div className="absolute bottom-[4.3%] right-[7%] bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg">
        <div className="text-xs text-gray-600 mb-1">Vessel Status</div>
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
          <span className="text-sm font-medium text-gray-900">In Transit</span>
        </div>
        <div className="text-xs text-gray-600 mt-1">Speed: 12.5 knots</div>
      </div>
    </div>
  );
};

// Info Item Component
const InfoItem = ({
  label,
  value,
  icon: Icon,
  className,
}: {
  label: string;
  value: string | React.ReactNode;
  icon: React.ElementType;
  className?: string;
}) => (
  <div className={cn("space-y-1", className)}>
    <div className="flex items-center gap-2 text-sm text-gray-500">
      <Icon size={14} />
      <span className="font-medium">{label}</span>
    </div>
    <div className="text-sm text-slate-900 font-medium pl-5">
      {typeof value === "string" ? (
        <span className="break-words">{value}</span>
      ) : (
        value
      )}
    </div>
  </div>
);

// Transform database shipment data to display format
const transformShipmentToDetails = (
  shipment: ShipmentWithRelations
): ShipmentDetails => {
  // Extract tracking metadata from database
  const trackingMetadata = (shipment as any).metadata;
  const hasRealTimeTracking = !!(
    trackingMetadata &&
    trackingMetadata.locations &&
    trackingMetadata.locations.length > 0
  );

  // Calculate progress and status
  const now = new Date();
  const departure = (shipment as any).departure
    ? new Date((shipment as any).departure)
    : null;
  const arrival = (shipment as any).arrival
    ? new Date((shipment as any).arrival)
    : null;
  const estimatedArrival = (shipment as any).estimated_time_of_arrival
    ? new Date((shipment as any).estimated_time_of_arrival)
    : arrival;

  let progress = 0;
  let status = "SCHEDULED";

  // Use tracking data status if available, otherwise calculate from schedule
  if (hasRealTimeTracking && trackingMetadata.metadata) {
    const trackingStatus = trackingMetadata.metadata.shippingStatus;
    switch (trackingStatus) {
      case "IN_TRANSIT":
        status = "IN_TRANSIT";
        progress = 50; // Default progress for in-transit
        break;
      case "DELIVERED":
      case "ARRIVED":
        status = "DELIVERED";
        progress = 100;
        break;
      case "SCHEDULED":
        status = "SCHEDULED";
        progress = 0;
        break;
      default:
        status = "ACTIVE";
        progress = 10;
        break;
    }
  } else if (departure && arrival) {
    // Fallback to schedule-based calculation
    if (now >= arrival) {
      progress = 100;
      status = "DELIVERED";
    } else if (now >= departure) {
      const totalJourneyMs = arrival.getTime() - departure.getTime();
      const elapsedMs = now.getTime() - departure.getTime();
      progress = Math.min(95, Math.max(5, (elapsedMs / totalJourneyMs) * 100));
      status = "IN_TRANSIT";
    }
  }

  // Check for delays (only if not using real-time tracking)
  if (!hasRealTimeTracking) {
    const isDelayed =
      estimatedArrival && now > estimatedArrival && status !== "DELIVERED";
    if (isDelayed) {
      status = "DELAYED";
    }
  }

  // Parse coordinates and determine current location
  let coordinates = { lat: 0, lng: 0 };
  let currentLocation = "Unknown Location";

  if (hasRealTimeTracking && trackingMetadata.locations) {
    // Use real-time tracking data to determine current location
    const trackingStatus = trackingMetadata.metadata?.shippingStatus;
    const origin = trackingMetadata.locations[0];
    const destination = trackingMetadata.locations[1];

    switch (trackingStatus) {
      case "IN_TRANSIT":
        currentLocation = `En route from ${origin?.name || "Unknown"} to ${destination?.name || "Unknown"}`;
        // Use midpoint coordinates for in-transit
        if (origin?.coordinates && destination?.coordinates) {
          coordinates = {
            lat: (origin.coordinates.lat + destination.coordinates.lat) / 2,
            lng: (origin.coordinates.lng + destination.coordinates.lng) / 2,
          };
        }
        break;
      case "DELIVERED":
      case "ARRIVED":
        currentLocation = destination?.name || "Delivered";
        if (destination?.coordinates) {
          coordinates = destination.coordinates;
        }
        break;
      default:
        currentLocation = origin?.name || "At Origin";
        if (origin?.coordinates) {
          coordinates = origin.coordinates;
        }
        break;
    }
  } else if ((shipment as any).coordinates) {
    // Fallback to parsing existing coordinate string
    const coordMatch = (shipment as any).coordinates.match(
      /(-?\d+\.?\d*)[°,\s]+([NS]?)[,\s]*(-?\d+\.?\d*)[°,\s]+([EW]?)/i
    );
    if (coordMatch && coordMatch[1] && coordMatch[3]) {
      let lat = parseFloat(coordMatch[1]);
      let lng = parseFloat(coordMatch[3]);

      if (coordMatch[2] && coordMatch[2].toUpperCase() === "S") lat = -lat;
      if (coordMatch[4] && coordMatch[4].toUpperCase() === "W") lng = -lng;

      coordinates = { lat, lng };
      currentLocation = (shipment as any).coordinates;
    }
  } else {
    // Use shipment-specific locations, fallback to freight locations
    const shipmentDeparting = (shipment as any).departing || "Unknown Location";
    const shipmentDestination =
      (shipment as any).destination || "Unknown Location";

    // Use departing or destination based on progress
    currentLocation =
      progress > 50 ? `En route to ${shipmentDestination}` : shipmentDeparting;
  }

  // Calculate time elapsed and ETA with tracking data support
  const calculateTimeElapsed = (departureTime: string | null): string => {
    if (hasRealTimeTracking && trackingMetadata.metadata?.updatedAt) {
      // Use tracking data update time for more accurate elapsed time
      const trackingStart = new Date(trackingMetadata.metadata.updatedAt);
      const elapsed = now.getTime() - trackingStart.getTime();
      const days = Math.floor(elapsed / (1000 * 60 * 60 * 24));
      const hours = Math.floor(
        (elapsed % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
      );
      return `${days}d ${hours}h (live)`;
    }

    if (!departureTime) return "Not started";
    const elapsed = now.getTime() - new Date(departureTime).getTime();
    const days = Math.floor(elapsed / (1000 * 60 * 60 * 24));
    const hours = Math.floor(
      (elapsed % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
    );
    return `${days}d ${hours}h`;
  };

  const calculateETA = (arrivalTime: string | null): string => {
    if (
      hasRealTimeTracking &&
      trackingMetadata.metadata?.shippingStatus === "DELIVERED"
    ) {
      return "Delivered";
    }

    if (
      hasRealTimeTracking &&
      trackingMetadata.metadata?.shippingStatus === "IN_TRANSIT"
    ) {
      // For in-transit shipments with tracking, show estimated based on typical transit times
      return "ETA updating...";
    }

    if (!arrivalTime) return "N/A";
    if (status === "DELIVERED") return "Delivered";
    const remaining = new Date(arrivalTime).getTime() - now.getTime();
    if (remaining <= 0) return "Overdue";
    const days = Math.floor(remaining / (1000 * 60 * 60 * 24));
    const hours = Math.floor(
      (remaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
    );
    return `${days}d ${hours}h`;
  };

  // Use tracking data locations first, then shipment-specific locations for display
  let shipmentDeparting = "Unknown Origin";
  let shipmentDestination = "Unknown Destination";

  if (hasRealTimeTracking && trackingMetadata.locations.length >= 2) {
    // Use tracking data locations
    shipmentDeparting = trackingMetadata.locations[0]?.name || "Unknown Origin";
    shipmentDestination =
      trackingMetadata.locations[1]?.name || "Unknown Destination";
  } else {
    // Fallback to shipment-specific locations
    shipmentDeparting = (shipment as any).departing || "Unknown Origin";
    shipmentDestination =
      (shipment as any).destination || "Unknown Destination";
  }

  return {
    ...shipment,
    id: shipment.id,
    name: `Shipment to ${shipmentDestination}`,
    trackingNumber: shipment.tracking_number || "N/A",
    billOfLading: (shipment as any).bill_of_lading || undefined,
    freight: {
      id: shipment.freights?.id || "",
      name: shipment.freights?.name || "Unknown Freight",
      code: shipment.freights?.scac_codes || "N/A",
      type: shipment.freights?.type || "GENERAL",
      origin: shipmentDeparting,
      destination: shipmentDestination,
    },
    batches: shipment.batches
      ? [
          {
            id: shipment.batches.id,
            name: shipment.batches.name,
            bill_of_lading: (shipment.batches as any).bill_of_lading || "",
            code: shipment.batches.code,
            type: shipment.batches.type || "GENERAL",
            weight: shipment.batches.weight,
            weightUnit: "KG", // Default weight unit
            status: shipment.batches.status || "ACTIVE",
            value: 0, // Will be calculated from cargo if available
            currency: "USD",
            customers: [], // Will be populated from cargo if available
            cargo: [], // Will be populated from cargo if available
          },
        ]
      : [],
    selectedBatchId: (shipment as any).batch_id || shipment.batches?.id || "",
    cargo: [], // Will be populated from selected batch
    schedule: {
      departureDateTime:
        (shipment as any).departure || new Date().toISOString(),
      arrivalDateTime: (shipment as any).arrival || new Date().toISOString(),
      estimatedTimeElapsed: calculateTimeElapsed((shipment as any).departure),
      estimatedTimeOfArrival: calculateETA(
        (shipment as any).estimated_time_of_arrival || (shipment as any).arrival
      ),
    },
    status,
    progress: Math.round(progress),
    currentLocation,
    coordinates,
    createdBy: {
      id: shipment.accounts?.id || "",
      name: shipment.accounts?.email || "System",
      email: shipment.accounts?.email || "",
      role: "User",
    },
    customers: [], // Will be populated from batch cargo
    tracking: [], // Placeholder for tracking events
    notes: shipment.attachment
      ? `Attachment: ${shipment.attachment}`
      : undefined,
    created_at: shipment.created_at || new Date().toISOString(),
    updated_at: shipment.updated_at || new Date().toISOString(),
    // Add tracking metadata
    trackingMetadata: hasRealTimeTracking ? trackingMetadata : null,
    hasRealTimeTracking,
    routeLocations: hasRealTimeTracking ? trackingMetadata.locations : [],
  };
};

export default function ShipmentDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [shipment, setShipment] = useState<ShipmentDetails | null>(null);
  const [selectedBatchId, setSelectedBatchId] = useState<string>("");
  const [activeBatchTab, setActiveBatchTab] = useState<string>("cargo");
  const [isTrackingActive, setIsTrackingActive] = useState<boolean>(false);
  const [freightBatches, setFreightBatches] = useState<BatchInfo[]>([]);
  const [loadingBatches, setLoadingBatches] = useState<boolean>(false);

  // Enhanced tracking data from Sinay API
  const [trackingData, setTrackingData] = useState<EnhancedTrackingData | null>(
    null
  );
  const [loadingTracking, setLoadingTracking] = useState<boolean>(false);
  const [trackingError, setTrackingError] = useState<string | null>(null);
  const [selectedBatchDetails, setSelectedBatchDetails] =
    useState<BatchInfo | null>(null);

  // Filter states for batch management
  const [cargoSearchTerm, setCargoSearchTerm] = useState("");
  const [cargoColumnFilters, setCargoColumnFilters] = useState<ColumnFilter[]>(
    []
  );
  const [customerSearchTerm, setCustomerSearchTerm] = useState("");
  const [customerColumnFilters, setCustomerColumnFilters] = useState<
    ColumnFilter[]
  >([]);
  const [batchDataRefreshing, setBatchDataRefreshing] = useState(false);

  // Edit shipment dialog state
  const [isEditShipmentOpen, setIsEditShipmentOpen] = useState(false);
  const [editFormData, setEditFormData] = useState({
    tracking_number_type: "INTERNAL" as const,
    freight_id: "",
    batch_id: "",
    bill_of_lading: "",
    freight_reference_id: "",
    departing: "",
    destination: "",
    departure: "",
    arrival: "",
    estimated_time_of_arrival: "",
  });
  const [isEditSubmitting, setIsEditSubmitting] = useState(false);
  const [editFreights, setEditFreights] = useState<FreightWithRelations[]>([]);
  const [editBatches, setEditBatches] = useState<any[]>([]);
  const [loadingEditBatches, setLoadingEditBatches] = useState(false);

  // Delete confirmation dialog state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Document management state
  const [documents, setDocuments] = useState<DocumentWithAccount[]>([]);
  const [loadingDocuments, setLoadingDocuments] = useState(false);
  const [uploadingDocument, setUploadingDocument] = useState(false);
  const [deletingDocuments, setDeletingDocuments] = useState<Set<string>>(
    new Set()
  );
  const {
    isOpen: isPreviewOpen,
    document: previewDocument,
    openPreview,
    closePreview,
  } = useDocumentPreview();

  // Table columns for filtering
  const cargoTableColumns: TableColumn[] = [
    {
      key: "description",
      label: "Description",
      type: "string",
      searchable: true,
    },
    { key: "name", label: "Name", type: "string", searchable: true },
    { key: "quantity", label: "Quantity", type: "number", searchable: false },
    { key: "weight", label: "Weight", type: "number", searchable: false },
    { key: "weightUnit", label: "Weight Unit", type: "enum", searchable: true },
    { key: "value", label: "Value", type: "number", searchable: false },
    { key: "currency", label: "Currency", type: "enum", searchable: true },
  ];

  const customerTableColumns: TableColumn[] = [
    { key: "name", label: "Customer Name", type: "string", searchable: true },
    { key: "company", label: "Company", type: "string", searchable: true },
    { key: "type", label: "Type", type: "enum", searchable: true },
    { key: "email", label: "Email", type: "string", searchable: true },
    { key: "phone", label: "Phone", type: "string", searchable: true },
  ];

  // Filter handlers for cargo
  const handleCargoColumnFilterAdd = (filter: ColumnFilter) => {
    setCargoColumnFilters((prev) => [...prev, filter]);
  };

  const handleCargoColumnFilterRemove = (index: number) => {
    setCargoColumnFilters((prev) => prev.filter((_, i) => i !== index));
  };

  // Filter handlers for customers
  const handleCustomerColumnFilterAdd = (filter: ColumnFilter) => {
    setCustomerColumnFilters((prev) => [...prev, filter]);
  };

  const handleCustomerColumnFilterRemove = (index: number) => {
    setCustomerColumnFilters((prev) => prev.filter((_, i) => i !== index));
  };

  let { user: authUser } = useAppSelector((state) => state.auth);

  // Filter cargo based on search term and column filters
  const filteredCargo = useMemo(() => {
    if (!selectedBatchDetails?.cargo) {
      return [];
    }

    let filtered = [...selectedBatchDetails.cargo];

    // Apply search term filter
    if (cargoSearchTerm) {
      const searchLower = cargoSearchTerm.toLowerCase();
      filtered = filtered.filter((item) => {
        return (
          item.name?.toLowerCase().includes(searchLower) ||
          item.description?.toLowerCase().includes(searchLower) ||
          item.weightUnit?.toLowerCase().includes(searchLower) ||
          item.currency?.toLowerCase().includes(searchLower)
        );
      });
    }

    // Apply column filters
    if (cargoColumnFilters.length > 0) {
      filtered = filtered.filter((item) => {
        return cargoColumnFilters.every((filter) => {
          const value = (item as any)[filter.column];
          if (value === null || value === undefined) return false;

          const stringValue = String(value).toLowerCase();
          const filterValue = filter.value.toLowerCase();

          return stringValue.includes(filterValue);
        });
      });
    }

    return filtered;
  }, [selectedBatchDetails?.cargo, cargoSearchTerm, cargoColumnFilters]);

  // Filter customers based on search term and column filters
  const filteredCustomers = useMemo(() => {
    if (!selectedBatchDetails?.customers) {
      return [];
    }

    let filtered = [...selectedBatchDetails.customers];

    // Apply search term filter
    if (customerSearchTerm) {
      const searchLower = customerSearchTerm.toLowerCase();
      filtered = filtered.filter((customer) => {
        return (
          customer.name?.toLowerCase().includes(searchLower) ||
          customer.company?.toLowerCase().includes(searchLower) ||
          customer.type?.toLowerCase().includes(searchLower) ||
          customer.email?.toLowerCase().includes(searchLower) ||
          customer.phone?.toLowerCase().includes(searchLower)
        );
      });
    }

    // Apply column filters
    if (customerColumnFilters.length > 0) {
      filtered = filtered.filter((customer) => {
        return customerColumnFilters.every((filter) => {
          const value = (customer as any)[filter.column];
          if (value === null || value === undefined) return false;

          const stringValue = String(value).toLowerCase();
          const filterValue = filter.value.toLowerCase();

          return stringValue.includes(filterValue);
        });
      });
    }

    return filtered;
  }, [
    selectedBatchDetails?.customers,
    customerSearchTerm,
    customerColumnFilters,
  ]);

  // Enhanced function to get shipment tracking data from Sinay API
  const getShipmentTrackingData = async (
    trackingNumber: string,
    billOfLading: string
  ): Promise<EnhancedTrackingData | null> => {
    try {
      // Validate required parameters
      const shipmentNumber = billOfLading || trackingNumber;
      if (!shipmentNumber) {
        throw new Error("No shipment number or bill of lading provided");
      }

      // Get freight SCAC code for the API call
      const freightScacCode = shipment?.freight?.code || "";
      if (!freightScacCode) {
        console.warn(
          "⚠️ No SCAC code available for freight, API call may fail"
        );
      }

      // Build query parameters
      const params = new URLSearchParams({
        shipmentNumber: shipmentNumber,
        sealine: freightScacCode,
        shipmentType: "BL", // leave default value as is
        route: "true",
        ais: "false",
      });

      const options = {
        method: "GET",
        headers: {
          accept: "application/json",
          API_KEY: "ccba8297-dd22-4e61-acf7-88367527a0db",
        },
      };

      const response = await fetch(
        `https://api.sinay.ai/container-tracking/api/v2/shipment?${params.toString()}`,
        options
      );

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          `API request failed: ${response.status} - ${response.statusText}`
        );
      }

      const data: SinayApiResponse = await response.json();

      // Transform Sinay API response to our enhanced tracking data format
      const enhancedData: EnhancedTrackingData = {
        currentLocation: {
          name: data.routeData.coordinates
            ? data.locations.find(
                (loc) =>
                  Math.abs(
                    loc.coordinates.lat - data.routeData.coordinates.lat
                  ) < 0.1 &&
                  Math.abs(
                    loc.coordinates.lng - data.routeData.coordinates.lng
                  ) < 0.1
              )?.name || "At Sea"
            : data.route.pol.location.name,
          coordinates:
            data.routeData.coordinates || data.route.pol.location.coordinates,
          country: data.routeData.coordinates
            ? data.locations.find(
                (loc) =>
                  Math.abs(
                    loc.coordinates.lat - data.routeData.coordinates.lat
                  ) < 0.1 &&
                  Math.abs(
                    loc.coordinates.lng - data.routeData.coordinates.lng
                  ) < 0.1
              )?.country || "International Waters"
            : data.route.pol.location.country,
          timezone: data.routeData.coordinates
            ? data.locations.find(
                (loc) =>
                  Math.abs(
                    loc.coordinates.lat - data.routeData.coordinates.lat
                  ) < 0.1 &&
                  Math.abs(
                    loc.coordinates.lng - data.routeData.coordinates.lng
                  ) < 0.1
              )?.timezone || "UTC"
            : data.route.pol.location.timezone,
        },
        route: {
          origin: data.route.pol.location,
          destination: data.route.pod.location,
          currentProgress: calculateRouteProgress(data),
        },
        vessel: data.vessels[0] || null,
        timeline: data.containers[0]?.events || [],
        routePath: data.routeData.routeSegments[0]?.path || [],
        status: data.metadata.shippingStatus,
        lastUpdated: data.metadata.updatedAt,
      };

      return enhancedData;
    } catch (error: any) {
      console.error("❌ Failed to fetch shipment tracking data:", {
        error: error.message,
        shipmentNumber: billOfLading || trackingNumber,
        freightCode: shipment?.freight?.code,
      });
      return null;
    }
  };

  // Calculate route progress based on events and timeline
  const calculateRouteProgress = (data: SinayApiResponse): number => {
    const events = data.containers[0]?.events || [];
    const route = data.route;

    // Check if vessel has departed from POL
    const departureEvent = events.find(
      (event) =>
        event.eventCode === "DEPA" &&
        event.location.locode === route.pol.location.locode
    );

    // Check if vessel has arrived at POD
    const arrivalEvent = events.find(
      (event) =>
        event.eventCode === "ARRI" &&
        event.location.locode === route.pod.location.locode
    );

    if (arrivalEvent) {
      return 100; // Arrived at destination
    }

    if (departureEvent) {
      // Calculate progress based on time elapsed
      const departureTime = new Date(departureEvent.date);
      const estimatedArrival = new Date(route.pod.date);
      const now = new Date();

      const totalJourney = estimatedArrival.getTime() - departureTime.getTime();
      const elapsed = now.getTime() - departureTime.getTime();

      if (totalJourney > 0) {
        return Math.min(95, Math.max(5, (elapsed / totalJourney) * 100));
      }
    }

    // Check for loading events
    const loadEvent = events.find((event) => event.eventCode === "LOAD");
    if (loadEvent) {
      return 10; // Container loaded, journey starting
    }

    return 0; // Not started
  };

  // Function to fetch and update tracking data for the current shipment
  const fetchShipmentTracking = async () => {
    if (!shipment?.trackingNumber && !shipment?.billOfLading) {
      setTrackingError("No tracking number or bill of lading available");
      return;
    }

    if (!shipment?.freight?.code) {
      setTrackingError("No freight SCAC code available for tracking");
      return;
    }

    setLoadingTracking(true);
    setTrackingError(null);

    try {
      console.log("🔄 Fetching tracking data for shipment:", {
        trackingNumber: shipment.trackingNumber,
        billOfLading: shipment.billOfLading,
        freightCode: shipment.freight.code,
      });

      const trackingData = await getShipmentTrackingData(
        shipment.trackingNumber || "",
        shipment.billOfLading || ""
      );

      if (trackingData) {
        setTrackingData(trackingData);
        console.log("✅ Tracking data loaded successfully");
      } else {
        setTrackingError("No tracking data available from Sinay API");
      }
    } catch (error: any) {
      const errorMessage = error.message || "Failed to load tracking data";
      setTrackingError(errorMessage);
      console.error("❌ Tracking fetch error:", error);
    } finally {
      setLoadingTracking(false);
    }
  };

  // Fetch batches for the freight with cargo and customer data
  const fetchFreightBatches = async (freightId: string) => {
    try {
      setLoadingBatches(true);

      // Note: getBatchesByFreight method has been removed as batches are no longer tied to freights
      // This function now returns empty data since batches are independent
      const transformedBatches: BatchInfo[] = [];
      setFreightBatches(transformedBatches);
    } catch (error) {
      console.error("Error fetching freight batches:", error);
      setFreightBatches([]);
    } finally {
      setLoadingBatches(false);
    }
  };

  const fetchShipment = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch shipment with relations from database
      const result = await shipmentService.getShipmentWithRelations(
        params.id as string
      );

      if (result.success && result.data) {
        const transformedShipment = transformShipmentToDetails(result.data);

        // Populate batch cargo and customers if available
        if (
          transformedShipment.batches.length > 0 &&
          (result.data as any).batches?.cargos
        ) {
          const batchData = (result.data as any).batches;
          const firstBatch = transformedShipment.batches[0];

          if (firstBatch) {
            firstBatch.cargo =
              batchData.cargos?.map((cargo: any) => ({
                id: cargo.id,
                description: cargo.particular || "..",
                quantity: cargo.quantity || 1, // Default quantity
                weight: cargo.weight_value || 0,
                weightUnit: cargo.weight_unit || "KG",
                dimensions: {
                  length: cargo.dimension_length || 0,
                  width: cargo.dimension_width || 0,
                  height: cargo.dimension_height || 0,
                  unit: cargo.dimension_unit || "CM",
                },
                cbm: cargo.cbm_value || 0,
                value: cargo.total_price || 0,
                currency: "USD",
                batchId: firstBatch.id,
              })) || [];

            firstBatch.customers =
              batchData.cargos?.flatMap((cargo: any) =>
                cargo.customers
                  ? [
                      {
                        id: cargo.customers.id,
                        name: cargo.customers.name,
                        email: cargo.customers.email,
                        phone: cargo.customers.phone || "",
                        company: "", // Not available in current schema
                        type: "SENDER" as const, // Default type
                      },
                    ]
                  : []
              ) || [];

            firstBatch.value =
              batchData.cargos?.reduce(
                (total: number, cargo: any) => total + (cargo.total_price || 0),
                0
              ) || 0;
          }
        }

        setShipment(transformedShipment);

        // Prioritize shipment's specific batch_id, then fall back to freight batches
        const shipmentBatchId = (result.data as any).batch_id;

        if (shipmentBatchId) {
          // If shipment has a specific batch_id, focus on that batch
          setSelectedBatchId(shipmentBatchId);
          console.log(
            `🎯 Focusing on shipment's specific batch: ${shipmentBatchId}`
          );
        } else {
          // If no specific batch_id, determine approach based on freight existence
          if (transformedShipment.freight.id) {
            // Use freight batches approach
            setSelectedBatchId(
              transformedShipment.selectedBatchId ||
                transformedShipment.batches[0]?.id ||
                ""
            );
            console.log("📦 Using freight batches approach");
          } else {
            // Use shipment.batches approach
            setSelectedBatchId(transformedShipment.batches[0]?.id || "");
            console.log("📦 Using shipment.batches approach (no freight)");
          }
        }

        // Fetch freight batches only if freight exists
        if (transformedShipment.freight.id) {
          await fetchFreightBatches(transformedShipment.freight.id);
        } else {
          // If no freight, we'll use shipment.batches directly
          console.log(
            "📦 No freight_id found, using shipment.batches directly"
          );
        }
      } else {
        setError(result.error || "Shipment not found");
      }
    } catch (error) {
      console.error("Error fetching shipment:", error);
      setError("Failed to fetch shipment details");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (params.id) {
      fetchShipment();
    }
  }, [params.id]);

  // Update selected batch details when selectedBatchId changes
  useEffect(() => {
    if (selectedBatchId) {
      let batchDetails = null;

      // First try to find in freight batches
      if (freightBatches.length > 0) {
        batchDetails = freightBatches.find(
          (batch) => batch.id === selectedBatchId
        );
      }

      // If not found in freight batches and shipment has batches, try there
      if (!batchDetails && shipment?.batches && shipment.batches.length > 0) {
        batchDetails = shipment.batches.find(
          (batch) => batch.id === selectedBatchId
        );
      }

      setSelectedBatchDetails(batchDetails || null);
    } else {
      setSelectedBatchDetails(null);
    }
  }, [selectedBatchId, freightBatches, shipment?.batches]);

  // Ensure shipment's specific batch remains focused when freight batches are loaded
  useEffect(() => {
    if (shipment && freightBatches.length > 0) {
      const shipmentBatchId = (shipment as any).batch_id;
      if (shipmentBatchId && selectedBatchId !== shipmentBatchId) {
        // Re-focus on shipment's specific batch if it exists in freight batches
        const shipmentBatch = freightBatches.find(
          (batch) => batch.id === shipmentBatchId
        );
        if (shipmentBatch) {
          setSelectedBatchId(shipmentBatchId);
          console.log(
            `🔄 Re-focusing on shipment's specific batch: ${shipmentBatchId}`
          );
        }
      }
    }
  }, [freightBatches, shipment, selectedBatchId]);

  useEffect(() => {
    // Fetch documents for this shipment
    if (shipment?.id) fetchDocuments();
  }, [shipment]);

  // Fetch tracking data when shipment is loaded
  useEffect(() => {
    if (shipment?.trackingNumber || shipment?.billOfLading) {
      fetchShipmentTracking();
    }
  }, [shipment?.trackingNumber, shipment?.billOfLading]);

  // Edit shipment functions
  const openEditShipment = async () => {
    if (!shipment) return;

    // Populate form with current shipment data
    setEditFormData({
      tracking_number_type:
        (shipment as any).tracking_number_type || "INTERNAL",
      freight_id: shipment.freight.id || "",
      batch_id: shipment.batches?.[0]?.id || shipment.selectedBatchId || "",
      bill_of_lading: shipment.billOfLading || "",
      freight_reference_id: (shipment as any).freight.reference_id || "",
      departing: shipment.freight.origin || "",
      destination: shipment.freight.destination || "",
      departure: shipment.schedule.departureDateTime
        ? new Date(shipment.schedule.departureDateTime)
            .toISOString()
            .slice(0, 16)
        : "",
      arrival: shipment.schedule.arrivalDateTime
        ? new Date(shipment.schedule.arrivalDateTime).toISOString().slice(0, 16)
        : "",
      estimated_time_of_arrival: (() => {
        // Try different date sources in order of preference
        const etaValue =
          (shipment as any).estimated_time_of_arrival ||
          (shipment as any).estimated_arrival ||
          shipment.schedule.arrivalDateTime;

        if (
          etaValue &&
          etaValue !== "20d 8h" &&
          !etaValue.includes("d") &&
          !etaValue.includes("h")
        ) {
          try {
            return new Date(etaValue).toISOString().slice(0, 16);
          } catch {
            return "";
          }
        }
        return "";
      })(),
    });

    // Load freights for the edit form
    try {
      const freightsResult = await freightService.getAll({ limit: 100 });
      if (freightsResult.success && freightsResult.data) {
        setEditFreights(freightsResult.data);
      }
    } catch (error) {
      console.error("Error loading freights for edit:", error);
    }

    // Load all available batches irrespective of freight
    await loadAllEditBatches();

    setIsEditShipmentOpen(true);
  };

  const handleEditInputChange = (field: string, value: string | string[]) => {
    setEditFormData((prev) => ({ ...prev, [field]: value }));

    // Handle freight selection and auto-populate locations
    if (field === "freight_id" && typeof value === "string") {
      if (value && value !== "" && value !== "none") {
        // Auto-populate departing and destination from selected freight
        const selectedFreight = editFreights.find((f) => f.id === value);
        if (selectedFreight) {
          setEditFormData((prev) => ({
            ...prev,
            [field]: value,
            departing: "",
            destination: "",
          }));
        }
      } else {
        // Handle "no freight" selection
        setEditFormData((prev) => ({
          ...prev,
          [field]: value === "none" ? "" : value, // Convert "none" to empty string
        }));
      }
    }

    // Handle batch selection
    if (field === "batch_id" && typeof value === "string") {
      setEditFormData((prev) => ({
        ...prev,
        [field]: value === "none" ? "" : value, // Convert "none" to empty string
      }));
    }
  };

  // Note: This function is no longer needed as batches are not tied to freights
  const loadEditBatchesForFreight = async (_freightId: string) => {
    // Batches are now independent of freights, so this function returns empty data
    setEditBatches([]);
    setLoadingEditBatches(false);
  };

  const loadAllEditBatches = async () => {
    try {
      setLoadingEditBatches(true);
      const result = await batchService.getAll({ limit: 100 });

      if (result.success && result.data) {
        setEditBatches(result.data);
      } else {
        setEditBatches([]);
      }
    } catch (error) {
      console.error("Error loading all batches for edit:", error);
      setEditBatches([]);
    } finally {
      setLoadingEditBatches(false);
    }
  };

  const refreshShipmentData = async () => {
    try {
      // Fetch updated shipment data
      const result = await shipmentService.getShipmentWithRelations(
        params.id as string
      );

      if (result.success && result.data) {
        const transformedShipment = transformShipmentToDetails(result.data);
        setShipment(transformedShipment);

        // Note: Freight batches are no longer available as batches are independent
        // Setting empty batches since they're no longer tied to freights
        setFreightBatches([]);

        console.log("Shipment data refreshed successfully");
      } else {
        toast.error("Failed to refresh shipment data");
      }
    } catch (error) {
      console.error("Error refreshing shipment data:", error);
      toast.error("An error occurred while refreshing data");
    }
  };

  const handleEditSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!shipment) return;

    if (!editFormData.departing) {
      toast.error("Please select a departing location");
      return;
    }

    if (!editFormData.destination) {
      toast.error("Please select a destination location");
      return;
    }

    setIsEditSubmitting(true);
    try {
      // Update shipment using the shipment service
      const result = await shipmentService.updateShipment(shipment.id, {
        freight_id:
          editFormData.freight_id && editFormData.freight_id !== "none"
            ? editFormData.freight_id
            : null,
        batch_id:
          editFormData.batch_id &&
          editFormData.batch_id !== "none" &&
          editFormData.batch_id !== ""
            ? editFormData.batch_id
            : null,
        bill_of_lading: editFormData.bill_of_lading || null,
        freight_reference_id: editFormData.freight_reference_id || null,
      });

      if (result.success) {
        toast.success("Shipment updated successfully!");
        setIsEditShipmentOpen(false);

        // Perform soft data refresh instead of full page reload
        await refreshShipmentData();
      } else {
        toast.error(`Failed to update shipment: ${result.error}`);
      }
    } catch (error: any) {
      console.error("Error updating shipment:", error);
      toast.error("An error occurred while updating the shipment");
    } finally {
      setIsEditSubmitting(false);
    }
  };

  // Handle shipment deletion
  const handleDeleteShipment = async () => {
    if (!shipment) return;

    setIsDeleting(true);
    try {
      const result = await shipmentService.delete(shipment.id);

      if (result.success) {
        toast.success("Shipment deleted successfully!");
        // Navigate back to shipment management after successful deletion
        router.push("/shipping-management");
      } else {
        toast.error(`Failed to delete shipment: ${result.error}`);
        setIsDeleteModalOpen(false);
      }
    } catch (error: any) {
      console.error("Error deleting shipment:", error);
      toast.error("An error occurred while deleting the shipment");
      setIsDeleteModalOpen(false);
    } finally {
      setIsDeleting(false);
    }
  };

  // Fetch documents for the shipment
  const fetchDocuments = async () => {
    if (!shipment?.id) return;

    setLoadingDocuments(true);
    try {
      const result = await documentService.getDocumentsByEntity(
        "shipments",
        shipment.id
      );

      if (result.success && result.data) {
        setDocuments(result.data);
      } else {
        setDocuments([]);
      }
    } catch (error) {
      console.error("Error fetching documents:", error);
      setDocuments([]);
    } finally {
      setLoadingDocuments(false);
    }
  };

  // Handle document upload
  const handleDocumentUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files;
    if (!files || files.length === 0 || !shipment?.id) return;

    setUploadingDocument(true);
    try {
      const file = files[0];
      if (!file) {
        toast.error("No file selected");
        return;
      }

      // First upload the file to storage
      const uploadResult = await documentService.uploadToStorage({
        content: file,
        fileName: file.name,
        contentType: file.type || "application/octet-stream",
        folder: "shipments",
        metadata: {
          shipmentId: shipment.id,
          trackingNumber: shipment.trackingNumber,
        },
      });

      if (!uploadResult.success || !uploadResult.data) {
        toast.error(`Failed to upload file: ${uploadResult.error}`);
        return;
      }

      // Create document record
      const documentResult = await documentService.createDocument({
        name: file.name,
        path: uploadResult.data,
        category: "shipment",
        description: `Document for shipment ${shipment.trackingNumber}`,
        associated_table: "shipments",
        associated_id: shipment.id,
        account_id: authUser?.accountId,
        status: "ACTIVE",
      });

      if (documentResult.success) {
        toast.success("Document uploaded successfully!");
        // Refresh documents list
        await fetchDocuments();
      } else {
        toast.error(
          `Failed to create document record: ${documentResult.error}`
        );
      }
    } catch (error: any) {
      console.error("Error uploading document:", error);
      toast.error("An error occurred while uploading the document");
    } finally {
      setUploadingDocument(false);
      // Reset file input
      event.target.value = "";
    }
  };

  // Handle document deletion
  const handleDeleteDocument = async (doc: DocumentWithAccount) => {
    if (
      !confirm(
        `Are you sure you want to delete "${doc.name}"? This action cannot be undone.`
      )
    ) {
      return;
    }

    setDeletingDocuments((prev) => new Set(prev).add(doc.id));
    try {
      // Use deleteDocumentWithFile to remove both the record and the file from storage
      const result = await documentService.deleteDocumentWithFile(doc.id);

      if (result.success) {
        toast.success("Document deleted successfully!");
        // Refresh documents list
        await fetchDocuments();
      } else {
        toast.error(`Failed to delete document: ${result.error}`);
      }
    } catch (error: any) {
      console.error("Error deleting document:", error);
      toast.error("An error occurred while deleting the document");
    } finally {
      setDeletingDocuments((prev) => {
        const newSet = new Set(prev);
        newSet.delete(doc.id);
        return newSet;
      });
    }
  };

  // Helper function to get file extension
  const getFileExtension = (fileName: string): string => {
    const extension = fileName.split(".").pop()?.toLowerCase();
    return extension || "pdf";
  };

  // Handle document view
  const handleViewDocument = async (doc: DocumentWithAccount) => {
    try {
      const downloadResult = await documentService.getDocumentDownloadUrl(
        doc.path
      );

      if (downloadResult.success && downloadResult.data) {
        openPreview({
          uri: downloadResult.data,
          fileName: doc.name,
          fileType: getFileExtension(doc.name),
        });
      } else {
        alert("Failed to load document for preview");
      }
    } catch (error) {
      console.error("Error loading document:", error);
      alert("Failed to load document");
    }
  };

  // Handle document download
  const handleDownloadDocument = async (doc: DocumentWithAccount) => {
    try {
      const downloadResult = await documentService.getDocumentDownloadUrl(
        doc.path
      );
      if (downloadResult.success && downloadResult.data) {
        const link = document.createElement("a");
        link.href = downloadResult.data;
        link.download = doc.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        alert("Failed to download document");
      }
    } catch (error) {
      console.error("Error downloading document:", error);
      alert("Failed to download document");
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto" />
          <p className="text-gray-600">Loading shipment details...</p>
        </div>
      </div>
    );
  }

  if (error || !shipment) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center space-y-4 max-w-md">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto" />
          <div>
            <h1 className="text-xl font-semibold text-gray-900">
              Error Loading Shipment
            </h1>
            <p className="text-gray-600 mt-2">
              {error || "Shipment not found"}
            </p>
          </div>
          <Button
            onClick={() => router.push("/shipping-management")}
            className="gap-2"
          >
            <ArrowLeft size={16} />
            Back to Shipments
          </Button>
        </div>
      </div>
    );
  }

  const FreightIcon = getFreightTypeIcon(shipment.freight.type);

  // Helper function to get KPI data based on shipment batch priority
  const getKPIData = () => {
    const shipmentBatchId = shipment && (shipment as any).batch_id;
    const hasFreight = shipment?.freight?.id;

    if (
      shipmentBatchId &&
      selectedBatchDetails &&
      selectedBatchId === shipmentBatchId
    ) {
      // Use shipment-specific batch data
      return {
        batches: [selectedBatchDetails],
        isShipmentFocus: true,
        focusLabel: "Shipment Batch",
        dataSource: "shipment_batch",
      };
    } else if (
      !hasFreight &&
      shipment?.batches &&
      shipment.batches.length > 0
    ) {
      // Use shipment.batches when no freight_id exists
      return {
        batches: shipment.batches,
        isShipmentFocus: false,
        focusLabel: "Shipment Batches",
        dataSource: "shipment_batches",
      };
    } else {
      // Use all freight batches data
      return {
        batches: freightBatches,
        isShipmentFocus: false,
        focusLabel: "All Freight Batches",
        dataSource: "freight_batches",
      };
    }
  };

  const kpiData = getKPIData();

  return (
    <PageTransition>
      <div className="p-6 md:p-8 lg:p-10 space-y-6 bg-gray-50 min-h-screen">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-4">
          <div>
            <div className="flex items-center text-sm text-gray-500 mb-1">
              <Link
                href="/shipping-management"
                className="hover:text-gray-700 flex items-center gap-1"
              >
                <ArrowLeft size={16} />
                Shipment Management
              </Link>
              <ChevronRight size={16} className="mx-1 text-gray-400" />
              <span className="text-gray-900 font-medium">
                {shipment.trackingNumber}
              </span>
            </div>
            <h1 className="text-3xl font-bold text-gray-900">
              Shipment Details
            </h1>
            <p className="text-sm text-gray-600 mt-1 flex items-center gap-2">
              <FreightIcon size={16} />
              {shipment.freight.type} Freight • {shipment.name}
            </p>
          </div>
          <div className="flex items-center gap-3 flex-shrink-0">
            <Button
              variant="outline"
              size="sm"
              onClick={async () => {
                await refreshShipmentData();
              }}
              className="gap-2"
            >
              <RefreshCw size={16} />
              Refresh
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={openEditShipment}
              className="gap-2"
            >
              <FileText size={16} />
              Edit Shipment
            </Button>
            <Badge
              variant={
                shipment.status === "IN_TRANSIT" ? "default" : "secondary"
              }
              className="text-sm"
            >
              {shipment.status.replace("_", " ")}
            </Badge>
          </div>
        </div>

        {/* Tabulated Summary & Live Tracking */}
        <AnimatedCard className="mb-6">
          <Tabs defaultValue="summary" className="w-full">
            <TabsList className="grid w-full grid-cols-2 rounded-none bg-gray-50">
              <TabsTrigger
                value="summary"
                className="flex items-center gap-2 data-[state=active]:bg-white"
              >
                <Package size={16} />
                Summary & Overview
              </TabsTrigger>
              <TabsTrigger
                value="tracking"
                className="flex items-center gap-2 data-[state=active]:bg-white"
              >
                <Map size={16} />
                Live Tracking
              </TabsTrigger>
            </TabsList>

            <div className="h-[1px] bg-gray-200 flex" />

            {/* Summary Tab */}
            <TabsContent value="summary" className="px-2 py-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Summary Information */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Shipment Summary
                  </h3>
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 gap-4 text-sm">
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-gray-600">
                            Tracking Number:
                          </span>
                          <span className="font-medium font-mono">
                            {shipment.trackingNumber}
                          </span>
                        </div>
                        {shipment.billOfLading && (
                          <div className="flex justify-between">
                            <span className="text-gray-600">
                              Bill of Lading (BIL):
                            </span>
                            <span className="font-medium font-mono">
                              {shipment.billOfLading}
                            </span>
                          </div>
                        )}
                        <div className="flex justify-between">
                          <span className="text-gray-600">Status:</span>
                          <Badge
                            variant={
                              shipment.status === "IN_TRANSIT"
                                ? "default"
                                : "secondary"
                            }
                            className="text-xs"
                          >
                            {shipment.status.replace("_", " ")}
                          </Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">
                            Current Location:
                          </span>
                          <span className="font-medium">
                            {shipment.currentLocation}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Time Elapsed:</span>
                          <span className="font-medium">
                            {shipment.schedule.estimatedTimeElapsed}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Freight:</span>
                          <span className="font-medium">
                            {shipment.freight.name}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Origin:</span>
                          <span className="font-medium">
                            {shipment.freight.origin}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Destination:</span>
                          <span className="font-medium">
                            {shipment.freight.destination}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">ETA:</span>
                          <span className="font-medium">
                            {shipment.schedule.estimatedTimeOfArrival}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Created By:</span>
                          <span className="font-medium">
                            {shipment.createdBy.name}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Enhanced KPIs */}
                <AnimatedCard className="p-4 space-y-4">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                      <Gauge size={18} />
                      Key Performance Indicators
                    </h2>
                    <Badge
                      variant={
                        kpiData.isShipmentFocus ? "default" : "secondary"
                      }
                      className="text-xs"
                    >
                      {kpiData.focusLabel}
                    </Badge>
                  </div>

                  {/* No Data State */}
                  {!loadingBatches && kpiData.batches.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      <Package
                        size={48}
                        className="mx-auto mb-4 text-gray-300"
                      />
                      <p className="text-lg font-medium">
                        {kpiData.isShipmentFocus
                          ? "No Shipment Batch Data Available"
                          : kpiData.dataSource === "shipment_batches"
                            ? "No Shipment Batches Available"
                            : "No Batch Data Available"}
                      </p>
                      <p className="text-sm">
                        {kpiData.isShipmentFocus
                          ? "Statistics will appear when the shipment's batch is loaded"
                          : kpiData.dataSource === "shipment_batches"
                            ? "Statistics will appear when batches are added to this shipment"
                            : "Statistics will appear when batches are created for this freight"}
                      </p>
                    </div>
                  )}

                  {/* KPI Content */}
                  {(loadingBatches || kpiData.batches.length > 0) && (
                    <>
                      {/* Shipment Batch Focus Indicator */}
                      {kpiData.isShipmentFocus && (
                        <div className="flex items-center gap-2 p-3 bg-blue-50 border border-blue-200 rounded-lg mb-4">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          <span className="text-sm font-medium text-blue-700">
                            KPIs focused on shipment's designated batch:{" "}
                            {selectedBatchDetails?.name}
                          </span>
                        </div>
                      )}

                      {/* Shipment Batches Indicator */}
                      {kpiData.dataSource === "shipment_batches" &&
                        !kpiData.isShipmentFocus && (
                          <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg mb-4">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span className="text-sm font-medium text-green-700">
                              KPIs showing shipment batches (no freight
                              association)
                            </span>
                          </div>
                        )}

                      {/* Count KPIs */}
                      <div className="grid grid-cols-3 gap-3 mb-4">
                        <div className="text-center p-3 bg-blue-50 rounded-lg">
                          <Package
                            size={18}
                            className="mx-auto mb-1 text-blue-600"
                          />
                          <p className="text-lg font-bold text-blue-900">
                            {loadingBatches ? "..." : kpiData.batches.length}
                          </p>
                          <p className="text-xs text-blue-700">
                            {kpiData.isShipmentFocus ? "Batch" : "Batches"}
                          </p>
                        </div>
                        <div className="text-center p-3 bg-green-50 rounded-lg">
                          <Container
                            size={18}
                            className="mx-auto mb-1 text-green-600"
                          />
                          <p className="text-lg font-bold text-green-900">
                            {loadingBatches
                              ? "..."
                              : kpiData.batches.reduce(
                                  (total, batch) => total + batch.cargo.length,
                                  0
                                )}
                          </p>
                          <p className="text-xs text-green-700">Cargo Items</p>
                        </div>
                        <div className="text-center p-3 bg-purple-50 rounded-lg">
                          <Users
                            size={18}
                            className="mx-auto mb-1 text-purple-600"
                          />
                          <p className="text-lg font-bold text-purple-900">
                            {loadingBatches
                              ? "..."
                              : (() => {
                                  const uniqueCustomers = new Set();
                                  kpiData.batches.forEach((batch) =>
                                    batch.customers.forEach((customer) =>
                                      uniqueCustomers.add(customer.id)
                                    )
                                  );
                                  return uniqueCustomers.size;
                                })()}
                          </p>
                          <p className="text-xs text-purple-700">Customers</p>
                        </div>
                      </div>

                      {/* Weight KPIs */}
                      <div className="space-y-3">
                        <h3 className="text-sm font-semibold text-gray-700 border-b pb-2">
                          Weight Analysis
                        </h3>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between items-center">
                            <span className="text-gray-600 flex items-center gap-2">
                              <Ship size={14} />
                              Freight Weight:
                            </span>
                            <span className="font-bold text-blue-600">
                              {loadingBatches
                                ? "Loading..."
                                : formatWeight(
                                    kpiData.batches.reduce(
                                      (total, batch) => total + batch.weight,
                                      0
                                    ),
                                    "KILOGRAMS"
                                  )}
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-gray-600 flex items-center gap-2">
                              <Package size={14} />
                              Batch Total:
                            </span>
                            <span className="font-bold text-green-600">
                              {loadingBatches
                                ? "Loading..."
                                : formatWeight(
                                    kpiData.batches.reduce(
                                      (total, batch) => total + batch.weight,
                                      0
                                    ),
                                    "KILOGRAMS"
                                  )}
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-gray-600 flex items-center gap-2">
                              <Container size={14} />
                              Cargo Total:
                            </span>
                            <span className="font-bold text-orange-600">
                              {loadingBatches
                                ? "Loading..."
                                : formatWeight(
                                    kpiData.batches.reduce(
                                      (total, batch) =>
                                        total +
                                        batch.cargo.reduce(
                                          (cargoTotal, item) =>
                                            cargoTotal + item.weight,
                                          0
                                        ),
                                      0
                                    ),
                                    "KILOGRAMS"
                                  )}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Value Analysis */}
                      <div className="space-y-3">
                        <h3 className="text-sm font-semibold text-gray-700 border-b pb-2">
                          Value Analysis
                        </h3>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between items-center">
                            <span className="text-gray-600 flex items-center gap-2">
                              <Globe size={14} />
                              Total Cargo Value:
                            </span>
                            <span className="font-bold text-green-600">
                              {loadingBatches
                                ? "Loading..."
                                : `USD ${kpiData.batches
                                    .reduce(
                                      (total, batch) => total + batch.value,
                                      0
                                    )
                                    .toLocaleString()}`}
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-gray-600 flex items-center gap-2">
                              <Package size={14} />
                              Average Batch Value:
                            </span>
                            <span className="font-bold text-blue-600">
                              {loadingBatches
                                ? "Loading..."
                                : kpiData.batches.length > 0
                                  ? `USD ${Math.round(
                                      kpiData.batches.reduce(
                                        (total, batch) => total + batch.value,
                                        0
                                      ) / kpiData.batches.length
                                    ).toLocaleString()}`
                                  : "USD 0"}
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-gray-600 flex items-center gap-2">
                              <Container size={14} />
                              CBM Total:
                            </span>
                            <span className="font-bold text-orange-600">
                              {loadingBatches
                                ? "Loading..."
                                : formatVolume(
                                    kpiData.batches.reduce(
                                      (total, batch) =>
                                        total +
                                        batch.cargo.reduce(
                                          (cargoTotal, item) =>
                                            cargoTotal + item.cbm.value,
                                          0
                                        ),
                                      0
                                    ),
                                    "METER_CUBIC"
                                  )}
                            </span>
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                </AnimatedCard>
              </div>
            </TabsContent>

            {/* Live Tracking Tab */}
            <TabsContent value="tracking" className="px-2 py-4">
              <div className="flex flex-col gap-8">
                {/* Live Map */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <Map size={18} />
                    Live Ship Location
                  </h3>
                  <div className="relative h-[33.65rem] rounded-lg overflow-hidden border border-gray-200">
                    {/* Google Maps Container */}
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: isTrackingActive ? 1 : 0 }}
                      transition={{ duration: 0.5 }}
                      className="absolute inset-0 z-10"
                    >
                      <GoogleMapComponent
                        vesselType={shipment.freight.type}
                        shipLocation={{
                          lat:
                            trackingData?.currentLocation.coordinates.lat ||
                            shipment.coordinates.lat,
                          lng:
                            trackingData?.currentLocation.coordinates.lng ||
                            shipment.coordinates.lng,
                        }}
                        currentLocation={
                          trackingData
                            ? `${trackingData.currentLocation.name}, ${trackingData.currentLocation.country}`
                            : shipment.currentLocation
                        }
                        routePath={trackingData?.routePath || []}
                      />
                    </motion.div>

                    {/* Overlay with Start Tracking Button */}
                    <AnimatePresence>
                      {!isTrackingActive && (
                        <motion.div
                          initial={{ opacity: 1 }}
                          exit={{ opacity: 0, scale: 0.95 }}
                          transition={{ duration: 0.5, ease: "easeInOut" }}
                          className="absolute inset-0 z-20 bg-gradient-to-br from-blue-50 to-blue-100 flex flex-col items-center justify-center"
                        >
                          <motion.div
                            initial={{ scale: 0.9, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            transition={{ delay: 0.2, duration: 0.3 }}
                            className="text-center space-y-6"
                          >
                            <div className="relative">
                              <motion.div
                                animate={{
                                  rotate: [0, 5, -5, 0],
                                  scale: [1, 1.05, 1],
                                }}
                                transition={{
                                  duration: 3,
                                  repeat: Infinity,
                                  ease: "easeInOut",
                                }}
                              >
                                <Ship
                                  size={64}
                                  className="mx-auto text-blue-500"
                                />
                              </motion.div>
                              <motion.div
                                animate={{ scale: [1, 1.2, 1] }}
                                transition={{
                                  duration: 2,
                                  repeat: Infinity,
                                  ease: "easeInOut",
                                }}
                                className="absolute -top-2 -right-2 w-4 h-4 bg-green-500 rounded-full"
                              />
                            </div>

                            <div className="space-y-2">
                              <h4 className="text-xl font-semibold text-blue-800">
                                Real-time Vessel Tracking
                              </h4>
                              <p className="text-blue-700 text-sm max-w-md">
                                Track your shipment's live location with
                                interactive map view
                              </p>
                            </div>

                            <motion.div
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                            >
                              <Button
                                onClick={() => setIsTrackingActive(true)}
                                className="gap-2 bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg font-medium shadow-lg"
                              >
                                <Navigation size={20} />
                                Start Tracking
                              </Button>
                            </motion.div>

                            <div className="space-y-2 text-sm text-blue-600 bg-white/50 rounded-lg p-4 max-w-sm">
                              <div className="flex items-center justify-center gap-2">
                                <Globe size={14} />
                                <span className="font-medium">
                                  {shipment.coordinates.lat}°N,{" "}
                                  {shipment.coordinates.lng}°E
                                </span>
                              </div>
                              <div className="flex items-center justify-center gap-2">
                                <MapPin size={14} />
                                <span>{shipment.currentLocation}</span>
                              </div>
                            </div>
                          </motion.div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>

                {/* Tracking Details */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="grid grid-cols-1">
                    <div className="flex justify-between items-center">
                      <h3 className="text-lg font-semibold text-gray-900">
                        Tracking Information
                      </h3>
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={fetchShipmentTracking}
                          disabled={loadingTracking}
                          className="text-xs"
                        >
                          {loadingTracking ? (
                            <Loader2 size={12} className="animate-spin mr-1" />
                          ) : (
                            <RefreshCw size={12} className="mr-1" />
                          )}
                          Refresh
                        </Button>
                        {isTrackingActive && (
                          <Button
                            size="sm"
                            className="text-xs"
                            onClick={() => setIsTrackingActive(false)}
                          >
                            Stop Tracking
                          </Button>
                        )}
                      </div>
                    </div>
                    <div className="grid grid-cols-1 gap-6">
                      <motion.div
                        className="p-4 bg-gray-50 rounded-lg"
                        animate={{
                          backgroundColor: isTrackingActive
                            ? "#f0f9ff"
                            : "#f9fafb",
                        }}
                        transition={{ duration: 0.3 }}
                      >
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-medium text-gray-900">
                            Current Status
                          </h4>
                          {loadingTracking && (
                            <Loader2
                              size={16}
                              className="animate-spin text-blue-500"
                            />
                          )}
                        </div>

                        {trackingError ? (
                          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                            <p className="text-sm text-red-600">
                              {trackingError}
                            </p>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={fetchShipmentTracking}
                              className="mt-2 text-xs"
                            >
                              <RefreshCw size={12} className="mr-1" />
                              Retry
                            </Button>
                          </div>
                        ) : trackingData ? (
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-600">Location:</span>
                              <span className="font-medium text-blue-600">
                                {trackingData.currentLocation.name},{" "}
                                {trackingData.currentLocation.country}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">
                                Coordinates:
                              </span>
                              <span className="font-mono text-xs">
                                {trackingData.currentLocation.coordinates.lat.toFixed(
                                  4
                                )}
                                °N,{" "}
                                {trackingData.currentLocation.coordinates.lng.toFixed(
                                  4
                                )}
                                °E
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Status:</span>
                              <Badge
                                variant={
                                  trackingData.status === "IN_TRANSIT"
                                    ? "default"
                                    : trackingData.status === "DELIVERED"
                                      ? "secondary"
                                      : "outline"
                                }
                                className="text-xs"
                              >
                                {trackingData.status.replace("_", " ")}
                              </Badge>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Progress:</span>
                              <span className="font-medium">
                                {Math.round(trackingData.route.currentProgress)}
                                %
                              </span>
                            </div>
                            {trackingData.vessel && (
                              <div className="flex justify-between">
                                <span className="text-gray-600">Vessel:</span>
                                <span className="font-medium text-xs">
                                  {trackingData.vessel.name}
                                </span>
                              </div>
                            )}
                            <div className="flex justify-between">
                              <span className="text-gray-600">Origin:</span>
                              <span className="font-medium text-xs">
                                {trackingData.route.origin.name}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">
                                Destination:
                              </span>
                              <span className="font-medium text-xs">
                                {trackingData.route.destination.name}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">
                                Last Updated:
                              </span>
                              <span className="font-medium text-xs">
                                {new Date(
                                  trackingData.lastUpdated
                                ).toLocaleString()}
                              </span>
                            </div>
                          </div>
                        ) : (
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-600">Location:</span>
                              <span className="font-medium text-blue-600">
                                {shipment.currentLocation}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">
                                Coordinates:
                              </span>
                              <span className="font-mono text-xs">
                                {shipment.coordinates.lat}°N,{" "}
                                {shipment.coordinates.lng}°E
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Status:</span>
                              <Badge
                                variant={
                                  shipment.status === "IN_TRANSIT"
                                    ? "default"
                                    : "secondary"
                                }
                              >
                                {shipment.status.replace("_", " ")}
                              </Badge>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Departure:</span>
                              <span className="font-medium text-sm">
                                {shipment.schedule.departureDateTime
                                  ? new Date(
                                      shipment.schedule.departureDateTime
                                    ).toLocaleDateString("en-US", {
                                      month: "short",
                                      day: "numeric",
                                      hour: "2-digit",
                                      minute: "2-digit",
                                    })
                                  : "Not scheduled"}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Arrival:</span>
                              <span className="font-medium text-sm">
                                {shipment.schedule.arrivalDateTime
                                  ? new Date(
                                      shipment.schedule.arrivalDateTime
                                    ).toLocaleDateString("en-US", {
                                      month: "short",
                                      day: "numeric",
                                      hour: "2-digit",
                                      minute: "2-digit",
                                    })
                                  : "Not scheduled"}
                              </span>
                            </div>
                          </div>
                        )}
                      </motion.div>

                      <motion.div
                        className="p-4 rounded-lg"
                        animate={{
                          backgroundColor: isTrackingActive
                            ? "#f0fdf4"
                            : "#f0fdf4",
                        }}
                        transition={{ duration: 0.3 }}
                      >
                        <h4 className="font-medium text-gray-900 mb-3">
                          GPS Tracking
                        </h4>
                        <div className="flex items-center justify-center gap-2 p-3 bg-white rounded-lg">
                          <motion.div
                            className="w-3 h-3 rounded-full"
                            animate={{
                              backgroundColor: isTrackingActive
                                ? "#22c55e"
                                : "#6b7280",
                              scale: isTrackingActive ? [1, 1.2, 1] : 1,
                            }}
                            transition={{
                              backgroundColor: { duration: 0.3 },
                              scale: {
                                duration: 1,
                                repeat: isTrackingActive ? Infinity : 0,
                              },
                            }}
                          />
                          <span
                            className={`font-medium text-sm ${
                              isTrackingActive
                                ? "text-green-700"
                                : "text-gray-500"
                            }`}
                          >
                            {isTrackingActive
                              ? "Live GPS Active"
                              : "GPS Standby"}
                          </span>
                        </div>
                      </motion.div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 gap-6">
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <h4 className="font-medium text-gray-900 mb-3">
                        Journey Progress
                      </h4>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">Progress:</span>
                          <span className="font-bold text-blue-600">
                            {trackingData
                              ? Math.round(trackingData.route.currentProgress)
                              : shipment.progress}
                            %
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <motion.div
                            className="bg-blue-600 h-2 rounded-full"
                            initial={{ width: 0 }}
                            animate={{
                              width: `${trackingData ? trackingData.route.currentProgress : shipment.progress}%`,
                            }}
                            transition={{ duration: 1, ease: "easeOut" }}
                          />
                        </div>
                        <div className="flex justify-between text-xs text-gray-500">
                          <span>
                            {trackingData?.route.origin.name ||
                              shipment.freight.origin}
                          </span>
                          <span>
                            {trackingData?.route.destination.name ||
                              shipment.freight.destination}
                          </span>
                        </div>
                        <div className="flex justify-between text-sm mt-3 pt-2 border-t border-blue-200">
                          <span className="text-gray-600">Time Elapsed:</span>
                          <span className="font-medium">
                            {shipment.schedule.estimatedTimeElapsed}
                          </span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">ETA:</span>
                          <span className="font-medium">
                            {shipment.schedule.estimatedTimeOfArrival}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Route Timeline from Sinay API */}
                    {trackingData && trackingData.timeline.length > 0 && (
                      <div className="p-4 bg-green-50 rounded-lg">
                        <h4 className="font-medium text-gray-900 mb-3">
                          Route Timeline
                        </h4>
                        <div className="space-y-3 max-h-64 overflow-y-auto">
                          {trackingData.timeline
                            .sort(
                              (a, b) =>
                                new Date(b.date).getTime() -
                                new Date(a.date).getTime()
                            )
                            .slice(0, 5)
                            .map((event, index) => (
                              <div
                                key={index}
                                className="flex items-start gap-3"
                              >
                                <div className="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2" />
                                <div className="flex-1 min-w-0">
                                  <div className="text-sm font-medium text-gray-900">
                                    {event.description}
                                  </div>
                                  <div className="text-xs text-gray-500 mt-1">
                                    {event.location.name},{" "}
                                    {event.location.country}
                                  </div>
                                  <div className="text-xs text-gray-400">
                                    {new Date(event.date).toLocaleString()}
                                  </div>
                                  {event.vessel && (
                                    <div className="text-xs text-blue-600 mt-1">
                                      Vessel: {event.vessel.name}
                                    </div>
                                  )}
                                </div>
                                <Badge variant="outline" className="text-xs">
                                  {event.eventCode}
                                </Badge>
                              </div>
                            ))}
                        </div>
                        {trackingData.timeline.length > 5 && (
                          <div className="text-xs text-gray-500 text-center mt-2 pt-2 border-t border-green-200">
                            Showing latest 5 events of{" "}
                            {trackingData.timeline.length} total
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </AnimatedCard>

        {/* Document Section */}
        <AnimatedCard className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              <FileText size={18} />
              Shipment Documents
            </h2>
            <div className="flex items-center gap-3">
              <div className="text-sm text-gray-500">
                {documents.length} document{documents.length !== 1 ? "s" : ""}
              </div>
              <div className="relative">
                <input
                  type="file"
                  id="document-upload"
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  onChange={handleDocumentUpload}
                  disabled={uploadingDocument}
                  accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif,.txt,.csv,.xlsx,.xls"
                />
                <Button
                  variant="outline"
                  size="sm"
                  disabled={uploadingDocument}
                  className="gap-2"
                >
                  {uploadingDocument ? (
                    <>
                      <Loader2 size={16} className="animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload size={16} />
                      Upload New
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>

          {loadingDocuments ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
              <span className="ml-2 text-gray-500">Loading documents...</span>
            </div>
          ) : documents.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <FileText size={48} className="mx-auto mb-4 text-gray-300" />
              <p className="text-lg font-medium">No Documents Available</p>
              <p className="text-sm">
                Documents related to this shipment will appear here
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {documents.map((doc) => (
                <div
                  key={doc.id}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <FileText size={20} className="text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">{doc.name}</h4>
                      <p className="text-sm text-gray-500">
                        {doc.category && (
                          <span className="capitalize">{doc.category}</span>
                        )}
                        {doc.category && doc.description && " • "}
                        {doc.description}
                      </p>
                      <p className="text-xs text-gray-400">
                        Created{" "}
                        {doc.created_at
                          ? new Date(doc.created_at).toLocaleDateString()
                          : "Unknown date"}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewDocument(doc)}
                      className="gap-2"
                    >
                      <Eye size={14} />
                      Preview
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDownloadDocument(doc)}
                      className="gap-2"
                    >
                      <Download size={14} />
                      Download
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteDocument(doc)}
                      disabled={deletingDocuments.has(doc.id)}
                      className="gap-2 text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200"
                    >
                      {deletingDocuments.has(doc.id) ? (
                        <>
                          <Loader2 size={14} className="animate-spin" />
                          Deleting...
                        </>
                      ) : (
                        <>
                          <Trash2 size={14} />
                          Delete
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </AnimatedCard>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-6">
            {/* Batch Selection and Information */}
            <AnimatedCard className="p-6">
              <div className="flex justify-between items-center mb-5">
                <div>
                  <h2 className="text-lg font-semibold text-gray-900">
                    Batch Management
                  </h2>
                </div>
                <Badge variant="secondary">
                  {loadingBatches
                    ? "Loading..."
                    : kpiData.dataSource === "shipment_batches"
                      ? `${shipment?.batches?.length || 0} Shipment Batches`
                      : `${freightBatches.length} Freight Batches Available`}
                </Badge>
              </div>

              {/* Batch Autocomplete Selection */}
              <div className="mb-6">
                <Label
                  htmlFor="batch-select"
                  className="text-sm font-medium mb-2 block"
                >
                  {shipment && (shipment as any).batch_id
                    ? "Shipment Batch (Default Focus)"
                    : "Select Batch to View Details"}
                </Label>
                <Select
                  value={selectedBatchId}
                  onValueChange={setSelectedBatchId}
                  disabled={loadingBatches}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue
                      placeholder={
                        loadingBatches
                          ? "Loading batches..."
                          : kpiData.batches.length === 0
                            ? kpiData.dataSource === "shipment_batches"
                              ? "No batches available for this shipment"
                              : "No batches available for this freight"
                            : "Choose a batch to view cargo and details"
                      }
                    />
                  </SelectTrigger>
                  <SelectContent className="max-h-[200px] overflow-y-auto">
                    {kpiData.batches.map((batch) => {
                      const isShipmentBatch =
                        shipment && (shipment as any).batch_id === batch.id;
                      const isFromShipmentBatches =
                        kpiData.dataSource === "shipment_batches";
                      return (
                        <SelectItem key={batch.id} value={batch.id}>
                          <div className="flex items-center justify-between w-full">
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{batch.name}</span>
                              {isShipmentBatch && (
                                <Badge variant="default" className="text-xs">
                                  Shipment Batch
                                </Badge>
                              )}
                              {isFromShipmentBatches && !isShipmentBatch && (
                                <Badge variant="outline" className="text-xs">
                                  Shipment
                                </Badge>
                              )}
                            </div>
                            <span className="text-xs text-gray-500 ml-2">
                              ({batch.code})
                            </span>
                          </div>
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>

              {/* Selected Batch Information */}
              {selectedBatchId && selectedBatchDetails && (
                <div className="space-y-4">
                  {/* Shipment Batch Indicator */}
                  {shipment &&
                    (shipment as any).batch_id === selectedBatchId && (
                      <div className="flex items-center gap-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-sm font-medium text-blue-700">
                          This is the shipment's designated batch
                        </span>
                      </div>
                    )}

                  {/* Enhanced Batch Summary */}
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 p-4 bg-gray-50 rounded-lg">
                    <InfoItem
                      label="Batch Name"
                      value={selectedBatchDetails.name}
                      icon={Package}
                    />
                    <InfoItem
                      label="Batch Code"
                      value={selectedBatchDetails.code}
                      icon={Hash}
                    />
                    <InfoItem
                      label="Weight"
                      value={formatWeight(
                        selectedBatchDetails.weight,
                        selectedBatchDetails.weightUnit
                      )}
                      icon={Gauge}
                    />
                    <InfoItem
                      label="Value"
                      value={`${selectedBatchDetails.currency} ${selectedBatchDetails.value.toLocaleString()}`}
                      icon={Globe}
                    />
                    <InfoItem
                      label="Cargo Count"
                      value={`${selectedBatchDetails.cargo.length} items`}
                      icon={Container}
                    />
                    <InfoItem
                      label="Customer Count"
                      value={`${selectedBatchDetails.customers.length} customers`}
                      icon={Users}
                    />
                  </div>

                  {/* Tabulated Table Views */}
                  <div className="bg-white rounded-lg border">
                    <Tabs
                      value={activeBatchTab}
                      onValueChange={setActiveBatchTab}
                      className="w-full"
                    >
                      <TabsList className="grid w-full grid-cols-2 rounded-none border-b bg-gray-50">
                        <TabsTrigger
                          value="cargo"
                          className="flex items-center gap-2 data-[state=active]:bg-white"
                        >
                          <Container size={16} />
                          Cargo ({filteredCargo.length} of{" "}
                          {selectedBatchDetails.cargo.length})
                        </TabsTrigger>
                        <TabsTrigger
                          value="customers"
                          className="flex items-center gap-2 data-[state=active]:bg-white"
                        >
                          <Users size={16} />
                          Customers ({filteredCustomers.length} of{" "}
                          {selectedBatchDetails.customers.length})
                        </TabsTrigger>
                      </TabsList>

                      {/* Cargo Table */}
                      <TabsContent value="cargo" className="p-0 m-0">
                        {/* Filter Panel for Cargo */}
                        <FilterPanel
                          className="border-none pt-0"
                          searchTerm={cargoSearchTerm}
                          onSearchChange={setCargoSearchTerm}
                          columnFilters={cargoColumnFilters}
                          onColumnFilterAdd={handleCargoColumnFilterAdd}
                          onColumnFilterRemove={handleCargoColumnFilterRemove}
                          enableDynamicFilters={true}
                          columns={cargoTableColumns}
                          tableData={selectedBatchDetails?.cargo || []}
                          defaultFilterColumn="description"
                          autoSelectDefaultColumn={true}
                          onRefresh={async () => {
                            setBatchDataRefreshing(true);
                            // Refresh batch data by re-selecting the batch
                            if (selectedBatchId) {
                              const batchDetails = freightBatches.find(
                                (batch) => batch.id === selectedBatchId
                              );
                              if (batchDetails) {
                                setSelectedBatchDetails(batchDetails);
                              }
                            }
                            setBatchDataRefreshing(false);
                          }}
                          loading={loadingBatches || batchDataRefreshing}
                        />

                        <div className="overflow-x-auto">
                          <Table>
                            <TableHeader>
                              <TableRow className="bg-gray-50">
                                <TableHead className="font-semibold">
                                  Description
                                </TableHead>
                                <TableHead className="font-semibold text-center">
                                  Qty
                                </TableHead>
                                <TableHead className="font-semibold text-center">
                                  Weight
                                </TableHead>
                                <TableHead className="font-semibold text-center">
                                  Dimensions
                                </TableHead>
                                <TableHead className="font-semibold text-center">
                                  CBM
                                </TableHead>
                                <TableHead className="font-semibold text-right">
                                  Value
                                </TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {filteredCargo.length > 0 ? (
                                filteredCargo.map((item) => (
                                  <TableRow
                                    key={item.id}
                                    className="hover:bg-gray-50"
                                  >
                                    <TableCell className="text-sm text-gray-600 max-w-[200px] truncate">
                                      {item.description}
                                    </TableCell>
                                    <TableCell className="text-center">
                                      <Badge
                                        variant="outline"
                                        className="text-xs"
                                      >
                                        {item.quantity}
                                      </Badge>
                                    </TableCell>
                                    <TableCell className="text-center text-sm">
                                      {formatWeight(
                                        item.weight,
                                        item.weightUnit
                                      )}
                                    </TableCell>
                                    <TableCell className="text-center text-sm">
                                      {item.dimensions.length}×
                                      {item.dimensions.width}×
                                      {item.dimensions.height}{" "}
                                      {item.dimensions.unit}
                                    </TableCell>
                                    <TableCell className="text-center text-sm font-medium">
                                      {formatVolume(
                                        item.cbm.value,
                                        item.cbm.unit
                                      )}
                                    </TableCell>
                                    <TableCell className="text-right font-semibold text-green-600">
                                      {formatCurrency(
                                        item.value,
                                        item.currency
                                      )}
                                    </TableCell>
                                  </TableRow>
                                ))
                              ) : (
                                <TableRow>
                                  <TableCell
                                    colSpan={6}
                                    className="text-center py-8 text-gray-500"
                                  >
                                    {selectedBatchDetails?.cargo.length ===
                                    0 ? (
                                      <>
                                        <Container
                                          size={48}
                                          className="mx-auto mb-4 text-gray-300"
                                        />
                                        <p className="text-lg font-medium">
                                          No Cargo Items
                                        </p>
                                        <p className="text-sm">
                                          This batch doesn't have any cargo
                                          items.
                                        </p>
                                      </>
                                    ) : (
                                      <>
                                        <Container
                                          size={48}
                                          className="mx-auto mb-4 text-gray-300"
                                        />
                                        <p className="text-lg font-medium">
                                          No Matching Cargo
                                        </p>
                                        <p className="text-sm">
                                          No cargo items match your current
                                          search or filter criteria.
                                        </p>
                                      </>
                                    )}
                                  </TableCell>
                                </TableRow>
                              )}
                            </TableBody>
                          </Table>
                        </div>
                      </TabsContent>

                      {/* Customers Table */}
                      <TabsContent value="customers" className="p-0 m-0">
                        {/* Filter Panel for Customers */}
                        <FilterPanel
                          className="border-none pt-0"
                          searchTerm={customerSearchTerm}
                          onSearchChange={setCustomerSearchTerm}
                          columnFilters={customerColumnFilters}
                          onColumnFilterAdd={handleCustomerColumnFilterAdd}
                          onColumnFilterRemove={
                            handleCustomerColumnFilterRemove
                          }
                          enableDynamicFilters={true}
                          columns={customerTableColumns}
                          tableData={selectedBatchDetails?.customers || []}
                          defaultFilterColumn="name"
                          autoSelectDefaultColumn={true}
                          onRefresh={async () => {
                            setBatchDataRefreshing(true);
                            // Refresh batch data by re-selecting the batch
                            if (selectedBatchId) {
                              const batchDetails = freightBatches.find(
                                (batch) => batch.id === selectedBatchId
                              );
                              if (batchDetails) {
                                setSelectedBatchDetails(batchDetails);
                              }
                            }
                            setBatchDataRefreshing(false);
                          }}
                          loading={loadingBatches || batchDataRefreshing}
                        />

                        <div className="overflow-x-auto">
                          <Table>
                            <TableHeader>
                              <TableRow className="bg-gray-50">
                                <TableHead className="font-semibold">
                                  Customer Name
                                </TableHead>
                                <TableHead className="font-semibold">
                                  Company
                                </TableHead>
                                <TableHead className="font-semibold">
                                  Type
                                </TableHead>
                                <TableHead className="font-semibold">
                                  Email
                                </TableHead>
                                <TableHead className="font-semibold">
                                  Phone
                                </TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {filteredCustomers.length > 0 ? (
                                filteredCustomers.map((customer, index) => (
                                  <TableRow
                                    key={index}
                                    className="hover:bg-gray-50"
                                  >
                                    <TableCell className="font-medium">
                                      {customer.name}
                                    </TableCell>
                                    <TableCell className="text-sm text-gray-600">
                                      {customer.company}
                                    </TableCell>
                                    <TableCell>
                                      <Badge
                                        variant={
                                          customer.type === "SENDER"
                                            ? "default"
                                            : "secondary"
                                        }
                                        className="text-xs"
                                      >
                                        {customer.type}
                                      </Badge>
                                    </TableCell>
                                    <TableCell className="text-sm">
                                      {customer.email}
                                    </TableCell>
                                    <TableCell className="text-sm">
                                      {customer.phone}
                                    </TableCell>
                                  </TableRow>
                                ))
                              ) : (
                                <TableRow>
                                  <TableCell
                                    colSpan={5}
                                    className="text-center py-8 text-gray-500"
                                  >
                                    {selectedBatchDetails?.customers.length ===
                                    0 ? (
                                      <>
                                        <Users
                                          size={48}
                                          className="mx-auto mb-4 text-gray-300"
                                        />
                                        <p className="text-lg font-medium">
                                          No Customers
                                        </p>
                                        <p className="text-sm">
                                          This batch doesn't have any associated
                                          customers.
                                        </p>
                                      </>
                                    ) : (
                                      <>
                                        <Users
                                          size={48}
                                          className="mx-auto mb-4 text-gray-300"
                                        />
                                        <p className="text-lg font-medium">
                                          No Matching Customers
                                        </p>
                                        <p className="text-sm">
                                          No customers match your current search
                                          or filter criteria.
                                        </p>
                                      </>
                                    )}
                                  </TableCell>
                                </TableRow>
                              )}
                            </TableBody>
                          </Table>
                        </div>
                      </TabsContent>
                    </Tabs>
                  </div>
                </div>
              )}

              {/* Default State */}
              {!selectedBatchId && !loadingBatches && (
                <div className="text-center py-8 text-gray-500">
                  <Package size={48} className="mx-auto mb-4 text-gray-300" />
                  <p className="text-lg font-medium">
                    {freightBatches.length === 0
                      ? "No Batches Available"
                      : "Select a Batch"}
                  </p>
                  <p className="text-sm">
                    {freightBatches.length === 0
                      ? "This freight has no batches created yet"
                      : "Choose a batch from the dropdown above to view cargo details"}
                  </p>
                </div>
              )}

              {/* Loading State */}
              {loadingBatches && (
                <div className="text-center py-8 text-gray-500">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-lg font-medium">Loading Batches</p>
                  <p className="text-sm">
                    Fetching batch data for this freight...
                  </p>
                </div>
              )}
            </AnimatedCard>
          </div>

          {/* Right Column */}
          <div className="lg:col-span-1 space-y-6">
            {/* Actions */}
            <AnimatedCard className="p-6 space-y-3">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Actions
              </h2>
              <Button
                size="sm"
                onClick={openEditShipment}
                className="w-full gap-2 bg-primary hover:bg-primary/90"
              >
                <FileText size={16} />
                Edit Shipment
              </Button>
              <Button
                variant="outline"
                className="w-full gap-2 bg-primary hover:bg-primary/90"
                onClick={() => setIsDeleteModalOpen(true)}
              >
                <Trash2 size={14} />
                Delete Shipment
              </Button>
            </AnimatedCard>

            {/* Notes */}
            {shipment.notes && (
              <AnimatedCard className="p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-3">
                  Notes
                </h2>
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-sm text-yellow-800">{shipment.notes}</p>
                </div>
              </AnimatedCard>
            )}

            {/* Creator Information */}
            <AnimatedCard className="p-6 space-y-4">
              <h2 className="text-lg font-semibold text-gray-900 mb-1">
                Created By
              </h2>
              <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                  <User size={16} className="text-blue-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">
                    {shipment.createdBy.name}
                  </p>
                  <p className="text-sm text-gray-600">
                    {shipment.createdBy.role}
                  </p>
                  <p className="text-xs text-gray-500">
                    {shipment.createdBy.email}
                  </p>
                </div>
              </div>
            </AnimatedCard>
          </div>
        </div>
      </div>

      {/* Edit Shipment Dialog */}
      <Dialog open={isEditShipmentOpen} onOpenChange={setIsEditShipmentOpen}>
        <DialogContent className="sm:max-w-4xl p-0">
          <DialogHeader className="p-6 pb-4">
            <DialogTitle className="text-xl font-semibold text-gray-900 flex items-center gap-2">
              <FileText size={20} className="text-primary" />
              Edit Shipment
            </DialogTitle>
          </DialogHeader>
          <div className="px-6 pb-6 max-h-[70vh] overflow-y-auto">
            <form onSubmit={handleEditSubmit} className="space-y-6">
              {/* Tracking Type Selection */}
              <div className="space-y-2">
                <Label
                  htmlFor="tracking_number_type"
                  className="text-sm font-medium flex items-center justify-between"
                >
                  <span>Tracking Number Type</span>
                  <span className="text-xs text-gray-500 font-normal">
                    Current:{" "}
                    {(shipment as any)?.tracking_number_type || "INTERNAL"}
                  </span>
                </Label>
                <Select
                  value={editFormData.tracking_number_type}
                  onValueChange={(value) =>
                    handleEditInputChange("tracking_number_type", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select tracking type" />
                  </SelectTrigger>
                  <SelectContent>
                    {Constants.public.Enums.tracking_type_enum.map((type) => (
                      <SelectItem key={type} value={type}>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            {type}
                          </Badge>
                          <span>{type.replace("_", " ")}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Freight and Batch Selection */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Freight Selection */}
                <div className="space-y-2">
                  <Label
                    htmlFor="freight_id"
                    className="text-sm font-medium flex items-center justify-between"
                  >
                    <span>Select Freight</span>
                    <span className="text-xs text-gray-500 font-normal">
                      Current:{" "}
                      {editFreights.find(
                        (f) => f.id === editFormData.freight_id
                      )?.name ||
                        shipment?.freight.name ||
                        "None"}
                    </span>
                  </Label>
                  <Select
                    value={editFormData.freight_id}
                    onValueChange={(value) =>
                      handleEditInputChange("freight_id", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a freight for this shipment" />
                    </SelectTrigger>
                    <SelectContent className="max-h-[200px] overflow-y-auto">
                      <SelectItem value="none">
                        <div className="flex items-center gap-2">
                          <Package size={14} className="text-gray-400" />
                          <span>No Freight</span>
                        </div>
                      </SelectItem>
                      {editFreights.map((freight) => {
                        const Icon = getFreightTypeIcon(freight.type || "SEA");
                        const isCurrentFreight =
                          freight.id === editFormData.freight_id;
                        return (
                          <SelectItem key={freight.id} value={freight.id}>
                            <div className="flex items-center gap-2">
                              <Icon size={14} className="text-blue-500" />
                              <span>
                                {freight.scac_codes} - {freight.name}
                              </span>
                              {isCurrentFreight && (
                                <Badge
                                  variant="default"
                                  className="text-xs ml-2"
                                >
                                  Current
                                </Badge>
                              )}
                            </div>
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                </div>

                {/* Batch Selection */}
                <div className="space-y-2">
                  <Label
                    htmlFor="batch_id"
                    className="text-sm font-medium flex items-center justify-between"
                  >
                    <span>Select Batch (Optional)</span>
                    <span className="text-xs text-gray-500 font-normal">
                      Current:{" "}
                      {editBatches.find((b) => b.id === editFormData.batch_id)
                        ?.name ||
                        shipment?.batches?.[0]?.name ||
                        (editFormData.batch_id ? "Assigned" : "None")}
                    </span>
                  </Label>
                  <Select
                    value={editFormData.batch_id}
                    onValueChange={(value) =>
                      handleEditInputChange("batch_id", value)
                    }
                    disabled={loadingEditBatches}
                  >
                    <SelectTrigger>
                      <SelectValue
                        placeholder={
                          loadingEditBatches
                            ? "Loading all batches..."
                            : "Choose from all available batches"
                        }
                      />
                    </SelectTrigger>
                    <SelectContent className="max-h-[200px] overflow-y-auto">
                      <SelectItem value="none">
                        <div className="flex items-center gap-2">
                          <Package size={14} className="text-gray-400" />
                          <span>No Batch</span>
                        </div>
                      </SelectItem>
                      {editBatches.map((batch) => {
                        const isCurrentBatch =
                          batch.id === editFormData.batch_id;
                        const batchFreight = editFreights.find(
                          (f) => f.id === (batch as any).freight_id
                        );
                        return (
                          <SelectItem key={batch.id} value={batch.id}>
                            <div className="flex items-center gap-2">
                              <Package size={14} className="text-green-500" />
                              <div className="flex flex-col">
                                <span className="font-medium">
                                  {batch.name || batch.code}
                                </span>
                                {batchFreight && (
                                  <span className="text-xs text-gray-500">
                                    Freight: {batchFreight.scac_codes}
                                  </span>
                                )}
                              </div>
                              {isCurrentBatch && (
                                <Badge
                                  variant="default"
                                  className="text-xs ml-2"
                                >
                                  Current
                                </Badge>
                              )}
                            </div>
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Location Fields - Mandatory */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Departing Location */}
                <div className="space-y-2">
                  <Label
                    htmlFor="departing"
                    className="text-sm font-medium flex items-center justify-between"
                  >
                    <span>
                      Departing Location <span className="text-red-500">*</span>
                    </span>
                    <span className="text-xs text-gray-500 font-normal">
                      Current:{" "}
                      {(shipment as any)?.departing ||
                        shipment?.freight.origin ||
                        "Not set"}
                    </span>
                  </Label>
                  <Select
                    value={editFormData.departing}
                    onValueChange={(value) =>
                      handleEditInputChange("departing", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select departing location" />
                    </SelectTrigger>
                    <SelectContent className="max-h-[200px] md:max-h-[300px] lg:max-h-[400px] overflow-y-auto">
                      {SHIPPING_LOCATIONS.map((location) => (
                        <SelectItem
                          key={location.id}
                          value={formatLocationDisplay(location)}
                        >
                          <div className="flex items-center gap-2">
                            <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                              {location.code}
                            </span>
                            <span>{formatLocationDisplay(location)}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Destination Location */}
                <div className="space-y-2">
                  <Label
                    htmlFor="destination"
                    className="text-sm font-medium flex items-center justify-between"
                  >
                    <span>
                      Destination Location{" "}
                      <span className="text-red-500">*</span>
                    </span>
                    <span className="text-xs text-gray-500 font-normal">
                      Current:{" "}
                      {(shipment as any)?.destination ||
                        shipment?.freight.destination ||
                        "Not set"}
                    </span>
                  </Label>
                  <Select
                    value={editFormData.destination}
                    onValueChange={(value) =>
                      handleEditInputChange("destination", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select destination location" />
                    </SelectTrigger>
                    <SelectContent className="max-h-[200px] md:max-h-[300px] lg:max-h-[400px] overflow-y-auto">
                      {SHIPPING_LOCATIONS.map((location) => (
                        <SelectItem
                          key={location.id}
                          value={formatLocationDisplay(location)}
                        >
                          <div className="flex items-center gap-2">
                            <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                              {location.code}
                            </span>
                            <span>{formatLocationDisplay(location)}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Bill of Lading */}
                <div className="space-y-2">
                  <Label
                    htmlFor="bill_of_lading"
                    className="text-sm font-medium flex items-center justify-between"
                  >
                    <span>Bill of Lading (BIL)</span>
                    <span className="text-xs text-gray-500 font-normal">
                      Current: {shipment?.billOfLading || "Not set"}
                    </span>
                  </Label>
                  <Input
                    id="bill_of_lading"
                    placeholder="e.g: BIL-2024-001"
                    value={editFormData.bill_of_lading}
                    onChange={(e) =>
                      handleEditInputChange("bill_of_lading", e.target.value)
                    }
                  />
                </div>

                {/* Reference ID */}
                <div className="space-y-2">
                  <Label
                    htmlFor="freight_reference_id"
                    className="text-sm font-medium flex items-center justify-between"
                  >
                    <span>Reference ID</span>
                    <span className="text-xs text-gray-500 font-normal">
                      Current:{" "}
                      {(shipment as any)?.freight_reference_id || "Not set"}
                    </span>
                  </Label>
                  <Input
                    id="freight_reference_id"
                    placeholder="e.g: Container/Crate ID"
                    value={editFormData.freight_reference_id}
                    onChange={(e) =>
                      handleEditInputChange(
                        "freight_reference_id",
                        e.target.value
                      )
                    }
                  />
                </div>
              </div>

              {/* Date & Time Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label
                    htmlFor="departure"
                    className="text-sm font-medium flex items-center justify-between"
                  >
                    <span>Departure</span>
                    <span className="text-xs text-gray-500 font-normal">
                      Current:{" "}
                      {shipment.schedule.departureDateTime
                        ? new Date(
                            shipment.schedule.departureDateTime
                          ).toLocaleDateString("en-US", {
                            month: "short",
                            day: "numeric",
                            hour: "2-digit",
                            minute: "2-digit",
                          })
                        : "Not scheduled"}
                    </span>
                  </Label>
                  <Input
                    id="departure"
                    type="datetime-local"
                    value={editFormData.departure}
                    onChange={(e) =>
                      handleEditInputChange("departure", e.target.value)
                    }
                  />
                </div>
                <div className="space-y-2">
                  <Label
                    htmlFor="arrival"
                    className="text-sm font-medium flex items-center justify-between"
                  >
                    <span>Arrival</span>
                    <span className="text-xs text-gray-500 font-normal">
                      Current:{" "}
                      {shipment.schedule.arrivalDateTime
                        ? new Date(
                            shipment.schedule.arrivalDateTime
                          ).toLocaleDateString("en-US", {
                            month: "short",
                            day: "numeric",
                            hour: "2-digit",
                            minute: "2-digit",
                          })
                        : "Not scheduled"}
                    </span>
                  </Label>
                  <Input
                    id="arrival"
                    type="datetime-local"
                    value={editFormData.arrival}
                    onChange={(e) =>
                      handleEditInputChange("arrival", e.target.value)
                    }
                  />
                </div>
              </div>

              {/* Estimated Time of Arrival */}
              <div className="space-y-2">
                <Label
                  htmlFor="estimated_time_of_arrival"
                  className="text-sm font-medium flex items-center justify-between"
                >
                  <span>Estimated Time of Arrival (Optional)</span>
                  <span className="text-xs text-gray-500 font-normal">
                    Current:{" "}
                    {(() => {
                      const etaValue =
                        (shipment as any).estimated_time_of_arrival ||
                        (shipment as any).estimated_arrival ||
                        shipment.schedule.arrivalDateTime;

                      if (
                        etaValue &&
                        etaValue !== "20d 8h" &&
                        !etaValue.includes("d") &&
                        !etaValue.includes("h")
                      ) {
                        try {
                          return new Date(etaValue).toLocaleDateString(
                            "en-US",
                            {
                              month: "short",
                              day: "numeric",
                              hour: "2-digit",
                              minute: "2-digit",
                            }
                          );
                        } catch {
                          return "Invalid date";
                        }
                      }
                      return "Not scheduled";
                    })()}
                  </span>
                </Label>
                <Input
                  id="estimated_time_of_arrival"
                  type="datetime-local"
                  value={editFormData.estimated_time_of_arrival}
                  onChange={(e) =>
                    handleEditInputChange(
                      "estimated_time_of_arrival",
                      e.target.value
                    )
                  }
                />
              </div>

              {/* Submit Button */}
              <div className="flex justify-end gap-3 pt-4 border-t">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEditShipmentOpen(false)}
                  disabled={isEditSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isEditSubmitting}
                  className="gap-2"
                >
                  {isEditSubmitting ? (
                    <>
                      <Loader2 size={16} className="animate-spin" />
                      Updating...
                    </>
                  ) : (
                    <>
                      <FileText size={16} />
                      Update Shipment
                    </>
                  )}
                </Button>
              </div>
            </form>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-red-600">
              <Trash2 size={20} />
              Delete Shipment
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="flex items-center gap-3 p-4 bg-red-50 rounded-lg border border-red-200">
              <AlertCircle className="h-8 w-8 text-red-500 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-red-800">
                  This action cannot be undone
                </h4>
                <p className="text-sm text-red-700 mt-1">
                  This will permanently delete the shipment and all associated
                  data.
                </p>
              </div>
            </div>

            {shipment && (
              <div className="space-y-2 p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium text-gray-900">Shipment Details:</h4>
                <div className="text-sm text-gray-600 space-y-1">
                  <p>
                    <strong>Tracking Number:</strong> {shipment.trackingNumber}
                  </p>
                  <p>
                    <strong>Destination:</strong> {shipment.freight.destination}
                  </p>
                  <p>
                    <strong>Status:</strong> {shipment.status.replace("_", " ")}
                  </p>
                </div>
              </div>
            )}

            <div className="flex justify-end gap-3 pt-4 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsDeleteModalOpen(false)}
                disabled={isDeleting}
              >
                Cancel
              </Button>
              <Button
                type="button"
                variant="destructive"
                onClick={handleDeleteShipment}
                disabled={isDeleting}
                className="gap-2"
              >
                {isDeleting ? (
                  <>
                    <Loader2 size={16} className="animate-spin" />
                    Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 size={16} />
                    Delete Shipment
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Document Preview Dialog */}
      <DocumentPreviewDialog
        isOpen={isPreviewOpen}
        onClose={closePreview}
        document={previewDocument}
        title="Shipment Document Preview"
      />
    </PageTransition>
  );
}
