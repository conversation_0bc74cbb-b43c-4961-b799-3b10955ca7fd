"use client";

import { memo } from "react";
import { RefreshCw, Download, Bell, Plus } from "lucide-react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { Overview } from "@/modules/layouts/overview";

interface TaskManagementHeaderProps {
  loading: boolean;
  showNotificationTester: boolean;
  shouldShowCreateButton: (entity: string) => boolean;
  onRefresh: () => void;
  onToggleNotificationTester: () => void;
  onCreateTask: () => void;
}

/**
 * Header component for Task Management page
 *
 * Displays the page title, description, and action buttons.
 * Memoized to prevent unnecessary re-renders.
 */
export const TaskManagementHeader = memo<TaskManagementHeaderProps>(
  ({
    loading,
    showNotificationTester,
    shouldShowCreateButton,
    onRefresh,
    onToggleNotificationTester,
    onCreateTask,
  }) => {
    return (
      <Overview.Header
        title="Task Management"
        caption="Track and manage tasks, assignments, and project progress"
        actions={
          <>
            <Button
              variant="outline"
              onClick={onRefresh}
              disabled={loading}
              aria-label="Refresh tasks"
            >
              <RefreshCw
                className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`}
              />
              Refresh
            </Button>

            <Button variant="outline" aria-label="Export tasks">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>

            {shouldShowCreateButton("tasks") && (
              <Button onClick={onCreateTask} aria-label="Create new task">
                <Plus className="h-4 w-4 mr-2" />
                New Task
              </Button>
            )}
          </>
        }
      />
    );
  }
);

TaskManagementHeader.displayName = "TaskManagementHeader";
