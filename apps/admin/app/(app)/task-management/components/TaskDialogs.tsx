"use client";

import { memo } from "react";
import { CreateTaskDialog } from "@/components/task-management/create-task-dialog";
import { EditTaskDialog } from "@/components/task-management/edit-task-dialog";
import { ViewTaskDialog } from "@/components/task-management/view-task-dialog";
import { DeleteTaskDialog } from "@/components/task-management/delete-task-dialog";
import { type TaskManagementState } from "./TaskManagementContainer";

interface TaskDialogsProps {
  state: TaskManagementState;
  onClose: (dialogType: string) => void;
  onTaskMutation: () => Promise<void>;
}

/**
 * Task dialogs component
 * 
 * Manages all task-related dialogs (create, edit, view, delete).
 * Memoized to prevent unnecessary re-renders.
 */
export const TaskDialogs = memo<TaskDialogsProps>(({
  state,
  onClose,
  onTaskMutation,
}) => {
  return (
    <>
      {/* Create Task Dialog */}
      <CreateTaskDialog
        isOpen={state.isCreateDialogOpen}
        onClose={() => onClose("Create")}
        onTaskCreated={onTaskMutation}
      />

      {/* Edit Task Dialog */}
      <EditTaskDialog
        isOpen={state.isEditDialogOpen}
        onClose={() => onClose("Edit")}
        onTaskUpdated={onTaskMutation}
        task={state.selectedTask}
      />

      {/* View Task Dialog */}
      <ViewTaskDialog
        isOpen={state.isViewDialogOpen}
        onClose={() => onClose("View")}
        task={state.selectedTask}
      />

      {/* Delete Task Dialog */}
      <DeleteTaskDialog
        isOpen={state.isDeleteDialogOpen}
        onClose={() => onClose("Delete")}
        onTaskDeleted={onTaskMutation}
        task={state.selectedTask}
      />
    </>
  );
});

TaskDialogs.displayName = "TaskDialogs";
