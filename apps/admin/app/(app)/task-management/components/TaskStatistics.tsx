"use client";

import { memo } from "react";
import {
  Check<PERSON><PERSON><PERSON>,
  Clock,
  <PERSON><PERSON>Triangle,
  Users,
  Target,
  TrendingUp,
  Timer,
  User,
  CheckCircle2,
} from "lucide-react";
import { Listing } from "@/modules/listing";
import { type TaskStats } from "./TaskManagementContainer";
import { type TaskWithAssignee } from "@/lib/logistics";

interface TaskStatisticsProps {
  taskStats: TaskStats;
  tasks: TaskWithAssignee[];
  loading: boolean;
}

/**
 * Statistics component for Task Management
 * 
 * Displays key metrics and KPIs for tasks in a grid layout.
 * Memoized to prevent unnecessary re-renders when parent state changes.
 */
export const TaskStatistics = memo<TaskStatisticsProps>(({
  taskStats,
  tasks,
  loading,
}) => {
  // Calculate unique team members count
  const uniqueTeamMembers = new Set(tasks.map((t) => t.assignee)).size;

  return (
    <Listing.Statistics columns="grid-cols-5">
      <Listing.StatCard
        icon={CheckSquare}
        name="Total Tasks"
        value={taskStats.totalTasks}
        valueType="number"
        caption={
          <span className="text-xs text-blue-600 flex items-center gap-1">
            <Target className="h-3 w-3" /> All projects
          </span>
        }
        color="primary"
        loading={loading}
      />
      
      <Listing.StatCard
        icon={CheckCircle2}
        name="Completed"
        value={taskStats.completedTasks}
        valueType="number"
        caption={
          <span className="text-xs text-green-600 flex items-center gap-1">
            <TrendingUp className="h-3 w-3" />
            {taskStats.completionRate.toFixed(1)}% rate
          </span>
        }
        color="green"
        loading={loading}
      />
      
      <Listing.StatCard
        icon={Timer}
        name="In Progress"
        value={taskStats.inProgressTasks}
        valueType="number"
        caption={
          <span className="text-xs text-blue-600 flex items-center gap-1">
            <Clock className="h-3 w-3" /> Active work
          </span>
        }
        color="blue"
        loading={loading}
      />
      
      <Listing.StatCard
        icon={AlertTriangle}
        name="Overdue"
        value={taskStats.overdueTasks}
        valueType="number"
        caption={
          <span className="text-xs text-red-600 flex items-center gap-1">
            <AlertTriangle className="h-3 w-3" /> Need attention
          </span>
        }
        color="red"
        loading={loading}
      />
      
      <Listing.StatCard
        icon={Users}
        name="Team Members"
        value={uniqueTeamMembers}
        valueType="number"
        caption={
          <span className="text-xs text-purple-600 flex items-center gap-1">
            <User className="h-3 w-3" /> Active assignees
          </span>
        }
        color="purple"
        loading={loading}
      />
    </Listing.Statistics>
  );
});

TaskStatistics.displayName = "TaskStatistics";
