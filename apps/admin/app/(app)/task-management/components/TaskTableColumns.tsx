"use client";

import {
  <PERSON>,
  Timer,
  CheckCircle2,
  <PERSON>Circle,
  Flag,
  User,
  Eye,
  Edit,
  Trash2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "lucide-react";
import { Badge } from "@workspace/ui/components/badge";
import { But<PERSON> } from "@workspace/ui/components/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { type TaskWithAssignee } from "@/lib/logistics";

interface TaskTableColumnsProps {
  onViewTask: (task: TaskWithAssignee) => void;
  onEditTask: (task: TaskWithAssignee) => void;
  onDeleteTask: (task: TaskWithAssignee) => void;
}

/**
 * Table columns configuration for tasks
 *
 * Returns an array of column definitions for the task table.
 * Includes status badges, priority indicators, and action buttons.
 */
export function TaskTableColumns({
  onViewTask,
  onEditTask,
  onDeleteTask,
}: TaskTableColumnsProps) {
  // Helper function for status badges
  const getStatusBadge = (status: TaskWithAssignee["status"]) => {
    const defaultConfig = {
      color: "bg-gray-100 text-gray-800",
      icon: Clock,
    };
    const statusConfig: Record<string, { color: string; icon: any }> = {
      CREATED: { color: "bg-gray-100 text-gray-800", icon: Clock },
      PENDING: { color: "bg-yellow-100 text-yellow-800", icon: Clock },
      PROCESSING: { color: "bg-blue-100 text-blue-800", icon: Timer },
      COMPLETED: {
        color: "bg-green-100 text-green-800",
        icon: CheckCircle2,
      },
      CANCELLED: { color: "bg-red-100 text-red-800", icon: XCircle },
    };
    const config = statusConfig[status] || defaultConfig;
    const Icon = config.icon;
    return (
      <Badge className={`${config.color} gap-1`}>
        <Icon className="h-3 w-3" />
        {status.replace("_", " ")}
      </Badge>
    );
  };

  // Helper function for priority badges
  const getPriorityBadge = (priority: TaskWithAssignee["priority"]) => {
    const priorityConfig = {
      LOW: { color: "bg-green-100 text-green-800" },
      NORMAL: { color: "bg-blue-100 text-blue-800" },
      HIGH: { color: "bg-orange-100 text-orange-800" },
      URGENT: { color: "bg-red-100 text-red-800" },
    };
    const priorityValue = priority || "NORMAL";
    const config = priorityConfig[priorityValue];
    return (
      <Badge className={`${config.color} gap-1`}>
        <Flag className="h-3 w-3" />
        {priorityValue}
      </Badge>
    );
  };

  return [
    {
      key: "name",
      label: "Task Name",
      render: (task: TaskWithAssignee) => (
        <div>
          <div className="font-medium text-gray-900">{task.name}</div>
          <div className="text-sm text-gray-500">
            {task.category || "No category"}
          </div>
        </div>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (task: TaskWithAssignee) => getStatusBadge(task.status),
    },
    {
      key: "priority",
      label: "Priority",
      render: (task: TaskWithAssignee) => getPriorityBadge(task.priority),
    },
    {
      key: "assignee",
      label: "Assignee",
      render: (task: TaskWithAssignee) => (
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
            <User className="h-4 w-4 text-primary" />
          </div>
          <div>
            <div className="font-medium text-sm">
              {task.assignee_details?.user?.name ||
                task.assignee_details?.email ||
                "Unassigned"}
            </div>
            {task.assignee_details?.email && (
              <div className="text-xs text-gray-500">
                {task.assignee_details.email}
              </div>
            )}
          </div>
        </div>
      ),
    },
    {
      key: "due",
      label: "Due Date",
      render: (task: TaskWithAssignee) => (
        <div className="text-sm">
          {task.due ? (
            <>
              <div className="font-medium">
                {new Date(task.due).toLocaleDateString()}
              </div>
              <div
                className={`text-xs ${
                  new Date(task.due) < new Date() && task.status !== "COMPLETED"
                    ? "text-red-500"
                    : "text-gray-500"
                }`}
              >
                {new Date(task.due) < new Date() && task.status !== "COMPLETED"
                  ? "Overdue"
                  : `${Math.ceil((new Date(task.due).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} days left`}
              </div>
            </>
          ) : (
            <span className="text-gray-400">No due date</span>
          )}
        </div>
      ),
    },
    {
      key: "actions",
      label: "Actions",
      className: "text-right",
      render: (task: TaskWithAssignee) => (
        <div className="flex justify-end">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onViewTask(task)}>
                <Eye className="h-4 w-4 mr-2" />
                View
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onEditTask(task)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onDeleteTask(task)}
                className="text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
    },
  ];
}
