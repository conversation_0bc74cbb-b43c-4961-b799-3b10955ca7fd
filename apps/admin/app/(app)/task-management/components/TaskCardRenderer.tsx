"use client";

import { memo } from "react";
import { CheckSquare, Clock, Flag } from "lucide-react";
import { Badge } from "@workspace/ui/components/badge";
import { Listing } from "@/modules/listing";
import { type TaskWithAssignee } from "@/lib/logistics";

interface TaskCardRendererProps {
  tasks: TaskWithAssignee[];
  loading: boolean;
  emptyState: React.ReactNode;
  taskStatuses: Array<{ key: string; label: string; count: number }>;
  onViewTask: (task: TaskWithAssignee) => void;
}

/**
 * Card renderer for tasks
 * 
 * Renders tasks in a card layout with category grouping.
 * Optimized with memoization to prevent unnecessary re-renders.
 */
export const TaskCardRenderer = memo<TaskCardRendererProps>(({
  tasks,
  loading,
  emptyState,
  taskStatuses,
  onViewTask,
}) => {
  // Helper functions for task styling
  const getTaskStatusColor = (status: string): string => {
    const colors: Record<string, string> = {
      CREATED: "#6b7280",
      PENDING: "#f59e0b",
      PROCESSING: "#3b82f6",
      COMPLETED: "#10b981",
      CANCELLED: "#ef4444",
    };
    return colors[status] || "#6b7280";
  };

  const getTaskStatusIcon = (status: string) => {
    const icons: Record<string, any> = {
      CREATED: Clock,
      PENDING: Clock,
      PROCESSING: Clock,
      COMPLETED: CheckSquare,
      CANCELLED: CheckSquare,
    };
    return icons[status] || Clock;
  };

  return (
    <Listing.Cards
      data={tasks}
      loading={loading}
      emptyState={emptyState}
      columns="grid-cols-3"
      groupByCategory={true}
      categories={taskStatuses.map((status) => ({
        key: status.key,
        name: status.label,
        description: `${status.count} tasks`,
        color: getTaskStatusColor(status.key.toUpperCase()),
        icon: getTaskStatusIcon(status.key.toUpperCase()),
      }))}
      getCategoryKey={(task) => task.status.toLowerCase()}
      renderCard={(task: TaskWithAssignee, category) => (
        <div className="p-4 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center gap-2">
              <div
                className="p-1.5 rounded-md"
                style={{
                  backgroundColor: category?.color
                    ? `${category.color}20`
                    : "#f3f4f6",
                  color: category?.color || "#6b7280",
                }}
              >
                {category?.icon ? (
                  <category.icon className="h-3 w-3" />
                ) : (
                  <CheckSquare className="h-3 w-3" />
                )}
              </div>
              <h5 className="font-medium text-gray-900 truncate">
                {task.name.length > 35
                  ? `${task.name.substring(0, 35)}...`
                  : task.name}
              </h5>
            </div>
            <Badge className="bg-blue-100 text-blue-800 gap-1">
              <Clock className="h-3 w-3" />
              {task.status.replace("_", " ")}
            </Badge>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-500">Priority</span>
              <Badge className="bg-orange-100 text-orange-800 gap-1">
                <Flag className="h-3 w-3" />
                {task.priority || "NORMAL"}
              </Badge>
            </div>

            <div className="flex justify-between text-sm">
              <span className="text-gray-500">Assignee</span>
              <span className="text-gray-900 font-medium">
                {task.assignee_details?.user?.name ||
                  task.assignee_details?.email ||
                  "Unassigned"}
              </span>
            </div>

            <div className="flex flex-row justify-between gap-1">
              <span className="text-xs text-gray-500">
                {task.due
                  ? new Date(task.due).toLocaleDateString()
                  : "No due date"}
              </span>
              <small className="text-gray-500 text-xs bg-gray-100 px-2 py-0.5 rounded-md">
                {task.category || "No category"}
              </small>
            </div>
          </div>
        </div>
      )}
      onItemClick={(task) => onViewTask(task)}
    />
  );
});

TaskCardRenderer.displayName = "TaskCardRenderer";
