"use client";

import { memo } from "react";
import { User, User<PERSON>he<PERSON> } from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON>Trigger,
} from "@workspace/ui/components/tabs";
import { type ColumnFilter } from "@/components/ui/filter-panel";
import { type TaskWithAssignee } from "@/lib/logistics";
import { TasksList } from "./TasksList";

interface TaskTabsProps {
  activeTab: "assigned-to-me" | "assigned-by-me";
  viewMode: "cards" | "table";
  searchTerm: string;
  categoryFilter: string;
  currentPage: number;
  columnFilters: ColumnFilter[];
  loading: boolean;
  filteredTasksAssignedToMe: TaskWithAssignee[];
  filteredTasksAssignedByMe: TaskWithAssignee[];
  onTabChange: (tab: "assigned-to-me" | "assigned-by-me") => void;
  onViewModeChange: (mode: "cards" | "table") => void;
  onSearchChange: (term: string) => void;
  onCategoryFilterChange: (filter: string) => void;
  onPageChange: (page: number) => void;
  onColumnFiltersChange: (filters: ColumnFilter[]) => void;
  onRefresh: () => void;
  onTaskAction: (action: string, task?: TaskWithAssignee) => void;
  // Checkbox support
  selectedTasks: Set<string>;
  setSelectedTasks: (tasks: Set<string>) => void;
  // Bulk actions
  onBulkStatusUpdate: (status: string) => void;
  onBulkDelete: () => void;
  onClearSelections?: () => void;
}

/**
 * Tabs component for Task Management
 *
 * Handles the tabbed interface for "Assigned to Me" and "Assigned by Me" views.
 * Each tab contains a TasksList component with appropriate filtering.
 */
export const TaskTabs = memo<TaskTabsProps>(
  ({
    activeTab,
    viewMode,
    searchTerm,
    categoryFilter,
    currentPage,
    columnFilters,
    loading,
    filteredTasksAssignedToMe,
    filteredTasksAssignedByMe,
    onTabChange,
    onViewModeChange,
    onSearchChange,
    onCategoryFilterChange,
    onPageChange,
    onColumnFiltersChange,
    onRefresh,
    onTaskAction,
    selectedTasks,
    setSelectedTasks,
    onBulkStatusUpdate,
    onBulkDelete,
    onClearSelections,
  }) => {
    const itemsPerPage = 10;

    const assignedToMeCount = filteredTasksAssignedToMe.length;
    const assignedByMeCount = filteredTasksAssignedByMe.length;

    return (
      <Tabs value={activeTab} onValueChange={onTabChange} className="w-full">
        <TabsList className="grid w-max grid-cols-2">
          <TabsTrigger
            value="assigned-to-me"
            className="flex items-center gap-2"
          >
            <User className="h-4 w-4" />
            Assigned to Me ({assignedToMeCount})
          </TabsTrigger>
          <TabsTrigger
            value="assigned-by-me"
            className="flex items-center gap-2"
          >
            <UserCheck className="h-4 w-4" />
            Assigned by Me ({assignedByMeCount})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="assigned-to-me" className="mt-6">
          <TasksList
            viewMode={viewMode}
            setViewMode={onViewModeChange}
            searchTerm={searchTerm}
            setSearchTerm={onSearchChange}
            categoryFilter={categoryFilter}
            setCategoryFilter={onCategoryFilterChange}
            currentPage={currentPage}
            setCurrentPage={onPageChange}
            itemsPerPage={itemsPerPage}
            loading={loading}
            onRefresh={onRefresh}
            columnFilters={columnFilters}
            setColumnFilters={onColumnFiltersChange}
            tasks={filteredTasksAssignedToMe}
            emptyMessage={
              searchTerm ||
              (Array.isArray(columnFilters) && columnFilters.length > 0)
                ? "No tasks match your filters"
                : "No tasks assigned to you yet"
            }
            onViewTask={(task) => onTaskAction("view", task)}
            onEditTask={(task) => onTaskAction("edit", task)}
            onDeleteTask={(task) => onTaskAction("delete", task)}
            onCreateTask={() => onTaskAction("create")}
            selectedTasks={selectedTasks}
            setSelectedTasks={setSelectedTasks}
            onBulkStatusUpdate={onBulkStatusUpdate}
            onBulkDelete={onBulkDelete}
            onClearSelections={onClearSelections}
          />
        </TabsContent>

        <TabsContent value="assigned-by-me" className="mt-6">
          <TasksList
            viewMode={viewMode}
            setViewMode={onViewModeChange}
            searchTerm={searchTerm}
            setSearchTerm={onSearchChange}
            categoryFilter={categoryFilter}
            setCategoryFilter={onCategoryFilterChange}
            currentPage={currentPage}
            setCurrentPage={onPageChange}
            itemsPerPage={itemsPerPage}
            loading={loading}
            onRefresh={onRefresh}
            columnFilters={columnFilters}
            setColumnFilters={onColumnFiltersChange}
            tasks={filteredTasksAssignedByMe}
            emptyMessage={
              searchTerm ||
              (Array.isArray(columnFilters) && columnFilters.length > 0)
                ? "No tasks match your filters"
                : "You haven't assigned any tasks yet"
            }
            onViewTask={(task) => onTaskAction("view", task)}
            onEditTask={(task) => onTaskAction("edit", task)}
            onDeleteTask={(task) => onTaskAction("delete", task)}
            onCreateTask={() => onTaskAction("create")}
            selectedTasks={selectedTasks}
            setSelectedTasks={setSelectedTasks}
            onBulkStatusUpdate={onBulkStatusUpdate}
            onBulkDelete={onBulkDelete}
            onClearSelections={onClearSelections}
          />
        </TabsContent>
      </Tabs>
    );
  }
);

TaskTabs.displayName = "TaskTabs";
