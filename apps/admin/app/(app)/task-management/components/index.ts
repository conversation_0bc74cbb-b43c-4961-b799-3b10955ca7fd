/**
 * Task Management Components
 * 
 * Optimized component exports for the task management module.
 * All components are memoized for performance and follow React best practices.
 */

export { TaskManagementContainer } from "./TaskManagementContainer";
export { TaskManagementHeader } from "./TaskManagementHeader";
export { TaskStatistics } from "./TaskStatistics";
export { TaskTabs } from "./TaskTabs";
export { TasksList } from "./TasksList";
export { TaskTableColumns } from "./TaskTableColumns";
export { TaskCardRenderer } from "./TaskCardRenderer";
export { TaskDialogs } from "./TaskDialogs";

// Re-export types
export type { TaskStats, TaskManagementState } from "./TaskManagementContainer";
