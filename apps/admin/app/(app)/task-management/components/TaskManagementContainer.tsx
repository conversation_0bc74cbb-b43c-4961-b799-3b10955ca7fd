"use client";

import { useRB<PERSON> } from "@/lib/hooks/useRBAC";
import { useTaskManagement } from "../hooks/useTaskManagement";
import { type TaskWithAssignee } from "@/lib/logistics";
import { type ColumnFilter } from "@/components/ui/filter-panel";

// Components
import { TaskManagementHeader } from "./TaskManagementHeader";
import { TaskStatistics } from "./TaskStatistics";
import { TaskTabs } from "./TaskTabs";
import { TaskDialogs } from "./TaskDialogs";

// Types
export interface TaskStats {
  totalTasks: number;
  completedTasks: number;
  inProgressTasks: number;
  overdueTasks: number;
  completionRate: number;
  averageCompletionTime: number;
}

export interface TaskManagementState {
  // View state
  viewMode: "cards" | "table";
  searchTerm: string;
  categoryFilter: string;
  currentPage: number;
  loading: boolean;
  refreshing: boolean;

  // Data state
  tasks: TaskWithAssignee[];
  taskStats: TaskStats;

  // Dialog state
  isCreateDialogOpen: boolean;
  isEditDialogOpen: boolean;
  isViewDialogOpen: boolean;
  isDeleteDialogOpen: boolean;
  selectedTask: TaskWithAssignee | null;

  // Filter state
  columnFilters: ColumnFilter[];
  activeTab: "assigned-to-me" | "assigned-by-me";
  showNotificationTester: boolean;

  // Checkbox state
  selectedTasks: Set<string>;
}

/**
 * Main container component for Task Management
 *
 * This component manages all the state and business logic for the task management page.
 * It follows the container/presenter pattern for better separation of concerns.
 */
export function TaskManagementContainer() {
  const { shouldShowCreateButton } = useRBAC();

  // Use custom hook for all task management logic
  const {
    state,
    filteredTasksAssignedToMe,
    filteredTasksAssignedByMe,
    handleRefresh,
    handleTaskAction,
    handleDialogClose,
    handleTaskMutation,
    handleBulkStatusUpdate,
    handleBulkDelete,
    handleClearSelections,
    updateState,
  } = useTaskManagement();

  return (
    <div className="p-6 space-y-8">
      <TaskManagementHeader
        loading={state.loading}
        showNotificationTester={state.showNotificationTester}
        shouldShowCreateButton={shouldShowCreateButton}
        onRefresh={handleRefresh}
        onToggleNotificationTester={() =>
          updateState({ showNotificationTester: !state.showNotificationTester })
        }
        onCreateTask={() => handleTaskAction("create")}
      />
      <div className="space-y-8">
        <TaskStatistics
          taskStats={state.taskStats}
          tasks={state.tasks}
          loading={state.loading}
        />
        <TaskTabs
          activeTab={state.activeTab}
          viewMode={state.viewMode}
          searchTerm={state.searchTerm}
          categoryFilter={state.categoryFilter}
          currentPage={state.currentPage}
          columnFilters={state.columnFilters}
          loading={state.loading}
          filteredTasksAssignedToMe={filteredTasksAssignedToMe}
          filteredTasksAssignedByMe={filteredTasksAssignedByMe}
          onTabChange={(tab: "assigned-to-me" | "assigned-by-me") =>
            updateState({ activeTab: tab })
          }
          onViewModeChange={(mode: "cards" | "table") =>
            updateState({ viewMode: mode })
          }
          onSearchChange={(term: string) => updateState({ searchTerm: term })}
          onCategoryFilterChange={(filter: string) =>
            updateState({ categoryFilter: filter })
          }
          onPageChange={(page: number) => updateState({ currentPage: page })}
          onColumnFiltersChange={(filters: ColumnFilter[]) =>
            updateState({ columnFilters: filters })
          }
          onRefresh={handleRefresh}
          onTaskAction={handleTaskAction}
          selectedTasks={state.selectedTasks}
          setSelectedTasks={(tasks: Set<string>) =>
            updateState({ selectedTasks: tasks })
          }
          onBulkStatusUpdate={handleBulkStatusUpdate}
          onBulkDelete={handleBulkDelete}
          onClearSelections={handleClearSelections}
        />
      </div>

      <TaskDialogs
        state={state}
        onClose={handleDialogClose}
        onTaskMutation={handleTaskMutation}
      />
    </div>
  );
}
