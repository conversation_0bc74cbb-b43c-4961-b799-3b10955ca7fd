"use client";

import { memo, useMemo, useCallback } from "react";
import { Dispatch, SetStateAction } from "react";
import { CheckSquare, Plus, Download, Trash2 } from "lucide-react";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import {
  BulkStatusSelector,
  TASK_STATUS_OPTIONS,
} from "@/components/ui/bulk-status-selector";
import { Listing } from "@/modules/listing";
import { type ColumnFilter } from "@/components/ui/filter-panel";
import { type TaskWithAssignee } from "@/lib/logistics";
import { TaskTableColumns } from "./TaskTableColumns";
import { TaskCardRenderer } from "./TaskCardRenderer";

interface TasksListProps {
  viewMode: "cards" | "table";
  setViewMode: (mode: "cards" | "table") => void;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  categoryFilter: string;
  setCategoryFilter: (category: string) => void;
  currentPage: number;
  setCurrentPage: (page: number) => void;
  itemsPerPage: number;
  loading: boolean;
  onRefresh: () => void;
  columnFilters: ColumnFilter[];
  setColumnFilters: Dispatch<SetStateAction<ColumnFilter[]>>;
  tasks: TaskWithAssignee[];
  emptyMessage: string;
  onViewTask: (task: TaskWithAssignee) => void;
  onEditTask: (task: TaskWithAssignee) => void;
  onDeleteTask: (task: TaskWithAssignee) => void;
  onCreateTask: () => void;
  // Checkbox support
  selectedTasks: Set<string>;
  setSelectedTasks: (tasks: Set<string>) => void;
  // Bulk actions
  onBulkStatusUpdate: (status: string) => void;
  onBulkDelete: () => void;
  onClearSelections?: () => void;
}

/**
 * Tasks list component with filtering, search, and view mode switching
 *
 * Supports both table and card views with comprehensive filtering capabilities.
 * Memoized for performance optimization.
 */
export const TasksList = memo<TasksListProps>(
  ({
    viewMode,
    setViewMode,
    searchTerm,
    setSearchTerm,
    categoryFilter,
    setCategoryFilter,
    currentPage,
    setCurrentPage,
    itemsPerPage,
    loading,
    onRefresh,
    columnFilters,
    setColumnFilters,
    tasks,
    emptyMessage,
    onViewTask,
    onEditTask,
    onDeleteTask,
    onCreateTask,
    selectedTasks,
    setSelectedTasks,
    onBulkStatusUpdate,
    onBulkDelete,
    onClearSelections,
  }) => {
    // Render bulk actions
    const renderBulkActions = useCallback(
      () => (
        <>
          <BulkStatusSelector
            statusOptions={TASK_STATUS_OPTIONS}
            onStatusUpdate={onBulkStatusUpdate}
            placeholder="Mark as..."
            onAfterUpdate={onRefresh}
          />
          <Button
            variant="destructive"
            size="sm"
            onClick={onBulkDelete}
            className="gap-2"
          >
            <Trash2 size={16} />
            Delete
          </Button>
        </>
      ),
      [onBulkStatusUpdate, onBulkDelete, onRefresh]
    );

    // Filter data based on search, category, and column filters
    const filteredTasks = useMemo(() => {
      return tasks.filter((task) => {
        const assigneeName =
          task.assignee_details?.user?.name ||
          task.assignee_details?.email ||
          "";
        const categoryName = task.category || "";
        const dueDate = task.due
          ? new Date(task.due).toISOString().split("T")[0]
          : "";

        const matchesSearch =
          !searchTerm ||
          task.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          assigneeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          categoryName.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesStatus =
          categoryFilter === "all" ||
          task.status.toLowerCase() === categoryFilter;

        // Apply column filters (with safety check and improved logic)
        const matchesColumnFilters = Array.isArray(columnFilters)
          ? columnFilters.every((filter) => {
              let value: any;

              // Handle nested properties for specific columns
              switch (filter.column) {
                case "assignee":
                  value =
                    task.assignee_details?.user?.name ||
                    task.assignee_details?.email ||
                    "";
                  break;
                case "category":
                  value = task.category || "";
                  break;
                case "due":
                  value = task.due
                    ? new Date(task.due).toISOString().split("T")[0]
                    : "";
                  break;
                default:
                  value = (task as any)[filter.column];
              }

              if (value === null || value === undefined) return false;

              // Handle different data types
              if (typeof value === "string") {
                return value.toLowerCase().includes(filter.value.toLowerCase());
              } else if (typeof value === "number") {
                return value.toString() === filter.value;
              } else if (value instanceof Date) {
                const dateStr = value.toISOString().split("T")[0];
                return dateStr === filter.value;
              } else {
                return value
                  .toString()
                  .toLowerCase()
                  .includes(filter.value.toLowerCase());
              }
            })
          : true; // If columnFilters is not an array, don't filter

        return matchesSearch && matchesStatus && matchesColumnFilters;
      });
    }, [tasks, searchTerm, categoryFilter, columnFilters]);

    const totalItems = filteredTasks.length;
    const totalPages = Math.ceil(totalItems / itemsPerPage);

    // Handle column filter changes
    const handleColumnFilterAdd = useCallback(
      (filter: ColumnFilter) => {
        setColumnFilters((prev) => [...prev, filter]);
      },
      [setColumnFilters]
    );

    const handleColumnFilterRemove = useCallback(
      (index: number) => {
        setColumnFilters((prev) => prev.filter((_, i) => i !== index));
      },
      [setColumnFilters]
    );

    // Define task statuses for filtering
    const taskStatuses = useMemo(
      () => [
        { key: "created", label: "Created", count: 0 },
        { key: "pending", label: "Pending", count: 0 },
        { key: "processing", label: "Processing", count: 0 },
        { key: "completed", label: "Completed", count: 0 },
        { key: "cancelled", label: "Cancelled", count: 0 },
      ],
      []
    );

    // Define table columns for tasks
    const taskTableColumns = useMemo(
      () => [
        {
          key: "name",
          label: "Name",
          type: "string" as const,
          searchable: true,
        },
        {
          key: "status",
          label: "Status",
          type: "enum" as const,
          searchable: true,
        },
        {
          key: "priority",
          label: "Priority",
          type: "enum" as const,
          searchable: true,
        },
        {
          key: "assignee",
          label: "Assignee",
          type: "string" as const,
          searchable: true,
        },
        {
          key: "category",
          label: "Category",
          type: "string" as const,
          searchable: true,
        },
        {
          key: "due",
          label: "Due Date",
          type: "date" as const,
          searchable: true,
        },
      ],
      []
    );

    // Empty state component
    const emptyState = useMemo(
      () => (
        <div className="text-center py-12">
          <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-3">
            <CheckSquare className="h-6 w-6 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-1">
            No tasks found
          </h3>
          <p className="text-gray-500 mb-4">{emptyMessage}</p>
          <Button className="gap-2" onClick={onCreateTask}>
            <Plus className="h-4 w-4" />
            Add Task
          </Button>
        </div>
      ),
      [emptyMessage, onCreateTask]
    );

    return (
      <Listing className="space-y-6">
        <Listing.Filters
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          onRefresh={onRefresh}
          loading={loading}
          columnFilters={columnFilters}
          onColumnFilterAdd={handleColumnFilterAdd}
          onColumnFilterRemove={handleColumnFilterRemove}
          enableDynamicFilters={true}
          columns={taskTableColumns}
          tableData={filteredTasks}
          defaultFilterColumn="name"
          autoSelectDefaultColumn={true}
          bulkActions={renderBulkActions()}
          selectedCount={selectedTasks.size}
          showBulkActions={true}
          onClearSelections={onClearSelections}
        />

        <Listing.Controls
          entity="tasks"
          length={filteredTasks.length}
          viewMode={viewMode}
          onViewModeChange={setViewMode}
          categoryFilter={categoryFilter}
          onCategoryFilterChange={setCategoryFilter}
          categories={taskStatuses}
          actions={
            <div>
              <Button variant="outline" size="sm" className="mr-2">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button size="sm" onClick={onCreateTask}>
                <Plus className="h-4 w-4 mr-2" />
                New Task
              </Button>
            </div>
          }
        />

        {viewMode === "cards" ? (
          <TaskCardRenderer
            tasks={filteredTasks}
            loading={loading}
            emptyState={emptyState}
            taskStatuses={taskStatuses}
            onViewTask={onViewTask}
          />
        ) : (
          <Listing.Table
            data={filteredTasks}
            columns={TaskTableColumns({ onViewTask, onEditTask, onDeleteTask })}
            loading={loading}
            enableCheckboxes={true}
            selectedRowIds={Array.from(selectedTasks)}
            onSelectionChange={(selectedIds) =>
              setSelectedTasks(new Set(selectedIds))
            }
            getRowId={(item) => item.id}
            emptyState={emptyState}
            pagination={{
              currentPage,
              totalPages,
              totalItems,
              itemsPerPage,
              onPageChange: setCurrentPage,
            }}
          />
        )}
      </Listing>
    );
  }
);

TasksList.displayName = "TasksList";
