"use client";

import { memo } from "react";
import { withRB<PERSON> } from "@/lib/components/RBACWrapper";
import { TaskManagementContainer } from "./components/TaskManagementContainer";

/**
 * Task Management Page Component
 *
 * This is the main page component for task management.
 * It's wrapped with RBAC for permission control and uses a container component
 * for better separation of concerns and performance optimization.
 */
const TaskManagementPage = memo(() => {
  return <TaskManagementContainer />;
});

TaskManagementPage.displayName = "TaskManagementPage";

export default withRBAC(
  TaskManagementPage,
  "tasks", // Entity
  "view", // Required action
  <div className="p-6 text-center">
    <h1 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h1>
    <p className="text-gray-500">
      You don't have permission to view task management.
    </p>
  </div> // Fallback content
);
