"use client";

import { useState, useEffect, useMemo, useCallback } from "react";
import { useAppSelector } from "@/store/hooks";
import { taskService, type TaskWithAssignee } from "@/lib/logistics";
import { taskDeadlineService } from "@/lib/services/taskDeadlineService";
import { toast } from "sonner";

import {
  type TaskStats,
  type TaskManagementState,
} from "../components/TaskManagementContainer";

/**
 * Custom hook for task management logic
 *
 * Encapsulates all business logic, state management, and side effects
 * for the task management feature. Follows React best practices with
 * proper memoization and optimization.
 */
export function useTaskManagement() {
  const { user: authUser } = useAppSelector((state) => state.auth);

  // State management
  const [state, setState] = useState<TaskManagementState>({
    viewMode: "table",
    searchTerm: "",
    categoryFilter: "all",
    currentPage: 1,
    loading: true,
    tasks: [],
    taskStats: {
      totalTasks: 0,
      completedTasks: 0,
      inProgressTasks: 0,
      overdueTasks: 0,
      completionRate: 0,
      averageCompletionTime: 0,
    },
    isCreateDialogOpen: false,
    isEditDialogOpen: false,
    isViewDialogOpen: false,
    isDeleteDialogOpen: false,
    selectedTask: null,
    columnFilters: [],
    activeTab: "assigned-to-me",
    showNotificationTester: false,
    selectedTasks: new Set<string>(),
    refreshing: false,
  });

  // Memoized calculations
  const calculateTaskStats = useCallback(
    (taskList: TaskWithAssignee[]): TaskStats => {
      const totalTasks = taskList.length;
      const completedTasks = taskList.filter(
        (t) => t.status === "COMPLETED"
      ).length;
      const inProgressTasks = taskList.filter((t) =>
        ["PROCESSING", "PENDING"].includes(t.status)
      ).length;
      const overdueTasks = taskList.filter(
        (t) => t.due && new Date(t.due) < new Date() && t.status !== "COMPLETED"
      ).length;
      const completionRate =
        totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;
      const averageCompletionTime = 0; // TODO: Calculate from actual completion times

      return {
        totalTasks,
        completedTasks,
        inProgressTasks,
        overdueTasks,
        completionRate,
        averageCompletionTime,
      };
    },
    []
  );

  // Filter tasks based on user relationship
  const tasksAssignedToMe = useMemo(() => {
    return state.tasks.filter((task) => task.assignee === authUser?.accountId);
  }, [state.tasks, authUser?.accountId]);

  const tasksAssignedByMe = useMemo(() => {
    return state.tasks.filter(
      (task) => task.account_id === authUser?.accountId
    );
  }, [state.tasks, authUser?.accountId]);

  // Return unfiltered tasks - filtering will be handled in the component
  const filteredTasksAssignedToMe = tasksAssignedToMe;
  const filteredTasksAssignedByMe = tasksAssignedByMe;

  // Data fetching
  const fetchTaskData = useCallback(
    async (refresh = false) => {
      if (!authUser) return;

      try {
        if (refresh) setState((prev) => ({ ...prev, refreshing: true }));
        else setState((prev) => ({ ...prev, loading: true }));

        const tasksResult = await taskService.getAllTasksWithAssignees({
          limit: 100,
        });

        if (tasksResult.success && tasksResult.data) {
          const stats = calculateTaskStats(tasksResult.data);
          setState((prev) => ({
            ...prev,
            tasks: tasksResult.data!,
            taskStats: stats,
          }));
        } else {
          console.error("Failed to fetch tasks:", tasksResult.error);
          setState((prev) => ({ ...prev, tasks: [] }));
        }
      } catch (error) {
        console.error("Error fetching task data:", error);
        setState((prev) => ({ ...prev, tasks: [] }));
      } finally {
        setState((prev) => ({ ...prev, loading: false, refreshing: false }));
      }
    },
    [authUser, calculateTaskStats]
  );

  // Effects
  useEffect(() => {
    if (authUser) {
      fetchTaskData();
    } else {
      setState((prev) => ({ ...prev, loading: false }));
    }
  }, [authUser, fetchTaskData]);

  // Set up deadline service
  useEffect(() => {
    if (!authUser) return;

    if (!taskDeadlineService.getStatus().isRunning) {
      taskDeadlineService.start();
    }
  }, [authUser]);

  // Recalculate stats when filtered tasks change
  useEffect(() => {
    const currentTasks =
      state.activeTab === "assigned-to-me"
        ? filteredTasksAssignedToMe
        : filteredTasksAssignedByMe;
    const stats = calculateTaskStats(currentTasks);
    setState((prev) => ({ ...prev, taskStats: stats }));
  }, [
    filteredTasksAssignedToMe,
    filteredTasksAssignedByMe,
    state.activeTab,
    calculateTaskStats,
  ]);

  // Event handlers
  const handleRefresh = useCallback(() => {
    fetchTaskData(true);
  }, [fetchTaskData]);

  const handleTaskAction = useCallback(
    (action: string, task?: TaskWithAssignee) => {
      switch (action) {
        case "create":
          setState((prev) => ({ ...prev, isCreateDialogOpen: true }));
          break;
        case "view":
          setState((prev) => ({
            ...prev,
            selectedTask: task!,
            isViewDialogOpen: true,
          }));
          break;
        case "edit":
          setState((prev) => ({
            ...prev,
            selectedTask: task!,
            isEditDialogOpen: true,
          }));
          break;
        case "delete":
          setState((prev) => ({
            ...prev,
            selectedTask: task!,
            isDeleteDialogOpen: true,
          }));
          break;
      }
    },
    []
  );

  const handleDialogClose = useCallback((dialogType: string) => {
    setState((prev) => ({
      ...prev,
      [`is${dialogType}DialogOpen`]: false,
      selectedTask: null,
    }));
  }, []);

  const handleTaskMutation = useCallback(() => {
    fetchTaskData(true);
  }, [fetchTaskData]);

  // Bulk actions handlers
  const handleBulkStatusUpdate = useCallback(
    async (status: string) => {
      const selectedIds = Array.from(state.selectedTasks);
      if (selectedIds.length === 0) {
        console.warn("No tasks selected for bulk status update");
        return;
      }

      try {
        // Update each selected task's status
        const updatePromises = selectedIds.map((taskId) =>
          taskService.update(taskId, { status: status as any })
        );

        const updateResults = await Promise.all(updatePromises);
        const successfulUpdates = updateResults.filter(
          (result) => result.success
        ).length;
        const failedUpdates = updateResults.length - successfulUpdates;

        if (successfulUpdates > 0) {
          console.log(
            `Successfully updated ${successfulUpdates} task(s) to ${status}`,
            failedUpdates > 0 ? `${failedUpdates} update(s) failed` : ""
          );

          // Clear selection after successful action
          setState((prev) => ({ ...prev, selectedTasks: new Set() }));

          // Refresh data to show updated statuses
          fetchTaskData(true);
        } else {
          console.error("Failed to update any task status");
        }
      } catch (error) {
        console.error("Error in bulk status update:", error);
      }
    },
    [state.selectedTasks, fetchTaskData]
  );

  const handleBulkDelete = useCallback(async () => {
    const selectedIds = Array.from(state.selectedTasks);
    if (selectedIds.length === 0) {
      console.warn("No tasks selected for bulk delete");
      return;
    }

    try {
      // Delete each selected task
      const deletePromises = selectedIds.map((taskId) =>
        taskService.delete(taskId)
      );

      const deleteResults = await Promise.all(deletePromises);
      const successfulDeletes = deleteResults.filter(
        (result) => result.success
      ).length;
      const failedDeletes = deleteResults.length - successfulDeletes;

      if (successfulDeletes > 0) {
        console.log(
          `Successfully deleted ${successfulDeletes} task(s)`,
          failedDeletes > 0 ? `${failedDeletes} delete(s) failed` : ""
        );

        // Clear selection after successful action
        setState((prev) => ({ ...prev, selectedTasks: new Set() }));

        // Refresh data to show updated list
        fetchTaskData(true);
      } else {
        console.error("Failed to delete any tasks");
      }
    } catch (error) {
      console.error("Error in bulk delete:", error);
    }
  }, [state.selectedTasks, fetchTaskData]);

  // State update handlers
  const updateState = useCallback((updates: Partial<TaskManagementState>) => {
    setState((prev) => ({ ...prev, ...updates }));
  }, []);

  // Clear selections handler
  const handleClearSelections = useCallback(() => {
    updateState({ selectedTasks: new Set() });
    toast.success("Task selections cleared");
  }, [updateState]);

  return {
    state,
    filteredTasksAssignedToMe,
    filteredTasksAssignedByMe,
    handleRefresh,
    handleTaskAction,
    handleDialogClose,
    handleTaskMutation,
    handleBulkStatusUpdate,
    handleBulkDelete,
    handleClearSelections,
    updateState,
  };
}
