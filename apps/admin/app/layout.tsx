import { Inter, Headland_One } from "next/font/google";
import { Providers } from "@/components/providers";
import "./global.css";
import { cn } from "@workspace/ui/lib/utils";
import { Metadata } from "next";

const fontInter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

const fontHeadland = Headland_One({
  subsets: ["latin"],
  weight: ["400"],
  variable: "--font-headland",
});

const metadata: Metadata = {
  title: "CRM | Shamwaafrica",
  description:
    "Logistics administration panel for internal operations and staff management.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>{metadata.title}</title>
        <meta name="description" content={metadata.description} />
      </head>
      <body
        className={cn(
          "min-h-screen bg-background-secondary font-sans antialiased",
          fontInter.variable,
          fontHeadland.variable
        )}
      >
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
