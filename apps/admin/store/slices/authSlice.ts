import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import {
  authService,
  AuthUser,
  AuthCredentials,
  RegisterData,
} from "@/lib/auth";
import { permissionService } from "@/lib/logistics/system/permissions";

export interface AuthState {
  user: AuthUser | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isAuthorized: boolean;
  error: string | null;
  session: any | null;
  accountsExist: boolean | null;
}

const initialState: AuthState = {
  user: null,
  isLoading: false,
  isAuthenticated: false,
  isAuthorized: false,
  error: null,
  session: null,
  accountsExist: null,
};

// Async thunks for auth actions
export const signInWithEmailPassword = createAsyncThunk(
  "auth/signInWithEmailPassword",
  async ({ email, password }: AuthCredentials, { rejectWithValue }) => {
    try {
      const result = await authService.signInWithEmailPassword({
        email,
        password,
      });
      if (result.error) {
        return rejectWithValue(result.error);
      }
      return result;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const signUp = createAsyncThunk(
  "auth/signUp",
  async (data: RegisterData, { rejectWithValue }) => {
    try {
      const result = await authService.signUp(data);
      if (result.error) {
        return rejectWithValue(result.error);
      }
      return result;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const signOut = createAsyncThunk(
  "auth/signOut",
  async (_, { rejectWithValue }) => {
    try {
      const result = await authService.signOut();
      if (result.error) {
        return rejectWithValue(result.error);
      }
      return true;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const getCurrentUser = createAsyncThunk(
  "auth/getCurrentUser",
  async (_, { rejectWithValue }) => {
    try {
      const user = await authService.getCurrentUser();
      return user;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const sendPasswordReset = createAsyncThunk(
  "auth/sendPasswordReset",
  async (email: string, { rejectWithValue }) => {
    try {
      const result = await authService.sendPasswordResetEmail(email);
      if (result.error) {
        return rejectWithValue(result.error);
      }
      return {};
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const checkAccountsExist = createAsyncThunk(
  "auth/checkAccountsExist",
  async (_, { rejectWithValue }) => {
    try {
      const exists = await authService.checkIfAnyAccountsExist();
      return exists;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const createFirstAdmin = createAsyncThunk(
  "auth/createFirstAdmin",
  async (
    data: {
      name: string;
      email: string;
      password: string;
      phone?: string;
      location?: string;
    },
    { rejectWithValue }
  ) => {
    try {
      const result = await authService.createFirstAdminAccount(data);
      if (result.error) {
        return rejectWithValue(result.error);
      }
      return result;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const requestAccess = createAsyncThunk(
  "auth/requestAccess",
  async (
    data: { name: string; email: string; message: string },
    { rejectWithValue }
  ) => {
    try {
      const result = await authService.requestAccess(data);
      if (result.error) {
        return rejectWithValue(result.error);
      }
      return {};
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const refreshUserPermissions = createAsyncThunk(
  "auth/refreshUserPermissions",
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { auth: AuthState };
      const user = state.auth.user;

      if (!user?.role?.id) {
        return rejectWithValue("No user role found");
      }

      const result = await permissionService.getPermissionsByRole(user.role.id);
      if (!result.success || !result.data) {
        return rejectWithValue(result.error || "Failed to load permissions");
      }

      // Convert from UI format back to JSONB format
      const permissions: { [entity: string]: string[] } = {};
      Object.entries(result.data.permissions).forEach(([entity, actions]) => {
        const grantedActions: string[] = [];
        Object.entries(actions || {}).forEach(([action, granted]) => {
          if (granted) {
            grantedActions.push(action);
          }
        });
        if (grantedActions.length > 0) {
          permissions[entity] = grantedActions;
        }
      });

      return permissions;
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setUser: (state, action) => {
      state.user = action.payload;
      if (action.payload) {
        state.isAuthenticated = !!action.payload.user;
        state.isAuthorized = !!action.payload.user?.role.permissions;
      }
    },
    setLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    resetAuth: (state) => {
      state.user = null;
      state.isAuthenticated = false;
      state.error = null;
      state.isLoading = false;
      state.session = null;
      state.accountsExist = null;
    },
    setSession: (state, action) => {
      state.session = action.payload;
    },
    updateUserPermissions: (state, action) => {
      if (state.user?.role) {
        state.user.role.permissions = action.payload;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Sign in
      .addCase(signInWithEmailPassword.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(signInWithEmailPassword.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.session = action.payload.session;
        state.isAuthenticated = !!action.payload.user;
        //
        let permissions = action.payload.user?.role?.permissions;
        if (permissions)
          state.isAuthorized = Object.keys(permissions).length > 0;
      })
      .addCase(signInWithEmailPassword.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.user = null;
        state.session = null;
        state.isAuthenticated = false;
        state.isAuthorized = false;
      })
      // Sign up
      .addCase(signUp.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(signUp.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.session = action.payload.session;
        state.isAuthenticated = !!action.payload.user;
        //
        let permissions = action.payload.user?.role?.permissions;
        if (permissions)
          state.isAuthorized = Object.keys(permissions).length > 0;
      })
      .addCase(signUp.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.user = null;
        state.session = null;
        state.isAuthenticated = false;
        state.isAuthorized = false;
      })
      // Sign out
      .addCase(signOut.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(signOut.fulfilled, (state) => {
        state.user = null;
        state.session = null;
        state.isAuthenticated = false;
        state.isAuthorized = false;
        state.isLoading = false;
        state.error = null;
      })
      .addCase(signOut.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Get current user
      .addCase(getCurrentUser.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getCurrentUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
        state.isAuthenticated = !!action.payload;
        //
        let permissions = action.payload?.role?.permissions;
        if (permissions)
          state.isAuthorized = Object.keys(permissions).length > 0;
      })
      .addCase(getCurrentUser.rejected, (state, action) => {
        state.isLoading = false;
        state.user = null;
        state.isAuthenticated = false;
        state.isAuthorized = false;
        state.error = action.payload as string;
      })
      // Password reset
      .addCase(sendPasswordReset.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(sendPasswordReset.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(sendPasswordReset.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Check accounts exist
      .addCase(checkAccountsExist.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(checkAccountsExist.fulfilled, (state, action) => {
        state.isLoading = false;
        state.accountsExist = action.payload;
      })
      .addCase(checkAccountsExist.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.accountsExist = null;
      })
      // Create first admin
      .addCase(createFirstAdmin.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createFirstAdmin.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.session = action.payload.session;
        state.isAuthenticated = !!action.payload.user;
        //
        let permissions = action.payload.user?.role?.permissions;
        if (permissions)
          state.isAuthorized = Object.keys(permissions).length > 0;
        //
        state.accountsExist = true;
      })
      .addCase(createFirstAdmin.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.user = null;
        state.session = null;
        state.isAuthenticated = false;
        state.isAuthorized = false;
      })
      // Request access
      .addCase(requestAccess.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(requestAccess.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(requestAccess.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Refresh user permissions
      .addCase(refreshUserPermissions.pending, (state) => {
        // Don't set loading to true for permission refresh to avoid UI flicker
      })
      .addCase(refreshUserPermissions.fulfilled, (state, action) => {
        if (state.user?.role) {
          state.user.role.permissions = action.payload;
        }
      })
      .addCase(refreshUserPermissions.rejected, (state, action) => {
        console.warn("Failed to refresh permissions:", action.payload);
      });
  },
});

export const {
  setUser,
  setLoading,
  clearError,
  resetAuth,
  setSession,
  updateUserPermissions,
} = authSlice.actions;

export type AuthSliceActions = typeof authSlice.actions;
export default authSlice.reducer;
