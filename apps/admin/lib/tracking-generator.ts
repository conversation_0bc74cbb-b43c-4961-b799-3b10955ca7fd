/**
 * @deprecated This file has been migrated to the centralized code generator service.
 * Please use @/lib/logistics/operations/code-generator instead.
 *
 * This file is kept for backward compatibility only.
 */

// Re-export from the centralized code generator service
export {
  codeGeneratorService,
  type TrackingNumberParams,
  type BatchCodeParams,
  type CargoTrackingParams,
  type InvoiceNumberParams,
  type DocumentNumberParams,
  generateBatchCode,
  generateTrackingNumber,
  validateBatchCode,
  parseTrackingNumber,
  generateSequentialTrackingNumber,
  // New sequential generation functions
  generateSequentialBatchCode,
  generateSequentialCargoTrackingNumber,
  generateSequentialShipmentTrackingNumber,
  generateSequentialInvoiceNumber,
  generateSequentialTrackingNumberSync,
  getLocationCode,
  getWeightCategory,
  LOCATION_CODES,
  SHIPPING_MODES,
  WEIGHT_CATEGORIES,
} from "@/lib/logistics/operations/code-generator";
