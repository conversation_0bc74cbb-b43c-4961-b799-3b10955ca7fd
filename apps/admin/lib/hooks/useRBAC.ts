"use client";

import { useState, useEffect } from "react";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { refreshUserPermissions } from "@/store/slices/authSlice";
import {
  EntityType,
  PermissionAction,
} from "@/lib/logistics/types/permissions";

interface UserPermissions {
  [entity: string]: string[]; // JSONB format: {"users": ["view", "create"]}
}

interface RBACHookReturn {
  // Permission checking functions
  hasPermission: (entity: EntityType, action: PermissionAction) => boolean;
  canView: (entity: EntityType) => boolean;
  canCreate: (entity: EntityType) => boolean;
  canUpdate: (entity: EntityType) => boolean;
  canDelete: (entity: EntityType) => boolean;

  // UI visibility functions
  shouldShowNavRoute: (entity: EntityType) => boolean;
  shouldShowCreateButton: (entity: EntityType) => boolean;
  shouldShowEditButton: (entity: EntityType) => boolean;
  shouldShowDeleteButton: (entity: EntityType) => boolean;

  // Utility functions
  isLoading: boolean;
  userRole: string | null;
  userDepartment: string | null;
  permissions: UserPermissions;
  authorized: boolean;

  // Admin/Manager checks
  isAdmin: () => boolean;
  isManager: () => boolean;

  // Refresh permissions
  refreshPermissions: () => Promise<void>;
}

// Entity to route mapping for navigation visibility
const ENTITY_ROUTE_MAP: Record<EntityType, string[]> = {
  users: ["/manage-staff"],
  accounts: ["/manage-staff"],
  roles: ["/manage-staff"],
  departments: ["/manage-staff"],
  customers: ["/customer-relations"],
  cargo: ["/cargo-management"],
  batches: ["/batch-management", "/cargo-management"],
  freights: ["/batch-management"],
  shipments: ["/shipping-management"],
  handovers: ["/release-authorized"],
  documents: ["/invoice-workflow"],
  notifications: ["/notifications", "/broadcast-messages"],
  logs: ["/analytics"],
  schedules: ["/shipping-management"],
  assignments: ["/task-manager"],
  approvals: ["/task-manager"],
  ledgers: ["/finance"],
  transactions: ["/finance"],
  sessions: ["/manage-staff"],
  recoveries: ["/manage-staff"],
};

export function useRBAC(): RBACHookReturn {
  const [authorized, setAuthorized] = useState<boolean>(false);
  const dispatch = useAppDispatch();
  const {
    user,
    isAuthenticated,
    isAuthorized,
    isLoading: authLoading,
  } = useAppSelector((state) => state.auth);

  // Get permissions from user object in auth slice
  const permissions: UserPermissions = user?.role?.permissions || {};

  // Load permissions when user changes or when permissions are empty
  useEffect(() => {
    if (user?.role?.id && isAuthenticated && !isAuthorized)
      dispatch(refreshUserPermissions());
  }, [isAuthenticated, user?.role?.id, permissions]);

  useEffect(() => {
    let authorized_status: boolean = Object.keys(permissions).length > 0;
    setAuthorized(authorized_status);
  }, [user, isAuthenticated, permissions]);

  // Core permission checking function
  const hasPermission = (
    entity: EntityType,
    action: PermissionAction
  ): boolean => {
    if (!user || !isAuthenticated) return false;

    // Admins have all permissions
    if (isAdmin()) return true;

    const entityPermissions = permissions[entity];
    return entityPermissions ? entityPermissions.includes(action) : false;
  };

  // Specific permission checks
  const canView = (entity: EntityType): boolean =>
    hasPermission(entity, "view");
  const canCreate = (entity: EntityType): boolean =>
    hasPermission(entity, "create");
  const canUpdate = (entity: EntityType): boolean =>
    hasPermission(entity, "update");
  const canDelete = (entity: EntityType): boolean =>
    hasPermission(entity, "delete");

  // UI visibility functions
  const shouldShowNavRoute = (entity: EntityType): boolean => {
    // Hide navigation routes if user doesn't have view permission
    return canView(entity);
  };

  const shouldShowCreateButton = (entity: EntityType): boolean => {
    // Hide "New [Entity]" buttons if user doesn't have create permission
    return canCreate(entity);
  };

  const shouldShowEditButton = (entity: EntityType): boolean => {
    // Hide edit buttons if user doesn't have update permission
    return canUpdate(entity);
  };

  const shouldShowDeleteButton = (entity: EntityType): boolean => {
    // Hide delete buttons if user doesn't have delete permission
    return canDelete(entity);
  };

  // Role-based checks
  const isAdmin = (): boolean => {
    if (!user?.role) return false;
    const roleName = user.role.name.toLowerCase();
    return roleName === "admin" || roleName === "system administrator";
  };

  const isManager = (): boolean => {
    if (!user?.role) return false;
    const roleName = user.role.name.toLowerCase();
    return roleName === "manager" || isAdmin();
  };

  // Refresh permissions function
  const refreshPermissions = async (): Promise<void> => {
    if (user?.role?.id && isAuthenticated) {
      dispatch(refreshUserPermissions());
    }
  };

  return {
    // Permission checking functions
    hasPermission,
    canView,
    canCreate,
    canUpdate,
    canDelete,

    // UI visibility functions
    shouldShowNavRoute,
    shouldShowCreateButton,
    shouldShowEditButton,
    shouldShowDeleteButton,

    // Utility functions
    isLoading:
      authLoading || (isAuthenticated && Object.keys(permissions).length === 0),
    userRole: user?.role?.name || null,
    userDepartment: user?.role?.department?.name || null,
    permissions,
    authorized,

    // Admin/Manager checks
    isAdmin,
    isManager,

    // Refresh permissions
    refreshPermissions,
  };
}

// Helper function to get entity from route path
export function getEntityFromRoute(pathname: string): EntityType | null {
  const routeEntityMap: Record<string, EntityType> = {
    "/manage-staff": "users",
    "/customer-relations": "customers",
    "/cargo-management": "cargo",
    "/batch-management": "batches",
    "/shipping-management": "shipments",
    "/release-authorized": "handovers",
    "/invoice-workflow": "documents",
    "/notifications": "notifications",
    "/broadcast-messages": "notifications",
    "/analytics": "logs",
    "/task-manager": "assignments",
    "/finance": "ledgers",
  };

  // Find matching route
  for (const [route, entity] of Object.entries(routeEntityMap)) {
    if (pathname.startsWith(route)) {
      return entity;
    }
  }

  return null;
}

// Helper function to check if route should be visible
export function shouldShowRoute(
  pathname: string,
  permissions: UserPermissions
): boolean {
  const entity = getEntityFromRoute(pathname);
  if (!entity) return true; // Show routes that don't map to entities

  const entityPermissions = permissions[entity];
  return entityPermissions ? entityPermissions.includes("view") : false;
}
