"use client";

import { useEffect, useState, useCallback, useRef } from "react";
import { redirect } from "next/navigation";
import {
  RealtimeChannel,
  RealtimePostgresChangesPayload,
} from "@supabase/supabase-js";

import { createRealtimeClient, createClient } from "@/lib/supabase/client";
import { useAppSelector } from "@/store/hooks";
import { Database } from "@/lib/types/supabase";
import { toast } from "sonner";

type NotificationRow = Database["public"]["Tables"]["notifications"]["Row"];
type NotificationInsert =
  Database["public"]["Tables"]["notifications"]["Insert"];
type NotificationUpdate =
  Database["public"]["Tables"]["notifications"]["Update"];

interface RealtimeNotificationState {
  notifications: NotificationRow[];
  unreadCount: number;
  isConnected: boolean;
  isLoading: boolean;
  error: string | null;
}

interface UseRealtimeNotificationsReturn extends RealtimeNotificationState {
  markAsRead: (notificationId: string) => void;
  markAsUnread: (notificationId: string) => void;
  markAllAsRead: () => void;
  deleteNotification: (notificationId: string) => void;
  refreshNotifications: () => void;
  subscribe: () => void;
  unsubscribe: () => void;
  playNotificationSound: () => void;
}

const INITIAL_STATE: RealtimeNotificationState = {
  notifications: [],
  unreadCount: 0,
  isConnected: false,
  isLoading: true,
  error: null,
};

export function useRealtimeNotifications(): UseRealtimeNotificationsReturn {
  const channelName: string = "realtime-notifications-channel";

  const realtime = createRealtimeClient();
  const supabase = createClient();

  const { user } = useAppSelector((state) => state.auth);
  const channelRef = useRef<RealtimeChannel>(null);

  const [state, setState] = useState<RealtimeNotificationState>(INITIAL_STATE);

  // Fetch initial notifications
  const fetchNotifications = useCallback(async () => {
    try {
      setState((prev) => ({ ...prev, isLoading: true, error: null }));

      const { data: notifications, error }: any = await supabase
        .from("notifications")
        .select(
          `
          *,
          accounts (
            id,
            email,
            users (
              id,
              name,
              avatar
            )
          )
        `
        )
        .in("status", ["UNREAD", "READ"])
        .in("to", [user?.accountId, "*"])
        .order("created_at", { ascending: false });

      if (error) {
        throw error;
      }

      // Count unread notifications (UNREAD status, excluding INUNREAD)
      const unreadCount = notifications.filter(
        (n: any) => n.status === ("UNREAD" as any)
      ).length;

      setState((prev) => ({
        ...prev,
        notifications,
        unreadCount,
        isLoading: false,
        error: null,
      }));
    } catch (error: any) {
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error: error.message || "Failed to fetch notifications",
      }));
    }
  }, [user?.accountId, supabase]);

  // Play notification sound
  const playNotificationSound = useCallback(() => {
    try {
      const audio = new Audio("/ringtone_antic_ios_17.mp3");
      audio.volume = 0.8; // Set volume to 50%
      audio.play().catch(() => {
        // Silent catch - sound may not play due to browser restrictions
      });
    } catch (error) {
      // Silent catch - audio creation may fail in some environments
    }
  }, []);

  // Handle realtime notification changes
  const handleRealtimeChange = useCallback(
    (payload: RealtimePostgresChangesPayload<NotificationRow>) => {
      const { eventType, new: newRecord, old: oldRecord } = payload;

      setState((prev) => {
        let updatedNotifications = [...prev.notifications];
        let updatedUnreadCount = prev.unreadCount;

        switch (eventType) {
          case "INSERT":
            // Check if notification is for current user
            // Support both direct 'to' field and 'to' field in details (temporary)
            const recordWithTo = newRecord as any;
            const isForCurrentUser =
              recordWithTo?.to === user?.accountId ||
              recordWithTo?.to === "*" || // Global notifications
              (newRecord?.details &&
                typeof newRecord.details === "object" &&
                (newRecord.details as any)?.to === user?.accountId) ||
              newRecord?.account_id === user?.accountId; // Fallback to account_id

            if (newRecord && isForCurrentUser) {
              // Add new notification to the beginning of the list
              updatedNotifications.unshift(newRecord);

              // Increment unread count if it's an UNREAD notification
              if (newRecord.status === "UNREAD") {
                updatedUnreadCount += 1;

                // Show toast notification for new notifications
                toast.info(newRecord.name, {
                  description: newRecord.message,
                  action: {
                    label: "View",
                    onClick: () => {
                      // Navigate to notifications page
                      redirect("/notifications");
                    },
                  },
                });
              }

              // Play notification sound
              fetchNotifications();
              playNotificationSound();
            }
            break;

          case "UPDATE":
            // Check if notification is for current user (same logic as INSERT)
            const updateRecordWithTo = newRecord as any;
            const isUpdateForCurrentUser =
              updateRecordWithTo?.to === user?.accountId ||
              updateRecordWithTo?.to === "*" ||
              (newRecord?.details &&
                typeof newRecord.details === "object" &&
                (newRecord.details as any)?.to === user?.accountId) ||
              newRecord?.account_id === user?.accountId;

            if (newRecord && isUpdateForCurrentUser) {
              const index = updatedNotifications.findIndex(
                (n) => n.id === newRecord.id
              );
              if (index !== -1) {
                const oldNotification = updatedNotifications[index];
                if (oldNotification) {
                  updatedNotifications[index] = newRecord;

                  // Update unread count based on status change
                  if (
                    oldNotification.status === "UNREAD" &&
                    newRecord.status !== "UNREAD"
                  ) {
                    updatedUnreadCount = Math.max(0, updatedUnreadCount - 1);
                  } else if (
                    oldNotification.status !== "UNREAD" &&
                    newRecord.status === "UNREAD"
                  ) {
                    updatedUnreadCount += 1;
                  }
                }
              }
            }
            break;

          case "DELETE":
            // Check if notification is for current user (same logic as INSERT)
            const deleteRecordWithTo = oldRecord as any;
            const isDeleteForCurrentUser =
              deleteRecordWithTo?.to === user?.accountId ||
              deleteRecordWithTo?.to === "*" ||
              (oldRecord?.details &&
                typeof oldRecord.details === "object" &&
                (oldRecord.details as any)?.to === user?.accountId) ||
              oldRecord?.account_id === user?.accountId;

            if (oldRecord && isDeleteForCurrentUser) {
              const index = updatedNotifications.findIndex(
                (n) => n.id === oldRecord.id
              );
              if (index !== -1) {
                const deletedNotification = updatedNotifications[index];
                if (deletedNotification) {
                  updatedNotifications.splice(index, 1);

                  // Decrease unread count if it was an UNREAD notification
                  if (deletedNotification.status === "UNREAD") {
                    updatedUnreadCount = Math.max(0, updatedUnreadCount - 1);
                  }
                }
              }
            }
            break;
        }

        return {
          ...prev,
          notifications: updatedNotifications,
          unreadCount: updatedUnreadCount,
        };
      });
    },
    [user?.accountId, playNotificationSound]
  );

  // Subscribe to realtime changes
  const subscribe = useCallback(() => {
    if (!user?.accountId || channelRef.current) return;

    console.log("subscribing...");

    const channel = realtime
      .channel(channelName, {
        config: {
          presence: {
            key: user?.id,
          },
          broadcast: {
            self: true,
          },
        },
      })
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "notifications",
        },
        handleRealtimeChange
      )
      .subscribe((status) => {
        console.log("status", status);

        setState((prev) => ({
          ...prev,
          isConnected: status === "SUBSCRIBED",
          error: status === "CHANNEL_ERROR" ? "Connection error" : null,
        }));
      });

    channelRef.current = channel as any;
  }, [user?.accountId]);

  // Unsubscribe from realtime changes
  const unsubscribe = useCallback(() => {
    if (channelRef.current) {
      realtime.removeChannel(channelRef.current);
      channelRef.current = null;
      setState((prev) => ({ ...prev, isConnected: false }));
    }
  }, [realtime]);

  // Mark notification as read
  const markAsRead = useCallback(
    async (notificationId: string) => {
      try {
        const { error } = await supabase
          .from("notifications")
          .update({ status: "READ" as any }) // Will use READ once enum is updated
          .eq("id", notificationId);

        if (error) {
          throw error;
        }

        const filterUnRead: number = state.notifications.filter(
          (notification) =>
            notification.id !== notificationId &&
            notification.status === "UNREAD"
        ).length;

        setState({
          ...state,
          unreadCount: filterUnRead,
        });
      } catch (error: any) {
        console.error("Error marking notification as read:", error);
        toast.error("Failed to mark notification as read");
      }
    },
    [supabase]
  );

  // Mark notification as unread
  const markAsUnread = useCallback(
    async (notificationId: string) => {
      try {
        const { error } = await supabase
          .from("notifications")
          .update({ status: "UNREAD" as any }) // Will use UNREAD once enum is updated
          .eq("id", notificationId);

        if (error) {
          throw error;
        }

        const filterUnRead: number = state.notifications.filter(
          (notification) =>
            notification.id === notificationId && notification.status === "READ"
        ).length;

        setState({
          ...state,
          unreadCount: filterUnRead,
        });

        toast.success("Notification marked as unread");
      } catch (error: any) {
        console.error("Error marking notification as unread:", error);
        toast.error("Failed to mark notification as unread");
      }
    },
    [supabase]
  );

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    if (!user?.accountId) return;

    try {
      const { error } = await supabase
        .from("notifications")
        .update({ status: "READ" as any })
        .eq("account_id", user.accountId)
        .eq("status", "UNREAD" as any); // Only mark UNREAD notifications as read

      if (error) {
        throw error;
      }

      toast.success("All notifications marked as read");
    } catch (error: any) {
      console.error("Error marking all notifications as read:", error);
      toast.error("Failed to mark all notifications as read");
    }
  }, [user?.accountId, supabase]);

  // Delete notification (soft delete by setting status to INUNREAD)
  const deleteNotification = useCallback(
    async (notificationId: string) => {
      try {
        const { error } = await supabase
          .from("notifications")
          .update({ status: "INUNREAD" })
          .eq("id", notificationId);

        if (error) {
          throw error;
        }

        toast.success("Notification deleted");
      } catch (error: any) {
        console.error("Error deleting notification:", error);
        toast.error("Failed to delete notification");
      }
    },
    [supabase]
  );

  // Initialize and cleanup
  useEffect(() => {
    if (user?.accountId) {
      fetchNotifications();
      subscribe();
    } else {
      unsubscribe();
      setState((prev) => ({
        ...prev,
        isConnected: false,
        isLoading: false,
        error: null,
        notifications: [],
      }));
    }

    return () => {
      unsubscribe();
    };
  }, []);

  return {
    ...state,
    markAsRead,
    markAsUnread,
    markAllAsRead,
    deleteNotification,
    refreshNotifications: fetchNotifications,
    playNotificationSound,
    subscribe,
    unsubscribe,
  };
}
