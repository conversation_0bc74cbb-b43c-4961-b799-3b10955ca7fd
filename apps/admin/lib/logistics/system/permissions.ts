import { createClient } from "@supabase/supabase-js";
import { ServiceResponse, ServiceListResponse } from "../types";
import {
  RolePermissions,
  EntityType,
  PermissionAction,
  DEFAULT_ROLE_PERMISSIONS,
} from "../types/permissions";

export class PermissionService {
  private supabase: any;

  constructor() {
    // Use environment variables for Supabase client
    if (typeof window !== "undefined") {
      // Client-side
      this.supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      );
    } else {
      // Server-side - use service role key if available
      this.supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY ||
          process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      );
    }
  }

  // Get all permissions for a specific role
  async getPermissionsByRole(
    roleId: string
  ): Promise<ServiceResponse<RolePermissions>> {
    try {
      const { data: roleData, error: roleError } = await this.supabase
        .from("roles")
        .select(
          `
          id,
          name,
          permissions,
          departments (
            name
          )
        `
        )
        .eq("id", roleId)
        .single();

      if (roleError || !roleData) {
        return {
          data: null,
          error: "Role not found",
          success: false,
        };
      }

      // Transform JSONB permissions into structured format
      const permissionsMap: RolePermissions["permissions"] = {};
      const rolePermissions = roleData.permissions || {};

      // Convert from {"entity": ["action1", "action2"]} to {"entity": {"action1": true, "action2": true}}
      Object.entries(rolePermissions).forEach(([entity, actions]) => {
        if (Array.isArray(actions)) {
          permissionsMap[entity as EntityType] = {};
          actions.forEach((action: string) => {
            if (permissionsMap[entity as EntityType]) {
              permissionsMap[entity as EntityType]![
                action as PermissionAction
              ] = true;
            }
          });
        }
      });

      return {
        data: {
          role_id: roleData.id,
          role_name: roleData.name,
          department_name: roleData.departments?.name || "No Department",
          permissions: permissionsMap,
        },
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to get permissions by role",
        success: false,
      };
    }
  }

  // Update permissions for a role
  async updateRolePermissions(
    roleId: string,
    permissions: {
      entity: EntityType;
      action: PermissionAction;
      granted: boolean;
    }[]
  ): Promise<ServiceResponse<boolean>> {
    try {
      // Convert permissions array to JSONB format
      const permissionsObject: Record<string, string[]> = {};

      permissions.forEach((perm) => {
        if (perm.granted) {
          if (!permissionsObject[perm.entity]) {
            permissionsObject[perm.entity] = [];
          }
          if (!permissionsObject[perm.entity]!.includes(perm.action)) {
            permissionsObject[perm.entity]!.push(perm.action);
          }
        }
      });

      console.log("Updating role permissions:", {
        roleId,
        inputPermissions: permissions,
        permissionsObject,
        totalEntities: Object.keys(permissionsObject).length,
        totalActions: Object.values(permissionsObject).flat().length,
      });

      // Upsert the role with new permissions (update if exists, insert if not)
      const { error } = await this.supabase
        .from("roles")
        .update({
          permissions: permissionsObject,
          updated_at: new Date().toISOString(),
        })
        .eq("id", roleId);

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: true,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to update role permissions",
        success: false,
      };
    }
  }

  // Check if a role has specific permission
  async hasPermission(
    roleId: string,
    entity: EntityType,
    action: PermissionAction
  ): Promise<ServiceResponse<boolean>> {
    try {
      const { data, error } = await this.supabase
        .from("roles")
        .select("permissions")
        .eq("id", roleId)
        .single();

      if (error || !data) {
        return {
          data: false,
          error: null,
          success: true,
        };
      }

      const permissions = data.permissions || {};
      const entityPermissions = permissions[entity];

      if (!entityPermissions || !Array.isArray(entityPermissions)) {
        return {
          data: false,
          error: null,
          success: true,
        };
      }

      return {
        data: entityPermissions.includes(action),
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: false,
        error: error.message || "Failed to check permission",
        success: false,
      };
    }
  }

  // Get all roles with their permissions
  async getAllRolePermissions(): Promise<ServiceListResponse<RolePermissions>> {
    try {
      const { data: roles, error: rolesError } = await this.supabase
        .from("roles")
        .select(
          `
          id,
          name,
          status,
          permissions,
          departments (
            name
          )
        `
        )
        .eq("status", "ACTIVE");

      if (rolesError) {
        return {
          data: [],
          error: rolesError.message,
          success: false,
        };
      }

      const rolePermissions: RolePermissions[] = [];

      for (const role of roles || []) {
        // Transform JSONB permissions into structured format
        const permissionsMap: RolePermissions["permissions"] = {};
        const rolePerms = role.permissions || {};

        // Convert from {"entity": ["action1", "action2"]} to {"entity": {"action1": true, "action2": true}}
        Object.entries(rolePerms).forEach(([entity, actions]) => {
          if (Array.isArray(actions)) {
            permissionsMap[entity as EntityType] = {};
            actions.forEach((action: string) => {
              if (permissionsMap[entity as EntityType]) {
                permissionsMap[entity as EntityType]![
                  action as PermissionAction
                ] = true;
              }
            });
          }
        });

        rolePermissions.push({
          role_id: role.id,
          role_name: role.name,
          department_name: role.departments?.name || "No Department",
          permissions: permissionsMap,
        });
      }

      return {
        data: rolePermissions,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: [],
        error: error.message || "Failed to get all role permissions",
        success: false,
      };
    }
  }

  // Apply default permissions to a role based on role name
  async applyDefaultPermissions(
    roleId: string,
    roleName: string
  ): Promise<ServiceResponse<boolean>> {
    try {
      const normalizedRoleName = roleName.toLowerCase();
      const defaultPermissions = DEFAULT_ROLE_PERMISSIONS[normalizedRoleName];

      if (!defaultPermissions) {
        return {
          data: false,
          error: `No default permissions found for role: ${roleName}`,
          success: false,
        };
      }

      // Convert default permissions to JSONB format
      const permissionsObject: Record<string, string[]> = {};

      Object.entries(defaultPermissions).forEach(([entity, actions]) => {
        if (actions && actions.length > 0) {
          permissionsObject[entity] = actions;
        }
      });

      // Update the role with default permissions
      const { error } = await this.supabase
        .from("roles")
        .update({
          permissions: permissionsObject,
          updated_at: new Date().toISOString(),
        })
        .eq("id", roleId);

      if (error) {
        return {
          data: false,
          error: error.message,
          success: false,
        };
      }

      return {
        data: true,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: false,
        error: error.message || "Failed to apply default permissions",
        success: false,
      };
    }
  }

  // Bulk update permissions for multiple roles
  async bulkUpdatePermissions(
    updates: {
      roleId: string;
      permissions: {
        entity: EntityType;
        action: PermissionAction;
        granted: boolean;
      }[];
    }[]
  ): Promise<ServiceResponse<boolean>> {
    try {
      for (const update of updates) {
        const result = await this.updateRolePermissions(
          update.roleId,
          update.permissions
        );
        if (!result.success) {
          return result;
        }
      }

      return {
        data: true,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: false,
        error: error.message || "Failed to bulk update permissions",
        success: false,
      };
    }
  }

  // Get permissions summary for dashboard
  async getPermissionsSummary(): Promise<
    ServiceResponse<{
      totalRoles: number;
      rolesWithPermissions: number;
      totalPermissions: number;
      permissionsByCategory: Record<string, number>;
    }>
  > {
    try {
      const { data: roles, error: rolesError } = await this.supabase
        .from("roles")
        .select("id, permissions")
        .eq("status", "ACTIVE");

      if (rolesError) {
        return {
          data: null,
          error: rolesError.message || "Failed to get summary",
          success: false,
        };
      }

      const totalRoles = roles?.length || 0;
      let totalPermissions = 0;
      let rolesWithPermissions = 0;

      const permissionsByCategory: Record<string, number> = {
        system: 0,
        operations: 0,
        finances: 0,
      };

      roles?.forEach((role) => {
        const rolePermissions = role.permissions || {};
        const hasPermissions = Object.keys(rolePermissions).length > 0;

        if (hasPermissions) {
          rolesWithPermissions++;
        }

        Object.entries(rolePermissions).forEach(([entity, actions]) => {
          if (Array.isArray(actions)) {
            totalPermissions += actions.length;

            // Categorize permissions
            if (
              ["users", "accounts", "roles", "departments"].includes(entity)
            ) {
              permissionsByCategory.system += actions.length;
            } else if (["cargo", "batches", "shipments"].includes(entity)) {
              permissionsByCategory.operations += actions.length;
            } else if (["ledgers", "transactions"].includes(entity)) {
              permissionsByCategory.finances += actions.length;
            }
          }
        });
      });

      return {
        data: {
          totalRoles,
          rolesWithPermissions,
          totalPermissions,
          permissionsByCategory,
        },
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to get permissions summary",
        success: false,
      };
    }
  }

  // Test database connection and permissions column
  async testConnection(): Promise<
    ServiceResponse<{
      connected: boolean;
      permissionsColumnExists: boolean;
      sampleRoleCount: number;
    }>
  > {
    try {
      // Test basic connection
      const { data: roles, error } = await this.supabase
        .from("roles")
        .select("id, name, permissions")
        .limit(1);

      if (error) {
        return {
          data: null,
          error: `Database connection failed: ${error.message}`,
          success: false,
        };
      }

      return {
        data: {
          connected: true,
          permissionsColumnExists: true,
          sampleRoleCount: roles?.length || 0,
        },
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: `Connection test failed: ${error.message}`,
        success: false,
      };
    }
  }
}

// Export singleton instance
export const permissionService = new PermissionService();
