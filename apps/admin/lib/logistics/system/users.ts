import { BaseService } from '../base/service'
import { 
  User, 
  UserInsert, 
  UserUpdate,
  UserWithAccount,
  ServiceResponse,
  ServiceListResponse,
  QueryParams
} from '../types'

export class UserService extends BaseService<User, UserInsert, UserUpdate> {
  protected tableName = 'users'

  // Get user with account information
  async getUserWithAccount(id: string): Promise<ServiceResponse<UserWithAccount>> {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .select(`
          *,
          accounts (
            id,
            email,
            status,
            role_id,
            roles (
              id,
              name,
              departments (
                id,
                name
              )
            )
          )
        `)
        .eq('id', id)
        .single()

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false
        }
      }

      return {
        data,
        error: null,
        success: true
      }
    } catch (error: any) {
      return {
        data: null,
        error: error.message || 'Failed to get user with account',
        success: false
      }
    }
  }

  // Get all users with account information
  async getAllUsersWithAccounts(params?: QueryParams): Promise<ServiceListResponse<UserWithAccount>> {
    try {
      let query = this.supabase
        .from('users')
        .select(`
          *,
          accounts (
            id,
            email,
            status,
            role_id,
            roles (
              id,
              name,
              departments (
                id,
                name
              )
            )
          )
        `, { count: 'exact' })

      // Apply filters
      if (params?.filters) {
        query = this.applyFilters(query, params.filters)
      }

      // Apply sorting
      if (params?.column) {
        query = query.order(params.column, { ascending: params.ascending ?? true })
      } else {
        query = query.order('name', { ascending: true })
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit)
        if (params.offset) {
          query = query.range(params.offset, params.offset + params.limit - 1)
        } else if (params.page && params.page > 1) {
          const offset = (params.page - 1) * params.limit
          query = query.range(offset, offset + params.limit - 1)
        }
      }

      const { data, error, count } = await query

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false
        }
      }

      return {
        data: data || [],
        error: null,
        success: true,
        count: count || 0
      }
    } catch (error: any) {
      return {
        data: [],
        error: error.message || 'Failed to get users with accounts',
        success: false
      }
    }
  }

  // Get active users only
  async getActiveUsers(params?: QueryParams): Promise<ServiceListResponse<UserWithAccount>> {
    const filters = { ...params?.filters, status: 'ACTIVE' as const }
    return this.getAllUsersWithAccounts({ ...params, filters })
  }

  // Search users by name, phone, location
  async searchUsers(searchTerm: string, params?: QueryParams): Promise<ServiceListResponse<UserWithAccount>> {
    try {
      let query = this.supabase
        .from('users')
        .select(`
          *,
          accounts (
            id,
            email,
            status,
            role_id,
            roles (
              id,
              name,
              departments (
                id,
                name
              )
            )
          )
        `, { count: 'exact' })
        .or(`name.ilike.%${searchTerm}%,phone.ilike.%${searchTerm}%,location.ilike.%${searchTerm}%,accounts.email.ilike.%${searchTerm}%`)

      // Apply additional filters
      if (params?.filters) {
        query = this.applyFilters(query, params.filters)
      }

      // Apply sorting
      if (params?.column) {
        query = query.order(params.column, { ascending: params.ascending ?? true })
      } else {
        query = query.order('name', { ascending: true })
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit)
        if (params.offset) {
          query = query.range(params.offset, params.offset + params.limit - 1)
        } else if (params.page && params.page > 1) {
          const offset = (params.page - 1) * params.limit
          query = query.range(offset, offset + params.limit - 1)
        }
      }

      const { data, error, count } = await query

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false
        }
      }

      return {
        data: data || [],
        error: null,
        success: true,
        count: count || 0
      }
    } catch (error: any) {
      return {
        data: [],
        error: error.message || 'Failed to search users',
        success: false
      }
    }
  }

  // Update user profile
  async updateProfile(id: string, data: UserUpdate): Promise<ServiceResponse<User>> {
    try {
      // Validate phone number format if provided
      if (data.phone && data.phone.length > 20) {
        return {
          data: null,
          error: 'Phone number too long (max 20 characters)',
          success: false
        }
      }

      // Validate location length if provided
      if (data.location && data.location.length > 100) {
        return {
          data: null,
          error: 'Location too long (max 100 characters)',
          success: false
        }
      }

      return this.update(id, data)
    } catch (error: any) {
      return {
        data: null,
        error: error.message || 'Failed to update user profile',
        success: false
      }
    }
  }

  // Update user avatar
  async updateAvatar(id: string, avatarUrl: string): Promise<ServiceResponse<User>> {
    try {
      if (avatarUrl.length > 255) {
        return {
          data: null,
          error: 'Avatar URL too long (max 255 characters)',
          success: false
        }
      }

      return this.update(id, { avatar: avatarUrl })
    } catch (error: any) {
      return {
        data: null,
        error: error.message || 'Failed to update user avatar',
        success: false
      }
    }
  }

  // Get users by role
  async getUsersByRole(roleId: string, params?: QueryParams): Promise<ServiceListResponse<UserWithAccount>> {
    try {
      let query = this.supabase
        .from('users')
        .select(`
          *,
          accounts!inner (
            id,
            email,
            status,
            role_id,
            roles (
              id,
              name,
              departments (
                id,
                name
              )
            )
          )
        `, { count: 'exact' })
        .eq('accounts.role_id', roleId)

      // Apply additional filters
      if (params?.filters) {
        query = this.applyFilters(query, params.filters)
      }

      // Apply sorting
      if (params?.column) {
        query = query.order(params.column, { ascending: params.ascending ?? true })
      } else {
        query = query.order('name', { ascending: true })
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit)
        if (params.offset) {
          query = query.range(params.offset, params.offset + params.limit - 1)
        } else if (params.page && params.page > 1) {
          const offset = (params.page - 1) * params.limit
          query = query.range(offset, offset + params.limit - 1)
        }
      }

      const { data, error, count } = await query

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false
        }
      }

      return {
        data: data || [],
        error: null,
        success: true,
        count: count || 0
      }
    } catch (error: any) {
      return {
        data: [],
        error: error.message || 'Failed to get users by role',
        success: false
      }
    }
  }

  // Get users by department
  async getUsersByDepartment(departmentId: string, params?: QueryParams): Promise<ServiceListResponse<UserWithAccount>> {
    try {
      let query = this.supabase
        .from('users')
        .select(`
          *,
          accounts!inner (
            id,
            email,
            status,
            role_id,
            roles!inner (
              id,
              name,
              department_id,
              departments (
                id,
                name
              )
            )
          )
        `, { count: 'exact' })
        .eq('accounts.roles.department_id', departmentId)

      // Apply additional filters
      if (params?.filters) {
        query = this.applyFilters(query, params.filters)
      }

      // Apply sorting
      if (params?.column) {
        query = query.order(params.column, { ascending: params.ascending ?? true })
      } else {
        query = query.order('name', { ascending: true })
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit)
        if (params.offset) {
          query = query.range(params.offset, params.offset + params.limit - 1)
        } else if (params.page && params.page > 1) {
          const offset = (params.page - 1) * params.limit
          query = query.range(offset, offset + params.limit - 1)
        }
      }

      const { data, error, count } = await query

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false
        }
      }

      return {
        data: data || [],
        error: null,
        success: true,
        count: count || 0
      }
    } catch (error: any) {
      return {
        data: [],
        error: error.message || 'Failed to get users by department',
        success: false
      }
    }
  }

  // Get user statistics
  async getUserStats(): Promise<ServiceResponse<{
    totalUsers: number
    activeUsers: number
    inactiveUsers: number
    usersWithoutAvatar: number
    usersWithPhone: number
    usersWithLocation: number
  }>> {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .select('status, avatar, phone, location')

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false
        }
      }

      const totalUsers = data?.length || 0
      const activeUsers = data?.filter(u => u.status === 'ACTIVE').length || 0
      const inactiveUsers = totalUsers - activeUsers
      const usersWithoutAvatar = data?.filter(u => !u.avatar).length || 0
      const usersWithPhone = data?.filter(u => u.phone).length || 0
      const usersWithLocation = data?.filter(u => u.location).length || 0

      return {
        data: {
          totalUsers,
          activeUsers,
          inactiveUsers,
          usersWithoutAvatar,
          usersWithPhone,
          usersWithLocation
        },
        error: null,
        success: true
      }
    } catch (error: any) {
      return {
        data: null,
        error: error.message || 'Failed to get user statistics',
        success: false
      }
    }
  }

  // Get users for assignment (for schedules, tasks, etc.)
  async getUsersForAssignment(excludeIds: string[] = [], params?: QueryParams): Promise<ServiceListResponse<UserWithAccount>> {
    try {
      let query = this.supabase
        .from('users')
        .select(`
          *,
          accounts (
            id,
            email,
            status,
            role_id,
            roles (
              id,
              name,
              departments (
                id,
                name
              )
            )
          )
        `, { count: 'exact' })
        .eq('status', 'ACTIVE')

      if (excludeIds.length > 0) {
        query = query.not('id', 'in', `(${excludeIds.join(',')})`)
      }

      // Apply additional filters
      if (params?.filters) {
        query = this.applyFilters(query, params.filters)
      }

      // Apply sorting
      if (params?.column) {
        query = query.order(params.column, { ascending: params.ascending ?? true })
      } else {
        query = query.order('name', { ascending: true })
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit)
        if (params.offset) {
          query = query.range(params.offset, params.offset + params.limit - 1)
        } else if (params.page && params.page > 1) {
          const offset = (params.page - 1) * params.limit
          query = query.range(offset, offset + params.limit - 1)
        }
      }

      const { data, error, count } = await query

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false
        }
      }

      return {
        data: data || [],
        error: null,
        success: true,
        count: count || 0
      }
    } catch (error: any) {
      return {
        data: [],
        error: error.message || 'Failed to get users for assignment',
        success: false
      }
    }
  }
}

// Export singleton instance
export const userService = new UserService() 