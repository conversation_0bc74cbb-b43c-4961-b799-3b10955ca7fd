"use client";

import { BaseService } from "../base/service";

import {
  Account,
  AccountInsert,
  AccountUpdate,
  AccountWithUserAndRole,
  ServiceResponse,
  ServiceListResponse,
  QueryParams,
} from "../types";

import bcrypt from "bcryptjs";
import { sendMail, getWelcomeUserTemplate } from "../../mail";

export class AccountService extends BaseService<
  Account,
  AccountInsert,
  AccountUpdate
> {
  protected tableName: any = "accounts";

  async createNewStaff(data: any) {
    try {
      // Create user first
      this.tableName = "users";
      const userResult: any = await this.create({
        name: data.name,
        phone: data.phone,
        status: "ACTIVE",
      });

      if (!userResult.success || !userResult.data)
        throw new Error(userResult.error || "Failed to create user");

      const tempPassword: string = "TempPassword123!";
      let hashedPassword: string = await bcrypt.hash(tempPassword, 12);

      this.tableName = "accounts";
      await this.create({
        email: data.email,
        password: hashedPassword,
        user_id: userResult.data.id,
        role_id: data.roleId,
        status: "ACTIVE",
      });

      // // Metadata
      this.tableName = "roles";
      let role: any = await this.getById(data.roleId, "name");
      //
      let template: any = await getWelcomeUserTemplate(
        data.name,
        data.email,
        role.name
      );

      await sendMail({
        to: data.email,
        ...template,
      });

      return {
        data: true,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to create user",
        success: false,
      };
    } finally {
      // Reverting tableName to default Context
      this.tableName = "accounts";
    }
  }
  // Get account with user and role information
  async getAccountWithUserAndRole(
    id: string
  ): Promise<ServiceResponse<AccountWithUserAndRole>> {
    try {
      const { data, error } = await this.supabase
        .from("accounts")
        .select(
          `
          *,
          users (
            id,
            name,
            phone,
            location,
            avatar,
            status
          ),
          roles (
            id,
            name,
            status,
            departments (
              id,
              name,
              status
            )
          )
        `
        )
        .eq("id", id)
        .single();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to get account with user and role",
        success: false,
      };
    }
  }

  // Get all accounts with user and role information
  async getAllAccountsWithUserAndRole(
    params?: QueryParams
  ): Promise<ServiceListResponse<AccountWithUserAndRole>> {
    try {
      let query = this.supabase.from("accounts").select(
        `
          *,
          users (
            id,
            name,
            phone,
            location,
            avatar,
            status
          ),
          roles (
            id,
            name,
            status,
            departments (
              id,
              name,
              status
            )
          )
        `,
        { count: "exact" }
      );

      // Apply filters
      if (params?.filters) {
        query = this.applyFilters(query, params.filters);
      }

      // Apply sorting
      if (params?.column) {
        query = query.order(params.column, {
          ascending: params.ascending ?? true,
        });
      } else {
        query = query.order("created_at", { ascending: false });
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit);
        if (params.offset) {
          query = query.range(params.offset, params.offset + params.limit - 1);
        } else if (params.page && params.page > 1) {
          const offset = (params.page - 1) * params.limit;
          query = query.range(offset, offset + params.limit - 1);
        }
      }

      const { data, error, count } = await query;

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false,
        };
      }

      return {
        data: data || [],
        error: null,
        success: true,
        count: count || 0,
      };
    } catch (error: any) {
      return {
        data: [],
        error: error.message || "Failed to get accounts with user and role",
        success: false,
      };
    }
  }

  // Get account by email
  async getAccountByEmail(
    email: string
  ): Promise<ServiceResponse<AccountWithUserAndRole>> {
    try {
      const { data, error } = await this.supabase
        .from("accounts")
        .select(
          `
          *,
          users (
            id,
            name,
            phone,
            location,
            avatar,
            status
          ),
          roles (
            id,
            name,
            status,
            departments (
              id,
              name,
              status
            )
          )
        `
        )
        .eq("email", email)
        .single();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to get account by email",
        success: false,
      };
    }
  }

  // Check if email exists
  async isEmailExists(
    email: string,
    excludeId?: string
  ): Promise<ServiceResponse<boolean>> {
    try {
      let query = this.supabase
        .from("accounts")
        .select("id")
        .eq("email", email);

      if (excludeId) {
        query = query.neq("id", excludeId);
      }

      const { data, error } = await query;

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: (data || []).length > 0,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to check email existence",
        success: false,
      };
    }
  }

  // Get accounts by role
  async getAccountsByRole(
    roleId: string,
    params?: QueryParams
  ): Promise<ServiceListResponse<AccountWithUserAndRole>> {
    const filters = { ...params?.filters, role_id: roleId };
    return this.getAllAccountsWithUserAndRole({ ...params, filters });
  }

  // Get accounts by department
  async getAccountsByDepartment(
    departmentId: string,
    params?: QueryParams
  ): Promise<ServiceListResponse<AccountWithUserAndRole>> {
    try {
      let query = this.supabase
        .from("accounts")
        .select(
          `
          *,
          users (
            id,
            name,
            phone,
            location,
            avatar,
            status
          ),
          roles!inner (
            id,
            name,
            status,
            department_id,
            departments (
              id,
              name,
              status
            )
          )
        `,
          { count: "exact" }
        )
        .eq("roles.department_id", departmentId);

      // Apply additional filters
      if (params?.filters) {
        query = this.applyFilters(query, params.filters);
      }

      // Apply sorting
      if (params?.column) {
        query = query.order(params.column, {
          ascending: params.ascending ?? true,
        });
      } else {
        query = query.order("created_at", { ascending: false });
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit);
        if (params.offset) {
          query = query.range(params.offset, params.offset + params.limit - 1);
        } else if (params.page && params.page > 1) {
          const offset = (params.page - 1) * params.limit;
          query = query.range(offset, offset + params.limit - 1);
        }
      }

      const { data, error, count } = await query;

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false,
        };
      }

      return {
        data: data || [],
        error: null,
        success: true,
        count: count || 0,
      };
    } catch (error: any) {
      return {
        data: [],
        error: error.message || "Failed to get accounts by department",
        success: false,
      };
    }
  }

  // Get active accounts only
  async getActiveAccounts(
    params?: QueryParams
  ): Promise<ServiceListResponse<AccountWithUserAndRole>> {
    const filters = { ...params?.filters, status: "ACTIVE" as const };
    return this.getAllAccountsWithUserAndRole({ ...params, filters });
  }

  // Search accounts by email or user name
  async searchAccounts(
    searchTerm: string,
    params?: QueryParams
  ): Promise<ServiceListResponse<AccountWithUserAndRole>> {
    try {
      let query = this.supabase
        .from("accounts")
        .select(
          `
          *,
          users (
            id,
            name,
            phone,
            location,
            avatar,
            status
          ),
          roles (
            id,
            name,
            status,
            departments (
              id,
              name,
              status
            )
          )
        `,
          { count: "exact" }
        )
        .or(
          `email.ilike.%${searchTerm}%,users.name.ilike.%${searchTerm}%,users.phone.ilike.%${searchTerm}%`
        );

      // Apply additional filters
      if (params?.filters) {
        query = this.applyFilters(query, params.filters);
      }

      // Apply sorting
      if (params?.column) {
        query = query.order(params.column, {
          ascending: params.ascending ?? true,
        });
      } else {
        query = query.order("created_at", { ascending: false });
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit);
        if (params.offset) {
          query = query.range(params.offset, params.offset + params.limit - 1);
        } else if (params.page && params.page > 1) {
          const offset = (params.page - 1) * params.limit;
          query = query.range(offset, offset + params.limit - 1);
        }
      }

      const { data, error, count } = await query;

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false,
        };
      }

      return {
        data: data || [],
        error: null,
        success: true,
        count: count || 0,
      };
    } catch (error: any) {
      return {
        data: [],
        error: error.message || "Failed to search accounts",
        success: false,
      };
    }
  }

  // Update account role
  async updateAccountRole(
    id: string,
    roleId: string
  ): Promise<ServiceResponse<Account>> {
    return this.update(id, { role_id: roleId });
  }

  // Change account password with current password verification
  async changePassword(
    id: string,
    currentPassword: string,
    newPassword: string
  ): Promise<ServiceResponse<Account>> {
    try {
      // First, get the current account to verify the current password
      const { data: account, error: fetchError } = await this.supabase
        .from(this.tableName)
        .select("id, password")
        .eq("id", id)
        .single();

      if (fetchError || !account) {
        return {
          data: null,
          error: "Account not found",
          success: false,
        };
      }

      // Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(
        currentPassword,
        account.password
      );
      if (!isCurrentPasswordValid) {
        return {
          data: null,
          error: "Current password is incorrect",
          success: false,
        };
      }

      // Hash the new password
      const hashedNewPassword = await bcrypt.hash(newPassword, 12);

      // Update the password
      const { data, error } = await this.supabase
        .from(this.tableName)
        .update({
          password: hashedNewPassword,
          updated_at: new Date().toISOString(),
        })
        .eq("id", id)
        .select()
        .single();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to change password",
        success: false,
      };
    }
  }

  // Legacy method for admin password updates (without current password verification)
  async adminChangePassword(
    id: string,
    newPassword: string
  ): Promise<ServiceResponse<Account>> {
    try {
      const { data: existingRows, error: existingError } = await this.supabase
        .from(this.tableName)
        .select("*")
        .eq("id", id)
        .single();

      if (existingError || !existingRows)
        throw Error(existingError.message || "No existing rows where found");

      // Hash the new password
      const hashedNewPassword = await bcrypt.hash(newPassword, 12);

      // Update the password
      const { data, error } = await this.supabase
        .from(this.tableName)
        .update({
          password: hashedNewPassword,
          updated_at: new Date().toISOString(),
        })
        .eq("id", id)
        .select();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to change password",
        success: false,
      };
    }
  }

  // Get account statistics
  async getAccountStats(): Promise<
    ServiceResponse<{
      totalAccounts: number;
      activeAccounts: number;
      inactiveAccounts: number;
      accountsByDepartment: Array<{
        departmentId: string;
        departmentName: string;
        count: number;
      }>;
      accountsByRole: Array<{
        roleId: string;
        roleName: string;
        count: number;
      }>;
    }>
  > {
    try {
      const { data: accounts, error } = await this.supabase.from("accounts")
        .select(`
          status,
          roles (
            id,
            name,
            departments (
              id,
              name
            )
          )
        `);

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      const totalAccounts = accounts?.length || 0;
      const activeAccounts =
        accounts?.filter((a) => a.status === "ACTIVE").length || 0;
      const inactiveAccounts = totalAccounts - activeAccounts;

      // Group by department
      const departmentCounts = new Map<
        string,
        { name: string; count: number }
      >();
      accounts?.forEach((account) => {
        if (account.roles?.departments) {
          const deptId = account.roles.departments.id;
          const deptName = account.roles.departments.name;
          const current = departmentCounts.get(deptId) || {
            name: deptName,
            count: 0,
          };
          departmentCounts.set(deptId, {
            ...current,
            count: current.count + 1,
          });
        }
      });

      // Group by role
      const roleCounts = new Map<string, { name: string; count: number }>();
      accounts?.forEach((account) => {
        if (account.roles) {
          const roleId = account.roles.id;
          const roleName = account.roles.name;
          const current = roleCounts.get(roleId) || {
            name: roleName,
            count: 0,
          };
          roleCounts.set(roleId, { ...current, count: current.count + 1 });
        }
      });

      const accountsByDepartment = Array.from(departmentCounts.entries()).map(
        ([id, data]) => ({
          departmentId: id,
          departmentName: data.name,
          count: data.count,
        })
      );

      const accountsByRole = Array.from(roleCounts.entries()).map(
        ([id, data]) => ({
          roleId: id,
          roleName: data.name,
          count: data.count,
        })
      );

      return {
        data: {
          totalAccounts,
          activeAccounts,
          inactiveAccounts,
          accountsByDepartment,
          accountsByRole,
        },
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to get account statistics",
        success: false,
      };
    }
  }

  async removeStaffAccount(
    userId: string,
    accountId: string
  ): Promise<
    ServiceResponse<{
      success: any;
      error: any;
      data: any;
    }>
  > {
    if (!userId || !accountId)
      return {
        success: false,
        error: "User/Account identifiers not found",
        data: null,
      };

    try {
      this.tableName = "users";
      let { data, error } = await this.update(userId, { status: "INACTIVE" });

      if (error)
        return {
          data: null,
          error: error?.message || "Failed to remove staff account",
          success: false,
        };

      this.tableName = "accounts";
      await this.update(accountId, { status: "INACTIVE" });

      return {
        success: true,
        error: null,
        data: null,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to remove staff account",
        success: false,
      };
    } finally {
      this.tableName = "accounts";
    }
  }

  // Check if any accounts exist in the system
  async checkIfAnyAccountsExist(): Promise<ServiceResponse<boolean>> {
    try {
      const { count, error } = await this.supabase
        .from("accounts")
        .select("*", { count: "exact", head: true });

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: (count || 0) > 0,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to check accounts existence",
        success: false,
      };
    }
  }
}

// Export singleton instance
export const accountService = new AccountService();
