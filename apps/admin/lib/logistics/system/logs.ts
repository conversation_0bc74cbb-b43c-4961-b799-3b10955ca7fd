import { BaseService } from '../base/service'
import { 
  Log, 
  LogInsert, 
  LogUpdate,
  LogWithAccount,
  ServiceResponse,
  ServiceListResponse,
  QueryParams
} from '../types'

export class LogService extends BaseService<Log, LogInsert, LogUpdate> {
  protected tableName = 'logs'

  // Get log with account information
  async getLogWithAccount(id: string): Promise<ServiceResponse<LogWithAccount>> {
    try {
      const { data, error } = await this.supabase
        .from('logs')
        .select(`
          *,
          accounts (
            id,
            email,
            users (
              id,
              name
            )
          )
        `)
        .eq('id', id)
        .single()

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false
        }
      }

      return {
        data,
        error: null,
        success: true
      }
    } catch (error: any) {
      return {
        data: null,
        error: error.message || 'Failed to get log with account',
        success: false
      }
    }
  }

  // Get all logs with account information
  async getAllLogsWithAccounts(params?: QueryParams): Promise<ServiceListResponse<LogWithAccount>> {
    try {
      let query = this.supabase
        .from('logs')
        .select(`
          *,
          accounts (
            id,
            email,
            users (
              id,
              name
            )
          )
        `, { count: 'exact' })

      // Apply filters
      if (params?.filters) {
        query = this.applyFilters(query, params.filters)
      }

      // Apply sorting
      if (params?.column) {
        query = query.order(params.column, { ascending: params.ascending ?? true })
      } else {
        query = query.order('created_at', { ascending: false })
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit)
        if (params.offset) {
          query = query.range(params.offset, params.offset + params.limit - 1)
        } else if (params.page && params.page > 1) {
          const offset = (params.page - 1) * params.limit
          query = query.range(offset, offset + params.limit - 1)
        }
      }

      const { data, error, count } = await query

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false
        }
      }

      return {
        data: data || [],
        error: null,
        success: true,
        count: count || 0
      }
    } catch (error: any) {
      return {
        data: [],
        error: error.message || 'Failed to get logs with accounts',
        success: false
      }
    }
  }

  // Create log entry
  async createLog(data: LogInsert): Promise<ServiceResponse<Log>> {
    try {
      const logData = {
        ...data,
        status: data.status || 'ACTIVE'
      }

      return this.create(logData)
    } catch (error: any) {
      return {
        data: null,
        error: error.message || 'Failed to create log',
        success: false
      }
    }
  }

  // Log an event with automatic account detection
  async logEvent(
    event: string,
    message: string,
    accountId: string,
    associatedTable?: string,
    associatedId?: string
  ): Promise<ServiceResponse<Log>> {
    return this.createLog({
      event,
      message,
      account_id: accountId,
      associatedTable,
      associatedId,
      status: 'ACTIVE'
    })
  }

  // Log user action
  async logUserAction(
    accountId: string,
    action: string,
    details: string,
    entityTable?: string,
    entityId?: string
  ): Promise<ServiceResponse<Log>> {
    return this.logEvent(
      'USER_ACTION',
      `${action}: ${details}`,
      accountId,
      entityTable,
      entityId
    )
  }

  // Log system event
  async logSystemEvent(
    event: string,
    message: string,
    accountId?: string,
    entityTable?: string,
    entityId?: string
  ): Promise<ServiceResponse<Log>> {
    return this.createLog({
      event: `SYSTEM_${event}`,
      message,
      account_id: accountId,
      associatedTable: entityTable,
      associatedId: entityId,
      status: 'ACTIVE'
    })
  }

  // Log security event
  async logSecurityEvent(
    event: string,
    message: string,
    accountId?: string,
    ipAddress?: string
  ): Promise<ServiceResponse<Log>> {
    const securityMessage = ipAddress ? `${message} (IP: ${ipAddress})` : message
    return this.createLog({
      event: `SECURITY_${event}`,
      message: securityMessage,
      account_id: accountId,
      status: 'ACTIVE'
    })
  }

  // Log data change
  async logDataChange(
    accountId: string,
    table: string,
    recordId: string,
    action: 'CREATE' | 'UPDATE' | 'DELETE',
    changes?: any
  ): Promise<ServiceResponse<Log>> {
    const changeDetails = changes ? ` Changes: ${JSON.stringify(changes)}` : ''
    return this.logEvent(
      'DATA_CHANGE',
      `${action} ${table} record${changeDetails}`,
      accountId,
      table,
      recordId
    )
  }

  // Get logs by account
  async getLogsByAccount(accountId: string, params?: QueryParams): Promise<ServiceListResponse<LogWithAccount>> {
    const filters = { ...params?.filters, account_id: accountId }
    return this.getAllLogsWithAccounts({ ...params, filters })
  }

  // Get logs by event type
  async getLogsByEvent(event: string, params?: QueryParams): Promise<ServiceListResponse<LogWithAccount>> {
    const filters = { ...params?.filters, event }
    return this.getAllLogsWithAccounts({ ...params, filters })
  }

  // Get logs by entity
  async getLogsByEntity(tableName: string, entityId: string, params?: QueryParams): Promise<ServiceListResponse<LogWithAccount>> {
    const filters = { 
      ...params?.filters, 
      associatedTable: tableName,
      associatedId: entityId
    }
    return this.getAllLogsWithAccounts({ ...params, filters })
  }

  // Search logs by event or message
  async searchLogs(searchTerm: string, params?: QueryParams): Promise<ServiceListResponse<LogWithAccount>> {
    try {
      let query = this.supabase
        .from('logs')
        .select(`
          *,
          accounts (
            id,
            email,
            users (
              id,
              name
            )
          )
        `, { count: 'exact' })
        .or(`event.ilike.%${searchTerm}%,message.ilike.%${searchTerm}%`)

      // Apply additional filters
      if (params?.filters) {
        query = this.applyFilters(query, params.filters)
      }

      // Apply sorting
      if (params?.column) {
        query = query.order(params.column, { ascending: params.ascending ?? true })
      } else {
        query = query.order('created_at', { ascending: false })
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit)
        if (params.offset) {
          query = query.range(params.offset, params.offset + params.limit - 1)
        } else if (params.page && params.page > 1) {
          const offset = (params.page - 1) * params.limit
          query = query.range(offset, offset + params.limit - 1)
        }
      }

      const { data, error, count } = await query

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false
        }
      }

      return {
        data: data || [],
        error: null,
        success: true,
        count: count || 0
      }
    } catch (error: any) {
      return {
        data: [],
        error: error.message || 'Failed to search logs',
        success: false
      }
    }
  }

  // Get logs by date range
  async getLogsByDateRange(
    startDate: string,
    endDate: string,
    params?: QueryParams
  ): Promise<ServiceListResponse<LogWithAccount>> {
    try {
      let query = this.supabase
        .from('logs')
        .select(`
          *,
          accounts (
            id,
            email,
            users (
              id,
              name
            )
          )
        `, { count: 'exact' })
        .gte('created_at', startDate)
        .lte('created_at', endDate)

      // Apply additional filters
      if (params?.filters) {
        query = this.applyFilters(query, params.filters)
      }

      // Apply sorting
      if (params?.column) {
        query = query.order(params.column, { ascending: params.ascending ?? true })
      } else {
        query = query.order('created_at', { ascending: false })
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit)
        if (params.offset) {
          query = query.range(params.offset, params.offset + params.limit - 1)
        } else if (params.page && params.page > 1) {
          const offset = (params.page - 1) * params.limit
          query = query.range(offset, offset + params.limit - 1)
        }
      }

      const { data, error, count } = await query

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false
        }
      }

      return {
        data: data || [],
        error: null,
        success: true,
        count: count || 0
      }
    } catch (error: any) {
      return {
        data: [],
        error: error.message || 'Failed to get logs by date range',
        success: false
      }
    }
  }

  // Get recent logs
  async getRecentLogs(limit: number = 50): Promise<ServiceListResponse<LogWithAccount>> {
    return this.getAllLogsWithAccounts({ 
      limit,
      column: 'created_at',
      ascending: false
    })
  }

  // Get log statistics
  async getLogStats(): Promise<ServiceResponse<{
    totalLogs: number
    logsByEvent: Array<{ event: string; count: number }>
    logsByAccount: Array<{
      accountId: string
      userEmail: string
      userName: string
      logCount: number
    }>
    logsByDate: Array<{
      date: string
      count: number
    }>
    topEvents: Array<{ event: string; count: number }>
  }>> {
    try {
      const { data: logs, error } = await this.supabase
        .from('logs')
        .select(`
          event,
          created_at,
          account_id,
          accounts (
            email,
            users (
              name
            )
          )
        `)

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false
        }
      }

      const totalLogs = logs?.length || 0

      // Group by event
      const eventCounts = new Map<string, number>()
      logs?.forEach(log => {
        const event = log.event || 'UNKNOWN'
        eventCounts.set(event, (eventCounts.get(event) || 0) + 1)
      })

      // Group by account
      const accountCounts = new Map<string, { email: string; name: string; count: number }>()
      logs?.forEach(log => {
        if (log.account_id && log.accounts) {
          const accountId = log.account_id
          const email = (log.accounts as any)?.email || 'Unknown'
          const name = (log.accounts as any)?.users?.name || 'Unknown'
          
          const current = accountCounts.get(accountId) || { email, name, count: 0 }
          current.count += 1
          accountCounts.set(accountId, current)
        }
      })

      // Group by date (last 7 days)
      const dateCounts = new Map<string, number>()
      const today = new Date()
      for (let i = 6; i >= 0; i--) {
        const date = new Date(today)
        date.setDate(date.getDate() - i)
        const dateStr = date.toISOString().split('T')[0]
        dateCounts.set(dateStr, 0)
      }

      logs?.forEach(log => {
        if (log.created_at) {
          const dateStr = new Date(log.created_at).toISOString().split('T')[0]
          if (dateCounts.has(dateStr)) {
            dateCounts.set(dateStr, (dateCounts.get(dateStr) || 0) + 1)
          }
        }
      })

      // Top 10 events
      const topEvents = Array.from(eventCounts.entries())
        .map(([event, count]) => ({ event, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10)

      return {
        data: {
          totalLogs,
          logsByEvent: Array.from(eventCounts.entries()).map(([event, count]) => ({ event, count })),
          logsByAccount: Array.from(accountCounts.entries()).map(([accountId, data]) => ({
            accountId,
            userEmail: data.email,
            userName: data.name,
            logCount: data.count
          })),
          logsByDate: Array.from(dateCounts.entries()).map(([date, count]) => ({ date, count })),
          topEvents
        },
        error: null,
        success: true
      }
    } catch (error: any) {
      return {
        data: null,
        error: error.message || 'Failed to get log statistics',
        success: false
      }
    }
  }

  // Clean up old logs (older than specified days)
  async cleanupOldLogs(daysOld: number = 90): Promise<ServiceResponse<number>> {
    try {
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - daysOld)

      const { data, error } = await this.supabase
        .from('logs')
        .delete()
        .lt('created_at', cutoffDate.toISOString())
        .select('id')

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false
        }
      }

      return {
        data: data?.length || 0,
        error: null,
        success: true
      }
    } catch (error: any) {
      return {
        data: null,
        error: error.message || 'Failed to cleanup old logs',
        success: false
      }
    }
  }

  // Archive logs (move to separate table or export)
  async archiveLogs(daysOld: number = 30): Promise<ServiceResponse<number>> {
    try {
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - daysOld)

      // For now, just mark as archived by updating status
      // In a full implementation, you might move to a separate archive table
      const { data, error } = await this.supabase
        .from('logs')
        .update({ status: 'INACTIVE' })
        .lt('created_at', cutoffDate.toISOString())
        .eq('status', 'ACTIVE')
        .select('id')

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false
        }
      }

      return {
        data: data?.length || 0,
        error: null,
        success: true
      }
    } catch (error: any) {
      return {
        data: null,
        error: error.message || 'Failed to archive logs',
        success: false
      }
    }
  }

  // Export logs for external analysis
  async exportLogs(
    startDate?: string,
    endDate?: string,
    format: 'json' | 'csv' = 'json'
  ): Promise<ServiceResponse<string>> {
    try {
      let query = this.supabase
        .from('logs')
        .select(`
          *,
          accounts (
            email,
            users (
              name
            )
          )
        `)

      if (startDate) {
        query = query.gte('created_at', startDate)
      }
      if (endDate) {
        query = query.lte('created_at', endDate)
      }

      query = query.order('created_at', { ascending: false })

      const { data, error } = await query

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false
        }
      }

      if (format === 'json') {
        return {
          data: JSON.stringify(data, null, 2),
          error: null,
          success: true
        }
      } else {
        // Convert to CSV format
        if (!data || data.length === 0) {
          return {
            data: '',
            error: null,
            success: true
          }
        }

        const headers = ['created_at', 'event', 'message', 'account_email', 'user_name', 'associatedTable', 'associatedId']
        const csvRows = [headers.join(',')]

        data.forEach(log => {
          const row = [
            log.created_at || '',
            log.event || '',
            `"${(log.message || '').replace(/"/g, '""')}"`,
            (log.accounts as any)?.email || '',
            (log.accounts as any)?.users?.name || '',
            log.associatedTable || '',
            log.associatedId || ''
          ]
          csvRows.push(row.join(','))
        })

        return {
          data: csvRows.join('\n'),
          error: null,
          success: true
        }
      }
    } catch (error: any) {
      return {
        data: null,
        error: error.message || 'Failed to export logs',
        success: false
      }
    }
  }
}

// Export singleton instance
export const logService = new LogService() 