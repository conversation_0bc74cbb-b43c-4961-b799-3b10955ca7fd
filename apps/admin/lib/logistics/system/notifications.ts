import { BaseService } from "../base/service";
import {
  Notification,
  NotificationInsert,
  NotificationUpdate,
  NotificationWithAccount,
  ServiceResponse,
  ServiceListResponse,
  QueryParams,
} from "../types";

export class NotificationService extends BaseService<
  Notification,
  NotificationInsert,
  NotificationUpdate
> {
  protected tableName = "notifications";

  // Get notification with account information
  async getNotificationWithAccount(
    id: string
  ): Promise<ServiceResponse<NotificationWithAccount>> {
    try {
      const { data, error } = await this.supabase
        .from("notifications")
        .select(
          `
          *,
          accounts (
            id,
            email,
            users (
              id,
              name,
              avatar
            )
          )
        `
        )
        .eq("id", id)
        .single();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to get notification with account",
        success: false,
      };
    }
  }

  // Get all notifications with account information
  async getAllNotificationsWithAccounts(
    params?: QueryParams
  ): Promise<ServiceListResponse<NotificationWithAccount>> {
    try {
      let query = this.supabase.from("notifications").select(
        `
          *,
          accounts (
            id,
            email,
            users (
              id,
              name,
              avatar
            )
          )
        `,
        { count: "exact" }
      );

      // Apply filters
      if (params?.filters) {
        query = this.applyFilters(query, params.filters);
      }

      // Apply sorting
      if (params?.column) {
        query = query.order(params.column, {
          ascending: params.ascending ?? true,
        });
      } else {
        query = query.order("created_at", { ascending: false });
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit);
        if (params.offset) {
          query = query.range(params.offset, params.offset + params.limit - 1);
        } else if (params.page && params.page > 1) {
          const offset = (params.page - 1) * params.limit;
          query = query.range(offset, offset + params.limit - 1);
        }
      }

      const { data, error, count } = await query;

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false,
        };
      }

      return {
        data: data || [],
        error: null,
        success: true,
        count: count || 0,
      };
    } catch (error: any) {
      return {
        data: [],
        error: error.message || "Failed to get notifications with accounts",
        success: false,
      };
    }
  }

  // Get notifications for a specific account
  async getNotificationsByAccount(
    accountId: string,
    params?: QueryParams
  ): Promise<ServiceListResponse<NotificationWithAccount>> {
    const filters = { ...params?.filters, account_id: accountId };
    return this.getAllNotificationsWithAccounts({ ...params, filters });
  }

  // Get unread notifications for an account
  async getUnreadNotificationsByAccount(
    accountId: string,
    params?: QueryParams
  ): Promise<ServiceListResponse<NotificationWithAccount>> {
    const filters = {
      ...params?.filters,
      account_id: accountId,
      status: "UNREAD" as const,
    };
    return this.getAllNotificationsWithAccounts({ ...params, filters });
  }

  // Get read notifications for an account
  async getReadNotificationsByAccount(
    accountId: string,
    params?: QueryParams
  ): Promise<ServiceListResponse<NotificationWithAccount>> {
    const filters = {
      ...params?.filters,
      account_id: accountId,
    };
    return this.getAllNotificationsWithAccounts({ ...params, filters });
  }

  // Create notification
  async createNotification(
    data: NotificationInsert
  ): Promise<ServiceResponse<Notification>> {
    try {
      if (!data.to) data.to = "*";

      // Validate message length
      if (data.message && data.message.length > 255) {
        return {
          data: null,
          error: "Message too long (max 255 characters)",
          success: false,
        };
      }

      return this.create(data);
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to create notification",
        success: false,
      };
    }
  }

  // Mark notification as read
  async markAsRead(id: string): Promise<ServiceResponse<Notification>> {
    return this.updateStatus(id, "READ");
  }

  // Mark notification as unread
  async markAsUnread(id: string): Promise<ServiceResponse<Notification>> {
    return this.updateStatus(id, "UNREAD");
  }

  // Mark multiple notifications as read
  async markMultipleAsRead(ids: string[]): Promise<ServiceResponse<boolean>> {
    try {
      const updates = ids.map((id) => ({
        id,
        data: { status: "READ" as const },
      }));
      return this.bulkUpdate(updates);
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to mark notifications as read",
        success: false,
      };
    }
  }

  // Mark all notifications as read for an account
  async markAllAsReadForAccount(
    accountId: string
  ): Promise<ServiceResponse<boolean>> {
    try {
      const { error } = await this.supabase
        .from("notifications")
        .update({ status: "READ" })
        .eq("account_id", accountId)
        .eq("status", "UNREAD");

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: true,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to mark all notifications as read",
        success: false,
      };
    }
  }

  // Get notification count for account
  async getNotificationCountByAccount(
    accountId: string,
    includeRead: boolean = false
  ): Promise<
    ServiceResponse<{
      total: number;
      unread: number;
      read: number;
    }>
  > {
    try {
      // Get total count
      const { count: totalCount, error: totalError } = await this.supabase
        .from("notifications")
        .select("*", { count: "exact", head: true })
        .eq("account_id", accountId);

      if (totalError) {
        return {
          data: null,
          error: totalError.message,
          success: false,
        };
      }

      // Get unread count
      const { count: unreadCount, error: unreadError } = await this.supabase
        .from("notifications")
        .select("*", { count: "exact", head: true })
        .eq("account_id", accountId)
        .eq("status", "UNREAD");

      if (unreadError) {
        return {
          data: null,
          error: unreadError.message,
          success: false,
        };
      }

      const total = totalCount || 0;
      const unread = unreadCount || 0;
      const read = total - unread;

      return {
        data: {
          total,
          unread,
          read,
        },
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to get notification count",
        success: false,
      };
    }
  }

  // Search notifications by name or message
  async searchNotifications(
    searchTerm: string,
    params?: QueryParams
  ): Promise<ServiceListResponse<NotificationWithAccount>> {
    try {
      let query = this.supabase
        .from("notifications")
        .select(
          `
          *,
          accounts (
            id,
            email,
            users (
              id,
              name,
              avatar
            )
          )
        `,
          { count: "exact" }
        )
        .or(`name.ilike.%${searchTerm}%,message.ilike.%${searchTerm}%`);

      // Apply additional filters
      if (params?.filters) {
        query = this.applyFilters(query, params.filters);
      }

      // Apply sorting
      if (params?.column) {
        query = query.order(params.column, {
          ascending: params.ascending ?? true,
        });
      } else {
        query = query.order("created_at", { ascending: false });
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit);
        if (params.offset) {
          query = query.range(params.offset, params.offset + params.limit - 1);
        } else if (params.page && params.page > 1) {
          const offset = (params.page - 1) * params.limit;
          query = query.range(offset, offset + params.limit - 1);
        }
      }

      const { data, error, count } = await query;

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false,
        };
      }

      return {
        data: data || [],
        error: null,
        success: true,
        count: count || 0,
      };
    } catch (error: any) {
      return {
        data: [],
        error: error.message || "Failed to search notifications",
        success: false,
      };
    }
  }

  // Get notifications by entity (associatedTable and associatedId)
  async getNotificationsByEntity(
    tableName: string,
    entityId: string,
    params?: QueryParams
  ): Promise<ServiceListResponse<NotificationWithAccount>> {
    const filters = {
      ...params?.filters,
      associatedTable: tableName,
      associatedId: entityId,
    };
    return this.getAllNotificationsWithAccounts({ ...params, filters });
  }

  // Create entity-related notification with targeting
  async createEntityNotification(
    accountId: string,
    name: string,
    message: string,
    tableName: string,
    entityId: string,
    details?: any,
    to?: string // "*" for global, specific account_id for personalized
  ): Promise<ServiceResponse<Notification>> {
    return this.createNotification({
      account_id: accountId,
      name,
      message,
      associated_table: tableName,
      associated_id: entityId,
      details: details ? JSON.stringify(details) : undefined,
      status: "ACTIVE",
      to: to || "*",
    });
  }

  // Create targeted notification (new method with 'to' field support)
  async createTargetedNotification(data: {
    account_id?: string | null;
    name: string;
    message: string;
    associated_table?: string | null;
    associated_id?: string | null;
    details?: any;
    status?: string;
    to: string; // "*" for global, specific account_id for personalized
  }): Promise<ServiceResponse<Notification>> {
    try {
      // Validate message length
      if (data.message && data.message.length > 255) {
        return {
          data: null,
          error: "Message too long (max 255 characters)",
          success: false,
        };
      }

      // For now, we'll store the 'to' field in the details object
      // until the database schema is updated to include the 'to' column
      const notificationData = {
        account_id: data.account_id,
        name: data.name,
        message: data.message,
        associated_table: data.associated_table,
        associated_id: data.associated_id,
        status: data.status || ("UNREAD" as any),
        to: data.to || "*",
        details: JSON.stringify({
          ...(data.details || {}),
          to: data.to, // Store targeting info in details for now
        }),
      };

      return this.create(notificationData);
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to create targeted notification",
        success: false,
      };
    }
  }

  // Broadcast notification to multiple accounts
  async broadcastNotification(
    accountIds: string[],
    name: string,
    message: string,
    details?: any
  ): Promise<ServiceListResponse<Notification>> {
    try {
      const notifications: NotificationInsert[] = accountIds.map(
        (accountId) => ({
          account_id: accountId,
          name,
          message,
          details: details ? JSON.stringify(details) : undefined,
          status: "UNREAD",
          to: "*",
        })
      );

      return this.bulkCreate(notifications);
    } catch (error: any) {
      return {
        data: [],
        error: error.message || "Failed to broadcast notification",
        success: false,
      };
    }
  }

  // Get notification statistics
  async getNotificationStats(): Promise<
    ServiceResponse<{
      totalNotifications: number;
      activeNotifications: number;
      completedNotifications: number;
      notificationsByAccount: Array<{
        accountId: string;
        userEmail: string;
        userName: string;
        totalNotifications: number;
        unreadNotifications: number;
      }>;
    }>
  > {
    try {
      const { data: notifications, error } = await this.supabase
        .from("notifications")
        .select(
          `
          status,
          account_id,
          accounts (
            email,
            users (
              name
            )
          )
        `
        )
        .neq("status", "INACTIVE"); // Exclude deleted notifications

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      const totalNotifications = notifications?.length || 0;
      const activeNotifications =
        notifications?.filter((n) => n.status === "ACTIVE").length || 0;
      const completedNotifications = totalNotifications - activeNotifications;

      // Group by account
      const accountCounts = new Map<
        string,
        {
          email: string;
          name: string;
          total: number;
          unread: number;
        }
      >();

      notifications?.forEach((notification) => {
        if (notification.account_id && notification.accounts) {
          const accountId = notification.account_id;
          const email = (notification.accounts as any)?.email || "Unknown";
          const name = (notification.accounts as any)?.users?.name || "Unknown";

          const current = accountCounts.get(accountId) || {
            email,
            name,
            total: 0,
            unread: 0,
          };
          current.total += 1;
          if (notification.status === "ACTIVE") {
            current.unread += 1;
          }
          accountCounts.set(accountId, current);
        }
      });

      const notificationsByAccount = Array.from(accountCounts.entries()).map(
        ([accountId, data]) => ({
          accountId,
          userEmail: data.email,
          userName: data.name,
          totalNotifications: data.total,
          unreadNotifications: data.unread,
        })
      );

      return {
        data: {
          totalNotifications,
          activeNotifications,
          completedNotifications,
          notificationsByAccount,
        },
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to get notification statistics",
        success: false,
      };
    }
  }

  // Clean up old notifications (older than specified days)
  async cleanupOldNotifications(
    daysOld: number = 30
  ): Promise<ServiceResponse<number>> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      const { data, error } = await this.supabase
        .from("notifications")
        .delete()
        .lt("created_at", cutoffDate.toISOString())
        .select("id");

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: data?.length || 0,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to cleanup old notifications",
        success: false,
      };
    }
  }
}

// Export singleton instance
export const notificationService = new NotificationService();
