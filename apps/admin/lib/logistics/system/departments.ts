import { BaseService } from '../base/service'
import { 
  Department, 
  DepartmentInsert, 
  DepartmentUpdate,
  ServiceResponse,
  ServiceListResponse,
  QueryParams,
  RoleWithDepartment
} from '../types'

export class DepartmentService extends BaseService<Department, DepartmentInsert, DepartmentUpdate> {
  protected tableName = 'departments'

  // Get department with its roles
  async getDepartmentWithRoles(id: string): Promise<ServiceResponse<Department & { roles: RoleWithDepartment[] }>> {
    try {
      const { data, error } = await this.supabase
        .from('departments')
        .select(`
          *,
          roles (
            id,
            name,
            status,
            created_at,
            updated_at
          )
        `)
        .eq('id', id)
        .single()

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false
        }
      }

      return {
        data,
        error: null,
        success: true
      }
    } catch (error: any) {
      return {
        data: null,
        error: error.message || 'Failed to get department with roles',
        success: false
      }
    }
  }

  // Get all departments with their roles
  async getAllDepartmentsWithRoles(params?: QueryParams): Promise<ServiceListResponse<Department & { roles: RoleWithDepartment[] }>> {
    try {
      let query = this.supabase
        .from('departments')
        .select(`
          *,
          roles (
            id,
            name,
            status,
            created_at,
            updated_at
          )
        `, { count: 'exact' })

      // Apply filters
      if (params?.filters) {
        query = this.applyFilters(query, params.filters)
      }

      // Apply sorting
      if (params?.column) {
        query = query.order(params.column, { ascending: params.ascending ?? true })
      } else {
        query = query.order('name', { ascending: true })
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit)
        if (params.offset) {
          query = query.range(params.offset, params.offset + params.limit - 1)
        } else if (params.page && params.page > 1) {
          const offset = (params.page - 1) * params.limit
          query = query.range(offset, offset + params.limit - 1)
        }
      }

      const { data, error, count } = await query

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false
        }
      }

      return {
        data: data || [],
        error: null,
        success: true,
        count: count || 0
      }
    } catch (error: any) {
      return {
        data: [],
        error: error.message || 'Failed to get departments with roles',
        success: false
      }
    }
  }

  // Get active departments only
  async getActiveDepartments(params?: QueryParams): Promise<ServiceListResponse<Department>> {
    const filters = { ...params?.filters, status: 'ACTIVE' as const }
    return this.getAll({ ...params, filters })
  }

  // Check if department name exists
  async isDepartmentNameExists(name: string, excludeId?: string): Promise<ServiceResponse<boolean>> {
    try {
      let query = this.supabase
        .from('departments')
        .select('id')
        .eq('name', name)

      if (excludeId) {
        query = query.neq('id', excludeId)
      }

      const { data, error } = await query

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false
        }
      }

      return {
        data: (data || []).length > 0,
        error: null,
        success: true
      }
    } catch (error: any) {
      return {
        data: null,
        error: error.message || 'Failed to check department name',
        success: false
      }
    }
  }

  // Create department with validation
  async createDepartment(data: DepartmentInsert): Promise<ServiceResponse<Department>> {
    try {
      // Check if name already exists
      const nameExists = await this.isDepartmentNameExists(data.name)
      if (!nameExists.success) {
        return nameExists as ServiceResponse<Department>
      }

      if (nameExists.data) {
        return {
          data: null,
          error: 'Department name already exists',
          success: false
        }
      }

      return this.create(data)
    } catch (error: any) {
      return {
        data: null,
        error: error.message || 'Failed to create department',
        success: false
      }
    }
  }

  // Update department with validation
  async updateDepartment(id: string, data: DepartmentUpdate): Promise<ServiceResponse<Department>> {
    try {
      // Check if name already exists (excluding current department)
      if (data.name) {
        const nameExists = await this.isDepartmentNameExists(data.name, id)
        if (!nameExists.success) {
          return nameExists as ServiceResponse<Department>
        }

        if (nameExists.data) {
          return {
            data: null,
            error: 'Department name already exists',
            success: false
          }
        }
      }

      return this.update(id, data)
    } catch (error: any) {
      return {
        data: null,
        error: error.message || 'Failed to update department',
        success: false
      }
    }
  }

  // Get department statistics
  async getDepartmentStats(id: string): Promise<ServiceResponse<{
    totalRoles: number
    activeRoles: number
    totalAccounts: number
    activeAccounts: number
  }>> {
    try {
      // Get role counts
      const { data: roleData, error: roleError } = await this.supabase
        .from('roles')
        .select('status')
        .eq('department_id', id)

      if (roleError) {
        return {
          data: null,
          error: roleError.message,
          success: false
        }
      }

      const totalRoles = roleData?.length || 0
      const activeRoles = roleData?.filter(r => r.status === 'ACTIVE').length || 0

      // Get account counts through roles
      const { data: accountData, error: accountError } = await this.supabase
        .from('accounts')
        .select('status, roles!inner(department_id)')
        .eq('roles.department_id', id)

      if (accountError) {
        return {
          data: null,
          error: accountError.message,
          success: false
        }
      }

      const totalAccounts = accountData?.length || 0
      const activeAccounts = accountData?.filter(a => a.status === 'ACTIVE').length || 0

      return {
        data: {
          totalRoles,
          activeRoles,
          totalAccounts,
          activeAccounts
        },
        error: null,
        success: true
      }
    } catch (error: any) {
      return {
        data: null,
        error: error.message || 'Failed to get department statistics',
        success: false
      }
    }
  }

  // Search departments by name
  async searchDepartments(searchTerm: string, params?: QueryParams): Promise<ServiceListResponse<Department>> {
    return this.search(searchTerm, ['name'], params)
  }
}

// Export singleton instance
export const departmentService = new DepartmentService() 