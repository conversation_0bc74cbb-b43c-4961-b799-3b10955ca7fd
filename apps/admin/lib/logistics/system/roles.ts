import { BaseService } from '../base/service'
import { 
  Role, 
  RoleInsert, 
  RoleUpdate,
  RoleWithDepartment,
  ServiceResponse,
  ServiceListResponse,
  QueryParams
} from '../types'

export class RoleService extends BaseService<Role, RoleInsert, RoleUpdate> {
  protected tableName = 'roles'

  // Get role with department information
  async getRoleWithDepartment(id: string): Promise<ServiceResponse<RoleWithDepartment>> {
    try {
      const { data, error } = await this.supabase
        .from('roles')
        .select(`
          *,
          departments (
            id,
            name,
            status
          )
        `)
        .eq('id', id)
        .single()

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false
        }
      }

      return {
        data,
        error: null,
        success: true
      }
    } catch (error: any) {
      return {
        data: null,
        error: error.message || 'Failed to get role with department',
        success: false
      }
    }
  }

  // Get all roles with department information
  async getAllRolesWithDepartments(params?: QueryParams): Promise<ServiceListResponse<RoleWithDepartment>> {
    try {
      let query = this.supabase
        .from('roles')
        .select(`
          *,
          departments (
            id,
            name,
            status
          )
        `, { count: 'exact' })

      // Apply filters
      if (params?.filters) {
        query = this.applyFilters(query, params.filters)
      }

      // Apply sorting
      if (params?.column) {
        query = query.order(params.column, { ascending: params.ascending ?? true })
      } else {
        query = query.order('name', { ascending: true })
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit)
        if (params.offset) {
          query = query.range(params.offset, params.offset + params.limit - 1)
        } else if (params.page && params.page > 1) {
          const offset = (params.page - 1) * params.limit
          query = query.range(offset, offset + params.limit - 1)
        }
      }

      const { data, error, count } = await query

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false
        }
      }

      return {
        data: data || [],
        error: null,
        success: true,
        count: count || 0
      }
    } catch (error: any) {
      return {
        data: [],
        error: error.message || 'Failed to get roles with departments',
        success: false
      }
    }
  }

  // Get roles by department
  async getRolesByDepartment(departmentId: string, params?: QueryParams): Promise<ServiceListResponse<Role>> {
    const filters = { ...params?.filters, department_id: departmentId }
    return this.getAll({ ...params, filters })
  }

  // Get active roles by department
  async getActiveRolesByDepartment(departmentId: string, params?: QueryParams): Promise<ServiceListResponse<Role>> {
    const filters = { ...params?.filters, department_id: departmentId, status: 'ACTIVE' as const }
    return this.getAll({ ...params, filters })
  }

  // Get active roles only
  async getActiveRoles(params?: QueryParams): Promise<ServiceListResponse<RoleWithDepartment>> {
    const filters = { ...params?.filters, status: 'ACTIVE' as const }
    return this.getAllRolesWithDepartments({ ...params, filters })
  }

  // Check if role name exists within department
  async isRoleNameExistsInDepartment(name: string, departmentId: string, excludeId?: string): Promise<ServiceResponse<boolean>> {
    try {
      let query = this.supabase
        .from('roles')
        .select('id')
        .eq('name', name)
        .eq('department_id', departmentId)

      if (excludeId) {
        query = query.neq('id', excludeId)
      }

      const { data, error } = await query

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false
        }
      }

      return {
        data: (data || []).length > 0,
        error: null,
        success: true
      }
    } catch (error: any) {
      return {
        data: null,
        error: error.message || 'Failed to check role name',
        success: false
      }
    }
  }

  // Create role with validation
  async createRole(data: RoleInsert): Promise<ServiceResponse<Role>> {
    try {
      // Check if name already exists in department
      const nameExists = await this.isRoleNameExistsInDepartment(data.name, data.department_id)
      if (!nameExists.success) {
        return nameExists as ServiceResponse<Role>
      }

      if (nameExists.data) {
        return {
          data: null,
          error: 'Role name already exists in this department',
          success: false
        }
      }

      return this.create(data)
    } catch (error: any) {
      return {
        data: null,
        error: error.message || 'Failed to create role',
        success: false
      }
    }
  }

  // Update role with validation
  async updateRole(id: string, data: RoleUpdate): Promise<ServiceResponse<Role>> {
    try {
      // If updating name or department, check for conflicts
      if (data.name || data.department_id) {
        // Get current role to check department
        const currentRole = await this.getById(id)
        if (!currentRole.success || !currentRole.data) {
          return {
            data: null,
            error: 'Role not found',
            success: false
          }
        }

        const nameToCheck = data.name || currentRole.data.name
        const departmentToCheck = data.department_id || currentRole.data.department_id

        const nameExists = await this.isRoleNameExistsInDepartment(nameToCheck, departmentToCheck, id)
        if (!nameExists.success) {
          return nameExists as ServiceResponse<Role>
        }

        if (nameExists.data) {
          return {
            data: null,
            error: 'Role name already exists in this department',
            success: false
          }
        }
      }

      return this.update(id, data)
    } catch (error: any) {
      return {
        data: null,
        error: error.message || 'Failed to update role',
        success: false
      }
    }
  }

  // Get role statistics
  async getRoleStats(id: string): Promise<ServiceResponse<{
    totalAccounts: number
    activeAccounts: number
    inactiveAccounts: number
  }>> {
    try {
      const { data: accountData, error } = await this.supabase
        .from('accounts')
        .select('status')
        .eq('role_id', id)

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false
        }
      }

      const totalAccounts = accountData?.length || 0
      const activeAccounts = accountData?.filter(a => a.status === 'ACTIVE').length || 0
      const inactiveAccounts = totalAccounts - activeAccounts

      return {
        data: {
          totalAccounts,
          activeAccounts,
          inactiveAccounts
        },
        error: null,
        success: true
      }
    } catch (error: any) {
      return {
        data: null,
        error: error.message || 'Failed to get role statistics',
        success: false
      }
    }
  }

  // Search roles by name
  async searchRoles(searchTerm: string, params?: QueryParams): Promise<ServiceListResponse<RoleWithDepartment>> {
    try {
      let query = this.supabase
        .from('roles')
        .select(`
          *,
          departments (
            id,
            name,
            status
          )
        `, { count: 'exact' })
        .or(`name.ilike.%${searchTerm}%,departments.name.ilike.%${searchTerm}%`)

      // Apply additional filters
      if (params?.filters) {
        query = this.applyFilters(query, params.filters)
      }

      // Apply sorting
      if (params?.column) {
        query = query.order(params.column, { ascending: params.ascending ?? true })
      } else {
        query = query.order('name', { ascending: true })
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit)
        if (params.offset) {
          query = query.range(params.offset, params.offset + params.limit - 1)
        } else if (params.page && params.page > 1) {
          const offset = (params.page - 1) * params.limit
          query = query.range(offset, offset + params.limit - 1)
        }
      }

      const { data, error, count } = await query

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false
        }
      }

      return {
        data: data || [],
        error: null,
        success: true,
        count: count || 0
      }
    } catch (error: any) {
      return {
        data: [],
        error: error.message || 'Failed to search roles',
        success: false
      }
    }
  }

  // Get role by name and department
  async getRoleByNameAndDepartment(name: string, departmentId: string): Promise<ServiceResponse<Role>> {
    try {
      const { data, error } = await this.supabase
        .from('roles')
        .select('*')
        .eq('name', name)
        .eq('department_id', departmentId)
        .single()

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false
        }
      }

      return {
        data,
        error: null,
        success: true
      }
    } catch (error: any) {
      return {
        data: null,
        error: error.message || 'Failed to get role by name and department',
        success: false
      }
    }
  }

  // Bulk create roles for a department
  async bulkCreateRolesForDepartment(departmentId: string, roleNames: string[]): Promise<ServiceListResponse<Role>> {
    try {
      const rolesToCreate: RoleInsert[] = roleNames.map(name => ({
        name,
        department_id: departmentId,
        status: 'ACTIVE' as const
      }))

      return this.bulkCreate(rolesToCreate)
    } catch (error: any) {
      return {
        data: [],
        error: error.message || 'Failed to bulk create roles',
        success: false
      }
    }
  }
}

// Export singleton instance
export const roleService = new RoleService() 