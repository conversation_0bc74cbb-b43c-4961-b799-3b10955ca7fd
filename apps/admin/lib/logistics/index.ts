// Re-export all types
export * from "./types";

// Base service for extending
export { BaseService } from "./base/service";

// System module services
export { departmentService, DepartmentService } from "./system/departments";
export { roleService, RoleService } from "./system/roles";
export { userService, UserService } from "./system/users";
export { accountService, AccountService } from "./system/accounts";
export {
  notificationService,
  NotificationService,
} from "./system/notifications";
export { logService, LogService } from "./system/logs";
export { permissionService, PermissionService } from "./system/permissions";

// Operations module services
export { customerService, CustomerService } from "./operations/customers";
export { cargoService, CargoService } from "./operations/cargos";
export {
  invoiceService,
  type InvoiceData,
  type InvoiceInsert,
  type InvoiceUpdate,
  type InvoiceWithCustomer,
  type InvoiceWithRelations,
  type GeneratedInvoiceResult,
} from "./operations/invoices";
export {
  releaseAuthorizationService,
  ReleaseAuthorizationService,
} from "./operations/release-authorizations";
export { freightService, FreightService } from "./operations/freights";
export { batchService, BatchService } from "./operations/batches";
export { shipmentService, ShipmentService } from "./operations/shipments";
export { documentService, DocumentService } from "./operations/documents";
export {
  releaseDocumentService,
  ReleaseDocumentService,
} from "./operations/release-documents";
export {
  ledgerService,
  LedgerService,
  LEDGER_CATEGORIES,
  type Ledger,
  type LedgerInsert,
  type LedgerUpdate,
  type LedgerWithStats,
} from "./operations/ledgers";
export {
  transactionService,
  TransactionService,
} from "./operations/transactions";
export {
  taskService,
  TaskService,
  type Task,
  type TaskInsert,
  type TaskUpdate,
  type TaskWithAssignee,
  type TaskPriorityEnum,
} from "./operations/tasks";

export {
  supplierService,
  SupplierService,
  type Supplier,
  type SupplierInsert,
  type SupplierUpdate,
} from "./operations/suppliers";

export {
  bulkFetchService,
  BulkFetchService,
  DetailLevel,
  type BulkFetchOptions,
  type BulkFetchResult,
  type EntityType,
} from "./operations/bulk-fetch";
