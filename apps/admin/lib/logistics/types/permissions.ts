// Permission system types and interfaces

export type PermissionAction = "view" | "create" | "update" | "delete";

export type EntityType =
  | "users"
  | "accounts"
  | "roles"
  | "departments"
  | "customers"
  | "cargo"
  | "batches"
  | "freights"
  | "shipments"
  | "handovers"
  | "documents"
  | "notifications"
  | "tasks"
  | "logs"
  | "schedules"
  | "assignments"
  | "approvals"
  | "ledgers"
  | "transactions"
  | "suppliers"
  | "invoices"
  | "sessions"
  | "recoveries";

// Note: Permissions are now stored as JSONB in the roles.permissions column
// Format: {"entity": ["action1", "action2"]} e.g. {"users": ["view", "create"]}

export interface RolePermissions {
  role_id: string;
  role_name: string;
  department_name: string;
  permissions: {
    [entity in EntityType]?: {
      [action in PermissionAction]?: boolean;
    };
  };
}

export interface EntityPermissionGroup {
  entity: EntityType;
  displayName: string;
  description: string;
  category: "system" | "operations" | "finances";
  permissions: {
    [action in PermissionAction]: boolean;
  };
}

export interface PermissionCategory {
  name: string;
  displayName: string;
  description: string;
  entities: EntityType[];
}

// Predefined entity configurations
export const ENTITY_CONFIGS: Record<
  EntityType,
  {
    displayName: string;
    description: string;
    category: "system" | "operations" | "finances";
  }
> = {
  users: {
    displayName: "Users",
    description: "System user profiles and personal information",
    category: "system",
  },
  accounts: {
    displayName: "Accounts",
    description: "User authentication and login accounts",
    category: "system",
  },
  roles: {
    displayName: "Roles",
    description: "User roles and permission groups",
    category: "system",
  },
  departments: {
    displayName: "Departments",
    description: "Organizational departments and divisions",
    category: "system",
  },
  customers: {
    displayName: "Customers",
    description: "Customer profiles and contact information",
    category: "operations",
  },
  cargo: {
    displayName: "Cargo",
    description: "Individual cargo items and shipments",
    category: "operations",
  },
  batches: {
    displayName: "Batches",
    description: "Cargo batch consolidation and management",
    category: "operations",
  },
  freights: {
    displayName: "Freights",
    description: "Freight booking and transportation methods",
    category: "operations",
  },
  shipments: {
    displayName: "Shipments",
    description: "Shipment tracking and logistics coordination",
    category: "operations",
  },
  handovers: {
    displayName: "Handovers",
    description: "Cargo delivery and handover verification",
    category: "operations",
  },
  documents: {
    displayName: "Documents",
    description: "Document management and file storage",
    category: "system",
  },
  notifications: {
    displayName: "Notifications",
    description: "System notifications and alerts",
    category: "system",
  },
  logs: {
    displayName: "Logs",
    description: "System activity logs and audit trails",
    category: "system",
  },
  tasks: {
    displayName: "Tasks",
    description: "Task and resource assignments",
    category: "operations",
  },
  schedules: {
    displayName: "Schedules",
    description: "Scheduling and calendar management",
    category: "operations",
  },
  assignments: {
    displayName: "Assignments",
    description: "Task and resource assignments",
    category: "operations",
  },
  approvals: {
    displayName: "Approvals",
    description: "Approval workflows and processes",
    category: "operations",
  },
  ledgers: {
    displayName: "Ledgers",
    description: "Financial ledgers and accounting records",
    category: "finances",
  },
  transactions: {
    displayName: "Transactions",
    description: "Financial transactions and payments",
    category: "finances",
  },
  sessions: {
    displayName: "Sessions",
    description: "User login sessions and authentication",
    category: "system",
  },
  suppliers: {
    displayName: "Suppliers",
    description: "Supplier profiles and contact information",
    category: "operations",
  },
  invoices: {
    displayName: "Invoices",
    description: "Invoice management and billing operations",
    category: "finances",
  },
  recoveries: {
    displayName: "Recoveries",
    description: "Password recovery and account restoration",
    category: "system",
  },
};

// Permission categories for organization
export const PERMISSION_CATEGORIES: PermissionCategory[] = [
  {
    name: "system",
    displayName: "System Management",
    description: "Core system functionality and user management",
    entities: [
      "users",
      "accounts",
      "roles",
      "departments",
      "documents",
      "notifications",
      "logs",
      "sessions",
      "recoveries",
    ],
  },
  {
    name: "operations",
    displayName: "Operations Management",
    description: "Cargo and logistics operations",
    entities: [
      "customers",
      "cargo",
      "batches",
      "freights",
      "shipments",
      "handovers",
      "suppliers",
      "tasks",
      "schedules",
      "assignments",
      "approvals",
    ],
  },
  {
    name: "finances",
    displayName: "Financial Management",
    description: "Financial records and transactions",
    entities: ["ledgers", "transactions"],
  },
];

// Action configurations
export const ACTION_CONFIGS: Record<
  PermissionAction,
  {
    displayName: string;
    description: string;
    icon: string;
    color: string;
  }
> = {
  view: {
    displayName: "View",
    description: "Read and view records",
    icon: "Eye",
    color: "text-blue-600",
  },
  create: {
    displayName: "Create",
    description: "Add new records",
    icon: "Plus",
    color: "text-green-600",
  },
  update: {
    displayName: "Update",
    description: "Edit existing records",
    icon: "Edit",
    color: "text-yellow-600",
  },
  delete: {
    displayName: "Delete",
    description: "Remove records",
    icon: "Trash2",
    color: "text-red-600",
  },
};

// Default permissions for common roles
export const DEFAULT_ROLE_PERMISSIONS: Record<
  string,
  Partial<Record<EntityType, PermissionAction[]>>
> = {
  administrator: {
    // Full access to everything
    users: ["view", "create", "update", "delete"],
    accounts: ["view", "create", "update", "delete"],
    roles: ["view", "create", "update", "delete"],
    departments: ["view", "create", "update", "delete"],
    customers: ["view", "create", "update", "delete"],
    cargo: ["view", "create", "update", "delete"],
    batches: ["view", "create", "update", "delete"],
    freights: ["view", "create", "update", "delete"],
    shipments: ["view", "create", "update", "delete"],
    handovers: ["view", "create", "update", "delete"],
    documents: ["view", "create", "update", "delete"],
    notifications: ["view", "create", "update", "delete"],
    suppliers: ["view", "create", "update", "delete"],
    invoices: ["view", "create", "update", "delete"],
    tasks: ["view", "create", "update", "delete"],
    logs: ["view"],
    schedules: ["view", "create", "update", "delete"],
    assignments: ["view", "create", "update", "delete"],
    approvals: ["view", "create", "update", "delete"],
    ledgers: ["view", "create", "update", "delete"],
    transactions: ["view", "create", "update", "delete"],
    sessions: ["view", "delete"],
    recoveries: ["view", "delete"],
  },
  manager: {
    // Management level access
    users: ["view", "create", "update"],
    accounts: ["view", "update"],
    roles: ["view"],
    departments: ["view"],
    customers: ["view", "create", "update", "delete"],
    cargo: ["view", "create", "update", "delete"],
    batches: ["view", "create", "update", "delete"],
    freights: ["view", "create", "update", "delete"],
    shipments: ["view", "create", "update", "delete"],
    handovers: ["view", "create", "update"],
    documents: ["view", "create", "update"],
    suppliers: ["view", "create", "update", "delete"],
    invoices: ["view", "create", "update", "delete"],
    tasks: ["view", "create", "update", "delete"],
    notifications: ["view", "create"],
    logs: ["view"],
    schedules: ["view", "create", "update", "delete"],
    assignments: ["view", "create", "update", "delete"],
    approvals: ["view", "create", "update"],
    ledgers: ["view", "create", "update"],
    transactions: ["view", "create", "update"],
    sessions: ["view"],
    recoveries: ["view"],
  },
  operator: {
    // Operational level access
    customers: ["view", "create", "update"],
    cargo: ["view", "create", "update"],
    batches: ["view", "create", "update"],
    freights: ["view", "update"],
    shipments: ["view", "update"],
    handovers: ["view", "create", "update"],
    suppliers: ["view", "create", "update"],
    invoices: ["view", "create", "update"],
    tasks: ["view", "create", "update"],
    documents: ["view", "create"],
    notifications: ["view"],
    schedules: ["view", "update"],
    assignments: ["view", "update"],
    approvals: ["view"],
    ledgers: ["view"],
    transactions: ["view"],
  },
  staff: {
    // Basic staff access
    customers: ["view"],
    cargo: ["view", "create"],
    batches: ["view"],
    freights: ["view"],
    suppliers: ["view"],
    invoices: ["view"],
    tasks: ["view"],
    shipments: ["view"],
    handovers: ["view"],
    documents: ["view"],
    notifications: ["view"],
    schedules: ["view"],
    assignments: ["view"],
    approvals: ["view"],
    ledgers: ["view"],
    transactions: ["view"],
  },
};
