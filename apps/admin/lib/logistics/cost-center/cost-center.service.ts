import { BaseService } from "../base/service";
import { ServiceResponse } from "../types";

// Cost Center KPI Interfaces
export interface CostCenterKPIs {
  totalRevenue: number;
  totalExpenses: number;
  profitMargin: number;
  netSurplus: number; // Revenue - Expenses
  transactionCount: number;
  ledgerCount: number;
  averageTransactionValue: number;
}

export interface TransactionSummary {
  totalCredit: number;
  totalDebit: number;
  netBalance: number; // Credit - Debit
  transactionCount: number;
  transactions: any[];
}

export interface LedgerSummary {
  totalCredit: number;
  totalDebit: number;
  netBalance: number;
  ledgerCount: number;
  activeLedgers: number;
  ledgers: any[];
}

export interface InvoiceSummary {
  totalInvoices: number;
  totalAmount: number;
  paidAmount: number;
  pendingAmount: number;
  overdueCount: number;
  invoicesByStatus: Array<{ status: string; count: number; amount: number }>;
  invoices: any[];
}

export interface InvoiceSummary {
  totalInvoices: number;
  totalAmount: number;
  paidAmount: number;
  pendingAmount: number;
  invoicesByStatus: Array<{ status: string; count: number; amount: number }>;
  invoices: any[];
}

export interface CostCenterChartData {
  revenueVsExpenses: Array<{ name: string; revenue: number; expenses: number }>;
  transactionDistribution: Array<{
    name: string;
    value: number;
    color: string;
  }>;
  ledgerPerformance: Array<{
    name: string;
    credit: number;
    debit: number;
    net: number;
  }>;
  monthlyTrends: Array<{
    month: string;
    revenue: number;
    expenses: number;
    profit: number;
  }>;
  entityComparison: Array<{
    entity: string;
    revenue: number;
    expenses: number;
    transactions: number;
  }>;
}

export interface CostCenterAnalysis {
  kpis: CostCenterKPIs;
  transactions: TransactionSummary;
  ledgers: LedgerSummary;
  invoices: InvoiceSummary;
  charts: CostCenterChartData;
  entityInfo: {
    id: string;
    name: string;
    type: string;
    metadata?: Record<string, any>;
  };
}

export class CostCenterService extends BaseService<any, any, any> {
  protected tableName = "cost_center_analysis"; // Virtual table for analysis

  /**
   * Get comprehensive cost center analysis for a specific entity
   */
  async getCostCenterAnalysis(
    entityType: string,
    entityId: string,
    dateRange?: { from: string; to: string }
  ): Promise<ServiceResponse<CostCenterAnalysis>> {
    try {
      // Get entity information
      const entityInfo = await this.getEntityInfo(entityType, entityId);
      if (!entityInfo.success) {
        return {
          success: false,
          data: null,
          error: `Failed to fetch entity information: ${entityInfo.error}`,
        };
      }

      // Get associated ledgers
      const ledgersResult = await this.getEntityLedgers(
        entityType,
        entityId,
        dateRange
      );
      if (!ledgersResult.success) {
        return {
          success: false,
          data: null,
          error: `Failed to fetch ledgers: ${ledgersResult.error}`,
        };
      }

      // Get related invoices first (graceful fallback to empty data)
      const invoicesResult = await this.getEntityInvoices(
        entityType,
        entityId,
        dateRange
      );

      // Use empty invoice data if fetch fails
      const invoicesData =
        invoicesResult.success && invoicesResult.data
          ? invoicesResult.data
          : {
              totalInvoices: 0,
              totalAmount: 0,
              paidAmount: 0,
              pendingAmount: 0,
              overdueCount: 0,
              invoicesByStatus: [],
              invoices: [],
            };

      // Get comprehensive transaction data including invoice-related transactions
      const transactionsResult = await this.getComprehensiveEntityTransactions(
        entityType,
        entityId,
        ledgersResult.data?.ledgers.map((l: any) => l.id) || [],
        invoicesData.invoices.map((inv: any) => inv.id) || [],
        dateRange
      );

      if (!transactionsResult.success || !transactionsResult.data) {
        return {
          success: false,
          data: null,
          error: `Failed to fetch transactions: ${transactionsResult.error}`,
        };
      }

      // Ensure we have valid data before proceeding
      if (!ledgersResult.data || !transactionsResult.data) {
        return {
          success: false,
          data: null,
          error: "Missing ledger or transaction data",
        };
      }

      // Calculate KPIs
      const kpis = this.calculateKPIs(
        ledgersResult.data,
        transactionsResult.data
      );

      // Generate chart data
      const charts = await this.generateChartData(
        entityType,
        entityId,
        ledgersResult.data,
        transactionsResult.data,
        dateRange
      );

      const analysis: CostCenterAnalysis = {
        kpis,
        transactions: transactionsResult.data,
        ledgers: ledgersResult.data,
        invoices: invoicesData,
        charts,
        entityInfo: entityInfo.data,
      };

      return {
        success: true,
        data: analysis,
        error: null,
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: error.message || "Failed to generate cost center analysis",
      };
    }
  }

  /**
   * Get related invoices for a specific entity
   */
  private async getEntityInvoices(
    entityType: string,
    entityId: string,
    dateRange?: { from: string; to: string }
  ): Promise<ServiceResponse<InvoiceSummary>> {
    try {
      let query = this.supabase
        .from("invoices")
        .select(
          `
          *,
          customer:customer_id (
            id,
            name,
            email,
            phone,
            location
          ),
          supplier:supplier_id (
            id,
            tracking_number,
            phone,
            location
          )
        `
        )
        .neq("status", "INACTIVE"); // Exclude soft-deleted invoices

      // Filter invoices based on entity type
      switch (entityType) {
        case "customers":
          query = query.eq("customer_id", entityId);
          break;
        case "suppliers":
          query = query.eq("supplier_id", entityId);
          break;
        case "batches":
          // For batches, we need to find invoices through cargo relationships
          // First get cargo with invoice_id for this batch, then find those invoices
          const { data: cargoData } = await this.supabase
            .from("cargos")
            .select("invoice_id")
            .eq("batch_id", entityId)
            .not("invoice_id", "is", null);

          if (cargoData && cargoData.length > 0) {
            const invoiceIds = cargoData
              .map((c) => c.invoice_id)
              .filter((id) => id !== null);

            if (invoiceIds.length > 0) {
              query = query.in("id", invoiceIds);
            } else {
              // No invoices found for this batch, return empty result
              return {
                success: true,
                data: {
                  totalInvoices: 0,
                  totalAmount: 0,
                  paidAmount: 0,
                  pendingAmount: 0,
                  overdueCount: 0,
                  invoicesByStatus: [],
                  invoices: [],
                },
                error: null,
              };
            }
          } else {
            // No cargo found for this batch, return empty result
            return {
              success: true,
              data: {
                totalInvoices: 0,
                totalAmount: 0,
                paidAmount: 0,
                pendingAmount: 0,
                overdueCount: 0,
                invoicesByStatus: [],
                invoices: [],
              },
              error: null,
            };
          }
          break;
        case "freights":
          // For freights, find invoices through batches and cargo
          const { data: freightBatches } = await this.supabase
            .from("batches")
            .select("id")
            .eq("freight_id", entityId);

          if (freightBatches && freightBatches.length > 0) {
            const batchIds = freightBatches.map((b) => b.id);
            const { data: freightCargoData } = await this.supabase
              .from("cargos")
              .select("invoice_id")
              .in("batch_id", batchIds)
              .not("invoice_id", "is", null);

            if (freightCargoData && freightCargoData.length > 0) {
              const invoiceIds = freightCargoData
                .map((c) => c.invoice_id)
                .filter((id) => id !== null);

              if (invoiceIds.length > 0) {
                query = query.in("id", invoiceIds);
              } else {
                return {
                  success: true,
                  data: {
                    totalInvoices: 0,
                    totalAmount: 0,
                    paidAmount: 0,
                    pendingAmount: 0,
                    overdueCount: 0,
                    invoicesByStatus: [],
                    invoices: [],
                  },
                  error: null,
                };
              }
            } else {
              return {
                success: true,
                data: {
                  totalInvoices: 0,
                  totalAmount: 0,
                  paidAmount: 0,
                  pendingAmount: 0,
                  overdueCount: 0,
                  invoicesByStatus: [],
                  invoices: [],
                },
                error: null,
              };
            }
          } else {
            return {
              success: true,
              data: {
                totalInvoices: 0,
                totalAmount: 0,
                paidAmount: 0,
                pendingAmount: 0,
                overdueCount: 0,
                invoicesByStatus: [],
                invoices: [],
              },
              error: null,
            };
          }
          break;
        case "shipments":
          // For shipments, find invoices through freight, batches, and cargo
          const { data: shipmentData } = await this.supabase
            .from("shipments")
            .select("freight_id")
            .eq("id", entityId)
            .single();

          if (shipmentData?.freight_id) {
            // Recursively call for the freight
            return this.getEntityInvoices(
              "freights",
              shipmentData.freight_id,
              dateRange
            );
          } else {
            return {
              success: true,
              data: {
                totalInvoices: 0,
                totalAmount: 0,
                paidAmount: 0,
                pendingAmount: 0,
                overdueCount: 0,
                invoicesByStatus: [],
                invoices: [],
              },
              error: null,
            };
          }
        default:
          // For other entity types, return empty result
          return {
            success: true,
            data: {
              totalInvoices: 0,
              totalAmount: 0,
              paidAmount: 0,
              pendingAmount: 0,
              overdueCount: 0,
              invoicesByStatus: [],
              invoices: [],
            },
            error: null,
          };
      }

      // Apply date range filter if provided
      if (dateRange) {
        query = query
          .gte("created_at", dateRange.from)
          .lte("created_at", dateRange.to);
      }

      const { data: invoices, error } = await query.order("created_at", {
        ascending: false,
      });

      if (error) {
        return {
          success: false,
          data: null,
          error: error.message,
        };
      }

      // Calculate invoice summary
      const totalInvoices = invoices?.length || 0;
      const totalAmount =
        invoices?.reduce((sum, inv) => sum + (inv.total || 0), 0) || 0;
      const paidAmount =
        invoices
          ?.filter((inv) => inv.status === "PAID")
          .reduce((sum, inv) => sum + (inv.total || 0), 0) || 0;
      const pendingAmount = totalAmount - paidAmount;
      const overdueCount =
        invoices?.filter((inv) => {
          if (!inv.due_at || inv.status === "PAID") return false;
          return new Date(inv.due_at) < new Date();
        }).length || 0;

      // Group by status
      const statusGroups =
        invoices?.reduce(
          (acc, inv) => {
            const status = inv.status || "UNKNOWN";
            if (!acc[status]) {
              acc[status] = { count: 0, amount: 0 };
            }
            acc[status].count++;
            acc[status].amount += inv.total || 0;
            return acc;
          },
          {} as Record<string, { count: number; amount: number }>
        ) || {};

      const invoicesByStatus = Object.entries(statusGroups).map(
        ([status, data]) => ({
          status,
          count: data.count,
          amount: data.amount,
        })
      );

      const summary: InvoiceSummary = {
        totalInvoices,
        totalAmount,
        paidAmount,
        pendingAmount,
        overdueCount,
        invoicesByStatus,
        invoices: invoices || [],
      };

      return {
        success: true,
        data: summary,
        error: null,
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: error.message || "Failed to fetch entity invoices",
      };
    }
  }

  /**
   * Get entity information based on type and ID
   */
  private async getEntityInfo(
    entityType: string,
    entityId: string
  ): Promise<ServiceResponse<any>> {
    try {
      let query;
      let nameField = "name";

      switch (entityType) {
        case "customers":
          query = this.supabase
            .from("customers")
            .select("id, name, email, location, status")
            .eq("id", entityId)
            .single();
          break;
        case "batches":
          query = this.supabase
            .from("batches")
            .select("id, code, status, weight, freight_id, freights(name)")
            .eq("id", entityId)
            .single();
          nameField = "code";
          break;
        case "freights":
          query = this.supabase
            .from("freights")
            .select("id, name, code, type, origin, destination, status")
            .eq("id", entityId)
            .single();
          break;
        case "shipments":
          query = this.supabase
            .from("shipments")
            .select(
              "id, tracking_number, freight_id, freights(name, destination)"
            )
            .eq("id", entityId)
            .single();
          nameField = "tracking_number";
          break;
        case "suppliers":
          // Use type assertion to handle suppliers table
          query = (this.supabase as any)
            .from("suppliers")
            .select("id, tracking_number, phone, location, status")
            .eq("id", entityId)
            .single();
          nameField = "tracking_number";
          break;
        case "ledgers":
          query = this.supabase
            .from("ledgers")
            .select("id, name, status, associated_id, associated_table")
            .eq("id", entityId)
            .single();
          break;
        case "transactions":
          query = this.supabase
            .from("transactions")
            .select("id, name, amount, value, status, ledger_id, ledgers(name)")
            .eq("id", entityId)
            .single();
          break;
        default:
          return {
            success: false,
            data: null,
            error: `Unsupported entity type: ${entityType}`,
          };
      }

      const { data, error } = await query;

      if (error) {
        return {
          success: false,
          data: null,
          error: error.message,
        };
      }

      // Handle name resolution based on entity type
      let entityName = `${entityType} ${data.id}`;

      if (nameField && (data as any)[nameField]) {
        entityName = (data as any)[nameField];
      } else if ((data as any).name) {
        entityName = (data as any).name;
      } else if ((data as any).company_name) {
        entityName = (data as any).company_name;
      }

      return {
        success: true,
        data: {
          id: data.id,
          name: entityName,
          type: entityType,
          metadata: data,
        },
        error: null,
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: error.message || "Failed to fetch entity information",
      };
    }
  }

  /**
   * Get ledgers associated with an entity
   */
  private async getEntityLedgers(
    entityType: string,
    entityId: string,
    dateRange?: { from: string; to: string }
  ): Promise<ServiceResponse<LedgerSummary>> {
    try {
      // Try both singular and plural forms of entity type
      const possibleTableNames = [
        entityType,
        entityType.endsWith("s") ? entityType.slice(0, -1) : entityType + "s",
      ];

      let ledgers: any[] = [];
      let error: any = null;

      // Try each possible table name
      for (const tableName of possibleTableNames) {
        let query = this.supabase
          .from("ledgers")
          .select("*")
          .eq("associated_table", tableName)
          .eq("associated_id", entityId)
          .neq("status", "INACTIVE");

        if (dateRange) {
          query = query
            .gte("created_at", dateRange.from)
            .lte("created_at", dateRange.to);
        }

        const result = await query;

        if (result.data && result.data.length > 0) {
          ledgers = result.data;
          error = null;
          break; // Found ledgers, stop searching
        } else if (result.error) {
          error = result.error;
        }
      }

      // If no ledgers found with any table name, return empty result
      if (ledgers.length === 0 && !error) {
        return {
          success: true,
          data: {
            totalCredit: 0,
            totalDebit: 0,
            netBalance: 0,
            ledgerCount: 0,
            activeLedgers: 0,
            ledgers: [],
          },
          error: null,
        };
      }

      if (error) {
        return {
          success: false,
          data: null,
          error: error.message,
        };
      }

      // Calculate ledger summaries by fetching transactions for each ledger
      let totalCredit = 0;
      let totalDebit = 0;

      // For each ledger, calculate its credit/debit from transactions
      for (const ledger of ledgers) {
        try {
          const { data: ledgerTransactions } = await this.supabase
            .from("transactions")
            .select("amount, value")
            .eq("ledger_id", ledger.id)
            .neq("status", "INACTIVE");

          if (ledgerTransactions) {
            const ledgerCredit = ledgerTransactions
              .filter((t) => (t.amount || 0) > 0)
              .reduce((sum, t) => sum + (t.amount || 0), 0);
            const ledgerDebit = ledgerTransactions
              .filter((t) => (t.value || 0) > 0)
              .reduce((sum, t) => sum + (t.value || 0), 0);

            totalCredit += ledgerCredit;
            totalDebit += ledgerDebit;

            // Add calculated values to ledger object for chart data
            (ledger as any).total_credit = ledgerCredit;
            (ledger as any).total_debit = ledgerDebit;
          }
        } catch (error) {
          // Set default values if transaction fetch fails
          (ledger as any).total_credit = 0;
          (ledger as any).total_debit = 0;
        }
      }

      const activeLedgers = ledgers.filter((l) => l.status === "ACTIVE").length;

      const summary: LedgerSummary = {
        totalCredit,
        totalDebit,
        netBalance: totalCredit - totalDebit,
        ledgerCount: ledgers.length,
        activeLedgers,
        ledgers,
      };

      return {
        success: true,
        data: summary,
        error: null,
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: error.message || "Failed to fetch entity ledgers",
      };
    }
  }

  /**
   * Get transactions for specific ledgers
   */
  private async getEntityTransactions(
    ledgerIds: string[],
    dateRange?: { from: string; to: string }
  ): Promise<ServiceResponse<TransactionSummary>> {
    try {
      if (ledgerIds.length === 0) {
        return {
          success: true,
          data: {
            totalCredit: 0,
            totalDebit: 0,
            netBalance: 0,
            transactionCount: 0,
            transactions: [],
          },
          error: null,
        };
      }

      let query = this.supabase
        .from("transactions")
        .select(
          `
          *,
          ledgers (
            id,
            name,
            books
          )
        `
        )
        .in("ledger_id", ledgerIds)
        .neq("status", "INACTIVE");

      if (dateRange) {
        query = query
          .gte("created_at", dateRange.from)
          .lte("created_at", dateRange.to);
      }

      const { data: transactions, error } = await query;

      if (error) {
        return {
          success: false,
          data: null,
          error: error.message,
        };
      }

      // Calculate transaction summaries based on transaction type
      // Credit transactions represent income/revenue
      const totalCredit = transactions
        .filter((t) => (t as any).type === "CREDIT")
        .reduce((sum, t) => sum + Math.abs(t.amount || 0), 0);

      // Debit transactions represent expenses/outgoing
      const totalDebit = transactions
        .filter((t) => (t as any).type === "DEBIT")
        .reduce((sum, t) => sum + Math.abs(t.amount || 0), 0);

      const summary: TransactionSummary = {
        totalCredit,
        totalDebit,
        netBalance: totalCredit - totalDebit,
        transactionCount: transactions.length,
        transactions,
      };

      return {
        success: true,
        data: summary,
        error: null,
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: error.message || "Failed to fetch entity transactions",
      };
    }
  }

  /**
   * Get transactions directly associated with an entity (fallback method)
   */
  private async getDirectEntityTransactions(
    entityType: string,
    entityId: string,
    dateRange?: { from: string; to: string }
  ): Promise<ServiceResponse<TransactionSummary>> {
    try {
      // Try both singular and plural forms of entity type
      const possibleTableNames = [
        entityType,
        entityType.endsWith("s") ? entityType.slice(0, -1) : entityType + "s",
      ];

      let transactions: any[] = [];

      // Try each possible table name
      for (const tableName of possibleTableNames) {
        let query = this.supabase
          .from("transactions")
          .select("*")
          .eq("associated_table", tableName)
          .eq("associated_id", entityId)
          .neq("status", "INACTIVE");

        if (dateRange) {
          query = query
            .gte("created_at", dateRange.from)
            .lte("created_at", dateRange.to);
        }

        const result = await query;

        if (result.data && result.data.length > 0) {
          transactions = [...transactions, ...result.data];
        }
      }

      // Calculate transaction summaries based on transaction type
      const totalCredit = transactions
        .filter((t) => (t as any).type === "CREDIT")
        .reduce((sum, t) => sum + Math.abs(t.amount || 0), 0);

      const totalDebit = transactions
        .filter((t) => (t as any).type === "DEBIT")
        .reduce((sum, t) => sum + Math.abs(t.amount || 0), 0);

      const summary: TransactionSummary = {
        totalCredit,
        totalDebit,
        netBalance: totalCredit - totalDebit,
        transactionCount: transactions.length,
        transactions,
      };

      return {
        success: true,
        data: summary,
        error: null,
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: error.message || "Failed to fetch direct entity transactions",
      };
    }
  }

  /**
   * Get comprehensive transaction data including entity, ledger, and invoice-related transactions
   */
  private async getComprehensiveEntityTransactions(
    entityType: string,
    entityId: string,
    ledgerIds: string[],
    invoiceIds: string[],
    dateRange?: { from: string; to: string }
  ): Promise<ServiceResponse<TransactionSummary>> {
    try {
      let allTransactions: any[] = [];

      // 1. Get transactions from entity ledgers
      if (ledgerIds.length > 0) {
        const ledgerTransactionsResult = await this.getEntityTransactions(
          ledgerIds,
          dateRange
        );
        if (ledgerTransactionsResult.success && ledgerTransactionsResult.data) {
          allTransactions = [
            ...allTransactions,
            ...ledgerTransactionsResult.data.transactions,
          ];
        }
      }

      // 2. Get direct entity-associated transactions
      const directTransactionsResult = await this.getDirectEntityTransactions(
        entityType,
        entityId,
        dateRange
      );
      if (directTransactionsResult.success && directTransactionsResult.data) {
        allTransactions = [
          ...allTransactions,
          ...directTransactionsResult.data.transactions,
        ];
      }

      // 3. Get invoice-related transactions
      if (invoiceIds.length > 0) {
        const invoiceTransactionsResult =
          await this.getInvoiceRelatedTransactions(invoiceIds, dateRange);
        if (
          invoiceTransactionsResult.success &&
          invoiceTransactionsResult.data
        ) {
          allTransactions = [
            ...allTransactions,
            ...invoiceTransactionsResult.data.transactions,
          ];
        }
      }

      // 4. Remove duplicates based on transaction ID
      const uniqueTransactions = allTransactions.filter(
        (transaction, index, self) =>
          index === self.findIndex((t) => t.id === transaction.id)
      );

      // 5. Sort by creation date (newest first)
      uniqueTransactions.sort(
        (a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );

      // 6. Calculate comprehensive summaries
      const totalCredit = uniqueTransactions
        .filter((t) => (t as any).type === "CREDIT")
        .reduce((sum, t) => sum + Math.abs(t.amount || 0), 0);

      const totalDebit = uniqueTransactions
        .filter((t) => (t as any).type === "DEBIT")
        .reduce((sum, t) => sum + Math.abs(t.amount || 0), 0);

      const summary: TransactionSummary = {
        totalCredit,
        totalDebit,
        netBalance: totalCredit - totalDebit,
        transactionCount: uniqueTransactions.length,
        transactions: uniqueTransactions,
      };

      return {
        success: true,
        data: summary,
        error: null,
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error:
          error.message || "Failed to fetch comprehensive entity transactions",
      };
    }
  }

  /**
   * Get transactions related to specific invoices
   */
  private async getInvoiceRelatedTransactions(
    invoiceIds: string[],
    dateRange?: { from: string; to: string }
  ): Promise<ServiceResponse<TransactionSummary>> {
    try {
      if (invoiceIds.length === 0) {
        return {
          success: true,
          data: {
            totalCredit: 0,
            totalDebit: 0,
            netBalance: 0,
            transactionCount: 0,
            transactions: [],
          },
          error: null,
        };
      }

      let query = this.supabase
        .from("transactions")
        .select(
          `
          *,
          ledgers (
            id,
            name,
            books
          )
        `
        )
        .eq("associated_table", "invoices")
        .in("associated_id", invoiceIds)
        .neq("status", "INACTIVE");

      if (dateRange) {
        query = query
          .gte("created_at", dateRange.from)
          .lte("created_at", dateRange.to);
      }

      const { data: transactions, error } = await query;

      if (error) {
        return {
          success: false,
          data: null,
          error: error.message,
        };
      }

      // Calculate transaction summaries
      const totalCredit = (transactions || [])
        .filter((t) => (t as any).type === "CREDIT")
        .reduce((sum, t) => sum + Math.abs(t.amount || 0), 0);

      const totalDebit = (transactions || [])
        .filter((t) => (t as any).type === "DEBIT")
        .reduce((sum, t) => sum + Math.abs(t.amount || 0), 0);

      const summary: TransactionSummary = {
        totalCredit,
        totalDebit,
        netBalance: totalCredit - totalDebit,
        transactionCount: transactions?.length || 0,
        transactions: transactions || [],
      };

      return {
        success: true,
        data: summary,
        error: null,
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: error.message || "Failed to fetch invoice-related transactions",
      };
    }
  }

  /**
   * Calculate KPIs from ledger and transaction data
   */
  private calculateKPIs(
    ledgerSummary: LedgerSummary,
    transactionSummary: TransactionSummary
  ): CostCenterKPIs {
    // Revenue is total DEBIT transactions (as per user requirement)
    const totalRevenue = transactionSummary.totalDebit;

    // Expenses are total CREDIT transactions (as per user requirement)
    const totalExpenses = transactionSummary.totalCredit;

    // Calculate profit margin
    const profitMargin =
      totalRevenue > 0
        ? ((totalRevenue - totalExpenses) / totalRevenue) * 100
        : 0;

    // Net surplus/deficit
    const netSurplus = totalRevenue - totalExpenses;

    // Average transaction value
    const averageTransactionValue =
      transactionSummary.transactionCount > 0
        ? (transactionSummary.totalCredit + transactionSummary.totalDebit) /
          transactionSummary.transactionCount
        : 0;

    return {
      totalRevenue,
      totalExpenses,
      profitMargin,
      netSurplus,
      transactionCount: transactionSummary.transactionCount,
      ledgerCount: ledgerSummary.ledgerCount,
      averageTransactionValue,
    };
  }

  /**
   * Generate chart data for cost center analysis
   */
  private async generateChartData(
    entityType: string,
    entityId: string,
    ledgerSummary: LedgerSummary,
    transactionSummary: TransactionSummary,
    dateRange?: { from: string; to: string }
  ): Promise<CostCenterChartData> {
    // Revenue vs Expenses Chart (Updated to match KPI logic)
    const revenueVsExpenses = [
      {
        name: "Financial Overview",
        revenue: transactionSummary.totalDebit, // Revenue = DEBIT transactions
        expenses: transactionSummary.totalCredit, // Expenses = CREDIT transactions
      },
    ];

    // Transaction Distribution (Pie Chart) - Updated labels to match business logic
    const transactionDistribution = [
      {
        name: "Revenue (Debit)",
        value: transactionSummary.totalDebit,
        color: "#10B981", // Green for revenue
      },
      {
        name: "Expenses (Credit)",
        value: transactionSummary.totalCredit,
        color: "#EF4444", // Red for expenses
      },
    ].filter((item) => item.value > 0); // Only include non-zero values

    // Ledger Performance
    const ledgerPerformance = ledgerSummary.ledgers.map((ledger: any) => ({
      name: ledger.name || `Ledger ${ledger.id}`,
      credit: ledger.total_credit || 0,
      debit: ledger.total_debit || 0,
      net: (ledger.total_credit || 0) - (ledger.total_debit || 0),
    }));

    // Monthly Trends (simplified for now)
    const monthlyTrends = [
      {
        month: "Current",
        revenue: transactionSummary.totalCredit,
        expenses: transactionSummary.totalDebit,
        profit: transactionSummary.totalCredit - transactionSummary.totalDebit,
      },
    ];

    // Entity Comparison (simplified for now)
    const entityComparison = [
      {
        entity: "Current Entity",
        revenue: transactionSummary.totalCredit,
        expenses: transactionSummary.totalDebit,
        transactions: transactionSummary.transactionCount,
      },
    ];

    return {
      revenueVsExpenses,
      transactionDistribution,
      ledgerPerformance,
      monthlyTrends,
      entityComparison,
    };
  }

  /**
   * Get cost center summary for multiple entities (for dashboard overview)
   */
  async getCostCenterSummary(
    entityType: string,
    entityIds: string[],
    dateRange?: { from: string; to: string }
  ): Promise<
    ServiceResponse<{
      totalRevenue: number;
      totalExpenses: number;
      totalProfit: number;
      entityCount: number;
      topPerformers: Array<{ id: string; name: string; profit: number }>;
    }>
  > {
    try {
      let totalRevenue = 0;
      let totalExpenses = 0;
      const performers = [];

      for (const entityId of entityIds) {
        const analysis = await this.getCostCenterAnalysis(
          entityType,
          entityId,
          dateRange
        );
        if (analysis.success && analysis.data) {
          totalRevenue += analysis.data.kpis.totalRevenue;
          totalExpenses += analysis.data.kpis.totalExpenses;
          performers.push({
            id: entityId,
            name: analysis.data.entityInfo.name,
            profit: analysis.data.kpis.netSurplus,
          });
        }
      }

      // Sort performers by profit
      const topPerformers = performers
        .sort((a, b) => b.profit - a.profit)
        .slice(0, 5);

      return {
        success: true,
        data: {
          totalRevenue,
          totalExpenses,
          totalProfit: totalRevenue - totalExpenses,
          entityCount: entityIds.length,
          topPerformers,
        },
        error: null,
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: error.message || "Failed to generate cost center summary",
      };
    }
  }

  /**
   * Get cost center insights with recommendations
   */
  async getCostCenterInsights(
    entityType: string,
    entityId: string,
    dateRange?: { from: string; to: string }
  ): Promise<
    ServiceResponse<{
      insights: string[];
      recommendations: string[];
      riskFactors: string[];
      performanceScore: number;
    }>
  > {
    try {
      const analysis = await this.getCostCenterAnalysis(
        entityType,
        entityId,
        dateRange
      );

      if (!analysis.success || !analysis.data) {
        return {
          success: false,
          data: null,
          error: "Failed to get analysis data for insights",
        };
      }

      const { kpis, transactions, ledgers } = analysis.data;
      const insights = [];
      const recommendations = [];
      const riskFactors = [];

      // Generate insights based on KPIs
      if (kpis.profitMargin > 20) {
        insights.push(
          `Excellent profit margin of ${kpis.profitMargin.toFixed(1)}%`
        );
      } else if (kpis.profitMargin > 10) {
        insights.push(`Good profit margin of ${kpis.profitMargin.toFixed(1)}%`);
      } else if (kpis.profitMargin > 0) {
        insights.push(`Low profit margin of ${kpis.profitMargin.toFixed(1)}%`);
        recommendations.push("Consider cost optimization strategies");
      } else {
        insights.push(
          `Operating at a loss with ${kpis.profitMargin.toFixed(1)}% margin`
        );
        riskFactors.push("Negative profit margin indicates financial stress");
        recommendations.push(
          "Urgent review of expenses and revenue streams needed"
        );
      }

      // Transaction analysis
      if (transactions.transactionCount > 50) {
        insights.push(
          `High transaction volume with ${transactions.transactionCount} transactions`
        );
      } else if (transactions.transactionCount < 10) {
        insights.push(
          `Low transaction activity with only ${transactions.transactionCount} transactions`
        );
        recommendations.push(
          "Consider strategies to increase transaction volume"
        );
      }

      // Ledger analysis
      if (ledgers.activeLedgers < ledgers.ledgerCount) {
        riskFactors.push(
          `${ledgers.ledgerCount - ledgers.activeLedgers} inactive ledgers detected`
        );
        recommendations.push("Review and activate or archive inactive ledgers");
      }

      // Calculate performance score (0-100)
      let performanceScore = 50; // Base score

      if (kpis.profitMargin > 0) performanceScore += 20;
      if (kpis.profitMargin > 10) performanceScore += 15;
      if (kpis.profitMargin > 20) performanceScore += 15;
      if (transactions.transactionCount > 20) performanceScore += 10;
      if (ledgers.activeLedgers === ledgers.ledgerCount) performanceScore += 10;

      performanceScore = Math.min(100, Math.max(0, performanceScore));

      return {
        success: true,
        data: {
          insights,
          recommendations,
          riskFactors,
          performanceScore,
        },
        error: null,
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: error.message || "Failed to generate cost center insights",
      };
    }
  }
}

// Export service instance
export const costCenterService = new CostCenterService();
