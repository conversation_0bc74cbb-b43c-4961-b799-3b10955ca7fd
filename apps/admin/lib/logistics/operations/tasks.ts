import { BaseService } from "../base/service";
import {
  ServiceResponse,
  ServiceListResponse,
  StatusEnum,
  CategoryEnum,
} from "../types";
import { notificationService } from "../system/notifications";

// Task priority enum based on database schema
export type TaskPriorityEnum = "LOW" | "NORMAL" | "HIGH" | "URGENT";

// Task interfaces based on database schema
export interface Task {
  id: string;
  name: string;
  status: StatusEnum;
  category: CategoryEnum | null;
  priority: TaskPriorityEnum | null;
  assignee: string | null; // UUID reference to accounts table
  account_id: string | null; // UUID reference to accounts table (task creator)
  start: string | null; // timestamp
  due: string | null; // timestamp
  associated_table: string | null;
  associated_id: string | null;
  associated_ids: string[] | null; // Array of associated entity IDs
  created_at: string;
  updated_at: string;
}

export interface TaskInsert {
  name: string;
  status?: StatusEnum;
  category?: CategoryEnum | null;
  priority?: TaskPriorityEnum | null;
  assignee?: string | null;
  account_id?: string | null;
  start?: string | null;
  due?: string | null;
  associated_table?: string | null;
  associated_id?: string | null;
  associated_ids?: string[] | null; // Array of associated entity IDs
}

export interface TaskUpdate {
  name?: string;
  status?: StatusEnum;
  category?: CategoryEnum | null;
  priority?: TaskPriorityEnum | null;
  assignee?: string | null;
  account_id?: string | null;
  start?: string | null;
  due?: string | null;
  associated_table?: string | null;
  associated_id?: string | null;
  associated_ids?: string[] | null; // Array of associated entity IDs
}

// Task with assignee details and entity information
export interface TaskWithAssignee extends Task {
  assignee_details?: {
    id: string;
    email: string;
    user?: {
      id: string;
      name: string;
      phone?: string;
    };
  };
  creator_details?: {
    id: string;
    email: string;
    user?: {
      id: string;
      name: string;
      phone?: string;
    };
  };
  entity_details?: {
    name: string;
    type: string;
    identifier?: string; // tracking_number, code, etc.
  };
  // Additional details for multiple associations
  entities_details?: Array<{
    id: string;
    name: string;
    type: string;
    identifier?: string;
  }>;
}

export class TaskService extends BaseService<Task, TaskInsert, TaskUpdate> {
  protected tableName = "tasks";

  /**
   * Create notification for task assigned
   */
  private async createTaskAssignedNotification(
    task: TaskWithAssignee
  ): Promise<void> {
    if (!task.assignee) return;

    try {
      const creatorName =
        task.creator_details?.user?.name ||
        task.creator_details?.email ||
        "Someone";
      const message = `You have been assigned a new task: "${task.name}" by ${creatorName}`;

      await notificationService.createTargetedNotification({
        account_id: task.account_id, // Creator's account
        name: "Task Assigned",
        message,
        associated_table: "tasks",
        associated_id: task.id,
        to: task.assignee, // Map assignee to "to" field
        details: {
          taskId: task.id,
          taskName: task.name,
          priority: task.priority,
          dueDate: task.due,
          assignedBy: creatorName,
          assigneeId: task.assignee, // Store assignee ID for reference
          type: "task_assigned",
          timestamp: new Date().toISOString(),
        },
      });

      console.log(`✅ Task assigned notification sent for task: ${task.name}`);
    } catch (error) {
      console.error("Error creating task assigned notification:", error);
    }
  }

  /**
   * Create notification for task updated
   */
  private async createTaskUpdatedNotification(
    task: TaskWithAssignee,
    changes: string[]
  ): Promise<void> {
    if (!task.assignee || changes.length === 0) return;

    try {
      const changesText = ` (${changes.join(", ")})`;
      const message = `Task "${task.name}" has been updated${changesText}`;

      await notificationService.createTargetedNotification({
        account_id: task.account_id, // Creator's account
        name: "Task Updated",
        message,
        associated_table: "tasks",
        associated_id: task.id,
        to: task.assignee, // Map assignee to "to" field
        details: {
          taskId: task.id,
          taskName: task.name,
          changes,
          priority: task.priority,
          status: task.status,
          dueDate: task.due,
          assigneeId: task.assignee, // Store assignee ID for reference
          type: "task_updated",
          timestamp: new Date().toISOString(),
        },
      });

      console.log(`✅ Task updated notification sent for task: ${task.name}`);
    } catch (error) {
      console.error("Error creating task updated notification:", error);
    }
  }

  /**
   * Create notification for task deleted
   */
  private async createTaskDeletedNotification(
    task: TaskWithAssignee
  ): Promise<void> {
    if (!task.assignee) return;

    try {
      const message = `Task "${task.name}" has been deleted`;

      await notificationService.createTargetedNotification({
        account_id: task.account_id, // Creator's account
        name: "Task Deleted",
        message,
        associated_table: "tasks",
        associated_id: task.id,
        to: task.assignee, // Map assignee to "to" field
        details: {
          taskId: task.id,
          taskName: task.name,
          priority: task.priority,
          assigneeId: task.assignee, // Store assignee ID for reference
          type: "task_deleted",
          timestamp: new Date().toISOString(),
        },
      });

      console.log(`✅ Task deleted notification sent for task: ${task.name}`);
    } catch (error) {
      console.error("Error creating task deleted notification:", error);
    }
  }

  /**
   * Create a new task with proper account_id association
   * @param data - Task data to insert
   * @param currentUserId - ID of the user creating the task (will be set as account_id)
   * @returns Promise<ServiceResponse<Task>>
   */
  async createTask(
    data: TaskInsert,
    currentUserId?: string
  ): Promise<ServiceResponse<Task>> {
    try {
      // Validate that we have a user ID for task creation
      if (!currentUserId && !data.account_id) {
        return {
          success: false,
          data: null,
          error:
            "User ID is required to create a task. Please ensure you are logged in.",
        };
      }

      // Set default values and ensure account_id is set
      const taskData: TaskInsert = {
        status: "CREATED" as StatusEnum,
        priority: "NORMAL" as TaskPriorityEnum,
        ...data, // Apply user data first
        account_id: data.account_id || currentUserId || null, // Prioritize data.account_id, then currentUserId
      };

      // Final validation to ensure account_id is not null
      if (!taskData.account_id) {
        return {
          success: false,
          data: null,
          error: "Account ID must be provided for task creation",
        };
      }

      const result = await this.create(taskData);

      if (result.success && result.data) {
        console.log(
          `Task created successfully by user ${taskData.account_id}:`,
          result.data?.id
        );

        // Send notification if task has an assignee
        if (result.data.assignee) {
          // Get task with assignee details for notification
          const taskWithDetails = await this.getTaskWithAssignee(
            result.data.id
          );
          if (taskWithDetails.success && taskWithDetails.data) {
            await this.createTaskAssignedNotification(taskWithDetails.data);
          }
        }
      }

      return result;
    } catch (error: any) {
      console.error("Error in createTask:", error);
      return {
        success: false,
        data: null,
        error: error.message || "Failed to create task",
      };
    }
  }

  /**
   * Override update method to include change tracking and notifications
   */
  async update(id: string, data: TaskUpdate): Promise<ServiceResponse<Task>> {
    try {
      // Get the original task for comparison
      const originalTaskResult = await this.getTaskWithAssignee(id);
      if (!originalTaskResult.success || !originalTaskResult.data) {
        return {
          success: false,
          data: null,
          error: "Task not found for update",
        };
      }

      const originalTask = originalTaskResult.data;

      // Perform the update
      const result = await super.update(id, data);

      if (result.success && result.data) {
        // Track changes for notification
        const changes: string[] = [];
        if (data.name && data.name !== originalTask.name) changes.push("name");
        if (data.status && data.status !== originalTask.status)
          changes.push("status");
        if (data.priority && data.priority !== originalTask.priority)
          changes.push("priority");
        if (data.assignee && data.assignee !== originalTask.assignee)
          changes.push("assignee");
        if (data.due && data.due !== originalTask.due) changes.push("due date");
        if (data.start && data.start !== originalTask.start)
          changes.push("start date");

        // Send notification if there are changes and task has an assignee
        if (changes.length > 0 && result.data.assignee) {
          // Get updated task with assignee details for notification
          const updatedTaskResult = await this.getTaskWithAssignee(
            result.data.id
          );
          if (updatedTaskResult.success && updatedTaskResult.data) {
            await this.createTaskUpdatedNotification(
              updatedTaskResult.data,
              changes
            );
          }
        }

        console.log(`Task updated successfully: ${result.data.id}`, changes);
      }

      return result;
    } catch (error: any) {
      console.error("Error in task update:", error);
      return {
        success: false,
        data: null,
        error: error.message || "Failed to update task",
      };
    }
  }

  /**
   * Override delete method to include notifications
   */
  async delete(
    id: string,
    hardDelete: boolean = false
  ): Promise<ServiceResponse<boolean>> {
    try {
      // Get the task before deletion for notification
      const taskResult = await this.getTaskWithAssignee(id);
      const taskForNotification = taskResult.success ? taskResult.data : null;

      // Perform the deletion
      const result = await super.delete(id, hardDelete);

      if (result.success && taskForNotification) {
        // Send notification if task had an assignee
        await this.createTaskDeletedNotification(taskForNotification);
        console.log(`Task deleted successfully: ${id}`);
      }

      return result;
    } catch (error: any) {
      console.error("Error in task delete:", error);
      return {
        success: false,
        data: null,
        error: error.message || "Failed to delete task",
      };
    }
  }

  /**
   * Get a single task with assignee details
   */
  async getTaskWithAssignee(
    id: string
  ): Promise<ServiceResponse<TaskWithAssignee>> {
    try {
      const { data, error } = await this.supabase
        .from(this.tableName as any)
        .select(
          `
          *,
          assignee_details:accounts!tasks_assignee_fkey(
            id,
            email,
            users(
              id,
              name,
              phone,
              location
            )
          ),
          creator_details:accounts!tasks_account_id_fkey(
            id,
            email,
            users(
              id,
              name,
              phone,
              location
            )
          )
        `
        )
        .eq("id", id)
        .single();

      if (error) {
        return {
          success: false,
          data: null,
          error: error.message,
        };
      }

      // Cast data to Task type for proper type checking
      const taskData = data as unknown as Task;

      // Fetch entity details for single and multiple associations
      let entityDetails = null;
      let entitiesDetails = null;

      // Handle single association
      if (taskData.associated_table && taskData.associated_id) {
        entityDetails = await this.getEntityDetails(
          taskData.associated_table,
          taskData.associated_id
        );
      }

      // Handle multiple associations
      if (
        taskData.associated_table &&
        taskData.associated_ids &&
        Array.isArray(taskData.associated_ids)
      ) {
        entitiesDetails = await Promise.all(
          taskData.associated_ids.map(async (id: string) => {
            const details = await this.getEntityDetails(
              taskData.associated_table!,
              id
            );
            return details ? { id, ...details } : null;
          })
        );
        // Filter out null results
        entitiesDetails = entitiesDetails.filter(Boolean);
      }

      return {
        success: true,
        data: {
          ...taskData,
          entity_details: entityDetails,
          entities_details: entitiesDetails,
        } as TaskWithAssignee,
        error: null,
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: error.message || "Failed to get task with assignee",
      };
    }
  }

  /**
   * Create a task with automatic account_id detection from Supabase session
   * This method automatically gets the current user from the Supabase session
   * @param data - Task data to insert
   * @returns Promise<ServiceResponse<Task>>
   */
  async createTaskWithCurrentUser(
    data: TaskInsert
  ): Promise<ServiceResponse<Task>> {
    try {
      // Get current user from Supabase session
      const {
        data: { user },
        error: userError,
      } = await this.supabase.auth.getUser();

      if (userError || !user) {
        return {
          success: false,
          data: null,
          error: "No authenticated user found. Please log in to create tasks.",
        };
      }

      // Get the account_id from the accounts table using the auth user ID
      const { data: accountData, error: accountError } = await this.supabase
        .from("accounts")
        .select("id")
        .eq("auth_user_id", user.id)
        .single();

      if (accountError || !accountData) {
        return {
          success: false,
          data: null,
          error: "User account not found. Please contact support.",
        };
      }

      // Create the task with the found account_id
      return this.createTask(data, accountData.id);
    } catch (error: any) {
      console.error("Error in createTaskWithCurrentUser:", error);
      return {
        success: false,
        data: null,
        error: error.message || "Failed to create task with current user",
      };
    }
  }

  /**
   * Get entity details for a task
   */
  private async getEntityDetails(
    entityType: string,
    entityId: string
  ): Promise<{ name: string; type: string; identifier?: string } | null> {
    try {
      let query: any;
      let nameField = "name";
      let identifierField = "";

      switch (entityType) {
        case "cargos":
          query = this.supabase
            .from("cargos")
            .select("id, particular, tracking_number")
            .eq("id", entityId)
            .single();
          nameField = "particular";
          identifierField = "tracking_number";
          break;
        case "shipments":
          query = this.supabase
            .from("shipments")
            .select("id, name, tracking_number")
            .eq("id", entityId)
            .single();
          identifierField = "tracking_number";
          break;
        case "freights":
          query = this.supabase
            .from("freights")
            .select("id, name, origin, destination")
            .eq("id", entityId)
            .single();
          break;
        case "batches":
          query = this.supabase
            .from("batches")
            .select("id, name, code")
            .eq("id", entityId)
            .single();
          identifierField = "code";
          break;
        case "customers":
          query = this.supabase
            .from("customers")
            .select("id, name, email")
            .eq("id", entityId)
            .single();
          identifierField = "email";
          break;
        case "users":
          query = this.supabase
            .from("users")
            .select("id, name, email")
            .eq("id", entityId)
            .single();
          identifierField = "email";
          break;
        default:
          return null;
      }

      const { data, error } = await query;

      if (error || !data) {
        return null;
      }

      // Format the name based on entity type
      let displayName = data[nameField] || data.name || "Unknown";

      if (entityType === "freights" && data.origin && data.destination) {
        displayName = `${displayName} (${data.origin} → ${data.destination})`;
      }

      return {
        name: displayName,
        type: entityType,
        identifier: identifierField ? data[identifierField] : undefined,
      };
    } catch (error) {
      console.error(`Error fetching ${entityType} details:`, error);
      return null;
    }
  }

  /**
   * Get all tasks with assignee details and entity information
   */
  async getAllTasksWithAssignees(
    params?: any
  ): Promise<ServiceListResponse<TaskWithAssignee>> {
    try {
      let query = this.supabase.from(this.tableName as any).select(
        `
          *,
          assignee_details:accounts!tasks_assignee_fkey (
            id,
            email,
            users (
              id,
              name,
              phone
            )
          ),
          creator_details:accounts!tasks_account_id_fkey (
            id,
            email,
            users (
              id,
              name,
              phone
            )
          )
        `,
        { count: "exact" }
      );

      // Apply default status filtering to exclude INACTIVE items
      query = query.neq("status", "INACTIVE");

      // Apply filters if provided
      if (params?.filters) {
        query = this.applyFilters(query, params.filters);
      }

      // Apply sorting
      if (params?.column) {
        query = query.order(params.column, {
          ascending: params.ascending ?? false,
        });
      } else {
        query = query
          .order("created_at", { ascending: false, nullsFirst: false })
          .order("updated_at", { ascending: false, nullsFirst: false });
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit);
      }
      if (params?.offset) {
        query = query.range(
          params.offset,
          params.offset + (params.limit || 50) - 1
        );
      }

      const { data, error, count } = await query;

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false,
          count: 0,
        };
      }

      // Fetch entity details for tasks that have associations
      const tasksWithEntityDetails = await Promise.all(
        (data || []).map(async (task: any) => {
          let entityDetails = null;
          let entitiesDetails = null;

          // Handle single association
          if (task.associated_table && task.associated_id) {
            entityDetails = await this.getEntityDetails(
              task.associated_table,
              task.associated_id
            );
          }

          // Handle multiple associations
          if (
            task.associated_table &&
            task.associated_ids &&
            Array.isArray(task.associated_ids)
          ) {
            entitiesDetails = await Promise.all(
              task.associated_ids.map(async (id: string) => {
                const details = await this.getEntityDetails(
                  task.associated_table,
                  id
                );
                return details ? { id, ...details } : null;
              })
            );
            // Filter out null results
            entitiesDetails = entitiesDetails.filter(Boolean);
          }

          return {
            ...task,
            entity_details: entityDetails,
            entities_details: entitiesDetails,
          } as TaskWithAssignee;
        })
      );

      return {
        data: tasksWithEntityDetails,
        error: null,
        success: true,
        count: count || 0,
      };
    } catch (error: any) {
      return {
        data: [],
        error: error.message || "Failed to get tasks with assignees",
        success: false,
        count: 0,
      };
    }
  }

  /**
   * Get tasks by assignee
   */
  async getTasksByAssignee(
    assigneeId: string,
    params?: any
  ): Promise<ServiceListResponse<TaskWithAssignee>> {
    try {
      const filters = {
        ...params?.filters,
        assignee: assigneeId,
      };

      return this.getAllTasksWithAssignees({
        ...params,
        filters,
      });
    } catch (error: any) {
      return {
        data: [],
        error: error.message || "Failed to get tasks by assignee",
        success: false,
        count: 0,
      };
    }
  }

  /**
   * Get task statistics
   */
  async getTaskStats(): Promise<
    ServiceResponse<{
      totalTasks: number;
      completedTasks: number;
      inProgressTasks: number;
      overdueTasks: number;
      tasksByPriority: Record<TaskPriorityEnum, number>;
      tasksByStatus: Record<string, number>;
    }>
  > {
    try {
      // Get all active tasks
      const { data: tasks, error } = await this.supabase
        .from(this.tableName as any)
        .select(
          "id, name, status, priority, due, associated_table, associated_id, associated_ids, created_at, updated_at"
        )
        .neq("status", "INACTIVE");

      if (error) {
        return {
          success: false,
          data: null,
          error: error.message,
        };
      }

      const taskList = (tasks || []) as unknown as Task[];
      const totalTasks = taskList.length;
      const completedTasks = taskList.filter(
        (t) => t.status === "COMPLETED"
      ).length;
      const inProgressTasks = taskList.filter((t) =>
        ["PROCESSING", "PENDING"].includes(t.status)
      ).length;

      // Calculate overdue tasks (due date passed and not completed)
      const now = new Date();
      const overdueTasks = taskList.filter(
        (t) => t.due && new Date(t.due) < now && t.status !== "COMPLETED"
      ).length;

      // Group by priority
      const tasksByPriority = taskList.reduce(
        (acc, task) => {
          const priority = task.priority || "NORMAL";
          acc[priority] = (acc[priority] || 0) + 1;
          return acc;
        },
        {} as Record<TaskPriorityEnum, number>
      );

      // Group by status
      const tasksByStatus = taskList.reduce(
        (acc, task) => {
          acc[task.status] = (acc[task.status] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>
      );

      return {
        success: true,
        data: {
          totalTasks,
          completedTasks,
          inProgressTasks,
          overdueTasks,
          tasksByPriority,
          tasksByStatus,
        },
        error: null,
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: error.message || "Failed to get task statistics",
      };
    }
  }
}

export const taskService = new TaskService();
