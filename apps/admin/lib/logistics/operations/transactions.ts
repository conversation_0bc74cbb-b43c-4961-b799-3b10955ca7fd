import { BaseService } from "../base/service";
import {
  ServiceResponse,
  QueryParams,
  StatusEnum,
  Document,
  DocumentInsert,
  DocumentWithAccount,
} from "../types";
import { Transaction, TransactionInsert, Ledger } from "./ledgers";
import { DocumentService } from "./documents";

// Lazy imports to avoid circular dependencies
let tagService: any = null;
let invoiceService: any = null;
let cargoService: any = null;
let customerService: any = null;
let supplierService: any = null;
let batchService: any = null;
let freightService: any = null;
let shipmentService: any = null;

async function getTagService() {
  if (!tagService) {
    const { tagService: service } = await import("./tags");
    tagService = service;
  }
  return tagService;
}

async function getInvoiceService() {
  if (!invoiceService) {
    const { invoiceService: service } = await import("./invoices");
    invoiceService = service;
  }
  return invoiceService;
}

async function getCargoService() {
  if (!cargoService) {
    const { cargoService: service } = await import("./cargos");
    cargoService = service;
  }
  return cargoService;
}

async function getCustomerService() {
  if (!customerService) {
    const { customerService: service } = await import("./customers");
    customerService = service;
  }
  return customerService;
}

async function getSupplierService() {
  if (!supplierService) {
    const { supplierService: service } = await import("./suppliers");
    supplierService = service;
  }
  return supplierService;
}

async function getBatchService() {
  if (!batchService) {
    const { batchService: service } = await import("./batches");
    batchService = service;
  }
  return batchService;
}

async function getFreightService() {
  if (!freightService) {
    const { freightService: service } = await import("./freights");
    freightService = service;
  }
  return freightService;
}

async function getShipmentService() {
  if (!shipmentService) {
    const { shipmentService: service } = await import("./shipments");
    shipmentService = service;
  }
  return shipmentService;
}

// Extended QueryParams to include search functionality
interface ExtendedQueryParams extends QueryParams {
  search?: string;
}

export interface TransactionWithLedger extends Transaction {
  ledger?: Ledger;
}

export interface AssociatedEntity {
  id: string;
  label: string;
  type: string;
  metadata?: Record<string, any>;
}

export interface TransactionWithAssociations extends TransactionWithLedger {
  associated_entity?: AssociatedEntity | null;
}

export interface TransactionStats {
  totalTransactions: number;
  totalIncome: number;
  totalExpenses: number;
  netAmount: number;
  transactionsByStatus: { status: string; count: number; amount: number }[];
  transactionsByLedger: {
    ledger_name: string;
    count: number;
    amount: number;
  }[];
  recentTransactions: TransactionWithLedger[];
}

export interface TransactionUpdate {
  name?: string;
  status?: StatusEnum;
  tags?: string[];
  context?: string;
  value?: number;
  amount?: number;
  book?: string; // Ledger book category
  ledger_id?: string;
}

export class TransactionService extends BaseService {
  protected tableName = "transactions" as any; // Type assertion for table access
  private documentService = new DocumentService();

  /**
   * Helper function to fetch associated entity data for transactions using entity-specific methods
   */
  private async fetchAssociatedEntities(
    transactions: TransactionWithLedger[]
  ): Promise<TransactionWithAssociations[]> {
    const transactionsWithAssociations: TransactionWithAssociations[] = [];

    for (const transaction of transactions) {
      let associatedEntity: AssociatedEntity | null = null;

      if (transaction.associated_table && transaction.associated_id) {
        try {
          associatedEntity = await this.fetchSingleEntity(
            transaction.associated_table,
            transaction.associated_id
          );
        } catch (error) {
          console.warn(
            `Failed to fetch associated entity for transaction ${transaction.id}:`,
            error
          );
        }
      }

      transactionsWithAssociations.push({
        ...transaction,
        associated_entity: associatedEntity,
      });
    }

    return transactionsWithAssociations;
  }

  /**
   * Fetch a single entity using entity-specific service methods
   */
  private async fetchSingleEntity(
    entityType: string,
    entityId: string
  ): Promise<AssociatedEntity | null> {
    try {
      let result: any = null;
      let label = "";
      let metadata: Record<string, any> = {};

      switch (entityType) {
        case "invoices":
          const invoiceService = await getInvoiceService();
          result = await invoiceService.getInvoiceWithCustomer(entityId);
          if (result.success && result.data) {
            const invoice = result.data;
            label = `Invoice ${invoice.inv_number || invoice.id.slice(0, 8)}`;
            metadata = {
              invoice_number: invoice.inv_number,
              customer: invoice.customer?.name,
              supplier: invoice.supplier?.tracking_number,
              total_amount: invoice.total,
              status: invoice.status,
              type: "invoice",
            };
          }
          break;

        case "cargo":
          const cargoService = await getCargoService();
          result = await cargoService.getCargoWithRelations(entityId);
          if (result.success && result.data) {
            const cargo = result.data;
            label = `${cargo.particular || "Cargo"} - ${cargo.tracking_number || cargo.id.slice(0, 8)}`;
            metadata = {
              tracking_number: cargo.tracking_number,
              particular: cargo.particular,
              customer: cargo.customers?.name,
              weight: cargo.weight_value,
              status: cargo.status,
              type: "cargo",
            };
          }
          break;

        case "customers":
          const customerService = await getCustomerService();
          result = await customerService.getById(entityId);
          if (result.success && result.data) {
            const customer = result.data;
            label = customer.name || customer.id.slice(0, 8);
            metadata = {
              name: customer.name,
              email: customer.email,
              phone: customer.phone,
              location: customer.location,
              type: "customer",
            };
          }
          break;

        case "suppliers":
          const supplierService = await getSupplierService();
          result = await supplierService.getById(entityId);
          if (result.success && result.data) {
            const supplier = result.data;
            label =
              supplier.tracking_number ||
              supplier.phone ||
              supplier.id.slice(0, 8);
            metadata = {
              tracking_number: supplier.tracking_number,
              phone: supplier.phone,
              location: supplier.location,
              type: "supplier",
            };
          }
          break;

        case "batches":
          const batchService = await getBatchService();
          result = await batchService.getBatchWithRelations(entityId);
          if (result.success && result.data) {
            const batch = result.data;
            label = `${batch.name || "Batch"} - ${batch.code || batch.id.slice(0, 8)}`;
            metadata = {
              name: batch.name,
              code: batch.code,
              status: batch.status,
              weight: batch.weight,
              type: "batch",
            };
          }
          break;

        case "freights":
          const freightService = await getFreightService();
          result = await freightService.getFreightWithRelations(entityId);
          if (result.success && result.data) {
            const freight = result.data;
            label = `${freight.name || "Freight"} - ${freight.code || freight.id.slice(0, 8)}`;
            metadata = {
              name: freight.name,
              code: freight.code,
              origin: freight.origin,
              destination: freight.destination,
              type: freight.type,
              vehicle: freight.vehicle,
            };
          }
          break;

        case "shipments":
          const shipmentService = await getShipmentService();
          result = await shipmentService.getShipmentWithRelations(entityId);
          if (result.success && result.data) {
            const shipment = result.data;
            label = `Shipment ${shipment.tracking_number || shipment.id.slice(0, 8)}`;
            metadata = {
              tracking_number: shipment.tracking_number,
              status: shipment.status,
              departure: shipment.departure,
              arrival: shipment.arrival,
              type: "shipment",
            };
          }
          break;

        default:
          console.warn(`Unsupported entity type: ${entityType}`);
          return null;
      }

      if (result && result.success && result.data) {
        return {
          id: entityId, // Use the original entity ID
          label,
          type: entityType,
          metadata,
        };
      }

      return null;
    } catch (error) {
      console.error(`Error fetching ${entityType} with ID ${entityId}:`, error);
      return null;
    }
  }

  /**
   * Create a new transaction
   */
  async createTransaction(
    data: TransactionInsert
  ): Promise<ServiceResponse<Transaction>> {
    try {
      // Process tags if provided
      if (data.tags && data.tags.length > 0) {
        const tagService = await getTagService();
        await tagService.getOrCreateTags(data.tags);
      }

      const result = await this.create(data);
      return result;
    } catch (error) {
      return {
        success: false,
        data: null,
        error:
          error instanceof Error
            ? error.message
            : "Failed to create transaction",
      };
    }
  }

  /**
   * Get all transactions with ledger information
   * @param params - Query parameters including filters, search, pagination, etc.
   * @param ledgerId - Optional ledger ID to filter transactions by specific ledger
   */
  async getAllTransactionsWithLedgers(
    params?: ExtendedQueryParams,
    ledgerId?: string
  ): Promise<ServiceResponse<TransactionWithAssociations[]>>;

  /**
   * Get all transactions with ledger information (backward compatibility overload)
   * @param params - Query parameters including filters, search, pagination, etc.
   */
  async getAllTransactionsWithLedgers(
    params?: ExtendedQueryParams
  ): Promise<ServiceResponse<TransactionWithAssociations[]>>;

  /**
   * Get all transactions with ledger information (with ledger filtering)
   * @param params - Query parameters including filters, search, pagination, etc.
   * @param ledgerId - Optional ledger ID to filter transactions
   * @param includeInactive - Whether to include INACTIVE transactions
   */
  async getAllTransactionsWithLedgers(
    params?: ExtendedQueryParams,
    ledgerId?: string,
    includeInactive?: boolean
  ): Promise<ServiceResponse<TransactionWithAssociations[]>>;

  async getAllTransactionsWithLedgers(
    params?: ExtendedQueryParams,
    ledgerId?: string,
    includeInactive: boolean = false
  ): Promise<ServiceResponse<TransactionWithAssociations[]>> {
    try {
      let query = this.supabase.from(this.tableName).select(`
        *,
        ledgers:ledger_id(
          id,
          name,
          status,
          tags
        ),
        accounts:account_id(
          email,
          role:role_id (
            name
          ),
          users(
            name
          )
        )
      `);

      // Apply default status filtering to exclude INACTIVE transactions unless explicitly requested
      if (!includeInactive) {
        query = query.neq("status", "INACTIVE");
      }

      // Apply ledger_id filter if provided as direct parameter
      if (ledgerId) {
        query = query.eq("ledger_id", ledgerId);
      }

      // Apply additional filters from params
      if (params?.filters) {
        Object.entries(params.filters).forEach(([key, value]) => {
          if (key === "ledger_id" && !ledgerId) {
            // Only apply ledger_id from filters if not provided as direct parameter
            query = query.eq("ledger_id", value);
          } else if (key !== "ledger_id") {
            // Apply all other filters
            query = query.eq(key, value);
          }
        });
      }

      if (params?.search) {
        query = query.or(
          `name.ilike.%${params.search}%,context.ilike.%${params.search}%,tags.cs.{${params.search}}`
        );
      }

      if (params?.column && params?.ascending !== undefined) {
        query = query.order(params.column, { ascending: params.ascending });
      } else {
        query = query
          .order("created_at", { ascending: false, nullsFirst: false })
          .order("updated_at", { ascending: false, nullsFirst: false });
      }

      if (params?.limit) {
        query = query.limit(params.limit);
      }

      if (params?.offset) {
        query = query.range(
          params.offset,
          params.offset + (params.limit || 10) - 1
        );
      }

      const { data, error } = await query;

      if (error) {
        return {
          success: false,
          data: [],
          error: error.message,
        };
      }

      const transactions = (data as unknown as TransactionWithLedger[]) || [];

      // Fetch associated entity data for transactions that have associations
      const transactionsWithAssociations =
        await this.fetchAssociatedEntities(transactions);

      return {
        success: true,
        data: transactionsWithAssociations,
        error: null,
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch transactions",
      };
    }
  }

  /**
   * Get transactions by ledger ID (excludes INACTIVE by default)
   */
  async getTransactionsByLedger(
    ledgerId: string,
    params?: ExtendedQueryParams,
    includeInactive: boolean = false
  ): Promise<ServiceResponse<Transaction[]>> {
    try {
      // Apply ledger filter directly to query

      let query = this.supabase.from(this.tableName).select(`
        *,
        accounts:account_id(
          id,
          email,
          users(
            id,
            name  
          )
        )
      `);

      // Apply default status filtering to exclude INACTIVE transactions unless explicitly requested
      if (!includeInactive) {
        query = query.neq("status", "INACTIVE");
      }

      query = query.eq("ledger_id", ledgerId);

      if (params?.filters) {
        Object.entries(params.filters).forEach(([key, value]) => {
          if (key !== "ledger_id") {
            query = query.eq(key, value);
          }
        });
      }

      if (params?.search) {
        query = query.or(
          `name.ilike.%${params.search}%,context.ilike.%${params.search}%`
        );
      }

      if (params?.column && params?.ascending !== undefined) {
        query = query.order(params.column, { ascending: params.ascending });
      } else {
        query = query
          .order("created_at", { ascending: false, nullsFirst: false })
          .order("updated_at", { ascending: false, nullsFirst: false });
      }

      if (params?.limit) {
        query = query.limit(params.limit);
      }

      if (params?.offset) {
        query = query.range(
          params.offset,
          params.offset + (params.limit || 10) - 1
        );
      }

      const { data, error } = await query;

      if (error) {
        return {
          success: false,
          data: [],
          error: error.message,
        };
      }

      return {
        success: true,
        data: (data as unknown as Transaction[]) || [],
        error: null,
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch transactions by ledger",
      };
    }
  }

  /**
   * Get transaction by ID
   */
  async getTransactionById(
    id: string
  ): Promise<ServiceResponse<TransactionWithLedger>> {
    try {
      const { data, error } = await this.supabase
        .from(this.tableName)
        .select(
          `
          *,
          ledgers:ledger_id(
            id,
            name,
            status,
            tags
          ),
          accounts:account_id(
            id,
            email,
            users(
              id,
              name
            )
          )
        `
        )
        .eq("id", id)
        .single();

      if (error) {
        return {
          success: false,
          data: null,
          error: error.message,
        };
      }

      return {
        success: true,
        data: data as unknown as TransactionWithLedger,
        error: null,
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch transaction",
      };
    }
  }

  /**
   * Get transactions associated with a parent transaction (for excess payments)
   */
  async getTransactionsByParentId(
    parentTransactionId: string
  ): Promise<ServiceResponse<TransactionWithLedger[]>> {
    try {
      const { data, error } = await this.supabase
        .from(this.tableName)
        .select(
          `
          *,
          ledgers:ledger_id(
            id,
            name,
            status,
            tags
          ),
          accounts:account_id(
            id,
            email,
            users(
              id,
              name
            )
          )
        `
        )
        .eq("associated_table", "transactions")
        .eq("associated_id", parentTransactionId)
        .order("created_at", { ascending: false });

      if (error) {
        return {
          success: false,
          data: [],
          error: error.message,
        };
      }

      return {
        success: true,
        data: data as unknown as TransactionWithLedger[],
        error: null,
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch child transactions",
      };
    }
  }

  /**
   * Update transaction
   */
  async updateTransaction(
    id: string,
    data: TransactionUpdate
  ): Promise<ServiceResponse<Transaction>> {
    try {
      // Process tags if provided
      if (data.tags && data.tags.length > 0) {
        const tagService = await getTagService();
        await tagService.getOrCreateTags(data.tags);
      }

      const result = await this.update(id, data);
      return result;
    } catch (error) {
      return {
        success: false,
        data: null,
        error:
          error instanceof Error
            ? error.message
            : "Failed to update transaction",
      };
    }
  }

  /**
   * Get transactions by tags
   */
  async getTransactionsByTags(
    tags: string[],
    params?: ExtendedQueryParams
  ): Promise<ServiceResponse<TransactionWithLedger[]>> {
    try {
      let query = this.supabase.from(this.tableName).select(`
        *,
        ledgers:ledger_id(
          id,
          name,
          status,
          tags
        ),
        accounts:account_id(
          email,
          role:role_id (
            name
          ),
          users(
            name
          )
        )
      `);

      // Apply default status filtering to exclude INACTIVE transactions
      query = query.neq("status", "INACTIVE");

      // Filter by tags using PostgreSQL array overlap operator
      if (tags.length > 0) {
        query = query.overlaps("tags", tags);
      }

      // Apply additional filters
      if (params?.filters) {
        Object.entries(params.filters).forEach(([key, value]) => {
          if (value !== null && value !== undefined && value !== "") {
            query = query.eq(key, value);
          }
        });
      }

      // Apply search
      if (params?.search) {
        query = query.or(
          `name.ilike.%${params.search}%,context.ilike.%${params.search}%`
        );
      }

      // Apply sorting
      if (params?.column && params?.ascending !== undefined) {
        query = query.order(params.column, { ascending: params.ascending });
      } else {
        query = query
          .order("created_at", { ascending: false, nullsFirst: false })
          .order("updated_at", { ascending: false, nullsFirst: false });
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit);
      }
      if (params?.offset) {
        query = query.range(
          params.offset,
          params.offset + (params.limit || 10) - 1
        );
      }

      const { data, error } = await query;

      if (error) {
        return {
          success: false,
          data: [],
          error: error.message,
        };
      }

      return {
        success: true,
        data: (data as unknown as TransactionWithLedger[]) || [],
        error: null,
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        error:
          error instanceof Error
            ? error.message
            : "Failed to get transactions by tags",
      };
    }
  }

  /**
   * Get popular tags used in transactions
   */
  async getPopularTransactionTags(
    limit: number = 20
  ): Promise<ServiceResponse<{ tag: string; count: number }[]>> {
    try {
      const { data: transactions, error } = await this.supabase
        .from(this.tableName)
        .select("tags")
        .neq("status", "INACTIVE")
        .not("tags", "is", null);

      if (error) {
        return {
          success: false,
          data: [],
          error: error.message,
        };
      }

      // Count tag occurrences
      const tagCounts: { [tag: string]: number } = {};
      transactions.forEach((transaction: any) => {
        if (transaction.tags && Array.isArray(transaction.tags)) {
          transaction.tags.forEach((tag: string) => {
            tagCounts[tag] = (tagCounts[tag] || 0) + 1;
          });
        }
      });

      // Sort by count and limit
      const popularTags = Object.entries(tagCounts)
        .map(([tag, count]) => ({ tag, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, limit);

      return {
        success: true,
        data: popularTags,
        error: null,
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        error:
          error instanceof Error
            ? error.message
            : "Failed to get popular transaction tags",
      };
    }
  }

  /**
   * Delete transaction
   */
  async deleteTransaction(id: string): Promise<ServiceResponse<boolean>> {
    try {
      const result = await this.delete(id);
      return result;
    } catch (error) {
      return {
        success: false,
        data: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to delete transaction",
      };
    }
  }

  /**
   * Update all transactions for a specific ledger to INACTIVE status
   * This is used when a ledger is deleted to cascade the status change
   */
  async updateTransactionsByLedgerStatus(
    ledgerId: string,
    status: StatusEnum = "INACTIVE"
  ): Promise<ServiceResponse<boolean>> {
    try {
      const { error } = await this.supabase
        .from(this.tableName)
        .update({
          status,
          updated_at: new Date().toISOString(),
        })
        .eq("ledger_id", ledgerId)
        .neq("status", "INACTIVE"); // Only update transactions that are not already INACTIVE

      if (error) {
        return {
          success: false,
          data: false,
          error: error.message,
        };
      }

      return {
        success: true,
        data: true,
        error: null,
      };
    } catch (error) {
      return {
        success: false,
        data: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to update transactions status by ledger",
      };
    }
  }

  /**
   * Get comprehensive transaction statistics (excludes INACTIVE transactions)
   */
  async getTransactionStats(): Promise<ServiceResponse<TransactionStats>> {
    try {
      // Get all transactions with ledger information, excluding INACTIVE transactions
      const { data: transactions, error } = await this.supabase
        .from("transactions")
        .select(
          `
          *,
          ledgers:ledger_id(
            id,
            name
          )
        `
        )
        .neq("status", "INACTIVE")
        .order("updated_at", { ascending: false, nullsFirst: false })
        .order("created_at", { ascending: false });

      if (error) {
        return {
          success: false,
          data: {
            totalTransactions: 0,
            totalIncome: 0,
            totalExpenses: 0,
            netAmount: 0,
            transactionsByStatus: [],
            transactionsByLedger: [],
            recentTransactions: [],
          },
          error: error.message,
        };
      }

      const stats: TransactionStats = {
        totalTransactions: transactions?.length || 0,
        totalIncome: 0,
        totalExpenses: 0,
        netAmount: 0,
        transactionsByStatus: [],
        transactionsByLedger: [],
        recentTransactions: [],
      };

      if (transactions && transactions.length > 0) {
        // Type assertion for transaction data
        const typedTransactions = transactions as unknown as Transaction[];

        // Calculate totals
        typedTransactions.forEach((transaction) => {
          if (transaction.type === "DEBIT") {
            stats.totalIncome += transaction.amount;
          } else if (transaction.type === "CREDIT") {
            stats.totalExpenses += Math.abs(transaction.amount);
          }

          stats.netAmount += stats.totalIncome - stats.totalExpenses;
        });

        // Group by status
        const statusGroups = typedTransactions.reduce(
          (acc, transaction) => {
            const status = transaction.status || "UNKNOWN";
            if (!acc[status]) {
              acc[status] = { count: 0, amount: 0 };
            }
            acc[status].count++;
            acc[status].amount += transaction.amount;
            return acc;
          },
          {} as Record<string, { count: number; amount: number }>
        );

        stats.transactionsByStatus = Object.entries(statusGroups).map(
          ([status, data]) => ({
            status,
            count: data.count,
            amount: data.amount,
          })
        );

        // Group by ledger
        const ledgerGroups = typedTransactions.reduce(
          (acc, transaction) => {
            const ledgerName =
              (transaction as any).ledgers?.name || "Unknown Ledger";
            if (!acc[ledgerName]) {
              acc[ledgerName] = { count: 0, amount: 0 };
            }
            acc[ledgerName].count++;
            acc[ledgerName].amount += transaction.amount;
            return acc;
          },
          {} as Record<string, { count: number; amount: number }>
        );

        stats.transactionsByLedger = Object.entries(ledgerGroups).map(
          ([ledger_name, data]) => ({
            ledger_name,
            count: data.count,
            amount: data.amount,
          })
        );

        // Get recent transactions (last 10)
        stats.recentTransactions = typedTransactions.slice(
          0,
          10
        ) as unknown as TransactionWithLedger[];
      }

      return {
        success: true,
        data: stats,
        error: null,
      };
    } catch (error) {
      return {
        success: false,
        data: {
          totalTransactions: 0,
          totalIncome: 0,
          totalExpenses: 0,
          netAmount: 0,
          transactionsByStatus: [],
          transactionsByLedger: [],
          recentTransactions: [],
        },
        error:
          error instanceof Error
            ? error.message
            : "Failed to calculate transaction stats",
      };
    }
  }

  /**
   * Search transactions across all ledgers or within a specific ledger
   * @param searchTerm - Search term to filter transactions
   * @param params - Query parameters including filters, pagination, etc.
   * @param ledgerId - Optional ledger ID to limit search to specific ledger
   * @param includeInactive - Whether to include INACTIVE transactions in search
   */
  async searchTransactions(
    searchTerm: string,
    params?: ExtendedQueryParams,
    ledgerId?: string,
    includeInactive: boolean = false
  ): Promise<ServiceResponse<TransactionWithLedger[]>> {
    try {
      const searchParams: ExtendedQueryParams = {
        ...params,
        search: searchTerm,
      };

      return await this.getAllTransactionsWithLedgers(
        searchParams,
        ledgerId,
        includeInactive
      );
    } catch (error) {
      return {
        success: false,
        data: [],
        error:
          error instanceof Error
            ? error.message
            : "Failed to search transactions",
      };
    }
  }

  /**
   * Upload and attach documents to a transaction
   */
  async uploadTransactionDocuments(
    transactionId: string,
    files: File[],
    accountId: string,
    ledgerId?: string
  ): Promise<ServiceResponse<Document[]>> {
    try {
      const uploadedDocuments: Document[] = [];

      for (const file of files) {
        // Upload file to storage
        const uploadResult = await this.documentService.uploadToStorage({
          content: file,
          fileName: file.name,
          contentType: file.type,
          folder: "transaction-documents",
          metadata: {
            transactionId,
            ledgerId,
          },
        });

        if (!uploadResult.success || !uploadResult.data) {
          throw new Error(
            `Failed to upload ${file.name}: ${uploadResult.error}`
          );
        }

        // Create document record
        const documentData: DocumentInsert = {
          name: file.name,
          path: uploadResult.data,
          category: "transaction",
          description: `Document attached to transaction`,
          associated_table: "transactions",
          associated_id: transactionId,
          account_id: accountId,
          status: "ACTIVE" as StatusEnum,
        };

        const createResult =
          await this.documentService.createDocument(documentData);
        if (!createResult.success || !createResult.data) {
          throw new Error(
            `Failed to create document record for ${file.name}: ${createResult.error}`
          );
        }

        uploadedDocuments.push(createResult.data);
      }

      return {
        success: true,
        data: uploadedDocuments,
        error: null,
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        error:
          error instanceof Error
            ? error.message
            : "Failed to upload transaction documents",
      };
    }
  }

  /**
   * Get documents associated with a transaction
   */
  async getTransactionDocuments(
    transactionId: string
  ): Promise<ServiceResponse<DocumentWithAccount[]>> {
    try {
      const result = await this.documentService.getDocumentsByEntity(
        "transactions",
        transactionId
      );

      return {
        success: result.success,
        data: result.data || [],
        error: result.error,
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch transaction documents",
      };
    }
  }

  /**
   * Delete a document associated with a transaction
   */
  async deleteTransactionDocument(
    documentId: string
  ): Promise<ServiceResponse<boolean>> {
    try {
      const result =
        await this.documentService.deleteDocumentWithFile(documentId);
      return result;
    } catch (error) {
      return {
        success: false,
        data: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to delete transaction document",
      };
    }
  }

  /**
   * Create transaction with document attachments and handle excess payments
   */
  async createTransactionWithDocuments(
    transactionData: TransactionInsert,
    files: File[] = [],
    invoiceData?: { invoiceTotal: number; invoiceId: string }
  ): Promise<
    ServiceResponse<{
      transaction: Transaction;
      excessTransaction?: Transaction;
      documents: Document[];
    }>
  > {
    try {
      let mainTransaction: Transaction;
      let excessTransaction: Transaction | undefined;
      let documents: Document[] = [];

      // Handle excess payment scenario
      if (invoiceData && transactionData.amount > invoiceData.invoiceTotal) {
        const excessAmount = transactionData.amount - invoiceData.invoiceTotal;

        // Create main transaction for the invoice amount
        const mainTransactionData: TransactionInsert = {
          ...transactionData,
          amount: invoiceData.invoiceTotal,
          name: `${transactionData.name} - Invoice Payment`,
        };

        const mainTransactionResult =
          await this.createTransaction(mainTransactionData);

        if (!mainTransactionResult.success || !mainTransactionResult.data) {
          return {
            success: false,
            data: null,
            error:
              mainTransactionResult.error ||
              "Failed to create main transaction",
          };
        }

        mainTransaction = mainTransactionResult.data;

        // Create excess transaction
        const excessTransactionData: TransactionInsert = {
          ...transactionData,
          amount: excessAmount,
          name: `${transactionData.name} - Excess Payment`,
          status: "EXCESS" as any,
          associated_table: "transactions",
          associated_id: mainTransaction.id,
          context: `Excess payment of $${excessAmount} from invoice payment. Original payment: $${transactionData.amount}, Invoice total: $${invoiceData.invoiceTotal}`,
        };

        const excessTransactionResult = await this.createTransaction(
          excessTransactionData
        );

        if (excessTransactionResult.success && excessTransactionResult.data) {
          excessTransaction = excessTransactionResult.data;
        }
      } else {
        // Regular transaction creation
        const transactionResult = await this.createTransaction(transactionData);

        if (!transactionResult.success || !transactionResult.data) {
          return {
            success: false,
            data: null,
            error: transactionResult.error || "Failed to create transaction",
          };
        }

        mainTransaction = transactionResult.data;
      }

      // Upload documents if any are provided
      if (files.length > 0 && transactionData.account_id) {
        const documentsResult = await this.uploadTransactionDocuments(
          mainTransaction.id,
          files,
          transactionData.account_id,
          transactionData.ledger_id
        );

        if (!documentsResult.success) {
          // Transaction was created but documents failed - log warning but don't fail
          console.warn(
            "Transaction created but document upload failed:",
            documentsResult.error
          );
        } else {
          documents = documentsResult.data || [];
        }
      }

      return {
        success: true,
        data: {
          transaction: mainTransaction,
          excessTransaction,
          documents,
        },
        error: null,
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        error:
          error instanceof Error
            ? error.message
            : "Failed to create transaction with documents",
      };
    }
  }
}

// Create service instance
export const transactionService = new TransactionService();
