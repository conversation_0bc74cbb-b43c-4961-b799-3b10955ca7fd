/**
 * Document Service
 *
 * Handles CRUD operations for documents with Supabase integration
 * including object storage for PDF files and document management.
 */

import { BaseService } from "../base/service";
import { createClient } from "@/lib/supabase/client";
import type {
  Document,
  DocumentInsert,
  DocumentUpdate,
  DocumentWithAccount,
  ServiceResponse,
  ServiceListResponse,
  QueryParams,
  StatusEnum,
} from "../types";

// Document-specific types
export interface DocumentUploadOptions {
  content: Blob | Buffer | Uint8Array;
  fileName: string;
  contentType: string;
  folder?: string;
  metadata?: Record<string, any>;
}

export interface DocumentGenerationData {
  name: string;
  category: string;
  description?: string;
  associatedTable?: string;
  associatedId?: string;
  details?: Record<string, any>;
  content: Blob;
  contentType: string;
}

export interface DocumentSearchOptions extends QueryParams {
  category?: string;
  associatedTable?: string;
  associatedId?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface DocumentStatistics {
  totalDocuments: number;
  documentsByCategory: { category: string; count: number }[];
  documentsByStatus: { status: StatusEnum; count: number }[];
  recentDocuments: DocumentWithAccount[];
  storageUsed: number; // in bytes
}

export class DocumentService extends BaseService<
  Document,
  DocumentInsert,
  DocumentUpdate
> {
  protected tableName = "documents";
  private storageClient = createClient();

  /**
   * Create a new document record
   */
  async createDocument(
    data: DocumentInsert
  ): Promise<ServiceResponse<Document>> {
    try {
      console.log(data);
      const result = await this.create(data);
      return result;
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to create document",
        success: false,
      };
    }
  }

  /**
   * Get document by ID with account relations
   */
  async getDocumentWithAccount(
    id: string
  ): Promise<ServiceResponse<DocumentWithAccount | null>> {
    try {
      const { data, error } = await this.supabase
        .from(this.tableName)
        .select(
          `
          *,
          accounts:account_id(
            id,
            email,
            users(
              id,
              name,
              email
            )
          )
        `
        )
        .eq("id", id)
        .single();

      if (error) {
        return this.handleError("Failed to fetch document", error);
      }

      let documentWithSignedUrl = { data };

      if (data.path) {
        documentWithSignedUrl = this.getDocumentDownloadUrl(data.path);
      }

      return { success: true, data: documentWithSignedUrl.data, error: null };
    } catch (error) {
      return this.handleError("Failed to fetch document", error);
    }
  }

  /**
   * Get documents by entity (associated table and ID)
   */
  async getDocumentsByEntity(
    table: string,
    entityId: string,
    options?: DocumentSearchOptions
  ): Promise<ServiceListResponse<DocumentWithAccount>> {
    try {
      let query = this.supabase
        .from(this.tableName)
        .select(`*`, { count: "exact" })
        .eq("associated_table", table)
        .eq("associated_id", entityId);

      // Apply filters
      if (options?.category) {
        query = query.eq("category", options.category);
      }

      if (options?.filters?.status) {
        query = query.eq("status", options.filters.status);
      }

      // Apply sorting
      if (options?.column) {
        query = query.order(options.column, {
          ascending: options.ascending ?? false,
        });
      } else {
        query = query
          .order("created_at", { ascending: false, nullsFirst: false })
          .order("updated_at", { ascending: false, nullsFirst: false });
      }

      // Apply pagination
      if (options?.limit) {
        query = query.limit(options.limit);
        if (options.offset) {
          query = query.range(
            options.offset,
            options.offset + options.limit - 1
          );
        } else if (options.page && options.page > 1) {
          const offset = (options.page - 1) * options.limit;
          query = query.range(offset, offset + options.limit - 1);
        }
      }

      const { data, error, count } = await query;

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false,
        };
      }

      return {
        data: data || [],
        error: null,
        success: true,
        count: count || 0,
      };
    } catch (error: any) {
      return {
        data: [],
        error: error.message || "Failed to fetch documents by entity",
        success: false,
      };
    }
  }

  /**
   * Upload file to Supabase Storage
   */
  async uploadToStorage(
    options: DocumentUploadOptions
  ): Promise<ServiceResponse<string>> {
    try {
      const {
        content,
        fileName,
        contentType,
        folder = "documents",
        metadata,
      } = options;

      // Create a unique file path
      const timestamp = new Date().getTime();
      const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, "_");
      const filePath = `${folder}/${timestamp}-${sanitizedFileName}`;

      // Upload to Supabase Storage
      const { data, error } = await this.storageClient.storage
        .from("documents") // Bucket name
        .upload(filePath, content, {
          contentType,
          metadata,
          upsert: false,
        });

      if (error) {
        console.error("Storage upload error:", error);
        return {
          data: null,
          error: `Failed to upload file: ${error.message}`,
          success: false,
        };
      }

      return {
        data: data.path,
        error: null,
        success: true,
      };
    } catch (error: any) {
      console.error("Upload exception:", error);
      return {
        data: null,
        error: error.message || "Failed to upload file to storage",
        success: false,
      };
    }
  }

  /**
   * Generate and store a document (e.g., PDF)
   */
  async generateAndStoreDocument(
    data: DocumentGenerationData,
    accountId: string
  ): Promise<ServiceResponse<Document>> {
    try {
      // First upload the file to storage
      const uploadResult = await this.uploadToStorage({
        content: data.content,
        fileName: `${data.name}.pdf`,
        contentType: data.contentType,
        folder: data.category,
        metadata: {
          category: data.category,
          associatedTable: data.associatedTable,
          associatedId: data.associatedId,
        },
      });

      if (!uploadResult.success || !uploadResult.data) {
        return {
          data: null,
          error: uploadResult.error || "Failed to upload document",
          success: false,
        };
      }

      // Create document record
      const documentData: DocumentInsert = {
        name: data.name,
        path: uploadResult.data,
        category: data.category,
        description: data.description,
        associated_table: data.associatedTable,
        associated_id: data.associatedId,
        details: data.details,
        account_id: accountId,
        status: "ACTIVE" as StatusEnum,
      };

      const createResult = await this.createDocument(documentData);
      return createResult;
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to generate and store document",
        success: false,
      };
    }
  }

  /**
   * Get document download URL
   */
  async getDocumentDownloadUrl(
    path: string,
    expiresIn: number = 3600
  ): Promise<ServiceResponse<string>> {
    try {
      const { data, error } = await this.storageClient.storage
        .from("documents")
        .createSignedUrl(path, expiresIn);

      if (error) {
        return {
          data: null,
          error: `Failed to create download URL: ${error.message}`,
          success: false,
        };
      }

      return {
        data: data.signedUrl,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to create download URL",
        success: false,
      };
    }
  }

  /**
   * Delete document and its file from storage
   */
  async deleteDocumentWithFile(id: string): Promise<ServiceResponse<boolean>> {
    try {
      // First get the document to get the file path
      const documentResult = await this.getById(id);
      if (!documentResult.success || !documentResult.data) {
        return {
          data: null,
          error: "Document not found",
          success: false,
        };
      }

      const document = documentResult.data;

      // Delete from storage if path exists
      if (document.path) {
        const { error: storageError } = await this.storageClient.storage
          .from("documents")
          .remove([document.path]);

        if (storageError) {
          console.warn("Failed to delete file from storage:", storageError);
          // Continue with database deletion even if storage deletion fails
        }
      }

      // Delete document record
      const deleteResult = await this.delete(id, true); // Hard delete
      return deleteResult;
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to delete document",
        success: false,
      };
    }
  }

  /**
   * Search documents by text
   */
  async searchDocuments(
    searchTerm: string,
    options?: DocumentSearchOptions
  ): Promise<ServiceListResponse<DocumentWithAccount>> {
    try {
      let query = this.supabase.from(this.tableName).select(
        `
          *,
          accounts:account_id(
            id,
            email,
            users(
              id,
              name,
              email
            )
          )
        `,
        { count: "exact" }
      );

      // Add text search
      query = query.or(
        `name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%,category.ilike.%${searchTerm}%`
      );

      // Apply additional filters
      if (options?.category) {
        query = query.eq("category", options.category);
      }

      if (options?.associatedTable) {
        query = query.eq("associated_table", options.associatedTable);
      }

      if (options?.associatedId) {
        query = query.eq("associated_id", options.associatedId);
      }

      if (options?.filters?.status) {
        query = query.eq("status", options.filters.status);
      }

      // Apply date filters
      if (options?.dateFrom) {
        query = query.gte("created_at", options.dateFrom);
      }

      if (options?.dateTo) {
        query = query.lte("created_at", options.dateTo);
      }

      // Apply sorting
      if (options?.column) {
        query = query.order(options.column, {
          ascending: options.ascending ?? false,
        });
      } else {
        query = query
          .order("created_at", { ascending: false, nullsFirst: false })
          .order("updated_at", { ascending: false, nullsFirst: false });
      }

      // Apply pagination
      if (options?.limit) {
        query = query.limit(options.limit);
        if (options.offset) {
          query = query.range(
            options.offset,
            options.offset + options.limit - 1
          );
        } else if (options.page && options.page > 1) {
          const offset = (options.page - 1) * options.limit;
          query = query.range(offset, offset + options.limit - 1);
        }
      }

      const { data, error, count } = await query;

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false,
        };
      }

      return {
        data: data || [],
        error: null,
        success: true,
        count: count || 0,
      };
    } catch (error: any) {
      return {
        data: [],
        error: error.message || "Failed to search documents",
        success: false,
      };
    }
  }

  /**
   * Handle errors consistently
   */
  private handleError(message: string, error: any): ServiceResponse<any> {
    console.error(`[DocumentService] ${message}:`, error);
    return {
      data: null,
      error: error.message || message,
      success: false,
    };
  }
}

// Export singleton instance
export const documentService = new DocumentService();
