import { BaseService } from "../base/service";
import {
  <PERSON>eight,
  FreightInsert,
  FreightUpdate,
  FreightWithRelations,
  ServiceResponse,
  ServiceListResponse,
  QueryParams,
  StatusEnum,
} from "../types";

export class FreightService extends BaseService<
  Freight,
  FreightInsert,
  FreightUpdate
> {
  protected tableName = "freights";

  // Override getAll to include account relationships
  async getAll(
    params?: QueryParams
  ): Promise<ServiceListResponse<FreightWithRelations>> {
    try {
      let query = this.supabase
        .from("freights")
        .select(`*`, { count: "exact" });

      // Apply additional filters
      if (params?.filters) {
        query = this.applyFilters(query, params.filters);
      }

      // Apply sorting
      if (params?.column) {
        query = query.order(params.column, {
          ascending: params.ascending ?? false,
        });
      } else {
        query = query
          .order("created_at", { ascending: false, nullsFirst: false })
          .order("updated_at", { ascending: false, nullsFirst: false });
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit);
        if (params.offset) {
          query = query.range(params.offset, params.offset + params.limit - 1);
        } else if (params.page && params.page > 1) {
          const offset = (params.page - 1) * params.limit;
          query = query.range(offset, offset + params.limit - 1);
        }
      }

      const { data, error, count } = await query;

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false,
        };
      }

      return {
        data: (data || []) as any,
        error: null,
        success: true,
        count: count || 0,
      };
    } catch (error: any) {
      return {
        data: [],
        error: error.message || "Failed to get freights",
        success: false,
      };
    }
  }

  // Search freights by name, scac_codes, type, or prefixes
  async searchFreights(
    searchTerm: string,
    params?: QueryParams
  ): Promise<ServiceListResponse<FreightWithRelations>> {
    try {
      let query = this.supabase
        .from("freights")
        .select(
          `
          *,
          accounts (
            id,
            name,
            email
          )
        `,
          { count: "exact" }
        )
        .or(
          `name.ilike.%${searchTerm}%,scac_codes.ilike.%${searchTerm}%,type.ilike.%${searchTerm}%,prefixes.ilike.%${searchTerm}%`
        );

      // Apply additional filters
      if (params?.filters) {
        query = this.applyFilters(query, params.filters);
      }

      // Apply sorting
      if (params?.column) {
        query = query.order(params.column, {
          ascending: params.ascending ?? false,
        });
      } else {
        query = query
          .order("created_at", { ascending: false, nullsFirst: false })
          .order("updated_at", { ascending: false, nullsFirst: false });
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit);
        if (params.offset) {
          query = query.range(params.offset, params.offset + params.limit - 1);
        } else if (params.page && params.page > 1) {
          const offset = (params.page - 1) * params.limit;
          query = query.range(offset, offset + params.limit - 1);
        }
      }

      const { data, error, count } = await query;

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false,
        };
      }

      return {
        data: (data || []) as any,
        error: null,
        success: true,
        count: count || 0,
      };
    } catch (error: any) {
      return {
        data: [],
        error: error.message || "Failed to search freights",
        success: false,
      };
    }
  }

  // Get freight by SCAC codes
  async getFreightByScacCodes(
    scacCodes: string
  ): Promise<ServiceResponse<FreightWithRelations>> {
    try {
      const { data, error } = await this.supabase
        .from("freights")
        .select(
          `
          *,
          accounts (
            id,
            name,
            email
          )
        `
        )
        .eq("scac_codes", scacCodes)
        .single();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: data as any,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to get freight by SCAC codes",
        success: false,
      };
    }
  }

  // Get active freights only
  async getActiveFreights(
    params?: QueryParams
  ): Promise<ServiceListResponse<FreightWithRelations>> {
    try {
      let query = this.supabase
        .from("freights")
        .select(
          `
          *,
          accounts (
            id,
            name,
            email
          )
        `,
          { count: "exact" }
        )
        .eq("active", true)
        .eq("status", "ACTIVE");

      // Apply additional filters
      if (params?.filters) {
        query = this.applyFilters(query, params.filters);
      }

      // Apply sorting
      if (params?.column) {
        query = query.order(params.column, {
          ascending: params.ascending ?? false,
        });
      } else {
        query = query
          .order("created_at", { ascending: false, nullsFirst: false })
          .order("updated_at", { ascending: false, nullsFirst: false });
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit);
        if (params.offset) {
          query = query.range(params.offset, params.offset + params.limit - 1);
        } else if (params.page && params.page > 1) {
          const offset = (params.page - 1) * params.limit;
          query = query.range(offset, offset + params.limit - 1);
        }
      }

      const { data, error, count } = await query;

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false,
        };
      }

      return {
        data: (data || []) as any,
        error: null,
        success: true,
        count: count || 0,
      };
    } catch (error: any) {
      return {
        data: [],
        error: error.message || "Failed to get active freights",
        success: false,
      };
    }
  }

  // Update freight status and activity flags
  async updateFreightStatus(
    id: string,
    updates: {
      status?: StatusEnum;
      active?: boolean;
      ct_active?: boolean;
      bk_active?: boolean;
      bl_active?: boolean;
      maintenance?: boolean;
    }
  ): Promise<ServiceResponse<Freight>> {
    try {
      const { data, error } = await this.supabase
        .from("freights")
        .update(updates)
        .eq("id", id)
        .select()
        .single();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: data as Freight,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to update freight status",
        success: false,
      };
    }
  }

  // Check if SCAC codes exist
  async isScacCodesExists(
    scacCodes: string,
    excludeId?: string
  ): Promise<ServiceResponse<boolean>> {
    try {
      let query = this.supabase
        .from("freights")
        .select("id")
        .eq("scac_codes", scacCodes);

      if (excludeId) {
        query = query.neq("id", excludeId);
      }

      const { data, error } = await query;

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: (data || []).length > 0,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to check SCAC codes",
        success: false,
      };
    }
  }

  // Create freight with validation
  async createFreight(data: FreightInsert): Promise<ServiceResponse<Freight>> {
    try {
      // Validate SCAC codes uniqueness
      const scacExists = await this.isScacCodesExists(data.scac_codes);
      if (!scacExists.success) {
        return scacExists as unknown as ServiceResponse<Freight>;
      }

      if (scacExists.data) {
        return {
          data: null,
          error: "SCAC codes already exist",
          success: false,
        };
      }

      // Set defaults
      const payload: FreightInsert = {
        ...data,
        type: data.type || "SEA",
        status: data.status || "ACTIVE",
        active: data.active ?? false,
        ct_active: data.ct_active ?? false,
        bk_active: data.bk_active ?? false,
        bl_active: data.bl_active ?? false,
        maintenance: data.maintenance ?? false,
      };

      return this.create(payload);
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to create freight",
        success: false,
      };
    }
  }
}

// Export singleton instance
export const freightService = new FreightService();
