// Export all services
export { customerService } from "./customers";
export { supplierService } from "./suppliers";
export { cargoService } from "./cargos";
export { batchService } from "./batches";
export { freightService } from "./freights";
export { shipmentService } from "./shipments";
// export { handoverService } from "./handovers";
export { ledgerService } from "./ledgers";
export { transactionService } from "./transactions";
export { documentService } from "./documents";
export { invoiceService } from "./invoices";
export { releaseAuthorizationService } from "./release-authorizations";
export { tagService } from "./tags";
export { taskService } from "./tasks";
export { bulkFetchService } from "./bulk-fetch";

// Export the centralized code generator service
export {
  codeGeneratorService,
  CodeGeneratorService,
  type TrackingNumberParams,
  type BatchCodeParams,
  type CargoTrackingParams,
  type InvoiceNumberParams,
  type DocumentNumberParams,
  // Legacy exports for backward compatibility
  generateBatchCode,
  generateTrackingNumber,
  validateBatchCode,
  parseTrackingNumber,
  generateSequentialTrackingNumber,
  // New sequential generation functions
  generateSequentialBatchCode,
  generateSequentialCargoTrackingNumber,
  generateSequentialShipmentTrackingNumber,
  generateSequentialInvoiceNumber,
  generateSequentialTrackingNumberSync,
  getLocationCode,
  getWeightCategory,
  LOCATION_CODES,
  SHIPPING_MODES,
  WEIGHT_CATEGORIES,
} from "./code-generator";

// Export types
export type { Customer, CustomerInsert, CustomerUpdate } from "./customers";
export type { Supplier, SupplierInsert, SupplierUpdate } from "./suppliers";
export type { Cargo, CargoInsert, CargoUpdate } from "./cargos";
export type { Batch, BatchInsert, BatchUpdate } from "./batches";
export type { Freight, FreightInsert, FreightUpdate } from "./freights";
export type { Shipment, ShipmentInsert, ShipmentUpdate } from "./shipments";
export type {
  Ledger,
  LedgerInsert,
  LedgerUpdate,
  Transaction,
  TransactionInsert,
} from "./ledgers";
export type {
  Tag,
  TagInsert,
  TagUpdate,
  TagWithStats,
  PopularTagsResponse,
} from "./tags";
export type {
  Task,
  TaskInsert,
  TaskUpdate,
  TaskWithAssignee,
  TaskPriorityEnum,
} from "./tasks";

// Export bulk fetch types and enums
export {
  BulkFetchService,
  DetailLevel,
  type BulkFetchOptions,
  type BulkFetchResult,
  type EntityType,
} from "./bulk-fetch";

// Export tag helpers
export { TagHelpers, POPULAR_TAGS } from "./tags";

// Export service helper functions
// getTagService commented out since tagService doesn't exist

export const getLedgerService = async () => {
  const { ledgerService } = await import("./ledgers");
  return ledgerService;
};

export const getTransactionService = async () => {
  const { transactionService } = await import("./transactions");
  return transactionService;
};

export const getCargoService = async () => {
  const { cargoService } = await import("./cargos");
  return cargoService;
};

export const getCustomerService = async () => {
  const { customerService } = await import("./customers");
  return customerService;
};

export const getSupplierService = async () => {
  const { supplierService } = await import("./suppliers");
  return supplierService;
};

export const getBatchService = async () => {
  const { batchService } = await import("./batches");
  return batchService;
};

export const getFreightService = async () => {
  const { freightService } = await import("./freights");
  return freightService;
};

export const getShipmentService = async () => {
  const { shipmentService } = await import("./shipments");
  return shipmentService;
};

export const getDocumentService = async () => {
  const { documentService } = await import("./documents");
  return documentService;
};

export const getInvoiceService = async () => {
  const { invoiceService } = await import("./invoices");
  return invoiceService;
};

export const getReleaseAuthorizationService = async () => {
  const { releaseAuthorizationService } = await import(
    "./release-authorizations"
  );
  return releaseAuthorizationService;
};

export const getBulkFetchService = async () => {
  const { bulkFetchService } = await import("./bulk-fetch");
  return bulkFetchService;
};
