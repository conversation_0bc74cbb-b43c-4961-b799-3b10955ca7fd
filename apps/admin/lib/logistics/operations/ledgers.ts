import { BaseService } from "../base/service";
import { ServiceResponse, ServiceListResponse, QueryParams } from "../types";
import type { StatusEnum } from "../types";

// Lazy import to avoid circular dependencies
let tagService: any = null;

async function getTagService() {
  if (!tagService) {
    const { tagService: service } = await import("./tags");
    tagService = service;
  }
  return tagService;
}

// Ledger interfaces
export interface Ledger {
  id: string;
  name: string;
  code: string | null;
  category: string | null;
  books: string[] | null;
  status: StatusEnum | null;
  tags: string[] | null;
  associated_table: string | null;
  associated_id: string | null;
  account_id: string | null;
  created_at: string | null;
  updated_at: string | null;
}

export interface LedgerInsert {
  name: string;
  code?: string | null;
  category?: string | null;
  books?: string[] | null;
  status?: StatusEnum | null;
  tags?: string[] | null;
  associated_table?: string | null;
  associated_id?: string | null;
  account_id?: string | null;
}

export interface LedgerUpdate {
  name?: string;
  code?: string | null;
  category?: string | null;
  books?: string[] | null;
  status?: StatusEnum | null;
  tags?: string[] | null;
  associated_table?: string | null;
  associated_id?: string | null;
}

export interface Transaction {
  id: string;
  name: string;
  status: StatusEnum;
  tags?: string[];
  context?: string;
  type: TransactionType;
  amount: number;
  book?: string; // Ledger book category
  ledger_id: string;
  account_id: string;
  associated_table?: string;
  associated_id?: string;
  created_at: string;
  updated_at: string;
}

export type TransactionType = "DEBIT" | "CREDIT";

export interface TransactionInsert {
  name: string;
  status?: StatusEnum;
  tags?: string[];
  context?: string;
  type: TransactionType;
  amount: number;
  book?: string; // Ledger book category
  ledger_id: string;
  account_id: string;
  associated_table?: string;
  associated_id?: string;
}

export interface LedgerWithTransactions extends Ledger {
  transactions?: Transaction[];
}

export interface LedgerWithStats extends Ledger {
  totalTransactions: number;
  totalRevenue: number;
  totalExpenses: number;
  balance: number;
}

export interface LedgerStats {
  totalTransactions: number;
  totalIncome: number;
  totalExpenses: number;
  netAmount: number;
  lastTransactionDate?: string;
}

// Popular book categories
export const LEDGER_CATEGORIES = {
  EXPENSE: {
    name: "Expense",
    description: "Operating expenses and costs",
    tags: ["expenses", "costs", "operational"],
    color: "#ef4444",
    icon: "TrendingDown",
  },
  REVENUE: {
    name: "Revenue",
    description: "Income and revenue streams",
    tags: ["revenue", "income", "sales"],
    color: "#22c55e",
    icon: "TrendingUp",
  },
  ASSET: {
    name: "Asset",
    description: "Company assets and investments",
    tags: ["assets", "investments", "equipment"],
    color: "#3b82f6",
    icon: "Building",
  },
  LIABILITY: {
    name: "Liability",
    description: "Debts and liabilities",
    tags: ["liabilities", "debts", "payables"],
    color: "#8b5cf6",
    icon: "CreditCard",
  },
  EQUITY: {
    name: "Equity",
    description: "Retained earnings",
    tags: ["sharess", "investments"],
    color: "#8b5cf6",
    icon: "CreditCard",
  },
  REALISED_INVOICE: {
    name: "Realised Invoices",
    description: "Closed invoices recorded as credit",
    tags: ["realised", "invoices", "credit", "expenses"],
    color: "#f59e0b",
    icon: "FileText",
  },
} as const;

export class LedgerService extends BaseService<
  Ledger,
  LedgerInsert,
  LedgerUpdate
> {
  protected tableName = "ledgers";

  /**
   * Create a new ledger
   */
  async createLedger(data: LedgerInsert): Promise<ServiceResponse<Ledger>> {
    try {
      // Process tags if provided
      if (data.tags && data.tags.length > 0) {
        const tagService = await getTagService();
        await tagService.getOrCreateTags(data.tags);
      }

      console.log("Debug (data):", data);
      return this.create(data);
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: error.message || "Failed to create ledger",
      };
    }
  }

  /**
   * Get all ledgers with optional filtering (excludes INACTIVE by default)
   */
  async getAllLedgers(
    params?: QueryParams,
    includeInactive: boolean = false
  ): Promise<ServiceListResponse<LedgerWithStats>> {
    let ledgers = await this.getAll(
      params,
      "*, transactions (id, status, type, amount)",
      includeInactive
    );

    // Calculate transaction statistics for each ledger
    ledgers.data = ledgers.data.map((ledger: any) => {
      const transactions = ledger.transactions || [];
      const activeTransactions = transactions.filter(
        (transaction: any) => transaction.status !== "INACTIVE"
      );

      const totalTransactions = activeTransactions.length;
      const totalRevenue = activeTransactions
        .filter((t: any) => t.type === "DEBIT")
        .reduce((sum: number, t: any) => sum + (t.amount || 0), 0);
      const totalExpenses = activeTransactions
        .filter((t: any) => t.type === "CREDIT")
        .reduce((sum: number, t: any) => sum + Math.abs(t.amount || 0), 0);

      return {
        ...ledger,
        totalTransactions,
        totalRevenue,
        totalExpenses,
        balance: totalRevenue - totalExpenses,
        transactions: totalTransactions, // Keep backward compatibility
      };
    });

    return ledgers;
  }

  /**
   * Get ledger by ID
   */
  async getLedgerById(id: string): Promise<ServiceResponse<Ledger>> {
    return this.getById(id);
  }

  /**
   * Update ledger
   */
  async updateLedger(
    id: string,
    data: LedgerUpdate
  ): Promise<ServiceResponse<Ledger>> {
    return this.update(id, data);
  }

  /**
   * Delete ledger with cascading status update to related transactions
   * This marks both the ledger and all its transactions as INACTIVE
   */
  async deleteLedger(id: string): Promise<ServiceResponse<boolean>> {
    try {
      // Import transaction service dynamically to avoid circular dependency
      const { TransactionService } = await import("./transactions");
      const transactionService = new TransactionService();

      // First, update all related transactions to INACTIVE status
      const transactionUpdateResult =
        await transactionService.updateTransactionsByLedgerStatus(
          id,
          "INACTIVE"
        );

      if (!transactionUpdateResult.success) {
        return {
          success: false,
          data: false,
          error: `Failed to update related transactions: ${transactionUpdateResult.error}`,
        };
      }

      // Then, mark the ledger as INACTIVE (soft delete)
      const ledgerUpdateResult = await this.update(id, { status: "INACTIVE" });

      if (!ledgerUpdateResult.success) {
        return {
          success: false,
          data: false,
          error: `Failed to update ledger status: ${ledgerUpdateResult.error}`,
        };
      }

      return {
        success: true,
        data: true,
        error: null,
      };
    } catch (error) {
      return {
        success: false,
        data: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to delete ledger and related transactions",
      };
    }
  }

  /**
   * Get statistics for a specific ledger
   */
  async getLedgerStats(
    ledgerId: string
  ): Promise<ServiceResponse<LedgerStats>> {
    try {
      // Get all transactions for this ledger excluding INACTIVE ones
      const { data: transactions, error } = await this.supabase
        .from("transactions")
        .select("*")
        .eq("ledger_id", ledgerId)
        .neq("status", "INACTIVE")
        .order("created_at", { ascending: false });

      if (error) {
        return {
          success: false,
          data: {
            totalTransactions: 0,
            totalIncome: 0,
            totalExpenses: 0,
            netAmount: 0,
          },
          error: error.message,
        };
      }

      const totalTransactions = transactions?.length || 0;
      const totalIncome =
        transactions
          ?.filter((t: any) => t.type === "DEBIT")
          .reduce((sum: number, t: any) => sum + (t.amount || 0), 0) || 0;
      const totalExpenses =
        transactions
          ?.filter((t: any) => t.type === "CREDIT")
          .reduce((sum: number, t: any) => sum + Math.abs(t.amount || 0), 0) ||
        0;
      const netAmount = totalIncome - totalExpenses;
      const lastTransactionDate = transactions?.[0]?.created_at || undefined;

      return {
        success: true,
        data: {
          totalTransactions,
          totalIncome,
          totalExpenses,
          netAmount,
          lastTransactionDate,
        },
        error: null,
      };
    } catch (error: any) {
      return {
        success: false,
        data: {
          totalTransactions: 0,
          totalIncome: 0,
          totalExpenses: 0,
          netAmount: 0,
        },
        error: error.message || "Failed to get ledger statistics",
      };
    }
  }

  /**
   * Get overall ledger statistics (excludes INACTIVE ledgers)
   */
  async getOverallLedgerStats(): Promise<
    ServiceResponse<{
      totalLedgers: number;
      activeLedgers: number;
      pendingLedgers: number;
      ledgersByStatus: { status: string; count: number }[];
      recentLedgers: Ledger[];
    }>
  > {
    try {
      // Get all ledgers excluding INACTIVE ones
      const { data: ledgers, error } = await this.supabase
        .from("ledgers")
        .select("*")
        .neq("status", "INACTIVE")
        .order("created_at", { ascending: false, nullsFirst: false })
        .order("updated_at", { ascending: false, nullsFirst: false });

      if (error) {
        return {
          success: false,
          data: {
            totalLedgers: 0,
            activeLedgers: 0,
            pendingLedgers: 0,
            ledgersByStatus: [],
            recentLedgers: [],
          },
          error: error.message,
        };
      }

      const totalLedgers = ledgers.length;
      const activeLedgers = ledgers.filter(
        (l: any) => l.status === "ACTIVE"
      ).length;
      const pendingLedgers = ledgers.filter(
        (l: any) => l.status === "PENDING"
      ).length;

      // Group by status
      const statusCounts = ledgers.reduce((acc: any, ledger: any) => {
        const status = ledger.status || "UNKNOWN";
        acc[status] = (acc[status] || 0) + 1;
        return acc;
      }, {});

      const ledgersByStatus = Object.entries(statusCounts).map(
        ([status, count]) => ({
          status,
          count: count as number,
        })
      );

      return {
        success: true,
        data: {
          totalLedgers,
          activeLedgers,
          pendingLedgers,
          ledgersByStatus,
          recentLedgers: ledgers.slice(0, 10) as Ledger[], // Last 10 ledgers
        },
        error: null,
      };
    } catch (error) {
      return {
        success: false,
        data: {
          totalLedgers: 0,
          activeLedgers: 0,
          pendingLedgers: 0,
          ledgersByStatus: [],
          recentLedgers: [],
        },
        error:
          error instanceof Error
            ? error.message
            : "Failed to get ledger statistics",
      };
    }
  }

  /**
   * Create a ledger from a popular book category
   */
  async createLedgerFromCategory(
    category: keyof typeof LEDGER_CATEGORIES,
    accountId: string,
    customName?: string,
    customBooks?: string[],
    customTags?: string[],
    associatedTable?: string,
    associatedId?: string
  ): Promise<ServiceResponse<Ledger>> {
    const categoryData = LEDGER_CATEGORIES[category];

    const ledgerData: LedgerInsert = {
      name: customName || categoryData.name,
      books: customBooks,
      status: "ACTIVE" as any,
      tags: customTags || [...categoryData.tags],
      associated_table: associatedTable,
      associated_id: associatedId,
      account_id: accountId,
    };

    return this.createLedger(ledgerData);
  }

  /**
   * Get ledgers by category tags
   */
  async getLedgersByCategory(
    category: keyof typeof LEDGER_CATEGORIES,
    params?: QueryParams
  ): Promise<ServiceListResponse<Ledger>> {
    const categoryData = LEDGER_CATEGORIES[category];
    return this.getLedgersByTags([...categoryData.tags], params);
  }

  /**
   * Get ledgers by tags
   */
  async getLedgersByTags(
    tags: string[],
    params?: QueryParams
  ): Promise<ServiceListResponse<Ledger>> {
    try {
      let query = this.supabase
        .from("ledgers" as any)
        .select("*", { count: "exact" })
        .neq("status", "INACTIVE");

      // Filter by tags using PostgreSQL array overlap operator
      if (tags.length > 0) {
        query = query.overlaps("tags", tags);
      }

      // Apply additional filters
      if (params?.filters) {
        Object.entries(params.filters).forEach(([key, value]) => {
          if (value !== null && value !== undefined && value !== "") {
            query = query.eq(key, value);
          }
        });
      }

      // Apply sorting
      if (params?.column) {
        query = query.order(params.column, {
          ascending: params.ascending ?? false,
        });
      } else {
        query = query
          .order("created_at", { ascending: false, nullsFirst: false })
          .order("updated_at", { ascending: false, nullsFirst: false });
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit);
      }
      if (params?.offset) {
        query = query.range(
          params.offset,
          params.offset + (params.limit || 50) - 1
        );
      }

      const { data, error, count } = await query;

      if (error) {
        return {
          success: false,
          data: [],
          error: error.message,
          count: 0,
        };
      }

      return {
        success: true,
        data: data as unknown as Ledger[],
        error: null,
        count: count || 0,
      };
    } catch (error: any) {
      return {
        success: false,
        data: [],
        error: error.message || "Failed to get ledgers by tags",
        count: 0,
      };
    }
  }

  /**
   * Find or create "Realised Invoices" ledger for invoice close transactions
   */
  async findOrCreateRealisedInvoicesLedger(
    accountId: string
  ): Promise<ServiceResponse<Ledger>> {
    try {
      // First, try to find existing "Realised Invoices" ledger
      const existingLedgersResult = await this.getAllLedgers({
        filters: {
          name: "Realised Invoices",
          account_id: accountId,
        },
        limit: 1,
      });

      if (
        existingLedgersResult.success &&
        existingLedgersResult.data &&
        existingLedgersResult.data.length > 0
      ) {
        return {
          success: true,
          data: existingLedgersResult.data[0] || null,
          error: null,
        };
      }

      // If not found, create new "Realised Invoices" ledger
      console.log(
        "Creating new Realised Invoices ledger for account:",
        accountId
      );
      return this.createLedgerFromCategory(
        "REALISED_INVOICES",
        accountId,
        "Realised Invoices"
      );
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error:
          error.message || "Failed to find or create Realised Invoices ledger",
      };
    }
  }

  /**
   * Get popular tags used in ledgers
   */
  async getPopularLedgerTags(
    limit: number = 20
  ): Promise<ServiceResponse<{ tag: string; count: number }[]>> {
    try {
      const { data: ledgers, error } = await this.supabase
        .from("ledgers" as any)
        .select("tags")
        .neq("status", "INACTIVE")
        .not("tags", "is", null);

      if (error) {
        return {
          success: false,
          data: [],
          error: error.message,
        };
      }

      // Count tag occurrences
      const tagCounts: { [tag: string]: number } = {};
      ledgers.forEach((ledger: any) => {
        if (ledger.tags && Array.isArray(ledger.tags)) {
          ledger.tags.forEach((tag: string) => {
            tagCounts[tag] = (tagCounts[tag] || 0) + 1;
          });
        }
      });

      // Sort by count and limit
      const popularTags = Object.entries(tagCounts)
        .map(([tag, count]) => ({ tag, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, limit);

      return {
        success: true,
        data: popularTags,
        error: null,
      };
    } catch (error: any) {
      return {
        success: false,
        data: [],
        error: error.message || "Failed to get popular ledger tags",
      };
    }
  }
}

// Create service instance
export const ledgerService = new LedgerService();
