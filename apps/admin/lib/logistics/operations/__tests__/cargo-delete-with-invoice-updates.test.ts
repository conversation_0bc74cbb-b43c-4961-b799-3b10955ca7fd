/**
 * Test file for cargo deletion with invoice updates functionality
 * This tests the enhanced delete method that handles related invoice data
 */

import { cargoService } from '../cargos';
import { invoiceService } from '../invoices';

// Mock the invoice service
jest.mock('../invoices', () => ({
  invoiceService: {
    deleteInvoice: jest.fn(),
  },
}));

// Mock the supabase client
jest.mock('../../supabase/client', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        contains: jest.fn(() => ({
          data: [],
          error: null,
        })),
        not: jest.fn(() => ({
          data: [],
          error: null,
        })),
        eq: jest.fn(() => ({
          single: jest.fn(() => ({
            data: null,
            error: null,
          })),
        })),
      })),
      update: jest.fn(() => ({
        eq: jest.fn(() => ({
          data: null,
          error: null,
        })),
      })),
    })),
  },
}));

describe('Cargo Delete with Invoice Updates', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('removeCargoFromInvoices', () => {
    it('should delete invoice when it only contains the deleted cargo', async () => {
      // Mock data for an invoice with only one line item
      const mockInvoice = {
        id: 'invoice-1',
        line_items: [
          {
            cargo_id: 'cargo-1',
            description: 'Test cargo',
            quantity: 1,
            unitPrice: 100,
            factor_value: 1,
            total: 100,
          },
        ],
        subtotal: 100,
        total: 100,
      };

      const mockCargo = {
        id: 'cargo-1',
        tracking_number: 'TRK-001',
      };

      // Mock supabase queries
      const mockSupabase = require('../../supabase/client').supabase;
      mockSupabase.from.mockReturnValue({
        select: jest.fn(() => ({
          contains: jest.fn(() => ({
            data: [mockInvoice],
            error: null,
          })),
        })),
        update: jest.fn(() => ({
          eq: jest.fn(() => ({
            data: null,
            error: null,
          })),
        })),
      });

      // Mock invoice service delete method
      const mockDeleteInvoice = invoiceService.deleteInvoice as jest.MockedFunction<typeof invoiceService.deleteInvoice>;
      mockDeleteInvoice.mockResolvedValue({
        success: true,
        data: true,
        error: null,
      });

      // Call the private method through the public method
      const result = await cargoService.deleteCargoWithInvoiceUpdates('cargo-1');

      // Verify that the invoice was deleted
      expect(mockDeleteInvoice).toHaveBeenCalledWith('invoice-1');
      expect(mockDeleteInvoice).toHaveBeenCalledTimes(1);
    });

    it('should update invoice when it has multiple line items', async () => {
      // Mock data for an invoice with multiple line items
      const mockInvoice = {
        id: 'invoice-1',
        line_items: [
          {
            cargo_id: 'cargo-1',
            description: 'Test cargo 1',
            quantity: 1,
            unitPrice: 100,
            factor_value: 1,
            total: 100,
          },
          {
            cargo_id: 'cargo-2',
            description: 'Test cargo 2',
            quantity: 1,
            unitPrice: 200,
            factor_value: 1,
            total: 200,
          },
        ],
        subtotal: 300,
        total: 300,
      };

      const mockCargo = {
        id: 'cargo-1',
        tracking_number: 'TRK-001',
      };

      // Mock supabase queries
      const mockSupabase = require('../../supabase/client').supabase;
      const mockUpdate = jest.fn(() => ({
        eq: jest.fn(() => ({
          data: null,
          error: null,
        })),
      }));

      mockSupabase.from.mockReturnValue({
        select: jest.fn(() => ({
          contains: jest.fn(() => ({
            data: [mockInvoice],
            error: null,
          })),
        })),
        update: mockUpdate,
      });

      // Mock invoice service delete method (should not be called)
      const mockDeleteInvoice = invoiceService.deleteInvoice as jest.MockedFunction<typeof invoiceService.deleteInvoice>;
      mockDeleteInvoice.mockResolvedValue({
        success: true,
        data: true,
        error: null,
      });

      // Call the private method through the public method
      const result = await cargoService.deleteCargoWithInvoiceUpdates('cargo-1');

      // Verify that the invoice was updated, not deleted
      expect(mockDeleteInvoice).not.toHaveBeenCalled();
      expect(mockUpdate).toHaveBeenCalledWith({
        line_items: [
          {
            cargo_id: 'cargo-2',
            description: 'Test cargo 2',
            quantity: 1,
            unitPrice: 200,
            factor_value: 1,
            total: 200,
          },
        ],
        subtotal: 200,
        total: 200,
        updated_at: expect.any(String),
      });
    });

    it('should handle multiple invoices correctly', async () => {
      // Mock data for multiple invoices
      const mockInvoices = [
        {
          id: 'invoice-1',
          line_items: [
            {
              cargo_id: 'cargo-1',
              description: 'Test cargo',
              quantity: 1,
              unitPrice: 100,
              factor_value: 1,
              total: 100,
            },
          ],
        },
        {
          id: 'invoice-2',
          line_items: [
            {
              cargo_id: 'cargo-1',
              description: 'Test cargo',
              quantity: 1,
              unitPrice: 150,
              factor_value: 1,
              total: 150,
            },
            {
              cargo_id: 'cargo-2',
              description: 'Other cargo',
              quantity: 1,
              unitPrice: 200,
              factor_value: 1,
              total: 200,
            },
          ],
        },
      ];

      const mockCargo = {
        id: 'cargo-1',
        tracking_number: 'TRK-001',
      };

      // Mock supabase queries
      const mockSupabase = require('../../supabase/client').supabase;
      const mockUpdate = jest.fn(() => ({
        eq: jest.fn(() => ({
          data: null,
          error: null,
        })),
      }));

      mockSupabase.from.mockReturnValue({
        select: jest.fn(() => ({
          contains: jest.fn(() => ({
            data: mockInvoices,
            error: null,
          })),
        })),
        update: mockUpdate,
      });

      // Mock invoice service delete method
      const mockDeleteInvoice = invoiceService.deleteInvoice as jest.MockedFunction<typeof invoiceService.deleteInvoice>;
      mockDeleteInvoice.mockResolvedValue({
        success: true,
        data: true,
        error: null,
      });

      // Call the private method through the public method
      const result = await cargoService.deleteCargoWithInvoiceUpdates('cargo-1');

      // Verify that invoice-1 was deleted (single line item) and invoice-2 was updated (multiple line items)
      expect(mockDeleteInvoice).toHaveBeenCalledWith('invoice-1');
      expect(mockDeleteInvoice).toHaveBeenCalledTimes(1);
      expect(mockUpdate).toHaveBeenCalledWith({
        line_items: [
          {
            cargo_id: 'cargo-2',
            description: 'Other cargo',
            quantity: 1,
            unitPrice: 200,
            factor_value: 1,
            total: 200,
          },
        ],
        subtotal: 200,
        total: 200,
        updated_at: expect.any(String),
      });
    });
  });
});
