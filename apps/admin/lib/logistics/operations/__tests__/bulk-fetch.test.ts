import { describe, it, expect, beforeEach, vi } from "vitest";
import { bulkFetchService, DetailLevel } from "../bulk-fetch";

// Mock Supabase client
const mockSupabase = {
  from: vi.fn(() => ({
    select: vi.fn(() => ({
      neq: vi.fn(() => ({
        order: vi.fn(() => ({
          limit: vi.fn(() => ({
            range: vi.fn(() =>
              Promise.resolve({
                data: [],
                error: null,
                count: 0,
              })
            ),
          })),
        })),
      })),
    })),
  })),
};

// Mock the BaseService
vi.mock("../base/service", () => ({
  BaseService: class {
    protected supabase = mockSupabase;
    protected applyFilters = vi.fn((query, filters) => query);
  },
}));

describe("BulkFetchService", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    bulkFetchService.clearCache();
  });

  describe("fetchEntityType", () => {
    it("should fetch customers with minimal detail level", async () => {
      const mockData = [
        {
          id: "1",
          name: "Test Customer",
          email: "<EMAIL>",
          phone: "123456789",
          status: "ACTIVE",
        },
      ];

      // Setup mock chain
      const mockRange = vi.fn(() =>
        Promise.resolve({
          data: mockData,
          error: null,
          count: 1,
        })
      );
      const mockLimit = vi.fn(() => ({ range: mockRange }));
      const mockOrder = vi.fn(() => ({ limit: mockLimit }));
      const mockNeq = vi.fn(() => ({ order: mockOrder }));
      const mockSelect = vi.fn(() => ({ neq: mockNeq }));
      mockSupabase.from.mockReturnValue({ select: mockSelect });

      const result = await bulkFetchService.fetchEntityType(
        "customers",
        { limit: 10 },
        { detailLevel: DetailLevel.MINIMAL }
      );

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockData);
      expect(result.count).toBe(1);
      expect(result.entityType).toBe("customers");
      expect(mockSupabase.from).toHaveBeenCalledWith("customers");
      expect(mockSelect).toHaveBeenCalledWith(
        "id, name, email, phone, status",
        { count: "exact" }
      );
    });

    it("should fetch cargos with detailed relations", async () => {
      const mockData = [
        {
          id: "1",
          tracking_number: "TRK001",
          particular: "Test Cargo",
          status: "ACTIVE",
          customers: { id: "1", name: "Test Customer" },
        },
      ];

      const mockRange = vi.fn(() =>
        Promise.resolve({
          data: mockData,
          error: null,
          count: 1,
        })
      );
      const mockLimit = vi.fn(() => ({ range: mockRange }));
      const mockOrder = vi.fn(() => ({ limit: mockLimit }));
      const mockNeq = vi.fn(() => ({ order: mockOrder }));
      const mockSelect = vi.fn(() => ({ neq: mockNeq }));
      mockSupabase.from.mockReturnValue({ select: mockSelect });

      const result = await bulkFetchService.fetchEntityType(
        "cargos",
        { limit: 10 },
        { detailLevel: DetailLevel.DETAILED }
      );

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockData);
      expect(mockSelect).toHaveBeenCalledWith(
        expect.stringContaining("customers ("),
        { count: "exact" }
      );
    });

    it("should handle errors gracefully", async () => {
      const mockError = new Error("Database error");
      mockSupabase.from.mockReturnValue({
        select: vi.fn(() => ({
          neq: vi.fn(() => ({
            order: vi.fn(() => ({
              limit: vi.fn(() => ({
                range: vi.fn(() =>
                  Promise.resolve({
                    data: null,
                    error: mockError,
                    count: 0,
                  })
                ),
              })),
            })),
          })),
        })),
      });

      const result = await bulkFetchService.fetchEntityType("customers");

      expect(result.success).toBe(false);
      expect(result.error).toBe("Database error");
      expect(result.data).toEqual([]);
    });
  });

  describe("fetchMultipleEntityTypes", () => {
    it("should fetch multiple entity types in parallel", async () => {
      const mockCustomers = [{ id: "1", name: "Customer 1" }];
      const mockCargos = [{ id: "1", tracking_number: "TRK001" }];

      // Mock successful responses for both entity types
      const mockRange = vi
        .fn()
        .mockResolvedValueOnce({
          data: mockCustomers,
          error: null,
          count: 1,
        })
        .mockResolvedValueOnce({
          data: mockCargos,
          error: null,
          count: 1,
        });

      const mockLimit = vi.fn(() => ({ range: mockRange }));
      const mockOrder = vi.fn(() => ({ limit: mockLimit }));
      const mockNeq = vi.fn(() => ({ order: mockOrder }));
      const mockSelect = vi.fn(() => ({ neq: mockNeq }));
      mockSupabase.from.mockReturnValue({ select: mockSelect });

      const results = await bulkFetchService.fetchMultipleEntityTypes(
        ["customers", "cargos"],
        { limit: 10 }
      );

      expect(results).toHaveLength(2);
      expect(results[0].entityType).toBe("customers");
      expect(results[0].success).toBe(true);
      expect(results[1].entityType).toBe("cargos");
      expect(results[1].success).toBe(true);
    });
  });

  describe("caching", () => {
    it("should cache results when enabled", async () => {
      const mockData = [{ id: "1", name: "Test Customer" }];

      const mockRange = vi.fn(() =>
        Promise.resolve({
          data: mockData,
          error: null,
          count: 1,
        })
      );
      const mockLimit = vi.fn(() => ({ range: mockRange }));
      const mockOrder = vi.fn(() => ({ limit: mockLimit }));
      const mockNeq = vi.fn(() => ({ order: mockOrder }));
      const mockSelect = vi.fn(() => ({ neq: mockNeq }));
      mockSupabase.from.mockReturnValue({ select: mockSelect });

      // First call
      const result1 = await bulkFetchService.fetchEntityType(
        "customers",
        { limit: 10 },
        { cacheResults: true }
      );

      // Second call should use cache
      const result2 = await bulkFetchService.fetchEntityType(
        "customers",
        { limit: 10 },
        { cacheResults: true }
      );

      expect(result1.success).toBe(true);
      expect(result2.success).toBe(true);
      expect(result2.cached).toBe(true);
      expect(mockSupabase.from).toHaveBeenCalledTimes(1); // Only called once due to caching
    });

    it("should provide cache statistics", () => {
      const stats = bulkFetchService.getCacheStats();
      expect(stats).toHaveProperty("size");
      expect(stats).toHaveProperty("keys");
      expect(Array.isArray(stats.keys)).toBe(true);
    });
  });

  describe("specialized methods", () => {
    it("should call getAllCustomersWithCargoStats", async () => {
      const spy = vi.spyOn(bulkFetchService, "fetchEntityType");
      spy.mockResolvedValue({
        entityType: "customers",
        data: [],
        count: 0,
        success: true,
      });

      await bulkFetchService.getAllCustomersWithCargoStats();

      expect(spy).toHaveBeenCalledWith(
        "customers",
        {},
        { detailLevel: DetailLevel.DETAILED }
      );
    });

    it("should call getAllCargosWithRelations", async () => {
      const spy = vi.spyOn(bulkFetchService, "fetchEntityType");
      spy.mockResolvedValue({
        entityType: "cargos",
        data: [],
        count: 0,
        success: true,
      });

      await bulkFetchService.getAllCargosWithRelations();

      expect(spy).toHaveBeenCalledWith(
        "cargos",
        {},
        { detailLevel: DetailLevel.DETAILED }
      );
    });

    it("should call fetchEntitiesByIds with proper ID filtering", async () => {
      const spy = vi.spyOn(bulkFetchService, "fetchEntityType");
      spy.mockResolvedValue({
        entityType: "customers",
        data: [
          { id: "1", name: "Customer 1" },
          { id: "2", name: "Customer 2" },
        ],
        count: 2,
        success: true,
        error: null,
      });

      const ids = ["1", "2"];
      const result = await bulkFetchService.fetchEntitiesByIds(
        "customers",
        ids
      );

      expect(spy).toHaveBeenCalledWith(
        "customers",
        {
          filters: { id: ids },
          limit: ids.length,
        },
        { cacheResults: false }
      );
      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(2);
    });

    it("should handle empty IDs array in fetchEntitiesByIds", async () => {
      const result = await bulkFetchService.fetchEntitiesByIds("customers", []);

      expect(result.success).toBe(true);
      expect(result.data).toEqual([]);
      expect(result.count).toBe(0);
    });
  });
});
