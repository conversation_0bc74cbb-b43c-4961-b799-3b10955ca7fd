import { BaseService } from "../base/service";
import {
  ServiceListResponse,
  QueryParams,
  Customer,
  CargoWithRelations,
  BatchWithRelations,
  FreightWithRelations,
  ShipmentWithRelations,
} from "../types";

// Detail level enum for controlling query complexity
export enum DetailLevel {
  MINIMAL = "minimal",
  STANDARD = "standard",
  DETAILED = "detailed",
}

// Bulk fetch options interface
export interface BulkFetchOptions {
  detailLevel?: DetailLevel;
  includeInactive?: boolean;
  batchSize?: number;
  cacheResults?: boolean;
}

// Entity type mapping for bulk operations
export type EntityType =
  | "customers"
  | "cargos"
  | "batches"
  | "freights"
  | "shipments";

// Bulk fetch result interface
export interface BulkFetchResult<T = any> {
  entityType: EntityType;
  data: T[];
  count: number;
  success: boolean;
  error?: string | null;
  cached?: boolean;
}

// Detail populators for different entity types
class DetailPopulators {
  // Customer detail selectors
  static getCustomerSelect(level: DetailLevel): string {
    switch (level) {
      case DetailLevel.MINIMAL:
        return "id, name, email, phone, status";
      case DetailLevel.STANDARD:
        return "id, name, email, phone, location, status, created_at, updated_at";
      case DetailLevel.DETAILED:
        return `
          *,
          cargos(count)
        `;
      default:
        return "*";
    }
  }

  // Cargo detail selectors
  static getCargoSelect(level: DetailLevel): string {
    switch (level) {
      case DetailLevel.MINIMAL:
        return "id, tracking_number, particular, status, weight_value, weight_unit";
      case DetailLevel.STANDARD:
        return `
          id, tracking_number, particular, status, weight_value, weight_unit,
          cbm_value, customer_id, batch_id, created_at, updated_at
        `;
      case DetailLevel.DETAILED:
        return `
          *,
          customers (
            id,
            name,
            email,
            phone,
            location
          ),
          batches (
            id,
            name,
            code,
            status
          ),
          suppliers (
            id,
            tracking_number,
            phone,
            location
          ),
          accounts (
            id,
            email,
            users (
              id,
              name
            )
          ),
          users:assigned_to (
            id,
            name
          )
        `;
      default:
        return "*";
    }
  }

  // Batch detail selectors
  static getBatchSelect(level: DetailLevel): string {
    switch (level) {
      case DetailLevel.MINIMAL:
        return "id, name, code, status, batch_type";
      case DetailLevel.STANDARD:
        return `
          id, name, code, status, batch_type, origin, destination,
          weight_value, weight_unit, cbm_value, created_at, updated_at
        `;
      case DetailLevel.DETAILED:
        return `
          *,
          cargos (
            id,
            tracking_number,
            particular,
            status,
            weight_value,
            weight_unit,
            cbm_value
          ),
          accounts (
            id,
            email,
            users (
              id,
              name
            )
          )
        `;
      default:
        return "*";
    }
  }

  // Freight detail selectors
  static getFreightSelect(level: DetailLevel): string {
    switch (level) {
      case DetailLevel.MINIMAL:
        return "id, name, scac_codes, status, freight_type";
      case DetailLevel.STANDARD:
        return `
          id, name, scac_codes, status, freight_type, capacity_weight,
          capacity_cbm, created_at, updated_at
        `;
      case DetailLevel.DETAILED:
        return `
          *,
          shipments (
            id,
            tracking_number,
            status,
            bill_of_lading
          ),
          accounts (
            id,
            email,
            users (
              id,
              name
            )
          )
        `;
      default:
        return "*";
    }
  }

  // Shipment detail selectors
  static getShipmentSelect(level: DetailLevel): string {
    switch (level) {
      case DetailLevel.MINIMAL:
        return "id, tracking_number, status, bill_of_lading";
      case DetailLevel.STANDARD:
        return `
          id, tracking_number, status, bill_of_lading, freight_id,
          batch_id, freight_reference_id, created_at, updated_at
        `;
      case DetailLevel.DETAILED:
        return `
          *,
          freights (
            id,
            name,
            scac_codes,
            freight_type
          ),
          batches (
            id,
            name,
            code,
            status
          ),
          accounts (
            id,
            email,
            users (
              id,
              name
            )
          )
        `;
      default:
        return "*";
    }
  }
}

export class BulkFetchService extends BaseService {
  protected tableName = ""; // Not used for bulk operations
  private cache = new Map<string, { data: any; timestamp: number }>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  // Generate cache key for query
  private getCacheKey(
    entityType: EntityType,
    params: QueryParams,
    options: BulkFetchOptions
  ): string {
    return `${entityType}_${JSON.stringify(params)}_${JSON.stringify(options)}`;
  }

  // Check if cached data is still valid
  private isCacheValid(timestamp: number): boolean {
    return Date.now() - timestamp < this.CACHE_TTL;
  }

  // Get cached data if available and valid
  private getCachedData(cacheKey: string): any | null {
    const cached = this.cache.get(cacheKey);
    if (cached && this.isCacheValid(cached.timestamp)) {
      return cached.data;
    }
    return null;
  }

  // Cache data with timestamp
  private setCachedData(cacheKey: string, data: any): void {
    this.cache.set(cacheKey, {
      data,
      timestamp: Date.now(),
    });
  }

  // Clear expired cache entries
  private clearExpiredCache(): void {
    for (const [key, value] of this.cache.entries()) {
      if (!this.isCacheValid(value.timestamp)) {
        this.cache.delete(key);
      }
    }
  }

  // Bulk fetch single entity type with optimized query
  async fetchEntityType<T = any>(
    entityType: EntityType,
    params: QueryParams = {},
    options: BulkFetchOptions = {}
  ): Promise<BulkFetchResult<T>> {
    try {
      const {
        detailLevel = DetailLevel.STANDARD,
        includeInactive = false,
        cacheResults = true,
      } = options;

      // Check cache first if enabled
      if (cacheResults) {
        this.clearExpiredCache();
        const cacheKey = this.getCacheKey(entityType, params, options);
        const cachedData = this.getCachedData(cacheKey);
        if (cachedData) {
          return {
            entityType,
            ...cachedData,
            cached: true,
          };
        }
      }

      // Get appropriate select statement based on entity type and detail level
      let selectStatement: string;
      switch (entityType) {
        case "customers":
          selectStatement = DetailPopulators.getCustomerSelect(detailLevel);
          break;
        case "cargos":
          selectStatement = DetailPopulators.getCargoSelect(detailLevel);
          break;
        case "batches":
          selectStatement = DetailPopulators.getBatchSelect(detailLevel);
          break;
        case "freights":
          selectStatement = DetailPopulators.getFreightSelect(detailLevel);
          break;
        case "shipments":
          selectStatement = DetailPopulators.getShipmentSelect(detailLevel);
          break;
        default:
          selectStatement = "*";
      }

      // Build optimized query
      let query = this.supabase
        .from(entityType)
        .select(selectStatement, { count: "exact" });

      // Apply status filtering
      if (!includeInactive) {
        query = query.neq("status", "INACTIVE");
      }

      // Apply filters
      if (params.filters) {
        query = this.applyFilters(query, params.filters);
      }

      // Apply sorting
      if (params.column) {
        query = query.order(params.column, {
          ascending: params.ascending ?? false,
        });
      } else {
        query = query
          .order("updated_at", { ascending: false, nullsFirst: false })
          .order("created_at", { ascending: false, nullsFirst: false });
      }

      // Apply pagination
      if (params.limit) {
        query = query.limit(params.limit);
        if (params.offset) {
          query = query.range(params.offset, params.offset + params.limit - 1);
        }
      }

      const { data, error, count } = await query;

      if (error) {
        return {
          entityType,
          data: [],
          count: 0,
          success: false,
          error: error.message,
        };
      }

      const result: BulkFetchResult<T> = {
        entityType,
        data: (data || []) as T[],
        count: count || 0,
        success: true,
        error: null,
      };

      // Cache results if enabled
      if (cacheResults) {
        const cacheKey = this.getCacheKey(entityType, params, options);
        this.setCachedData(cacheKey, result);
      }

      return result;
    } catch (error: any) {
      return {
        entityType,
        data: [],
        count: 0,
        success: false,
        error: error.message || `Failed to fetch ${entityType}`,
      };
    }
  }

  // Bulk fetch multiple entity types in parallel
  async fetchMultipleEntityTypes(
    entityTypes: EntityType[],
    params: QueryParams = {},
    options: BulkFetchOptions = {}
  ): Promise<BulkFetchResult[]> {
    try {
      const promises = entityTypes.map((entityType) =>
        this.fetchEntityType(entityType, params, options)
      );

      const results = await Promise.all(promises);
      return results;
    } catch (error: any) {
      return entityTypes.map((entityType) => ({
        entityType,
        data: [],
        count: 0,
        success: false,
        error: error.message || `Failed to fetch ${entityType}`,
      }));
    }
  }

  // Specialized method: Get all customers with cargo statistics
  async getAllCustomersWithCargoStats(
    params: QueryParams = {},
    options: BulkFetchOptions = {}
  ): Promise<ServiceListResponse<Customer & { cargoCount: number }>> {
    try {
      const result = await this.fetchEntityType("customers", params, {
        ...options,
        detailLevel: DetailLevel.DETAILED,
      });

      return {
        data: result.data,
        count: result.count,
        success: result.success,
        error: result.error || null,
      };
    } catch (error: any) {
      return {
        data: [],
        count: 0,
        success: false,
        error: error.message || "Failed to fetch customers with cargo stats",
      };
    }
  }

  // Specialized method: Get all cargos with full relations
  async getAllCargosWithRelations(
    params: QueryParams = {},
    options: BulkFetchOptions = {}
  ): Promise<ServiceListResponse<CargoWithRelations>> {
    try {
      const result = await this.fetchEntityType("cargos", params, {
        ...options,
        detailLevel: DetailLevel.DETAILED,
      });

      return {
        data: result.data,
        count: result.count,
        success: result.success,
        error: result.error || null,
      };
    } catch (error: any) {
      return {
        data: [],
        count: 0,
        success: false,
        error: error.message || "Failed to fetch cargos with relations",
      };
    }
  }

  // Specialized method: Get all batches with cargo information
  async getAllBatchesWithCargo(
    params: QueryParams = {},
    options: BulkFetchOptions = {}
  ): Promise<ServiceListResponse<BatchWithRelations>> {
    try {
      const result = await this.fetchEntityType("batches", params, {
        ...options,
        detailLevel: DetailLevel.DETAILED,
      });

      return {
        data: result.data,
        count: result.count,
        success: result.success,
        error: result.error || null,
      };
    } catch (error: any) {
      return {
        data: [],
        count: 0,
        success: false,
        error: error.message || "Failed to fetch batches with cargo",
      };
    }
  }

  // Specialized method: Get all freights with relations
  async getAllFreightsWithRelations(
    params: QueryParams = {},
    options: BulkFetchOptions = {}
  ): Promise<ServiceListResponse<FreightWithRelations>> {
    try {
      const result = await this.fetchEntityType("freights", params, {
        ...options,
        detailLevel: DetailLevel.DETAILED,
      });

      return {
        data: result.data,
        count: result.count,
        success: result.success,
        error: result.error || null,
      };
    } catch (error: any) {
      return {
        data: [],
        count: 0,
        success: false,
        error: error.message || "Failed to fetch freights with relations",
      };
    }
  }

  // Specialized method: Get all shipments with relations
  async getAllShipmentsWithRelations(
    params: QueryParams = {},
    options: BulkFetchOptions = {}
  ): Promise<ServiceListResponse<ShipmentWithRelations>> {
    try {
      const result = await this.fetchEntityType("shipments", params, {
        ...options,
        detailLevel: DetailLevel.DETAILED,
      });

      return {
        data: result.data,
        count: result.count,
        success: result.success,
        error: result.error || null,
      };
    } catch (error: any) {
      return {
        data: [],
        count: 0,
        success: false,
        error: error.message || "Failed to fetch shipments with relations",
      };
    }
  }

  // Specialized method: Fetch entities by specific IDs
  async fetchEntitiesByIds<T = any>(
    entityType: EntityType,
    ids: string[],
    options: BulkFetchOptions = {}
  ): Promise<BulkFetchResult<T>> {
    try {
      if (!ids || ids.length === 0) {
        return {
          entityType,
          data: [],
          count: 0,
          success: true,
          error: null,
        };
      }

      // Use the existing fetchEntityType method with ID filtering
      const result = await this.fetchEntityType<T>(
        entityType,
        {
          filters: { id: ids }, // This will be handled by applyFilters as an 'in' operation
          limit: ids.length, // Limit to the number of IDs we're looking for
        },
        {
          ...options,
          cacheResults: false, // Don't cache ID-specific queries as they're usually one-time
        }
      );

      return result;
    } catch (error: any) {
      return {
        entityType,
        data: [],
        count: 0,
        success: false,
        error: error.message || `Failed to fetch ${entityType} by IDs`,
      };
    }
  }

  // Batch processing method for large datasets
  async fetchInBatches<T = any>(
    entityType: EntityType,
    params: QueryParams = {},
    options: BulkFetchOptions = {}
  ): Promise<ServiceListResponse<T>> {
    try {
      const { batchSize = 1000 } = options;
      let allData: T[] = [];
      let offset = 0;
      let hasMore = true;
      let totalCount = 0;

      while (hasMore) {
        const batchParams = {
          ...params,
          limit: batchSize,
          offset,
        };

        const result = await this.fetchEntityType<T>(
          entityType,
          batchParams,
          { ...options, cacheResults: false } // Don't cache individual batches
        );

        if (!result.success) {
          return {
            data: allData,
            count: allData.length,
            success: false,
            error: result.error || null,
          };
        }

        allData = allData.concat(result.data);
        totalCount = result.count;

        // Check if we have more data to fetch
        hasMore =
          result.data.length === batchSize && offset + batchSize < totalCount;
        offset += batchSize;
      }

      return {
        data: allData,
        count: totalCount,
        success: true,
        error: null,
      };
    } catch (error: any) {
      return {
        data: [],
        count: 0,
        success: false,
        error: error.message || `Failed to fetch ${entityType} in batches`,
      };
    }
  }

  // Clear all cached data
  clearCache(): void {
    this.cache.clear();
  }

  // Get cache statistics
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }
}

// Export singleton instance
export const bulkFetchService = new BulkFetchService();
