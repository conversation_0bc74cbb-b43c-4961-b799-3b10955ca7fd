/**
 * Frontend Code Generator Service
 *
 * This service provides a frontend interface to the centralized code generator
 * and maintains backward compatibility with existing code.
 *
 * Migrated from:
 * - apps/admin/lib/tracking-generator.ts
 * - Various management modules
 */

// Re-export types for backward compatibility
export interface TrackingNumberParams {
  shippingMode: "AIR" | "SEA" | "ROAD" | "LAND";
  origin: string;
  weight?: number; // in kg
  batchIndex?: number; // If provided, use this index, otherwise generate
}

export interface BatchCodeParams {
  freightName: string;
  shippingMode: string;
  origin: string;
}

export interface CargoTrackingParams {
  category?: "dangerous" | "regular" | "shamwaa";
  customPrefix?: string;
}

export interface InvoiceNumberParams {
  prefix?: string;
  customSuffix?: string;
}

export interface DocumentNumberParams {
  prefix?: string;
  includeDate?: boolean;
}

// Location codes mapping for worldwide shipping locations
const LOCATION_CODES: Record<string, string> = {
  guangzhou: "G",
  shanghai: "S",
  beijing: "B",
  shenzhen: "SZ",
  "hong kong": "HK",
  singapore: "SG",
  dubai: "D",
  london: "L",
  "new york": "NY",
  "los angeles": "LA",
  mumbai: "M",
  karachi: "K",
  "dar es salaam": "DSM",
  nairobi: "NBO",
  lagos: "LOS",
  default: "G", // Default to Guangzhou
};

// Shipping mode prefixes
const SHIPPING_MODES: Record<string, string> = {
  AIR: "AIR",
  SEA: "SEA",
  ROAD: "ROAD",
  LAND: "LAND",
};

// Weight categories
const WEIGHT_CATEGORIES = {
  LIGHT: "L", // < 100kg
  HEAVY: "H", // >= 100kg
};

// Cargo categories for tracking prefixes
const CARGO_CATEGORIES = {
  DANGEROUS: "DGR",
  REGULAR: "REG",
  SHAMWAA: "SHAMWAA",
};

/**
 * Frontend Code Generator Service Class
 * Provides all code generation functionality for the admin application
 */
export class CodeGeneratorService {
  /**
   * Extracts location code from origin string
   */
  private getLocationCode(origin: string): string {
    const normalizedOrigin = origin.toLowerCase().trim();

    // Check for exact matches first
    if (LOCATION_CODES[normalizedOrigin]) {
      return LOCATION_CODES[normalizedOrigin];
    }

    // Check for partial matches
    for (const [location, code] of Object.entries(LOCATION_CODES)) {
      if (normalizedOrigin.includes(location) && location !== "default") {
        return code;
      }
    }

    // Default fallback
    return LOCATION_CODES.default || "G";
  }

  /**
   * Determines weight category based on weight
   */
  private getWeightCategory(weight?: number): string {
    if (!weight) return WEIGHT_CATEGORIES.LIGHT;
    return weight >= 100 ? WEIGHT_CATEGORIES.HEAVY : WEIGHT_CATEGORIES.LIGHT;
  }

  /**
   * Generates a random batch index (3 digits)
   */
  private generateBatchIndex(): string {
    return Math.floor(Math.random() * 999 + 1)
      .toString()
      .padStart(3, "0");
  }

  /**
   * Generates a batch code following the format:
   * BCH/S{FreightType}/{year}{month}/{index}
   * Example: BCH/SA/2412/001
   */
  generateBatchCode(params: BatchCodeParams & { index?: number }): string {
    const { shippingMode, index } = params;

    // Get freight type code (A for air, S for sea)
    const freightType =
      shippingMode.toUpperCase() === "AIR"
        ? "A"
        : shippingMode.toUpperCase() === "SEA"
          ? "S"
          : "R"; // R for road/land

    // Get current date components
    const now = new Date();
    const year = now.getFullYear().toString().slice(-2); // Last 2 digits
    const month = (now.getMonth() + 1).toString().padStart(2, "0"); // Zero-padded month

    // Generate or use provided index
    const batchIndex = index
      ? index.toString().padStart(3, "0")
      : this.generateBatchIndex();

    // Construct batch code: BCH/S{FreightType}/{year}{month}/{index}
    return `BCH/S${freightType}/${year}${month}/${batchIndex}`;
  }

  /**
   * Generates a batch code with sequential index based on existing batch codes
   * Automatically increments index based on existing codes with same prefix
   */
  generateSequentialBatchCode(
    params: BatchCodeParams,
    existingBatchCodes: string[] = []
  ): string {
    const { shippingMode } = params;

    // Get freight type code (A for air, S for sea)
    const freightType =
      shippingMode.toUpperCase() === "AIR"
        ? "A"
        : shippingMode.toUpperCase() === "SEA"
          ? "S"
          : "R"; // R for road/land

    // Get current date components
    const now = new Date();
    const year = now.getFullYear().toString().slice(-2); // Last 2 digits
    const month = (now.getMonth() + 1).toString().padStart(2, "0"); // Zero-padded month

    // Create prefix to match existing codes
    const codePrefix = `BCH/S${freightType}/${year}${month}/`;

    // Find existing codes with same prefix and extract highest index
    let maxIndex = 0;
    existingBatchCodes.forEach((code) => {
      if (code.startsWith(codePrefix)) {
        const indexMatch = code.match(/\/(\d{3})$/);
        if (indexMatch && indexMatch[1]) {
          const index = parseInt(indexMatch[1], 10);
          if (index > maxIndex) {
            maxIndex = index;
          }
        }
      }
    });

    // Generate next index
    const nextIndex = maxIndex + 1;

    return this.generateBatchCode({
      ...params,
      index: nextIndex,
    });
  }

  /**
   * Generates a tracking number following the format:
   * {Prefix}-{LocationCode}-{Year}-{Month}-{Index}-{WeightCategory}
   */
  generateTrackingNumber(params: TrackingNumberParams): string {
    const { shippingMode, origin, weight, batchIndex } = params;

    // Get shipping mode prefix
    const prefix = SHIPPING_MODES[shippingMode] || "AIR";

    // Get location code
    const locationCode = this.getLocationCode(origin);

    // Get current date components
    const now = new Date();
    const year = now.getFullYear().toString().slice(-2); // Last 2 digits
    const month = (now.getMonth() + 1).toString().padStart(2, "0"); // Zero-padded month

    // Generate or use provided batch index
    const index = batchIndex
      ? batchIndex.toString().padStart(3, "0")
      : this.generateBatchIndex();

    // Determine weight category
    const weightCategory = this.getWeightCategory(weight);

    // Construct tracking number
    return `${prefix}-${locationCode}-${year}-${month}-${index}-${weightCategory}`;
  }

  /**
   * Generates a shipment tracking number following the format:
   * SHP/{BatchCode or FreightCode}/{index}
   * Example: SHP/BCH-SA-2412-001/001 or SHP/FRT-001/001
   */
  generateShipmentTrackingNumber(params: {
    batchCode?: string;
    freightCode?: string;
    index?: number;
  }): string {
    const { batchCode, freightCode, index } = params;

    // Use batch code or freight code
    const baseCode = batchCode || freightCode || "UNKNOWN";

    // Generate or use provided index
    const shipmentIndex = index
      ? index.toString().padStart(3, "0")
      : this.generateBatchIndex();

    return `SHP/${baseCode}/${shipmentIndex}`;
  }

  /**
   * Generates a shipment tracking number with sequential index based on existing shipment tracking numbers
   * Automatically increments index based on existing codes with same base code
   */
  generateSequentialShipmentTrackingNumber(
    params: {
      batchCode?: string;
      freightCode?: string;
    },
    existingShipmentTrackingNumbers: string[] = []
  ): string {
    const { batchCode, freightCode } = params;

    // Use batch code or freight code
    const baseCode = batchCode || freightCode || "UNKNOWN";

    // Create prefix to match existing codes
    const codePrefix = `SHP/${baseCode}/`;

    // Find existing codes with same prefix and extract highest index
    let maxIndex = 0;
    existingShipmentTrackingNumbers.forEach((trackingNumber) => {
      if (trackingNumber.startsWith(codePrefix)) {
        const indexMatch = trackingNumber.match(/\/(\d{3})$/);
        if (indexMatch && indexMatch[1]) {
          const index = parseInt(indexMatch[1], 10);
          if (index > maxIndex) {
            maxIndex = index;
          }
        }
      }
    });

    // Generate next index
    const nextIndex = maxIndex + 1;

    return this.generateShipmentTrackingNumber({
      ...params,
      index: nextIndex,
    });
  }

  /**
   * Generates a cargo tracking number following the format:
   * CG/{BatchCode}/{index}
   * Example: CG/BCH-SA-2412-001/001
   */
  generateCargoTrackingNumber(
    params: CargoTrackingParams & {
      batchCode: string;
      index?: number;
    }
  ): string {
    const { batchCode, index } = params;

    // Generate or use provided index
    const cargoIndex = index
      ? index.toString().padStart(3, "0")
      : this.generateBatchIndex();

    // Construct cargo tracking number: CG/{BatchCode}/{index}
    return `CG/${batchCode}/${cargoIndex}`;
  }

  /**
   * Generates a cargo tracking number with sequential index based on existing cargo tracking numbers
   * Automatically increments index based on existing codes with same batch code
   */
  generateSequentialCargoTrackingNumber(
    params: CargoTrackingParams & {
      batchCode: string;
    },
    existingCargoTrackingNumbers: string[] = []
  ): string {
    const { batchCode } = params;

    // Create prefix to match existing codes
    const codePrefix = `CG/${batchCode}/`;

    // Find existing codes with same prefix and extract highest index
    let maxIndex = 0;
    existingCargoTrackingNumbers.forEach((trackingNumber) => {
      if (trackingNumber.startsWith(codePrefix)) {
        const indexMatch = trackingNumber.match(/\/(\d{3})$/);
        if (indexMatch && indexMatch[1]) {
          const index = parseInt(indexMatch[1], 10);
          if (index > maxIndex) {
            maxIndex = index;
          }
        }
      }
    });

    // Generate next index
    const nextIndex = maxIndex + 1;

    return this.generateCargoTrackingNumber({
      ...params,
      batchCode,
      index: nextIndex,
    });
  }

  /**
   * Generates a release authorization code following the format:
   * REL/{cargoTrackingNumber}
   * Example: REL/CG-BCH-SA-2412-001-001
   */
  generateReleaseCode(cargoTrackingNumber: string): string {
    return `REL/${cargoTrackingNumber}`;
  }

  /**
   * Generates an invoice number following the format:
   * INV/{BatchCode}/{customer.name or supplier.tracking_number}
   * Example: INV/BCH-SA-2412-001/ACME-CORP or INV/BCH-SA-2412-001/SUP-001
   */
  generateInvoiceNumber(
    params: InvoiceNumberParams & {
      batchCode: string;
      customerName?: string;
      supplierTrackingNumber?: string;
    }
  ): string {
    const { batchCode, customerName, supplierTrackingNumber } = params;

    // Use customer name or supplier tracking number
    const identifier = customerName || supplierTrackingNumber || "UNKNOWN";

    // Clean identifier (remove spaces, special chars, convert to uppercase)
    const cleanIdentifier = identifier
      .replace(/[^a-zA-Z0-9-]/g, "-")
      .replace(/-+/g, "-")
      .replace(/^-|-$/g, "")
      .toUpperCase();

    return `INV/${batchCode}/${cleanIdentifier}`;
  }

  /**
   * Generates an invoice number with sequential index based on existing invoice numbers
   * Automatically increments index based on existing codes with same batch code and identifier
   */
  generateSequentialInvoiceNumber(
    params: InvoiceNumberParams & {
      batchCode: string;
      customerName?: string;
      supplierTrackingNumber?: string;
    },
    existingInvoiceNumbers: string[] = []
  ): string {
    const { batchCode, customerName, supplierTrackingNumber } = params;

    // Use customer name or supplier tracking number
    const identifier = customerName || supplierTrackingNumber || "UNKNOWN";

    // Clean identifier (remove spaces, special chars, convert to uppercase)
    const cleanIdentifier = identifier
      .replace(/[^a-zA-Z0-9-]/g, "-")
      .replace(/-+/g, "-")
      .replace(/^-|-$/g, "")
      .toUpperCase();

    // Create base invoice number
    const baseInvoiceNumber = `INV/${batchCode}/${cleanIdentifier}`;

    // Check if base number exists, if not return it
    if (!existingInvoiceNumbers.includes(baseInvoiceNumber)) {
      return baseInvoiceNumber;
    }

    // Find existing codes with same base and extract highest index
    let maxIndex = 0;
    const basePattern = `${baseInvoiceNumber}-`;

    existingInvoiceNumbers.forEach((invoiceNumber) => {
      if (invoiceNumber.startsWith(basePattern)) {
        const indexMatch = invoiceNumber.match(/-(\d+)$/);
        if (indexMatch && indexMatch[1]) {
          const index = parseInt(indexMatch[1], 10);
          if (index > maxIndex) {
            maxIndex = index;
          }
        }
      }
    });

    // Generate next index (start from 2 since base number is essentially index 1)
    const nextIndex = maxIndex + 1;

    return `${baseInvoiceNumber}-${nextIndex}`;
  }

  /**
   * Generates a document number (generic)
   */
  generateDocumentNumber(params: DocumentNumberParams = {}): string {
    const { prefix = "DOC", includeDate = true } = params;

    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const day = date.getDate().toString().padStart(2, "0");
    const random = Math.floor(Math.random() * 9999)
      .toString()
      .padStart(4, "0");

    if (includeDate) {
      return `${prefix}-${year}${month}${day}-${random}`;
    } else {
      return `${prefix}-${random}`;
    }
  }

  /**
   * Generates the next sequential tracking number for a given month/year
   */
  async generateSequentialTrackingNumber(
    params: Omit<TrackingNumberParams, "batchIndex">,
    getLastIndexFn?: (
      year: number,
      month: number,
      shippingMode: string,
      locationCode: string
    ) => Promise<number>
  ): Promise<string> {
    const { shippingMode, origin, weight } = params;

    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    const locationCode = this.getLocationCode(origin);

    let nextIndex = 1;

    if (getLastIndexFn) {
      try {
        const lastIndex = await getLastIndexFn(
          year,
          month,
          shippingMode,
          locationCode
        );
        nextIndex = lastIndex + 1;
      } catch (error) {
        console.warn("Failed to get last index, using default:", error);
      }
    }

    return this.generateTrackingNumber({
      shippingMode,
      origin,
      weight,
      batchIndex: nextIndex,
    });
  }

  /**
   * Generates a tracking number with sequential index based on existing tracking numbers
   * Automatically increments index based on existing codes with same prefix pattern
   */
  generateSequentialTrackingNumberSync(
    params: Omit<TrackingNumberParams, "batchIndex">,
    existingTrackingNumbers: string[] = []
  ): string {
    const { shippingMode, origin, weight } = params;

    // Get shipping mode prefix
    const prefix = SHIPPING_MODES[shippingMode] || "AIR";

    // Get location code
    const locationCode = this.getLocationCode(origin);

    // Get current date components
    const now = new Date();
    const year = now.getFullYear().toString().slice(-2); // Last 2 digits
    const month = (now.getMonth() + 1).toString().padStart(2, "0"); // Zero-padded month

    // Determine weight category
    const weightCategory = this.getWeightCategory(weight);

    // Create prefix to match existing codes
    const codePrefix = `${prefix}-${locationCode}-${year}-${month}-`;
    const codeSuffix = `-${weightCategory}`;

    // Find existing codes with same prefix and suffix, extract highest index
    let maxIndex = 0;
    existingTrackingNumbers.forEach((trackingNumber) => {
      if (
        trackingNumber.startsWith(codePrefix) &&
        trackingNumber.endsWith(codeSuffix)
      ) {
        const indexMatch = trackingNumber.match(/-(\d{3})-[HL]$/);
        if (indexMatch && indexMatch[1]) {
          const index = parseInt(indexMatch[1], 10);
          if (index > maxIndex) {
            maxIndex = index;
          }
        }
      }
    });

    // Generate next index
    const nextIndex = maxIndex + 1;

    return this.generateTrackingNumber({
      shippingMode,
      origin,
      weight,
      batchIndex: nextIndex,
    });
  }

  // ============ VALIDATION FUNCTIONS ============

  /**
   * Validates a batch code format
   * Format: BCH/S{FreightType}/{year}{month}/{index}
   */
  validateBatchCode(batchCode: string): boolean {
    const pattern = /^BCH\/S[ASR]\/\d{4}\/\d{3}$/;
    return pattern.test(batchCode);
  }

  /**
   * Validates a tracking number format
   */
  validateTrackingNumber(trackingNumber: string): boolean {
    const pattern =
      /^(AIR|SEA|ROAD|LAND)-([A-Z]{1,3})-(\d{2})-(\d{2})-(\d{3})-(H|L)$/;
    return pattern.test(trackingNumber);
  }

  /**
   * Parses a tracking number into its components
   */
  parseTrackingNumber(trackingNumber: string): {
    shippingMode: "AIR" | "SEA" | "ROAD" | "LAND";
    locationCode: string;
    year: number;
    month: number;
    batchIndex: number;
    weightCategory: "H" | "L";
    isHeavy: boolean;
  } {
    const pattern =
      /^(AIR|SEA|ROAD|LAND)-([A-Z]{1,3})-(\d{2})-(\d{2})-(\d{3})-(H|L)$/;
    const match = trackingNumber.match(pattern);

    if (!match) {
      throw new Error("Invalid tracking number format");
    }

    const [, prefix, locationCode, year, month, index, weightCategory] = match;

    return {
      shippingMode: prefix as "AIR" | "SEA" | "ROAD" | "LAND",
      locationCode: locationCode || "",
      year: parseInt(`20${year || "00"}`), // Convert to full year
      month: parseInt(month || "01"),
      batchIndex: parseInt(index || "001"),
      weightCategory: (weightCategory as "H" | "L") || "L",
      isHeavy: weightCategory === "H",
    };
  }

  /**
   * Validates a shipment tracking number format
   * Format: SHP/{BatchCode or FreightCode}/{index}
   */
  validateShipmentTrackingNumber(trackingNumber: string): boolean {
    const pattern = /^SHP\/[A-Z0-9\/-]+\/\d{3}$/;
    return pattern.test(trackingNumber);
  }

  /**
   * Validates a cargo tracking number format
   * Format: CG/{BatchCode}/{index}
   */
  validateCargoTrackingNumber(trackingNumber: string): boolean {
    const pattern = /^CG\/[A-Z0-9\/-]+\/\d{3}$/;
    return pattern.test(trackingNumber);
  }

  /**
   * Validates a release code format
   * Format: REL/{cargoTrackingNumber}
   */
  validateReleaseCode(releaseCode: string): boolean {
    const pattern = /^REL\/CG\/[A-Z0-9\/-]+\/\d{3}$/;
    return pattern.test(releaseCode);
  }

  /**
   * Validates an invoice number format
   * Format: INV/{BatchCode}/{customer.name or supplier.tracking_number}
   */
  validateInvoiceNumber(invoiceNumber: string): boolean {
    const pattern = /^INV\/[A-Z0-9\/-]+\/[A-Z0-9\-]+$/;
    return pattern.test(invoiceNumber);
  }

  // ============ CONSTANTS EXPORT ============

  getLocationCodes(): Record<string, string> {
    return { ...LOCATION_CODES };
  }

  getShippingModes(): Record<string, string> {
    return { ...SHIPPING_MODES };
  }

  getWeightCategories(): typeof WEIGHT_CATEGORIES {
    return { ...WEIGHT_CATEGORIES };
  }
}

// Export singleton instance for global application reference
export const codeGeneratorService = new CodeGeneratorService();

// Export legacy functions for backward compatibility
export function generateBatchCode(
  params: BatchCodeParams & { index?: number }
): string {
  return codeGeneratorService.generateBatchCode(params);
}

export function generateTrackingNumber(params: TrackingNumberParams): string {
  return codeGeneratorService.generateTrackingNumber(params);
}

export function validateBatchCode(batchCode: string): boolean {
  return codeGeneratorService.validateBatchCode(batchCode);
}

export function parseTrackingNumber(trackingNumber: string): {
  shippingMode: "AIR" | "SEA" | "ROAD" | "LAND";
  locationCode: string;
  year: number;
  month: number;
  batchIndex: number;
  weightCategory: "H" | "L";
  isHeavy: boolean;
} {
  return codeGeneratorService.parseTrackingNumber(trackingNumber);
}

export async function generateSequentialTrackingNumber(
  params: Omit<TrackingNumberParams, "batchIndex">,
  getLastIndexFn?: (
    year: number,
    month: number,
    shippingMode: string,
    locationCode: string
  ) => Promise<number>
): Promise<string> {
  return codeGeneratorService.generateSequentialTrackingNumber(
    params,
    getLastIndexFn
  );
}

// Export new sequential generation functions
export function generateSequentialBatchCode(
  params: BatchCodeParams,
  existingBatchCodes: string[] = []
): string {
  return codeGeneratorService.generateSequentialBatchCode(
    params,
    existingBatchCodes
  );
}

export function generateSequentialCargoTrackingNumber(
  params: CargoTrackingParams & { batchCode: string },
  existingCargoTrackingNumbers: string[] = []
): string {
  return codeGeneratorService.generateSequentialCargoTrackingNumber(
    params,
    existingCargoTrackingNumbers
  );
}

export function generateSequentialShipmentTrackingNumber(
  params: { batchCode?: string; freightCode?: string },
  existingShipmentTrackingNumbers: string[] = []
): string {
  return codeGeneratorService.generateSequentialShipmentTrackingNumber(
    params,
    existingShipmentTrackingNumbers
  );
}

export function generateSequentialInvoiceNumber(
  params: InvoiceNumberParams & {
    batchCode: string;
    customerName?: string;
    supplierTrackingNumber?: string;
  },
  existingInvoiceNumbers: string[] = []
): string {
  return codeGeneratorService.generateSequentialInvoiceNumber(
    params,
    existingInvoiceNumbers
  );
}

export function generateSequentialTrackingNumberSync(
  params: Omit<TrackingNumberParams, "batchIndex">,
  existingTrackingNumbers: string[] = []
): string {
  return codeGeneratorService.generateSequentialTrackingNumberSync(
    params,
    existingTrackingNumbers
  );
}

// Export helper functions and constants for external use
export function getLocationCode(origin: string): string {
  return codeGeneratorService["getLocationCode"](origin);
}

export function getWeightCategory(weight?: number): string {
  return codeGeneratorService["getWeightCategory"](weight);
}

export { LOCATION_CODES, SHIPPING_MODES, WEIGHT_CATEGORIES };
