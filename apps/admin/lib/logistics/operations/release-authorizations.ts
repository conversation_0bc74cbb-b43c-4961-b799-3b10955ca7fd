import { BaseService } from "../base/service";
import {
  ServiceResponse,
  ServiceListResponse,
  QueryParams,
  StatusEnum,
  CargoWithRelations,
} from "../types";
import { notificationService } from "../system/notifications";
import { codeGeneratorService } from "./code-generator";

// Release Authorization Types
export interface ReleaseAuthorizationBase {
  id: string;
  cargo_id: string;
  status: StatusEnum;
  release_code: string;
  authorized_by: string;
  authorized_date: string | null;
  qr_verified: boolean;
  qr_verified_at: string | null;
  qr_verified_by: string | null;
  documents_verified: boolean;
  payment_verified: boolean;
  customs_cleared: boolean;
  recipient_verified: boolean;
  verification_notes: string | null;
  pickup_authorized_person: string | null;
  notification_sent: boolean;
  created_at: string | null;
  updated_at: string | null;
}

export interface ReleaseAuthorizationInsert {
  cargo_id: string;
  code: string;
  authorized_by: string;
  qr_verified?: boolean;
  documents_verified?: boolean;
  documents_generated?: boolean;
  payment_verified?: boolean;
  customs_cleared?: boolean;
  recipient_verified?: boolean;
  verification_notes?: string;
  pickup_authorized_person?: string;
}

export interface ReleaseAuthorizationUpdate {
  status?: StatusEnum;
  authorized_date?: string;
  qr_verified?: boolean;
  qr_verified_at?: string;
  qr_verified_by?: string;
  documents_verified?: boolean;
  payment_verified?: boolean;
  customs_cleared?: boolean;
  recipient_verified?: boolean;
  verification_notes?: string;
  pickup_authorized_person?: string;
  notification_sent?: boolean;
}

export interface ReleaseAuthorizationWithRelations
  extends ReleaseAuthorizationBase {
  cargo?: CargoWithRelations;
  authorized_by_user?: {
    id: string;
    name: string;
    email: string;
  };
  handover?: {
    id: string;
    handover_date: string;
    qr_verification: boolean;
    biometric_verification: boolean;
    verification_data: any;
    location_data: any;
    handover_notes: string;
  };
}

// Cargo interface for release authorization workflow
export interface CargoForRelease {
  id: string;
  customer: string;
  customerPhone?: string;
  cargoType: string;
  arrivalDate: string;
  docsComplete: boolean;
  paymentComplete: boolean;
  status: string;
  origin?: string; // Note: Origin/destination no longer available from freight relations
  destination?: string; // Note: Origin/destination no longer available from freight relations
  weight?: string;
  container?: string;
  agent?: string;
  requestedDate?: string;
  authorizedDate?: string;
  releaseCode?: string;
  trackingNumber?: string;
  scannedByDeviceId?: string;
  // Timestamp fields
  createdAt: string;
  updatedAt: string;
  // Release authorization specific fields
  releaseAuthorization?: {
    id: string;
    status: StatusEnum;
    authorizedBy: string;
    authorizedDate: string | null;
    qrVerified: boolean;
    qrVerifiedAt: string | null;
    qrVerifiedBy: string | null;
    documentsVerified: boolean;
    paymentVerified: boolean;
    customsCleared: boolean;
    recipientVerified: boolean;
    verificationNotes: string | null;
    pickupAuthorizedPerson: string | null;
    notificationSent: boolean;
    createdAt: string | null;
    updatedAt: string | null;
  };
  // Computed release status fields
  hasReleaseAuthorization?: boolean;
  releaseStatus?:
    | "not_initiated"
    | "pending"
    | "authorized"
    | "completed"
    | "on_hold";
  documentGenerated?: boolean;
  shared?: boolean; // Track if document has been shared with customer
  // Handover-specific fields
  handoverId?: string;
  handoverDate?: string;
  handoverNotes?: string | null;
  qrVerification?: boolean | null;
  biometricVerification?: boolean | null;
  verificationData?: any;
  locationData?: any;
  // Enhanced verification data fields
  authorizedBy?: string;
  verificationNotes?: string;
  documentGeneratedAt?: string;
  documentGeneratedBy?: string;
  // Batch information
  batchCode?: string;
  batchId?: string;
}

// Backward compatibility alias
// @deprecated Use CargoForRelease instead
export type ShipmentForRelease = CargoForRelease;

interface QRVerificationData {
  cargo_id: string;
  verification_time: string;
  location: string;
  verified_by: string;
  device_info?: any;
}

export class ReleaseAuthorizationService extends BaseService<any, any, any> {
  protected tableName = "handovers";

  // Get all cargo eligible for release authorization
  async getCargoForRelease(
    params?: QueryParams
  ): Promise<ServiceListResponse<CargoForRelease>> {
    try {
      let query = this.supabase
        .from("handovers")
        .select(
          `
          id,
          handover_date,
          handover_notes,
          qr_verification,
          biometric_verification,
          verification_data,
          status,
          location_data,
          created_at,
          updated_at,
          cargos!cargo_id (
            id,
            tracking_number,
            particular,
            status,
            weight_value,
            weight_unit,
            total_price,
            created_at,
            updated_at,
            customers (
              id,
              name,
              email,
              phone,
              location
            ),
            batches (
              id,
              name,
              code,
              type
            )
          )
        `
        )
        .order("created_at", { ascending: false, nullsFirst: false })
        .order("updated_at", { ascending: false, nullsFirst: false });

      if (params?.filters) {
        const { status, search } = params.filters;
        if (status && status !== "all") {
          if (typeof status === "string") {
            query = query.eq("status", status as any);
          }
        }
        if (search && typeof search === "string") {
          query = query.or(
            `tracking_number.ilike.%${search}%,` +
              `particular.ilike.%${search}%,` +
              `customers.name.ilike.%${search}%`
          );
        }
      }

      if (params?.page && params?.limit) {
        const { page = 1, limit = 10 } = params;
        const start = (page - 1) * limit;
        const end = start + limit - 1;
        query = query.range(start, end);
      }

      const { data, error, count } = await query;

      console.log("🔍 Release Authorization Query Results:", {
        dataCount: data?.length || 0,
        error: error?.message,
        sampleData: data?.slice(0, 2), // Log first 2 items for debugging
      });

      if (error) {
        console.error("❌ Query error:", error);
        return { success: false, data: [], error: error.message };
      }

      // Data is now from handovers table with cargo relations

      // Transform handover data to CargoForRelease format
      const cargoItems: CargoForRelease[] = (data || []).map(
        (handover: any) => {
          const cargo = handover.cargos;
          const releaseAuth = this.transformHandoverToReleaseAuth(handover);
          const releaseStatus = this.determineReleaseStatus(handover);
          // const documentGenerated = this.hasDocumentGenerated(handover); // Unused for now

          // Handle case where cargo data is missing
          if (!cargo) {
            return {
              id: handover.id,
              customer: "Unknown Customer",
              cargoType: "General",
              arrivalDate: new Date(
                handover.created_at || ""
              ).toLocaleDateString(),
              docsComplete: false,
              paymentComplete: false,
              status: this.mapStatusForUI(handover.status || "PENDING"),
              origin: "N/A",
              destination: "N/A",
              weight: undefined,
              container: "N/A",
              agent: "N/A",
              requestedDate: handover.created_at
                ? new Date(handover.created_at).toLocaleDateString()
                : undefined,
              trackingNumber: "N/A",
              createdAt: handover.created_at || new Date().toISOString(),
              updatedAt: handover.updated_at || new Date().toISOString(),
              authorizedDate: releaseAuth?.authorizedDate
                ? new Date(releaseAuth.authorizedDate).toLocaleDateString()
                : undefined,
              releaseCode:
                (handover?.verification_data as any)?.release_code || undefined,
              scannedByDeviceId: releaseAuth?.qrVerifiedBy || undefined,
              releaseAuthorization: releaseAuth,
              hasReleaseAuthorization: true,
              releaseStatus,
              documentGenerated:
                (handover?.verification_data as any)?.documents_generated ||
                false,
              shared: (handover?.verification_data as any)?.shared || false,
              // Enhanced verification data mapping
              authorizedBy:
                (handover?.verification_data as any)?.authorized_by ||
                undefined,
              verificationNotes:
                (handover?.verification_data as any)?.verification_notes ||
                undefined,
              documentGeneratedAt:
                (handover?.verification_data as any)?.document_generated_at ||
                undefined,
              documentGeneratedBy:
                (handover?.verification_data as any)?.document_generated_by ||
                undefined,
              // Handover-specific fields
              handoverId: handover.id,
              handoverDate: handover.handover_date,
              handoverNotes: handover.handover_notes,
              qrVerification: handover.qr_verification,
              biometricVerification: handover.biometric_verification,
              verificationData: handover.verification_data,
              locationData: handover.location_data,
              // Batch information (not available when cargo data is missing)
              batchCode: undefined,
              batchId: undefined,
            };
          }

          return {
            id: cargo.id,
            customer: cargo.customers?.name || "Unknown Customer",
            customerPhone: cargo.customers?.phone,
            cargoType: cargo.batches?.type || "General",
            arrivalDate: new Date(cargo.created_at || "").toLocaleDateString(),
            docsComplete: this.isDocumentationComplete(cargo),
            paymentComplete: this.isPaymentComplete(cargo),
            status: this.mapStatusForUI(cargo.status || "PENDING"),
            origin: "N/A", // Origin/destination no longer available from freight relations
            destination: "N/A", // Origin/destination no longer available from freight relations
            weight: cargo.weight_value
              ? `${cargo.weight_value} ${cargo.weight_unit || "kg"}`
              : undefined,
            container: cargo.tracking_number,
            agent: "N/A", // Agent info not available in current query
            requestedDate: cargo.created_at
              ? new Date(cargo.created_at).toLocaleDateString()
              : undefined,
            trackingNumber: cargo.tracking_number,
            // Timestamp fields
            createdAt: cargo.created_at || new Date().toISOString(),
            updatedAt: cargo.updated_at || new Date().toISOString(),
            // Release authorization data
            authorizedDate: releaseAuth?.authorizedDate
              ? new Date(releaseAuth.authorizedDate).toLocaleDateString()
              : undefined,
            releaseCode:
              (handover?.verification_data as any)?.release_code || undefined,
            scannedByDeviceId: releaseAuth?.qrVerifiedBy || undefined,
            releaseAuthorization: releaseAuth,
            hasReleaseAuthorization: true,
            releaseStatus,
            documentGenerated:
              (handover?.verification_data as any)?.documents_generated ||
              false,
            shared: (handover?.verification_data as any)?.shared || false,
            // Enhanced verification data mapping
            authorizedBy:
              (handover?.verification_data as any)?.authorized_by || undefined,
            verificationNotes:
              (handover?.verification_data as any)?.verification_notes ||
              undefined,
            documentGeneratedAt:
              (handover?.verification_data as any)?.document_generated_at ||
              undefined,
            documentGeneratedBy:
              (handover?.verification_data as any)?.document_generated_by ||
              undefined,
            // Batch information
            batchCode: cargo.batches?.code || undefined,
            batchId: cargo.batches?.id || undefined,
            // Handover-specific fields
            handoverId: handover.id,
            handoverDate: handover.handover_date,
            handoverNotes: handover.handover_notes,
            qrVerification: handover.qr_verification,
            biometricVerification: handover.biometric_verification,
            verificationData: handover.verification_data,
            locationData: handover.location_data,
          };
        }
      );

      return {
        success: true,
        data: cargoItems,
        error: null,
        count: count || 0,
      };
    } catch (error) {
      console.error("Error fetching cargo for release:", error);
      return {
        success: false,
        data: [],
        error: error instanceof Error ? error.message : "Failed to fetch cargo",
      };
    }
  }

  // Get all shipments eligible for release authorization (backward compatibility)
  // @deprecated Use getCargoForRelease instead
  async getShipmentsForRelease(
    params?: QueryParams
  ): Promise<ServiceListResponse<CargoForRelease>> {
    return this.getCargoForRelease(params);
  }

  // Get single shipment/cargo for release with customer information
  async getShipmentForRelease(cargoId: string): Promise<ServiceResponse<any>> {
    try {
      const { data, error } = await this.supabase
        .from("handovers")
        .select(
          `
          id,
          handover_date,
          handover_notes,
          qr_verification,
          biometric_verification,
          verification_data,
          status,
          location_data,
          created_at,
          updated_at,
          cargos!cargo_id (
            id,
            tracking_number,
            particular,
            status,
            weight_value,
            weight_unit,
            total_price,
            created_at,
            updated_at,
            customers (
              id,
              name,
              email,
              phone,
              location
            ),
            batches (
              id,
              name,
              code,
              type
            )
          )
        `
        )
        .eq("cargo_id", cargoId)
        .single();

      if (error) {
        return { success: false, data: null, error: error.message };
      }

      if (!data || !data.cargos) {
        return {
          success: false,
          data: null,
          error: "Cargo not found or not eligible for release authorization",
        };
      }

      // Transform the data to match expected format
      const transformedData = {
        ...data.cargos,
        customers: data.cargos.customers,
        handover: {
          id: data.id,
          status: data.status,
          verification_data: data.verification_data,
        },
      };

      return { success: true, data: transformedData, error: null };
    } catch (error) {
      console.error("Error fetching shipment for release:", error);
      return {
        success: false,
        data: null,
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch shipment for release",
      };
    }
  }

  // Get release authorization statistics
  async getReleaseAuthorizationStats(): Promise<
    ServiceResponse<{
      totalCargo: number;
      pendingAuthorization: number;
      authorized: number;
      onHold: number;
    }>
  > {
    try {
      const { data: totalData, error: totalError } = await this.supabase
        .from("cargos")
        .select("id", { count: "exact" })
        .in("status", ["IN_TRANSIT", "DELIVERED", "PROCESSING", "RELEASED"])
        .order("created_at", { ascending: false, nullsFirst: false })
        .order("updated_at", { ascending: false, nullsFirst: false });

      const { data: pendingData, error: pendingError } = await this.supabase
        .from("cargos")
        .select("id", { count: "exact" })
        .in("status", ["IN_TRANSIT", "DELIVERED", "PROCESSING"])
        .order("created_at", { ascending: false, nullsFirst: false })
        .order("updated_at", { ascending: false, nullsFirst: false });

      const { data: authorizedData, error: authorizedError } =
        await this.supabase
          .from("cargos")
          .select("id", { count: "exact" })
          .eq("status", "RELEASED")
          .order("created_at", { ascending: false, nullsFirst: false })
          .order("updated_at", { ascending: false, nullsFirst: false });

      const { data: holdData, error: holdError } = await this.supabase
        .from("cargos")
        .select("id", { count: "exact" })
        .eq("status", "PROCESSING")
        .order("updated_at", { ascending: false, nullsFirst: false })
        .order("created_at", { ascending: false }); // Assuming PROCESSING means on hold

      if (totalError || pendingError || authorizedError || holdError) {
        return {
          success: false,
          data: null,
          error: "Failed to fetch release authorization statistics",
        };
      }

      return {
        success: true,
        data: {
          totalCargo: totalData?.length || 0,
          pendingAuthorization: pendingData?.length || 0,
          authorized: authorizedData?.length || 0,
          onHold: holdData?.length || 0,
        },
        error: null,
      };
    } catch (error) {
      console.error("Error fetching release authorization stats:", error);
      return {
        success: false,
        data: null,
        error:
          error instanceof Error ? error.message : "Failed to fetch statistics",
      };
    }
  }

  // Update verification data for a handover record
  async updateVerificationData(
    cargoId: string,
    verificationData: any
  ): Promise<ServiceResponse<any>> {
    try {
      const { data, error } = await this.supabase
        .from("handovers")
        .update({
          verification_data: verificationData,
          updated_at: new Date().toISOString(),
        })
        .eq("cargo_id", cargoId)
        .select()
        .single();

      if (error) {
        return {
          success: false,
          data: null,
          error: error.message,
        };
      }

      return {
        success: true,
        data,
        error: null,
      };
    } catch (error) {
      console.error("Error updating verification data:", error);
      return {
        success: false,
        data: null,
        error:
          error instanceof Error
            ? error.message
            : "Failed to update verification data",
      };
    }
  }

  // Generate release code
  generateReleaseCode(cargoTrackingNumber: string): string {
    return codeGeneratorService.generateReleaseCode(cargoTrackingNumber);
  }

  // Helper method to determine release status
  private determineReleaseStatus(
    handover: any
  ): CargoForRelease["releaseStatus"] {
    if (!handover) return "not_initiated";

    switch (handover.status) {
      case "PENDING":
        return "pending";
      case "APPROVED":
      case "COMPLETED":
        return handover.qr_verification ? "completed" : "authorized";
      case "REJECTED":
      case "CANCELLED":
        return "on_hold";
      default:
        return "pending";
    }
  }

  // Note: Removed hasDocumentGenerated method as it's no longer used

  // Helper method to transform handover data to release authorization format
  private transformHandoverToReleaseAuth(
    handover: any
  ): CargoForRelease["releaseAuthorization"] | undefined {
    if (!handover) return undefined;

    const verificationData = handover.verification_data || {};

    return {
      id: handover.id,
      status: handover.status,
      authorizedBy: verificationData.authorized_by || "",
      authorizedDate: handover.handover_date,
      qrVerified: handover.qr_verification || false,
      qrVerifiedAt: verificationData.qr_verification?.verification_time || null,
      qrVerifiedBy: verificationData.qr_verification?.verified_by || null,
      documentsVerified: verificationData.documents_verified || false,
      paymentVerified: verificationData.payment_verified || false,
      customsCleared: verificationData.customs_cleared || false,
      recipientVerified: verificationData.recipient_verified || false,
      verificationNotes: handover.handover_notes,
      pickupAuthorizedPerson: verificationData.pickup_authorized_person || null,
      notificationSent: false, // This would need to be tracked separately
      createdAt: handover.created_at,
      updatedAt: handover.updated_at,
    };
  }

  // Create release authorization
  async createReleaseAuthorization(
    data: ReleaseAuthorizationInsert
  ): Promise<ServiceResponse<any>> {
    try {
      // First, verify the cargo exists and is eligible
      const { data: cargo, error: cargoError } = await this.supabase
        .from("cargos")
        .select("*")
        .eq("id", data.cargo_id)
        .single();

      if (cargoError || !cargo) {
        return {
          success: false,
          data: null,
          error: "Cargo not found or ineligible for release authorization",
        };
      }

      // Check if release authorization already exists
      const { data: existingAuth } = await this.supabase
        .from("handovers")
        .select("*")
        .eq("cargo_id", data.cargo_id)
        .single();

      if (existingAuth) {
        return {
          success: false,
          data: null,
          error: "Release authorization already exists for this cargo",
        };
      }

      // Create handover record
      const handoverData = {
        cargo_id: data.cargo_id,
        handover_date: new Date().toISOString(),
        qr_verification: data.qr_verified || false,
        biometric_verification: false,
        verification_data: {
          release_code: data.code,
          authorized_by: data.authorized_by,
          documents_generated: data.documents_generated || false,
          documents_verified: data.documents_verified || false,
          payment_verified: data.payment_verified || false,
          customs_cleared: data.customs_cleared || false,
          recipient_verified: data.recipient_verified || false,
          pickup_authorized_person: data.pickup_authorized_person,
          verification_notes: data.verification_notes,
        },
        status: "PENDING" as any,
        handover_notes:
          data.verification_notes || "Release authorization initiated",
      };

      const { data: handover, error: handoverError } = await this.supabase
        .from("handovers")
        .insert(handoverData)
        .select()
        .single();

      if (handoverError) {
        return { success: false, data: null, error: handoverError.message };
      }

      // Create approval record
      const approvalData = {
        details: {
          type: "release_authorization",
          cargo_id: data.cargo_id,
          release_code: data.code,
          authorized_by: data.authorized_by,
          step: 1,
          workflow_data: handoverData.verification_data,
        },
        status: "PENDING" as any,
        associated_table: "cargos",
        associated_id: data.cargo_id,
      };

      const { error: approvalError } = await this.supabase
        .from("approvals")
        .insert(approvalData)
        .select()
        .single();

      if (approvalError) {
        console.warn("Failed to create approval record:", approvalError);
      }

      // Create notification for release authorization creation
      if (cargo.account_id) {
        try {
          const trackingNumber = cargo.tracking_number || data.cargo_id;
          await notificationService.createTargetedNotification({
            account_id: cargo.account_id,
            name: "Release Authorization Created",
            message: `Release authorization has been created for your cargo ${trackingNumber}. Please prepare for pickup.`,
            associated_table: "handovers",
            associated_id: handover.id,
            to: "*", // Personalized notification
            details: {
              cargoId: data.cargo_id,
              trackingNumber,
              releaseCode: data.code,
              authorizedBy: data.authorized_by,
              timestamp: new Date().toISOString(),
            },
          });

          console.log(
            `✅ Release authorization notification created for cargo ${trackingNumber}`
          );
        } catch (notificationError) {
          console.warn(
            "⚠️  Failed to create release authorization notification:",
            notificationError
          );
        }
      }

      return { success: true, data: handover, error: null };
    } catch (error) {
      console.error("Error creating release authorization:", error);
      return {
        success: false,
        data: null,
        error:
          error instanceof Error
            ? error.message
            : "Failed to create release authorization",
      };
    }
  }

  // Update release authorization status
  async updateReleaseAuthorizationStatus(
    cargoId: string,
    status: StatusEnum,
    additionalData?: Partial<ReleaseAuthorizationUpdate>
  ): Promise<ServiceResponse<any>> {
    try {
      const updateData = {
        status,
        ...additionalData,
        updated_at: new Date().toISOString(),
      };

      const { data, error } = await this.supabase
        .from("handovers")
        .update(updateData)
        .eq("cargo_id", cargoId)
        .select()
        .single();

      if (error) {
        return { success: false, data: null, error: error.message };
      }

      // If status is APPROVED or COMPLETED, also update the cargo status to RELEASED
      if (status === "APPROVED" || status === "COMPLETED") {
        const { error: cargoError } = await this.supabase
          .from("cargos")
          .update({ status: "RELEASED" })
          .eq("id", cargoId);

        if (cargoError) {
          console.warn("Failed to update cargo status:", cargoError);
        }
      }

      return { success: true, data, error: null };
    } catch (error) {
      console.error("Error updating release authorization status:", error);
      return {
        success: false,
        data: null,
        error:
          error instanceof Error
            ? error.message
            : "Failed to update release authorization",
      };
    }
  }

  // Verify QR code
  async verifyQRCode(
    cargoId: string,
    verificationData: QRVerificationData
  ): Promise<ServiceResponse<any>> {
    try {
      // First get the current verification_data
      const { data: currentData } = await this.supabase
        .from("handovers")
        .select("verification_data")
        .eq("cargo_id", cargoId)
        .single();

      const currentVerificationData =
        (currentData?.verification_data as any) || {};
      const updatedVerificationData = {
        ...currentVerificationData,
        qr_verification: {
          verified: true,
          verification_time: verificationData.verification_time,
          location: verificationData.location,
          verified_by: verificationData.verified_by,
          device_info: verificationData.device_info,
        },
      };

      const { data, error } = await this.supabase
        .from("handovers")
        .update({
          qr_verification: true,
          verification_data: updatedVerificationData,
          updated_at: new Date().toISOString(),
        })
        .eq("cargo_id", cargoId)
        .select()
        .single();

      if (error) {
        return { success: false, data: null, error: error.message };
      }

      return { success: true, data, error: null };
    } catch (error) {
      console.error("Error verifying QR code:", error);
      return {
        success: false,
        data: null,
        error:
          error instanceof Error ? error.message : "Failed to verify QR code",
      };
    }
  }

  // Get existing release authorization for a cargo
  async getReleaseAuthorizationByCargoId(
    cargoId: string
  ): Promise<ServiceResponse<ReleaseAuthorizationWithRelations | null>> {
    try {
      // First get the handover record
      const { data: handoverData, error: handoverError } = await this.supabase
        .from("handovers")
        .select(
          `
          id,
          cargo_id,
          status,
          handover_date,
          qr_verification,
          biometric_verification,
          verification_data,
          created_at,
          updated_at
        `
        )
        .eq("cargo_id", cargoId)
        .single();

      if (handoverError && handoverError.code !== "PGRST116") {
        return { success: false, data: null, error: handoverError.message };
      }

      if (!handoverData) {
        return { success: true, data: null, error: null };
      }

      // Then get the cargo data separately
      const { data: cargoData, error: cargoError } = await this.supabase
        .from("cargos")
        .select(
          `
          id,
          tracking_number,
          particular,
          status,
          weight_value,
          weight_unit,
          total_price,
          created_at,
          updated_at,
          customers (
            id,
            name,
            email,
            phone,
            location
          )
        `
        )
        .eq("id", cargoId)
        .single();

      if (cargoError) {
        return { success: false, data: null, error: cargoError.message };
      }

      if (!cargoData) {
        return { success: false, data: null, error: "Cargo not found" };
      }

      // Transform the data to match our interface
      const verificationData = (handoverData.verification_data as any) || {};
      const releaseAuth: ReleaseAuthorizationWithRelations = {
        id: handoverData.id,
        cargo_id: handoverData.cargo_id || "",
        status: (handoverData.status || "PENDING") as any,
        release_code: verificationData.release_code || "",
        authorized_by: verificationData.authorized_by || "",
        authorized_date: handoverData.handover_date,
        qr_verified: handoverData.qr_verification || false,
        qr_verified_at:
          verificationData.qr_verification?.verification_time || null,
        qr_verified_by: verificationData.qr_verification?.verified_by || null,
        documents_verified: verificationData.documents_verified || false,
        payment_verified: verificationData.payment_verified || false,
        customs_cleared: verificationData.customs_cleared || false,
        recipient_verified: verificationData.recipient_verified || false,
        verification_notes: verificationData.verification_notes || null,
        pickup_authorized_person:
          verificationData.pickup_authorized_person || null,
        notification_sent: false,
        created_at: handoverData.created_at || "",
        updated_at: handoverData.updated_at || "",
        cargo: cargoData as any, // Type assertion for partial cargo data
      };

      return { success: true, data: releaseAuth, error: null };
    } catch (error) {
      console.error("Error getting release authorization:", error);
      return {
        success: false,
        data: null,
        error:
          error instanceof Error
            ? error.message
            : "Failed to get release authorization",
      };
    }
  }

  // Complete release authorization
  async completeReleaseAuthorization(
    cargoId: string
  ): Promise<ServiceResponse<any>> {
    try {
      console.log(
        `🔄 Starting release authorization completion for cargo: ${cargoId}`
      );

      // First check if handover exists
      const { data: existingHandover, error: handoverCheckError } =
        await this.supabase
          .from("handovers")
          .select("*")
          .eq("cargo_id", cargoId)
          .single();

      if (handoverCheckError && handoverCheckError.code !== "PGRST116") {
        return {
          success: false,
          data: null,
          error: `Error checking handover: ${handoverCheckError.message}`,
        };
      }

      if (!existingHandover) {
        return {
          success: false,
          data: null,
          error:
            "No release authorization found for this cargo. Please create one first.",
        };
      }

      // Update handover status to COMPLETED
      const { data: handover, error: handoverError } = await this.supabase
        .from("handovers")
        .update({
          status: "COMPLETED",
          handover_date: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          verification_data: {
            ...((existingHandover.verification_data as any) || {}),
            completed_by: "system",
            completion_time: new Date().toISOString(),
            final_verification: true,
          },
        })
        .eq("cargo_id", cargoId)
        .select()
        .single();

      if (handoverError) {
        console.error("❌ Error updating handover:", handoverError);
        return {
          success: false,
          data: null,
          error: `Failed to update handover: ${handoverError.message}`,
        };
      }

      console.log("✅ Handover updated successfully");

      // Update cargo status to RELEASED
      const { error: cargoError } = await this.supabase
        .from("cargos")
        .update({
          status: "RELEASED",
          updated_at: new Date().toISOString(),
        })
        .eq("id", cargoId);

      if (cargoError) {
        console.error("❌ Error updating cargo status:", cargoError);
        // Don't fail the entire operation for cargo update errors, but log them
        console.warn(
          "Failed to update cargo status to RELEASED:",
          cargoError.message
        );
      } else {
        console.log("✅ Cargo status updated to RELEASED");
      }

      // Update approval status to APPROVED
      const { error: approvalError } = await this.supabase
        .from("approvals")
        .update({
          status: "APPROVED",
          updated_at: new Date().toISOString(),
        })
        .eq("associated_id", cargoId)
        .eq("associated_table", "cargos");

      if (approvalError) {
        console.error("❌ Error updating approval status:", approvalError);
        console.warn(
          "Failed to update approval status:",
          approvalError.message
        );
      } else {
        console.log("✅ Approval status updated to APPROVED");
      }

      console.log(
        `🎉 Release authorization completed successfully for cargo: ${cargoId}`
      );

      // Create notification for release authorization completion
      try {
        // Get cargo details for notification
        const { data: cargo, error: cargoError } = await this.supabase
          .from("cargos")
          .select("account_id, tracking_number")
          .eq("id", cargoId)
          .single();

        if (!cargoError && cargo?.account_id) {
          const trackingNumber = cargo.tracking_number || cargoId;
          await notificationService.createTargetedNotification({
            account_id: cargo.account_id,
            name: "Release Authorization Completed",
            message: `Your cargo ${trackingNumber} has been authorized for release. You can now proceed with pickup.`,
            associated_table: "handovers",
            associated_id: handover.id,
            to: "*", // Personalized notification
            details: {
              cargoId,
              trackingNumber,
              releaseCode: (existingHandover.verification_data as any)
                ?.release_code,
              completedBy: "system",
              timestamp: new Date().toISOString(),
            },
          });

          console.log(
            `✅ Release authorization completion notification created for cargo ${trackingNumber}`
          );
        }
      } catch (notificationError) {
        console.warn(
          "⚠️  Failed to create release authorization completion notification:",
          notificationError
        );
      }

      return {
        success: true,
        data: {
          handover,
          message: "Release authorization completed successfully",
          cargoId,
          releaseCode: (existingHandover.verification_data as any)
            ?.release_code,
        },
        error: null,
      };
    } catch (error) {
      console.error("❌ Error completing release authorization:", error);
      return {
        success: false,
        data: null,
        error:
          error instanceof Error
            ? error.message
            : "Failed to complete release authorization",
      };
    }
  }

  // Helper methods
  private isDocumentationComplete(cargo: any): boolean {
    // This would check if all required documents are present
    // For now, we'll use a simple heuristic
    return !!(cargo.particular && cargo.tracking_number);
  }

  private isPaymentComplete(cargo: any): boolean {
    // This would check payment status from related tables
    // For now, we'll use total_price as an indicator
    return !!(cargo.total_price && cargo.total_price > 0);
  }

  private mapStatusForUI(status: string): string {
    const statusMap: Record<string, string> = {
      CREATED: "pending",
      PROCESSING: "pending",
      IN_TRANSIT: "pending",
      DELIVERED: "pending",
      RELEASED: "authorized",
      PICKED_UP: "authorized",
      APPROVED: "authorized",
      REJECTED: "hold",
    };
    return statusMap[status] || "pending";
  }

  // Update shared status for a release authorization
  async updateSharedStatus(
    cargoId: string,
    shared: boolean = true
  ): Promise<ServiceResponse<any>> {
    try {
      // Get current verification data
      const { data: currentData } = await this.supabase
        .from("handovers")
        .select("verification_data")
        .eq("cargo_id", cargoId)
        .single();

      const currentVerificationData =
        (currentData?.verification_data as any) || {};

      // Update verification data with shared status
      const updatedVerificationData = {
        ...currentVerificationData,
        shared,
        shared_at: shared ? new Date().toISOString() : null,
      };

      const { data, error } = await this.supabase
        .from("handovers")
        .update({
          verification_data: updatedVerificationData,
          updated_at: new Date().toISOString(),
        })
        .eq("cargo_id", cargoId)
        .select()
        .single();

      if (error) {
        return { success: false, data: null, error: error.message };
      }

      return { success: true, data, error: null };
    } catch (error) {
      console.error("Error updating shared status:", error);
      return {
        success: false,
        data: null,
        error:
          error instanceof Error
            ? error.message
            : "Failed to update shared status",
      };
    }
  }

  // Note: Removed unused helper methods for freight relations
}

export const releaseAuthorizationService = new ReleaseAuthorizationService();
