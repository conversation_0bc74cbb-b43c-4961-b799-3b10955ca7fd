import { BaseService } from "../base/service";
import {
  prepareInvoiceTemplateData,
  generateGeneralInvoiceDocument,
  type GeneratedInvoiceDocument,
} from "../../invoice-template-generator";
import type { ServiceResponse } from "../types";
// Note: toast will be called from the UI layer, not from service layer

// Lazy imports to avoid circular dependencies
let cargoService: any = null;
let ledgerService: any = null;
let transactionService: any = null;

async function getCargoService() {
  if (!cargoService) {
    const { cargoService: service } = await import("./cargos");
    cargoService = service;
  }
  return cargoService;
}

async function getLedgerService() {
  if (!ledgerService) {
    const { ledgerService: service } = await import("./ledgers");
    ledgerService = service;
  }
  return ledgerService;
}

async function getTransactionService() {
  if (!transactionService) {
    const { transactionService: service } = await import("./transactions");
    transactionService = service;
  }
  return transactionService;
}

// Updated Invoice interface to match the provided schema
export interface InvoiceData {
  id: string;
  due_at: string | null;
  inv_number: string | null;
  terms_and_conditions: string | null;
  notes: string | null;
  line_items: any | null; // JSONB field
  customer_id: string | null;
  batch_id?: string | null; // Batch ID for direct batch association
  billing_address: string | null;
  supplier_id: string | null;
  subtotal?: number | null;
  total?: number | null;
  status: string | null; // Invoice status field
  type?: string | null; // Invoice type field (e.g., "AUTOMATED")
  currency_conv_rate?: number | null; // Custom USD to TZS conversion rate
  shared?: boolean | null; // Track if invoice has been shared with customer
  created_at: string;
  updated_at: string | null;
}

export interface InvoiceInsert {
  due_at?: string | null;
  inv_number?: string | null;
  terms_and_conditions?: string | null;
  notes?: string | null;
  line_items?: any | null;
  customer_id?: string | null;
  batch_id?: string | null;
  billing_address?: string | null;
  subtotal?: number | null;
  total?: number | null;
  status?: string | null;
  currency_conv_rate?: number | null;
  shared?: boolean | null;
}

export interface InvoiceUpdate extends Partial<InvoiceInsert> {}

export interface InvoiceWithCustomer extends InvoiceData {
  customer?: {
    id: string;
    name: string;
    email: string | null;
    phone: string | null;
    location: string | null;
  } | null;
  batch?: {
    id: string;
    code: string;
    name: string;
    freight_type: string;
  } | null;
}

export interface InvoiceWithRelations extends InvoiceData {
  customer?: {
    id: string;
    name: string;
    email: string | null;
    phone: string | null;
    location: string | null;
  } | null;
  supplier?: {
    id: string;
    tracking_number: string | null;
    phone: string | null;
    location: string | null;
    cargo_tracking_number: string | null;
    batch_number: string | null;
  } | null;
  batches?: {
    id: string;
    code: string;
    name: string;
  } | null;
}

export interface GeneratedInvoiceResult {
  invoice: InvoiceData;
  document?: GeneratedInvoiceDocument;
}

export class InvoiceService extends BaseService<
  InvoiceData,
  InvoiceInsert,
  InvoiceUpdate
> {
  protected tableName = "invoices" as const;

  /**
   * Create a new invoice with document generation
   */

  async getCurrentUSDTZSRate() {
    const url: string =
      "https://v6.exchangerate-api.com/v6/************************/pair/USD/TZS";
    let rate = await fetch(url, {
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": window.location.origin,
        "Access-Control-Allow-Methods": "GET, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
      },
    });
    let conv = await rate.json();

    if (conv.result === "success") return parseFloat(conv.conversion_rate);
    return 2622;
  }

  async createInvoice(
    data: InvoiceInsert
  ): Promise<ServiceResponse<GeneratedInvoiceResult>> {
    try {
      // Use custom conversion rate if provided, otherwise fetch current internet rate
      let conversionRate: number;

      if (data.currency_conv_rate && data.currency_conv_rate > 0) {
        // Use the custom conversion rate from batch
        conversionRate = data.currency_conv_rate;
      } else {
        // Fetch current internet rate as fallback
        conversionRate = await this.getCurrentUSDTZSRate();
      }

      let payload = {
        ...data,
        currency_conv_rate: conversionRate,
      };

      // Create the invoice record with calculated totals
      const result = await this.create(payload);

      if (!result.success || !result.data) {
        return {
          data: null,
          error: result.error || "Failed to create invoice",
          success: false,
        };
      }

      // Generate invoice document if needed
      let documentInfo = undefined;
      if (data.line_items && data.customer_id) {
        try {
          const documentResult = await this.generateInvoiceDocument(
            result.data
          );
          if (documentResult.success) {
            documentInfo = documentResult.data;
          }
        } catch (docError) {
          console.warn("Failed to generate invoice document:", docError);
          // Don't fail the invoice creation if document generation fails
        }
      }

      return {
        data: {
          invoice: result.data,
          document: documentInfo || undefined,
        },
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to create invoice",
        success: false,
      };
    }
  }

  /**
   * Generate invoice document using template generator
   */
  private async generateInvoiceDocument(
    invoice: InvoiceData
  ): Promise<ServiceResponse<GeneratedInvoiceDocument>> {
    try {
      // Get invoice with customer data for template generation
      const invoiceWithCustomer = await this.getInvoiceWithCustomer(invoice.id);

      if (!invoiceWithCustomer.success || !invoiceWithCustomer.data) {
        return {
          success: false,
          data: null,
          error: "Failed to fetch invoice with customer data",
        };
      }

      // Prepare template data
      const templateData = prepareInvoiceTemplateData(invoiceWithCustomer.data);

      // Generate document using template generator
      const documentResult = await generateGeneralInvoiceDocument(templateData);

      if (!documentResult.success || !documentResult.data) {
        return {
          success: false,
          data: null,
          error: documentResult.error || "Failed to generate invoice document",
        };
      }

      return {
        success: true,
        data: documentResult.data,
        error: null,
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: error.message || "Failed to generate invoice document",
      };
    }
  }

  /**
   * Get all invoices with customer relations
   */
  async getAllInvoicesWithCustomers(params?: {
    limit?: number;
    offset?: number;
  }): Promise<ServiceResponse<InvoiceWithCustomer[]>> {
    try {
      const { data: invoices, error } = await this.supabase
        .from(this.tableName)
        .select(
          `
          *,
          customer:customer_id (
            id,
            name,
            email,
            phone,
            location
          ),
          batch:batch_id (
            id,
            code,
            name,
            freight_type
          )
        `
        )
        .not("status", "eq", "INACTIVE") // Filter out soft-deleted invoices
        .order("created_at", { ascending: false, nullsFirst: false })
        .order("updated_at", { ascending: false, nullsFirst: false })
        .limit(params?.limit || 50)
        .range(
          params?.offset || 0,
          (params?.offset || 0) + (params?.limit || 50) - 1
        );

      if (error) {
        return {
          success: false,
          data: [],
          error: error.message,
        };
      }

      return {
        success: true,
        data: (invoices || []) as unknown as InvoiceWithCustomer[],
        error: null,
      };
    } catch (error: any) {
      return {
        data: [],
        error: error.message || "Failed to fetch invoices",
        success: false,
      };
    }
  }

  /**
   * Get all invoices with both customer and supplier relations
   * Optimized to reduce N+1 query problem by batching related entity fetches
   */
  async getAllInvoicesWithRelations(options?: {
    limit?: number;
    offset?: number;
  }): Promise<ServiceResponse<InvoiceWithRelations[]>> {
    try {
      console.log(
        "[InvoiceService] Starting getAllInvoicesWithRelations with optimized queries"
      );

      // First, get all invoices (excluding INACTIVE ones for soft delete)
      const { data: invoices, error } = await this.supabase
        .from(this.tableName)
        .select("*, batches (id, code), customers (email, phone)")
        .not("status", "eq", "INACTIVE") // Filter out soft-deleted invoices
        .order("created_at", { ascending: false, nullsFirst: false })
        .order("updated_at", { ascending: false, nullsFirst: false })
        .limit(options?.limit || 50)
        .range(
          options?.offset || 0,
          (options?.offset || 0) + (options?.limit || 50) - 1
        );

      if (error) {
        console.error("Error fetching invoices:", error);
        return {
          data: [],
          error: error.message,
          success: false,
        };
      }

      if (!invoices || invoices.length === 0) {
        console.log("[InvoiceService] No invoices found");
        return {
          data: [],
          error: null,
          success: true,
        };
      }

      console.log(
        `[InvoiceService] Found ${invoices.length} invoices, fetching relations...`
      );

      // Extract unique customer and supplier IDs to batch fetch
      const customerIds = [
        ...new Set(
          invoices
            .map((invoice) => invoice.customer_id)
            .filter((id) => id !== null && id !== undefined)
        ),
      ];

      const supplierIds = [
        ...new Set(
          invoices
            .map((invoice) => (invoice as any).supplier_id)
            .filter((id) => id !== null && id !== undefined)
        ),
      ];

      // Extract invoice IDs to fetch related cargo and batch information
      const invoiceIds = invoices.map((invoice) => invoice.id);

      console.log(
        `[InvoiceService] Batching ${customerIds.length} customers, ${supplierIds.length} suppliers, and ${invoiceIds.length} invoices for batch lookup`
      );

      // Batch fetch customers, suppliers, and cargo/batch information to avoid N+1 queries
      const [customersResult, suppliersResult, cargoResult] = await Promise.all(
        [
          customerIds.length > 0
            ? this.supabase
                .from("customers")
                .select("id, name, email, phone, location")
                .in("id", customerIds)
            : Promise.resolve({ data: [], error: null }),
          supplierIds.length > 0
            ? this.supabase
                .from("suppliers" as any)
                .select(
                  "id, tracking_number, phone, location, cargo_tracking_number, batch_number"
                )
                .in("id", supplierIds)
            : Promise.resolve({ data: [], error: null }),
          // Fetch cargo items for these invoices to get batch information
          invoiceIds.length > 0
            ? this.supabase
                .from("cargos")
                .select("invoice_id, batch_id")
                .in("invoice_id", invoiceIds)
                .not("batch_id", "is", null)
            : Promise.resolve({ data: [], error: null }),
        ]
      );

      // Create lookup maps for O(1) access
      const customersMap = new Map();
      const suppliersMap = new Map();
      const invoiceToBatchMap = new Map();

      if (customersResult.data) {
        customersResult.data.forEach((customer) => {
          customersMap.set(customer.id, customer);
        });
      }

      if (suppliersResult.data) {
        suppliersResult.data.forEach((supplier: any) => {
          suppliersMap.set(supplier.id, supplier);
        });
      }

      // Process cargo data to create invoice-to-batch mapping
      if (cargoResult.data && cargoResult.data.length > 0) {
        // Get unique batch IDs from cargo data
        const batchIds = [
          ...new Set(
            cargoResult.data
              .map((cargo: any) => cargo.batch_id)
              .filter((id) => id !== null && id !== undefined)
          ),
        ];

        // Fetch batch information
        if (batchIds.length > 0) {
          const { data: batchData } = await this.supabase
            .from("batches")
            .select("id, code, name, freight_type")
            .in("id", batchIds);

          if (batchData) {
            const batchMap = new Map();
            batchData.forEach((batch: any) => {
              batchMap.set(batch.id, batch);
            });

            // Create invoice-to-batch mapping
            cargoResult.data.forEach((cargo: any) => {
              if (cargo.batch_id && batchMap.has(cargo.batch_id)) {
                invoiceToBatchMap.set(
                  cargo.invoice_id,
                  batchMap.get(cargo.batch_id)
                );
              }
            });
          }
        }
      }

      // Map invoices with their relations using lookup maps
      const invoicesWithRelations: InvoiceWithRelations[] = invoices.map(
        (invoice) => {
          const invoiceWithSupplier = invoice as any;

          return {
            ...invoice,
            customer: invoice.customer_id
              ? customersMap.get(invoice.customer_id) || null
              : null,
            supplier: invoiceWithSupplier.supplier_id
              ? suppliersMap.get(invoiceWithSupplier.supplier_id) || null
              : null,
            batch: invoiceToBatchMap.get(invoice.id) || null,
          } as unknown as InvoiceWithRelations;
        }
      );

      console.log(
        `[InvoiceService] Successfully processed ${invoicesWithRelations.length} invoices with relations`
      );

      return {
        data: invoicesWithRelations,
        error: null,
        success: true,
      };
    } catch (error: any) {
      console.error("Error in getAllInvoicesWithRelations:", error);
      return {
        data: [],
        error: error.message || "Failed to fetch invoices with relations",
        success: false,
      };
    }
  }

  /**
   * Get invoice by ID with customer relation
   */
  async getInvoiceWithCustomer(
    id: string
  ): Promise<ServiceResponse<InvoiceWithCustomer | null>> {
    try {
      const { data: invoice, error } = await this.supabase
        .from(this.tableName)
        .select(
          `
          *,
          customer:customer_id (
            id,
            name,
            email,
            phone,
            location
          ),
          batch:batch_id (
            id,
            code,
            name,
            freight_type
          )
        `
        )
        .eq("id", id)
        .not("status", "eq", "INACTIVE") // Filter out soft-deleted invoices
        .single();

      if (error) {
        return {
          success: false,
          data: null,
          error: error.message,
        };
      }

      return {
        success: true,
        data: invoice as unknown as InvoiceWithCustomer,
        error: null,
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Update invoice with follow-up actions for payment processing
   */
  async updateInvoice(
    id: string,
    data: InvoiceUpdate
  ): Promise<ServiceResponse<InvoiceData>> {
    try {
      // Get the current invoice to check for status changes
      const currentInvoice = await this.getInvoiceWithCustomer(id);
      if (!currentInvoice.success || !currentInvoice.data) {
        return {
          success: false,
          data: null,
          error: "Invoice not found",
        };
      }

      // Validate cargo batch assignment and active ledgers before marking invoice as PAID
      // This validation only applies to AUTOMATED invoices
      if (
        data.status === "PAID" &&
        currentInvoice.data.status !== "PAID" &&
        currentInvoice.data.type === "AUTOMATED"
      ) {
        const cargoService = await getCargoService();

        // Cast invoice to access supplier_id field
        const invoiceWithSupplier = currentInvoice.data as any;

        // Determine entity type and ID for validation
        let entityId: string | null = null;
        let entityType: "customer" | "supplier" | null = null;

        if (currentInvoice.data.customer_id) {
          entityId = currentInvoice.data.customer_id;
          entityType = "customer";
        } else if (invoiceWithSupplier.supplier_id) {
          entityId = invoiceWithSupplier.supplier_id;
          entityType = "supplier";
        }

        if (entityId && entityType) {
          const batchValidation =
            await cargoService.validateCargoInvoicePaymentEligibility(
              entityId,
              entityType
            );
          if (!batchValidation.isValid) {
            // Return success but with validation error in error field for toast notification
            // The UI should check for this specific error pattern and show toast
            return {
              success: true,
              data: currentInvoice.data,
              error: `VALIDATION_ERROR: ${batchValidation.error || "Cargo batch and ledger validation failed"}`,
            };
          }
        }
      }

      const result = await this.update(id, data);

      // Check if invoice status changed to PAID and trigger follow-up actions
      if (
        result.success &&
        result.data &&
        data.status === "PAID" &&
        currentInvoice.data.status !== "PAID"
      ) {
        try {
          await this.handleInvoicePaidFollowUp(result.data);
        } catch (followUpError) {
          console.error(
            "Failed to process invoice paid follow-up actions:",
            followUpError
          );
          // Note: UI layer should handle toast notifications
        }
      }

      return result;
    } catch (error) {
      return {
        success: false,
        data: null,
        error:
          error instanceof Error ? error.message : "Failed to update invoice",
      };
    }
  }

  /**
   * Handle follow-up actions when an invoice is marked as paid
   */
  private async handleInvoicePaidFollowUp(invoice: InvoiceData): Promise<void> {
    let processedCargos = 0;
    let createdTransactions = 0;
    let failedTransactions = 0;

    try {
      // Get invoice with customer/supplier data for transaction naming
      const invoiceWithRelations = await this.getInvoiceWithCustomer(
        invoice.id
      );
      let entityName = "Unknown Entity";

      if (invoiceWithRelations.success && invoiceWithRelations.data?.customer) {
        entityName = invoiceWithRelations.data.customer.name;
      } else {
        // Check if it's a supplier invoice and get supplier name
        const invoiceWithSupplier = invoice as any;
        if (invoiceWithSupplier.supplier_id) {
          try {
            const { data: supplierData } = await this.supabase
              .from("suppliers" as any)
              .select("tracking_number")
              .eq("id", invoiceWithSupplier.supplier_id)
              .single();

            if (supplierData && (supplierData as any).tracking_number) {
              entityName = `Supplier ${(supplierData as any).tracking_number}`;
            }
          } catch (error) {
            console.warn(
              `Failed to fetch supplier name for ${invoiceWithSupplier.supplier_id}:`,
              error
            );
          }
        }
      }

      // Find cargo associated with this invoice through customer_id or supplier_id
      const cargoService = await getCargoService();

      // Cast invoice to access supplier_id field
      const invoiceWithSupplier = invoice as any;

      // Determine entity type and get cargos accordingly
      let cargosResult;
      let entityType: "customer" | "supplier";
      let entityId: string;

      if (invoice.customer_id) {
        entityType = "customer";
        entityId = invoice.customer_id;
        cargosResult = await cargoService.getCargosByCustomer(entityId, {
          limit: 100, // Get all cargos for this customer
        });
      } else if (invoiceWithSupplier.supplier_id) {
        entityType = "supplier";
        entityId = invoiceWithSupplier.supplier_id;
        cargosResult = await cargoService.getCargosBySupplier(entityId, {
          limit: 100, // Get all cargos for this supplier
        });
      } else {
        console.warn(
          "No customer_id or supplier_id found in paid invoice:",
          invoice.id
        );
        return;
      }

      if (
        !cargosResult.success ||
        !cargosResult.data ||
        cargosResult.data.length === 0
      ) {
        console.warn(
          `No cargo found for ${entityType} in paid invoice:`,
          entityId
        );
        return;
      }

      const eligibleCargos = cargosResult.data.filter(
        (cargo: any) => cargo.batch_id
      );

      if (eligibleCargos.length === 0) {
        console.info("No batched cargo found for invoice:", invoice.id);
        return;
      }

      // Process each cargo that might be associated with this invoice
      for (const cargo of eligibleCargos) {
        processedCargos++;

        try {
          // Find the batch ledger
          const ledgerService = await getLedgerService();
          const ledgersResult = await ledgerService.getAllLedgers({
            filters: {
              associated_table: "batches",
              associated_id: cargo.batch_id,
            },
            limit: 1,
          });

          if (
            !ledgersResult.success ||
            !ledgersResult.data ||
            ledgersResult.data.length === 0
          ) {
            console.warn("No ledger found for batch:", cargo.batch_id);
            failedTransactions++;
            continue;
          }

          const batchLedger = ledgersResult.data[0];

          // Create transaction in the batch ledger
          const transactionService = await getTransactionService();
          const transactionData = {
            name: `Payment for Cargo ${cargo.tracking_number || cargo.id} - Invoice ${invoice.inv_number} - ${entityName}`,
            status: "COMPLETED" as any,
            tags: ["payment", "invoice", "cargo"],
            context: `Invoice ${invoice.inv_number} marked as paid for cargo ${cargo.tracking_number || cargo.id} by ${entityName}`,
            type: "DEBIT" as any, // Debit transaction for payment received
            amount: invoice.total || 0,
            ledger_id: batchLedger.id,
            account_id: cargo.account_id,
          };

          await transactionService.createTransaction(transactionData);
          createdTransactions++;

          console.log(
            `Created transaction in batch ledger for cargo ${cargo.tracking_number || cargo.id}`
          );
        } catch (cargoError) {
          console.error(
            `Failed to process cargo ${cargo.id} for paid invoice:`,
            cargoError
          );
          failedTransactions++;
          // Continue processing other cargos
        }
      }

      // Log final status based on results
      if (createdTransactions > 0 && failedTransactions === 0) {
        console.log(
          `Payment processing completed! Successfully created ${createdTransactions} transaction${createdTransactions === 1 ? "" : "s"} in batch ledgers.`
        );
      } else if (createdTransactions > 0 && failedTransactions > 0) {
        console.warn(
          `Payment processing partially completed. Created ${createdTransactions} transaction${createdTransactions === 1 ? "" : "s"}, but ${failedTransactions} failed.`
        );
      } else if (failedTransactions > 0) {
        console.error(
          `Payment processing failed. Failed to create transactions for ${failedTransactions} cargo item${failedTransactions === 1 ? "" : "s"}.`
        );
      }
    } catch (error) {
      console.error("Error in handleInvoicePaidFollowUp:", error);
      throw error;
    }
  }

  /**
   * Delete invoice
   */
  async deleteInvoice(id: string): Promise<ServiceResponse<boolean>> {
    try {
      const result = await this.delete(id);
      return result;
    } catch (error) {
      return {
        success: false,
        data: false,
        error:
          error instanceof Error ? error.message : "Failed to delete invoice",
      };
    }
  }

  /**
   * Search invoices by invoice number, notes, customer name, cargo tracking number, or batch number
   */
  async searchInvoices(
    searchTerm: string,
    params?: { limit?: number; offset?: number }
  ): Promise<ServiceResponse<InvoiceWithRelations[]>> {
    try {
      // First, get all invoices that match basic search criteria (excluding INACTIVE ones)
      const { data: invoices, error } = await this.supabase
        .from(this.tableName)
        .select("*")
        .not("status", "eq", "INACTIVE") // Filter out soft-deleted invoices
        .or(`inv_number.ilike.%${searchTerm}%,notes.ilike.%${searchTerm}%`)
        .order("created_at", { ascending: false, nullsFirst: false })
        .order("updated_at", { ascending: false, nullsFirst: false })
        .limit(params?.limit || 50)
        .range(
          params?.offset || 0,
          (params?.offset || 0) + (params?.limit || 50) - 1
        );

      if (error) {
        console.error("Error searching invoices:", error);
        return {
          data: [],
          error: error.message,
          success: false,
        };
      }

      if (!invoices || invoices.length === 0) {
        return {
          data: [],
          error: null,
          success: true,
        };
      }

      // Fetch related entities separately to handle nullable relationships and search in supplier fields
      const invoicesWithRelations: InvoiceWithRelations[] = await Promise.all(
        invoices.map(async (invoice) => {
          let customer = null;
          let supplier = null;
          let batch = null;

          // Cast invoice to include supplier_id for type safety
          const invoiceWithSupplier = invoice as any;

          // Fetch customer if customer_id exists
          if (invoice.customer_id) {
            try {
              const { data: customerData } = await this.supabase
                .from("customers")
                .select("id, name, email, phone, location")
                .eq("id", invoice.customer_id)
                .single();
              customer = customerData;
            } catch (error) {
              console.warn(
                `Failed to fetch customer ${invoice.customer_id}:`,
                error
              );
            }
          }

          // Fetch supplier if supplier_id exists
          if (invoiceWithSupplier.supplier_id) {
            try {
              const { data: supplierData } = await this.supabase
                .from("suppliers" as any)
                .select(
                  "id, tracking_number, phone, location, cargo_tracking_number, batch_number"
                )
                .eq("id", invoiceWithSupplier.supplier_id)
                .single();

              if (supplierData && (supplierData as any).id) {
                supplier = {
                  id: (supplierData as any).id,
                  tracking_number: (supplierData as any).tracking_number,
                  phone: (supplierData as any).phone,
                  location: (supplierData as any).location,
                  cargo_tracking_number: (supplierData as any)
                    .cargo_tracking_number,
                  batch_number: (supplierData as any).batch_number,
                };
              }
            } catch (error) {
              console.warn(
                `Failed to fetch supplier ${invoiceWithSupplier.supplier_id}:`,
                error
              );
            }
          }

          // Fetch batch information through cargo
          try {
            const { data: cargoData } = await this.supabase
              .from("cargos")
              .select("batch_id")
              .eq("invoice_id", invoice.id)
              .not("batch_id", "is", null)
              .limit(1);

            if (cargoData && cargoData.length > 0 && cargoData[0]?.batch_id) {
              const { data: batchData } = await this.supabase
                .from("batches")
                .select("id, code, name, freight_type")
                .eq("id", cargoData[0].batch_id)
                .single();

              if (batchData) {
                batch = batchData;
              }
            }
          } catch (error) {
            console.warn(
              `Failed to fetch batch for invoice ${invoice.id}:`,
              error
            );
          }

          return {
            ...invoice,
            customer,
            supplier,
            batch,
          } as unknown as InvoiceWithRelations;
        })
      );

      // Filter results to include matches in customer name, cargo tracking, batch number, or batch code
      const filteredResults = invoicesWithRelations.filter((invoice) => {
        const searchLower = searchTerm.toLowerCase();
        return (
          invoice.inv_number?.toLowerCase().includes(searchLower) ||
          invoice.notes?.toLowerCase().includes(searchLower) ||
          invoice.customer?.name?.toLowerCase().includes(searchLower) ||
          invoice.supplier?.cargo_tracking_number
            ?.toLowerCase()
            .includes(searchLower) ||
          invoice.supplier?.batch_number?.toLowerCase().includes(searchLower) ||
          invoice.batch?.code?.toLowerCase().includes(searchLower) ||
          invoice.batch?.name?.toLowerCase().includes(searchLower)
        );
      });

      return {
        data: filteredResults,
        error: null,
        success: true,
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        error:
          error instanceof Error ? error.message : "Failed to search invoices",
      };
    }
  }

  /**
   * Get invoice statistics with accurate aggregates for counts and amounts
   */
  async getInvoiceStats(): Promise<
    ServiceResponse<{
      totalInvoices: number;
      totalAmount: number;
      paidAmount: number;
      pendingAmount: number;
      overDueAmount: number;
      invoicesByStatus: { [key: string]: number };
    }>
  > {
    try {
      // Fetch invoices with necessary fields, excluding INACTIVE ones
      const { data: invoices, error } = await this.supabase
        .from(this.tableName)
        .select("status, total, subtotal, line_items, due_at, created_at")
        .not("status", "eq", "INACTIVE"); // Filter out soft-deleted invoices from stats

      if (error) {
        return {
          success: false,
          data: {
            totalInvoices: 0,
            totalAmount: 0,
            paidAmount: 0,
            pendingAmount: 0,
            overDueAmount: 0,
            invoicesByStatus: {},
          },
          error: error.message,
        };
      }

      // Initialize aggregation variables
      let invoicesByStatus: { [key: string]: number } = {};
      let totalAmount = 0;
      let paidAmount = 0;
      let pendingAmount = 0;
      let overDueAmount = 0;
      const currentDate = new Date();

      if (invoices && invoices.length > 0) {
        invoices.forEach((invoice: any) => {
          // Normalize status to lowercase for consistent comparison
          const status = (invoice.status?.toLowerCase() || "unknown").trim();

          // Count invoices by status
          invoicesByStatus[status] = (invoicesByStatus[status] || 0) + 1;

          // Calculate invoice total with fallback logic
          let invoiceTotal = 0;
          if (invoice.total !== null && invoice.total !== undefined) {
            invoiceTotal = Number(invoice.total) || 0;
          } else if (
            invoice.subtotal !== null &&
            invoice.subtotal !== undefined
          ) {
            invoiceTotal = Number(invoice.subtotal) || 0;
          } else if (invoice.line_items && Array.isArray(invoice.line_items)) {
            invoiceTotal = invoice.line_items.reduce(
              (sum: number, item: any) => {
                const itemTotal =
                  (item.factor_value || 0) * (item.unitPrice || 0);
                return sum + itemTotal;
              },
              0
            );
          }

          // Aggregate total amount
          totalAmount += invoiceTotal;

          // Categorize amounts by status
          if (status === "paid" || status === "completed") {
            paidAmount += invoiceTotal;
          } else if (status === "pending" || status === "draft") {
            // Check if this pending invoice is overdue
            if (invoice.due_at && new Date(invoice.due_at) < currentDate) {
              overDueAmount += invoiceTotal;
            } else {
              pendingAmount += invoiceTotal;
            }
          } else if (status === "overdue") {
            overDueAmount += invoiceTotal;
          } else {
            // For any other status, consider as pending if not explicitly overdue
            if (invoice.due_at && new Date(invoice.due_at) < currentDate) {
              overDueAmount += invoiceTotal;
            } else {
              pendingAmount += invoiceTotal;
            }
          }
        });
      }

      const stats = {
        totalInvoices: invoices?.length || 0,
        totalAmount,
        paidAmount,
        pendingAmount,
        overDueAmount,
        invoicesByStatus,
      };

      return {
        success: true,
        data: stats,
        error: null,
      };
    } catch (error) {
      return {
        success: false,
        data: {
          totalInvoices: 0,
          totalAmount: 0,
          paidAmount: 0,
          pendingAmount: 0,
          overDueAmount: 0,
          invoicesByStatus: {},
        },
        error:
          error instanceof Error
            ? error.message
            : "Failed to get invoice statistics",
      };
    }
  }

  /**
   * Get upcoming invoices (due soon)
   */
  async getUpcomingInvoices(
    days: number = 30
  ): Promise<ServiceResponse<InvoiceWithCustomer[]>> {
    try {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + days);

      const { data: invoices, error } = await this.supabase
        .from(this.tableName)
        .select(
          `
          *,
          customer:customer_id (
            id,
            name,
            email,
            phone,
            location
          )
        `
        )
        .not("status", "eq", "INACTIVE") // Filter out soft-deleted invoices
        .gte("due_at", new Date().toISOString())
        .lte("due_at", futureDate.toISOString())
        .order("due_at", { ascending: false });

      if (error) {
        return {
          success: false,
          data: [],
          error: error.message,
        };
      }

      return {
        success: true,
        data: (invoices || []) as unknown as InvoiceWithCustomer[],
        error: null,
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch upcoming invoices",
      };
    }
  }
}

// Create service instance
export const invoiceService = new InvoiceService();
