/**
 * Release Document Service
 *
 * Specialized service for generating and managing release authorization documents.
 * Integrates with the invoice template generator and document storage.
 *
 * ATTACHMENTS FEATURE:
 * - Automatically fetches all documents associated with a cargo
 * - Includes them as an "DOCUMENT ATTACHMENTS" section in the PDF
 * - Shows document name, category, description, upload date, and uploaded by
 * - Supports any document type uploaded to the cargo
 */

import { documentService, type DocumentGenerationData } from "./documents";
import { DEFAULT_COMPANY_CONFIG } from "@/lib/utils";
import {
  releaseAuthorizationService,
  type ShipmentForRelease,
} from "./release-authorizations";
import {
  generateReleaseAuthorizationInvoice,
  generateDocumentNumber,
  type ReleaseAuthorizationData,
  type InvoiceTemplateOptions,
  type PersonnelInfo,
  type CargoItem,
  type FreightInfo,
  type BatchInfo,
  type PaymentInfo,
  type QRCodeData,
  type DocumentMetadata,
  type CompanyInfo,
} from "../../invoice-templates";
import { convertCargoToReleaseData } from "../../release-document-generator";
import { cargoService } from "./cargos";
import { codeGeneratorService } from "./code-generator";
import type { ServiceResponse, Document, StatusEnum } from "../types";

// Release document specific types
export interface ReleaseDocumentGenerationOptions {
  cargo: ShipmentForRelease;
  personnelInfo: PersonnelInfo;
  templateOptions?: InvoiceTemplateOptions;
  additionalData?: {
    freight?: Partial<FreightInfo>;
    batch?: Partial<BatchInfo>;
    cargoItems?: CargoItem[];
    payment?: PaymentInfo;
    notes?: string;
    specialInstructions?: string;
  };
  autoUpdateStatus?: boolean;
}

export interface BatchReleaseDocumentOptions {
  batchData: {
    batchId: string;
    batchCode: string;
    freight: FreightInfo;
    cargoItems: CargoItem[];
    payment?: PaymentInfo;
  };
  personnelInfo: PersonnelInfo;
  templateOptions?: InvoiceTemplateOptions;
  notes?: string;
  specialInstructions?: string;
  autoUpdateStatus?: boolean;
}

export interface ReleaseDocumentResult {
  success: boolean;
  data?: {
    document: Document;
    pdfBlob: Blob;
    documentNumber: string;
    releaseCode: string;
    fileName: string;
    downloadUrl?: string;
  };
  error?: string;
}

export interface ReleaseDocumentStatistics {
  totalReleaseDocuments: number;
  pendingAuthorizations: number;
  completedAuthorizations: number;
  documentsThisMonth: number;
  averageProcessingTime: number; // in hours
  recentDocuments: Document[];
}

// Default company configuration
export class ReleaseDocumentService {
  /**
   * Generate release authorization document for cargo
   */
  async generateReleaseDocument(
    options: ReleaseDocumentGenerationOptions,
    accountId: string
  ): Promise<ReleaseDocumentResult> {
    try {
      const {
        cargo,
        personnelInfo,
        templateOptions,
        additionalData,
        autoUpdateStatus = true,
      } = options;

      // Generate document number and release code
      const documentNumber = generateDocumentNumber("REL");
      const releaseCode = this.generateReleaseCode(
        cargo.trackingNumber || "UNKNOWN"
      );

      // Fetch cargo with full relations for real data mapping
      const cargoResult = await cargoService.getCargoWithRelations(cargo.id);
      const cargoWithRelations = cargoResult.success ? cargoResult.data : null;

      // Fetch all documents associated with this cargo to include as attachments
      // This includes cargo documents, invoices, and any other related files
      const attachments = await documentService.getDocumentsByEntity(
        "cargos",
        cargo.id
      );

      // Convert cargo to release authorization data using real database data
      const releaseData = convertCargoToReleaseData(
        cargoWithRelations || cargo,
        personnelInfo,
        {
          ...additionalData,
          // Override with generated values
          freight: additionalData?.freight
            ? {
                ...additionalData.freight,
              }
            : undefined,
          batch: additionalData?.batch
            ? {
                ...additionalData.batch,
              }
            : undefined,
          payment: additionalData?.payment
            ? {
                ...additionalData.payment,
                status: additionalData.payment.status || "PAID",
                totalAmount: additionalData.payment.totalAmount || 0,
                paidAmount: additionalData.payment.paidAmount || 0,
                currency: additionalData.payment.currency || "USD",
              }
            : undefined,
          attachments: attachments,
        }
      );

      // Set the release code in QR data
      releaseData.qrCode.releaseCode = releaseCode;

      // Generate PDF document
      const pdfResult = await generateReleaseAuthorizationInvoice(
        releaseData,
        templateOptions
      );

      if (!pdfResult.success || !pdfResult.data) {
        return {
          success: false,
          error: pdfResult.error || "Failed to generate PDF document",
        };
      }

      // Store document in database and storage
      const documentData: DocumentGenerationData = {
        name: `Release Authorization - ${cargo.trackingNumber || cargo.id}`,
        category: "release-authorization",
        description: `Release authorization document for cargo ${cargo.trackingNumber || cargo.id}`,
        associatedTable: "handovers",
        associatedId: cargo.id,
        details: {
          documentNumber,
          releaseCode,
          cargoId: cargo.id,
          customerId: cargo.customer,
          cargoType: cargo.cargoType,
          authorizedBy: personnelInfo.authorizedBy,
          authorizedDate: new Date().toISOString(),
          qrCodeData: pdfResult.data.qrCodeData,
          attachments: attachments.success ? attachments.data : [],
          attachmentCount: attachments.success
            ? attachments.data?.length || 0
            : 0,
        },
        content: pdfResult.data.pdfBlob,
        contentType: "application/pdf",
      };

      const storeResult = await documentService.generateAndStoreDocument(
        documentData,
        accountId
      );

      if (!storeResult.success || !storeResult.data) {
        return {
          success: false,
          error: storeResult.error || "Failed to store document",
        };
      }

      // Update existing handover record with document generation status
      if (autoUpdateStatus) {
        await this.updateHandoverWithDocumentGeneration(
          cargo.id,
          releaseCode,
          personnelInfo
        );
      }

      // Get download URL
      const downloadUrlResult = await documentService.getDocumentDownloadUrl(
        storeResult.data.path
      );

      return {
        success: true,
        data: {
          document: storeResult.data,
          pdfBlob: pdfResult.data.pdfBlob,
          documentNumber,
          releaseCode,
          fileName: pdfResult.data.fileName,
          downloadUrl: downloadUrlResult.success
            ? downloadUrlResult.data
            : undefined,
        },
      };
    } catch (error: any) {
      console.error("Error generating release document:", error);
      return {
        success: false,
        error: error.message || "Failed to generate release document",
      };
    }
  }

  /**
   * Generate batch release authorization document
   */
  async generateBatchReleaseDocument(
    options: BatchReleaseDocumentOptions,
    accountId: string
  ): Promise<ReleaseDocumentResult> {
    try {
      const {
        batchData,
        personnelInfo,
        templateOptions,
        notes,
        specialInstructions,
        autoUpdateStatus = true,
      } = options;

      // Generate document number and release code
      const documentNumber = generateDocumentNumber("BATCH-REL");
      // Use first cargo's tracking number or batch ID for release code
      const cargoTrackingNumber =
        batchData.cargoItems?.[0]?.trackingNumber ||
        batchData.batchId ||
        "UNKNOWN";
      const releaseCode = this.generateReleaseCode(cargoTrackingNumber);

      // Create release authorization data for batch
      const releaseData = this.createBatchReleaseData(
        batchData,
        personnelInfo,
        documentNumber,
        releaseCode,
        notes,
        specialInstructions
      );

      // Generate PDF document
      const pdfResult = await generateReleaseAuthorizationInvoice(
        releaseData,
        templateOptions
      );

      if (!pdfResult.success || !pdfResult.data) {
        return {
          success: false,
          error: pdfResult.error || "Failed to generate batch PDF document",
        };
      }

      // Store document
      const documentData: DocumentGenerationData = {
        name: `Batch Release Authorization - ${batchData.batchCode}`,
        category: "batch-release-authorization",
        description: `Batch release authorization document for batch ${batchData.batchCode}`,
        associatedTable: "batches",
        associatedId: batchData.batchId,
        details: {
          documentNumber,
          releaseCode,
          batchId: batchData.batchId,
          batchCode: batchData.batchCode,
          cargoCount: batchData.cargoItems.length,
          authorizedBy: personnelInfo.authorizedBy,
          authorizedDate: new Date().toISOString(),
          qrCodeData: pdfResult.data.qrCodeData,
        },
        content: pdfResult.data.pdfBlob,
        contentType: "application/pdf",
      };

      const storeResult = await documentService.generateAndStoreDocument(
        documentData,
        accountId
      );

      if (!storeResult.success || !storeResult.data) {
        return {
          success: false,
          error: storeResult.error || "Failed to store batch document",
        };
      }

      // Get download URL
      const downloadUrlResult = await documentService.getDocumentDownloadUrl(
        storeResult.data.path
      );

      return {
        success: true,
        data: {
          document: storeResult.data,
          pdfBlob: pdfResult.data.pdfBlob,
          documentNumber,
          releaseCode,
          fileName: pdfResult.data.fileName,
          downloadUrl: downloadUrlResult.success
            ? downloadUrlResult.data
            : undefined,
        },
      };
    } catch (error: any) {
      console.error("Error generating batch release document:", error);
      return {
        success: false,
        error: error.message || "Failed to generate batch release document",
      };
    }
  }

  /**
   * Get release documents for cargo
   */
  async getReleaseDocumentsByCargo(
    cargoId: string
  ): Promise<ServiceResponse<Document[]>> {
    try {
      const result = await documentService.getDocumentsByEntity(
        "cargos",
        cargoId,
        {
          category: "release-authorization",
        }
      );

      return {
        success: result.success,
        data: result.data || [],
        error: result.error,
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: error.message || "Failed to get release documents",
      };
    }
  }

  /**
   * Get release documents for a shipment (backward compatibility)
   * @deprecated Use getReleaseDocumentsByCargo instead
   */
  async getReleaseDocumentsByShipment(
    shipmentId: string
  ): Promise<ServiceResponse<Document[]>> {
    return this.getReleaseDocumentsByCargo(shipmentId);
  }

  /**
   * Get release documents for a batch
   */
  async getReleaseDocumentsByBatch(
    batchId: string
  ): Promise<ServiceResponse<Document[]>> {
    try {
      const result = await documentService.getDocumentsByEntity(
        "batches",
        batchId,
        {
          category: "batch-release-authorization",
        }
      );

      return {
        success: result.success,
        data: result.data || [],
        error: result.error,
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: error.message || "Failed to get batch release documents",
      };
    }
  }

  /**
   * Verify QR code and complete authorization
   */
  async verifyQRCodeAndCompleteAuthorization(
    releaseCode: string,
    verifiedBy: string,
    location?: { latitude: number; longitude: number }
  ): Promise<ServiceResponse<boolean>> {
    try {
      // Find the document by release code
      const searchResult = await documentService.searchDocuments(releaseCode, {
        category: "release-authorization",
      });

      if (
        !searchResult.success ||
        !searchResult.data ||
        searchResult.data.length === 0
      ) {
        return {
          success: false,
          data: null,
          error: "Release authorization document not found",
        };
      }

      const document: any = searchResult.data[0];
      const details = document.details as any;

      if (!details?.cargoId && !details?.shipmentId) {
        return {
          success: false,
          data: null,
          error: "Invalid document details",
        };
      }

      // Complete the release authorization (support both cargoId and shipmentId for backward compatibility)
      const cargoId = details.cargoId || details.shipmentId;
      const completeResult =
        await releaseAuthorizationService.completeReleaseAuthorization(cargoId);

      if (!completeResult.success) {
        return {
          success: false,
          data: null,
          error: completeResult.error || "Failed to complete authorization",
        };
      }

      // Update document with verification details
      const updateResult = await documentService.update(document.id, {
        details: {
          ...details,
          verifiedBy,
          verifiedAt: new Date().toISOString(),
          verificationLocation: location,
          status: "COMPLETED",
        },
      });

      return {
        success: true,
        data: true,
        error: null,
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: error.message || "Failed to verify QR code",
      };
    }
  }

  /**
   * Get attachments for a cargo release document
   * This method demonstrates how attachments are fetched and included in release documents
   */
  async getCargoAttachmentsForRelease(
    cargoId: string
  ): Promise<ServiceResponse<any[]>> {
    try {
      const attachments = await documentService.getDocumentsByEntity(
        "cargos",
        cargoId,
        {
          category: "cargo-document",
        }
      );

      if (!attachments.success) {
        return {
          success: false,
          data: null,
          error: attachments.error || "Failed to fetch attachments",
        };
      }

      // Transform attachments to the format expected by the PDF generator
      const formattedAttachments = (attachments.data || []).map((doc) => ({
        id: doc.id,
        name: doc.name,
        path: doc.path,
        category: doc.category || "Document",
        description: doc.description,
        uploadedAt: doc.created_at,
        uploadedBy: doc.accounts?.users?.name || "System",
      }));

      return {
        success: true,
        data: formattedAttachments,
        error: null,
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: error.message || "Failed to get cargo attachments",
      };
    }
  }

  /**
   * Get release document statistics
   */
  async getReleaseDocumentStatistics(): Promise<
    ServiceResponse<ReleaseDocumentStatistics>
  > {
    try {
      // This would typically involve multiple queries to get comprehensive statistics
      // For now, returning a basic structure
      const stats: ReleaseDocumentStatistics = {
        totalReleaseDocuments: 0,
        pendingAuthorizations: 0,
        completedAuthorizations: 0,
        documentsThisMonth: 0,
        averageProcessingTime: 0,
        recentDocuments: [],
      };

      return {
        success: true,
        data: stats,
        error: null,
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: error.message || "Failed to get statistics",
      };
    }
  }

  /**
   * Convert cargo to release authorization data
   */
  private convertCargoToReleaseData(
    cargo: ShipmentForRelease,
    personnelInfo: PersonnelInfo,
    documentNumber: string,
    releaseCode: string,
    additionalData?: ReleaseDocumentGenerationOptions["additionalData"]
  ): ReleaseAuthorizationData {
    const now = new Date();

    const metadata: DocumentMetadata = {
      documentNumber,
      generatedDate: now.toISOString(),
      generatedTime: now.toLocaleTimeString(),
      location: "Main Warehouse",
      documentType: "RELEASE_AUTHORIZATION",
      version: "1.0",
      validUntil: new Date(
        now.getTime() + 30 * 24 * 60 * 60 * 1000
      ).toISOString(),
    };

    const freight: FreightInfo = {
      id: cargo.id,
      name: `Freight-${cargo.id.slice(-6)}`,
      type: "SEA",
      origin: cargo.origin || "Unknown Origin",
      destination: cargo.destination || "Unknown Destination",
      arrivalDate: cargo.arrivalDate,
      ...additionalData?.freight,
    };

    const batch: BatchInfo = {
      id: `batch-${cargo.id}`,
      code: `BATCH-${cargo.id.slice(-6)}`,
      freightId: freight.id,
      totalWeight: parseFloat(cargo.weight?.replace(/[^\d.]/g, "") || "0"),
      weightUnit: "kg",
      totalValue: 1000,
      currency: "USD",
      cargoCount: 1,
      customerCount: 1,
      ...additionalData?.batch,
    };

    const defaultCargoItem: CargoItem = {
      id: cargo.id,
      trackingNumber: cargo.trackingNumber || `TRK-${cargo.id.slice(-8)}`,
      description: cargo.cargoType || "General Cargo",
      category: cargo.cargoType || "General",
      quantity: 1,
      weight: parseFloat(cargo.weight?.replace(/[^\d.]/g, "") || "0"),
      weightUnit: "kg",
      value: 1000,
      currency: "USD",
      customerName: cargo.customer,
      customerCompany: cargo.customer,
    };

    const cargoItems = additionalData?.cargoItems || [defaultCargoItem];

    const payment: PaymentInfo = {
      status: cargo.paymentComplete ? "PAID" : "PENDING",
      totalAmount: batch.totalValue,
      paidAmount: cargo.paymentComplete ? batch.totalValue : 0,
      currency: "USD",
      ...additionalData?.payment,
    };

    const qrCode: QRCodeData = {
      id: cargo.id,
      releaseCode,
      cargoIds: cargoItems.map((item) => item.id),
      batchId: batch.id,
      authorizedBy: personnelInfo.authorizedBy,
      authorizedDate: now.toISOString(),
      expiryDate: metadata.validUntil,
      verificationUrl: `${window.location.origin}/release-authorization/${cargo.id}`,
    };

    return {
      metadata,
      company: DEFAULT_COMPANY_CONFIG,
      personnel: personnelInfo,
      freight,
      batch,
      cargoItems,
      payment,
      qrCode,
      notes: additionalData?.notes,
      specialInstructions: additionalData?.specialInstructions,
      requiresPhotoId: true,
      requiresSignature: true,
      requiresWitness: false,
    };
  }

  /**
   * Convert shipment to release authorization data (backward compatibility)
   * @deprecated Use convertCargoToReleaseData instead
   */
  private convertShipmentToReleaseData(
    shipment: ShipmentForRelease,
    personnelInfo: PersonnelInfo,
    documentNumber: string,
    releaseCode: string,
    additionalData?: ReleaseDocumentGenerationOptions["additionalData"]
  ): ReleaseAuthorizationData {
    return this.convertCargoToReleaseData(
      shipment,
      personnelInfo,
      documentNumber,
      releaseCode,
      additionalData
    );
  }

  /**
   * Create batch release authorization data
   */
  private createBatchReleaseData(
    batchData: BatchReleaseDocumentOptions["batchData"],
    personnelInfo: PersonnelInfo,
    documentNumber: string,
    releaseCode: string,
    notes?: string,
    specialInstructions?: string
  ): ReleaseAuthorizationData {
    const now = new Date();
    const totalWeight = batchData.cargoItems.reduce(
      (sum, item) => sum + item.weight,
      0
    );
    const totalValue = batchData.cargoItems.reduce(
      (sum, item) => sum + item.value,
      0
    );
    const customerCount = new Set(
      batchData.cargoItems.map((item) => item.customerName)
    ).size;

    return {
      metadata: {
        documentNumber,
        generatedDate: now.toISOString(),
        generatedTime: now.toLocaleTimeString(),
        location: "Main Warehouse",
        documentType: "RELEASE_AUTHORIZATION",
        version: "1.0",
        validUntil: new Date(
          now.getTime() + 30 * 24 * 60 * 60 * 1000
        ).toISOString(),
      },
      company: DEFAULT_COMPANY_CONFIG,
      personnel: personnelInfo,
      freight: batchData.freight,
      batch: {
        id: batchData.batchId,
        code: batchData.batchCode,
        freightId: batchData.freight.id,
        totalWeight,
        weightUnit: "kg",
        totalValue,
        currency: "USD",
        cargoCount: batchData.cargoItems.length,
        customerCount,
      },
      cargoItems: batchData.cargoItems,
      payment: batchData.payment || {
        status: "PAID",
        totalAmount: totalValue,
        paidAmount: totalValue,
        currency: "USD",
      },
      qrCode: {
        id: batchData.batchId,
        releaseCode,
        cargoIds: batchData.cargoItems.map((item) => item.id),
        batchId: batchData.batchId,
        authorizedBy: personnelInfo.authorizedBy,
        authorizedDate: now.toISOString(),
        expiryDate: new Date(
          now.getTime() + 30 * 24 * 60 * 60 * 1000
        ).toISOString(),
        verificationUrl: `${window.location.origin}/release-authorization/${batchData.batchId}`,
      },
      notes,
      specialInstructions,
      requiresPhotoId: true,
      requiresSignature: true,
      requiresWitness: false,
    };
  }

  /**
   * Generate a unique release code
   */
  private generateReleaseCode(cargoTrackingNumber: string): string {
    return codeGeneratorService.generateReleaseCode(cargoTrackingNumber);
  }

  /**
   * Update existing handover record with document generation status
   */
  private async updateHandoverWithDocumentGeneration(
    cargoId: string,
    releaseCode: string,
    personnelInfo: PersonnelInfo
  ): Promise<void> {
    try {
      // Get existing handover record for this cargo
      const existingAuth =
        await releaseAuthorizationService.getReleaseAuthorizationByCargoId(
          cargoId
        );

      if (!existingAuth.success || !existingAuth.data) {
        console.warn(
          "No existing handover record found for cargo:",
          cargoId,
          "- Document generated but handover not updated"
        );
        return;
      }

      // Get current verification_data from the handover
      const currentVerificationData =
        existingAuth.data.handover?.verification_data || {};

      // Update verification_data with document generation status
      const updatedVerificationData = {
        ...(typeof currentVerificationData === "object"
          ? currentVerificationData
          : {}),
        release_code: releaseCode,
        authorized_by: personnelInfo.authorizedBy,
        documents_generated: true, // ✅ Key field for conditional rendering
        document_generated_at: new Date().toISOString(),
        document_generated_by: personnelInfo.authorizedBy,
        verification_notes:
          "Release document generated - pending QR verification",
      };

      // Update the handover record in the database
      const updateResult =
        await releaseAuthorizationService.updateVerificationData(
          cargoId,
          updatedVerificationData
        );

      if (!updateResult.success) {
        throw new Error(`Failed to update handover: ${updateResult.error}`);
      }

      console.log(
        `✅ Handover record updated with documents_generated: true for cargo: ${cargoId}`
      );
    } catch (error) {
      console.warn(
        "Failed to update handover with document generation status:",
        error
      );
      // Don't fail the whole process if this fails
    }
  }
}

// Export singleton instance
export const releaseDocumentService = new ReleaseDocumentService();
