import { BaseService } from "../base/service";
import {
  Shipment,
  StatusEnum,
  ShipmentInsert,
  ShipmentUpdate,
  ServiceResponse,
  ShipmentWithRelations,
} from "../types";
import { codeGeneratorService } from "./code-generator";
import { notificationService } from "../system/notifications";

// Extended interfaces for shipment aggregates

export interface ShipmentAggregates {
  totalWeight: number;
  totalCBM: number;
  totalValue: number;
  cargoCount: number;
  batchCount: number;
  customerCount: number;
  freightCapacityUtilization: number;
}

export class ShipmentService extends BaseService<
  Shipment,
  ShipmentInsert,
  ShipmentUpdate
> {
  protected tableName = "shipments";

  // Generate unique tracking number based on freight type and batch info
  private generateTrackingNumber(
    freightType: string,
    batchCode?: string,
    freightCode?: string
  ): string {
    return codeGeneratorService.generateShipmentTrackingNumber({
      batchCode,
      freightCode: freightCode || `FRT-${freightType}`,
    });
  }

  // Generate sequential tracking number based on existing shipment tracking numbers
  private async generateSequentialTrackingNumber(
    freightType: string,
    batchCode?: string,
    freightCode?: string
  ): Promise<string> {
    try {
      const baseCode = batchCode || freightCode || `FRT-${freightType}`;

      // Fetch existing shipment tracking numbers for the same base code
      const { data: existingShipments, error } = await this.supabase
        .from("shipments")
        .select("tracking_number")
        .like("tracking_number", `SHP/${baseCode}/%`)
        .not("tracking_number", "is", null);

      if (error) {
        console.warn(
          "Failed to fetch existing shipment tracking numbers:",
          error
        );
        return this.generateTrackingNumber(freightType, batchCode, freightCode);
      }

      const existingTrackingNumbers = existingShipments
        .map((shipment) => shipment.tracking_number)
        .filter((trackingNumber): trackingNumber is string => !!trackingNumber);

      return codeGeneratorService.generateSequentialShipmentTrackingNumber(
        {
          batchCode,
          freightCode: freightCode || `FRT-${freightType}`,
        },
        existingTrackingNumbers
      );
    } catch (error) {
      console.warn(
        "Error generating sequential shipment tracking number:",
        error
      );
      return this.generateTrackingNumber(freightType, batchCode, freightCode);
    }
  }

  // Create shipment with auto-generated tracking number
  async createShipment(
    data: Omit<ShipmentInsert, "tracking_number">
  ): Promise<ServiceResponse<Shipment>> {
    try {
      // Get batch and freight info for tracking number generation
      let batchCode = "";
      let freightCode = "";

      if (data.batch_id) {
        const { data: batch } = await this.supabase
          .from("batches")
          .select("code, freights(code, name)")
          .eq("id", data.batch_id)
          .single();

        if (batch) {
          batchCode = batch.code || "";
          freightCode = (batch.freights as any)?.code || "";
        }
      }

      if (data.freight_id && !freightCode) {
        const { data: freight } = await this.supabase
          .from("freights")
          .select("name, scac_codes")
          .eq("id", data.freight_id)
          .single();

        if (freight) {
          freightCode = freight.scac_codes || freight.name || "";
        }
      }

      // Generate sequential tracking number
      const trackingNumber = await this.generateSequentialTrackingNumber(
        "SHIPMENT",
        batchCode,
        freightCode
      );

      // Create shipment with generated tracking number and default status
      const shipmentData: ShipmentInsert = {
        ...data,
        tracking_number: trackingNumber,
        status: data.status || "ACTIVE",
      };

      // Create the shipment
      const result = await this.create(shipmentData);

      // If shipment creation was successful, create notification
      if (result.success && result.data && data.account_id) {
        try {
          await notificationService.createTargetedNotification({
            account_id: data.account_id,
            name: "Shipping Schedule Created",
            message: `New shipping schedule created with tracking number ${trackingNumber}. Your cargo is now scheduled for shipment.`,
            associated_table: "shipments",
            associated_id: result.data.id,
            to: "*",
            details: {
              trackingNumber,
              batchId: data.batch_id,
              freightId: data.freight_id,
              billOfLading: data.bill_of_lading,
              freightReferenceId: data.freight_reference_id,
              timestamp: new Date().toISOString(),
            },
          });

          console.log(
            `✅ Shipment creation notification sent for tracking: ${trackingNumber}`
          );
        } catch (notificationError) {
          // Don't fail shipment creation if notification fails
          console.warn(
            "⚠️  Failed to create shipment creation notification:",
            notificationError
          );
        }
      }

      return result;
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to create shipment",
        success: false,
      };
    }
  }

  // Get shipment with all relations and aggregates
  async getShipmentWithRelations(
    id: string
  ): Promise<ServiceResponse<ShipmentWithRelations>> {
    try {
      const { data, error } = await this.supabase
        .from("shipments")
        .select(
          `
          *,
          accounts (
            id,
            email,
            users (
              id,
              name,
              phone
            )
          ),
          freights (
            id,
            name,
            scac_codes
          ),
          batches (
            id,
            name,
            code,
            type,
            weight,
            cbm_value,
            cbm_unit,
            status,
            freights (
              id,
              name,
              scac_codes
            ),
            cargos (
              id,
              tracking_number,
              particular,
              quantity,
              dimension_length,
              dimension_width,
              dimension_height,
              dimension_unit,
              weight_value,
              weight_unit,
              cbm_value,
              cbm_unit,
              total_price,
              customers (
                id,
                name,
                email,
                phone
              )
            )
          )
        `
        )
        .eq("id", id)
        .single();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: data as any as ShipmentWithRelations,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to fetch shipment with relations",
        success: false,
      };
    }
  }

  // Calculate shipment aggregates
  async calculateShipmentAggregates(
    shipmentId: string
  ): Promise<ServiceResponse<ShipmentAggregates>> {
    try {
      const { data: shipment, error } = await this.supabase
        .from("shipments")
        .select(
          `
          id,
          freights (
            id
          ),
          batches (
            id,
            weight,
            cbm_value,
            cargos (
              id,
              weight_value,
              weight_unit,
              cbm_value,
              cbm_unit,
              total_price,
              customers (
                id
              )
            )
          )
        `
        )
        .eq("id", shipmentId)
        .single();

      if (error || !shipment) {
        return {
          data: null,
          error: "Shipment not found",
          success: false,
        };
      }

      // Calculate aggregates
      let totalWeight = 0;
      let totalCBM = 0;
      let totalValue = 0;
      let cargoCount = 0;
      const uniqueCustomers = new Set();

      // Process batches and their cargos
      const batches = Array.isArray(shipment.batches)
        ? [shipment.batches]
        : shipment.batches
          ? [shipment.batches]
          : [];

      for (const batch of batches) {
        if (batch.cargos) {
          const cargos = Array.isArray(batch.cargos)
            ? batch.cargos
            : [batch.cargos];

          for (const cargo of cargos) {
            cargoCount++;
            totalWeight += cargo.weight_value || 0;
            totalCBM += cargo.cbm_value || 0;
            totalValue += cargo.total_price || 0;

            if (cargo.customers?.id) {
              uniqueCustomers.add(cargo.customers.id);
            }
          }
        }
      }

      // Calculate freight capacity utilization (simplified - no capacity field in new schema)
      const freightCapacityUtilization = 0;

      const aggregates: ShipmentAggregates = {
        totalWeight,
        totalCBM,
        totalValue,
        cargoCount,
        batchCount: batches.length,
        customerCount: uniqueCustomers.size,
        freightCapacityUtilization: Math.min(freightCapacityUtilization, 100),
      };

      return {
        data: aggregates,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to calculate shipment aggregates",
        success: false,
      };
    }
  }

  // Get basic shipment information (simplified - no tracking data)
  async getShipmentBasicInfo(shipmentId: string): Promise<
    ServiceResponse<{
      id: string;
      trackingNumber: string;
      status: StatusEnum;
      billOfLading?: string;
      freightReferenceId?: string;
      aggregates: ShipmentAggregates;
    }>
  > {
    try {
      const shipmentResult = await this.getShipmentWithRelations(shipmentId);
      if (!shipmentResult.success || !shipmentResult.data) {
        return {
          data: null,
          error: shipmentResult.error || "Shipment not found",
          success: false,
        };
      }

      const shipment = shipmentResult.data;
      const aggregatesResult =
        await this.calculateShipmentAggregates(shipmentId);

      const basicInfo = {
        id: shipment.id,
        trackingNumber: shipment.tracking_number || "",
        status: (shipment.status as StatusEnum) || "ACTIVE",
        billOfLading: shipment.bill_of_lading || undefined,
        freightReferenceId: shipment.freight_reference_id || undefined,
        aggregates: aggregatesResult.data || {
          totalWeight: 0,
          totalCBM: 0,
          totalValue: 0,
          cargoCount: 0,
          batchCount: 0,
          customerCount: 0,
          freightCapacityUtilization: 0,
        },
      };

      return {
        data: basicInfo,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to get shipment basic info",
        success: false,
      };
    }
  }

  // Update shipment status
  async updateShipmentStatus(
    shipmentId: string,
    status: StatusEnum
  ): Promise<ServiceResponse<Shipment>> {
    try {
      const updateData: ShipmentUpdate = {
        status,
        updated_at: new Date().toISOString(),
      };

      const result = await this.update(shipmentId, updateData);

      // Create notification for status update
      if (result.success && result.data && result.data.account_id) {
        try {
          const trackingNumber = result.data.tracking_number || shipmentId;
          await notificationService.createTargetedNotification({
            account_id: result.data.account_id,
            name: "Shipment Status Updated",
            message: `Your shipment ${trackingNumber} status has been updated to ${status}.`,
            associated_table: "shipments",
            associated_id: shipmentId,
            to: "*",
            details: {
              trackingNumber,
              newStatus: status,
              timestamp: new Date().toISOString(),
            },
          });

          console.log(
            `✅ Shipment status update notification sent for tracking: ${trackingNumber}`
          );
        } catch (notificationError) {
          console.warn(
            "⚠️  Failed to create shipment status update notification:",
            notificationError
          );
        }
      }

      return result;
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to update shipment status",
        success: false,
      };
    }
  }

  // Update shipment with full data
  async updateShipment(
    shipmentId: string,
    updateData: Partial<ShipmentInsert>
  ): Promise<ServiceResponse<Shipment>> {
    try {
      // Prepare the update data with timestamp
      const shipmentUpdate: ShipmentUpdate = {
        ...updateData,
        updated_at: new Date().toISOString(),
      };

      // Update the shipment
      const result = await this.update(shipmentId, shipmentUpdate);

      if (result.success && result.data) {
        // Create notification for shipment update
        try {
          await notificationService.createTargetedNotification({
            account_id: result.data.account_id,
            name: "Shipment Updated",
            message: `Shipment ${result.data.tracking_number || shipmentId} has been updated`,
            associated_table: "shipments",
            associated_id: result.data.id,
            to: "*",
            details: {
              shipmentId: result.data.id,
              trackingNumber: result.data.tracking_number,
              updatedFields: Object.keys(updateData),
              timestamp: new Date().toISOString(),
            },
          });
        } catch (notificationError) {
          console.warn(
            "Failed to create shipment update notification:",
            notificationError
          );
        }
      }

      return result;
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to update shipment",
        success: false,
      };
    }
  }

  // Get shipments by tracking number
  async getShipmentByTrackingNumber(
    trackingNumber: string
  ): Promise<ServiceResponse<ShipmentWithRelations>> {
    try {
      const { data, error } = await this.supabase
        .from("shipments")
        .select(
          `
          *,
          accounts (
            id,
            email,
            users (
              id,
              name,
              phone,
              location
            )
          ),
          freights (
            id,
            name,
            scac_codes
          ),
          batches (
            id,
            name,
            code,
            type,
            weight,
            cbm_value,
            cbm_unit,
            status
          )
        `
        )
        .eq("tracking_number", trackingNumber)
        .single();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: data as any as ShipmentWithRelations,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to fetch shipment by tracking number",
        success: false,
      };
    }
  }
}

export const shipmentService = new ShipmentService();
