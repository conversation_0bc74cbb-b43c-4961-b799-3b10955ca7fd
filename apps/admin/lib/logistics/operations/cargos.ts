import { BaseService } from "../base/service";
import {
  Cargo,
  CargoInsert,
  CargoUpdate,
  CargoWithRelations,
  CargoWithDocuments,
  CargoTrackingInfo,
  ServiceResponse,
  ServiceListResponse,
  QueryParams,
  StatusEnum,
} from "../types";
import { documentService } from "./documents";
import { notificationService } from "../system/notifications";
import { invoiceService } from "./invoices";
import { codeGeneratorService } from "./code-generator";

// Import release authorization service for automatic generation
let releaseAuthorizationService: any;

// Lazy import to avoid circular dependencies
async function getReleaseAuthorizationService() {
  if (!releaseAuthorizationService) {
    const { releaseAuthorizationService: service } = await import(
      "./release-authorizations"
    );
    releaseAuthorizationService = service;
  }
  return releaseAuthorizationService;
}

export class CargoService extends BaseService<Cargo, CargoInsert, CargoUpdate> {
  protected tableName = "cargos";

  /**
   * Validate cargo batch assignment and active ledger for invoice payment processing
   * This constraint prevents logging unlinked transactions when invoice status changes to PAID
   * Supports both customer and supplier invoices
   */
  async validateCargoInvoicePaymentEligibility(
    entityId: string,
    entityType: "customer" | "supplier"
  ): Promise<{ isValid: boolean; error: string | null; cargoDetails?: any[] }> {
    try {
      if (!entityId) {
        return {
          isValid: false,
          error: `No ${entityType} associated with this invoice. Please add a ${entityType} before marking as paid.`,
        };
      }

      // Find cargo associated with this entity (customer or supplier)
      const cargosResult =
        entityType === "customer"
          ? await this.getCargosByCustomer(entityId, { limit: 100 })
          : await this.getCargosBySupplier(entityId, { limit: 100 });

      if (
        !cargosResult.success ||
        !cargosResult.data ||
        cargosResult.data.length === 0
      ) {
        return {
          isValid: false,
          error: `No cargo found for this ${entityType}. Please create cargo and assign it to a batch first.`,
        };
      }

      // Check if any cargo is assigned to a batch
      const batchedCargos = cargosResult.data.filter(
        (cargo: any) => cargo.batch_id
      );

      if (batchedCargos.length === 0) {
        const cargoTrackingNumbers = cargosResult.data
          .map((cargo: any) => cargo.tracking_number || cargo.id)
          .join(", ");

        return {
          isValid: false,
          error: `Cargo items (${cargoTrackingNumbers}) must be assigned to a batch before marking invoice as paid.`,
        };
      }

      // Validate that batches have active ledgers
      const batchIds = [
        ...new Set(batchedCargos.map((cargo: any) => cargo.batch_id)),
      ];
      const ledgerValidationResults = await this.validateBatchLedgers(batchIds);

      if (!ledgerValidationResults.isValid) {
        return {
          isValid: false,
          error: ledgerValidationResults.error,
          cargoDetails: batchedCargos.map((cargo: any) => ({
            id: cargo.id,
            tracking_number: cargo.tracking_number,
            batch_id: cargo.batch_id,
          })),
        };
      }

      return {
        isValid: true,
        error: null,
        cargoDetails: batchedCargos.map((cargo: any) => ({
          id: cargo.id,
          tracking_number: cargo.tracking_number,
          batch_id: cargo.batch_id,
        })),
      };
    } catch (error) {
      return {
        isValid: false,
        error: `Unable to validate cargo batch assignment and ledger status. Please try again.`,
      };
    }
  }

  /**
   * Validate that all batches have active ledgers for financial transaction logging
   */
  private async validateBatchLedgers(
    batchIds: string[]
  ): Promise<{ isValid: boolean; error: string | null }> {
    try {
      // Import ledger service dynamically to avoid circular dependencies
      const { ledgerService } = await import("./ledgers");

      const batchesWithoutLedgers: string[] = [];

      for (const batchId of batchIds) {
        // Check if batch has an active ledger using getAllLedgers with filters
        const ledgersResult = await ledgerService.getAllLedgers({
          filters: {
            associated_table: "batches",
            associated_id: batchId,
            status: "ACTIVE",
          },
          limit: 1,
        });

        if (
          !ledgersResult.success ||
          !ledgersResult.data ||
          ledgersResult.data.length === 0
        ) {
          batchesWithoutLedgers.push(batchId);
        }
      }

      if (batchesWithoutLedgers.length > 0) {
        return {
          isValid: false,
          error: `Batches (${batchesWithoutLedgers.join(", ")}) do not have active ledgers. Financial transactions cannot be logged without active batch ledgers. Please ensure all batches have active ledgers before marking invoice as paid.`,
        };
      }

      return { isValid: true, error: null };
    } catch (error) {
      return {
        isValid: false,
        error: "Unable to validate batch ledger status. Please try again.",
      };
    }
  }

  // Generate unique tracking number (fallback method)
  private generateTrackingNumber(batchCode?: string): string {
    return codeGeneratorService.generateCargoTrackingNumber({
      category: "shamwaa",
      batchCode: batchCode || "UNKNOWN",
    });
  }

  // Generate sequential tracking number based on existing cargo tracking numbers
  private async generateSequentialTrackingNumber(
    batchCode?: string
  ): Promise<string> {
    try {
      if (!batchCode) {
        return this.generateTrackingNumber(batchCode);
      }

      // Fetch existing cargo tracking numbers for the same batch
      const { data: existingCargos, error } = await this.supabase
        .from("cargos")
        .select("tracking_number")
        .like("tracking_number", `CG/${batchCode}/%`)
        .not("tracking_number", "is", null);

      if (error) {
        console.warn("Failed to fetch existing cargo tracking numbers:", error);
        return this.generateTrackingNumber(batchCode);
      }

      const existingTrackingNumbers = existingCargos
        .map((cargo) => cargo.tracking_number)
        .filter((trackingNumber): trackingNumber is string => !!trackingNumber);

      return codeGeneratorService.generateSequentialCargoTrackingNumber(
        {
          category: "shamwaa",
          batchCode,
        },
        existingTrackingNumbers
      );
    } catch (error) {
      console.warn("Error generating sequential tracking number:", error);
      return this.generateTrackingNumber(batchCode);
    }
  }

  // Handle invoice creation or update using BaseService pattern
  async handleInvoiceUpsert(
    data: Omit<CargoInsert, "tracking_number"> & {
      tracking_number?: string;
      invoice_id?: string;
    },
    customer_id: string,
    supplier_id: string,
    cargo_id?: string
  ): Promise<void> {
    const originalTableName = "cargos";

    try {
      // Calculate pricing
      let total_price: number = data?.total_price || 0;

      // Determine which entity to use (customer or supplier)
      const isCustomer = !!customer_id;
      const isSupplier = !!supplier_id;
      const entityId = isCustomer ? customer_id : supplier_id;
      const entityType = isCustomer ? "customers" : "suppliers";

      // Get entity information (customer or supplier)
      this.tableName = entityType;
      let entity = await this.getById(entityId);

      // Create new line item for this cargo with comprehensive data
      const entityLabel = isCustomer ? "Customer" : "Supplier";
      const newLineItem = {
        // Core cargo identification
        cargo_id: cargo_id || data.tracking_number, // Use cargo ID if provided, fallback to tracking number
        tracking_number: data.tracking_number || "",
        china_tracking_number: data.china_tracking_number || "",
        // Description with cargo details
        description: data.particular,
        // Physical properties
        weight_value: Number(data.weight_value || 0),
        weight_unit: data.weight_unit || "KILOGRAMS",
        cbm_value: Number(data.cbm_value || 0),
        cbm_unit: data.cbm_unit || "METER_CUBIC",
        ctn: Number(data.ctn || 0),
        // Pricing and quantity
        quantity: Number(data.quantity || 1),
        factor_unit: data.factor_unit || "Quantity", // Default factor unit for cargo
        factor_value: Number(data.factor_value || 1), // Default factor value
        unitPrice: Number(data.unit_price || 0),
        total: Number(total_price || 0),
      };

      if (data.invoice_id) {
        // Update existing invoice by adding new line item
        this.tableName = "invoices";
        const existingInvoiceResult = await this.getById(data.invoice_id);

        if (existingInvoiceResult.success && existingInvoiceResult.data) {
          const existingInvoice = existingInvoiceResult.data as any;
          const currentLineItems = existingInvoice.line_items || [];
          const updatedLineItems = [...currentLineItems, newLineItem];

          // Recalculate totals using the revised formula
          const newTotal = updatedLineItems.reduce((sum: number, item: any) => {
            return (
              sum +
              (parseFloat(item.factor_value) || 1) *
                (parseFloat(item.unitPrice) || 0) *
                (parseFloat(item.quantity) || 1)
            );
          }, 0);

          // Update the invoice using direct Supabase update to avoid type conflicts
          const { error: updateError } = await this.supabase
            .from("invoices")
            .update({
              line_items: updatedLineItems,
              total: newTotal,
              updated_at: new Date().toISOString(),
            })
            .eq("id", data.invoice_id);

          if (updateError) {
            console.error("Error updating invoice:", updateError);
            throw new Error(`Failed to update invoice: ${updateError.message}`);
          }
        }
      } else {
        // Create new invoice using BaseService pattern
        let now = new Date();
        let due_at = new Date(now.setDate(now.getDay() + 30)).toLocaleString();

        const invoiceData = {
          due_at: due_at,
          type: "AUTOMATED",
          inv_number: `INV-${Math.floor(Math.random() * 10000)}`,
          terms_and_conditions:
            "Payment is due within 30 days of receipt of this invoice",
          notes: "No extra information",
          status: "PENDING" as const,
          line_items: [newLineItem],
          customer_id: isCustomer ? customer_id : null,
          supplier_id: isSupplier ? supplier_id : null,
          billing_address:
            (entity?.data as any)?.location ||
            (entity?.data as any)?.phone ||
            "",
          total: total_price,
        };

        // Create new invoice using direct Supabase insert to avoid type conflicts
        const { error: createError } = await this.supabase
          .from("invoices")
          .insert(invoiceData);

        if (createError) {
          console.error("Error creating invoice:", createError);
          throw new Error(`Failed to create invoice: ${createError.message}`);
        }
      }
    } finally {
      // Always restore original table name
      this.tableName = originalTableName;
    }
  }

  // Helper method to append document URLs to cargo data (for release authorization)
  private async appendDocumentUrls(cargo: any): Promise<CargoWithDocuments> {
    try {
      // Get documents associated with this cargo
      const documentsResult = await documentService.getDocumentsByEntity(
        "cargos",
        cargo.id,
        { category: "release-authorization" }
      );

      if (documentsResult.success && documentsResult.data) {
        // Get download URLs for each document
        const documentsWithUrls = await Promise.all(
          documentsResult.data.map(async (doc) => {
            const urlResult = await documentService.getDocumentDownloadUrl(
              doc.path
            );
            return {
              ...doc,
              downloadUrl: urlResult.success ? urlResult.data : null,
            };
          })
        );

        return {
          ...cargo,
          documents: documentsWithUrls,
          documentUrls: documentsWithUrls
            .map((doc) => doc.downloadUrl)
            .filter(Boolean),
        };
      }

      return {
        ...cargo,
        documents: [],
        documentUrls: [],
      };
    } catch (error) {
      console.error(
        `Error appending document URLs for cargo ${cargo.id}:`,
        error
      );
      return {
        ...cargo,
        documents: [],
        documentUrls: [],
      };
    }
  }

  // Get cargo with all relationships (customers and suppliers)
  // Uses soft recursion to handle nullable relationships
  async getCargoWithRelations(
    id: string
  ): Promise<ServiceResponse<CargoWithRelations>> {
    try {
      // First, get cargo with basic relations (excluding suppliers for now)
      const { data, error } = await this.supabase
        .from("cargos")
        .select(
          `
          *,
          customers (
            id,
            name,
            email,
            phone,
            location
          ),
          batches (
            id,
            name,
            code,
            status
          ),
          accounts (
            id,
            email,
            users (
              id,
              name
            )
          ),
          users:assigned_to (
            id,
            name
          )
        `
        )
        .eq("id", id)
        .single();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      if (!data) {
        return {
          data: null,
          error: "Cargo not found",
          success: false,
        };
      }

      // Soft recursion: Fetch supplier relation separately if supplier_id exists
      let supplier = null;
      const cargoWithSupplier = data as any;

      if (cargoWithSupplier.supplier_id) {
        try {
          const { data: supplierData } = await this.supabase
            .from("suppliers" as any)
            .select("id, tracking_number, phone, location")
            .eq("id", cargoWithSupplier.supplier_id)
            .single();

          if (supplierData && (supplierData as any).id) {
            supplier = {
              id: (supplierData as any).id,
              tracking_number: (supplierData as any).tracking_number,
              phone: (supplierData as any).phone,
              location: (supplierData as any).location,
            };
          }
        } catch (error) {
          console.warn(
            `Failed to fetch supplier ${cargoWithSupplier.supplier_id}:`,
            error
          );
        }
      }

      const cargoWithRelations = {
        ...data,
        suppliers: supplier,
      };

      return {
        data: cargoWithRelations as any,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to get cargo with relations",
        success: false,
      };
    }
  }

  // Get all cargos with relationships (customers and suppliers)
  // Uses soft recursion to handle nullable relationships
  async getAllCargosWithRelations(
    params?: QueryParams
  ): Promise<ServiceListResponse<CargoWithRelations>> {
    try {
      // First, get all cargos with basic relations (excluding suppliers for now)
      let query = this.supabase
        .from("cargos")
        .select(
          `
          *,
          customers (
            id,
            name,
            email,
            phone
          ),
          batches (
            id,
            name,
            code,
            status
          ),
          suppliers (
            id,
            tracking_number,
            phone,
            location
          ),
          invoices (
            id,
            inv_number,
            due_at,
            status
          ),
          accounts (
            id,
            email,
            users (
              id,
              name
            )
          ),
          users:assigned_to (
            id,
            name
          )
        `,
          { count: "exact" }
        )
        .not("status", "eq", "INACTIVE");

      // Apply filters
      if (params?.filters) {
        query = this.applyFilters(query, params.filters);
      }

      // Apply sorting
      if (params?.column) {
        query = query.order(params.column, {
          ascending: params.ascending ?? false,
        });
      } else {
        query = query
          .order("created_at", { ascending: false, nullsFirst: false })
          .order("updated_at", { ascending: false, nullsFirst: false });
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit);
        if (params.offset) {
          query = query.range(params.offset, params.offset + params.limit - 1);
        } else if (params.page && params.page > 1) {
          const offset = (params.page - 1) * params.limit;
          query = query.range(offset, offset + params.limit - 1);
        }
      }

      const { data, error, count } = await query;

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false,
        };
      }

      if (!data || data.length === 0) {
        return {
          data: [],
          error: null,
          success: true,
          count: count || 0,
        };
      }

      return {
        data: data as any,
        error: null,
        success: true,
        count: count || 0,
      };
    } catch (error: any) {
      return {
        data: [],
        error: error.message || "Failed to get cargos with relations",
        success: false,
      };
    }
  }

  // Create cargo with auto-generated tracking number (invoice creation removed)
  async createCargo(data: CargoInsert): Promise<ServiceResponse<Cargo>> {
    try {
      // Extract invoice_id from data before creating cargo record (not used for auto-creation)

      // Generate tracking number with sequential index if not provided
      let trackingNumber = data.tracking_number;
      if (!trackingNumber) {
        // Get batch code if batch_id is provided
        let batchCode: string | undefined;
        if (data.batch_id) {
          const { data: batch } = await this.supabase
            .from("batches")
            .select("code")
            .eq("id", data.batch_id)
            .single();
          batchCode = batch?.code;
        }
        trackingNumber = await this.generateSequentialTrackingNumber(batchCode);
      }

      const cargoData: CargoInsert = {
        ...data,
        tracking_number: trackingNumber,
        status: "CREATED",
      };

      const createResult = await this.create(cargoData);

      if (!createResult.success || !createResult.data) {
        return {
          success: false,
          error: createResult.error || "Failed to create cargo",
          data: null,
        };
      }

      const createdCargo = createResult.data;
      let customer_id: string = data.customer_id || "";
      let supplier_id: string = data.supplier_id || "";

      // Ensure either customer_id OR supplier_id is provided (not both required)
      if (!customer_id && !supplier_id) {
        return {
          success: false,
          error: "Either Customer or Supplier must be referenced",
          data: null,
        };
      }

      // Auto invoice creation removed - invoices must be created manually
      // The handleInvoiceUpsert call has been removed

      return {
        success: true,
        error: null,
        data: createdCargo,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to create cargo",
        success: false,
      };
    } finally {
      this.tableName = "cargos";
    }
  }

  // Check if tracking number exists
  async isTrackingNumberExists(
    trackingNumber: string,
    excludeId?: string
  ): Promise<ServiceResponse<boolean>> {
    try {
      let query = this.supabase
        .from("cargos")
        .select("id")
        .eq("tracking_number", trackingNumber);

      if (excludeId) {
        query = query.neq("id", excludeId);
      }

      const { data, error } = await query;

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: (data || []).length > 0,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to check tracking number",
        success: false,
      };
    }
  }

  // Get cargo by tracking number
  async getCargoByTrackingNumber(
    trackingNumber: string
  ): Promise<ServiceResponse<CargoWithRelations>> {
    try {
      const { data, error } = await this.supabase
        .from("cargos")
        .select(
          `
          *,
          customers (
            id,
            name,
            email,
            phone,
            location
          ),
          batches (
            id,
            name,
            code,
            status
          ),
          accounts (
            id,
            email,
            users (
              id,
              name
            )
          ),
          assigned_user:users!assigned_to (
            id,
            name,
            email
          )
        `
        )
        .eq("tracking_number", trackingNumber)
        .single();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: data as any,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to get cargo by tracking number",
        success: false,
      };
    }
  }

  // Get cargos by customer
  async getCargosByCustomer(
    customerId: string,
    params?: QueryParams
  ): Promise<ServiceListResponse<CargoWithRelations>> {
    const filters = { ...params?.filters, customer_id: customerId };
    return this.getAllCargosWithRelations({ ...params, filters });
  }

  // Get cargos by supplier
  async getCargosBySupplier(
    supplierId: string,
    params?: QueryParams
  ): Promise<ServiceListResponse<CargoWithRelations>> {
    const filters = { ...params?.filters, supplier_id: supplierId };
    return this.getAllCargosWithRelations({ ...params, filters });
  }

  // Get cargos by batch
  async getCargosByBatch(
    batchId: string,
    params?: QueryParams
  ): Promise<ServiceListResponse<CargoWithRelations>> {
    const filters = { ...params?.filters, batch_id: batchId };
    return this.getAllCargosWithRelations({ ...params, filters });
  }

  // Get cargos by status
  async getCargosByStatus(
    status: StatusEnum,
    params?: QueryParams
  ): Promise<ServiceListResponse<CargoWithRelations>> {
    const filters = { ...params?.filters, status };
    return this.getAllCargosWithRelations({ ...params, filters });
  }

  // Search cargos by tracking number, customer name, supplier info, or particulars
  // Uses soft recursion to handle both customer and supplier relationships
  async searchCargos(
    searchTerm: string,
    params?: QueryParams
  ): Promise<ServiceListResponse<CargoWithRelations>> {
    try {
      // First, search in basic cargo fields and customer names
      let query = this.supabase
        .from("cargos")
        .select(
          `
          *,
          customers (
            id,
            name,
            email,
            phone
          ),
          batches (
            id,
            name,
            code,
            status
          ),
          accounts (
            id,
            email,
            users (
              id,
              name
            )
          ),
          assigned_user:users!assigned_to (
            id,
            name,
            email
          )
        `,
          { count: "exact" }
        )
        .or(
          `tracking_number.ilike.%${searchTerm}%,particular.ilike.%${searchTerm}%,customers.name.ilike.%${searchTerm}%`
        );

      // Apply additional filters
      if (params?.filters) {
        query = this.applyFilters(query, params.filters);
      }

      // Apply sorting
      if (params?.column) {
        query = query.order(params.column, {
          ascending: params.ascending ?? false,
        });
      } else {
        query = query
          .order("created_at", { ascending: false, nullsFirst: false })
          .order("updated_at", { ascending: false, nullsFirst: false });
      }

      // Apply pagination
      if (params?.limit) {
        query = query.limit(params.limit);
        if (params.offset) {
          query = query.range(params.offset, params.offset + params.limit - 1);
        } else if (params.page && params.page > 1) {
          const offset = (params.page - 1) * params.limit;
          query = query.range(offset, offset + params.limit - 1);
        }
      }

      const { data, error, count } = await query;

      if (error) {
        return {
          data: [],
          error: error.message,
          success: false,
        };
      }

      if (!data || data.length === 0) {
        return {
          data: [],
          error: null,
          success: true,
          count: count || 0,
        };
      }

      // Soft recursion: Fetch supplier relations and filter by supplier fields
      const cargosWithSuppliers = await Promise.all(
        data.map(async (cargo: any) => {
          let supplier = null;

          // Fetch supplier if supplier_id exists
          if (cargo.supplier_id) {
            try {
              const { data: supplierData } = await this.supabase
                .from("suppliers" as any)
                .select("id, tracking_number, phone, location")
                .eq("id", cargo.supplier_id)
                .single();

              if (supplierData && (supplierData as any).id) {
                supplier = {
                  id: (supplierData as any).id,
                  tracking_number: (supplierData as any).tracking_number,
                  phone: (supplierData as any).phone,
                  location: (supplierData as any).location,
                };
              }
            } catch (error) {
              console.warn(
                `Failed to fetch supplier ${cargo.supplier_id}:`,
                error
              );
            }
          }

          return {
            ...cargo,
            suppliers: supplier,
          };
        })
      );

      // Additional filtering for supplier fields (client-side)
      const filteredCargos = cargosWithSuppliers.filter((cargo) => {
        // If already matched by the database query, include it
        const matchesBasicFields =
          cargo.tracking_number
            ?.toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
          cargo.particular?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          cargo.customers?.name
            ?.toLowerCase()
            .includes(searchTerm.toLowerCase());

        // Check supplier fields
        const matchesSupplierFields =
          cargo.suppliers &&
          (cargo.suppliers.tracking_number
            ?.toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
            cargo.suppliers.phone
              ?.toLowerCase()
              .includes(searchTerm.toLowerCase()) ||
            cargo.suppliers.location
              ?.toLowerCase()
              .includes(searchTerm.toLowerCase()));

        return matchesBasicFields || matchesSupplierFields;
      });

      return {
        data: filteredCargos as any,
        error: null,
        success: true,
        count: filteredCargos.length,
      };
    } catch (error: any) {
      return {
        data: [],
        error: error.message || "Failed to search cargos",
        success: false,
      };
    }
  }

  // Get cargo tracking information
  async getCargoTrackingInfo(
    trackingNumber: string
  ): Promise<ServiceResponse<CargoTrackingInfo>> {
    try {
      const { data, error } = await this.supabase
        .from("cargos")
        .select(
          `
          tracking_number,
          status,
          updated_at,
          batches (
            shipments (
              coordinates,
              estimated_time_of_arrival,
              updated_at
            )
          )
        `
        )
        .eq("tracking_number", trackingNumber)
        .single();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      // Get latest shipment info
      const shipments = (data as any).batches?.shipments || [];
      const latestShipment = shipments.sort(
        (a: any, b: any) =>
          new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
      )[0];

      const trackingInfo: CargoTrackingInfo = {
        trackingNumber: data.tracking_number,
        status: data.status || "CREATED",
        currentLocation: latestShipment?.coordinates || undefined,
        estimatedDelivery:
          latestShipment?.estimated_time_of_arrival || undefined,
        lastUpdate: data.updated_at || new Date().toISOString(),
      };

      return {
        data: trackingInfo,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to get cargo tracking info",
        success: false,
      };
    }
  }

  // Update cargo status with tracking and automatic release authorization generation
  async updateCargoStatus(
    id: string,
    status: string,
    notes?: string,
    authorizedBy?: string
  ): Promise<ServiceResponse<Cargo>> {
    try {
      // Get current cargo data before update
      const currentCargoResult = await this.getById(id);
      if (!currentCargoResult.success || !currentCargoResult.data) {
        return {
          data: null,
          error: "Cargo not found",
          success: false,
        };
      }

      const currentCargo = currentCargoResult.data;
      const previousStatus = currentCargo.status;

      // Update cargo status
      const result = await this.updateStatus(id, status as any);

      if (!result.success) {
        return result;
      }

      // Create notification for cargo status change if status actually changed
      if (previousStatus !== status && currentCargo.account_id) {
        try {
          const statusMessages: Record<string, string> = {
            CREATED: "has been registered and is being processed",
            PROCESSING: "is being prepared for shipment",
            IN_TRANSIT: "is now in transit to its destination",
            READY_FOR_PICKUP: "is ready for pickup",
            ARRIVED: "has arrived at the destination",
            CLEARED: "has been cleared for pickup",
            UNLOADED: "has been unloaded from the vehicle",
            LOADED: "has been loaded into the vehicle",
            DELIVERED: "has been delivered and is ready for pickup",
            PICKED_UP: "has been successfully picked up",
            RELEASED: "has been released from customs",
            CANCELLED: "has been cancelled",
          };

          const message =
            statusMessages[status] || `status has been updated to ${status}`;
          const trackingNumber = currentCargo.tracking_number || id;

          await notificationService.createTargetedNotification({
            account_id: currentCargo.account_id,
            name: "Cargo Status Update",
            message: `Your cargo ${trackingNumber} ${message}`,
            associated_table: "cargos",
            associated_id: id,
            to: "*",
            details: {
              previousStatus,
              newStatus: status,
              trackingNumber,
              notes,
              authorizedBy,
              timestamp: new Date().toISOString(),
            },
          });

          console.log(
            `✅ Notification created for cargo ${trackingNumber} status change: ${previousStatus} → ${status}`
          );

          // Add a notification
        } catch (notificationError) {
          // Don't fail the status update if notification creation fails
          console.warn(
            "⚠️  Failed to create cargo status notification:",
            notificationError
          );
        }
      }

      // If status changed to DELIVERED, automatically generate release authorization
      if (
        status === "READY_FOR_PICKUP" &&
        previousStatus !== "READY_FOR_PICKUP"
      ) {
        try {
          const releaseService = await getReleaseAuthorizationService();

          // Create release authorization
          const releaseAuthData = {
            cargo_id: id,
            authorized_by: authorizedBy || "system", // Use provided user ID or default to system
            qr_verified: false,
            documents_verified: true, // Assume docs are verified when cargo is delivered
            payment_verified: true, // Assume payment is verified when cargo is delivered
            customs_cleared: true, // Assume customs cleared when cargo is delivered
            recipient_verified: false, // Recipient verification happens during pickup
            verification_notes:
              notes ||
              `Automatic release authorization generated for delivered cargo ${currentCargo.tracking_number || id}`,
            pickup_authorized_person: null,
          };

          const releaseResult =
            await releaseService.createReleaseAuthorization(releaseAuthData);

          if (releaseResult.success) {
            console.log(
              `✅ Release authorization automatically generated for cargo ${id}`
            );
          } else {
            console.warn(
              `⚠️  Failed to auto-generate release authorization for cargo ${id}:`,
              releaseResult.error
            );
          }
        } catch (error) {
          // Don't fail the status update if release authorization creation fails
          console.error(
            "❌ Error auto-generating release authorization:",
            error
          );
        }
      }

      // TODO: Create a log entry for the status change
      // This would be handled by the logs service when created

      return result;
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to update cargo status",
        success: false,
      };
    }
  }

  // Assign cargo to batch
  async assignToBatch(
    cargoId: string,
    batchId: string
  ): Promise<ServiceResponse<Cargo>> {
    const { data, error } = await this.supabase
      .from("cargos")
      .update({
        batch_id: batchId,
        updated_at: new Date().toISOString(),
      } as any)
      .eq("id", cargoId)
      .select()
      .single();

    if (error) {
      return {
        data: null,
        error: error.message,
        success: false,
      };
    }

    return {
      data: data as any,
      error: null,
      success: true,
    };
  }

  // Remove cargo from batch
  async removeFromBatch(cargoId: string): Promise<ServiceResponse<Cargo>> {
    const { data, error } = await this.supabase
      .from("cargos")
      .update({
        batch_id: null,
        updated_at: new Date().toISOString(),
      } as any)
      .eq("id", cargoId)
      .select()
      .single();

    if (error) {
      return {
        data: null,
        error: error.message,
        success: false,
      };
    }

    return {
      data: data as any,
      error: null,
      success: true,
    };
  }

  // Get cargo statistics
  async getCargoStats(): Promise<
    ServiceResponse<{
      totalCargos: number;
      cargosByStatus: Array<{ status: string; count: number }>;
      totalWeight: number;
      totalCBM: number;
      totalValue: number;
      averageWeight: number;
      averageCBM: number;
      averageValue: number;
    }>
  > {
    try {
      const { data: cargos, error } = await this.supabase
        .from("cargos")
        .select("status, weight_value, cbm_value, total_price")
        .not("status", "eq", "INACTIVE");

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      const totalCargos = cargos?.length || 0;

      // Group by status
      const statusCounts = new Map<string, number>();
      cargos?.forEach((cargo) => {
        const status = cargo.status || "UNKNOWN";
        statusCounts.set(status, (statusCounts.get(status) || 0) + 1);
      });

      // Calculate totals
      const totalWeight =
        cargos?.reduce((sum, cargo) => sum + (cargo.weight_value || 0), 0) || 0;
      const totalCBM =
        cargos?.reduce((sum, cargo) => sum + (cargo.cbm_value || 0), 0) || 0;
      const totalValue =
        cargos?.reduce((sum, cargo) => sum + (cargo.total_price || 0), 0) || 0;

      // Calculate averages
      const averageWeight = totalCargos > 0 ? totalWeight / totalCargos : 0;
      const averageCBM = totalCargos > 0 ? totalCBM / totalCargos : 0;
      const averageValue = totalCargos > 0 ? totalValue / totalCargos : 0;

      return {
        data: {
          totalCargos,
          cargosByStatus: Array.from(statusCounts.entries()).map(
            ([status, count]) => ({ status, count })
          ),
          totalWeight,
          totalCBM,
          totalValue,
          averageWeight,
          averageCBM,
          averageValue,
        },
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to get cargo statistics",
        success: false,
      };
    }
  }

  // Get unassigned cargos (not in any batch)
  async getUnassignedCargos(
    params?: QueryParams
  ): Promise<ServiceListResponse<CargoWithRelations>> {
    const filters = { ...params?.filters, batch_id: null };
    return this.getAllCargosWithRelations({ ...params, filters });
  }

  // Release Authorization specific methods with document URLs

  // Get cargo with relations and documents for release authorization
  async getCargoWithDocuments(
    id: string
  ): Promise<ServiceResponse<CargoWithDocuments>> {
    try {
      // First get the cargo with relations
      const cargoResult = await this.getCargoWithRelations(id);

      if (!cargoResult.success || !cargoResult.data) {
        return cargoResult as ServiceResponse<CargoWithDocuments>;
      }

      // Then append document URLs
      const cargoWithDocuments = await this.appendDocumentUrls(
        cargoResult.data
      );

      return {
        data: cargoWithDocuments,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to get cargo with documents",
        success: false,
      };
    }
  }

  // Get cargo by tracking number with documents for release authorization
  async getCargoByTrackingNumberWithDocuments(
    trackingNumber: string
  ): Promise<ServiceResponse<CargoWithDocuments>> {
    try {
      // First get the cargo by tracking number
      const cargoResult = await this.getCargoByTrackingNumber(trackingNumber);

      if (!cargoResult.success || !cargoResult.data) {
        return cargoResult as ServiceResponse<CargoWithDocuments>;
      }

      // Then append document URLs
      const cargoWithDocuments = await this.appendDocumentUrls(
        cargoResult.data
      );

      return {
        data: cargoWithDocuments,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error:
          error.message ||
          "Failed to get cargo by tracking number with documents",
        success: false,
      };
    }
  }

  // Calculate cargo pricing
  calculateCargoPrice(
    weight_value: number,
    cbm_value: number,
    unit_price: number
  ): number {
    // Simple pricing calculation - can be made more complex based on business rules
    const weightPrice = weight_value * unit_price;
    const volumePrice = cbm_value * unit_price * 100; // Assuming volume is more expensive
    return Math.max(weightPrice, volumePrice); // Charge based on higher of weight or volume
  }

  // Update cargo pricing
  async updateCargoPricing(
    id: string,
    unit_price: number
  ): Promise<ServiceResponse<Cargo>> {
    try {
      const cargoResult = await this.getById(id);
      if (!cargoResult.success || !cargoResult.data) {
        return cargoResult;
      }

      const cargo = cargoResult.data;
      const total_price = this.calculateCargoPrice(
        cargo.weight_value || 0,
        cargo.cbm_value || 0,
        unit_price
      );

      // Use direct Supabase update to avoid type conflicts
      const { data, error } = await this.supabase
        .from("cargos")
        .update({
          unit_price,
          total_price,
          updated_at: new Date().toISOString(),
        } as any)
        .eq("id", id)
        .select()
        .single();

      if (error) {
        return {
          data: null,
          error: error.message,
          success: false,
        };
      }

      return {
        data: data as any,
        error: null,
        success: true,
      };
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to update cargo pricing",
        success: false,
      };
    }
  }

  // Update cargo with invoice line item and entity relation updates
  async updateCargoWithInvoiceUpdates(
    id: string,
    updateData: Partial<CargoInsert>
  ): Promise<ServiceResponse<Cargo>> {
    const originalTableName = this.tableName;

    try {
      // Get current cargo data
      const currentCargoResult = await this.getCargoWithRelations(id);
      if (!currentCargoResult.success || !currentCargoResult.data) {
        return {
          data: null,
          error: "Cargo not found",
          success: false,
        };
      }

      const currentCargo = currentCargoResult.data;

      // Calculate new pricing if weight, CBM, or unit price changed
      let calculatedData = { ...updateData };

      // Update the cargo using direct Supabase update to avoid type conflicts
      const { data: cargoData, error: cargoError } = await this.supabase
        .from("cargos")
        .update({
          ...updateData,
          updated_at: new Date().toISOString(),
        } as any)
        .eq("id", id)
        .select()
        .single();

      const updateResult = cargoError
        ? {
            data: null,
            error: cargoError.message,
            success: false,
          }
        : {
            data: cargoData as any,
            error: null,
            success: true,
          };
      if (!updateResult.success) {
        return updateResult;
      }

      // Handle invoice line item updates if pricing, quantity, or entity changed
      const pricingChanged =
        updateData.weight_value !== undefined ||
        updateData.cbm_value !== undefined ||
        updateData.unit_price !== undefined ||
        updateData.total_price !== undefined ||
        updateData.quantity !== undefined; // Include quantity changes

      const entityChanged =
        updateData.customer_id !== undefined ||
        (updateData as any).supplier_id !== undefined;

      const invoiceIdChanged =
        updateData.invoice_id !== undefined &&
        updateData.invoice_id !== currentCargo.invoice_id;

      if (pricingChanged || entityChanged) {
        await this.updateAssociatedInvoices(currentCargo, calculatedData);
      }

      // Handle moving cargo between invoices
      if (invoiceIdChanged) {
        await this.handleInvoiceTransfer(
          currentCargo,
          updateData.invoice_id || null,
          calculatedData
        );
      }

      return updateResult;
    } catch (error: any) {
      return {
        data: null,
        error: error.message || "Failed to update cargo with invoice updates",
        success: false,
      };
    } finally {
      this.tableName = originalTableName;
    }
  }

  // Update associated invoices when cargo changes
  private async updateAssociatedInvoices(
    currentCargo: any,
    updateData: Partial<CargoInsert>
  ): Promise<void> {
    const originalTableName = this.tableName;

    try {
      // Find invoices that contain this cargo as a line item using JSONB query
      this.tableName = "invoices";
      const { data: invoices } = await this.supabase
        .from("invoices")
        .select("*")
        .filter(
          "line_items",
          "cs",
          JSON.stringify([{ cargo_id: currentCargo.id }])
        );

      // Also check for direct invoice_id relationship
      let directlyReferencedInvoice = null;
      if (currentCargo.invoice_id) {
        const { data: directInvoice } = await this.supabase
          .from("invoices")
          .select("*")
          .eq("id", currentCargo.invoice_id)
          .single();

        if (directInvoice) {
          directlyReferencedInvoice = directInvoice;
        }
      }

      // If the above query doesn't work well, fall back to fetching all invoices and filtering
      if (!invoices || invoices.length === 0) {
        // Alternative approach: fetch all invoices and filter client-side
        const { data: allInvoices } = await this.supabase
          .from("invoices")
          .select("*")
          .not("line_items", "is", null);

        const matchingInvoices = (allInvoices || []).filter((invoice: any) => {
          const lineItems = invoice.line_items || [];
          return lineItems.some(
            (item: any) =>
              item.cargo_id === currentCargo.id ||
              item.cargo_id === currentCargo.tracking_number
          );
        });

        // Combine both sets of invoices, avoiding duplicates
        const allMatchingInvoices = [...matchingInvoices];
        if (
          directlyReferencedInvoice &&
          !allMatchingInvoices.find(
            (inv) => inv.id === directlyReferencedInvoice.id
          )
        ) {
          allMatchingInvoices.push(directlyReferencedInvoice);
        }

        if (allMatchingInvoices.length === 0) {
          return;
        }

        // Process the matching invoices
        await this.processInvoiceUpdates(
          allMatchingInvoices,
          currentCargo,
          updateData
        );
        return;
      }

      // Process the invoices found by the JSONB query, including directly referenced invoice
      const allInvoicesFromQuery = [...invoices];
      if (
        directlyReferencedInvoice &&
        !allInvoicesFromQuery.find(
          (inv) => inv.id === directlyReferencedInvoice.id
        )
      ) {
        allInvoicesFromQuery.push(directlyReferencedInvoice);
      }
      await this.processInvoiceUpdates(
        allInvoicesFromQuery,
        currentCargo,
        updateData
      );
    } catch (error) {
      console.error("Error updating associated invoices:", error);
    } finally {
      this.tableName = originalTableName;
    }
  }

  // Helper method to process invoice updates for line items
  private async processInvoiceUpdates(
    invoices: any[],
    currentCargo: any,
    updateData: Partial<CargoInsert>
  ): Promise<void> {
    for (const invoice of invoices) {
      const lineItems = invoice.line_items || [];
      let invoiceUpdated = false;
      let newSubtotal = 0;
      let newTotal = 0;

      // Update line items that match this cargo
      const updatedLineItems = lineItems.map((item: any) => {
        if (
          item.cargo_id === currentCargo.id ||
          item.cargo_id === currentCargo.tracking_number
        ) {
          // Update the line item with new cargo data
          const newQuantity =
            updateData.quantity !== undefined
              ? updateData.quantity
              : item.quantity;
          const newUnitPrice =
            updateData.unit_price !== undefined
              ? updateData.unit_price
              : item.unitPrice;
          const newFactorValue =
            updateData.quantity !== undefined
              ? updateData.quantity
              : item.factor_value || 1;

          // Calculate the new total based on factor_value and unit price
          const calculatedTotal = newFactorValue * newUnitPrice;

          const updatedItem = {
            ...item,
            // Core cargo identification
            cargo_id: currentCargo.id,
            tracking_number:
              updateData.tracking_number || currentCargo.tracking_number || "",
            china_tracking_number:
              updateData.china_tracking_number ||
              currentCargo.china_tracking_number ||
              "",
            // Description with cargo details
            description: item.description || item.particular,
            // Physical properties
            weight_value:
              updateData.weight_value !== undefined
                ? updateData.weight_value
                : currentCargo.weight_value || 0,
            weight_unit:
              updateData.weight_unit || currentCargo.weight_unit || "KILOGRAMS",
            cbm_value:
              updateData.cbm_value !== undefined
                ? updateData.cbm_value
                : currentCargo.cbm_value || 0,
            cbm_unit:
              updateData.cbm_unit || currentCargo.cbm_unit || "METER_CUBIC",
            ctn:
              updateData.ctn !== undefined
                ? updateData.ctn
                : currentCargo.ctn || 0,
            // Pricing and quantity
            quantity: newQuantity,
            factor_unit:
              updateData.factor_unit || currentCargo.factor_unit || "Quantity",
            factor_value: newFactorValue, // Update factor_value when quantity changes
            unitPrice: newUnitPrice,
            total:
              updateData.total_price !== undefined
                ? updateData.total_price
                : calculatedTotal, // Use provided total or calculated total
          };
          invoiceUpdated = true;
          return updatedItem;
        }
        return item;
      });

      // Recalculate invoice totals using the updated line item totals
      updatedLineItems.forEach((item: any) => {
        // Use the item's total field if available, otherwise calculate using factor_value * unitPrice
        const itemTotal =
          item.total !== undefined
            ? Number(item.total)
            : (item.factor_value || 1) * (item.unitPrice || 0);
        newSubtotal += itemTotal;
      });
      newTotal = newSubtotal;

      if (invoiceUpdated) {
        // Update the invoice
        await this.supabase
          .from("invoices")
          .update({
            line_items: updatedLineItems,
            subtotal: newSubtotal,
            total: newTotal,
            updated_at: new Date().toISOString(),
            // Update entity relation if changed
            customer_id:
              updateData.customer_id !== undefined
                ? updateData.customer_id
                : invoice.customer_id,
            supplier_id:
              (updateData as any).supplier_id !== undefined
                ? (updateData as any).supplier_id
                : (invoice as any).supplier_id,
          })
          .eq("id", invoice.id);
      }
    }
  }

  /**
   * Enhanced delete method that handles related invoice data
   * Removes cargo line items from invoices and recalculates totals
   * If an invoice only contains the deleted cargo, the entire invoice is deleted
   */
  async deleteCargoWithInvoiceUpdates(
    id: string
  ): Promise<ServiceResponse<boolean>> {
    const originalTableName = this.tableName;

    try {
      // Get current cargo data before deletion
      const currentCargoResult = await this.getById(id);
      if (!currentCargoResult.success || !currentCargoResult.data) {
        return {
          data: false,
          error: "Cargo not found",
          success: false,
        };
      }

      const currentCargo = currentCargoResult.data;

      // Find and update associated invoices before deleting cargo
      console.log(`Processing invoice updates for cargo ${id}...`);
      await this.removeCargoFromInvoices(currentCargo);
      console.log(`Invoice updates completed for cargo ${id}`);

      // Perform the soft delete (set status to INACTIVE)
      console.log(`Performing soft delete for cargo ${id}...`);
      const deleteResult = await this.delete(id);
      console.log(
        `Soft delete completed for cargo ${id}:`,
        deleteResult.success
      );

      return deleteResult;
    } catch (error: any) {
      return {
        data: false,
        error: error.message || "Failed to delete cargo with invoice updates",
        success: false,
      };
    } finally {
      this.tableName = originalTableName;
    }
  }

  /**
   * Remove cargo from all associated invoices and recalculate totals
   * Deletes invoices that only contain the removed cargo as their sole line item
   * Now also handles direct invoice_id relationships
   */
  private async removeCargoFromInvoices(currentCargo: any): Promise<void> {
    const originalTableName = this.tableName;

    try {
      // Find invoices that contain this cargo as a line item OR are directly referenced
      this.tableName = "invoices";

      // Use client-side filtering approach as JSONB contains queries can be unreliable
      // Fetch all invoices with line_items and filter client-side for better reliability
      const { data: allInvoices, error } = await this.supabase
        .from("invoices")
        .select("*")
        .not("line_items", "is", null);

      if (error) {
        console.error("Error fetching invoices for cargo deletion:", error);
        return;
      }

      // Also find invoices directly referenced by the cargo's invoice_id
      let directlyReferencedInvoice = null;
      if (currentCargo.invoice_id) {
        const { data: directInvoice } = await this.supabase
          .from("invoices")
          .select("*")
          .eq("id", currentCargo.invoice_id)
          .single();

        if (directInvoice) {
          directlyReferencedInvoice = directInvoice;
        }
      }

      const matchingInvoices = (allInvoices || []).filter((invoice: any) => {
        const lineItems = invoice.line_items || [];
        return lineItems.some(
          (item: any) =>
            item.cargo_id === currentCargo.id ||
            item.cargo_id === currentCargo.tracking_number
        );
      });

      // Combine both sets of invoices, avoiding duplicates
      const allMatchingInvoices = [...matchingInvoices];
      if (
        directlyReferencedInvoice &&
        !allMatchingInvoices.find(
          (inv) => inv.id === directlyReferencedInvoice.id
        )
      ) {
        allMatchingInvoices.push(directlyReferencedInvoice);
      }

      if (allMatchingInvoices.length === 0) {
        console.log(
          `No invoices found containing cargo ${currentCargo.id} (${currentCargo.tracking_number})`
        );
        return;
      }

      console.log(
        `Processing ${allMatchingInvoices.length} invoices containing cargo ${currentCargo.id}`
      );

      // Process each invoice to remove the cargo line item
      for (const invoice of allMatchingInvoices) {
        // Validate invoice ID
        if (!invoice.id || typeof invoice.id !== "string") {
          console.error(`Invalid invoice ID: ${invoice.id}`);
          continue;
        }

        const lineItems = invoice.line_items || [];
        // Filter out line items that match this cargo
        const updatedLineItems = lineItems.filter(
          (item: any) =>
            item.cargo_id !== currentCargo.id &&
            item.cargo_id !== currentCargo.tracking_number
        );

        console.log(
          `Invoice ${invoice.id}: ${lineItems.length} → ${updatedLineItems.length} line items`
        );

        // If the invoice only had this one cargo line item, delete the entire invoice
        if (lineItems.length === 1 && updatedLineItems.length === 0) {
          console.log(
            `Deleting invoice ${invoice.id} as it only contained the deleted cargo`
          );
          try {
            const deleteResult = await invoiceService.deleteInvoice(invoice.id);
            if (!deleteResult.success) {
              console.error(
                `Failed to delete invoice ${invoice.id}:`,
                deleteResult.error
              );
            } else {
              console.log(`Successfully deleted invoice ${invoice.id}`);
            }
          } catch (error) {
            console.error(
              `Exception while deleting invoice ${invoice.id}:`,
              error
            );
          }
          continue; // Skip to next invoice since this one is deleted
        }

        // If there are remaining line items, update the invoice
        if (updatedLineItems.length > 0) {
          // Recalculate invoice totals
          const newSubtotal = updatedLineItems.reduce(
            (sum: number, item: any) => {
              // Use the total field if available, otherwise calculate it
              const itemTotal =
                item.total || (item.factor_value || 1) * (item.unitPrice || 0);
              return sum + Number(itemTotal);
            },
            0
          );

          const newTotal = newSubtotal;

          // Update the invoice
          const { error: updateError } = await this.supabase
            .from("invoices")
            .update({
              line_items: updatedLineItems,
              subtotal: Number(newSubtotal),
              total: Number(newTotal),
              updated_at: new Date().toISOString(),
            })
            .eq("id", invoice.id);

          if (updateError) {
            console.error(
              `Failed to update invoice ${invoice.id}:`,
              updateError
            );
          } else {
            console.log(
              `Successfully updated invoice ${invoice.id} with ${updatedLineItems.length} line items`
            );
          }
        }
      }
    } catch (error) {
      console.error("Error removing cargo from invoices:", error);
    } finally {
      this.tableName = originalTableName;
    }
  }

  // Handle transferring cargo between invoices
  private async handleInvoiceTransfer(
    currentCargo: any,
    newInvoiceId: string | null,
    updateData: Partial<CargoInsert>
  ): Promise<void> {
    const originalTableName = this.tableName;

    try {
      // Remove cargo from old invoice(s) if it exists in line items
      if (currentCargo.invoice_id || newInvoiceId !== currentCargo.invoice_id) {
        await this.removeCargoFromInvoices(currentCargo.id);
      }

      // Add cargo to new invoice if newInvoiceId is provided
      if (newInvoiceId) {
        this.tableName = "invoices";
        const { data: targetInvoice } = await this.supabase
          .from("invoices")
          .select("*")
          .eq("id", newInvoiceId)
          .single();

        if (targetInvoice) {
          const lineItems = targetInvoice.line_items || [];

          // Create new line item for this cargo with comprehensive data
          const newLineItem = {
            // Core cargo identification
            cargo_id: currentCargo.id,
            tracking_number:
              updateData.tracking_number || currentCargo.tracking_number || "",
            china_tracking_number:
              updateData.china_tracking_number ||
              currentCargo.china_tracking_number ||
              "",
            // Description with cargo details
            description: `${updateData.particular || currentCargo.particular} - ${updateData.weight_unit || currentCargo.weight_unit}: ${updateData.weight_value || currentCargo.weight_value}, ${updateData.cbm_unit || currentCargo.cbm_unit}: ${updateData.cbm_value || currentCargo.cbm_value}`,
            // Physical properties
            weight_value:
              updateData.weight_value !== undefined
                ? updateData.weight_value
                : currentCargo.weight_value || 0,
            weight_unit:
              updateData.weight_unit || currentCargo.weight_unit || "KILOGRAMS",
            cbm_value:
              updateData.cbm_value !== undefined
                ? updateData.cbm_value
                : currentCargo.cbm_value || 0,
            cbm_unit:
              updateData.cbm_unit || currentCargo.cbm_unit || "METER_CUBIC",
            ctn:
              updateData.ctn !== undefined
                ? updateData.ctn
                : currentCargo.ctn || 0,
            // Pricing and quantity
            quantity: updateData.quantity || currentCargo.quantity || 1,
            factor_unit:
              updateData.factor_unit || currentCargo.factor_unit || "Quantity",
            factor_value:
              updateData.factor_value ||
              updateData.quantity ||
              currentCargo.quantity ||
              1,
            unitPrice: updateData.unit_price || currentCargo.unit_price || 0,
            total: updateData.total_price || currentCargo.total_price || 0,
          };

          const updatedLineItems = [...lineItems, newLineItem];

          // Recalculate invoice totals
          const newSubtotal = updatedLineItems.reduce(
            (sum: number, item: any) => {
              const itemTotal =
                item.total !== undefined
                  ? Number(item.total)
                  : (item.factor_value || 1) * (item.unitPrice || 0);
              return sum + itemTotal;
            },
            0
          );

          // Update the target invoice
          const { error: updateError } = await this.supabase
            .from("invoices")
            .update({
              line_items: updatedLineItems,
              subtotal: newSubtotal,
              total: newSubtotal,
              updated_at: new Date().toISOString(),
            })
            .eq("id", newInvoiceId);

          if (updateError) {
            console.error("Error adding cargo to new invoice:", updateError);
            throw new Error(
              `Failed to add cargo to invoice: ${updateError.message}`
            );
          }
        }
      }
    } catch (error) {
      console.error("Error transferring cargo between invoices:", error);
      throw error;
    } finally {
      this.tableName = originalTableName;
    }
  }

  /**
   * Enhanced update method that handles related invoice data
   * This is an alias for the existing updateCargoWithInvoiceUpdates method
   */
  async updateCargoWithInvoiceSync(
    id: string,
    updateData: Partial<CargoInsert>
  ): Promise<ServiceResponse<Cargo>> {
    return this.updateCargoWithInvoiceUpdates(id, updateData);
  }
}

// Export singleton instance
export const cargoService = new CargoService();
