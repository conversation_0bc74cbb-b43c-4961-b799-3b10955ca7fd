/**
 * Excel Module Test File
 * Simple tests to verify the Excel generation functionality
 */

import { ExcelGenerator, quickExport, EXCEL_THEMES } from "./index";

// Test data
const sampleInvoices = [
  {
    id: "1",
    inv_number: "INV-001",
    customer: { name: "<PERSON>", email: "<EMAIL>" },
    total: 1500.0,
    subtotal: 1350.0,
    status: "PAID",
    created_at: "2024-01-15T10:30:00Z",
    due_at: "2024-02-15T10:30:00Z",
    currency: "USD",
  },
  {
    id: "2",
    inv_number: "INV-002",
    customer: { name: "<PERSON>", email: "<EMAIL>" },
    total: 2200.0,
    subtotal: 2000.0,
    status: "PENDING",
    created_at: "2024-01-16T14:20:00Z",
    due_at: "2024-02-16T14:20:00Z",
    currency: "USD",
  },
];

const sampleCargo = [
  {
    id: "1",
    tracking_number: "CG001",
    china_tracking_number: "CN123456",
    description: "Electronics - Smartphones",
    customer: { name: "<PERSON>" },
    weight_value: 15.5,
    weight_unit: "KILOGRAMS",
    cbm_value: 0.25,
    cbm_unit: "METER_CUBIC",
    quantity: 10,
    status: "ACTIVE",
    batch: { code: "BCH001" },
    created_at: "2024-01-15T08:00:00Z",
  },
  {
    id: "2",
    tracking_number: "CG002",
    china_tracking_number: "CN789012",
    description: "Clothing - Winter Jackets",
    customer: { name: "Jane Smith" },
    weight_value: 8.2,
    weight_unit: "KILOGRAMS",
    cbm_value: 0.18,
    cbm_unit: "METER_CUBIC",
    quantity: 5,
    status: "ACTIVE",
    batch: { code: "BCH001" },
    created_at: "2024-01-15T09:30:00Z",
  },
];

const sampleTransactions = [
  {
    id: "1",
    type: "CREDIT",
    amount: 1500.0,
    description: "Invoice payment - INV-001",
    created_at: "2024-01-20T10:00:00Z",
    ledger: { name: "Customer Payments" },
    status: "COMPLETED",
  },
  {
    id: "2",
    type: "DEBIT",
    amount: 500.0,
    description: "Shipping costs",
    created_at: "2024-01-21T15:30:00Z",
    ledger: { name: "Operating Expenses" },
    status: "COMPLETED",
  },
];

/**
 * Test basic JSON export
 */
export async function testBasicExport() {
  try {
    const generator = new ExcelGenerator();
    const result = await generator.generateFromJSON(sampleInvoices, {
      sheetName: "Test Invoices",
      filename: "test_invoices.xlsx",
      theme: "FINANCIAL",
    });

    if (result.success && result.data) {
      return result.data.buffer;
    } else {
      return null;
    }
  } catch (error) {
    return null;
  }
}

/**
 * Test template-based export
 */
export async function testTemplateExport() {
  console.log("Testing template export...");

  try {
    const generator = new ExcelGenerator();
    const result = await generator.generateFromTemplate("Cargo Manifest", {
      cargo: sampleCargo,
    });

    if (result.success && result.data) {
      console.log("✅ Template export successful");
      console.log(`File: ${result.data.filename}`);
      console.log(`Size: ${result.data.size} bytes`);
      return result.data.buffer;
    } else {
      console.error("❌ Template export failed:", result.error);
      return null;
    }
  } catch (error) {
    console.error("❌ Template export error:", error);
    return null;
  }
}

/**
 * Test multi-sheet export
 */
export async function testMultiSheetExport() {
  console.log("Testing multi-sheet export...");

  try {
    const generator = new ExcelGenerator();
    const result = await generator.generateMultiSheet(
      [
        {
          name: "Invoices",
          data: sampleInvoices,
          options: { theme: "FINANCIAL" },
        },
        {
          name: "Cargo",
          data: sampleCargo,
          options: { theme: "LOGISTICS" },
        },
        {
          name: "Transactions",
          data: sampleTransactions,
          options: { theme: "FINANCIAL" },
        },
      ],
      {
        filename: "multi_sheet_test.xlsx",
      }
    );

    if (result.success && result.data) {
      console.log("✅ Multi-sheet export successful");
      console.log(`File: ${result.data.filename}`);
      console.log(`Size: ${result.data.size} bytes`);
      return result.data.buffer;
    } else {
      console.error("❌ Multi-sheet export failed:", result.error);
      return null;
    }
  } catch (error) {
    console.error("❌ Multi-sheet export error:", error);
    return null;
  }
}

/**
 * Test quick export functions
 */
export async function testQuickExports() {
  console.log("Testing quick export functions...");

  try {
    // Test JSON quick export
    console.log("Testing quickExport.json...");
    const jsonResult = await quickExport.json(
      sampleInvoices,
      "quick_invoices.xlsx"
    );
    console.log(
      jsonResult
        ? "✅ Quick JSON export successful"
        : "❌ Quick JSON export failed"
    );

    // Test template quick export
    console.log("Testing quickExport.template...");
    const templateResult = await quickExport.template(
      "Financial Report",
      {
        summary: { totalRevenue: 3700, totalExpenses: 500 },
        transactions: sampleTransactions,
        totals: { netIncome: 3200 },
      },
      "quick_financial.xlsx"
    );
    console.log(
      templateResult
        ? "✅ Quick template export successful"
        : "❌ Quick template export failed"
    );

    // Test multi-sheet quick export
    console.log("Testing quickExport.multiSheet...");
    const multiResult = await quickExport.multiSheet(
      [
        { name: "Invoices", data: sampleInvoices },
        { name: "Cargo", data: sampleCargo },
      ],
      "quick_multi.xlsx"
    );
    console.log(
      multiResult
        ? "✅ Quick multi-sheet export successful"
        : "❌ Quick multi-sheet export failed"
    );

    return jsonResult && templateResult && multiResult;
  } catch (error) {
    console.error("❌ Quick exports error:", error);
    return false;
  }
}

/**
 * Test data transformation utilities
 */
export async function testDataTransformation() {
  console.log("Testing data transformation utilities...");

  try {
    const { jsonToExcelTable, formatHeaderName, createFinancialReport } =
      await import("./utils");

    // Test header formatting
    const headers = ["first_name", "lastName", "email_address", "phoneNumber"];
    const formattedHeaders = headers.map(formatHeaderName);
    console.log("Header formatting:", formattedHeaders);

    // Test table creation
    const table = jsonToExcelTable(sampleInvoices, {
      tableName: "Test Table",
      excludeColumns: ["id"],
      columnMapping: {
        inv_number: "Invoice Number",
        created_at: "Created Date",
      },
    });

    console.log("Table headers:", table.headers);
    console.log("Table rows:", table.rows.length);

    // Test financial report creation
    const report = createFinancialReport({
      summary: { totalRevenue: 3700, totalExpenses: 500 },
      transactions: sampleTransactions,
      totals: { netIncome: 3200 },
    });

    console.log("Financial report worksheets:", report.worksheets.length);

    console.log("✅ Data transformation tests successful");
    return true;
  } catch (error) {
    console.error("❌ Data transformation error:", error);
    return false;
  }
}

/**
 * Test import functionality (mock)
 */
export async function testImportFunctionality() {
  console.log("Testing import functionality...");

  try {
    // Create a simple Excel buffer first
    const generator = new ExcelGenerator();
    const exportResult = await generator.generateFromJSON(sampleInvoices, {
      filename: "test_import.xlsx",
    });

    if (!exportResult.success || !exportResult.data) {
      throw new Error("Failed to create test file for import");
    }

    // Test import
    const importResult = await generator.importExcel(exportResult.data.buffer);

    if (importResult.success && importResult.data) {
      console.log("✅ Import test successful");
      console.log(`Worksheets: ${importResult.data.worksheets.length}`);
      importResult.data.worksheets.forEach((sheet) => {
        console.log(`  - ${sheet.name}: ${sheet.rowCount} rows`);
      });
      return true;
    } else {
      console.error("❌ Import test failed:", importResult.error);
      return false;
    }
  } catch (error) {
    console.error("❌ Import test error:", error);
    return false;
  }
}

/**
 * Run all tests
 */
export async function runAllTests() {
  console.log("🧪 Starting Excel Module Tests...\n");

  const results = {
    basicExport: await testBasicExport(),
    templateExport: await testTemplateExport(),
    multiSheetExport: await testMultiSheetExport(),
    quickExports: await testQuickExports(),
    dataTransformation: await testDataTransformation(),
    importFunctionality: await testImportFunctionality(),
  };

  console.log("\n📊 Test Results Summary:");
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? "✅" : "❌"} ${test}`);
  });

  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;

  console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);

  return passedTests === totalTests;
}

// Export test runner for use in components
export default runAllTests;
