/**
 * React Hook for Excel Export Functionality
 * Provides easy integration with existing Shamwaa Logistics components
 */

import { useState, useCallback } from 'react';
import { ExcelGenerator, quickExport, EXCEL_THEMES } from './index';
import type { ExcelExportResult, ExcelGenerationOptions } from './types';

interface UseExcelExportOptions {
  onSuccess?: (result: ExcelExportResult) => void;
  onError?: (error: string) => void;
  autoDownload?: boolean;
}

interface ExcelExportState {
  isExporting: boolean;
  lastExport: ExcelExportResult | null;
  error: string | null;
}

export function useExcelExport(options: UseExcelExportOptions = {}) {
  const [state, setState] = useState<ExcelExportState>({
    isExporting: false,
    lastExport: null,
    error: null
  });

  const generator = new ExcelGenerator();

  const setExporting = useCallback((isExporting: boolean) => {
    setState(prev => ({ ...prev, isExporting }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setState(prev => ({ ...prev, error }));
    if (error && options.onError) {
      options.onError(error);
    }
  }, [options]);

  const setSuccess = useCallback((result: ExcelExportResult) => {
    setState(prev => ({ ...prev, lastExport: result, error: null }));
    if (options.onSuccess) {
      options.onSuccess(result);
    }
  }, [options]);

  /**
   * Export JSON data as Excel
   */
  const exportJSON = useCallback(async (
    data: Record<string, any>[],
    exportOptions?: {
      filename?: string;
      sheetName?: string;
      theme?: keyof typeof EXCEL_THEMES;
      excludeColumns?: string[];
      columnMapping?: Record<string, string>;
    }
  ) => {
    if (!data || data.length === 0) {
      setError('No data to export');
      return false;
    }

    setExporting(true);
    setError(null);

    try {
      const result = await generator.generateFromJSON(data, exportOptions);
      
      if (result.success && result.data) {
        setSuccess(result);
        
        if (options.autoDownload !== false) {
          generator.downloadExcel(result.data.buffer, result.data.filename);
        }
        
        return true;
      } else {
        setError(result.error || 'Export failed');
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(errorMessage);
      return false;
    } finally {
      setExporting(false);
    }
  }, [generator, setExporting, setError, setSuccess, options.autoDownload]);

  /**
   * Export using a predefined template
   */
  const exportTemplate = useCallback(async (
    templateName: string,
    data: any,
    exportOptions?: ExcelGenerationOptions
  ) => {
    setExporting(true);
    setError(null);

    try {
      const result = await generator.generateFromTemplate(templateName, data, exportOptions);
      
      if (result.success && result.data) {
        setSuccess(result);
        
        if (options.autoDownload !== false) {
          generator.downloadExcel(result.data.buffer, result.data.filename);
        }
        
        return true;
      } else {
        setError(result.error || 'Template export failed');
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(errorMessage);
      return false;
    } finally {
      setExporting(false);
    }
  }, [generator, setExporting, setError, setSuccess, options.autoDownload]);

  /**
   * Export multiple sheets
   */
  const exportMultiSheet = useCallback(async (
    sources: Array<{
      name: string;
      data: Record<string, any>[];
      options?: {
        theme?: keyof typeof EXCEL_THEMES;
        excludeColumns?: string[];
        columnMapping?: Record<string, string>;
      };
    }>,
    exportOptions?: ExcelGenerationOptions
  ) => {
    if (!sources || sources.length === 0) {
      setError('No data sources provided');
      return false;
    }

    setExporting(true);
    setError(null);

    try {
      const result = await generator.generateMultiSheet(sources, exportOptions);
      
      if (result.success && result.data) {
        setSuccess(result);
        
        if (options.autoDownload !== false) {
          generator.downloadExcel(result.data.buffer, result.data.filename);
        }
        
        return true;
      } else {
        setError(result.error || 'Multi-sheet export failed');
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(errorMessage);
      return false;
    } finally {
      setExporting(false);
    }
  }, [generator, setExporting, setError, setSuccess, options.autoDownload]);

  /**
   * Quick export functions
   */
  const quickExports = {
    invoices: useCallback(async (invoices: any[], filename?: string) => {
      setExporting(true);
      try {
        const success = await quickExport.template('Invoice Export', { invoices }, filename);
        if (success) {
          setSuccess({ success: true });
        } else {
          setError('Invoice export failed');
        }
        return success;
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Invoice export error');
        return false;
      } finally {
        setExporting(false);
      }
    }, [setExporting, setError, setSuccess]),

    cargo: useCallback(async (cargo: any[], filename?: string) => {
      setExporting(true);
      try {
        const success = await quickExport.template('Cargo Manifest', { cargo }, filename);
        if (success) {
          setSuccess({ success: true });
        } else {
          setError('Cargo export failed');
        }
        return success;
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Cargo export error');
        return false;
      } finally {
        setExporting(false);
      }
    }, [setExporting, setError, setSuccess]),

    transactions: useCallback(async (transactions: any[], summary?: any, filename?: string) => {
      setExporting(true);
      try {
        const data = {
          summary: summary || {},
          transactions,
          totals: summary || {}
        };
        const success = await quickExport.template('Financial Report', data, filename);
        if (success) {
          setSuccess({ success: true });
        } else {
          setError('Transaction export failed');
        }
        return success;
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Transaction export error');
        return false;
      } finally {
        setExporting(false);
      }
    }, [setExporting, setError, setSuccess]),

    shipments: useCallback(async (shipments: any[], filename?: string) => {
      setExporting(true);
      try {
        const success = await quickExport.template('Shipment Tracking', { shipments }, filename);
        if (success) {
          setSuccess({ success: true });
        } else {
          setError('Shipment export failed');
        }
        return success;
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Shipment export error');
        return false;
      } finally {
        setExporting(false);
      }
    }, [setExporting, setError, setSuccess])
  };

  /**
   * Import Excel file
   */
  const importExcel = useCallback(async (file: File) => {
    setExporting(true);
    setError(null);

    try {
      const result = await generator.importExcel(file);
      
      if (result.success) {
        setSuccess({ success: true });
        return result.data;
      } else {
        setError(result.error || 'Import failed');
        return null;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Import error';
      setError(errorMessage);
      return null;
    } finally {
      setExporting(false);
    }
  }, [generator, setExporting, setError, setSuccess]);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, [setError]);

  /**
   * Download last export
   */
  const downloadLastExport = useCallback(() => {
    if (state.lastExport?.success && state.lastExport.data) {
      generator.downloadExcel(state.lastExport.data.buffer, state.lastExport.data.filename);
    }
  }, [state.lastExport, generator]);

  return {
    // State
    isExporting: state.isExporting,
    error: state.error,
    lastExport: state.lastExport,
    
    // Export functions
    exportJSON,
    exportTemplate,
    exportMultiSheet,
    
    // Quick exports
    quickExports,
    
    // Import
    importExcel,
    
    // Utilities
    clearError,
    downloadLastExport
  };
}

/**
 * Specialized hook for invoice exports
 */
export function useInvoiceExport(options?: UseExcelExportOptions) {
  const excel = useExcelExport(options);
  
  const exportInvoices = useCallback(async (
    invoices: any[],
    includeLineItems: boolean = false,
    filename?: string
  ) => {
    if (includeLineItems) {
      return excel.exportTemplate('Invoice Export', { invoices }, { filename });
    } else {
      return excel.exportJSON(invoices, {
        filename: filename || 'invoices_export.xlsx',
        theme: 'FINANCIAL',
        excludeColumns: ['line_items', 'attachments'],
        columnMapping: {
          'inv_number': 'Invoice Number',
          'created_at': 'Created Date',
          'due_at': 'Due Date'
        }
      });
    }
  }, [excel]);

  return {
    ...excel,
    exportInvoices
  };
}

/**
 * Specialized hook for logistics exports
 */
export function useLogisticsExport(options?: UseExcelExportOptions) {
  const excel = useExcelExport(options);
  
  const exportLogisticsData = useCallback(async (data: {
    shipments?: any[];
    cargo?: any[];
    batches?: any[];
    freights?: any[];
  }, filename?: string) => {
    const sources = [];
    
    if (data.shipments?.length) {
      sources.push({
        name: 'Shipments',
        data: data.shipments,
        options: { theme: 'LOGISTICS' as keyof typeof EXCEL_THEMES }
      });
    }
    
    if (data.cargo?.length) {
      sources.push({
        name: 'Cargo',
        data: data.cargo,
        options: { theme: 'LOGISTICS' as keyof typeof EXCEL_THEMES }
      });
    }
    
    if (data.batches?.length) {
      sources.push({
        name: 'Batches',
        data: data.batches,
        options: { theme: 'LOGISTICS' as keyof typeof EXCEL_THEMES }
      });
    }
    
    if (data.freights?.length) {
      sources.push({
        name: 'Freights',
        data: data.freights,
        options: { theme: 'LOGISTICS' as keyof typeof EXCEL_THEMES }
      });
    }
    
    return excel.exportMultiSheet(sources, { filename });
  }, [excel]);

  return {
    ...excel,
    exportLogisticsData
  };
}
