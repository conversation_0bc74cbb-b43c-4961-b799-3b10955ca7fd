/**
 * Predefined Excel templates for common document types
 */

import type {
  ExcelTemplate,
  ExcelWorkbook,
  ExcelWorksheet,
  ExcelTable,
} from "./types";
import { EXCEL_THEMES, EXCEL_NUMBER_FORMATS } from "./types";
import {
  jsonToExcelTable,
  createFinancialReport,
  createLogisticsReport,
  createInvoiceExport,
} from "./utils";

/**
 * Invoice template
 */
export const INVOICE_TEMPLATE: ExcelTemplate = {
  name: "Invoice Export",
  description: "Standard invoice export with line items",
  category: "Financial",
  structure: {
    worksheets: [
      {
        name: "Invoices",
        tables: [],
        autoFilter: true,
        freezePane: { row: 1, col: 0 },
      },
      {
        name: "Line Items",
        tables: [],
        autoFilter: true,
        freezePane: { row: 1, col: 0 },
      },
    ],
    metadata: {
      title: "Invoice Export",
      author: "Shamwaa Logistics System",
      description: "Invoice data with line item details",
    },
  },
  transformers: {
    invoices: (data: any[]) =>
      createInvoiceExport(data, { includeLineItems: true }),
  },
};

/**
 * Financial report template
 */
export const FINANCIAL_REPORT_TEMPLATE: ExcelTemplate = {
  name: "Financial Report",
  description: "Comprehensive financial report with transactions and summary",
  category: "Financial",
  structure: {
    worksheets: [
      {
        name: "Summary",
        tables: [],
        pageSetup: {
          orientation: "portrait",
        },
      },
      {
        name: "Transactions",
        tables: [],
        autoFilter: true,
        freezePane: { row: 1, col: 0 },
      },
      {
        name: "Analysis",
        tables: [],
        charts: [],
      },
    ],
    metadata: {
      title: "Financial Report",
      author: "Shamwaa Logistics System",
      description:
        "Financial analysis with transaction details and summary metrics",
    },
  },
  transformers: {
    financial: (data: any) => createFinancialReport(data),
  },
};

/**
 * Cargo manifest template
 */
export const CARGO_MANIFEST_TEMPLATE: ExcelTemplate = {
  name: "Cargo Manifest",
  description: "Detailed cargo manifest for shipments",
  category: "Logistics",
  structure: {
    worksheets: [
      {
        name: "Manifest",
        tables: [],
        autoFilter: true,
        freezePane: { row: 1, col: 0 },
        pageSetup: {
          orientation: "landscape",
        },
      },
    ],
    metadata: {
      title: "Cargo Manifest",
      author: "Shamwaa Logistics System",
      description:
        "Detailed cargo manifest with tracking and customer information",
    },
  },
  transformers: {
    cargo: (data: any[]) => ({
      worksheets: [
        {
          name: "Manifest",
          tables: [
            jsonToExcelTable(data, {
              tableName: "Cargo Manifest",
              headerStyle: EXCEL_THEMES.LOGISTICS.headerStyle,
              dataStyle: EXCEL_THEMES.LOGISTICS.dataStyle,
              excludeColumns: ["metadata", "attachments"],
              columnMapping: {
                tracking_number: "Tracking Number",
                china_tracking_number: "China Tracking",
                description: "Description",
                weight_value: "Weight (kg)",
                cbm_value: "Volume (m³)",
                customer_name: "Customer",
                code: "Batch",
                status: "Status",
              },
              formatters: {
                weight_value: (value) => Number(value) || 0,
                cbm_value: (value) => Number(value) || 0,
                created_at: (value) => (value ? new Date(value) : null),
              },
            }),
          ],
        },
      ],
    }),
  },
};

/**
 * Shipment tracking template
 */
export const SHIPMENT_TRACKING_TEMPLATE: ExcelTemplate = {
  name: "Shipment Tracking",
  description: "Shipment tracking report with status updates",
  category: "Logistics",
  structure: {
    worksheets: [
      {
        name: "Shipments",
        tables: [],
        autoFilter: true,
        freezePane: { row: 1, col: 0 },
      },
      {
        name: "Tracking Events",
        tables: [],
        autoFilter: true,
        freezePane: { row: 1, col: 0 },
      },
    ],
    metadata: {
      title: "Shipment Tracking Report",
      author: "Shamwaa Logistics System",
      description: "Shipment status and tracking information",
    },
  },
  transformers: {
    shipments: (data: any[]) => createLogisticsReport({ shipments: data }),
  },
};

/**
 * Customer statement template
 */
export const CUSTOMER_STATEMENT_TEMPLATE: ExcelTemplate = {
  name: "Customer Statement",
  description: "Customer account statement with transactions",
  category: "Financial",
  structure: {
    worksheets: [
      {
        name: "Statement",
        tables: [],
        pageSetup: {
          orientation: "portrait",
        },
      },
    ],
    metadata: {
      title: "Customer Statement",
      author: "Shamwaa Logistics System",
      description: "Customer account statement with transaction history",
    },
  },
  transformers: {
    statement: (data: any) => ({
      worksheets: [
        {
          name: "Statement",
          tables: [
            // Customer info table
            {
              name: "Customer Information",
              headers: ["Field", "Value"],
              rows: [
                ["Customer Name", data.customer?.name || ""],
                ["Email", data.customer?.email || ""],
                ["Phone", data.customer?.phone || ""],
                ["Account Balance", data.balance || 0],
              ],
              headerStyle: EXCEL_THEMES.FINANCIAL.headerStyle,
              rowStyle: EXCEL_THEMES.FINANCIAL.dataStyle,
            },
            // Transactions table
            jsonToExcelTable(data.transactions || [], {
              tableName: "Transaction History",
              headerStyle: EXCEL_THEMES.FINANCIAL.headerStyle,
              dataStyle: EXCEL_THEMES.FINANCIAL.dataStyle,
              formatters: {
                amount: (value) => Number(value) || 0,
                created_at: (value) => (value ? new Date(value) : null),
              },
            }),
          ],
        },
      ],
    }),
  },
};

/**
 * Inventory report template
 */
export const INVENTORY_REPORT_TEMPLATE: ExcelTemplate = {
  name: "Inventory Report",
  description: "Comprehensive inventory report with stock levels",
  category: "Logistics",
  structure: {
    worksheets: [
      {
        name: "Inventory Summary",
        tables: [],
        autoFilter: true,
        freezePane: { row: 1, col: 0 },
      },
      {
        name: "Low Stock Items",
        tables: [],
        autoFilter: true,
      },
    ],
    metadata: {
      title: "Inventory Report",
      author: "Shamwaa Logistics System",
      description: "Current inventory levels and stock analysis",
    },
  },
  transformers: {
    inventory: (data: any) => ({
      worksheets: [
        {
          name: "Inventory Summary",
          tables: [
            jsonToExcelTable(data.items || [], {
              tableName: "Current Inventory",
              headerStyle: EXCEL_THEMES.LOGISTICS.headerStyle,
              dataStyle: EXCEL_THEMES.LOGISTICS.dataStyle,
              formatters: {
                quantity: (value) => Number(value) || 0,
                weight: (value) => Number(value) || 0,
                value: (value) => Number(value) || 0,
                last_updated: (value) => (value ? new Date(value) : null),
              },
            }),
          ],
        },
        {
          name: "Low Stock Items",
          tables: [
            jsonToExcelTable(data.lowStock || [], {
              tableName: "Items Requiring Attention",
              headerStyle: {
                ...EXCEL_THEMES.LOGISTICS.headerStyle,
                fill: { type: "pattern", pattern: "solid", fgColor: "#FF6B35" },
              },
              dataStyle: EXCEL_THEMES.LOGISTICS.dataStyle,
            }),
          ],
        },
      ],
    }),
  },
};

/**
 * Performance dashboard template
 */
export const PERFORMANCE_DASHBOARD_TEMPLATE: ExcelTemplate = {
  name: "Performance Dashboard",
  description: "KPI dashboard with metrics and trends",
  category: "Analytics",
  structure: {
    worksheets: [
      {
        name: "KPI Summary",
        tables: [],
        charts: [],
      },
      {
        name: "Detailed Metrics",
        tables: [],
        autoFilter: true,
        freezePane: { row: 1, col: 0 },
      },
    ],
    metadata: {
      title: "Performance Dashboard",
      author: "Shamwaa Logistics System",
      description: "Key performance indicators and operational metrics",
    },
  },
  transformers: {
    performance: (data: any) => ({
      worksheets: [
        {
          name: "KPI Summary",
          tables: [
            {
              name: "Key Performance Indicators",
              headers: ["Metric", "Current Value", "Target", "Status"],
              rows: Object.entries(data.kpis || {}).map(
                ([key, value]: [string, any]) => [
                  key
                    .replace(/_/g, " ")
                    .replace(/\b\w/g, (l) => l.toUpperCase()),
                  value.current || 0,
                  value.target || 0,
                  value.status || "Unknown",
                ]
              ),
              headerStyle: EXCEL_THEMES.DEFAULT.headerStyle,
              rowStyle: EXCEL_THEMES.DEFAULT.dataStyle,
            },
          ],
        },
        {
          name: "Detailed Metrics",
          tables: [
            jsonToExcelTable(data.metrics || [], {
              tableName: "Operational Metrics",
              headerStyle: EXCEL_THEMES.DEFAULT.headerStyle,
              dataStyle: EXCEL_THEMES.DEFAULT.dataStyle,
              formatters: {
                date: (value) => (value ? new Date(value) : null),
                value: (value) => Number(value) || 0,
              },
            }),
          ],
        },
      ],
    }),
  },
};

/**
 * Get template by name
 */
export function getTemplate(name: string): ExcelTemplate | undefined {
  const templates = [
    INVOICE_TEMPLATE,
    FINANCIAL_REPORT_TEMPLATE,
    CARGO_MANIFEST_TEMPLATE,
    SHIPMENT_TRACKING_TEMPLATE,
    CUSTOMER_STATEMENT_TEMPLATE,
    INVENTORY_REPORT_TEMPLATE,
    PERFORMANCE_DASHBOARD_TEMPLATE,
  ];

  return templates.find((template) => template.name === name);
}

/**
 * Get all available templates
 */
export function getAllTemplates(): ExcelTemplate[] {
  return [
    INVOICE_TEMPLATE,
    FINANCIAL_REPORT_TEMPLATE,
    CARGO_MANIFEST_TEMPLATE,
    SHIPMENT_TRACKING_TEMPLATE,
    CUSTOMER_STATEMENT_TEMPLATE,
    INVENTORY_REPORT_TEMPLATE,
    PERFORMANCE_DASHBOARD_TEMPLATE,
  ];
}

/**
 * Get templates by category
 */
export function getTemplatesByCategory(category: string): ExcelTemplate[] {
  return getAllTemplates().filter((template) => template.category === category);
}
