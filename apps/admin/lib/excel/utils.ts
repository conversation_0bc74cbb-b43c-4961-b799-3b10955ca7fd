/**
 * Excel utility functions for data transformation and formatting
 */

import type {
  ExcelDataType,
  ExcelTable,
  ExcelWorksheet,
  ExcelWorkbook,
  ExcelCellStyle,
  ExcelCell
} from './types';
import { EXCEL_THEMES, EXCEL_NUMBER_FORMATS } from './types';

/**
 * Transform JSON data to Excel table format
 */
export function jsonToExcelTable(
  data: Record<string, any>[],
  options?: {
    tableName?: string;
    excludeColumns?: string[];
    columnMapping?: Record<string, string>;
    headerStyle?: ExcelCellStyle;
    dataStyle?: ExcelCellStyle;
    formatters?: Record<string, (value: any) => ExcelDataType>;
  }
): ExcelTable {
  if (!data || data.length === 0) {
    return {
      headers: [],
      rows: []
    };
  }

  // Get all unique keys from the data
  const allKeys = Array.from(
    new Set(data.flatMap(item => Object.keys(item)))
  );

  // Filter out excluded columns
  const filteredKeys = options?.excludeColumns 
    ? allKeys.filter(key => !options.excludeColumns!.includes(key))
    : allKeys;

  // Create headers with optional mapping
  const headers = filteredKeys.map(key => 
    options?.columnMapping?.[key] || formatHeaderName(key)
  );

  // Transform data rows
  const rows = data.map(item => 
    filteredKeys.map(key => {
      const value = item[key];
      
      // Apply custom formatter if available
      if (options?.formatters?.[key]) {
        return options.formatters[key](value);
      }
      
      // Apply default formatting
      return formatCellValue(value);
    })
  );

  return {
    name: options?.tableName,
    headers,
    rows,
    headerStyle: options?.headerStyle || EXCEL_THEMES.DEFAULT.headerStyle,
    rowStyle: options?.dataStyle || EXCEL_THEMES.DEFAULT.dataStyle
  };
}

/**
 * Format header names (convert camelCase/snake_case to Title Case)
 */
export function formatHeaderName(key: string): string {
  return key
    .replace(/([A-Z])/g, ' $1') // Add space before capital letters
    .replace(/_/g, ' ') // Replace underscores with spaces
    .replace(/\b\w/g, l => l.toUpperCase()) // Capitalize first letter of each word
    .trim();
}

/**
 * Format cell values based on type
 */
export function formatCellValue(value: any): ExcelDataType {
  if (value === null || value === undefined) {
    return null;
  }
  
  if (typeof value === 'string') {
    // Try to parse dates
    if (isDateString(value)) {
      return new Date(value);
    }
    return value;
  }
  
  if (typeof value === 'number' || typeof value === 'boolean') {
    return value;
  }
  
  if (value instanceof Date) {
    return value;
  }
  
  // Convert objects/arrays to JSON strings
  if (typeof value === 'object') {
    return JSON.stringify(value);
  }
  
  return String(value);
}

/**
 * Check if string is a valid date
 */
function isDateString(value: string): boolean {
  const date = new Date(value);
  return !isNaN(date.getTime()) && value.match(/^\d{4}-\d{2}-\d{2}/) !== null;
}

/**
 * Create financial report structure
 */
export function createFinancialReport(
  data: {
    summary: Record<string, number>;
    transactions: Record<string, any>[];
    totals: Record<string, number>;
  },
  options?: {
    currency?: string;
    includeSummary?: boolean;
    includeCharts?: boolean;
  }
): ExcelWorkbook {
  const worksheets: ExcelWorksheet[] = [];

  // Summary worksheet
  if (options?.includeSummary !== false && data.summary) {
    const summaryTable = createSummaryTable(data.summary, options?.currency);
    worksheets.push({
      name: 'Summary',
      tables: [summaryTable]
    });
  }

  // Transactions worksheet
  if (data.transactions && data.transactions.length > 0) {
    const transactionsTable = jsonToExcelTable(data.transactions, {
      tableName: 'Transaction Details',
      headerStyle: EXCEL_THEMES.FINANCIAL.headerStyle,
      dataStyle: EXCEL_THEMES.FINANCIAL.dataStyle,
      formatters: {
        amount: (value) => Number(value) || 0,
        created_at: (value) => value ? new Date(value) : null,
        updated_at: (value) => value ? new Date(value) : null
      }
    });
    
    worksheets.push({
      name: 'Transactions',
      tables: [transactionsTable],
      autoFilter: true,
      freezePane: { row: 1, col: 0 }
    });
  }

  return {
    worksheets,
    metadata: {
      title: 'Financial Report',
      author: 'Shamwaa Logistics System',
      created: new Date(),
      description: 'Generated financial report with summary and transaction details'
    }
  };
}

/**
 * Create summary table from key-value data
 */
function createSummaryTable(
  summary: Record<string, number>,
  currency: string = 'USD'
): ExcelTable {
  const headers = ['Metric', 'Value'];
  const rows = Object.entries(summary).map(([key, value]) => [
    formatHeaderName(key),
    value
  ]);

  return {
    name: 'Financial Summary',
    headers,
    rows,
    headerStyle: EXCEL_THEMES.FINANCIAL.headerStyle,
    rowStyle: {
      ...EXCEL_THEMES.FINANCIAL.dataStyle,
      numFmt: currency === 'USD' ? EXCEL_NUMBER_FORMATS.CURRENCY_USD : EXCEL_NUMBER_FORMATS.CURRENCY_TZS
    }
  };
}

/**
 * Create logistics report structure
 */
export function createLogisticsReport(
  data: {
    shipments?: Record<string, any>[];
    cargo?: Record<string, any>[];
    batches?: Record<string, any>[];
    freights?: Record<string, any>[];
  }
): ExcelWorkbook {
  const worksheets: ExcelWorksheet[] = [];

  // Shipments worksheet
  if (data.shipments && data.shipments.length > 0) {
    const shipmentsTable = jsonToExcelTable(data.shipments, {
      tableName: 'Shipments Overview',
      headerStyle: EXCEL_THEMES.LOGISTICS.headerStyle,
      dataStyle: EXCEL_THEMES.LOGISTICS.dataStyle,
      excludeColumns: ['metadata', 'attachments'],
      formatters: {
        created_at: (value) => value ? new Date(value) : null,
        updated_at: (value) => value ? new Date(value) : null
      }
    });
    
    worksheets.push({
      name: 'Shipments',
      tables: [shipmentsTable],
      autoFilter: true,
      freezePane: { row: 1, col: 0 }
    });
  }

  // Cargo worksheet
  if (data.cargo && data.cargo.length > 0) {
    const cargoTable = jsonToExcelTable(data.cargo, {
      tableName: 'Cargo Inventory',
      headerStyle: EXCEL_THEMES.LOGISTICS.headerStyle,
      dataStyle: EXCEL_THEMES.LOGISTICS.dataStyle,
      formatters: {
        weight_value: (value) => Number(value) || 0,
        cbm_value: (value) => Number(value) || 0,
        created_at: (value) => value ? new Date(value) : null
      }
    });
    
    worksheets.push({
      name: 'Cargo',
      tables: [cargoTable],
      autoFilter: true,
      freezePane: { row: 1, col: 0 }
    });
  }

  // Batches worksheet
  if (data.batches && data.batches.length > 0) {
    const batchesTable = jsonToExcelTable(data.batches, {
      tableName: 'Batch Summary',
      headerStyle: EXCEL_THEMES.LOGISTICS.headerStyle,
      dataStyle: EXCEL_THEMES.LOGISTICS.dataStyle,
      formatters: {
        created_at: (value) => value ? new Date(value) : null,
        updated_at: (value) => value ? new Date(value) : null
      }
    });
    
    worksheets.push({
      name: 'Batches',
      tables: [batchesTable],
      autoFilter: true,
      freezePane: { row: 1, col: 0 }
    });
  }

  return {
    worksheets,
    metadata: {
      title: 'Logistics Report',
      author: 'Shamwaa Logistics System',
      created: new Date(),
      description: 'Comprehensive logistics report with shipments, cargo, and batch data'
    }
  };
}

/**
 * Create invoice export structure
 */
export function createInvoiceExport(
  invoices: Record<string, any>[],
  options?: {
    includeLineItems?: boolean;
    currency?: string;
  }
): ExcelWorkbook {
  const worksheets: ExcelWorksheet[] = [];

  // Main invoices worksheet
  const invoicesTable = jsonToExcelTable(invoices, {
    tableName: 'Invoice Summary',
    headerStyle: EXCEL_THEMES.FINANCIAL.headerStyle,
    dataStyle: EXCEL_THEMES.FINANCIAL.dataStyle,
    excludeColumns: ['line_items', 'attachments'],
    formatters: {
      total: (value) => Number(value) || 0,
      subtotal: (value) => Number(value) || 0,
      created_at: (value) => value ? new Date(value) : null,
      due_at: (value) => value ? new Date(value) : null
    }
  });

  worksheets.push({
    name: 'Invoices',
    tables: [invoicesTable],
    autoFilter: true,
    freezePane: { row: 1, col: 0 }
  });

  // Line items worksheet (if requested)
  if (options?.includeLineItems) {
    const lineItems: any[] = [];
    
    invoices.forEach(invoice => {
      if (invoice.line_items && Array.isArray(invoice.line_items)) {
        invoice.line_items.forEach((item: any) => {
          lineItems.push({
            invoice_number: invoice.inv_number,
            invoice_id: invoice.id,
            ...item
          });
        });
      }
    });

    if (lineItems.length > 0) {
      const lineItemsTable = jsonToExcelTable(lineItems, {
        tableName: 'Invoice Line Items',
        headerStyle: EXCEL_THEMES.FINANCIAL.headerStyle,
        dataStyle: EXCEL_THEMES.FINANCIAL.dataStyle,
        formatters: {
          unitPrice: (value) => Number(value) || 0,
          total: (value) => Number(value) || 0,
          quantity: (value) => Number(value) || 0
        }
      });

      worksheets.push({
        name: 'Line Items',
        tables: [lineItemsTable],
        autoFilter: true,
        freezePane: { row: 1, col: 0 }
      });
    }
  }

  return {
    worksheets,
    metadata: {
      title: 'Invoice Export',
      author: 'Shamwaa Logistics System',
      created: new Date(),
      description: 'Invoice data export with summary and line item details'
    }
  };
}

/**
 * Utility to merge multiple data sources into a single workbook
 */
export function mergeDataSources(
  sources: Array<{
    name: string;
    data: Record<string, any>[];
    options?: Parameters<typeof jsonToExcelTable>[1];
  }>
): ExcelWorkbook {
  const worksheets = sources.map(source => ({
    name: source.name,
    tables: [jsonToExcelTable(source.data, source.options)],
    autoFilter: true,
    freezePane: { row: 1, col: 0 }
  }));

  return {
    worksheets,
    metadata: {
      title: 'Multi-Source Data Export',
      author: 'Shamwaa Logistics System',
      created: new Date(),
      description: 'Combined data export from multiple sources'
    }
  };
}
