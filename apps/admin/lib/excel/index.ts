/**
 * Excel Generation Module
 * Dynamic composable Excel document generator for Shamwaa Logistics
 */

// Core service
export { ExcelService } from "./service";

// Types and interfaces
export type {
  ExcelDataType,
  ExcelAlignment,
  ExcelVerticalAlignment,
  ExcelBorderStyle,
  ExcelCellStyle,
  ExcelCell,
  ExcelRow,
  ExcelColumn,
  ExcelTable,
  ExcelChart,
  ExcelWorksheet,
  ExcelWorkbook,
  ExcelGenerationOptions,
  ExcelTemplate,
  ExcelExportResult,
  ExcelImportResult,
} from "./types";

export { EXCEL_THEMES, EXCEL_NUMBER_FORMATS } from "./types";

// Utility functions
export {
  jsonToExcelTable,
  formatHeaderName,
  formatCellValue,
  createFinancialReport,
  createLogisticsReport,
  createInvoiceExport,
  mergeDataSources,
} from "./utils";

// Templates
export {
  INVOICE_TEMPLATE,
  FINANCIAL_REPORT_TEMPLATE,
  CARGO_MANIFEST_TEMPLATE,
  SHIPMENT_TRACKING_TEMPLATE,
  CUSTOMER_STATEMENT_TEMPLATE,
  INVENTORY_REPORT_TEMPLATE,
  PERFORMANCE_DASHBOARD_TEMPLATE,
  getTemplate,
  getAllTemplates,
  getTemplatesByCategory,
} from "./templates";

import { ExcelService } from "./service";

// Main Excel generator class
export class ExcelGenerator {
  private service: ExcelService;

  constructor() {
    this.service = new ExcelService();
  }

  /**
   * Generate Excel from JSON data using a template
   */
  async generateFromTemplate(
    templateName: string,
    data: any,
    options?: ExcelGenerationOptions
  ): Promise<ExcelExportResult> {
    const template = await import("./templates").then((m) =>
      m.getTemplate(templateName)
    );

    if (!template) {
      return {
        success: false,
        error: `Template '${templateName}' not found`,
      };
    }

    // Apply template transformers
    let workbookData = template.structure;

    if (template.transformers) {
      for (const [key, transformer] of Object.entries(template.transformers)) {
        if (data[key]) {
          const transformedData = transformer(data[key]);
          workbookData = { ...workbookData, ...transformedData };
        }
      }
    }

    return this.service.createWorkbook(workbookData, {
      filename:
        options?.filename ||
        `${template.name.toLowerCase().replace(/\s+/g, "_")}_${new Date().toISOString().split("T")[0]}.xlsx`,
      ...options,
    });
  }

  /**
   * Generate Excel from raw JSON data
   */
  async generateFromJSON(
    data: Record<string, any>[],
    options?: {
      sheetName?: string;
      filename?: string;
      theme?: keyof typeof EXCEL_THEMES;
      excludeColumns?: string[];
      columnMapping?: Record<string, string>;
      formatters?: Record<string, (value: any) => any>;
    }
  ): Promise<ExcelExportResult> {
    const { jsonToExcelTable } = await import("./utils");
    const { EXCEL_THEMES } = await import("./types");

    const theme = options?.theme
      ? EXCEL_THEMES[options.theme]
      : EXCEL_THEMES.DEFAULT;

    const table = jsonToExcelTable(data, {
      headerStyle: theme.headerStyle,
      dataStyle: theme.dataStyle,
      excludeColumns: options?.excludeColumns,
      columnMapping: options?.columnMapping,
      formatters: options?.formatters,
    });

    const workbook = {
      worksheets: [
        {
          name: options?.sheetName || "Data",
          tables: [table],
          autoFilter: true,
          freezePane: { row: 1, col: 0 },
        },
      ],
      metadata: {
        title: "Data Export",
        author: "Shamwaa Logistics System",
        created: new Date(),
      },
    };

    return this.service.createWorkbook(workbook, {
      filename:
        options?.filename ||
        `data_export_${new Date().toISOString().split("T")[0]}.xlsx`,
    });
  }

  /**
   * Generate multi-sheet Excel from multiple data sources
   */
  async generateMultiSheet(
    sources: Array<{
      name: string;
      data: Record<string, any>[];
      options?: {
        theme?: keyof typeof EXCEL_THEMES;
        excludeColumns?: string[];
        columnMapping?: Record<string, string>;
        formatters?: Record<string, (value: any) => any>;
      };
    }>,
    options?: ExcelGenerationOptions
  ): Promise<ExcelExportResult> {
    const { mergeDataSources } = await import("./utils");
    const { EXCEL_THEMES } = await import("./types");

    const processedSources = sources.map((source) => {
      const theme = source.options?.theme
        ? EXCEL_THEMES[source.options.theme]
        : EXCEL_THEMES.DEFAULT;

      return {
        name: source.name,
        data: source.data,
        options: {
          headerStyle: theme.headerStyle,
          dataStyle: theme.dataStyle,
          excludeColumns: source.options?.excludeColumns,
          columnMapping: source.options?.columnMapping,
          formatters: source.options?.formatters,
        },
      };
    });

    const workbook = mergeDataSources(processedSources);

    return this.service.createWorkbook(workbook, {
      filename:
        options?.filename ||
        `multi_sheet_export_${new Date().toISOString().split("T")[0]}.xlsx`,
      ...options,
    });
  }

  /**
   * Generate simple table export
   */
  async generateSimpleTable(
    headers: string[],
    data: any[][],
    options?: {
      sheetName?: string;
      filename?: string;
      includeHeaders?: boolean;
    }
  ): Promise<ExcelExportResult> {
    return this.service.createSimpleTable(headers, data, options);
  }

  /**
   * Import Excel file and parse data
   */
  async importExcel(file: File | ArrayBuffer): Promise<ExcelImportResult> {
    return this.service.importExcel(file);
  }

  /**
   * Download Excel file in browser
   */
  downloadExcel(buffer: ArrayBuffer, filename: string): void {
    this.service.downloadExcel(buffer, filename);
  }

  /**
   * Generate and download Excel in one step
   */
  async generateAndDownload(
    data: any,
    type: "template" | "json" | "multisheet" | "simple",
    options: any = {}
  ): Promise<boolean> {
    try {
      let result: ExcelExportResult;

      switch (type) {
        case "template":
          result = await this.generateFromTemplate(
            options.templateName,
            data,
            options
          );
          break;
        case "json":
          result = await this.generateFromJSON(data, options);
          break;
        case "multisheet":
          result = await this.generateMultiSheet(data, options);
          break;
        case "simple":
          result = await this.generateSimpleTable(
            options.headers,
            data,
            options
          );
          break;
        default:
          throw new Error(`Unknown generation type: ${type}`);
      }

      if (result.success && result.data) {
        this.downloadExcel(result.data.buffer, result.data.filename);
        return true;
      } else {
        console.error("Excel generation failed:", result.error);
        return false;
      }
    } catch (error) {
      console.error("Excel generation error:", error);
      return false;
    }
  }
}

// Convenience functions for quick exports
export const excelGenerator = new ExcelGenerator();

/**
 * Quick export functions
 */
export const quickExport = {
  /**
   * Export JSON data as Excel
   */
  json: async (data: Record<string, any>[], filename?: string) => {
    return excelGenerator.generateAndDownload(data, "json", { filename });
  },

  /**
   * Export using a template
   */
  template: async (templateName: string, data: any, filename?: string) => {
    return excelGenerator.generateAndDownload(data, "template", {
      templateName,
      filename,
    });
  },

  /**
   * Export multiple sheets
   */
  multiSheet: async (sources: any[], filename?: string) => {
    return excelGenerator.generateAndDownload(sources, "multisheet", {
      filename,
    });
  },

  /**
   * Export simple table
   */
  table: async (headers: string[], data: any[][], filename?: string) => {
    return excelGenerator.generateAndDownload(data, "simple", {
      headers,
      filename,
    });
  },
};

// Default export
export default ExcelGenerator;
