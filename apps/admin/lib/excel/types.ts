/**
 * Types and interfaces for Excel generation module
 */

export type ExcelDataType = 
  | string 
  | number 
  | boolean 
  | Date 
  | null 
  | undefined;

export type ExcelAlignment = 
  | 'left' 
  | 'center' 
  | 'right' 
  | 'justify';

export type ExcelVerticalAlignment = 
  | 'top' 
  | 'middle' 
  | 'bottom';

export type ExcelBorderStyle = 
  | 'thin' 
  | 'medium' 
  | 'thick' 
  | 'dotted' 
  | 'dashed' 
  | 'double';

export interface ExcelCellStyle {
  // Font styling
  font?: {
    name?: string;
    size?: number;
    bold?: boolean;
    italic?: boolean;
    underline?: boolean;
    color?: string;
  };
  
  // Cell alignment
  alignment?: {
    horizontal?: ExcelAlignment;
    vertical?: ExcelVerticalAlignment;
    wrapText?: boolean;
    textRotation?: number;
  };
  
  // Cell borders
  border?: {
    top?: { style: ExcelBorderStyle; color?: string };
    bottom?: { style: ExcelBorderStyle; color?: string };
    left?: { style: ExcelBorderStyle; color?: string };
    right?: { style: ExcelBorderStyle; color?: string };
  };
  
  // Cell fill/background
  fill?: {
    type?: 'pattern' | 'gradient';
    pattern?: 'solid' | 'darkGray' | 'mediumGray' | 'lightGray';
    fgColor?: string;
    bgColor?: string;
  };
  
  // Number formatting
  numFmt?: string;
}

export interface ExcelCell {
  value: ExcelDataType;
  style?: ExcelCellStyle;
  formula?: string;
  hyperlink?: string;
  comment?: string;
}

export interface ExcelRow {
  cells: (ExcelCell | ExcelDataType)[];
  height?: number;
  style?: ExcelCellStyle;
}

export interface ExcelColumn {
  header?: string;
  key?: string;
  width?: number;
  style?: ExcelCellStyle;
}

export interface ExcelTable {
  name?: string;
  headers: string[];
  rows: ExcelDataType[][];
  headerStyle?: ExcelCellStyle;
  rowStyle?: ExcelCellStyle;
  alternatingRowStyle?: ExcelCellStyle;
  totalRow?: ExcelDataType[];
  totalRowStyle?: ExcelCellStyle;
}

export interface ExcelChart {
  type: 'line' | 'bar' | 'pie' | 'scatter' | 'area';
  title?: string;
  data: {
    categories: string[];
    series: {
      name: string;
      values: number[];
      color?: string;
    }[];
  };
  position?: {
    row: number;
    col: number;
    width?: number;
    height?: number;
  };
}

export interface ExcelWorksheet {
  name: string;
  data?: ExcelRow[];
  tables?: ExcelTable[];
  charts?: ExcelChart[];
  columns?: ExcelColumn[];
  freezePane?: { row: number; col: number };
  autoFilter?: boolean;
  protection?: {
    password?: string;
    selectLockedCells?: boolean;
    selectUnlockedCells?: boolean;
  };
  pageSetup?: {
    orientation?: 'portrait' | 'landscape';
    paperSize?: string;
    margins?: {
      top?: number;
      bottom?: number;
      left?: number;
      right?: number;
    };
  };
}

export interface ExcelWorkbook {
  worksheets: ExcelWorksheet[];
  metadata?: {
    title?: string;
    subject?: string;
    author?: string;
    company?: string;
    description?: string;
    keywords?: string[];
    created?: Date;
    modified?: Date;
  };
  protection?: {
    workbookPassword?: string;
    lockStructure?: boolean;
    lockWindows?: boolean;
  };
}

export interface ExcelGenerationOptions {
  filename?: string;
  format?: 'xlsx' | 'csv' | 'html';
  compression?: boolean;
  autoSave?: boolean;
  template?: string;
  locale?: string;
  timezone?: string;
}

export interface ExcelTemplate {
  name: string;
  description?: string;
  category: string;
  structure: ExcelWorkbook;
  variables?: Record<string, any>;
  transformers?: Record<string, (data: any) => any>;
}

export interface ExcelExportResult {
  success: boolean;
  data?: {
    buffer: ArrayBuffer;
    filename: string;
    mimeType: string;
    size: number;
  };
  error?: string;
  warnings?: string[];
}

export interface ExcelImportResult {
  success: boolean;
  data?: {
    worksheets: {
      name: string;
      data: Record<string, ExcelDataType>[];
      headers: string[];
      rowCount: number;
    }[];
    metadata?: Record<string, any>;
  };
  error?: string;
  warnings?: string[];
}

// Predefined style themes
export const EXCEL_THEMES = {
  DEFAULT: {
    headerStyle: {
      font: { bold: true, color: '#FFFFFF' },
      fill: { type: 'pattern' as const, pattern: 'solid' as const, fgColor: '#4472C4' },
      alignment: { horizontal: 'center' as const },
      border: {
        top: { style: 'thin' as const },
        bottom: { style: 'thin' as const },
        left: { style: 'thin' as const },
        right: { style: 'thin' as const }
      }
    },
    dataStyle: {
      border: {
        top: { style: 'thin' as const },
        bottom: { style: 'thin' as const },
        left: { style: 'thin' as const },
        right: { style: 'thin' as const }
      }
    }
  },
  FINANCIAL: {
    headerStyle: {
      font: { bold: true, color: '#FFFFFF' },
      fill: { type: 'pattern' as const, pattern: 'solid' as const, fgColor: '#2E7D32' },
      alignment: { horizontal: 'center' as const },
      border: {
        top: { style: 'medium' as const },
        bottom: { style: 'medium' as const },
        left: { style: 'thin' as const },
        right: { style: 'thin' as const }
      }
    },
    dataStyle: {
      border: {
        top: { style: 'thin' as const },
        bottom: { style: 'thin' as const },
        left: { style: 'thin' as const },
        right: { style: 'thin' as const }
      }
    },
    currencyStyle: {
      numFmt: '$#,##0.00',
      alignment: { horizontal: 'right' as const }
    }
  },
  LOGISTICS: {
    headerStyle: {
      font: { bold: true, color: '#FFFFFF' },
      fill: { type: 'pattern' as const, pattern: 'solid' as const, fgColor: '#FF6B35' },
      alignment: { horizontal: 'center' as const },
      border: {
        top: { style: 'thin' as const },
        bottom: { style: 'thin' as const },
        left: { style: 'thin' as const },
        right: { style: 'thin' as const }
      }
    },
    dataStyle: {
      border: {
        top: { style: 'thin' as const },
        bottom: { style: 'thin' as const },
        left: { style: 'thin' as const },
        right: { style: 'thin' as const }
      }
    }
  }
} as const;

// Common number formats
export const EXCEL_NUMBER_FORMATS = {
  GENERAL: 'General',
  NUMBER: '#,##0',
  DECIMAL: '#,##0.00',
  CURRENCY_USD: '$#,##0.00',
  CURRENCY_TZS: '#,##0.00" TZS"',
  PERCENTAGE: '0.00%',
  DATE: 'mm/dd/yyyy',
  DATETIME: 'mm/dd/yyyy hh:mm:ss',
  TIME: 'hh:mm:ss',
  ACCOUNTING: '_($* #,##0.00_);_($* (#,##0.00);_($* "-"??_);_(@_)',
  WEIGHT: '#,##0.00" kg"',
  VOLUME: '#,##0.00" m³"'
} as const;
