/**
 * Excel Generation Service
 * Core service for creating Excel documents from JSON data
 */

import * as XLSX from 'xlsx';
import type {
  ExcelWorkbook,
  ExcelWorksheet,
  ExcelTable,
  ExcelCell,
  ExcelCellStyle,
  ExcelGenerationOptions,
  ExcelExportResult,
  ExcelImportResult,
  ExcelDataType,
  ExcelRow
} from './types';

export class ExcelService {
  private workbook: XLSX.WorkBook;
  
  constructor() {
    this.workbook = XLSX.utils.book_new();
  }

  /**
   * Create a new Excel workbook from structured data
   */
  async createWorkbook(data: ExcelWorkbook, options?: ExcelGenerationOptions): Promise<ExcelExportResult> {
    try {
      // Reset workbook
      this.workbook = XLSX.utils.book_new();
      
      // Set workbook metadata
      if (data.metadata) {
        this.workbook.Props = {
          Title: data.metadata.title,
          Subject: data.metadata.subject,
          Author: data.metadata.author,
          Company: data.metadata.company,
          Comments: data.metadata.description,
          Keywords: data.metadata.keywords?.join(', '),
          CreatedDate: data.metadata.created,
          ModifiedDate: data.metadata.modified
        };
      }

      // Process each worksheet
      for (const worksheetData of data.worksheets) {
        await this.addWorksheet(worksheetData);
      }

      // Generate buffer
      const buffer = this.generateBuffer(options?.format || 'xlsx');
      const filename = options?.filename || `export_${new Date().toISOString().split('T')[0]}.xlsx`;
      
      return {
        success: true,
        data: {
          buffer,
          filename,
          mimeType: this.getMimeType(options?.format || 'xlsx'),
          size: buffer.byteLength
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create workbook'
      };
    }
  }

  /**
   * Add a worksheet to the workbook
   */
  private async addWorksheet(worksheetData: ExcelWorksheet): Promise<void> {
    let worksheet: XLSX.WorkSheet;

    if (worksheetData.tables && worksheetData.tables.length > 0) {
      // Create worksheet from tables
      worksheet = this.createWorksheetFromTables(worksheetData.tables);
    } else if (worksheetData.data && worksheetData.data.length > 0) {
      // Create worksheet from row data
      worksheet = this.createWorksheetFromRows(worksheetData.data);
    } else {
      // Create empty worksheet
      worksheet = XLSX.utils.aoa_to_sheet([]);
    }

    // Apply column settings
    if (worksheetData.columns) {
      const colWidths = worksheetData.columns.map(col => ({ wch: col.width || 15 }));
      worksheet['!cols'] = colWidths;
    }

    // Apply freeze panes
    if (worksheetData.freezePane) {
      worksheet['!freeze'] = {
        xSplit: worksheetData.freezePane.col,
        ySplit: worksheetData.freezePane.row
      };
    }

    // Apply auto filter
    if (worksheetData.autoFilter && worksheet['!ref']) {
      worksheet['!autofilter'] = { ref: worksheet['!ref'] };
    }

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(this.workbook, worksheet, worksheetData.name);
  }

  /**
   * Create worksheet from table data
   */
  private createWorksheetFromTables(tables: ExcelTable[]): XLSX.WorkSheet {
    const allData: any[][] = [];
    let currentRow = 0;

    for (const table of tables) {
      // Add table name if provided
      if (table.name) {
        allData.push([table.name]);
        allData.push([]); // Empty row
        currentRow += 2;
      }

      // Add headers
      allData.push(table.headers);
      currentRow++;

      // Add data rows
      for (const row of table.rows) {
        allData.push(row);
        currentRow++;
      }

      // Add total row if provided
      if (table.totalRow) {
        allData.push(table.totalRow);
        currentRow++;
      }

      // Add spacing between tables
      allData.push([]);
      currentRow++;
    }

    return XLSX.utils.aoa_to_sheet(allData);
  }

  /**
   * Create worksheet from row data
   */
  private createWorksheetFromRows(rows: ExcelRow[]): XLSX.WorkSheet {
    const data: any[][] = [];

    for (const row of rows) {
      const rowData: any[] = [];
      for (const cell of row.cells) {
        if (typeof cell === 'object' && cell !== null && 'value' in cell) {
          rowData.push((cell as ExcelCell).value);
        } else {
          rowData.push(cell);
        }
      }
      data.push(rowData);
    }

    return XLSX.utils.aoa_to_sheet(data);
  }

  /**
   * Generate buffer from workbook
   */
  private generateBuffer(format: string): ArrayBuffer {
    const writeOptions: XLSX.WritingOptions = {
      bookType: format as XLSX.BookType,
      type: 'array',
      compression: true
    };

    return XLSX.write(this.workbook, writeOptions);
  }

  /**
   * Get MIME type for format
   */
  private getMimeType(format: string): string {
    switch (format) {
      case 'xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case 'csv':
        return 'text/csv';
      case 'html':
        return 'text/html';
      default:
        return 'application/octet-stream';
    }
  }

  /**
   * Import Excel file and parse data
   */
  async importExcel(file: File | ArrayBuffer): Promise<ExcelImportResult> {
    try {
      let buffer: ArrayBuffer;
      
      if (file instanceof File) {
        buffer = await file.arrayBuffer();
      } else {
        buffer = file;
      }

      const workbook = XLSX.read(buffer, { type: 'array' });
      const worksheets: any[] = [];

      for (const sheetName of workbook.SheetNames) {
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        if (jsonData.length > 0) {
          const headers = jsonData[0] as string[];
          const rows = jsonData.slice(1);
          
          const data = rows.map(row => {
            const rowObj: Record<string, ExcelDataType> = {};
            headers.forEach((header, index) => {
              rowObj[header] = (row as any[])[index] || null;
            });
            return rowObj;
          });

          worksheets.push({
            name: sheetName,
            data,
            headers,
            rowCount: rows.length
          });
        }
      }

      return {
        success: true,
        data: {
          worksheets,
          metadata: workbook.Props
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to import Excel file'
      };
    }
  }

  /**
   * Create a simple table export
   */
  async createSimpleTable(
    headers: string[],
    data: any[][],
    options?: {
      sheetName?: string;
      filename?: string;
      includeHeaders?: boolean;
    }
  ): Promise<ExcelExportResult> {
    const tableData = options?.includeHeaders !== false ? [headers, ...data] : data;
    const worksheet = XLSX.utils.aoa_to_sheet(tableData);
    
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, options?.sheetName || 'Sheet1');
    
    const buffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const filename = options?.filename || `table_export_${new Date().toISOString().split('T')[0]}.xlsx`;
    
    return {
      success: true,
      data: {
        buffer,
        filename,
        mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        size: buffer.byteLength
      }
    };
  }

  /**
   * Download Excel file in browser
   */
  downloadExcel(buffer: ArrayBuffer, filename: string): void {
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
}
