/**
 * Invoice Templates Module
 *
 * This module provides a complete solution for generating release authorization
 * documents and invoices using the PDF generator package.
 */

// Core generator functions
import {
  ReleaseAuthorizationData,
  InvoiceTemplateOptions,
  PersonnelInfo,
} from "../types/invoice-template";

import { convertShipmentToReleaseData } from "../release-document-generator";

export {
  generateReleaseAuthorizationInvoice,
  downloadPDF,
  generateDocumentNumber,
} from "../invoice-template-generator";

// Integration utilities
export {
  generateReleaseDocument,
  generateBatchReleaseDocument,
  previewReleaseData,
  convertShipmentToReleaseData,
} from "../release-document-generator";

// Type definitions
export type {
  ReleaseAuthorizationData,
  InvoiceTemplateOptions,
  GenerateInvoiceResult,
  CompanyInfo,
  PersonnelInfo,
  FreightInfo,
  BatchInfo,
  CargoItem,
  PaymentInfo,
  QRCodeData,
  DocumentMetadata,
  DocumentAttachment,
} from "../types/invoice-template";
import { ShipmentForRelease } from "../logistics/operations/release-authorizations";

// Re-export logistics types for convenience
export type { ShipmentForRelease } from "../logistics/operations/release-authorizations";

/**
 * Default template configurations
 */
export const DEFAULT_TEMPLATE_OPTIONS: InvoiceTemplateOptions = {
  format: "A4",
  orientation: "portrait",
  primaryColor: "#1e40af",
  secondaryColor: "#64748b",
  fontFamily: "helvetica",
  fontSize: 10,
  showCompanyLogo: true,
  showCargoImages: false,
  showDetailedCargoInfo: true,
  showPaymentDetails: true,
  qrCodeSize: 150,
  qrCodePosition: "bottom-right",
  language: "en",
};

/**
 * Template presets for different document types
 */
export const TEMPLATE_PRESETS = {
  STANDARD_RELEASE: {
    ...DEFAULT_TEMPLATE_OPTIONS,
    showPaymentDetails: true,
    showDetailedCargoInfo: true,
    qrCodeSize: 150,
  },

  QUICK_RELEASE: {
    ...DEFAULT_TEMPLATE_OPTIONS,
    showPaymentDetails: false,
    showDetailedCargoInfo: false,
    fontSize: 12,
    qrCodeSize: 120,
  },

  DETAILED_MANIFEST: {
    ...DEFAULT_TEMPLATE_OPTIONS,
    showCargoImages: true,
    showDetailedCargoInfo: true,
    showPaymentDetails: true,
    fontSize: 9,
    qrCodeSize: 180,
  },

  BATCH_RELEASE: {
    ...DEFAULT_TEMPLATE_OPTIONS,
    showPaymentDetails: true,
    showDetailedCargoInfo: true,
    fontSize: 8,
    qrCodeSize: 140,
  },
} as const;

/**
 * Utility function to get template preset
 */
export function getTemplatePreset(
  presetName: keyof typeof TEMPLATE_PRESETS
): InvoiceTemplateOptions {
  return { ...TEMPLATE_PRESETS[presetName] };
}

/**
 * Quick generation functions with presets
 */
export async function generateStandardReleaseDocument(
  shipment: ShipmentForRelease,
  personnelInfo: PersonnelInfo,
  options?: {
    additionalData?: Parameters<typeof convertShipmentToReleaseData>[2];
    autoDownload?: boolean;
  }
) {
  const { generateReleaseDocument } = await import(
    "../release-document-generator"
  );

  return generateReleaseDocument(shipment, personnelInfo, {
    ...options,
    templateOptions: TEMPLATE_PRESETS.STANDARD_RELEASE,
  });
}

export async function generateQuickReleaseDocument(
  shipment: ShipmentForRelease,
  personnelInfo: PersonnelInfo,
  options?: {
    additionalData?: Parameters<typeof convertShipmentToReleaseData>[2];
    autoDownload?: boolean;
  }
) {
  const { generateReleaseDocument } = await import(
    "../release-document-generator"
  );

  return generateReleaseDocument(shipment, personnelInfo, {
    ...options,
    templateOptions: TEMPLATE_PRESETS.QUICK_RELEASE,
  });
}

export async function generateDetailedManifest(
  shipment: ShipmentForRelease,
  personnelInfo: PersonnelInfo,
  options?: {
    additionalData?: Parameters<typeof convertShipmentToReleaseData>[2];
    autoDownload?: boolean;
  }
) {
  const { generateReleaseDocument } = await import(
    "../release-document-generator"
  );

  return generateReleaseDocument(shipment, personnelInfo, {
    ...options,
    templateOptions: TEMPLATE_PRESETS.DETAILED_MANIFEST,
  });
}

/**
 * Validation utilities
 */
export function validateReleaseData(data: Partial<ReleaseAuthorizationData>): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!data.metadata?.documentNumber) {
    errors.push("Document number is required");
  }

  if (!data.personnel?.authorizedBy) {
    errors.push("Authorized personnel information is required");
  }

  if (!data.freight?.id) {
    errors.push("Freight information is required");
  }

  if (!data.batch?.id) {
    errors.push("Batch information is required");
  }

  if (!data.cargoItems || data.cargoItems.length === 0) {
    errors.push("At least one cargo item is required");
  }

  if (!data.qrCode?.releaseCode) {
    errors.push("Release code is required for QR generation");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Helper function to create personnel info from user session
 */
export function createPersonnelInfo(user: {
  id: string;
  name: string;
  role?: { name: string; department?: { name: string } };
}): PersonnelInfo {
  return {
    authorizedBy: user.name,
    authorizedById: user.id,
    department: user.role?.department?.name || "Operations",
    role: user.role?.name || "Staff",
  };
}

/**
 * Error handling utilities
 */
export class InvoiceTemplateError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = "InvoiceTemplateError";
  }
}

export function handleTemplateError(error: unknown): {
  success: false;
  error: string;
  code?: string;
} {
  if (error instanceof InvoiceTemplateError) {
    return {
      success: false,
      error: error.message,
      code: error.code,
    };
  }

  if (error instanceof Error) {
    return {
      success: false,
      error: error.message,
    };
  }

  return {
    success: false,
    error: "An unknown error occurred",
  };
}
