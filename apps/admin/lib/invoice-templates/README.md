# Invoice Template Generator

This module provides a comprehensive solution for generating release authorization documents and invoices for the Shamwaa Logistics system. It integrates with the PDF generator package to create professional, QR-code enabled documents.

## Features

- **Professional PDF Generation**: Creates formatted release authorization documents
- **QR Code Integration**: Generates verification QR codes for document authenticity
- **Multiple Templates**: Supports different document types and layouts
- **Cargo Manifest**: Detailed cargo item listings with customer information
- **Payment Confirmation**: Integrated payment status and details
- **Customizable Styling**: Configurable colors, fonts, and layout options
- **Type Safety**: Full TypeScript support with comprehensive type definitions

## Quick Start

### Basic Usage

```typescript
import {
  generateReleaseDocument,
  createPersonnelInfo,
  TEMPLATE_PRESETS,
} from "@/lib/invoice-templates";

// Create personnel info from user session
const personnelInfo = createPersonnelInfo({
  id: "user123",
  name: "<PERSON>",
  role: {
    name: "Manager",
    department: { name: "Operations" },
  },
});

// Generate a standard release document
const result = await generateReleaseDocument(
  shipment, // ShipmentForRelease object
  personnelInfo,
  {
    templateOptions: TEMPLATE_PRESETS.STANDARD_RELEASE,
    autoDownload: true,
  }
);

if (result.success) {
  console.log("Document generated:", result.data?.fileName);
} else {
  console.error("Error:", result.error);
}
```

### Advanced Usage with Custom Data

```typescript
import {
  generateReleaseAuthorizationInvoice,
  downloadPDF,
} from "@/lib/invoice-templates";

const releaseData: ReleaseAuthorizationData = {
  metadata: {
    documentNumber: "REL-240616-1234",
    generatedDate: new Date().toISOString(),
    generatedTime: new Date().toLocaleTimeString(),
    location: "Main Warehouse",
    documentType: "RELEASE_AUTHORIZATION",
    version: "1.0",
  },
  company: {
    name: "Shamwaa Logistics",
    address: "123 Logistics Avenue",
    phone: "+****************",
    email: "<EMAIL>",
  },
  personnel: {
    authorizedBy: "John Doe",
    authorizedById: "user123",
    department: "Operations",
    role: "Manager",
  },
  freight: {
    id: "freight123",
    name: "Freight-ABC123",
    type: "SEA",
    origin: "Guangzhou",
    destination: "Los Angeles",
  },
  batch: {
    id: "batch123",
    code: "BATCH-SEA-G-24-06",
    freightId: "freight123",
    totalWeight: 1500,
    weightUnit: "kg",
    totalValue: 25000,
    currency: "USD",
    cargoCount: 5,
    customerCount: 3,
  },
  cargoItems: [
    {
      id: "cargo1",
      trackingNumber: "TRK-********",
      description: "Electronics Components",
      category: "Electronics",
      quantity: 10,
      weight: 300,
      weightUnit: "kg",
      value: 5000,
      currency: "USD",
      customerName: "ABC Electronics",
      customerCompany: "ABC Electronics Ltd.",
    },
    // ... more cargo items
  ],
  payment: {
    status: "PAID",
    totalAmount: 25000,
    paidAmount: 25000,
    currency: "USD",
    paymentMethod: "Bank Transfer",
    paymentReference: "PAY-123456",
  },
  qrCode: {
    releaseCode: "REL-ABC123",
    cargoIds: ["cargo1", "cargo2"],
    batchId: "batch123",
    authorizedBy: "John Doe",
    authorizedDate: new Date().toISOString(),
  },
};

const result = await generateReleaseAuthorizationInvoice(releaseData);

if (result.success && result.data) {
  downloadPDF(result.data.pdfBlob, result.data.fileName);
}
```

## Template Presets

The module includes several predefined templates:

### STANDARD_RELEASE

- Full document with all sections
- Payment details included
- Detailed cargo information
- Standard QR code size (150px)

### QUICK_RELEASE

- Simplified document
- No payment details
- Basic cargo information
- Smaller QR code (120px)

### DETAILED_MANIFEST

- Comprehensive cargo manifest
- Cargo images (if available)
- Full payment details
- Large QR code (180px)

### BATCH_RELEASE

- Optimized for batch operations
- Multiple cargo items
- Compact layout
- Medium QR code (140px)

## Document Structure

Each generated document includes:

1. **Header Section**

   - Company logo and information
   - Document title and metadata
   - Generation timestamp and location

2. **Freight & Batch Information**

   - Freight details (route, type, vessel/flight)
   - Batch code and statistics
   - Weight and value summaries

3. **Authorization Details**

   - Authorized personnel information
   - Authorization timestamp
   - Release code

4. **Cargo Manifest Table**

   - Tracking numbers
   - Item descriptions and categories
   - Customer information
   - Quantities, weights, and values
   - Dimensions (if available)

5. **Payment Confirmation**

   - Payment status and amounts
   - Payment method and reference
   - Due dates (if applicable)

6. **QR Code Section**

   - Verification QR code
   - Release code display
   - Scanning instructions

7. **Additional Notes**
   - Special instructions
   - Terms and conditions
   - Contact information

## Integration with Release Authorization

The module integrates seamlessly with the existing release authorization system:

```typescript
// In your release authorization component
import { generateStandardReleaseDocument } from "@/lib/invoice-templates";

const handleGenerateDocument = async (shipment: ShipmentForRelease) => {
  const personnelInfo = createPersonnelInfo(currentUser);

  const result = await generateStandardReleaseDocument(
    shipment,
    personnelInfo,
    {
      additionalData: {
        notes: "Handle with care",
        specialInstructions: "Fragile items - use protective packaging",
      },
    }
  );

  if (result.success) {
    // Document generated and downloaded
    toast.success("Release document generated successfully");
  } else {
    toast.error(`Failed to generate document: ${result.error}`);
  }
};
```

## Customization Options

### Styling Options

```typescript
const customOptions: InvoiceTemplateOptions = {
  primaryColor: "#1e40af", // Header and accent color
  secondaryColor: "#64748b", // Secondary text color
  fontFamily: "helvetica", // Font family
  fontSize: 10, // Base font size
  format: "A4", // Page format
  orientation: "portrait", // Page orientation
};
```

### Layout Options

```typescript
const layoutOptions: InvoiceTemplateOptions = {
  showCompanyLogo: true, // Show company logo
  showCargoImages: false, // Show cargo item images
  showDetailedCargoInfo: true, // Show detailed cargo information
  showPaymentDetails: true, // Show payment section
  qrCodeSize: 150, // QR code size in pixels
  qrCodePosition: "bottom-right", // QR code position
};
```

## Error Handling

The module provides comprehensive error handling:

```typescript
import {
  handleTemplateError,
  InvoiceTemplateError,
} from "@/lib/invoice-templates";

try {
  const result = await generateReleaseDocument(shipment, personnelInfo);
  // Handle success
} catch (error) {
  const errorResult = handleTemplateError(error);
  console.error("Template error:", errorResult.error);

  if (errorResult.code) {
    // Handle specific error codes
    switch (errorResult.code) {
      case "INVALID_DATA":
        // Handle invalid data error
        break;
      case "QR_GENERATION_FAILED":
        // Handle QR code generation error
        break;
      default:
        // Handle other errors
        break;
    }
  }
}
```

## Dependencies

- `@workspace/pdf-generator`: Core PDF generation functionality
- `qrcode`: QR code generation
- `@types/qrcode`: TypeScript definitions for QR code library

## Environment Variables

```env
NEXT_PUBLIC_APPLICATION_URL=https://your-app-domain.com
```

This is used for generating verification URLs in QR codes.

## Contributing

When adding new features or templates:

1. Update type definitions in `types/invoice-template.ts`
2. Add new template functions in `invoice-template-generator.ts`
3. Create integration utilities in `release-document-generator.ts`
4. Export new functionality in `index.ts`
5. Update this README with usage examples
