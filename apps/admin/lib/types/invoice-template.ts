/**
 * Types for Release Authorization Invoice Template Generator
 */

import type { CompanyInfo } from "./shared";

// Re-export CompanyInfo from shared types
export type { CompanyInfo } from "./shared";

export interface PersonnelInfo {
  authorizedBy: string;
  authorizedById: string;
  department: string;
  role: string;
  signature?: string; // Base64 encoded signature image
}

export interface FreightInfo {
  id: string;
  name: string;
  type: "AIR" | "SEA";
  origin: string;
  destination: string;
  departureDate?: string;
  arrivalDate?: string;
  vessel?: string;
  flightNumber?: string;
}

export interface BatchInfo {
  id: string;
  code: string;
  totalWeight: number;
  weightUnit: string;
  totalValue: number;
  currency: string;
  cargoCount: number;
  customerCount: number;
}

export interface CargoItem {
  id: string;
  trackingNumber: string;
  description: string;
  category: string;
  quantity: number;
  weight: number;
  weightUnit: string;
  dimensions?: {
    length: number;
    width: number;
    height: number;
    unit: string;
  };
  value: number;
  currency: string;
  images?: string[]; // Base64 encoded images or URLs
  customerName: string;
  customerCompany?: string;
  customerPhone?: string;
  specialInstructions?: string;
}

export interface PaymentInfo {
  status: "PAID" | "PENDING" | "PARTIAL" | "OVERDUE";
  totalAmount: number;
  paidAmount: number;
  currency: string;
  paymentMethod?: string;
  paymentDate?: string;
  paymentReference?: string;
  dueDate?: string;
}

export interface QRCodeData {
  id: string;
  releaseCode: string;
  cargoIds: string[];
  batchId: string;
  authorizedBy: string;
  authorizedDate: string;
  expiryDate?: string;
  verificationUrl?: string;
}

export interface DocumentMetadata {
  documentNumber: string;
  generatedDate: string;
  generatedTime: string;
  location: string;
  documentType: "RELEASE_AUTHORIZATION";
  version: string;
  validUntil?: string;
}

export interface ReleaseAuthorizationData {
  // Document metadata
  metadata: DocumentMetadata;

  // Company information
  company: CompanyInfo;

  // Personnel information
  personnel: PersonnelInfo;

  // Freight and batch information
  freight: FreightInfo;
  batch: BatchInfo;

  // Cargo items
  cargoItems: CargoItem[];

  // Payment information
  payment: PaymentInfo;

  // QR code data
  qrCode: QRCodeData;

  // Document attachments
  attachments?: DocumentAttachment[];

  // Additional notes
  notes?: string;
  specialInstructions?: string;

  // Verification requirements
  requiresPhotoId?: boolean;
  requiresSignature?: boolean;
  requiresWitness?: boolean;
}

export interface DocumentAttachment {
  id: string;
  name: string;
  path: string;
  category: string;
  description?: string;
  size?: number;
  uploadedAt?: string;
  uploadedBy?: string;
}

export interface InvoiceTemplateOptions {
  // PDF options
  format?: "A4" | "LETTER";
  orientation?: "portrait" | "landscape";

  // Styling options
  primaryColor?: string;
  secondaryColor?: string;
  fontFamily?: string;
  fontSize?: number;

  // Layout options
  showCompanyLogo?: boolean;
  showCargoImages?: boolean;
  showDetailedCargoInfo?: boolean;
  showPaymentDetails?: boolean;

  // QR code options
  qrCodeSize?: number;
  qrCodePosition?: "top-right" | "bottom-center" | "bottom-right";

  // Language options
  language?: "en" | "zh" | "ar";

  // Watermark
  watermark?: string;
  watermarkOpacity?: number;
}

export interface GenerateInvoiceResult {
  success: boolean;
  data?: {
    pdfBlob: Blob;
    documentNumber: string;
    qrCodeData: string;
    fileName: string;
  };
  error?: string;
}
