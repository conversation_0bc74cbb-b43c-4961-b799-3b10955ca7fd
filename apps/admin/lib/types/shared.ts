/**
 * Shared type definitions used across the application
 * This file consolidates common types to avoid duplication
 */

// Company information interface
export interface CompanyInfo {
  name: string;
  logo?: string;
  address: string;
  phone: string;
  email: string;
  website?: string;
}

// User information interface
export interface UserInfo {
  id: string;
  name: string;
  email: string;
}

// Customer information interface
export interface CustomerInfo {
  id: string;
  name: string;
  company?: string | null;
}

// Account information interface
export interface AccountInfo {
  id: string;
  users?: UserInfo | null;
}

// Base statistics interface
export interface BaseEntityStats {
  total: number;
  active: number;
  newThisMonth: number;
  byStatus: { status: string; count: number }[];
}

// Generic pagination parameters
export interface PaginationParams {
  limit?: number;
  offset?: number;
}

// Generic sorting parameters
export interface SortParams {
  column?: string;
  ascending?: boolean;
}

// Generic query options
export interface QueryOptions extends PaginationParams, SortParams {
  filters?: Record<string, any>;
}

// Generic search options
export interface SearchOptions extends QueryOptions {
  searchFields?: string[];
}

// Generic service response
export interface ServiceResponse<T> {
  data: T | null;
  error: string | null;
  success: boolean;
}

// Generic service list response
export interface ServiceListResponse<T> extends ServiceResponse<T[]> {
  count?: number;
  totalCount?: number;
}

// Generic bulk operation response
export interface BulkOperationResponse {
  successful: number;
  failed: number;
  errors: string[];
}

// Generic form errors type
export type FormErrors<T> = Partial<Record<keyof T, string>>;

// Generic form touched type
export type FormTouched<T> = Partial<Record<keyof T, boolean>>;

// Generic entity with account relation
export type WithAccount<T> = T & { accounts?: AccountInfo };

// Generic entity with timestamps
export interface WithTimestamps {
  created_at: string;
  updated_at: string;
}

// Generic entity with ID
export interface WithId {
  id: string;
}

// Base entity interface
export interface BaseEntity extends WithId, WithTimestamps {}
