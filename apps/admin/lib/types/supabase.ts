export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  public: {
    Tables: {
      accounts: {
        Row: {
          created_at: string | null;
          email: string;
          id: string;
          password: string;
          role_id: string | null;
          status: Database["public"]["Enums"]["status_enum"] | null;
          updated_at: string | null;
          user_id: string | null;
        };
        Insert: {
          created_at?: string | null;
          email: string;
          id?: string;
          password: string;
          role_id?: string | null;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          updated_at?: string | null;
          user_id?: string | null;
        };
        Update: {
          created_at?: string | null;
          email?: string;
          id?: string;
          password?: string;
          role_id?: string | null;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          updated_at?: string | null;
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "accounts_role_id_fkey";
            columns: ["role_id"];
            isOneToOne: false;
            referencedRelation: "roles";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "accounts_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: true;
            referencedRelation: "users";
            referencedColumns: ["id"];
          },
        ];
      };
      approvals: {
        Row: {
          account_id: string | null;
          associated_id: string | null;
          associated_table: string | null;
          created_at: string | null;
          details: Json | null;
          id: string;
          status: Database["public"]["Enums"]["status_enum"] | null;
          updated_at: string | null;
        };
        Insert: {
          account_id?: string | null;
          associated_id?: string | null;
          associated_table?: string | null;
          created_at?: string | null;
          details?: Json | null;
          id?: string;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          updated_at?: string | null;
        };
        Update: {
          account_id?: string | null;
          associated_id?: string | null;
          associated_table?: string | null;
          created_at?: string | null;
          details?: Json | null;
          id?: string;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "approvals_account_id_fkey";
            columns: ["account_id"];
            isOneToOne: false;
            referencedRelation: "accounts";
            referencedColumns: ["id"];
          },
        ];
      };
      assignments: {
        Row: {
          account_id: string | null;
          associated_id: string | null;
          associated_table: string | null;
          attachment: string | null;
          cc_list: string[] | null;
          created_at: string | null;
          description: string;
          id: string;
          name: string;
          status: Database["public"]["Enums"]["status_enum"] | null;
          to_list: string[] | null;
          updated_at: string | null;
        };
        Insert: {
          account_id?: string | null;
          associated_id?: string | null;
          associated_table?: string | null;
          attachment?: string | null;
          cc_list?: string[] | null;
          created_at?: string | null;
          description: string;
          id?: string;
          name: string;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          to_list?: string[] | null;
          updated_at?: string | null;
        };
        Update: {
          account_id?: string | null;
          associated_id?: string | null;
          associated_table?: string | null;
          attachment?: string | null;
          cc_list?: string[] | null;
          created_at?: string | null;
          description?: string;
          id?: string;
          name?: string;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          to_list?: string[] | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "assignments_account_id_fkey";
            columns: ["account_id"];
            isOneToOne: false;
            referencedRelation: "accounts";
            referencedColumns: ["id"];
          },
        ];
      };
      batches: {
        Row: {
          account_id: string | null;
          bill_of_lading: string | null;
          cbm_unit: Database["public"]["Enums"]["dimension_unit_enum"];
          cbm_value: number;
          category: Database["public"]["Enums"]["category_enum"] | null;
          code: string;
          created_at: string | null;
          currency_conv_rate: number | null;
          freight_id: string | null;
          height: number;
          id: string;
          length: number;
          name: string;
          freight_type: Database["public"]["Enums"]["freight_type_enum"] | null;
          status: Database["public"]["Enums"]["status_enum"] | null;
          type: Database["public"]["Enums"]["batch_type_enum"];
          updated_at: string | null;
          weight: number;
          weight_unit: Database["public"]["Enums"]["weight_unit_enum"] | null;
          width: number;
        };
        Insert: {
          account_id?: string | null;
          bill_of_lading?: string | null;
          cbm_unit: Database["public"]["Enums"]["dimension_unit_enum"];
          cbm_value: number;
          code: string;
          category: Database["public"]["Enums"]["category_enum"] | null;
          created_at?: string | null;
          currency_conv_rate?: number | null;
          freight_id?: string | null;
          freight_type: Database["public"]["Enums"]["freight_type_enum"] | null;
          height: number;
          id?: string;
          length: number;
          name: string;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          type: Database["public"]["Enums"]["batch_type_enum"];
          updated_at?: string | null;
          weight: number;
          weight_unit?: Database["public"]["Enums"]["weight_unit_enum"] | null;
          width: number;
        };
        Update: {
          account_id?: string | null;
          bill_of_lading?: string | null;
          cbm_unit?: Database["public"]["Enums"]["dimension_unit_enum"];
          cbm_value?: number;
          category?: Database["public"]["Enums"]["category_enum"] | null;
          code?: string;
          created_at?: string | null;
          currency_conv_rate?: number | null;
          freight_id?: string | null;
          freight_type: Database["public"]["Enums"]["freight_type_enum"] | null;
          height?: number;
          id?: string;
          length?: number;
          name?: string;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          type?: Database["public"]["Enums"]["batch_type_enum"];
          updated_at?: string | null;
          weight?: number;
          weight_unit?: Database["public"]["Enums"]["weight_unit_enum"] | null;
          width?: number;
        };
        Relationships: [
          {
            foreignKeyName: "batches_account_id_fkey";
            columns: ["account_id"];
            isOneToOne: false;
            referencedRelation: "accounts";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "batches_freight_id_fkey";
            columns: ["freight_id"];
            isOneToOne: false;
            referencedRelation: "freights";
            referencedColumns: ["id"];
          },
        ];
      };
      cargos: {
        Row: {
          account_id: string | null;
          assigned_to: string | null;
          batch_id: string | null;
          category: Database["public"]["Enums"]["category_enum"] | null;
          cbm_unit: Database["public"]["Enums"]["dimension_unit_enum"] | null;
          cbm_value: number | null;
          created_at: string | null;
          customer_id: string | null;
          dimension_height: number | null;
          dimension_length: number | null;
          dimension_unit:
            | Database["public"]["Enums"]["dimension_unit_enum"]
            | null;
          dimension_width: number | null;
          ctn: number | null;
          estimated_arrival: string | null;
          estimated_departure: string | null;
          factor_unit: string | null;
          factor_value: number | null;
          id: string;
          type: string;
          invoice_id: string | null;
          china_tracking_number: string | null;
          particular: string | null;
          quantity: number | null;
          status: Database["public"]["Enums"]["status_enum"] | null;
          supplier_id: string | null;
          total_price: number | null;
          tracking_number: string;
          unit_price: number | null;
          updated_at: string | null;
          weight_unit: Database["public"]["Enums"]["weight_unit_enum"] | null;
          weight_value: number | null;
        };
        Insert: {
          account_id?: string | null;
          assigned_to?: string | null;
          batch_id?: string | null;
          type: string;
          category?: Database["public"]["Enums"]["category_enum"] | null;
          cbm_unit?: Database["public"]["Enums"]["dimension_unit_enum"] | null;
          cbm_value?: number | null;
          created_at?: string | null;
          customer_id?: string | null;
          supplier_id?: string | null;
          dimension_height?: number | null;
          dimension_length?: number | null;
          dimension_unit?:
            | Database["public"]["Enums"]["dimension_unit_enum"]
            | null;
          dimension_width?: number | null;
          ctn?: number | null;
          estimated_arrival?: string | null;
          estimated_departure?: string | null;
          factor_unit?: string | null;
          factor_value?: number | null;
          id?: string;
          invoice_id?: string | null;
          china_tracking_number?: string | null;
          particular?: string | null;
          quantity?: number | null;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          total_price?: number | null;
          tracking_number: string;
          unit_price?: number | null;
          updated_at?: string | null;
          weight_unit?: Database["public"]["Enums"]["weight_unit_enum"] | null;
          weight_value?: number | null;
        };
        Update: {
          account_id?: string | null;
          assigned_to?: string | null;
          batch_id?: string | null;
          type: string;
          category?: Database["public"]["Enums"]["category_enum"] | null;
          cbm_unit?: Database["public"]["Enums"]["dimension_unit_enum"] | null;
          cbm_value?: number | null;
          created_at?: string | null;
          customer_id?: string | null;
          supplier_id?: string | null;
          dimension_height?: number | null;
          dimension_length?: number | null;
          dimension_unit?:
            | Database["public"]["Enums"]["dimension_unit_enum"]
            | null;
          dimension_width?: number | null;
          ctn?: number | null;
          estimated_arrival?: string | null;
          estimated_departure?: string | null;
          factor_unit?: string | null;
          factor_value?: number | null;
          id?: string;
          invoice_id?: string | null;
          china_tracking_number?: string | null;
          particular?: string | null;
          quantity?: number | null;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          total_price?: number | null;
          tracking_number?: string;
          unit_price?: number | null;
          updated_at?: string | null;
          weight_unit?: Database["public"]["Enums"]["weight_unit_enum"] | null;
          weight_value?: number | null;
        };
        Relationships: [
          {
            foreignKeyName: "cargos_account_id_fkey";
            columns: ["account_id"];
            isOneToOne: false;
            referencedRelation: "accounts";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "cargos_assigned_to_fkey";
            columns: ["assigned_to"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "cargos_batch_id_fkey";
            columns: ["batch_id"];
            isOneToOne: false;
            referencedRelation: "batches";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "cargos_customer_id_fkey";
            columns: ["customer_id"];
            isOneToOne: false;
            referencedRelation: "customers";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "cargos_invoice_id_fkey";
            columns: ["invoice_id"];
            isOneToOne: false;
            referencedRelation: "invoices";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "cargos_supplier_id_fkey";
            columns: ["supplier_id"];
            isOneToOne: false;
            referencedRelation: "suppliers";
            referencedColumns: ["id"];
          },
        ];
      };
      customers: {
        Row: {
          account_id: string | null;
          code: string | null;
          created_at: string | null;
          email: string | null;
          id: string;
          location: string | null;
          name: string;
          phone: string | null;
          status: Database["public"]["Enums"]["status_enum"] | null;
          updated_at: string | null;
        };
        Insert: {
          account_id?: string | null;
          code?: string | null;
          created_at?: string | null;
          email?: string | null;
          id?: string;
          location?: string | null;
          name: string;
          phone?: string | null;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          updated_at?: string | null;
        };
        Update: {
          account_id?: string | null;
          code?: string | null;
          created_at?: string | null;
          email?: string | null;
          id?: string;
          location?: string | null;
          name?: string;
          phone?: string | null;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "customers_account_id_fkey";
            columns: ["account_id"];
            isOneToOne: false;
            referencedRelation: "accounts";
            referencedColumns: ["id"];
          },
        ];
      };
      departments: {
        Row: {
          created_at: string | null;
          id: string;
          name: string;
          status: Database["public"]["Enums"]["status_enum"] | null;
          updated_at: string | null;
        };
        Insert: {
          created_at?: string | null;
          id?: string;
          name: string;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          updated_at?: string | null;
        };
        Update: {
          created_at?: string | null;
          id?: string;
          name?: string;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          updated_at?: string | null;
        };
        Relationships: [];
      };
      documents: {
        Row: {
          account_id: string | null;
          associated_id: string | null;
          associated_table: string | null;
          category: string | null;
          created_at: string | null;
          description: string | null;
          details: Json | null;
          id: string;
          name: string;
          path: string;
          status: Database["public"]["Enums"]["status_enum"] | null;
          updated_at: string | null;
        };
        Insert: {
          account_id?: string | null;
          associated_id?: string | null;
          associated_table?: string | null;
          category?: string | null;
          created_at?: string | null;
          description?: string | null;
          details?: Json | null;
          id?: string;
          name: string;
          path: string;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          updated_at?: string | null;
        };
        Update: {
          account_id?: string | null;
          associated_id?: string | null;
          associated_table?: string | null;
          category?: string | null;
          created_at?: string | null;
          description?: string | null;
          details?: Json | null;
          id?: string;
          name?: string;
          path?: string;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "documents_account_id_fkey";
            columns: ["account_id"];
            isOneToOne: false;
            referencedRelation: "accounts";
            referencedColumns: ["id"];
          },
        ];
      };
      freights: {
        Row: {
          id: string;
          account_id: string | null;
          name: string;
          scac_codes: string;
          type: Database["public"]["Enums"]["freight_type_enum"] | null;
          status: Database["public"]["Enums"]["status_enum"] | null;
          prefixes: string | null;
          active: boolean | null;
          ct_active: boolean | null;
          bk_active: boolean | null;
          bl_active: boolean | null;
          maintenance: boolean | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          account_id?: string | null;
          name: string;
          scac_codes: string;
          type?: Database["public"]["Enums"]["freight_type_enum"] | null;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          prefixes?: string | null;
          active?: boolean | null;
          ct_active?: boolean | null;
          bk_active?: boolean | null;
          bl_active?: boolean | null;
          maintenance?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          account_id?: string | null;
          name?: string;
          scac_codes?: string;
          type?: Database["public"]["Enums"]["freight_type_enum"] | null;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          prefixes?: string | null;
          active?: boolean | null;
          ct_active?: boolean | null;
          bk_active?: boolean | null;
          bl_active?: boolean | null;
          maintenance?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "freights_account_id_fkey";
            columns: ["account_id"];
            isOneToOne: false;
            referencedRelation: "accounts";
            referencedColumns: ["id"];
          },
        ];
      };
      handovers: {
        Row: {
          biometric_verification: boolean | null;
          cargo_id: string | null;
          created_at: string | null;
          handover_date: string;
          handover_notes: string | null;
          id: string;
          location_data: Json | null;
          qr_verification: boolean | null;

          sender_id: string | null;
          status: Database["public"]["Enums"]["status_enum"] | null;
          updated_at: string | null;
          verification_data: Json | null;
        };
        Insert: {
          biometric_verification?: boolean | null;
          cargo_id?: string | null;
          created_at?: string | null;
          handover_date: string;
          handover_notes?: string | null;
          id?: string;
          location_data?: Json | null;
          qr_verification?: boolean | null;

          sender_id?: string | null;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          updated_at?: string | null;
          verification_data?: Json | null;
        };
        Update: {
          biometric_verification?: boolean | null;
          cargo_id?: string | null;
          created_at?: string | null;
          handover_date?: string;
          handover_notes?: string | null;
          id?: string;
          location_data?: Json | null;
          qr_verification?: boolean | null;

          sender_id?: string | null;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          updated_at?: string | null;
          verification_data?: Json | null;
        };
        Relationships: [
          {
            foreignKeyName: "handovers_cargo_id_fkey";
            columns: ["cargo_id"];
            isOneToOne: false;
            referencedRelation: "cargos";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "handovers_sender_id_fkey";
            columns: ["sender_id"];
            isOneToOne: false;
            referencedRelation: "accounts";
            referencedColumns: ["id"];
          },
        ];
      };
      invoices: {
        Row: {
          billing_address: string | null;
          created_at: string;
          customer_id: string | null;
          currency_conv_rate: number | null;
          due_at: string | null;
          id: string;
          inv_number: string | null;
          line_items: Json[] | null;
          notes: string | null;
          shared: boolean | null;
          status: Database["public"]["Enums"]["status_enum"] | null;
          subtotal: number | null;
          tax: number | null;
          terms_and_conditions: string | null;
          total: number | null;
          updated_at: string | null;
        };
        Insert: {
          billing_address?: string | null;
          created_at?: string;
          customer_id?: string | null;
          currency_conv_rate?: number | null;
          due_at?: string | null;
          id?: string;
          inv_number?: string | null;
          line_items?: Json[] | null;
          notes?: string | null;
          shared?: boolean | null;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          subtotal?: number | null;
          tax?: number | null;
          terms_and_conditions?: string | null;
          total?: number | null;
          updated_at?: string | null;
        };
        Update: {
          billing_address?: string | null;
          created_at?: string;
          customer_id?: string | null;
          currency_conv_rate?: number | null;
          due_at?: string | null;
          id?: string;
          inv_number?: string | null;
          line_items?: Json[] | null;
          notes?: string | null;
          shared?: boolean | null;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          subtotal?: number | null;
          tax?: number | null;
          terms_and_conditions?: string | null;
          total?: number | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "invoices_customer_id_fkey";
            columns: ["customer_id"];
            isOneToOne: false;
            referencedRelation: "customers";
            referencedColumns: ["id"];
          },
        ];
      };
      ledgers: {
        Row: {
          account_id: string | null;
          associated_id: string | null;
          associated_table: string | null;
          books: string[] | null;
          code: string | null;
          created_at: string | null;
          id: string;
          name: string;
          status: Database["public"]["Enums"]["status_enum"] | null;
          tags: string[] | null;
          updated_at: string | null;
        };
        Insert: {
          account_id?: string | null;
          associated_id?: string | null;
          associated_table?: string | null;
          books?: string[] | null;
          code?: string | null;
          created_at?: string | null;
          id?: string;
          name: string;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          tags?: string[] | null;
          updated_at?: string | null;
        };
        Update: {
          account_id?: string | null;
          associated_id?: string | null;
          associated_table?: string | null;
          books?: string[] | null;
          code?: string | null;
          created_at?: string | null;
          id?: string;
          name?: string;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          tags?: string[] | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "ledgers_account_id_fkey";
            columns: ["account_id"];
            isOneToOne: false;
            referencedRelation: "accounts";
            referencedColumns: ["id"];
          },
        ];
      };
      logs: {
        Row: {
          account_id: string | null;
          associated_id: string | null;
          associated_table: string | null;
          created_at: string | null;
          event: string;
          id: string;
          message: string;
          status: Database["public"]["Enums"]["status_enum"] | null;
          updated_at: string | null;
        };
        Insert: {
          account_id?: string | null;
          associated_id?: string | null;
          associated_table?: string | null;
          created_at?: string | null;
          event: string;
          id?: string;
          message: string;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          updated_at?: string | null;
        };
        Update: {
          account_id?: string | null;
          associated_id?: string | null;
          associated_table?: string | null;
          created_at?: string | null;
          event?: string;
          id?: string;
          message?: string;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "logs_account_id_fkey";
            columns: ["account_id"];
            isOneToOne: false;
            referencedRelation: "accounts";
            referencedColumns: ["id"];
          },
        ];
      };
      notifications: {
        Row: {
          account_id: string | null;
          associated_id: string | null;
          associated_table: string | null;
          created_at: string | null;
          details: Json | null;
          id: string;
          message: string;
          name: string;
          status: Database["public"]["Enums"]["status_enum"] | null;
          updated_at: string | null;
          to: string | null;
        };
        Insert: {
          account_id?: string | null;
          associated_id?: string | null;
          associated_table?: string | null;
          created_at?: string | null;
          details?: Json | null;
          id?: string;
          message: string;
          name: string;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          updated_at?: string | null;
          to: string | null;
        };
        Update: {
          account_id?: string | null;
          associated_id?: string | null;
          associated_table?: string | null;
          created_at?: string | null;
          details?: Json | null;
          id?: string;
          message?: string;
          name?: string;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          updated_at?: string | null;
          to: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "notifications_account_id_fkey";
            columns: ["account_id"];
            isOneToOne: false;
            referencedRelation: "accounts";
            referencedColumns: ["id"];
          },
        ];
      };
      recoveries: {
        Row: {
          account_id: string | null;
          created_at: string | null;
          expiry: string;
          id: string;
          token: string;
          updated_at: string | null;
        };
        Insert: {
          account_id?: string | null;
          created_at?: string | null;
          expiry: string;
          id?: string;
          token: string;
          updated_at?: string | null;
        };
        Update: {
          account_id?: string | null;
          created_at?: string | null;
          expiry?: string;
          id?: string;
          token?: string;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "recoveries_account_id_fkey";
            columns: ["account_id"];
            isOneToOne: false;
            referencedRelation: "accounts";
            referencedColumns: ["id"];
          },
        ];
      };
      roles: {
        Row: {
          created_at: string | null;
          department_id: string | null;
          id: string;
          name: string;
          status: Database["public"]["Enums"]["status_enum"] | null;
          updated_at: string | null;
        };
        Insert: {
          created_at?: string | null;
          department_id?: string | null;
          id?: string;
          name: string;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          updated_at?: string | null;
        };
        Update: {
          created_at?: string | null;
          department_id?: string | null;
          id?: string;
          name?: string;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "roles_department_id_fkey";
            columns: ["department_id"];
            isOneToOne: false;
            referencedRelation: "departments";
            referencedColumns: ["id"];
          },
        ];
      };
      schedules: {
        Row: {
          account_id: string | null;
          assigned: string[] | null;
          associated_id: string | null;
          associated_table: string | null;
          comment: string | null;
          created_at: string | null;
          deadline: string;
          id: string;
          name: string;
          start_time: string;
          status: Database["public"]["Enums"]["status_enum"] | null;
          updated_at: string | null;
        };
        Insert: {
          account_id?: string | null;
          assigned?: string[] | null;
          associated_id?: string | null;
          associated_table?: string | null;
          comment?: string | null;
          created_at?: string | null;
          deadline: string;
          id?: string;
          name: string;
          start_time: string;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          updated_at?: string | null;
        };
        Update: {
          account_id?: string | null;
          assigned?: string[] | null;
          associated_id?: string | null;
          associated_table?: string | null;
          comment?: string | null;
          created_at?: string | null;
          deadline?: string;
          id?: string;
          name?: string;
          start_time?: string;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "schedules_account_id_fkey";
            columns: ["account_id"];
            isOneToOne: false;
            referencedRelation: "accounts";
            referencedColumns: ["id"];
          },
        ];
      };
      sessions: {
        Row: {
          account_id: string | null;
          created_at: string | null;
          expiry: string;
          id: string;
          refresh_token: string;
          status: Database["public"]["Enums"]["status_enum"] | null;
          token: string;
          updated_at: string | null;
        };
        Insert: {
          account_id?: string | null;
          created_at?: string | null;
          expiry: string;
          id?: string;
          refresh_token: string;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          token: string;
          updated_at?: string | null;
        };
        Update: {
          account_id?: string | null;
          created_at?: string | null;
          expiry?: string;
          id?: string;
          refresh_token?: string;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          token?: string;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "sessions_account_id_fkey";
            columns: ["account_id"];
            isOneToOne: false;
            referencedRelation: "accounts";
            referencedColumns: ["id"];
          },
        ];
      };
      shipments: {
        Row: {
          account_id: string | null;
          attachment: string | null;
          batch_id: string | null;
          bill_of_lading: string | null;
          created_at: string | null;
          freight_id: string | null;
          freight_reference_id: string | null;
          id: string;
          metadata: Json | null;
          status: Database["public"]["Enums"]["status_enum"] | null;
          tracking_number: string | null;
          updated_at: string | null;
        };
        Insert: {
          account_id?: string | null;
          attachment?: string | null;
          batch_id?: string | null;
          bill_of_lading?: string | null;
          created_at?: string | null;
          freight_id?: string | null;
          freight_reference_id?: string | null;
          id?: string;
          metadata?: Json | null;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          tracking_number?: string | null;
          updated_at?: string | null;
        };
        Update: {
          account_id?: string | null;
          attachment?: string | null;
          batch_id?: string | null;
          bill_of_lading?: string | null;
          created_at?: string | null;
          freight_id?: string | null;
          freight_reference_id?: string | null;
          id?: string;
          metadata?: Json | null;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          tracking_number?: string | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "shipments_account_id_fkey";
            columns: ["account_id"];
            isOneToOne: false;
            referencedRelation: "accounts";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "shipments_batch_id_fkey";
            columns: ["batch_id"];
            isOneToOne: false;
            referencedRelation: "batches";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "shipments_freight_id_fkey";
            columns: ["freight_id"];
            isOneToOne: false;
            referencedRelation: "freights";
            referencedColumns: ["id"];
          },
        ];
      };
      tasks: {
        Row: {
          account_id: string | null;
          assignee: string | null;
          associated_id: string | null;
          associated_ids: string[] | null;
          associated_table: string | null;
          category: Database["public"]["Enums"]["category_enum"] | null;
          created_at: string | null;
          due: string | null;
          id: string;
          name: string;
          priority: Database["public"]["Enums"]["task_priority_enum"] | null;
          start: string | null;
          status: Database["public"]["Enums"]["status_enum"] | null;
          updated_at: string | null;
        };
        Insert: {
          account_id?: string | null;
          assignee?: string | null;
          associated_id?: string | null;
          associated_ids?: string[] | null;
          associated_table?: string | null;
          category?: Database["public"]["Enums"]["category_enum"] | null;
          created_at?: string | null;
          due?: string | null;
          id?: string;
          name: string;
          priority?: Database["public"]["Enums"]["task_priority_enum"] | null;
          start?: string | null;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          updated_at?: string | null;
        };
        Update: {
          account_id?: string | null;
          assignee?: string | null;
          associated_id?: string | null;
          associated_ids?: string[] | null;
          associated_table?: string | null;
          category?: Database["public"]["Enums"]["category_enum"] | null;
          created_at?: string | null;
          due?: string | null;
          id?: string;
          name?: string;
          priority?: Database["public"]["Enums"]["task_priority_enum"] | null;
          start?: string | null;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          updated_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "tasks_account_id_fkey";
            columns: ["account_id"];
            isOneToOne: false;
            referencedRelation: "accounts";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "tasks_assignee_fkey";
            columns: ["assignee"];
            isOneToOne: false;
            referencedRelation: "accounts";
            referencedColumns: ["id"];
          },
        ];
      };
      transactions: {
        Row: {
          account_id: string | null;
          amount: number;
          context: string | null;
          created_at: string | null;
          id: string;
          ledger_id: string | null;
          name: string;
          status: Database["public"]["Enums"]["status_enum"] | null;
          tags: string[] | null;
          updated_at: string | null;
          value: number;
          associated_table: string | null;
          associated_id: string | null;
        };
        Insert: {
          account_id?: string | null;
          amount: number;
          context?: string | null;
          created_at?: string | null;
          id?: string;
          ledger_id?: string | null;
          name: string;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          tags?: string[] | null;
          updated_at?: string | null;
          value: number;
          associated_table?: string | null;
          associated_id?: string | null;
        };
        Update: {
          account_id?: string | null;
          amount?: number;
          context?: string | null;
          created_at?: string | null;
          id?: string;
          ledger_id?: string | null;
          name?: string;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          tags?: string[] | null;
          updated_at?: string | null;
          value?: number;
          associated_table?: string | null;
          associated_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "transactions_account_id_fkey";
            columns: ["account_id"];
            isOneToOne: false;
            referencedRelation: "accounts";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "transactions_ledger_id_fkey";
            columns: ["ledger_id"];
            isOneToOne: false;
            referencedRelation: "ledgers";
            referencedColumns: ["id"];
          },
        ];
      };
      users: {
        Row: {
          avatar: string | null;
          created_at: string | null;
          id: string;
          location: string | null;
          name: string;
          phone: string | null;
          permissions: Json | null;
          status: Database["public"]["Enums"]["status_enum"] | null;
          updated_at: string | null;
        };
        Insert: {
          avatar?: string | null;
          created_at?: string | null;
          id?: string;
          location?: string | null;
          name: string;
          phone?: string | null;
          permissions?: Json | null;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          updated_at?: string | null;
        };
        Update: {
          avatar?: string | null;
          created_at?: string | null;
          id?: string;
          location?: string | null;
          name?: string;
          phone?: string | null;
          permissions?: Json | null;
          status?: Database["public"]["Enums"]["status_enum"] | null;
          updated_at?: string | null;
        };
        Relationships: [];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      batch_type_enum: "CONTAINER" | "PALLET" | "BULK" | "MIXED";
      category_enum: "DANGEROUS" | "SAFE" | "AIR" | "SEA" | "ROAD";
      dimension_unit_enum:
        | "METERS"
        | "FEET"
        | "CENTIMETERS"
        | "INCHES"
        | "METER_CUBIC"
        | "FEET_CUBIC"
        | "CENTIMETER_CUBIC"
        | "INCH_CUBIC";

      freight_type_enum: "AIR" | "SEA" | "LAND" | "RAIL" | "MULTIMODAL";
      status_enum:
        | "ACTIVE"
        | "INACTIVE"
        | "PENDING"
        | "COMPLETED"
        | "CANCELLED"
        | "CREATED"
        | "PROCESSING"
        | "IN_TRANSIT"
        | "DELIVERED"
        | "PARTIAL_PAYEMENT"
        | "PICKED_UP"
        | "RELEASED"
        | "APPROVED"
        | "REJECTED"
        | "DRAFT"
        | "PAID"
        | "OVERDUE"
        | "CANCELED"
        | "CLOSED"
        | "READ"
        | "UNREAD"
        | "LOADED"
        | "UNLOADED"
        | "ARRIVED"
        | "CLEARED"
        | "READY_FOR_PICKUP";
      task_priority_enum: "LOW" | "NORMAL" | "HIGH" | "URGENT";
      tracking_type_enum: "INTERNAL" | "EXTERNAL" | "GPS" | "RFID" | "BARCODE";
      weight_unit_enum:
        | "KILOGRAMS"
        | "POUNDS"
        | "GRAMS"
        | "TONS"
        | "OUNCES"
        | "SHORT_TON"
        | "LONG_TON";
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DefaultSchema = Database[Extract<keyof Database, "public">];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R;
      }
      ? R
      : never
    : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I;
      }
      ? I
      : never
    : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U;
      }
      ? U
      : never
    : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never;

export const Constants = {
  public: {
    Enums: {
      batch_type_enum: ["CONTAINER", "PALLET", "BULK", "MIXED"],
      category_enum: ["DANGEROUS", "SAFE", "AIR", "SEA", "ROAD"],
      dimension_unit_enum: [
        "METERS",
        "FEET",
        "CENTIMETERS",
        "INCHES",
        "METER_CUBIC",
        "FEET_CUBIC",
        "CENTIMETER_CUBIC",
        "INCH_CUBIC",
      ],
      freight_type_enum: ["AIR", "SEA", "LAND", "RAIL", "MULTIMODAL"],
      status_enum: [
        "ACTIVE",
        "INACTIVE",
        "PENDING",
        "COMPLETED",
        "CANCELLED",
        "CREATED",
        "PROCESSING",
        "IN_TRANSIT",
        "DELIVERED",
        "PICKED_UP",
        "RELEASED",
        "APPROVED",
        "REJECTED",
        "EXCESS",
      ],
      tracking_type_enum: ["INTERNAL", "EXTERNAL", "GPS", "RFID", "BARCODE"],
      weight_unit_enum: ["KILOGRAMS", "POUNDS", "GRAMS", "TONS"],
    },
  },
} as const;
