// Animation variants for consistent usage throughout the app

// Fade animation variants
export const fadeAnimation = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  exit: { opacity: 0 },
};

// Slide animation variants (horizontal)
export const slideAnimation = {
  hidden: { x: -30, opacity: 0 },
  visible: { x: 0, opacity: 1 },
  exit: { x: 30, opacity: 0 },
};

// Slide animation variants (vertical)
export const slideUpAnimation = {
  hidden: { y: 30, opacity: 0 },
  visible: { y: 0, opacity: 1 },
  exit: { y: -30, opacity: 0 },
};

// Scale animation variants
export const scaleAnimation = {
  hidden: { scale: 0.9, opacity: 0 },
  visible: { scale: 1, opacity: 1 },
  exit: { scale: 0.9, opacity: 0 },
};

// Staggered children animation
export const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

// Spring transition preset (natural motion)
export const springTransition = {
  type: "spring",
  stiffness: 300,
  damping: 30,
};

// Quick spring transition (faster, more bouncy)
export const quickSpringTransition = {
  type: "spring",
  stiffness: 500,
  damping: 25,
  mass: 1,
};

// Ease transition preset (smooth motion)
export const easeTransition = {
  type: "tween",
  ease: "easeInOut",
  duration: 0.3,
};

// Animation props for list items with stagger effect
export const getStaggeredChildrenProps = (index: number) => ({
  variants: {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  },
  transition: {
    ...springTransition,
    delay: index * 0.05,
  },
}); 