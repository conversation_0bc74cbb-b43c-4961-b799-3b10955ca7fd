"use client";

import React from "react";
import { useRB<PERSON> } from "@/lib/hooks/useRBAC";
import { EntityType, PermissionAction } from "@/lib/logistics/types/permissions";

interface RBACWrapperProps {
  children: React.ReactNode;
  entity: EntityType;
  action: PermissionAction;
  fallback?: React.ReactNode;
  className?: string;
}

interface ProtectedButtonProps {
  children: React.ReactNode;
  entity: EntityType;
  action: PermissionAction;
  fallback?: React.ReactNode;
  [key: string]: any; // Allow other button props
}

interface ProtectedRouteProps {
  children: React.ReactNode;
  entity: EntityType;
  fallback?: React.ReactNode;
}

// Main RBAC wrapper component
export function RBACWrapper({ 
  children, 
  entity, 
  action, 
  fallback = null,
  className 
}: RBACWrapperProps) {
  const { hasPermission, isLoading } = useRBAC();

  // Show loading state if permissions are still loading
  if (isLoading) {
    return <div className={className}>{fallback}</div>;
  }

  // Check if user has the required permission
  if (!hasPermission(entity, action)) {
    return <div className={className}>{fallback}</div>;
  }

  return <div className={className}>{children}</div>;
}

// Specialized component for protecting create buttons
export function ProtectedCreateButton({ 
  children, 
  entity, 
  fallback = null,
  ...props 
}: Omit<ProtectedButtonProps, 'action'>) {
  const { shouldShowCreateButton, isLoading } = useRBAC();

  if (isLoading || !shouldShowCreateButton(entity)) {
    return fallback;
  }

  return React.cloneElement(children as React.ReactElement, props);
}

// Specialized component for protecting edit buttons
export function ProtectedEditButton({ 
  children, 
  entity, 
  fallback = null,
  ...props 
}: Omit<ProtectedButtonProps, 'action'>) {
  const { shouldShowEditButton, isLoading } = useRBAC();

  if (isLoading || !shouldShowEditButton(entity)) {
    return fallback;
  }

  return React.cloneElement(children as React.ReactElement, props);
}

// Specialized component for protecting delete buttons
export function ProtectedDeleteButton({ 
  children, 
  entity, 
  fallback = null,
  ...props 
}: Omit<ProtectedButtonProps, 'action'>) {
  const { shouldShowDeleteButton, isLoading } = useRBAC();

  if (isLoading || !shouldShowDeleteButton(entity)) {
    return fallback;
  }

  return React.cloneElement(children as React.ReactElement, props);
}

// Component for protecting entire routes/sections
export function ProtectedRoute({ 
  children, 
  entity, 
  fallback = null 
}: ProtectedRouteProps) {
  const { shouldShowNavRoute, isLoading } = useRBAC();

  if (isLoading) {
    return <div className="flex items-center justify-center p-4">
      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
    </div>;
  }

  if (!shouldShowNavRoute(entity)) {
    return fallback;
  }

  return <>{children}</>;
}

// Higher-order component for protecting pages
export function withRBAC<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  entity: EntityType,
  action: PermissionAction = "view",
  fallback?: React.ReactNode
) {
  const ProtectedComponent = (props: P) => {
    const { hasPermission, isLoading } = useRBAC();

    if (isLoading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-500">Loading permissions...</p>
          </div>
        </div>
      );
    }

    if (!hasPermission(entity, action)) {
      return fallback || (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
            <p className="text-gray-500">You don't have permission to access this page.</p>
          </div>
        </div>
      );
    }

    return <WrappedComponent {...props} />;
  };

  ProtectedComponent.displayName = `withRBAC(${WrappedComponent.displayName || WrappedComponent.name})`;
  return ProtectedComponent;
}

// Utility component for conditional rendering based on permissions
export function PermissionGate({ 
  entity, 
  action, 
  children, 
  fallback = null 
}: {
  entity: EntityType;
  action: PermissionAction;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  const { hasPermission, isLoading } = useRBAC();

  if (isLoading) return fallback;
  if (!hasPermission(entity, action)) return fallback;
  
  return <>{children}</>;
}

// Component for showing different content based on role
export function RoleBasedContent({
  adminContent,
  managerContent,
  staffContent,
  fallback = null
}: {
  adminContent?: React.ReactNode;
  managerContent?: React.ReactNode;
  staffContent?: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  const { isAdmin, isManager, isLoading } = useRBAC();

  if (isLoading) return fallback;

  if (isAdmin() && adminContent) return <>{adminContent}</>;
  if (isManager() && managerContent) return <>{managerContent}</>;
  if (staffContent) return <>{staffContent}</>;
  
  return <>{fallback}</>;
}

// Hook for getting permission-based CSS classes
export function usePermissionClasses(entity: EntityType, action: PermissionAction) {
  const { hasPermission, isLoading } = useRBAC();

  return {
    hidden: isLoading || !hasPermission(entity, action),
    visible: !isLoading && hasPermission(entity, action),
    loading: isLoading,
    className: isLoading || !hasPermission(entity, action) ? "hidden" : "block"
  };
}
