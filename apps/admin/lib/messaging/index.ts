"use server";

import {
  MessagingService,
  LogisticsTemplates,
  WhatsAppTemplates,
  UrgentTemplates,
  CustomerServiceTemplates,
  type SMSOptions,
  type WhatsAppOptions,
  type MessageResponse,
} from "@workspace/messaging";

// Initialize messaging service with environment variables
const defaultConfig = {
  accountSid: process.env.TWILIO_ACCOUNT_SID!,
  authToken: process.env.TWILIO_AUTH_TOKEN!,
  phoneNumber: process.env.TWILIO_PHONE_NUMBER!,
  whatsappNumber: process.env.TWILIO_WHATSAPP_NUMBER!,
};

const messaging = new MessagingService(defaultConfig);

/**
 * Send SMS message
 */
export async function sendSMS(options: SMSOptions): Promise<MessageResponse> {
  try {
    return await messaging.sendSMS(options);
  } catch (error: any) {
    console.error("Failed to send SMS:", error);
    return {
      success: false,
      error: error.message || "Failed to send SMS",
    };
  }
}

/**
 * Send WhatsApp message
 */
export async function sendWhatsApp(
  options: WhatsAppOptions
): Promise<MessageResponse> {
  try {
    return await messaging.sendWhatsApp(options);
  } catch (error: any) {
    console.error("Failed to send WhatsApp message:", error);
    return {
      success: false,
      error: error.message || "Failed to send WhatsApp message",
    };
  }
}

/**
 * Logistics-specific messaging functions
 */

// Shipment notifications
export async function notifyShipmentCreated(
  phoneNumber: string,
  trackingNumber: string,
  origin: string,
  destination: string,
  useWhatsApp: boolean = false
): Promise<MessageResponse> {
  const message = LogisticsTemplates.shipmentCreated(
    trackingNumber,
    origin,
    destination
  );

  if (useWhatsApp) {
    return await sendWhatsApp({ to: phoneNumber, message });
  } else {
    return await sendSMS({ to: phoneNumber, message });
  }
}

export async function notifyShipmentInTransit(
  phoneNumber: string,
  trackingNumber: string,
  currentLocation: string,
  useWhatsApp: boolean = false
): Promise<MessageResponse> {
  const message = LogisticsTemplates.shipmentInTransit(
    trackingNumber,
    currentLocation
  );

  if (useWhatsApp) {
    return await sendWhatsApp({ to: phoneNumber, message });
  } else {
    return await sendSMS({ to: phoneNumber, message });
  }
}

export async function notifyShipmentDelivered(
  phoneNumber: string,
  trackingNumber: string,
  deliveryTime: string,
  useWhatsApp: boolean = false
): Promise<MessageResponse> {
  const message = LogisticsTemplates.shipmentDelivered(
    trackingNumber,
    deliveryTime
  );

  if (useWhatsApp) {
    return await sendWhatsApp({ to: phoneNumber, message });
  } else {
    return await sendSMS({ to: phoneNumber, message });
  }
}

export async function notifyShipmentDelayed(
  phoneNumber: string,
  trackingNumber: string,
  newETA: string,
  reason: string,
  useWhatsApp: boolean = false
): Promise<MessageResponse> {
  const message = LogisticsTemplates.shipmentDelayed(
    trackingNumber,
    newETA,
    reason
  );

  if (useWhatsApp) {
    return await sendWhatsApp({ to: phoneNumber, message });
  } else {
    return await sendSMS({ to: phoneNumber, message });
  }
}

// Invoice notifications
export async function notifyInvoiceGenerated(
  phoneNumber: string,
  invoiceNumber: string,
  amount: string,
  dueDate: string,
  useWhatsApp: boolean = false,
  paymentUrl?: string
): Promise<MessageResponse> {
  if (useWhatsApp && paymentUrl) {
    const message = WhatsAppTemplates.invoiceWithPaymentOptions(
      invoiceNumber,
      amount,
      dueDate,
      paymentUrl
    );
    return await sendWhatsApp({ to: phoneNumber, message });
  } else {
    const message = LogisticsTemplates.invoiceGenerated(
      invoiceNumber,
      amount,
      dueDate
    );
    return useWhatsApp
      ? await sendWhatsApp({ to: phoneNumber, message })
      : await sendSMS({ to: phoneNumber, message });
  }
}

export async function notifyPaymentReceived(
  phoneNumber: string,
  invoiceNumber: string,
  amount: string,
  useWhatsApp: boolean = false
): Promise<MessageResponse> {
  const message = LogisticsTemplates.paymentReceived(invoiceNumber, amount);

  if (useWhatsApp) {
    return await sendWhatsApp({ to: phoneNumber, message });
  } else {
    return await sendSMS({ to: phoneNumber, message });
  }
}

// Document notifications
export async function notifyDocumentReady(
  phoneNumber: string,
  documentType: string,
  trackingNumber: string,
  useWhatsApp: boolean = false,
  downloadUrl?: string
): Promise<MessageResponse> {
  const message = LogisticsTemplates.documentReady(
    documentType,
    trackingNumber,
    downloadUrl
  );

  if (useWhatsApp) {
    return await sendWhatsApp({ to: phoneNumber, message });
  } else {
    return await sendSMS({ to: phoneNumber, message });
  }
}

// Release authorization
export async function notifyReleaseAuthorization(
  phoneNumber: string,
  releaseCode: string,
  trackingNumber: string,
  instructions: string,
  useWhatsApp: boolean = false,
  qrCodeUrl?: string
): Promise<MessageResponse> {
  if (useWhatsApp && qrCodeUrl) {
    const message = WhatsAppTemplates.cargoReleaseWithQR(
      releaseCode,
      trackingNumber,
      instructions,
      qrCodeUrl
    );
    return await sendWhatsApp({ to: phoneNumber, message });
  } else {
    const message = LogisticsTemplates.releaseAuthorization(
      releaseCode,
      trackingNumber,
      instructions
    );
    return useWhatsApp
      ? await sendWhatsApp({ to: phoneNumber, message })
      : await sendSMS({ to: phoneNumber, message });
  }
}

// Emergency notifications
export async function notifyCargoDamage(
  phoneNumber: string,
  trackingNumber: string,
  damageDescription: string,
  contactNumber: string,
  useWhatsApp: boolean = true // Default to WhatsApp for urgent messages
): Promise<MessageResponse> {
  const message = UrgentTemplates.cargoDamage(
    trackingNumber,
    damageDescription,
    contactNumber
  );

  if (useWhatsApp) {
    return await sendWhatsApp({ to: phoneNumber, message });
  } else {
    return await sendSMS({ to: phoneNumber, message });
  }
}

export async function notifySecurityAlert(
  phoneNumber: string,
  trackingNumber: string,
  alertType: string,
  location: string,
  useWhatsApp: boolean = true
): Promise<MessageResponse> {
  const message = UrgentTemplates.securityAlert(
    trackingNumber,
    alertType,
    location
  );

  if (useWhatsApp) {
    return await sendWhatsApp({ to: phoneNumber, message });
  } else {
    return await sendSMS({ to: phoneNumber, message });
  }
}

// Customer service
export async function sendWelcomeMessage(
  phoneNumber: string,
  customerName: string,
  accountNumber: string,
  useWhatsApp: boolean = false
): Promise<MessageResponse> {
  const message = CustomerServiceTemplates.welcome(customerName, accountNumber);

  if (useWhatsApp) {
    return await sendWhatsApp({ to: phoneNumber, message });
  } else {
    return await sendSMS({ to: phoneNumber, message });
  }
}

export async function requestFeedback(
  phoneNumber: string,
  trackingNumber: string,
  useWhatsApp: boolean = false,
  surveyUrl?: string
): Promise<MessageResponse> {
  const message = CustomerServiceTemplates.feedbackRequest(
    trackingNumber,
    surveyUrl
  );

  if (useWhatsApp) {
    return await sendWhatsApp({ to: phoneNumber, message });
  } else {
    return await sendSMS({ to: phoneNumber, message });
  }
}

// Utility functions
export async function getMessageStatus(messageId: string) {
  try {
    return await messaging.getMessageStatus(messageId);
  } catch (error: any) {
    console.error("Failed to get message status:", error);
    return null;
  }
}
