export interface ShippingLocation {
  id: string;
  name: string;
  city: string;
  country: string;
  code: string;
  region: string;
  type: "port" | "airport" | "city";
  coordinates?: {
    lat: number;
    lng: number;
  };
}

export const SHIPPING_LOCATIONS: ShippingLocation[] = [
  // Africa
  {
    id: "cape-town-za",
    name: "Cape Town",
    city: "Cape Town",
    country: "South Africa",
    code: "CPT",
    region: "Africa",
    type: "port",
    coordinates: { lat: -33.9249, lng: 18.4241 },
  },
  {
    id: "durban-za",
    name: "Durban",
    city: "Durban",
    country: "South Africa",
    code: "DUR",
    region: "Africa",
    type: "port",
    coordinates: { lat: -29.8587, lng: 31.0218 },
  },
  {
    id: "lagos-ng",
    name: "Lagos (Apapa)",
    city: "Lagos",
    country: "Nigeria",
    code: "LOS",
    region: "Africa",
    type: "port",
    coordinates: { lat: 6.5244, lng: 3.3792 },
  },

  // Asia Pacific
  {
    id: "dalian-cn",
    name: "Dalian",
    city: "Dalian",
    country: "China",
    code: "DLC",
    region: "Asia Pacific",
    type: "port",
    coordinates: { lat: 38.914, lng: 121.6147 },
  },
  {
    id: "guangzhou-cn",
    name: "Guangzhou",
    city: "Guangzhou",
    country: "China",
    code: "CAN",
    region: "Asia Pacific",
    type: "port",
    coordinates: { lat: 23.1291, lng: 113.2644 },
  },
  {
    id: "ningbo-cn",
    name: "Ningbo-Zhoushan",
    city: "Ningbo",
    country: "China",
    code: "NGB",
    region: "Asia Pacific",
    type: "port",
    coordinates: { lat: 29.8683, lng: 121.544 },
  },
  {
    id: "qingdao-cn",
    name: "Qingdao",
    city: "Qingdao",
    country: "China",
    code: "TAO",
    region: "Asia Pacific",
    type: "port",
    coordinates: { lat: 36.0986, lng: 120.3719 },
  },
  {
    id: "shanghai-cn",
    name: "Shanghai",
    city: "Shanghai",
    country: "China",
    code: "SHA",
    region: "Asia Pacific",
    type: "port",
    coordinates: { lat: 31.2304, lng: 121.4737 },
  },
  {
    id: "shenzhen-cn",
    name: "Shenzhen",
    city: "Shenzhen",
    country: "China",
    code: "SZX",
    region: "Asia Pacific",
    type: "port",
    coordinates: { lat: 22.5431, lng: 114.0579 },
  },
  {
    id: "tianjin-cn",
    name: "Tianjin",
    city: "Tianjin",
    country: "China",
    code: "TSN",
    region: "Asia Pacific",
    type: "port",
    coordinates: { lat: 39.1042, lng: 117.2017 },
  },
  {
    id: "xiamen-cn",
    name: "Xiamen",
    city: "Xiamen",
    country: "China",
    code: "XMN",
    region: "Asia Pacific",
    type: "port",
    coordinates: { lat: 24.4798, lng: 118.0819 },
  },
  {
    id: "hong-kong-hk",
    name: "Hong Kong",
    city: "Hong Kong",
    country: "Hong Kong",
    code: "HKG",
    region: "Asia Pacific",
    type: "port",
    coordinates: { lat: 22.3193, lng: 114.1694 },
  },
  {
    id: "jakarta-id",
    name: "Jakarta (Tanjung Priok)",
    city: "Jakarta",
    country: "Indonesia",
    code: "JKT",
    region: "Asia Pacific",
    type: "port",
    coordinates: { lat: -6.1045, lng: 106.8794 },
  },
  {
    id: "kobe-jp",
    name: "Kobe",
    city: "Kobe",
    country: "Japan",
    code: "UKB",
    region: "Asia Pacific",
    type: "port",
    coordinates: { lat: 34.6901, lng: 135.1956 },
  },
  {
    id: "tokyo-jp",
    name: "Tokyo",
    city: "Tokyo",
    country: "Japan",
    code: "TYO",
    region: "Asia Pacific",
    type: "port",
    coordinates: { lat: 35.6762, lng: 139.6503 },
  },
  {
    id: "yokohama-jp",
    name: "Yokohama",
    city: "Yokohama",
    country: "Japan",
    code: "YOK",
    region: "Asia Pacific",
    type: "port",
    coordinates: { lat: 35.4437, lng: 139.638 },
  },
  {
    id: "port-klang-my",
    name: "Port Klang",
    city: "Klang",
    country: "Malaysia",
    code: "PKL",
    region: "Asia Pacific",
    type: "port",
    coordinates: { lat: 3.0319, lng: 101.3544 },
  },
  {
    id: "tanjung-pelepas-my",
    name: "Tanjung Pelepas",
    city: "Johor",
    country: "Malaysia",
    code: "TPP",
    region: "Asia Pacific",
    type: "port",
    coordinates: { lat: 1.3667, lng: 103.55 },
  },
  {
    id: "manila-ph",
    name: "Manila",
    city: "Manila",
    country: "Philippines",
    code: "MNL",
    region: "Asia Pacific",
    type: "port",
    coordinates: { lat: 14.5995, lng: 120.9842 },
  },
  {
    id: "singapore-sg",
    name: "Singapore",
    city: "Singapore",
    country: "Singapore",
    code: "SIN",
    region: "Asia Pacific",
    type: "port",
    coordinates: { lat: 1.3521, lng: 103.8198 },
  },
  {
    id: "busan-kr",
    name: "Busan",
    city: "Busan",
    country: "South Korea",
    code: "PUS",
    region: "Asia Pacific",
    type: "port",
    coordinates: { lat: 35.1796, lng: 129.0756 },
  },
  {
    id: "kaohsiung-tw",
    name: "Kaohsiung",
    city: "Kaohsiung",
    country: "Taiwan",
    code: "KHH",
    region: "Asia Pacific",
    type: "port",
    coordinates: { lat: 22.6273, lng: 120.3014 },
  },
  {
    id: "laem-chabang-th",
    name: "Laem Chabang",
    city: "Chonburi",
    country: "Thailand",
    code: "LCB",
    region: "Asia Pacific",
    type: "port",
    coordinates: { lat: 13.0827, lng: 100.8833 },
  },

  // East Africa
  {
    id: "bujumbura-bi",
    name: "Bujumbura",
    city: "Bujumbura",
    country: "Burundi",
    code: "BJM",
    region: "East Africa",
    type: "city",
    coordinates: { lat: -3.3614, lng: 29.3599 },
  },
  {
    id: "djibouti-dj",
    name: "Djibouti",
    city: "Djibouti",
    country: "Djibouti",
    code: "JIB",
    region: "East Africa",
    type: "port",
    coordinates: { lat: 11.8251, lng: 42.5903 },
  },
  {
    id: "addis-ababa-et",
    name: "Addis Ababa",
    city: "Addis Ababa",
    country: "Ethiopia",
    code: "ADD",
    region: "East Africa",
    type: "city",
    coordinates: { lat: 9.145, lng: 40.4897 },
  },
  {
    id: "mombasa-ke",
    name: "Mombasa",
    city: "Mombasa",
    country: "Kenya",
    code: "MBA",
    region: "East Africa",
    type: "port",
    coordinates: { lat: -4.0435, lng: 39.6682 },
  },
  {
    id: "nairobi-ke",
    name: "Nairobi",
    city: "Nairobi",
    country: "Kenya",
    code: "NBO",
    region: "East Africa",
    type: "city",
    coordinates: { lat: -1.2921, lng: 36.8219 },
  },
  {
    id: "antananarivo-mg",
    name: "Antananarivo",
    city: "Antananarivo",
    country: "Madagascar",
    code: "TNR",
    region: "East Africa",
    type: "city",
    coordinates: { lat: -18.8792, lng: 47.5079 },
  },
  {
    id: "port-louis-mu",
    name: "Port Louis",
    city: "Port Louis",
    country: "Mauritius",
    code: "MRU",
    region: "East Africa",
    type: "port",
    coordinates: { lat: -20.1609, lng: 57.5012 },
  },
  {
    id: "kigali-rw",
    name: "Kigali",
    city: "Kigali",
    country: "Rwanda",
    code: "KGL",
    region: "East Africa",
    type: "city",
    coordinates: { lat: -1.9441, lng: 30.0619 },
  },
  {
    id: "victoria-sc",
    name: "Victoria",
    city: "Victoria",
    country: "Seychelles",
    code: "SEZ",
    region: "East Africa",
    type: "port",
    coordinates: { lat: -4.6796, lng: 55.492 },
  },
  {
    id: "mogadishu-so",
    name: "Mogadishu",
    city: "Mogadishu",
    country: "Somalia",
    code: "MGQ",
    region: "East Africa",
    type: "port",
    coordinates: { lat: 2.0469, lng: 45.3182 },
  },
  {
    id: "dar-es-salaam-tz",
    name: "Dar es Salaam",
    city: "Dar es Salaam",
    country: "Tanzania",
    code: "DAR",
    region: "East Africa",
    type: "port",
    coordinates: { lat: -6.7924, lng: 39.2083 },
  },
  {
    id: "kampala-ug",
    name: "Kampala",
    city: "Kampala",
    country: "Uganda",
    code: "EBB",
    region: "East Africa",
    type: "city",
    coordinates: { lat: 0.3476, lng: 32.5825 },
  },

  // Europe
  {
    id: "antwerp-be",
    name: "Antwerp",
    city: "Antwerp",
    country: "Belgium",
    code: "ANR",
    region: "Europe",
    type: "port",
    coordinates: { lat: 51.2194, lng: 4.4025 },
  },
  {
    id: "le-havre-fr",
    name: "Le Havre",
    city: "Le Havre",
    country: "France",
    code: "LEH",
    region: "Europe",
    type: "port",
    coordinates: { lat: 49.4944, lng: 0.1079 },
  },
  {
    id: "marseille-fr",
    name: "Marseille",
    city: "Marseille",
    country: "France",
    code: "MRS",
    region: "Europe",
    type: "port",
    coordinates: { lat: 43.2965, lng: 5.3698 },
  },
  {
    id: "bremerhaven-de",
    name: "Bremerhaven",
    city: "Bremen",
    country: "Germany",
    code: "BRV",
    region: "Europe",
    type: "port",
    coordinates: { lat: 53.5396, lng: 8.581 },
  },
  {
    id: "hamburg-de",
    name: "Hamburg",
    city: "Hamburg",
    country: "Germany",
    code: "HAM",
    region: "Europe",
    type: "port",
    coordinates: { lat: 53.5511, lng: 9.9937 },
  },
  {
    id: "piraeus-gr",
    name: "Piraeus",
    city: "Athens",
    country: "Greece",
    code: "PIR",
    region: "Europe",
    type: "port",
    coordinates: { lat: 37.9755, lng: 23.7348 },
  },
  {
    id: "genoa-it",
    name: "Genoa",
    city: "Genoa",
    country: "Italy",
    code: "GOA",
    region: "Europe",
    type: "port",
    coordinates: { lat: 44.4056, lng: 8.9463 },
  },
  {
    id: "rotterdam-nl",
    name: "Rotterdam",
    city: "Rotterdam",
    country: "Netherlands",
    code: "RTM",
    region: "Europe",
    type: "port",
    coordinates: { lat: 51.9244, lng: 4.4777 },
  },
  {
    id: "barcelona-es",
    name: "Barcelona",
    city: "Barcelona",
    country: "Spain",
    code: "BCN",
    region: "Europe",
    type: "port",
    coordinates: { lat: 41.3851, lng: 2.1734 },
  },
  {
    id: "valencia-es",
    name: "Valencia",
    city: "Valencia",
    country: "Spain",
    code: "VLC",
    region: "Europe",
    type: "port",
    coordinates: { lat: 39.4699, lng: -0.3763 },
  },
  {
    id: "felixstowe-gb",
    name: "Felixstowe",
    city: "Felixstowe",
    country: "United Kingdom",
    code: "FXT",
    region: "Europe",
    type: "port",
    coordinates: { lat: 51.964, lng: 1.3518 },
  },
  {
    id: "london-gb",
    name: "London Gateway",
    city: "London",
    country: "United Kingdom",
    code: "LGW",
    region: "Europe",
    type: "port",
    coordinates: { lat: 51.5074, lng: -0.1278 },
  },

  // Middle East & Gulf
  {
    id: "manama-bh",
    name: "Manama",
    city: "Manama",
    country: "Bahrain",
    code: "BAH",
    region: "Middle East & Gulf",
    type: "port",
    coordinates: { lat: 26.0667, lng: 50.5577 },
  },
  {
    id: "alexandria-eg",
    name: "Alexandria",
    city: "Alexandria",
    country: "Egypt",
    code: "ALY",
    region: "Middle East & Gulf",
    type: "port",
    coordinates: { lat: 31.2001, lng: 29.9187 },
  },
  {
    id: "port-said-eg",
    name: "Port Said",
    city: "Port Said",
    country: "Egypt",
    code: "PSD",
    region: "Middle East & Gulf",
    type: "port",
    coordinates: { lat: 31.2653, lng: 32.3019 },
  },
  {
    id: "tehran-ir",
    name: "Tehran",
    city: "Tehran",
    country: "Iran",
    code: "IKA",
    region: "Middle East & Gulf",
    type: "city",
    coordinates: { lat: 35.6892, lng: 51.389 },
  },
  {
    id: "baghdad-iq",
    name: "Baghdad",
    city: "Baghdad",
    country: "Iraq",
    code: "BGW",
    region: "Middle East & Gulf",
    type: "city",
    coordinates: { lat: 33.3152, lng: 44.3661 },
  },
  {
    id: "amman-jo",
    name: "Amman",
    city: "Amman",
    country: "Jordan",
    code: "AMM",
    region: "Middle East & Gulf",
    type: "city",
    coordinates: { lat: 31.9454, lng: 35.9284 },
  },
  {
    id: "kuwait-city-kw",
    name: "Kuwait City",
    city: "Kuwait City",
    country: "Kuwait",
    code: "KWI",
    region: "Middle East & Gulf",
    type: "port",
    coordinates: { lat: 29.3759, lng: 47.9774 },
  },
  {
    id: "beirut-lb",
    name: "Beirut",
    city: "Beirut",
    country: "Lebanon",
    code: "BEY",
    region: "Middle East & Gulf",
    type: "port",
    coordinates: { lat: 33.8938, lng: 35.5018 },
  },
  {
    id: "muscat-om",
    name: "Muscat",
    city: "Muscat",
    country: "Oman",
    code: "MCT",
    region: "Middle East & Gulf",
    type: "port",
    coordinates: { lat: 23.5859, lng: 58.4059 },
  },
  {
    id: "doha-qa",
    name: "Doha",
    city: "Doha",
    country: "Qatar",
    code: "DOH",
    region: "Middle East & Gulf",
    type: "port",
    coordinates: { lat: 25.2854, lng: 51.531 },
  },
  {
    id: "dammam-sa",
    name: "Dammam",
    city: "Dammam",
    country: "Saudi Arabia",
    code: "DMM",
    region: "Middle East & Gulf",
    type: "port",
    coordinates: { lat: 26.4207, lng: 50.0888 },
  },
  {
    id: "jeddah-sa",
    name: "Jeddah",
    city: "Jeddah",
    country: "Saudi Arabia",
    code: "JED",
    region: "Middle East & Gulf",
    type: "port",
    coordinates: { lat: 21.4858, lng: 39.1925 },
  },
  {
    id: "riyadh-sa",
    name: "Riyadh",
    city: "Riyadh",
    country: "Saudi Arabia",
    code: "RUH",
    region: "Middle East & Gulf",
    type: "city",
    coordinates: { lat: 24.7136, lng: 46.6753 },
  },
  {
    id: "abu-dhabi-ae",
    name: "Abu Dhabi",
    city: "Abu Dhabi",
    country: "United Arab Emirates",
    code: "AUH",
    region: "Middle East & Gulf",
    type: "port",
    coordinates: { lat: 24.4539, lng: 54.3773 },
  },
  {
    id: "dubai-ae",
    name: "Dubai (Jebel Ali)",
    city: "Dubai",
    country: "United Arab Emirates",
    code: "DXB",
    region: "Middle East & Gulf",
    type: "port",
    coordinates: { lat: 25.2048, lng: 55.2708 },
  },
  {
    id: "sharjah-ae",
    name: "Sharjah",
    city: "Sharjah",
    country: "United Arab Emirates",
    code: "SHJ",
    region: "Middle East & Gulf",
    type: "port",
    coordinates: { lat: 25.3463, lng: 55.4209 },
  },

  // North America
  {
    id: "montreal-ca",
    name: "Montreal",
    city: "Montreal",
    country: "Canada",
    code: "YUL",
    region: "North America",
    type: "port",
    coordinates: { lat: 45.5017, lng: -73.5673 },
  },
  {
    id: "prince-rupert-ca",
    name: "Prince Rupert",
    city: "Prince Rupert",
    country: "Canada",
    code: "YPR",
    region: "North America",
    type: "port",
    coordinates: { lat: 54.315, lng: -130.3209 },
  },
  {
    id: "vancouver-ca",
    name: "Vancouver",
    city: "Vancouver",
    country: "Canada",
    code: "YVR",
    region: "North America",
    type: "port",
    coordinates: { lat: 49.2827, lng: -123.1207 },
  },
  {
    id: "charleston-us",
    name: "Charleston",
    city: "Charleston",
    country: "United States",
    code: "CHS",
    region: "North America",
    type: "port",
    coordinates: { lat: 32.7767, lng: -79.9311 },
  },
  {
    id: "houston-us",
    name: "Houston",
    city: "Houston",
    country: "United States",
    code: "HOU",
    region: "North America",
    type: "port",
    coordinates: { lat: 29.7604, lng: -95.3698 },
  },
  {
    id: "long-beach-us",
    name: "Long Beach",
    city: "Long Beach",
    country: "United States",
    code: "LGB",
    region: "North America",
    type: "port",
    coordinates: { lat: 33.7701, lng: -118.1937 },
  },
  {
    id: "los-angeles-us",
    name: "Los Angeles",
    city: "Los Angeles",
    country: "United States",
    code: "LAX",
    region: "North America",
    type: "port",
    coordinates: { lat: 34.0522, lng: -118.2437 },
  },
  {
    id: "new-york-us",
    name: "New York/New Jersey",
    city: "New York",
    country: "United States",
    code: "NYC",
    region: "North America",
    type: "port",
    coordinates: { lat: 40.7128, lng: -74.006 },
  },
  {
    id: "norfolk-us",
    name: "Norfolk",
    city: "Norfolk",
    country: "United States",
    code: "ORF",
    region: "North America",
    type: "port",
    coordinates: { lat: 36.8468, lng: -76.2852 },
  },
  {
    id: "oakland-us",
    name: "Oakland",
    city: "Oakland",
    country: "United States",
    code: "OAK",
    region: "North America",
    type: "port",
    coordinates: { lat: 37.8044, lng: -122.2712 },
  },
  {
    id: "savannah-us",
    name: "Savannah",
    city: "Savannah",
    country: "United States",
    code: "SAV",
    region: "North America",
    type: "port",
    coordinates: { lat: 32.0835, lng: -81.0998 },
  },
  {
    id: "seattle-us",
    name: "Seattle",
    city: "Seattle",
    country: "United States",
    code: "SEA",
    region: "North America",
    type: "port",
    coordinates: { lat: 47.6062, lng: -122.3321 },
  },
  {
    id: "tacoma-us",
    name: "Tacoma",
    city: "Tacoma",
    country: "United States",
    code: "TAC",
    region: "North America",
    type: "port",
    coordinates: { lat: 47.2529, lng: -122.4443 },
  },

  // Oceania
  {
    id: "auckland-nz",
    name: "Auckland",
    city: "Auckland",
    country: "New Zealand",
    code: "AKL",
    region: "Oceania",
    type: "port",
    coordinates: { lat: -36.8485, lng: 174.7633 },
  },
  {
    id: "brisbane-au",
    name: "Brisbane",
    city: "Brisbane",
    country: "Australia",
    code: "BNE",
    region: "Oceania",
    type: "port",
    coordinates: { lat: -27.4698, lng: 153.0251 },
  },
  {
    id: "fremantle-au",
    name: "Fremantle",
    city: "Perth",
    country: "Australia",
    code: "PER",
    region: "Oceania",
    type: "port",
    coordinates: { lat: -31.9505, lng: 115.8605 },
  },
  {
    id: "melbourne-au",
    name: "Melbourne",
    city: "Melbourne",
    country: "Australia",
    code: "MEL",
    region: "Oceania",
    type: "port",
    coordinates: { lat: -37.8136, lng: 144.9631 },
  },
  {
    id: "sydney-au",
    name: "Sydney",
    city: "Sydney",
    country: "Australia",
    code: "SYD",
    region: "Oceania",
    type: "port",
    coordinates: { lat: -33.8688, lng: 151.2093 },
  },

  // South America
  {
    id: "buenos-aires-ar",
    name: "Buenos Aires",
    city: "Buenos Aires",
    country: "Argentina",
    code: "BUE",
    region: "South America",
    type: "port",
    coordinates: { lat: -34.6118, lng: -58.396 },
  },
  {
    id: "santos-br",
    name: "Santos",
    city: "Santos",
    country: "Brazil",
    code: "SSZ",
    region: "South America",
    type: "port",
    coordinates: { lat: -23.9618, lng: -46.3322 },
  },
  {
    id: "valparaiso-cl",
    name: "Valparaíso",
    city: "Valparaíso",
    country: "Chile",
    code: "VAL",
    region: "South America",
    type: "port",
    coordinates: { lat: -33.0472, lng: -71.6127 },
  },
  {
    id: "callao-pe",
    name: "Callao",
    city: "Lima",
    country: "Peru",
    code: "LIM",
    region: "South America",
    type: "port",
    coordinates: { lat: -12.0464, lng: -77.0428 },
  },
];

// Helper functions for working with shipping locations
export const getLocationsByRegion = (region: string): ShippingLocation[] => {
  return SHIPPING_LOCATIONS.filter((location) => location.region === region);
};

export const getLocationsByType = (
  type: "port" | "airport" | "city"
): ShippingLocation[] => {
  return SHIPPING_LOCATIONS.filter((location) => location.type === type);
};

export const getLocationByCode = (
  code: string
): ShippingLocation | undefined => {
  return SHIPPING_LOCATIONS.find((location) => location.code === code);
};

export const getLocationById = (id: string): ShippingLocation | undefined => {
  return SHIPPING_LOCATIONS.find((location) => location.id === id);
};

export const searchLocations = (query: string): ShippingLocation[] => {
  const searchTerm = query.toLowerCase();
  return SHIPPING_LOCATIONS.filter(
    (location) =>
      location.name.toLowerCase().includes(searchTerm) ||
      location.city.toLowerCase().includes(searchTerm) ||
      location.country.toLowerCase().includes(searchTerm) ||
      location.code.toLowerCase().includes(searchTerm)
  );
};

export const getRegions = (): string[] => {
  return Array.from(
    new Set(SHIPPING_LOCATIONS.map((location) => location.region))
  );
};

// Format location for display
export const formatLocationDisplay = (location: ShippingLocation): string => {
  return `${location.name}, ${location.country} (${location.code})`;
};

// Format location for display with region
export const formatLocationDisplayWithRegion = (
  location: ShippingLocation
): string => {
  return `${location.name}, ${location.country} (${location.code}) - ${location.region}`;
};

// Group locations by region for organized display
export const getLocationsByRegionGrouped = (): Record<
  string,
  ShippingLocation[]
> => {
  return SHIPPING_LOCATIONS.reduce(
    (acc, location) => {
      if (!acc[location.region]) {
        acc[location.region] = [];
      }
      acc[location.region]?.push(location);
      return acc;
    },
    {} as Record<string, ShippingLocation[]>
  );
};

// Get location code for batch generation (simplified mapping)
export const getLocationCodeForBatch = (locationName: string): string => {
  const location = SHIPPING_LOCATIONS.find(
    (loc) =>
      loc.name.toLowerCase().includes(locationName.toLowerCase()) ||
      loc.city.toLowerCase().includes(locationName.toLowerCase())
  );

  if (location) {
    // Use first 2-3 characters of the code for batch generation
    return location.code.substring(0, 3);
  }

  // Fallback to generic code
  return "GEN";
};
