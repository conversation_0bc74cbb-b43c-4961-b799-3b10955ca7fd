import { createClient } from "./supabase/client";
import { redirect } from "next/navigation";

import { Database } from "./types/supabase";
import bcrypt from "bcryptjs";
import { connection } from "next/server";
import { v4 as uuidv4 } from "uuid";

import { delete<PERSON><PERSON>ie, get<PERSON><PERSON>ie, set<PERSON><PERSON>ie } from "cookies-next/client";

type User = Database["public"]["Tables"]["users"]["Row"];
type Account = Database["public"]["Tables"]["accounts"]["Row"];
type Role = Database["public"]["Tables"]["roles"]["Row"];
type Department = Database["public"]["Tables"]["departments"]["Row"];
type Session = Database["public"]["Tables"]["sessions"]["Row"];

export interface AuthUser {
  id: string;
  accountId: string;
  name: string;
  email: string;
  phone: string | null;
  location: string | null;
  avatar: string | null;
  status: string;
  role: {
    id: string;
    name: string;
    department: {
      id: string;
      name: string;
    };
    permissions: {
      [entity: string]: string[]; // JSONB format: {"users": ["view", "create"]}
    };
  } | null;
}

export interface AuthResult {
  user: AuthUser | null;
  session: Session | null;
  error: string | null;
}

export interface AuthCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
  phone?: string;
  location?: string;
  roleId?: string;
}

class AuthService {
  private supabase = createClient();

  async signInWithEmailPassword({
    email,
    password,
  }: AuthCredentials): Promise<AuthResult> {
    try {
      // Find account by email with user and role data
      const { data: accountData, error: accountError } = await this.supabase
        .from("accounts")
        .select(
          `
          *,
          users (*),
          roles (
            *,
            departments (*)
          )
        `
        )
        .eq("email", email?.toLowerCase()?.trim())
        .eq("status", "ACTIVE")
        .single();

      if (accountError || !accountData) {
        return {
          user: null,
          session: null,
          error: "Invalid email or password",
        };
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(
        password,
        accountData.password
      );
      if (!isPasswordValid) {
        return {
          user: null,
          session: null,
          error: "Invalid email or password",
        };
      }

      // Check if user is active
      if (accountData.users?.status !== "ACTIVE") {
        return {
          user: null,
          session: null,
          error: "Account is not active",
        };
      }

      // Create session
      const sessionToken = await connection().then(() =>
        this.generateSessionToken()
      );
      const refreshToken = await connection().then(() =>
        this.generateSessionToken()
      );
      const expiryDate = new Date();
      expiryDate.setHours(expiryDate.getHours() + 24); // 24 hour session

      const { data: sessionData, error: sessionError } = await this.supabase
        .from("sessions")
        .insert({
          account_id: accountData.id,
          token: sessionToken,
          refresh_token: refreshToken,
          expiry: expiryDate.toISOString(),
          status: "ACTIVE",
        })
        .select()
        .single();

      if (sessionError) {
        return {
          user: null,
          session: null,
          error: "Failed to create session",
        };
      }

      // Build auth user object
      const authUser: AuthUser = {
        id: accountData.users!.id,
        accountId: accountData.id,
        name: accountData.users!.name,
        email: accountData.email,
        phone: accountData.users!.phone,
        location: accountData.users!.location,
        avatar: accountData.users!.avatar,
        status: accountData.users!.status!,
        role: accountData.roles
          ? {
              id: accountData.roles.id,
              name: accountData.roles.name,
              department: {
                id: accountData.roles.departments!.id,
                name: accountData.roles.departments!.name,
              },
              permissions: (accountData.roles as any).permissions || {},
            }
          : null,
      };

      // Store session in localStorage
      setCookie("auth_session", sessionToken);

      return {
        user: authUser,
        session: sessionData,
        error: null,
      };
    } catch (error: any) {
      return {
        user: null,
        session: null,
        error: error.message || "Authentication failed",
      };
    }
  }

  async signUp({
    name,
    email,
    password,
    phone,
    location,
    roleId,
  }: RegisterData): Promise<AuthResult> {
    try {
      // Hash password
      const hashedPassword = await bcrypt.hash(password, 12);

      // Check if email already exists
      const { data: existingAccount } = await this.supabase
        .from("accounts")
        .select("id")
        .eq("email", email.toLowerCase().trim())
        .single();

      if (existingAccount) {
        return {
          user: null,
          session: null,
          error: "Email already exists",
        };
      }

      // Create user first
      const { data: userData, error: userError } = await this.supabase
        .from("users")
        .insert({
          name: name.trim(),
          phone: phone?.trim() || null,
          location: location?.trim() || null,
          status: "ACTIVE",
        })
        .select()
        .single();

      if (userError || !userData) {
        return {
          user: null,
          session: null,
          error: "Failed to create user profile",
        };
      }

      // Create account
      const { data: accountData, error: accountError } = await this.supabase
        .from("accounts")
        .insert({
          email: email.toLowerCase().trim(),
          password: hashedPassword,
          user_id: userData.id,
          role_id: roleId || null,
          status: "ACTIVE",
        })
        .select()
        .single();

      if (accountError || !accountData) {
        // Cleanup user if account creation fails
        await this.supabase.from("users").delete().eq("id", userData.id);
        return {
          user: null,
          session: null,
          error: "Failed to create account",
        };
      }

      // Get role and department data if role was assigned
      let roleData = null;
      if (roleId) {
        const { data } = await this.supabase
          .from("roles")
          .select("*, departments (*)")
          .eq("id", roleId)
          .single();
        roleData = data;
      }

      // Create initial session
      const sessionToken = await connection().then(() =>
        this.generateSessionToken()
      );
      const refreshToken = await connection().then(() =>
        this.generateSessionToken()
      );
      const expiryDate = new Date();
      expiryDate.setHours(expiryDate.getHours() + 24);

      const { data: sessionData, error: sessionError } = await this.supabase
        .from("sessions")
        .insert({
          account_id: accountData.id,
          token: sessionToken,
          refresh_token: refreshToken,
          expiry: expiryDate.toISOString(),
          status: "ACTIVE",
        })
        .select()
        .single();

      if (sessionError) {
        return {
          user: null,
          session: null,
          error: "Failed to create session",
        };
      }

      // Build auth user object
      const authUser: AuthUser = {
        id: userData.id,
        accountId: accountData.id,
        name: userData.name,
        email: accountData.email,
        phone: userData.phone,
        location: userData.location,
        avatar: userData.avatar,
        status: userData.status!,
        role: roleData
          ? {
              id: roleData.id,
              name: roleData.name,
              department: {
                id: roleData.departments!.id,
                name: roleData.departments!.name,
              },
              permissions: (roleData as any).permissions || {},
            }
          : null,
      };

      // Store session in localStorage
      setCookie("auth_session", sessionToken);

      return {
        user: authUser,
        session: sessionData,
        error: null,
      };
    } catch (error: any) {
      return {
        user: null,
        session: null,
        error: error.message || "Registration failed",
      };
    }
  }

  async signOut(): Promise<{ error: string | null }> {
    try {
      const sessionToken: string = getCookie("auth_session") as string;

      // Invalidate session in database
      await this.supabase
        .from("sessions")
        .update({ status: "INACTIVE", expiry: new Date().toISOString() })
        .eq("token", sessionToken);

      deleteCookie("auth_session");

      return { error: null };
    } catch (error: any) {
      return { error: error.message || "Sign out failed" };
    }
  }

  async getCurrentUser(): Promise<AuthUser | null> {
    try {
      const sessionToken = getCookie("auth_session");
      if (!sessionToken) {
        return null;
      }

      // Validate session and get user data
      const { data: sessionData, error: sessionError } = await this.supabase
        .from("sessions")
        .select(
          `
          *,
          accounts (
            *,
            users (*),
            roles (
              *,
              departments (*)
            )
          )
        `
        )
        .eq("token", sessionToken)
        .eq("status", "ACTIVE")
        .gt("expiry", new Date().toISOString())
        .single();

      if (sessionError || !sessionData || !sessionData.accounts) {
        deleteCookie("auth_session");
        return null;
      }

      const account = sessionData.accounts;
      const user = account.users;
      const role = account.roles;

      if (!user || user.status !== "ACTIVE" || account.status !== "ACTIVE") {
        deleteCookie("auth_session");
        return null;
      }

      return {
        id: user.id,
        accountId: account.id,
        name: user.name,
        email: account.email,
        phone: user.phone,
        location: user.location,
        avatar: user.avatar,
        status: user.status || "ACTIVE",
        role: role
          ? {
              id: role.id,
              name: role.name,
              department: {
                id: role.departments!.id,
                name: role.departments!.name,
              },
              permissions: (role as any).permissions || {},
            }
          : null,
      };
    } catch (error) {
      deleteCookie("auth_session");
      return null;
    }
  }

  async sendPasswordResetEmail(
    email: string
  ): Promise<{ error: string | null }> {
    try {
      // Find account by email
      const { data: accountData, error: accountError } = await this.supabase
        .from("accounts")
        .select("id")
        .eq("email", email.toLowerCase().trim())
        .eq("status", "ACTIVE")
        .single();

      if (accountError || !accountData) {
        // Don't reveal if email exists or not for security
        return { error: null };
      }

      // Generate recovery token
      const resetToken = this.generateSessionToken();
      const expiryDate = new Date();
      expiryDate.setHours(expiryDate.getHours() + 1); // 1 hour expiry

      // Store recovery token
      const { error: recoveryError } = await this.supabase
        .from("recoveries")
        .insert({
          account_id: accountData.id,
          token: resetToken,
          expiry: expiryDate.toISOString(),
        });

      if (recoveryError) {
        return { error: "Failed to generate recovery token" };
      }

      // TODO: Send email with reset link containing resetToken
      // For now, we'll just log it (implement email service later)
      console.log(`Password reset token for ${email}: ${resetToken}`);

      return { error: null };
    } catch (error: any) {
      return { error: error.message || "Password reset failed" };
    }
  }

  async updatePassword(
    newPassword: string,
    resetToken?: string
  ): Promise<{ error: string | null }> {
    try {
      let accountId: string | null = null;

      if (resetToken) {
        // Password reset flow
        const { data: recoveryData, error: recoveryError } = await this.supabase
          .from("recoveries")
          .select("account_id")
          .eq("token", resetToken)
          .gt("expiry", new Date().toISOString())
          .single();

        if (recoveryError || !recoveryData) {
          return { error: "Invalid or expired reset token" };
        }

        accountId = recoveryData.account_id;

        // Delete used recovery token
        await this.supabase.from("recoveries").delete().eq("token", resetToken);
      } else {
        // Authenticated user changing password
        const user = await this.getCurrentUser();
        if (!user) {
          return { error: "Not authenticated" };
        }

        const { data: accountData } = await this.supabase
          .from("accounts")
          .select("id")
          .eq("user_id", user.id)
          .single();

        if (!accountData) {
          return { error: "Account not found" };
        }

        accountId = accountData.id;
      }

      if (!accountId) {
        return { error: "Account ID not found" };
      }

      // Hash new password
      const hashedPassword = await bcrypt.hash(newPassword, 12);

      // Update password
      const { error: updateError } = await this.supabase
        .from("accounts")
        .update({ password: hashedPassword })
        .eq("id", accountId);

      if (updateError) {
        return { error: "Failed to update password" };
      }

      // Invalidate all existing sessions for this account
      await this.supabase
        .from("sessions")
        .update({ status: "INACTIVE" })
        .eq("account_id", accountId);

      return { error: null };
    } catch (error: any) {
      return { error: error.message || "Password update failed" };
    }
  }

  async checkIfAnyAccountsExist(): Promise<boolean> {
    try {
      const { data, error } = await this.supabase
        .from("accounts")
        .select("id")
        .limit(1);

      return !error && data && data.length > 0;
    } catch (error) {
      return false;
    }
  }

  async createFirstAdminAccount({
    name,
    email,
    password,
    phone,
    location,
  }: {
    name: string;
    email: string;
    password: string;
    phone?: string;
    location?: string;
  }): Promise<AuthResult> {
    try {
      // Double check no accounts exist
      const accountsExist = await this.checkIfAnyAccountsExist();
      if (accountsExist) {
        return {
          user: null,
          session: null,
          error: "Admin account already exists",
        };
      }

      // Find the Administration department and System Administrator role
      const { data: adminRole, error: roleError } = await this.supabase
        .from("roles")
        .select(
          `
          *,
          departments (*)
        `
        )
        .eq("name", "System Administrator")
        .eq("status", "ACTIVE")
        .single();

      if (roleError || !adminRole) {
        return {
          user: null,
          session: null,
          error: "System Administrator role not found. Please contact support.",
        };
      }

      // Verify the role belongs to Administration department
      if (adminRole.departments?.name !== "Administration") {
        return {
          user: null,
          session: null,
          error:
            "System Administrator role is not in Administration department",
        };
      }

      // Now create the first admin user using the existing signUp method
      const result = await this.signUp({
        name,
        email,
        password,
        phone,
        location,
        roleId: adminRole.id,
      });

      return result;
    } catch (error: any) {
      return {
        user: null,
        session: null,
        error: error.message || "Failed to create first admin account",
      };
    }
  }

  async requestAccess({
    name,
    email,
    message,
  }: {
    name: string;
    email: string;
    message: string;
  }): Promise<{ error: string | null }> {
    try {
      // Store access request in notifications table for admin review
      const { error } = await this.supabase.from("notifications").insert({
        name: "Access Request",
        message: `New access request from ${name} (${email}): ${message}`,
        details: {
          type: "access_request",
          requestor_name: name,
          requestor_email: email,
          message: message,
          status: "pending",
        },
        status: "ACTIVE",
        associated_table: "access_requests",
        associated_id: uuidv4(),
      });

      if (error) {
        return { error: "Failed to submit access request" };
      }

      // TODO: Send email notification to admin

      return { error: null };
    } catch (error: any) {
      return { error: error.message || "Failed to submit access request" };
    }
  }

  async getDepartments(): Promise<Department[]> {
    const { data, error } = await this.supabase
      .from("departments")
      .select("*")
      .eq("status", "ACTIVE")
      .order("name");

    return data || [];
  }

  async getRolesByDepartment(departmentId: string): Promise<Role[]> {
    const { data, error } = await this.supabase
      .from("roles")
      .select("*")
      .eq("department_id", departmentId)
      .eq("status", "ACTIVE")
      .order("name");

    return data || [];
  }

  private async generateSessionToken(): Promise<string> {
    return (
      uuidv4() +
      "-" +
      Date.now().toString(36) +
      "-" +
      Math.random().toString(36).substr(2)
    );
  }

  // Session validation for middleware
  async validateSession(token: string): Promise<AuthUser | null> {
    try {
      const { data: sessionData } = await this.supabase
        .from("sessions")
        .select(
          `
          *,
          accounts (
            *,
            users (*),
            roles (
              *,
              departments (*)
            )
          )
        `
        )
        .eq("token", token)
        .eq("status", "ACTIVE")
        .gt("expiry", new Date().toISOString())
        .single();

      if (!sessionData || !sessionData.accounts) {
        return null;
      }

      const account = sessionData.accounts;
      const user = account.users;
      const role = account.roles;

      if (!user || user.status !== "ACTIVE" || account.status !== "ACTIVE") {
        return null;
      }

      return {
        id: user.id,
        accountId: account.id,
        name: user.name,
        email: account.email,
        phone: user.phone,
        location: user.location,
        avatar: user.avatar,
        status: user.status || "ACTIVE",
        role: role
          ? {
              id: role.id,
              name: role.name,
              department: {
                id: role.departments!.id,
                name: role.departments!.name,
              },
              permissions: (role as any).permissions || {},
            }
          : null,
      };
    } catch (error) {
      return null;
    }
  }
}

export const authService = new AuthService();
