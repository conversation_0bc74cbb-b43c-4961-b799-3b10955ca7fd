import { BaseService } from "../logistics/base/service";
import { ServiceResponse } from "../logistics/types";

// Cost Center KPI Interfaces
export interface CostCenterKPIs {
  totalRevenue: number;
  totalExpenses: number;
  profitMargin: number;
  netSurplus: number; // Revenue - Expenses
  transactionCount: number;
  ledgerCount: number;
  averageTransactionValue: number;
}

export interface TransactionSummary {
  totalCredit: number;
  totalDebit: number;
  netBalance: number; // Credit - Debit
  transactionCount: number;
  transactions: any[];
}

export interface LedgerSummary {
  totalCredit: number;
  totalDebit: number;
  netBalance: number;
  ledgerCount: number;
  activeLedgers: number;
  ledgers: any[];
}

export interface CostCenterChartData {
  revenueVsExpenses: Array<{ name: string; revenue: number; expenses: number }>;
  transactionDistribution: Array<{
    name: string;
    value: number;
    color: string;
  }>;
  ledgerPerformance: Array<{
    name: string;
    credit: number;
    debit: number;
    net: number;
  }>;
  monthlyTrends: Array<{
    month: string;
    revenue: number;
    expenses: number;
    profit: number;
  }>;
  entityComparison: Array<{
    entity: string;
    revenue: number;
    expenses: number;
    transactions: number;
  }>;
}

export interface CostCenterAnalysis {
  kpis: CostCenterKPIs;
  transactions: TransactionSummary;
  ledgers: LedgerSummary;
  charts: CostCenterChartData;
  entityInfo: {
    id: string;
    name: string;
    type: string;
    metadata?: Record<string, any>;
  };
}

export class CostCenterService extends BaseService<any, any, any> {
  protected tableName = "cost_center_analysis"; // Virtual table for analysis

  /**
   * Get comprehensive cost center analysis for a specific entity
   */
  async getCostCenterAnalysis(
    entityType: string,
    entityId: string,
    dateRange?: { from: string; to: string }
  ): Promise<ServiceResponse<CostCenterAnalysis>> {
    try {
      // Get entity information
      const entityInfo = await this.getEntityInfo(entityType, entityId);
      if (!entityInfo.success) {
        return {
          success: false,
          data: null,
          error: `Failed to fetch entity information: ${entityInfo.error}`,
        };
      }

      // Get associated ledgers
      const ledgersResult = await this.getEntityLedgers(
        entityType,
        entityId,
        dateRange
      );
      if (!ledgersResult.success) {
        return {
          success: false,
          data: null,
          error: `Failed to fetch ledgers: ${ledgersResult.error}`,
        };
      }

      // Get associated transactions
      const transactionsResult = await this.getEntityTransactions(
        ledgersResult.data.ledgers.map((l: any) => l.id),
        dateRange
      );
      if (!transactionsResult.success) {
        return {
          success: false,
          data: null,
          error: `Failed to fetch transactions: ${transactionsResult.error}`,
        };
      }

      // Calculate KPIs
      const kpis = this.calculateKPIs(
        ledgersResult.data,
        transactionsResult.data
      );

      // Generate chart data
      const charts = await this.generateChartData(
        entityType,
        entityId,
        ledgersResult.data,
        transactionsResult.data,
        dateRange
      );

      const analysis: CostCenterAnalysis = {
        kpis,
        transactions: transactionsResult.data,
        ledgers: ledgersResult.data,
        charts,
        entityInfo: entityInfo.data,
      };

      return {
        success: true,
        data: analysis,
        error: null,
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: error.message || "Failed to generate cost center analysis",
      };
    }
  }

  /**
   * Get entity information based on type and ID
   */
  private async getEntityInfo(
    entityType: string,
    entityId: string
  ): Promise<ServiceResponse<any>> {
    try {
      let query;
      let nameField = "name";

      switch (entityType) {
        case "customers":
          query = this.supabase
            .from("customers")
            .select("id, name, company_name, email, location, status")
            .eq("id", entityId)
            .single();
          break;
        case "batches":
          query = this.supabase
            .from("batches")
            .select(
              "id, batch_code, status, total_weight, freight_id, freights(name)"
            )
            .eq("id", entityId)
            .single();
          nameField = "batch_code";
          break;
        case "freights":
          query = this.supabase
            .from("freights")
            .select(
              "id, name, freight_number, type, origin, destination, status"
            )
            .eq("id", entityId)
            .single();
          break;
        default:
          return {
            success: false,
            data: null,
            error: `Unsupported entity type: ${entityType}`,
          };
      }

      const { data, error } = await query;

      if (error) {
        return {
          success: false,
          data: null,
          error: error.message,
        };
      }

      return {
        success: true,
        data: {
          id: data.id,
          name:
            data[nameField] ||
            data.name ||
            data.company_name ||
            `${entityType} ${data.id}`,
          type: entityType,
          metadata: data,
        },
        error: null,
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: error.message || "Failed to fetch entity information",
      };
    }
  }

  /**
   * Get ledgers associated with an entity
   */
  private async getEntityLedgers(
    entityType: string,
    entityId: string,
    dateRange?: { from: string; to: string }
  ): Promise<ServiceResponse<LedgerSummary>> {
    try {
      let query = this.supabase
        .from("ledgers")
        .select("*")
        .eq("associated_table", entityType)
        .eq("associated_id", entityId)
        .neq("status", "INACTIVE");

      if (dateRange) {
        query = query
          .gte("created_at", dateRange.from)
          .lte("created_at", dateRange.to);
      }

      const { data: ledgers, error } = await query;

      if (error) {
        return {
          success: false,
          data: null,
          error: error.message,
        };
      }

      // Calculate ledger summaries
      const totalCredit = ledgers.reduce(
        (sum, ledger) => sum + (ledger.total_credit || 0),
        0
      );
      const totalDebit = ledgers.reduce(
        (sum, ledger) => sum + (ledger.total_debit || 0),
        0
      );
      const activeLedgers = ledgers.filter((l) => l.status === "ACTIVE").length;

      const summary: LedgerSummary = {
        totalCredit,
        totalDebit,
        netBalance: totalCredit - totalDebit,
        ledgerCount: ledgers.length,
        activeLedgers,
        ledgers,
      };

      return {
        success: true,
        data: summary,
        error: null,
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: error.message || "Failed to fetch entity ledgers",
      };
    }
  }

  /**
   * Get transactions for specific ledgers
   */
  private async getEntityTransactions(
    ledgerIds: string[],
    dateRange?: { from: string; to: string }
  ): Promise<ServiceResponse<TransactionSummary>> {
    try {
      if (ledgerIds.length === 0) {
        return {
          success: true,
          data: {
            totalCredit: 0,
            totalDebit: 0,
            netBalance: 0,
            transactionCount: 0,
            transactions: [],
          },
          error: null,
        };
      }

      let query = this.supabase
        .from("transactions")
        .select(
          `
          *,
          ledgers (
            id,
            name,
            book_category
          )
        `
        )
        .in("ledger_id", ledgerIds)
        .neq("status", "INACTIVE");

      if (dateRange) {
        query = query
          .gte("created_at", dateRange.from)
          .lte("created_at", dateRange.to);
      }

      const { data: transactions, error } = await query;

      if (error) {
        return {
          success: false,
          data: null,
          error: error.message,
        };
      }

      // Calculate transaction summaries
      const totalCredit = transactions
        .filter((t) => t.type === "CREDIT")
        .reduce((sum, t) => sum + (t.amount || 0), 0);

      const totalDebit = transactions
        .filter((t) => t.type === "DEBIT")
        .reduce((sum, t) => sum + (t.amount || 0), 0);

      const summary: TransactionSummary = {
        totalCredit,
        totalDebit,
        netBalance: totalCredit - totalDebit,
        transactionCount: transactions.length,
        transactions,
      };

      return {
        success: true,
        data: summary,
        error: null,
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: error.message || "Failed to fetch entity transactions",
      };
    }
  }

  /**
   * Calculate KPIs from ledger and transaction data
   */
  private calculateKPIs(
    ledgerSummary: LedgerSummary,
    transactionSummary: TransactionSummary
  ): CostCenterKPIs {
    // Revenue is typically credit transactions from revenue ledgers
    const totalRevenue = transactionSummary.totalCredit;

    // Expenses are typically debit transactions from expense ledgers
    const totalExpenses = transactionSummary.totalDebit;

    // Calculate profit margin
    const profitMargin =
      totalRevenue > 0
        ? ((totalRevenue - totalExpenses) / totalRevenue) * 100
        : 0;

    // Net surplus/deficit
    const netSurplus = totalRevenue - totalExpenses;

    // Average transaction value
    const averageTransactionValue =
      transactionSummary.transactionCount > 0
        ? (transactionSummary.totalCredit + transactionSummary.totalDebit) /
          transactionSummary.transactionCount
        : 0;

    return {
      totalRevenue,
      totalExpenses,
      profitMargin,
      netSurplus,
      transactionCount: transactionSummary.transactionCount,
      ledgerCount: ledgerSummary.ledgerCount,
      averageTransactionValue,
    };
  }

  /**
   * Generate chart data for cost center analysis
   */
  private async generateChartData(
    entityType: string,
    entityId: string,
    ledgerSummary: LedgerSummary,
    transactionSummary: TransactionSummary,
    dateRange?: { from: string; to: string }
  ): Promise<CostCenterChartData> {
    // Revenue vs Expenses Chart
    const revenueVsExpenses = [
      {
        name: "Financial Overview",
        revenue: transactionSummary.totalCredit,
        expenses: transactionSummary.totalDebit,
      },
    ];

    // Transaction Distribution (Pie Chart)
    const transactionDistribution = [
      {
        name: "Credit Transactions",
        value: transactionSummary.totalCredit,
        color: "#10B981", // Green
      },
      {
        name: "Debit Transactions",
        value: transactionSummary.totalDebit,
        color: "#EF4444", // Red
      },
    ];

    // Ledger Performance
    const ledgerPerformance = ledgerSummary.ledgers.map((ledger: any) => ({
      name: ledger.name || `Ledger ${ledger.id}`,
      credit: ledger.total_credit || 0,
      debit: ledger.total_debit || 0,
      net: (ledger.total_credit || 0) - (ledger.total_debit || 0),
    }));

    // Monthly Trends (simplified for now)
    const monthlyTrends = [
      {
        month: "Current",
        revenue: transactionSummary.totalCredit,
        expenses: transactionSummary.totalDebit,
        profit: transactionSummary.totalCredit - transactionSummary.totalDebit,
      },
    ];

    // Entity Comparison (simplified for now)
    const entityComparison = [
      {
        entity: "Current Entity",
        revenue: transactionSummary.totalCredit,
        expenses: transactionSummary.totalDebit,
        transactions: transactionSummary.transactionCount,
      },
    ];

    return {
      revenueVsExpenses,
      transactionDistribution,
      ledgerPerformance,
      monthlyTrends,
      entityComparison,
    };
  }

  /**
   * Get cost center summary for multiple entities (for dashboard overview)
   */
  async getCostCenterSummary(
    entityType: string,
    entityIds: string[],
    dateRange?: { from: string; to: string }
  ): Promise<
    ServiceResponse<{
      totalRevenue: number;
      totalExpenses: number;
      totalProfit: number;
      entityCount: number;
      topPerformers: Array<{ id: string; name: string; profit: number }>;
    }>
  > {
    try {
      let totalRevenue = 0;
      let totalExpenses = 0;
      const performers = [];

      for (const entityId of entityIds) {
        const analysis = await this.getCostCenterAnalysis(
          entityType,
          entityId,
          dateRange
        );
        if (analysis.success && analysis.data) {
          totalRevenue += analysis.data.kpis.totalRevenue;
          totalExpenses += analysis.data.kpis.totalExpenses;
          performers.push({
            id: entityId,
            name: analysis.data.entityInfo.name,
            profit: analysis.data.kpis.netSurplus,
          });
        }
      }

      // Sort performers by profit
      const topPerformers = performers
        .sort((a, b) => b.profit - a.profit)
        .slice(0, 5);

      return {
        success: true,
        data: {
          totalRevenue,
          totalExpenses,
          totalProfit: totalRevenue - totalExpenses,
          entityCount: entityIds.length,
          topPerformers,
        },
        error: null,
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: error.message || "Failed to generate cost center summary",
      };
    }
  }

  /**
   * Get cost center insights with recommendations
   */
  async getCostCenterInsights(
    entityType: string,
    entityId: string,
    dateRange?: { from: string; to: string }
  ): Promise<
    ServiceResponse<{
      insights: string[];
      recommendations: string[];
      riskFactors: string[];
      performanceScore: number;
    }>
  > {
    try {
      const analysis = await this.getCostCenterAnalysis(
        entityType,
        entityId,
        dateRange
      );

      if (!analysis.success || !analysis.data) {
        return {
          success: false,
          data: null,
          error: "Failed to get analysis data for insights",
        };
      }

      const { kpis, transactions, ledgers } = analysis.data;
      const insights = [];
      const recommendations = [];
      const riskFactors = [];

      // Generate insights based on KPIs
      if (kpis.profitMargin > 20) {
        insights.push(
          `Excellent profit margin of ${kpis.profitMargin.toFixed(1)}%`
        );
      } else if (kpis.profitMargin > 10) {
        insights.push(`Good profit margin of ${kpis.profitMargin.toFixed(1)}%`);
      } else if (kpis.profitMargin > 0) {
        insights.push(`Low profit margin of ${kpis.profitMargin.toFixed(1)}%`);
        recommendations.push("Consider cost optimization strategies");
      } else {
        insights.push(
          `Operating at a loss with ${kpis.profitMargin.toFixed(1)}% margin`
        );
        riskFactors.push("Negative profit margin indicates financial stress");
        recommendations.push(
          "Urgent review of expenses and revenue streams needed"
        );
      }

      // Transaction analysis
      if (transactions.transactionCount > 50) {
        insights.push(
          `High transaction volume with ${transactions.transactionCount} transactions`
        );
      } else if (transactions.transactionCount < 10) {
        insights.push(
          `Low transaction activity with only ${transactions.transactionCount} transactions`
        );
        recommendations.push(
          "Consider strategies to increase transaction volume"
        );
      }

      // Ledger analysis
      if (ledgers.activeLedgers < ledgers.ledgerCount) {
        riskFactors.push(
          `${ledgers.ledgerCount - ledgers.activeLedgers} inactive ledgers detected`
        );
        recommendations.push("Review and activate or archive inactive ledgers");
      }

      // Calculate performance score (0-100)
      let performanceScore = 50; // Base score

      if (kpis.profitMargin > 0) performanceScore += 20;
      if (kpis.profitMargin > 10) performanceScore += 15;
      if (kpis.profitMargin > 20) performanceScore += 15;
      if (transactions.transactionCount > 20) performanceScore += 10;
      if (ledgers.activeLedgers === ledgers.ledgerCount) performanceScore += 10;

      performanceScore = Math.min(100, Math.max(0, performanceScore));

      return {
        success: true,
        data: {
          insights,
          recommendations,
          riskFactors,
          performanceScore,
        },
        error: null,
      };
    } catch (error: any) {
      return {
        success: false,
        data: null,
        error: error.message || "Failed to generate cost center insights",
      };
    }
  }
}

// Export service instance
export const costCenterService = new CostCenterService();
