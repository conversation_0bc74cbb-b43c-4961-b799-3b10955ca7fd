import type { CompanyInfo } from "./types/shared";
import { toast } from "sonner";

export function capitalize(val: string) {
  if (val?.length)
    return `${val?.slice(0, 1)}${val
      ?.slice(1, val.length)
      .toLowerCase()
      .replace(/-|_/, "  ")}`;
}

export function trimming(val: string, size = 54) {
  let capitalized: any = capitalize(val);

  return capitalized?.length > size
    ? `${capitalized?.slice(0, size)}...`
    : capitalized;
}

export const DEFAULT_COMPANY_CONFIG: CompanyInfo = {
  name: "Shamwaa Africa",
  logo: "/shamwaa-logo-black.png",
  address:
    "Mezzanine Floor, Victoria Place Building, Old Bagamoyo Road Dar es Salaam, Tanzania",
  phone:
    "+*********** 000 / +*********** 418 / +86 191 574 65832 / +86 135 0224 6489",
  email: "<EMAIL>",
  website: "www.shamwaa.com",
};

export const showToast = (
  message: string,
  type: "success" | "error" | "warning" | "info" = "info"
) => {
  switch (type) {
    case "success":
      toast.success(message);
      break;
    case "error":
      toast.error(message);
      break;
    case "warning":
      toast.warning(message);
      break;
    case "info":
    default:
      toast.info(message);
      break;
  }
};
