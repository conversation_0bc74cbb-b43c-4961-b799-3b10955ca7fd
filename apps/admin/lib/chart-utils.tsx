import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart as <PERSON><PERSON><PERSON>,
} from "lucide-react";

interface NoDataAvailableProps {
  title?: string;
  description?: string;
  chartType?: "line" | "area" | "bar" | "pie";
  height?: number;
  className?: string;
}

export const NoDataAvailable: React.FC<NoDataAvailableProps> = ({
  title = "No Data Available",
  description = "There is no data to display at this time.",
  chartType = "bar",
  height = 280,
  className = "",
}) => {
  const getIcon = () => {
    switch (chartType) {
      case "line":
      case "area":
        return <AreaIcon size={32} className="text-gray-300" />;
      case "pie":
        return <PieIcon size={32} className="text-gray-300" />;
      case "bar":
      default:
        return <BarChart3 size={32} className="text-gray-300" />;
    }
  };

  return (
    <div
      className={`flex flex-col items-center justify-center bg-gray-50/50 border-2 border-dashed border-gray-200 rounded-lg ${className}`}
      style={{ height: `${height}px` }}
    >
      <div className="flex flex-col items-center space-y-3 p-6 text-center">
        {getIcon()}
        <div>
          <h3 className="text-sm font-medium text-gray-900">{title}</h3>
          <p className="text-sm text-gray-500 mt-1 max-w-sm">{description}</p>
        </div>
      </div>
    </div>
  );
};

// Utility functions to check if data is sufficient for charts
export const isDataSufficient = {
  // For line/area charts - need at least 2 data points to show a meaningful trend
  lineArea: (data: any[]): boolean => {
    return (
      Array.isArray(data) &&
      data.length >= 2 &&
      data.some((item) => typeof item.value === "number" && item.value > 0)
    );
  },

  // For pie charts - need at least 1 data point with positive value
  pie: (data: any[]): boolean => {
    return (
      Array.isArray(data) &&
      data.length >= 1 &&
      data.some((item) => typeof item.value === "number" && item.value > 0)
    );
  },

  // For bar charts - need at least 1 data point
  bar: (data: any[]): boolean => {
    return (
      Array.isArray(data) &&
      data.length >= 1 &&
      data.some((item) => {
        // Check if any numeric property has a value > 0
        return Object.values(item).some(
          (value) => typeof value === "number" && value > 0
        );
      })
    );
  },

  // For tables/lists - need at least 1 item
  table: (data: any[]): boolean => {
    return Array.isArray(data) && data.length > 0;
  },
};

// Helper to get appropriate no data message based on chart type and context
export const getNoDataMessage = (chartType: string, context?: string) => {
  const messages = {
    shipments: {
      title: "No Shipment Data",
      description: "Start creating shipments to see analytics and trends here.",
    },
    cargo: {
      title: "No Cargo Data",
      description: "Add cargo to your system to view distribution insights.",
    },
    customers: {
      title: "No Customer Data",
      description: "Add customers to your system to see customer analytics.",
    },
    performance: {
      title: "No Performance Data",
      description:
        "Performance metrics will appear once you have active deliveries.",
    },
    revenue: {
      title: "No Revenue Data",
      description:
        "Revenue trends will appear once you start processing transactions.",
    },
    expenses: {
      title: "No Expense Data",
      description:
        "Expense analytics will show when you have expense transactions.",
    },
    budget: {
      title: "No Budget Data",
      description: "Set up budget categories to track spending performance.",
    },
    default: {
      title: "No Data Available",
      description: "Data will appear here once you start using the system.",
    },
  };

  return messages[context as keyof typeof messages] || messages.default;
};

// Wrapper component that conditionally renders chart or no data state
interface ChartWrapperProps {
  data: any[];
  chartType: "line" | "area" | "bar" | "pie";
  context?: string;
  height?: number;
  children: React.ReactNode | React.ReactElement;
  className?: string;
}

export const ChartWrapper: React.FC<ChartWrapperProps> = ({
  data,
  chartType,
  context,
  height = 280,
  children,
  className = "",
}) => {
  const checkDataSufficiency = () => {
    switch (chartType) {
      case "line":
      case "area":
        return isDataSufficient.lineArea(data);
      case "pie":
        return isDataSufficient.pie(data);
      case "bar":
        return isDataSufficient.bar(data);
      default:
        return isDataSufficient.table(data);
    }
  };

  const hasSufficientData = checkDataSufficiency();
  const noDataMessage = getNoDataMessage(chartType, context);

  if (!hasSufficientData) {
    return (
      <NoDataAvailable
        title={noDataMessage.title}
        description={noDataMessage.description}
        chartType={chartType}
        height={height}
        className={className}
      />
    );
  }

  return <>{children}</>;
};
