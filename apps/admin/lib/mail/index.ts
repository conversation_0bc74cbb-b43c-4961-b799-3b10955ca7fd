"use server";

import {
  Mailer,
  EmailConfig,
  QuickTemplates,
  SendEmailOptions,
} from "@workspace/mail";

const defaultConfig: EmailConfig = {
  host: process.env.SMTP_HOST!, // Placeholder: Gmail SMTP
  port: 465,
  secure: true, // true for 465, false for other ports
  auth: {
    user: process.env.SMTP_USER!,
    pass: process.env.SMTP_PASS!,
  },
  from: {
    name: process.env.SMTP_FROM_NAME!,
    email: process.env.SMTP_FROM_EMAIL!,
  },
};

const mail = new Mailer(defaultConfig);

export async function sendMail(options: SendEmailOptions) {
  "use server";

  try {
    options.from = defaultConfig.from.email; // default sender
    await mail.sendEmail(options);
  } catch (error: any) {
    return error;
  }
}

export async function getWelcomeUserTemplate(
  name: string,
  email: string,
  role: string
) {
  "use server";

  try {
    let loginURL = `${window.location.origin}/login?email=${email}&password=TempPassword123!`;
    return QuickTemplates.welcomeUser(name, email, loginURL, role);
  } catch (error) {
    return error;
  }
}
