"use client";

import {
  sendSMS,
  sendWhatsApp,
  notifyShipmentCreated,
  notifyShipmentInTransit,
  notifyShipmentDelivered,
  notifyShipmentDelayed,
  notifyInvoiceGenerated,
  notifyRelease<PERSON>uthorization,
  notifyCargoDamage,
  notifySecurity<PERSON><PERSON><PERSON>,
} from "@/lib/messaging";

import { sendMail } from "@/lib/mail";
import {
  type CargoWithRelations,
  type Customer,
  type StatusEnum,
  type Invoice,
} from "@/lib/logistics";

// Local type definitions for missing exports
interface MessageResponse {
  success: boolean;
  messageId?: string;
  status?: string;
  to?: string;
  from?: string;
  error?: string;
}

interface SendEmailOptions {
  from: string;
  to: string;
  subject: string;
  html?: string;
  text?: string;
  attachments?: Array<{
    filename: string;
    content?: Buffer;
    path: string;
    contentType: string;
  }>;
}

// Import and re-export types from separate types file
import type {
  CustomerContact,
  AlertOptions,
  AlertResult,
  CargoStatusData,
  ShipmentStatusData,
  InvoiceAlertData,
  ReleaseDocumentData,
  EmergencyAlertData,
  DocumentAttachment,
  BulkAlertResult,
  AlertType,
  CustomerValidationResult,
} from "./types";

export type {
  CustomerContact,
  AlertOptions,
  AlertResult,
  CargoStatusData,
  ShipmentStatusData,
  InvoiceAlertData,
  ReleaseDocumentData,
  EmergencyAlertData,
  DocumentAttachment,
  BulkAlertResult,
  AlertType,
  CustomerValidationResult,
};

/**
 * Customer Alerts Service
 *
 * Combines messaging and mail functionality for scenario-specific customer notifications.
 * Handles cargo & shipment status updates, invoice & release document sharing.
 */
class CustomerAlertsService {
  /**
   * Send cargo status update alert to customer
   */
  static async sendCargoStatusUpdate(
    customer: CustomerContact,
    cargo: {
      trackingNumber: string;
      status: StatusEnum;
      currentLocation?: string;
      estimatedDelivery?: string;
      particular?: string;
    },
    options: AlertOptions = {}
  ): Promise<AlertResult> {
    const results: AlertResult = { success: false, errors: [] };

    try {
      const { trackingNumber, status, currentLocation, estimatedDelivery } =
        cargo;
      const urgency = options.urgency || "medium";
      const useWhatsApp = urgency === "high" || urgency === "critical";

      // Determine message based on status
      let messageResult: MessageResponse | undefined;

      switch (status) {
        case "IN_TRANSIT":
          if (currentLocation) {
            messageResult = await notifyShipmentInTransit(
              customer.phone!,
              trackingNumber,
              currentLocation,
              useWhatsApp
            );
          }
          break;

        case "DELIVERED":
          if (estimatedDelivery) {
            messageResult = await notifyShipmentDelivered(
              customer.phone!,
              trackingNumber,
              estimatedDelivery,
              useWhatsApp
            );
          }
          break;

        case "DELAYED" as any:
          if (estimatedDelivery) {
            messageResult = await notifyShipmentDelayed(
              customer.phone!,
              trackingNumber,
              estimatedDelivery,
              options.customMessage || "Unexpected delay",
              useWhatsApp
            );
          }
          break;

        default:
          // Generic status update
          const message = `Cargo Update: Your shipment ${trackingNumber} status has been updated to ${status}. ${options.customMessage || ""}`;
          messageResult = useWhatsApp
            ? await sendWhatsApp({ to: customer.phone!, message })
            : await sendSMS({ to: customer.phone!, message });
      }

      if (messageResult) {
        if (useWhatsApp) {
          results.whatsappResult = messageResult;
        } else {
          results.smsResult = messageResult;
        }
      }

      // Send email notification if customer has email and prefers email or both
      if (
        customer.email &&
        (customer.preferredMethod === "email" ||
          customer.preferredMethod === "both")
      ) {
        const emailResult = await this.sendCargoStatusEmail(
          customer,
          cargo,
          options
        );
        results.emailResult = emailResult;
      }

      // Determine overall success
      const hasSuccessfulMessage = messageResult?.success;
      const hasSuccessfulEmail =
        !customer.email || results.emailResult?.success !== false;
      results.success = hasSuccessfulMessage || hasSuccessfulEmail;
    } catch (error: any) {
      results.errors?.push(
        `Failed to send cargo status update: ${error.message}`
      );
    }

    return results;
  }

  /**
   * Send shipment status update alert
   */
  static async sendShipmentStatusUpdate(
    customer: CustomerContact,
    shipment: {
      trackingNumber: string;
      batchCode?: string;
      origin: string;
      destination: string;
      status: string;
      estimatedArrival?: string;
    },
    options: AlertOptions = {}
  ): Promise<AlertResult> {
    const results: AlertResult = { success: false, errors: [] };

    try {
      const { trackingNumber, origin, destination, status } = shipment;
      const urgency = options.urgency || "medium";
      const useWhatsApp = urgency === "high" || urgency === "critical";

      // Send SMS/WhatsApp notification
      if (customer.phone) {
        let messageResult: MessageResponse;

        if (status === "CREATED" || status === "REGISTERED") {
          messageResult = await notifyShipmentCreated(
            customer.phone,
            trackingNumber,
            origin,
            destination,
            useWhatsApp
          );
        } else {
          const message = `Shipment Update: ${trackingNumber} from ${origin} to ${destination} is now ${status}. ${options.customMessage || ""}`;
          messageResult = useWhatsApp
            ? await sendWhatsApp({ to: customer.phone, message })
            : await sendSMS({ to: customer.phone, message });
        }

        if (useWhatsApp) {
          results.whatsappResult = messageResult;
        } else {
          results.smsResult = messageResult;
        }
      }

      // Send email notification
      if (
        customer.email &&
        (customer.preferredMethod === "email" ||
          customer.preferredMethod === "both")
      ) {
        const emailResult = await this.sendShipmentStatusEmail(
          customer,
          shipment,
          options
        );
        results.emailResult = emailResult;
      }

      results.success = true;
    } catch (error: any) {
      results.errors?.push(
        `Failed to send shipment status update: ${error.message}`
      );
    }

    return results;
  }

  /**
   * Send invoice notification with document sharing
   */
  static async sendInvoiceAlert(
    customer: CustomerContact,
    invoice: {
      invoiceNumber: string;
      totalAmount: number;
      currency?: string;
      dueDate?: string;
      status: string;
      trackingNumber?: string;
    },
    document?: {
      path: string; // Signed URL to fetch document content
      fileName: string;
      content?: Buffer; // Optional - if not provided, will fetch from path
    },
    options: AlertOptions = {}
  ): Promise<AlertResult> {
    const results: AlertResult = { success: false, errors: [] };

    try {
      const { invoiceNumber, totalAmount, currency = "USD", dueDate } = invoice;
      const amount = `${currency} ${totalAmount.toFixed(2)}`;

      // Send SMS/WhatsApp notification
      if (customer.phone) {
        const messageResult = await notifyInvoiceGenerated(
          customer.phone,
          invoiceNumber,
          amount,
          dueDate || "Not specified",
          customer.preferredMethod === "whatsapp",
          options.documentUrl
        );

        if (customer.preferredMethod === "whatsapp") {
          results.whatsappResult = messageResult;
        } else {
          results.smsResult = messageResult;
        }
      }

      // Send email with invoice attachment
      if (customer.email) {
        const emailResult = await this.sendInvoiceEmail(
          customer,
          invoice,
          document,
          options
        );
        results.emailResult = emailResult;
      }

      results.success = true;
    } catch (error: any) {
      results.errors?.push(`Failed to send invoice alert: ${error.message}`);
    }

    return results;
  }

  /**
   * Send release document sharing alert
   */
  static async sendReleaseDocumentAlert(
    customer: CustomerContact,
    releaseData: {
      releaseCode: string;
      trackingNumber: string;
      instructions?: string;
      qrCodeData?: string;
    },
    document?: {
      path: string; // Signed URL to fetch document content
      fileName: string;
      content?: Buffer; // Optional - if not provided, will fetch from path
    },
    options: AlertOptions = {}
  ): Promise<AlertResult> {
    const results: AlertResult = { success: false, errors: [] };

    try {
      const {
        releaseCode,
        trackingNumber,
        instructions = "Please bring valid ID for cargo collection",
      } = releaseData;

      // Send SMS/WhatsApp notification with QR code
      if (customer.phone) {
        const messageResult = await notifyReleaseAuthorization(
          customer.phone,
          releaseCode,
          trackingNumber,
          instructions,
          customer.preferredMethod === "whatsapp",
          options.documentUrl
        );

        if (customer.preferredMethod === "whatsapp") {
          results.whatsappResult = messageResult;
        } else {
          results.smsResult = messageResult;
        }
      }

      // Send email with release document attachment
      if (customer.email) {
        const emailResult = await this.sendReleaseDocumentEmail(
          customer,
          releaseData,
          document,
          options
        );
        results.emailResult = emailResult;
      }

      results.success = true;
    } catch (error: any) {
      results.errors?.push(
        `Failed to send release document alert: ${error.message}`
      );
    }

    return results;
  }

  /**
   * Send emergency/urgent alerts
   */
  static async sendEmergencyAlert(
    customer: CustomerContact,
    alert: {
      type: "damage" | "security" | "delay" | "custom";
      trackingNumber: string;
      message: string;
      location?: string;
      contactNumber?: string;
    },
    options: AlertOptions = {}
  ): Promise<AlertResult> {
    const results: AlertResult = { success: false, errors: [] };

    try {
      const { type, trackingNumber, message, location, contactNumber } = alert;

      // Emergency alerts always use WhatsApp for better delivery
      if (customer.phone) {
        let messageResult: MessageResponse;

        if (type === "damage" && contactNumber) {
          messageResult = await notifyCargoDamage(
            customer.phone,
            trackingNumber,
            message,
            contactNumber,
            true
          );
        } else if (type === "security" && location) {
          messageResult = await notifySecurityAlert(
            customer.phone,
            trackingNumber,
            message,
            location,
            true
          );
        } else {
          // Custom emergency message
          const urgentMessage = `🚨 URGENT: ${message} - Tracking: ${trackingNumber}. Contact us immediately if you have questions.`;
          messageResult = await sendWhatsApp({
            to: customer.phone,
            message: urgentMessage,
          });
        }

        results.whatsappResult = messageResult;
      }

      // Send urgent email
      if (customer.email) {
        const emailResult = await this.sendEmergencyEmail(
          customer,
          alert,
          options
        );
        results.emailResult = emailResult;
      }

      results.success = true;
    } catch (error: any) {
      results.errors?.push(`Failed to send emergency alert: ${error.message}`);
    }

    return results;
  }

  /**
   * Send cargo status update email
   */
  private static async sendCargoStatusEmail(
    customer: CustomerContact,
    cargo: {
      trackingNumber: string;
      status: StatusEnum;
      currentLocation?: string;
      estimatedDelivery?: string;
      particular?: string;
    },
    options: AlertOptions
  ) {
    const {
      trackingNumber,
      status,
      currentLocation,
      estimatedDelivery,
      particular,
    } = cargo;

    const emailOptions: SendEmailOptions = {
      from: "", // Will be set by sendMail function
      to: customer.email!,
      subject: `Cargo Status Update - ${trackingNumber}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0;">
            <h2 style="margin: 0; font-size: 24px;">Cargo Status Update</h2>
          </div>

          <div style="background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px; border: 1px solid #e9ecef;">
            <div style="background: white; padding: 20px; border-radius: 6px; margin-bottom: 20px;">
              <h3 style="color: #333; margin-top: 0;">Tracking Number: ${trackingNumber}</h3>
              <div style="background: #e3f2fd; padding: 15px; border-radius: 4px; margin: 15px 0;">
                <p style="margin: 0; font-size: 18px; font-weight: bold; color: #1976d2;">
                  Status: ${status}
                </p>
              </div>

              ${particular ? `<p><strong>Item:</strong> ${particular}</p>` : ""}
              ${currentLocation ? `<p><strong>Current Location:</strong> ${currentLocation}</p>` : ""}
              ${estimatedDelivery ? `<p><strong>Estimated Delivery:</strong> ${estimatedDelivery}</p>` : ""}
              ${options.customMessage ? `<p><strong>Additional Information:</strong> ${options.customMessage}</p>` : ""}
            </div>

            ${
              options.trackingUrl
                ? `
              <div style="text-align: center; margin: 20px 0;">
                <a href="${options.trackingUrl}"
                   style="background-color: #28a745; color: white; padding: 12px 24px;
                          text-decoration: none; border-radius: 4px; display: inline-block;">
                  Track Your Cargo
                </a>
              </div>
            `
                : ""
            }

            <p style="color: #666; font-size: 14px; margin-top: 20px;">
              If you have any questions, please contact our customer service team.
            </p>
          </div>
        </div>
      `,
      text: `
        Cargo Status Update

        Tracking Number: ${trackingNumber}
        Status: ${status}
        ${particular ? `Item: ${particular}` : ""}
        ${currentLocation ? `Current Location: ${currentLocation}` : ""}
        ${estimatedDelivery ? `Estimated Delivery: ${estimatedDelivery}` : ""}
        ${options.customMessage ? `Additional Information: ${options.customMessage}` : ""}

        ${options.trackingUrl ? `Track your cargo: ${options.trackingUrl}` : ""}

        If you have any questions, please contact our customer service team.
      `,
    };

    return await sendMail(emailOptions);
  }

  /**
   * Send shipment status update email
   */
  private static async sendShipmentStatusEmail(
    customer: CustomerContact,
    shipment: {
      trackingNumber: string;
      batchCode?: string;
      origin: string;
      destination: string;
      status: string;
      estimatedArrival?: string;
    },
    options: AlertOptions
  ) {
    const {
      trackingNumber,
      batchCode,
      origin,
      destination,
      status,
      estimatedArrival,
    } = shipment;

    const emailOptions: SendEmailOptions = {
      from: "", // Will be set by sendMail function
      to: customer.email!,
      subject: `Shipment Update - ${trackingNumber}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0;">
            <h2 style="margin: 0; font-size: 24px;">Shipment Status Update</h2>
          </div>

          <div style="background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px; border: 1px solid #e9ecef;">
            <div style="background: white; padding: 20px; border-radius: 6px;">
              <h3 style="color: #333; margin-top: 0;">Tracking: ${trackingNumber}</h3>
              ${batchCode ? `<p><strong>Batch:</strong> ${batchCode}</p>` : ""}

              <div style="display: flex; align-items: center; margin: 20px 0;">
                <div style="flex: 1; text-align: center;">
                  <div style="background: #e8f5e8; padding: 10px; border-radius: 4px;">
                    <strong>${origin}</strong>
                  </div>
                </div>
                <div style="flex: 0 0 50px; text-align: center;">
                  <span style="font-size: 20px;">→</span>
                </div>
                <div style="flex: 1; text-align: center;">
                  <div style="background: #e8f5e8; padding: 10px; border-radius: 4px;">
                    <strong>${destination}</strong>
                  </div>
                </div>
              </div>

              <div style="background: #e3f2fd; padding: 15px; border-radius: 4px; margin: 15px 0;">
                <p style="margin: 0; font-size: 18px; font-weight: bold; color: #1976d2;">
                  Status: ${status}
                </p>
              </div>

              ${estimatedArrival ? `<p><strong>Estimated Arrival:</strong> ${estimatedArrival}</p>` : ""}
              ${options.customMessage ? `<p><strong>Additional Information:</strong> ${options.customMessage}</p>` : ""}
            </div>
          </div>
        </div>
      `,
    };

    return await sendMail(emailOptions);
  }

  /**
   * Send invoice email with attachment
   */
  private static async sendInvoiceEmail(
    customer: CustomerContact,
    invoice: {
      invoiceNumber: string;
      totalAmount: number;
      currency?: string;
      dueDate?: string;
      status: string;
      trackingNumber?: string;
    },
    document?: {
      path: string; // Signed URL to fetch document content
      fileName: string;
      content?: Buffer; // Optional - if not provided, will fetch from path
    },
    options: AlertOptions = {}
  ) {
    const {
      invoiceNumber,
      totalAmount,
      currency = "USD",
      dueDate,
      trackingNumber,
    } = invoice;

    const emailOptions: SendEmailOptions = {
      from: "", // Will be set by sendMail function
      to: customer.email!,
      subject: `Invoice ${invoiceNumber} - ${currency} ${totalAmount.toFixed(2)}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0;">
            <h2 style="margin: 0; font-size: 24px;">Invoice Generated</h2>
          </div>

          <div style="background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px; border: 1px solid #e9ecef;">
            <div style="background: white; padding: 20px; border-radius: 6px;">
              <h3 style="color: #333; margin-top: 0;">Invoice: ${invoiceNumber}</h3>
              ${trackingNumber ? `<p><strong>Tracking Number:</strong> ${trackingNumber}</p>` : ""}

              <div style="background: #fff3cd; padding: 15px; border-radius: 4px; margin: 15px 0; border-left: 4px solid #ffc107;">
                <p style="margin: 0; font-size: 20px; font-weight: bold; color: #856404;">
                  Amount: ${currency} ${totalAmount.toFixed(2)}
                </p>
                ${dueDate ? `<p style="margin: 5px 0 0 0; color: #856404;">Due Date: ${dueDate}</p>` : ""}
              </div>

              <p>Please find your invoice attached to this email. You can make payment using the following methods:</p>
              <ul>
                <li>Bank Transfer</li>
                <li>Mobile Money</li>
                <li>Cash Payment at our office</li>
              </ul>

              ${
                document?.path
                  ? `
                <div style="text-align: center; margin: 20px 0;">
                  <a href="${document?.path}"
                     style="background-color: #28a745; color: white; padding: 12px 24px;
                            text-decoration: none; border-radius: 4px; display: inline-block;">
                    View Invoice Online
                  </a>
                </div>
              `
                  : ""
              }
            </div>
          </div>
        </div>
      `,
      attachments: [
        {
          filename: document?.fileName || "invoice.pdf",
          path: document?.path || "",
          contentType: "application/pdf",
        },
      ],
    };

    return await sendMail(emailOptions);
  }

  /**
   * Send release document email with attachment
   */
  private static async sendReleaseDocumentEmail(
    customer: CustomerContact,
    releaseData: {
      releaseCode: string;
      trackingNumber: string;
      instructions?: string;
      qrCodeData?: string;
    },
    document?: {
      path: string; // Signed URL to fetch document content
      fileName: string;
      content?: Buffer; // Optional - if not provided, will fetch from path
    },
    options: AlertOptions = {}
  ) {
    const { releaseCode, trackingNumber, instructions } = releaseData;

    const emailOptions: SendEmailOptions = {
      from: "", // Will be set by sendMail function
      to: customer.email!,
      subject: `Release Authorization - ${trackingNumber}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #10ac84 0%, #1dd1a1 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0;">
            <h2 style="margin: 0; font-size: 24px;">🎉 Cargo Ready for Collection</h2>
          </div>

          <div style="background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px; border: 1px solid #e9ecef;">
            <div style="background: white; padding: 20px; border-radius: 6px;">
              <h3 style="color: #333; margin-top: 0;">Release Authorization</h3>

              <div style="background: #d4edda; padding: 15px; border-radius: 4px; margin: 15px 0; border-left: 4px solid #28a745;">
                <p style="margin: 0; font-size: 16px; color: #155724;">
                  <strong>Tracking Number:</strong> ${trackingNumber}
                </p>
                <p style="margin: 5px 0 0 0; font-size: 18px; font-weight: bold; color: #155724;">
                  <strong>Release Code:</strong> ${releaseCode}
                </p>
              </div>

              <div style="background: #fff3cd; padding: 15px; border-radius: 4px; margin: 15px 0;">
                <h4 style="margin: 0 0 10px 0; color: #856404;">Collection Instructions:</h4>
                <p style="margin: 0; color: #856404;">
                  ${instructions || "Please bring valid ID and this release document for cargo collection."}
                </p>
              </div>

              <p><strong>Important:</strong> Please present this release authorization and a valid ID when collecting your cargo.</p>

              ${
                document?.path
                  ? `
                <div style="text-align: center; margin: 20px 0;">
                  <a href="${document?.path}"
                     style="background-color: #28a745; color: white; padding: 12px 24px;
                            text-decoration: none; border-radius: 4px; display: inline-block;">
                    View Release Document
                  </a>
                </div>
              `
                  : ""
              }
            </div>
          </div>
        </div>
      `,
      attachments: [
        {
          filename: document?.fileName || "release-authorization.pdf",
          path: document?.path || "",
          contentType: "application/pdf",
        },
      ],
    };

    return await sendMail(emailOptions);
  }

  /**
   * Send emergency alert email
   */
  private static async sendEmergencyEmail(
    customer: CustomerContact,
    alert: {
      type: "damage" | "security" | "delay" | "custom";
      trackingNumber: string;
      message: string;
      location?: string;
      contactNumber?: string;
    },
    _options: AlertOptions
  ) {
    const { type, trackingNumber, message, location, contactNumber } = alert;

    const emailOptions: SendEmailOptions = {
      from: "", // Will be set by sendMail function
      to: customer.email!,
      subject: `🚨 URGENT: ${type.toUpperCase()} Alert - ${trackingNumber}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #ff3838 0%, #ff6b6b 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0;">
            <h2 style="margin: 0; font-size: 24px;">🚨 URGENT ALERT</h2>
          </div>

          <div style="background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px; border: 1px solid #e9ecef;">
            <div style="background: white; padding: 20px; border-radius: 6px;">
              <div style="background: #f8d7da; padding: 15px; border-radius: 4px; margin: 15px 0; border-left: 4px solid #dc3545;">
                <h3 style="margin: 0 0 10px 0; color: #721c24;">Alert Type: ${type.toUpperCase()}</h3>
                <p style="margin: 0; color: #721c24;"><strong>Tracking:</strong> ${trackingNumber}</p>
                ${location ? `<p style="margin: 5px 0 0 0; color: #721c24;"><strong>Location:</strong> ${location}</p>` : ""}
              </div>

              <div style="background: #fff3cd; padding: 15px; border-radius: 4px; margin: 15px 0;">
                <h4 style="margin: 0 0 10px 0; color: #856404;">Details:</h4>
                <p style="margin: 0; color: #856404;">${message}</p>
              </div>

              ${
                contactNumber
                  ? `
                <div style="text-align: center; margin: 20px 0;">
                  <p style="margin: 0; font-size: 16px; color: #333;">
                    <strong>Emergency Contact:</strong>
                    <a href="tel:${contactNumber}" style="color: #dc3545; text-decoration: none;">
                      ${contactNumber}
                    </a>
                  </p>
                </div>
              `
                  : ""
              }

              <p style="color: #721c24; font-weight: bold;">
                Please contact us immediately if you have any questions or concerns.
              </p>
            </div>
          </div>
        </div>
      `,
    };

    return await sendMail(emailOptions);
  }
}

/**
 * Utility functions for customer alerts
 */

/**
 * Transform customer data to CustomerContact format
 */
export async function transformCustomerToContact(
  customer: Customer,
  preferredMethod: "sms" | "whatsapp" | "email" | "both" = "both"
): Promise<CustomerContact> {
  return {
    id: customer.id,
    name: customer.name,
    email: customer.email || undefined,
    phone: customer.phone || undefined,
    preferredMethod,
    language: "en", // Default to English, can be enhanced with customer preferences
  };
}

/**
 * Validate customer contact information
 */
export async function validateCustomerContact(
  customer: CustomerContact
): Promise<{
  isValid: boolean;
  errors: string[];
}> {
  const errors: string[] = [];

  if (!customer.name) {
    errors.push("Customer name is required");
  }

  if (
    customer.preferredMethod === "email" ||
    customer.preferredMethod === "both"
  ) {
    if (!customer.email) {
      errors.push("Email is required for email notifications");
    }
  }

  if (
    customer.preferredMethod === "sms" ||
    customer.preferredMethod === "whatsapp" ||
    customer.preferredMethod === "both"
  ) {
    if (!customer.phone) {
      errors.push("Phone number is required for SMS/WhatsApp notifications");
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Convenience function to send cargo status update from CargoWithRelations
 */
export async function sendCargoStatusUpdateFromCargo(
  cargo: CargoWithRelations,
  options: AlertOptions = {}
): Promise<AlertResult> {
  if (!cargo.customers) {
    return {
      success: false,
      errors: ["No customer information found for cargo"],
    };
  }

  const customer = await transformCustomerToContact(cargo.customers);
  const validation = await validateCustomerContact(customer);

  if (!validation.isValid) {
    return {
      success: false,
      errors: validation.errors,
    };
  }

  return await CustomerAlertsService.sendCargoStatusUpdate(
    customer,
    {
      trackingNumber: cargo.tracking_number || "Unknown",
      status: (cargo.status || "PENDING") as StatusEnum,
      particular: cargo.particular || undefined,
    },
    options
  );
}

/**
 * Convenience function to send invoice alert from Invoice data
 */
export async function sendInvoiceAlertFromInvoice(
  invoice: Invoice,
  customer: Customer,
  document?: {
    path: string; // Signed URL to fetch document content
    fileName: string;
    content?: Buffer; // Optional - if not provided, will fetch from path
  },
  options: AlertOptions = {}
): Promise<AlertResult> {
  const customerContact = await transformCustomerToContact(customer);
  const validation = await validateCustomerContact(customerContact);

  if (!validation.isValid) {
    return {
      success: false,
      errors: validation.errors,
    };
  }

  return await CustomerAlertsService.sendInvoiceAlert(
    customerContact,
    {
      invoiceNumber: invoice.invoiceNumber,
      totalAmount: invoice.totalAmount,
      currency: "USD", // Can be enhanced with invoice currency
      dueDate: invoice.dueDate || undefined,
      status: invoice.status,
    },
    document,
    options
  );
}

/**
 * Bulk send alerts to multiple customers
 */
export async function sendBulkAlerts(
  customers: CustomerContact[],
  alertType:
    | "cargo_status"
    | "shipment_status"
    | "invoice"
    | "release_document"
    | "emergency",
  alertData: any,
  options: AlertOptions = {}
): Promise<{
  successful: number;
  failed: number;
  results: AlertResult[];
}> {
  const results: AlertResult[] = [];
  let successful = 0;
  let failed = 0;

  for (const customer of customers) {
    try {
      let result: AlertResult;

      switch (alertType) {
        case "cargo_status":
          result = await CustomerAlertsService.sendCargoStatusUpdate(
            customer,
            alertData,
            options
          );
          break;
        case "shipment_status":
          result = await CustomerAlertsService.sendShipmentStatusUpdate(
            customer,
            alertData,
            options
          );
          break;
        case "invoice":
          result = await CustomerAlertsService.sendInvoiceAlert(
            customer,
            alertData.invoice,
            alertData.document,
            options
          );
          break;
        case "release_document":
          result = await CustomerAlertsService.sendReleaseDocumentAlert(
            customer,
            alertData.releaseData,
            alertData.document,
            options
          );
          break;
        case "emergency":
          result = await CustomerAlertsService.sendEmergencyAlert(
            customer,
            alertData,
            options
          );
          break;
        default:
          result = {
            success: false,
            errors: [`Unknown alert type: ${alertType}`],
          };
      }

      results.push(result);

      if (result.success) {
        successful++;
      } else {
        failed++;
      }
    } catch (error: any) {
      failed++;
      results.push({
        success: false,
        errors: [`Failed to send alert to ${customer.name}: ${error.message}`],
      });
    }
  }

  return { successful, failed, results };
}

// Export the main service class and utility functions
export { CustomerAlertsService };
