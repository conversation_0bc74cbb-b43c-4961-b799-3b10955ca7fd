import { type MessageResponse } from "@/lib/messaging";

/**
 * Customer contact information for alerts
 */
export interface CustomerContact {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  preferredMethod: "sms" | "whatsapp" | "email" | "both";
  language?: "en" | "zh" | "ar";
}

/**
 * Options for customizing alerts
 */
export interface AlertOptions {
  urgency?: "low" | "medium" | "high" | "critical";
  includeAttachments?: boolean;
  customMessage?: string;
  trackingUrl?: string;
  documentUrl?: string;
}

/**
 * Result of sending an alert
 */
export interface AlertResult {
  success: boolean;
  smsResult?: MessageResponse;
  whatsappResult?: MessageResponse;
  emailResult?: any;
  errors?: string[];
}

/**
 * Cargo status update data
 */
export interface CargoStatusData {
  trackingNumber: string;
  status: string;
  currentLocation?: string;
  estimatedDelivery?: string;
  particular?: string;
}

/**
 * Shipment status update data
 */
export interface ShipmentStatusData {
  trackingNumber: string;
  batchCode?: string;
  origin: string;
  destination: string;
  status: string;
  estimatedArrival?: string;
}

/**
 * Invoice alert data
 */
export interface InvoiceAlertData {
  invoiceNumber: string;
  totalAmount: number;
  currency?: string;
  dueDate?: string;
  status: string;
  trackingNumber?: string;
}

/**
 * Release document alert data
 */
export interface ReleaseDocumentData {
  releaseCode: string;
  trackingNumber: string;
  instructions?: string;
  qrCodeData?: string;
}

/**
 * Emergency alert data
 */
export interface EmergencyAlertData {
  type: "damage" | "security" | "delay" | "custom";
  trackingNumber: string;
  message: string;
  location?: string;
  contactNumber?: string;
}

/**
 * Document attachment data
 */
export interface DocumentAttachment {
  path: string;
  fileName: string;
  content?: Buffer;
}

/**
 * Bulk alert operation result
 */
export interface BulkAlertResult {
  successful: number;
  failed: number;
  results: AlertResult[];
}

/**
 * Alert type enumeration
 */
export type AlertType = 
  | "cargo_status" 
  | "shipment_status" 
  | "invoice" 
  | "release_document" 
  | "emergency";

/**
 * Customer validation result
 */
export interface CustomerValidationResult {
  isValid: boolean;
  errors: string[];
}

/**
 * Email template data
 */
export interface EmailTemplateData {
  subject: string;
  html: string;
  text?: string;
  attachments?: Array<{
    filename: string;
    content: Buffer;
    contentType: string;
  }>;
}

/**
 * Alert configuration
 */
export interface AlertConfiguration {
  enableSMS: boolean;
  enableWhatsApp: boolean;
  enableEmail: boolean;
  defaultUrgency: "low" | "medium" | "high" | "critical";
  retryAttempts: number;
  retryDelay: number; // in milliseconds
}

/**
 * Alert statistics
 */
export interface AlertStatistics {
  totalSent: number;
  successfulSMS: number;
  successfulWhatsApp: number;
  successfulEmail: number;
  failedAlerts: number;
  averageDeliveryTime: number; // in seconds
}

/**
 * Template variables for dynamic content
 */
export interface TemplateVariables {
  customerName: string;
  trackingNumber: string;
  companyName: string;
  supportEmail: string;
  supportPhone: string;
  websiteUrl: string;
  [key: string]: string | number | boolean;
}

/**
 * Alert scheduling options
 */
export interface AlertScheduleOptions {
  sendAt?: Date;
  timezone?: string;
  recurring?: {
    frequency: "daily" | "weekly" | "monthly";
    interval: number;
    endDate?: Date;
  };
}

/**
 * Alert delivery status
 */
export interface AlertDeliveryStatus {
  alertId: string;
  customerId: string;
  method: "sms" | "whatsapp" | "email";
  status: "pending" | "sent" | "delivered" | "failed" | "bounced";
  sentAt?: Date;
  deliveredAt?: Date;
  errorMessage?: string;
  retryCount: number;
}

/**
 * Customer preferences for alerts
 */
export interface CustomerAlertPreferences {
  customerId: string;
  enableCargoUpdates: boolean;
  enableInvoiceAlerts: boolean;
  enableReleaseNotifications: boolean;
  enableEmergencyAlerts: boolean;
  preferredMethod: "sms" | "whatsapp" | "email" | "both";
  quietHours?: {
    start: string; // HH:MM format
    end: string;   // HH:MM format
    timezone: string;
  };
  language: "en" | "zh" | "ar";
}

/**
 * Alert template configuration
 */
export interface AlertTemplate {
  id: string;
  name: string;
  type: AlertType;
  subject: string;
  smsTemplate: string;
  whatsappTemplate: string;
  emailTemplate: string;
  variables: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Alert queue item
 */
export interface AlertQueueItem {
  id: string;
  customerId: string;
  alertType: AlertType;
  data: any;
  options: AlertOptions;
  scheduledFor: Date;
  priority: number;
  attempts: number;
  maxAttempts: number;
  status: "pending" | "processing" | "completed" | "failed";
  createdAt: Date;
  processedAt?: Date;
  errorMessage?: string;
}

/**
 * Alert metrics for analytics
 */
export interface AlertMetrics {
  period: {
    start: Date;
    end: Date;
  };
  totalAlerts: number;
  alertsByType: Record<AlertType, number>;
  alertsByMethod: Record<"sms" | "whatsapp" | "email", number>;
  successRate: number;
  averageDeliveryTime: number;
  topFailureReasons: Array<{
    reason: string;
    count: number;
  }>;
}
