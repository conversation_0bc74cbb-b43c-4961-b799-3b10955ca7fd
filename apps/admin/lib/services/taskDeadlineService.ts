"use client";

import { taskService, notificationService } from "@/lib/logistics";
import { TaskWithAssignee } from "@/lib/logistics";

interface DeadlineCheckOptions {
  enableNotifications?: boolean;
  checkIntervalHours?: number;
  deadlineWarningDays?: number[];
}

class TaskDeadlineService {
  private checkInterval: NodeJS.Timeout | null = null;
  private isRunning = false;
  private options: DeadlineCheckOptions = {
    enableNotifications: true,
    checkIntervalHours: 1, // Check every hour
    deadlineWarningDays: [1], // Warn 1 day before deadline
  };

  constructor(options?: Partial<DeadlineCheckOptions>) {
    this.options = { ...this.options, ...options };
  }

  // Start the deadline checking service
  start() {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;

    // Run initial check
    this.checkDeadlines();

    // Set up periodic checking
    this.checkInterval = setInterval(
      () => this.checkDeadlines(),
      this.options.checkIntervalHours! * 60 * 60 * 1000
    );
  }

  // Stop the deadline checking service
  stop() {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;

    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }

  // Check all tasks for deadline notifications
  private async checkDeadlines() {
    if (!this.options.enableNotifications) return;

    try {
      // Fetch all active tasks
      const tasksResult = await taskService.getAllTasksWithAssignees({
        limit: 1000, // Get all tasks
        filters: {
          status: ["CREATED", "PENDING", "PROCESSING"], // Only active tasks
        },
      });

      if (!tasksResult.success || !tasksResult.data) {
        return;
      }

      const tasks = tasksResult.data;
      const now = new Date();
      const notificationsToSend: Array<{
        task: TaskWithAssignee;
        type: "deadline" | "overdue";
        daysUntilDue?: number;
        daysOverdue?: number;
      }> = [];

      // Check each task
      for (const task of tasks) {
        if (!task.due || !task.assignee) continue;

        const dueDate = new Date(task.due);
        const timeDiff = dueDate.getTime() - now.getTime();
        const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

        // Check if task is approaching deadline
        if (
          this.options.deadlineWarningDays!.includes(daysDiff) &&
          daysDiff > 0
        ) {
          notificationsToSend.push({
            task,
            type: "deadline",
            daysUntilDue: daysDiff,
          });
        }
        // Check if task is overdue
        else if (daysDiff < 0) {
          const daysOverdue = Math.abs(daysDiff);
          notificationsToSend.push({
            task,
            type: "overdue",
            daysOverdue,
          });
        }
      }

      // Send notifications
      for (const notification of notificationsToSend) {
        await this.sendDeadlineNotification(notification);
      }
    } catch (error) {
      // Silent error handling for deadline checks
    }
  }

  // Send deadline or overdue notification
  private async sendDeadlineNotification(notification: {
    task: TaskWithAssignee;
    type: "deadline" | "overdue";
    daysUntilDue?: number;
    daysOverdue?: number;
  }) {
    const { task, type, daysUntilDue, daysOverdue } = notification;

    try {
      // Check if we've already sent this notification today
      const today = new Date().toISOString().split("T")[0];
      const notificationKey = `${task.id}_${type}_${today}`;

      // Use localStorage to track sent notifications (in a real app, use a database)
      const sentNotifications = JSON.parse(
        localStorage.getItem("sentTaskNotifications") || "{}"
      );

      if (sentNotifications[notificationKey]) {
        return; // Already sent today
      }

      let message: string;
      let notificationName: string;

      if (type === "deadline" && daysUntilDue) {
        notificationName = "Task Deadline Approaching";
        message =
          daysUntilDue === 1
            ? `Task "${task.name}" is due tomorrow!`
            : `Task "${task.name}" is due in ${daysUntilDue} days`;
      } else if (type === "overdue" && daysOverdue) {
        notificationName = "Task Overdue";
        message =
          daysOverdue === 1
            ? `Task "${task.name}" is 1 day overdue!`
            : `Task "${task.name}" is ${daysOverdue} days overdue!`;
      } else {
        return;
      }

      // Create notification
      const result = await notificationService.createTargetedNotification({
        account_id: task.account_id, // Creator's account
        name: notificationName,
        message,
        associated_table: "tasks",
        associated_id: task.id,
        to: task.assignee!, // Map assignee to "to" field
        details: {
          taskId: task.id,
          taskName: task.name,
          priority: task.priority,
          dueDate: task.due,
          assigneeId: task.assignee, // Store assignee ID for reference
          type: `task_${type}`,
          daysUntilDue,
          daysOverdue,
          timestamp: new Date().toISOString(),
        },
      });

      if (result.success) {
        // Mark as sent
        sentNotifications[notificationKey] = true;
        localStorage.setItem(
          "sentTaskNotifications",
          JSON.stringify(sentNotifications)
        );
      } else {
        // Silent error handling for notification failures
      }
    } catch (error) {
      // Silent error handling for notification sending
    }
  }

  // Manual check for a specific user's tasks
  async checkUserTasks(accountId: string) {
    try {
      const tasksResult = await taskService.getAllTasksWithAssignees({
        limit: 1000,
        filters: {
          assignee: accountId,
          status: ["CREATED", "PENDING", "PROCESSING"],
        },
      });

      if (!tasksResult.success || !tasksResult.data) {
        console.error("Failed to fetch user tasks:", tasksResult.error);
        return;
      }

      const tasks = tasksResult.data;
      const now = new Date();
      let deadlineCount = 0;
      let overdueCount = 0;

      for (const task of tasks) {
        if (!task.due) continue;

        const dueDate = new Date(task.due);
        const timeDiff = dueDate.getTime() - now.getTime();
        const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

        if (daysDiff === 1) {
          deadlineCount++;
        } else if (daysDiff < 0) {
          overdueCount++;
        }
      }

      return {
        deadlineCount,
        overdueCount,
        totalActiveTasks: tasks.length,
      };
    } catch (error) {
      console.error("Error checking user tasks:", error);
      return null;
    }
  }

  // Clean up old notification tracking data
  cleanupNotificationHistory() {
    try {
      const sentNotifications = JSON.parse(
        localStorage.getItem("sentTaskNotifications") || "{}"
      );

      const today = new Date();
      const threeDaysAgo = new Date(today.getTime() - 3 * 24 * 60 * 60 * 1000);
      const cutoffDate = threeDaysAgo.toISOString().split("T")[0];

      const cleanedNotifications: Record<string, boolean> = {};

      for (const [key, value] of Object.entries(sentNotifications)) {
        const keyDate = key.split("_").pop();
        if (keyDate && keyDate >= cutoffDate) {
          cleanedNotifications[key] = value as boolean;
        }
      }

      localStorage.setItem(
        "sentTaskNotifications",
        JSON.stringify(cleanedNotifications)
      );
    } catch (error) {
      // Silent error handling for notification cleanup
    }
  }

  // Get service status
  getStatus() {
    return {
      isRunning: this.isRunning,
      checkIntervalHours: this.options.checkIntervalHours,
      deadlineWarningDays: this.options.deadlineWarningDays,
      enableNotifications: this.options.enableNotifications,
    };
  }
}

// Create singleton instance
export const taskDeadlineService = new TaskDeadlineService();

// Auto-start the service when imported (can be disabled by setting enableNotifications to false)
if (typeof window !== "undefined") {
  // Only start in browser environment
  setTimeout(() => {
    taskDeadlineService.start();
  }, 5000); // Start after 5 seconds to allow app initialization
}

export default TaskDeadlineService;
