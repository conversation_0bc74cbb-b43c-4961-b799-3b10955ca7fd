/**
 * Global Entity Types Configuration
 *
 * This file contains all entity types used throughout the shamwaa-logistics application
 * for entity selectors, associations, and operations.
 *
 * DYNAMIC ENTITY FETCHER USAGE:
 *
 * 1. Basic Usage:
 *    ```typescript
 *    import { fetchEntityInstances } from '@/lib/constants/entity-types';
 *
 *    const response = await fetchEntityInstances('customers', { limit: 50 });
 *    if (response.success) {
 *      console.log(response.data); // EntityInstance[]
 *    }
 *    ```
 *
 * 2. Simplified Usage (returns empty array on error):
 *    ```typescript
 *    import { getEntityInstances } from '@/lib/constants/entity-types';
 *
 *    const entities = await getEntityInstances('suppliers', { limit: 100 });
 *    ```
 *
 * 3. Check if entity type is supported:
 *    ```typescript
 *    import { isEntityTypeSupported } from '@/lib/constants/entity-types';
 *
 *    if (isEntityTypeSupported('customers')) {
 *      // Fetch entities...
 *    }
 *    ```
 *
 * Supported Entity Types:
 * - customers, suppliers, cargo, batches, freights, shipments
 * - invoices, ledgers, transactions, users, accounts, roles
 * - departments, tasks, documents
 */

import {
  supplierService,
  invoiceService,
  ledgerService,
  transactionService,
  userService,
  accountService,
  roleService,
  departmentService,
  taskService,
  documentService,
} from "@/lib/logistics";
import { bulkFetchService } from "@/lib/logistics";

export interface EntityTypeOption {
  value: string;
  label: string;
  category: "operations" | "finance" | "system" | "logistics";
  description?: string;
}

// Core Operations Entities
export const OPERATIONS_ENTITIES: EntityTypeOption[] = [
  {
    value: "cargo",
    label: "Cargo",
    category: "operations",
    description: "Individual cargo items and shipments",
  },
  {
    value: "customers",
    label: "Customers",
    category: "operations",
    description: "Customer profiles and contact information",
  },
  {
    value: "suppliers",
    label: "Suppliers",
    category: "operations",
    description: "Supplier profiles and vendor information",
  },
  {
    value: "batches",
    label: "Batches",
    category: "logistics",
    description: "Cargo batches and container groupings",
  },
  {
    value: "freights",
    label: "Freights",
    category: "logistics",
    description: "Freight operations and transportation",
  },
  {
    value: "shipments",
    label: "Shipments",
    category: "logistics",
    description: "Shipment tracking and delivery",
  },
  {
    value: "handovers",
    label: "Handovers",
    category: "operations",
    description: "Cargo handover and transfer records",
  },
];

// Finance Entities
export const FINANCE_ENTITIES: EntityTypeOption[] = [
  {
    value: "invoices",
    label: "Invoices",
    category: "finance",
    description: "Customer invoices and billing",
  },
  {
    value: "ledgers",
    label: "Ledgers",
    category: "finance",
    description: "Financial ledger entries and accounts",
  },
  {
    value: "transactions",
    label: "Transactions",
    category: "finance",
    description: "Financial transactions and payments",
  },
];

// System Entities
export const SYSTEM_ENTITIES: EntityTypeOption[] = [
  {
    value: "users",
    label: "Users",
    category: "system",
    description: "System users and staff members",
  },
  {
    value: "accounts",
    label: "Accounts",
    category: "system",
    description: "User authentication accounts",
  },
  {
    value: "roles",
    label: "Roles",
    category: "system",
    description: "User roles and permissions",
  },
  {
    value: "departments",
    label: "Departments",
    category: "system",
    description: "Organizational departments",
  },
  {
    value: "tasks",
    label: "Tasks",
    category: "system",
    description: "Task management and assignments",
  },
  {
    value: "documents",
    label: "Documents",
    category: "system",
    description: "Document management and storage",
  },
  {
    value: "notifications",
    label: "Notifications",
    category: "system",
    description: "System notifications and alerts",
  },
  {
    value: "tags",
    label: "Tags",
    category: "system",
    description: "Tagging and categorization system",
  },
  {
    value: "logs",
    label: "Logs",
    category: "system",
    description: "System activity logs",
  },
];

// Additional Operational Entities
export const ADDITIONAL_ENTITIES: EntityTypeOption[] = [
  {
    value: "release_authorizations",
    label: "Release Authorizations",
    category: "operations",
    description: "Cargo release authorization documents",
  },
  {
    value: "schedules",
    label: "Schedules",
    category: "operations",
    description: "Operational schedules and planning",
  },
  {
    value: "assignments",
    label: "Assignments",
    category: "operations",
    description: "Task and resource assignments",
  },
  {
    value: "approvals",
    label: "Approvals",
    category: "operations",
    description: "Approval workflows and processes",
  },
];

// Combined Entity Types (All entities)
export const ALL_ENTITY_TYPES: EntityTypeOption[] = [
  ...OPERATIONS_ENTITIES,
  ...FINANCE_ENTITIES,
  ...SYSTEM_ENTITIES,
  ...ADDITIONAL_ENTITIES,
];

// Entity Types by Category
export const ENTITY_TYPES_BY_CATEGORY = {
  operations: OPERATIONS_ENTITIES,
  finance: FINANCE_ENTITIES,
  system: SYSTEM_ENTITIES,
  logistics: OPERATIONS_ENTITIES.filter((e) => e.category === "logistics"),
  additional: ADDITIONAL_ENTITIES,
} as const;

// Most commonly used entities for general selectors
export const COMMON_ENTITY_TYPES: EntityTypeOption[] = [
  ...OPERATIONS_ENTITIES.slice(0, 7), // cargo, customers, suppliers, batches, freights, shipments, handovers
  ...FINANCE_ENTITIES.slice(0, 2), // invoices, ledgers
  ...SYSTEM_ENTITIES.slice(0, 5), // users, accounts, roles, departments, tasks
];

// Legacy format for backward compatibility
export const ENTITY_TYPES = ALL_ENTITY_TYPES.map((entity) => ({
  value: entity.value,
  label: entity.label,
}));

// Helper functions
export const getEntityTypeByValue = (
  value: string
): EntityTypeOption | undefined => {
  return ALL_ENTITY_TYPES.find((entity) => entity.value === value);
};

export const getEntitiesByCategory = (
  category: EntityTypeOption["category"]
): EntityTypeOption[] => {
  return ALL_ENTITY_TYPES.filter((entity) => entity.category === category);
};

export const getEntityLabel = (value: string): string => {
  const entity = getEntityTypeByValue(value);
  return entity?.label || value;
};

export const getEntityDescription = (value: string): string => {
  const entity = getEntityTypeByValue(value);
  return entity?.description || "";
};

// Entity instance interface for dynamic fetching
export interface EntityInstance {
  id: string;
  label: string;
  description?: string;
  metadata?: Record<string, any>;
}

// Service response interface
export interface EntityFetchResponse {
  success: boolean;
  data: EntityInstance[];
  error?: string;
}

/**
 * Dynamic Entity Fetcher
 *
 * Fetches entity instances based on the entity type.
 * Returns a standardized format for use in entity selectors.
 *
 * @param entityType - The entity type to fetch (e.g., 'customers', 'suppliers', etc.)
 * @param options - Optional parameters for fetching (limit, filters, etc.)
 * @returns Promise<EntityFetchResponse> - Standardized response with entity instances
 */
export const fetchEntityInstances = async (
  entityType: string,
  options: { limit?: number; filters?: Record<string, any> } = {}
): Promise<EntityFetchResponse> => {
  const { limit, filters } = options ?? { limit: 100, filters: {} };

  try {
    let result: any;
    let entities: EntityInstance[] = [];

    switch (entityType) {
      case "customers":
        result = await bulkFetchService.getAllCustomersWithCargoStats({
          limit,
          filters,
        });
        if (result.success) {
          entities = result.data.map((customer: any) => ({
            id: customer.id,
            label: `${customer.name} - ${customer.code}`,
            description: customer.email
              ? `Email: ${customer.email}`
              : undefined,
            metadata: {
              email: customer.email,
              phone: customer.phone,
              location: customer.location,
              type: "customer",
            },
          }));
        }
        break;

      case "suppliers":
        result = await supplierService.getActiveSuppliers({ limit, filters });
        if (result.success) {
          entities = result.data.map((supplier: any) => ({
            id: supplier.id,
            label: `${supplier.tracking_number} - (${supplier.phone})`,
            description: supplier.location
              ? `Location: ${supplier.location}`
              : undefined,
            metadata: {
              tracking_number: supplier.tracking_number,
              phone: supplier.phone,
              location: supplier.location,
              type: "supplier",
            },
          }));
        }
        break;

      case "cargo":
        result = await bulkFetchService.getAllCargosWithRelations({
          limit,
          filters,
        });
        if (result.success) {
          entities = result.data.map((cargo: any) => ({
            id: cargo.id,
            label: `${cargo.tracking_number} - ${cargo.particular}`,
            description: cargo.customers?.name
              ? `Customer: ${cargo.customers.name}`
              : undefined,
            metadata: {
              tracking_number: cargo.tracking_number,
              particular: cargo.particular,
              customer: cargo.customers?.name,
              type: "cargo",
            },
          }));
        }
        break;

      case "batches":
        result = await bulkFetchService.getAllBatchesWithCargo({
          limit,
          filters,
        });
        if (result.success) {
          entities = result.data.map((batch: any) => ({
            id: batch.id,
            label: `${batch.name} - ${batch.code}`,
            description: batch.status ? `Status: ${batch.status}` : undefined,
            metadata: {
              name: batch.name,
              code: batch.code,
              status: batch.status,
              weight: batch.total_weight,
              type: "batch",
            },
          }));
        }
        break;

      case "freights":
        result = await bulkFetchService.getAllFreightsWithRelations({
          limit,
          filters,
        });
        if (result.success) {
          entities = result.data.map((freight: any) => ({
            id: freight.id,
            label: `${freight.name} - ${freight.code}`,
            description:
              freight.origin && freight.destination
                ? `${freight.origin} → ${freight.destination}`
                : undefined,
            metadata: {
              freight_number: freight.freight_number,
              name: freight.name,
              origin: freight.origin,
              destination: freight.destination,
              type: "freight",
            },
          }));
        }
        break;

      case "shipments":
        result = await bulkFetchService.getAllShipmentsWithRelations({
          limit,
          filters,
        });
        if (result.success) {
          entities = result.data.map((shipment: any) => ({
            id: shipment.id,
            label: `Shipment ${shipment.tracking_number}`,
            description: shipment.status
              ? `Status: ${shipment.status}`
              : undefined,
            metadata: {
              tracking_number: shipment.tracking_number,
              status: shipment.status,
              type: "shipment",
            },
          }));
        }
        break;

      case "invoices":
        result = await invoiceService.getAllInvoicesWithCustomers({
          limit,
        });
        if (result.success) {
          entities = result.data.map((invoice: any) => ({
            id: invoice.id,
            label: `Invoice ${invoice.inv_number || invoice.id.slice(0, 8)}`,
            description: invoice.customer?.name
              ? `Customer: ${invoice.customer.name}`
              : invoice.supplier?.tracking_number
                ? `Supplier: ${invoice.supplier.tracking_number}`
                : undefined,
            metadata: {
              invoice_number: invoice.inv_number,
              customer: invoice.customer?.name,
              supplier: invoice.supplier?.tracking_number,
              total_amount: invoice.total,
              status: invoice.status,
              type: "invoice",
            },
          }));
        }
        break;

      case "ledgers":
        result = await ledgerService.getAllLedgers({ limit, filters });
        if (result.success) {
          entities = result.data.map((ledger: any) => ({
            id: ledger.id,
            label: ledger.name || `Ledger ${ledger.id}`,
            description: ledger.description
              ? `Description: ${ledger.description}`
              : undefined,
            metadata: {
              name: ledger.name,
              description: ledger.description,
              category: ledger.category,
              type: "ledger",
            },
          }));
        }
        break;

      case "transactions":
        result = await transactionService.getAllTransactionsWithLedgers({
          limit,
        });
        if (result.success) {
          entities = result.data.map((transaction: any) => ({
            id: transaction.id,
            label: transaction.name || `Transaction ${transaction.id}`,
            description: transaction.ledgers?.name
              ? `Ledger: ${transaction.ledgers.name}`
              : undefined,
            metadata: {
              name: transaction.name,
              amount: transaction.amount,
              ledger: transaction.ledgers?.name,
              type: "transaction",
            },
          }));
        }
        break;

      case "users":
        result = await userService.getAll({ limit, filters });
        if (result.success) {
          entities = result.data.map((user: any) => ({
            id: user.id,
            label: user.name || `User ${user.id}`,
            description: user.email ? `Email: ${user.email}` : undefined,
            metadata: {
              name: user.name,
              email: user.email,
              phone: user.phone,
              type: "user",
            },
          }));
        }
        break;

      case "accounts":
        result = await accountService.getAll({ limit, filters });
        if (result.success) {
          entities = result.data.map((account: any) => ({
            id: account.id,
            label: account.email || `Account ${account.id}`,
            description: account.status
              ? `Status: ${account.status}`
              : undefined,
            metadata: {
              email: account.email,
              status: account.status,
              type: "account",
            },
          }));
        }
        break;

      case "roles":
        result = await roleService.getAll({ limit, filters });
        if (result.success) {
          entities = result.data.map((role: any) => ({
            id: role.id,
            label: role.name || `Role ${role.id}`,
            description: role.description
              ? `Description: ${role.description}`
              : undefined,
            metadata: {
              name: role.name,
              description: role.description,
              type: "role",
            },
          }));
        }
        break;

      case "departments":
        result = await departmentService.getAll({ limit, filters });
        if (result.success) {
          entities = result.data.map((department: any) => ({
            id: department.id,
            label: department.name || `Department ${department.id}`,
            description: department.description
              ? `Description: ${department.description}`
              : undefined,
            metadata: {
              name: department.name,
              description: department.description,
              type: "department",
            },
          }));
        }
        break;

      case "tasks":
        result = await taskService.getAll({ limit, filters });
        if (result.success) {
          entities = result.data.map((task: any) => ({
            id: task.id,
            label: task.name || `Task ${task.id}`,
            description: task.status ? `Status: ${task.status}` : undefined,
            metadata: {
              name: task.name,
              status: task.status,
              priority: task.priority,
              type: "task",
            },
          }));
        }
        break;

      case "documents":
        result = await documentService.getAll({ limit, filters });
        if (result.success) {
          entities = result.data.map((document: any) => ({
            id: document.id,
            label: document.name || `Document ${document.id}`,
            description: document.type ? `Type: ${document.type}` : undefined,
            metadata: {
              name: document.name,
              document_type: document.type,
              size: document.size,
              entity_type: "document",
            },
          }));
        }
        break;

      default:
        return {
          success: false,
          data: [],
          error: `Unsupported entity type: ${entityType}`,
        };
    }

    if (!result?.success) {
      return {
        success: false,
        data: [],
        error: result?.error || `Failed to fetch ${entityType}`,
      };
    }

    return {
      success: true,
      data: entities,
    };
  } catch (error: any) {
    return {
      success: false,
      data: [],
      error: error.message || `Error fetching ${entityType}`,
    };
  }
};

/**
 * Simplified Entity Fetcher Hook
 *
 * A convenience function that returns entity instances for use in React components.
 * This can be used directly in components without handling the response structure.
 *
 * @param entityType - The entity type to fetch
 * @param options - Optional parameters for fetching
 * @returns Promise<EntityInstance[]> - Array of entity instances (empty array on error)
 */
export const getEntityInstances = async (
  entityType: string,
  options?: { limit?: number; filters?: Record<string, any> }
): Promise<EntityInstance[]> => {
  const response = await fetchEntityInstances(entityType, options);
  return response.success ? response.data : [];
};

/**
 * Check if an entity type is supported by the dynamic fetcher
 *
 * @param entityType - The entity type to check
 * @returns boolean - True if the entity type is supported
 */
export const isEntityTypeSupported = (entityType: string): boolean => {
  const supportedTypes = [
    "customers",
    "suppliers",
    "cargo",
    "batches",
    "freights",
    "shipments",
    "invoices",
    "ledgers",
    "transactions",
    "users",
    "accounts",
    "roles",
    "departments",
    "tasks",
    "documents",
  ];
  return supportedTypes.includes(entityType);
};

/**
 * Get the appropriate service method name for an entity type
 *
 * @param entityType - The entity type
 * @returns string - The service method name used for fetching
 */
export const getEntityServiceMethod = (entityType: string): string => {
  const methodMap: Record<string, string> = {
    customers: "getAllCustomersWithCargoStats",
    suppliers: "getActiveSuppliers",
    cargo: "getAllCargosWithRelations",
    batches: "getAllBatchesWithCargo",
    freights: "getAllFreightsWithRelations",
    shipments: "getAllShipmentsWithRelations",
    invoices: "getAllInvoicesWithCustomers",
    ledgers: "getAllLedgers",
    transactions: "getAllTransactionsWithLedgers",
    users: "getAll",
    accounts: "getAll",
    roles: "getAll",
    departments: "getAll",
    tasks: "getAll",
    documents: "getAll",
  };
  return methodMap[entityType] || "getAll";
};
