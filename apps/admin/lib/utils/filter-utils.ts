import { QueryParams } from "@/lib/logistics/types";

export interface FilterState {
  [key: string]: string | string[] | null;
}

export interface FilterConfig {
  key: string;
  label: string;
  type: "date" | "text" | "tags" | "number" | "boolean";
  placeholder?: string;
  defaultValue?: string | string[];
}

/**
 * Convert filter state to QueryParams for service calls
 */
export function filterStateToQueryParams(
  filterState: FilterState,
  searchTerm: string,
  additionalParams?: Partial<QueryParams>
): QueryParams {
  const filters: { [key: string]: any } = {};

  // Add non-null filter values
  Object.entries(filterState).forEach(([key, value]) => {
    if (
      value !== null &&
      value !== "" &&
      (Array.isArray(value) ? value.length > 0 : true)
    ) {
      filters[key] = value;
    }
  });

  return {
    filters,
    search: searchTerm || undefined,
    ...additionalParams,
  };
}

/**
 * Create filter options with counts from data array
 */
export function createFilterOptionsWithCounts<T>(
  data: T[],
  fieldPath: string,
  labelFormatter?: (value: any) => string
): { value: string; label: string; count: number }[] {
  const counts: { [key: string]: number } = {};

  data.forEach((item) => {
    const value = getNestedValue(item, fieldPath);
    if (value !== null && value !== undefined && value !== "") {
      const stringValue = String(value);
      counts[stringValue] = (counts[stringValue] || 0) + 1;
    }
  });

  return Object.entries(counts)
    .map(([value, count]) => ({
      value,
      label: labelFormatter ? labelFormatter(value) : formatLabel(value),
      count,
    }))
    .sort((a, b) => b.count - a.count);
}

/**
 * Get nested value from object using dot notation
 */
function getNestedValue(obj: any, path: string): any {
  return path.split(".").reduce((current, key) => current?.[key], obj);
}

/**
 * Format label for display (capitalize, replace underscores, etc.)
 */
function formatLabel(value: string): string {
  return value
    .replace(/_/g, " ")
    .replace(/\b\w/g, (char) => char.toUpperCase());
}

/**
 * Filter data array based on filter state and search term
 */
export function filterData<T>(
  data: T[],
  filterState: FilterState,
  searchTerm: string,
  searchFields: string[] = []
): T[] {
  return data.filter((item) => {
    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      const matchesSearch = searchFields.some((field) => {
        const value = getNestedValue(item, field);
        return value && String(value).toLowerCase().includes(searchLower);
      });
      if (!matchesSearch) return false;
    }

    // Apply other filters
    for (const [key, filterValue] of Object.entries(filterState)) {
      if (
        filterValue === null ||
        filterValue === "" ||
        (Array.isArray(filterValue) && filterValue.length === 0)
      ) {
        continue;
      }

      const itemValue = getNestedValue(item, key);

      if (Array.isArray(filterValue)) {
        // Multi-select or tags filter
        if (Array.isArray(itemValue)) {
          // Check if any of the item's values match any of the filter values
          const hasMatch = filterValue.some((fv) =>
            itemValue.some((iv) =>
              String(iv).toLowerCase().includes(String(fv).toLowerCase())
            )
          );
          if (!hasMatch) return false;
        } else {
          // Check if item value matches any of the filter values
          const matches = filterValue.some((fv) =>
            String(itemValue).toLowerCase().includes(String(fv).toLowerCase())
          );
          if (!matches) return false;
        }
      } else {
        // Single value filter
        if (
          String(itemValue).toLowerCase() !== String(filterValue).toLowerCase()
        ) {
          return false;
        }
      }
    }

    return true;
  });
}

/**
 * Common filter configurations for different entity types
 */
export const COMMON_FILTERS = {
  status: {
    key: "status",
    label: "Status",
    type: "text" as const,
    placeholder: "Enter status (ACTIVE, INACTIVE, etc.)",
  },

  dateRange: {
    key: "created_at",
    label: "Created Date",
    type: "date" as const,
    placeholder: "Select date",
  },

  tags: {
    key: "tags",
    label: "Tags",
    type: "tags" as const,
    placeholder: "Enter tags separated by commas",
  },
};

/**
 * Finance-specific filter configurations
 */
export const FINANCE_FILTERS = {
  ledgerStatus: {
    key: "status",
    label: "Ledger Status",
    type: "text" as const,
    placeholder: "Enter ACTIVE, INACTIVE, or PENDING",
  },

  ledgerBooks: {
    key: "books",
    label: "Book Category",
    type: "text" as const,
    placeholder: "Enter book category (Expenses, Revenue, etc.)",
  },

  transactionType: {
    key: "type",
    label: "Transaction Type",
    type: "text" as const,
    placeholder: "Enter DEBIT or CREDIT",
  },

  amountRange: {
    key: "amount_min",
    label: "Minimum Amount",
    type: "text" as const,
    placeholder: "Enter minimum amount",
  },
};

/**
 * Release Authorization specific filter configurations
 */
export const RELEASE_AUTH_FILTERS = {
  authStatus: {
    key: "status",
    label: "Authorization Status",
    type: "text" as const,
    placeholder: "Enter PENDING, APPROVED, PROCESSING, or REJECTED",
  },

  paymentStatus: {
    key: "payment_complete",
    label: "Payment Status",
    type: "boolean" as const,
    placeholder: "Enter true or false",
  },

  documentStatus: {
    key: "docs_complete",
    label: "Document Status",
    type: "boolean" as const,
    placeholder: "Enter true or false",
  },
};

/**
 * Invoice specific filter configurations
 */
export const INVOICE_FILTERS = {
  invoiceStatus: {
    key: "status",
    label: "Invoice Status",
    type: "text" as const,
    placeholder: "Enter status (PAID, PENDING, etc.)",
  },

  dueDate: {
    key: "due_at",
    label: "Due Date",
    type: "date" as const,
    placeholder: "Select due date",
  },

  totalAmount: {
    key: "total_min",
    label: "Minimum Total",
    type: "text" as const,
    placeholder: "Enter minimum total",
  },
};

/**
 * Logistics specific filter configurations
 */
export const LOGISTICS_FILTERS = {
  freightType: {
    key: "type",
    label: "Freight Type",
    type: "text" as const,
    placeholder: "Enter AIR, SEA, or ROAD",
  },

  cargoCategory: {
    key: "category",
    label: "Cargo Category",
    type: "text" as const,
    placeholder: "Enter DANGEROUS or SAFE",
  },

  batchType: {
    key: "type",
    label: "Batch Type",
    type: "text" as const,
    placeholder: "Enter CONTAINER, PALLET, BULK, or MIXED",
  },

  origin: {
    key: "origin",
    label: "Origin",
    type: "text" as const,
    placeholder: "Enter origin location",
  },

  destination: {
    key: "destination",
    label: "Destination",
    type: "text" as const,
    placeholder: "Enter destination location",
  },
};

/**
 * Create default filter state
 */
export function createDefaultFilterState(filters: FilterConfig[]): FilterState {
  const state: FilterState = {};

  filters.forEach((filter) => {
    state[filter.key] = filter.defaultValue || null;
  });

  return state;
}

/**
 * Reset filter state to defaults
 */
export function resetFilterState(filters: FilterConfig[]): FilterState {
  return createDefaultFilterState(filters);
}
