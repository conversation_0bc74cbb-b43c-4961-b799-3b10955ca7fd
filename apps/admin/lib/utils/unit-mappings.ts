/**
 * Global Unit Mapping System
 * Centralized utility for weight, dimension, and metric unit abbreviations
 * Used across the application for consistent unit display
 */

import type { WeightUnitEnum, DimensionUnitEnum } from "@/lib/logistics/types";

// Weight Unit Mappings
export const WEIGHT_UNIT_MAPPINGS: Record<string, string> = {
  // Standard weight units
  KILOGRAMS: "kg",
  POUNDS: "lbs",
  GRAMS: "g",
  TONS: "t",

  // Alternative formats (for backward compatibility)
  KG: "kg",
  LB: "lbs",
  G: "g",
  T: "t",

  // Extended weight units
  MG: "mg",
  OZ: "oz",
  ST: "st",
  SHORT_TON: "short ton",
  LONG_TON: "long ton",
} as const;

// Dimension Unit Mappings
export const DIMENSION_UNIT_MAPPINGS: Record<string, string> = {
  // Linear dimensions
  METERS: "m",
  FEET: "ft",
  CENTIMETERS: "cm",
  INCHES: "in",

  // Alternative formats
  M: "m",
  FT: "ft",
  CM: "cm",
  IN: "in",
  MM: "mm",
  YD: "yd",

  // Volume/Cubic dimensions
  METER_CUBIC: "m³",
  FEET_CUBIC: "ft³",
  CENTIMETER_CUBIC: "cm³",
  INCH_CUBIC: "in³",

  // Alternative cubic formats
  M3: "m³",
  FT3: "ft³",
  CM3: "cm³",
  IN3: "in³",
  YD3: "yd³",
} as const;

// Currency Mappings
export const CURRENCY_MAPPINGS: Record<string, string> = {
  USD: "$",
  EUR: "€",
  GBP: "£",
  TZS: "TSh",
  KES: "KSh",
  UGX: "USh",
  RWF: "RWF",
  JPY: "¥",
  CNY: "¥",
} as const;

// CBM Conversion Factors (to cubic meters)
export const CBM_CONVERSION_FACTORS: Record<string, number> = {
  // Volume units to cubic meters
  METER_CUBIC: 1,
  M3: 1,
  FEET_CUBIC: 0.0283168,
  FT3: 0.0283168,
  CENTIMETER_CUBIC: 0.000001,
  CM3: 0.000001,
  INCH_CUBIC: 0.0000163871,
  IN3: 0.0000163871,
  YD3: 0.764555,
} as const;

/**
 * CBM Calculation and Conversion Utilities
 * Provides functions for calculating and converting CBM values
 */

/**
 * Convert CBM value from one unit to another
 * @param value - The CBM value to convert
 * @param fromUnit - Source unit
 * @param toUnit - Target unit
 * @returns Converted CBM value
 */
export function convertCBM(
  value: number,
  fromUnit: string,
  toUnit: string
): number {
  if (!value || value <= 0) return 0;

  const fromFactor = CBM_CONVERSION_FACTORS[fromUnit] || 1;
  const toFactor = CBM_CONVERSION_FACTORS[toUnit] || 1;

  // Convert to cubic meters first, then to target unit
  const cubicMeters = value * fromFactor;
  return cubicMeters / toFactor;
}

/**
 * Calculate CBM from dimensions
 * @param length - Length value
 * @param width - Width value
 * @param height - Height value
 * @param unit - Dimension unit
 * @returns CBM value in cubic meters
 */
export function calculateCBMFromDimensions(
  length: number,
  width: number,
  height: number,
  unit: string = "METERS"
): number {
  if (!length || !width || !height) return 0;

  // Calculate volume in the given unit
  const volume = length * width * height;

  // Convert to cubic meters based on linear unit
  let cubicMeters = 0;
  switch (unit) {
    case "METERS":
    case "M":
      cubicMeters = volume; // Already in cubic meters
      break;
    case "CENTIMETERS":
    case "CM":
      cubicMeters = volume / 1000000; // cm³ to m³
      break;
    case "FEET":
    case "FT":
      cubicMeters = volume * 0.0283168; // ft³ to m³
      break;
    case "INCHES":
    case "IN":
      cubicMeters = volume * 0.0000163871; // in³ to m³
      break;
    default:
      cubicMeters = volume; // Default to cubic meters
  }

  return cubicMeters;
}

/**
 * Format CBM value for display
 * @param cbm - CBM value in cubic meters
 * @param unit - Display unit (optional)
 * @param precision - Decimal places (default: 3)
 * @returns Formatted CBM string
 */
export function formatCBMDisplay(
  cbm: number | null | undefined,
  unit: string = "METER_CUBIC",
  precision: number = 3
): string {
  if (!cbm || cbm <= 0) return "0 m³";

  const displayUnit = DIMENSION_UNIT_MAPPINGS[unit] || "m³";
  const convertedValue = convertCBM(cbm, "METER_CUBIC", unit);

  return `${convertedValue.toFixed(precision)} ${displayUnit}`;
}

/**
 * Calculate total CBM from cargo array
 * @param cargos - Array of cargo items with cbm_value
 * @returns Total CBM in cubic meters
 */
export function calculateTotalCBM(
  cargos: Array<{ cbm_value?: number | null; cbm_unit?: string }>
): number {
  return cargos.reduce((total, cargo) => {
    if (!cargo.cbm_value) return total;

    // Convert cargo CBM to cubic meters if needed
    const cbmInCubicMeters = convertCBM(
      cargo.cbm_value,
      cargo.cbm_unit || "METER_CUBIC",
      "METER_CUBIC"
    );

    return total + cbmInCubicMeters;
  }, 0);
}

/**
 * Convert weight from one unit to another
 * @param weight - Weight value to convert
 * @param fromUnit - Source weight unit
 * @param toUnit - Target weight unit
 * @returns Converted weight value
 */
export function convertWeight(
  weight: number,
  fromUnit: string,
  toUnit: string
): number {
  if (!weight || fromUnit === toUnit) return weight;

  // Convert to kilograms first (base unit)
  let weightInKg = weight;
  const upperFromUnit = fromUnit.toUpperCase();

  switch (upperFromUnit) {
    case "GRAMS":
    case "G":
      weightInKg = weight / 1000;
      break;
    case "POUNDS":
    case "LB":
    case "LBS":
      weightInKg = weight * 0.453592;
      break;
    case "TONS":
    case "T":
      weightInKg = weight * 1000;
      break;
    case "KILOGRAMS":
    case "KG":
    default:
      weightInKg = weight;
      break;
  }

  // Convert from kilograms to target unit
  const upperToUnit = toUnit.toUpperCase();
  switch (upperToUnit) {
    case "GRAMS":
    case "G":
      return weightInKg * 1000;
    case "POUNDS":
    case "LB":
    case "LBS":
      return weightInKg / 0.453592;
    case "TONS":
    case "T":
      return weightInKg / 1000;
    case "KILOGRAMS":
    case "KG":
    default:
      return weightInKg;
  }
}

/**
 * Calculate total weight from cargo array
 * @param cargos - Array of cargo items with weight_value
 * @returns Total weight in kilograms
 */
export function calculateTotalWeight(
  cargos: Array<{ weight_value?: number | null; weight_unit?: string }>
): number {
  return cargos.reduce((total, cargo) => {
    if (!cargo.weight_value) return total;

    // Convert cargo weight to kilograms if needed
    const weightInKilograms = convertWeight(
      cargo.weight_value,
      cargo.weight_unit || "KILOGRAMS",
      "KILOGRAMS"
    );

    return total + weightInKilograms;
  }, 0);
}

/**
 * Calculate CBM capacity utilization percentage
 * @param usedCBM - Currently used CBM
 * @param totalCapacity - Total CBM capacity
 * @returns Utilization percentage (0-100)
 */
export function calculateCBMUtilization(
  usedCBM: number,
  totalCapacity: number
): number {
  if (!totalCapacity || totalCapacity <= 0) return 0;
  return Math.min((usedCBM / totalCapacity) * 100, 100);
}

/**
 * Get CBM capacity status
 * @param usedCBM - Currently used CBM
 * @param totalCapacity - Total CBM capacity
 * @returns Status object with level and color
 */
export function getCBMCapacityStatus(
  usedCBM: number,
  totalCapacity: number
): {
  level: "low" | "medium" | "high" | "critical";
  color: string;
  percentage: number;
} {
  const percentage = calculateCBMUtilization(usedCBM, totalCapacity);

  if (percentage >= 100) {
    return { level: "critical", color: "red", percentage };
  } else if (percentage >= 90) {
    return { level: "high", color: "orange", percentage };
  } else if (percentage >= 70) {
    return { level: "medium", color: "yellow", percentage };
  } else {
    return { level: "low", color: "green", percentage };
  }
}

// Status Display Mappings
export const STATUS_DISPLAY_MAPPINGS: Record<string, string> = {
  ACTIVE: "Active",
  INACTIVE: "Inactive",
  PENDING: "Pending",
  COMPLETED: "Completed",
  CANCELLED: "Cancelled",
  CREATED: "Created",
  PROCESSING: "Processing",
  IN_TRANSIT: "In Transit",
  DELIVERED: "Delivered",
  PICKED_UP: "Picked Up",
  RELEASED: "Released",
  APPROVED: "Approved",
  REJECTED: "Rejected",
  DRAFT: "Draft",
  READY_FOR_PICKUP: "Ready for Pickup",
} as const;

// Utility Functions

/**
 * Get weight unit abbreviation
 * @param unit - Weight unit enum or string
 * @returns Abbreviated unit string
 */
export function getWeightUnitAbbreviation(
  unit: WeightUnitEnum | string | null | undefined
): string {
  if (!unit) return "kg"; // Default fallback
  const upperUnit = unit.toString().toUpperCase();
  return WEIGHT_UNIT_MAPPINGS[upperUnit] || unit.toString().toLowerCase();
}

/**
 * Get dimension unit abbreviation
 * @param unit - Dimension unit enum or string
 * @returns Abbreviated unit string
 */
export function getDimensionUnitAbbreviation(
  unit: DimensionUnitEnum | string | null | undefined
): string {
  if (!unit) return "m"; // Default fallback
  const upperUnit = unit.toString().toUpperCase();
  return DIMENSION_UNIT_MAPPINGS[upperUnit] || unit.toString().toLowerCase();
}

/**
 * Get currency symbol
 * @param currency - Currency code
 * @returns Currency symbol
 */
export function getCurrencySymbol(currency: string | null | undefined): string {
  if (!currency) return "$"; // Default fallback
  const upperCurrency = currency.toString().toUpperCase();
  return CURRENCY_MAPPINGS[upperCurrency] || currency.toString();
}

/**
 * Get status display name
 * @param status - Status enum or string
 * @returns Formatted status string
 */
export function getStatusDisplay(status: string | null | undefined): string {
  if (!status) return "Unknown";
  const upperStatus = status.toString().toUpperCase();
  return (
    STATUS_DISPLAY_MAPPINGS[upperStatus] || status.toString().replace(/_/g, " ")
  );
}

/**
 * Format weight with unit
 * @param weight - Weight value
 * @param unit - Weight unit
 * @param options - Formatting options
 * @returns Formatted weight string
 */
export function formatWeight(
  weight: number | null | undefined,
  unit: WeightUnitEnum | string | null | undefined,
  options: {
    decimals?: number;
    showUnit?: boolean;
    locale?: string;
  } = {}
): string {
  const { decimals = 2, showUnit = true, locale = "en-US" } = options;

  if (weight === null || weight === undefined) return "N/A";

  const formattedWeight = weight.toLocaleString(locale, {
    minimumFractionDigits: 0,
    maximumFractionDigits: decimals,
  });

  if (!showUnit) return formattedWeight;

  const unitAbbr = getWeightUnitAbbreviation(unit);
  return `${formattedWeight} ${unitAbbr}`;
}

/**
 * Format dimensions with unit
 * @param length - Length value
 * @param width - Width value
 * @param height - Height value
 * @param unit - Dimension unit
 * @param options - Formatting options
 * @returns Formatted dimensions string
 */
export function formatDimensions(
  length: number | null | undefined,
  width: number | null | undefined,
  height: number | null | undefined,
  unit: DimensionUnitEnum | string | null | undefined,
  options: {
    decimals?: number;
    showUnit?: boolean;
    separator?: string;
  } = {}
): string {
  const { decimals = 2, showUnit = true, separator = " × " } = options;

  if (!length || !width || !height) return "N/A";

  const formatValue = (value: number) =>
    value.toLocaleString("en-US", {
      minimumFractionDigits: 0,
      maximumFractionDigits: decimals,
    });

  const dimensionString = [length, width, height]
    .map(formatValue)
    .join(separator);

  if (!showUnit) return dimensionString;

  const unitAbbr = getDimensionUnitAbbreviation(unit);
  return `${dimensionString} ${unitAbbr}`;
}

/**
 * Format volume/CBM with unit
 * @param volume - Volume value
 * @param unit - Volume unit
 * @param options - Formatting options
 * @returns Formatted volume string
 */
export function formatVolume(
  volume: number | null | undefined,
  unit: DimensionUnitEnum | string | null | undefined,
  options: {
    decimals?: number;
    showUnit?: boolean;
    locale?: string;
  } = {}
): string {
  const { decimals = 3, showUnit = true, locale = "en-US" } = options;

  if (volume === null || volume === undefined) return "N/A";

  const formattedVolume = volume.toLocaleString(locale, {
    minimumFractionDigits: 0,
    maximumFractionDigits: decimals,
  });

  if (!showUnit) return formattedVolume;

  const unitAbbr = getDimensionUnitAbbreviation(unit);
  return `${formattedVolume} ${unitAbbr}`;
}

/**
 * Format currency with symbol
 * @param amount - Currency amount
 * @param currency - Currency code
 * @param options - Formatting options
 * @returns Formatted currency string
 */
export function formatCurrency(
  amount: number | null | undefined,
  currency: string | null | undefined = "USD",
  options: {
    decimals?: number;
    showSymbol?: boolean;
    locale?: string;
  } = {}
): string {
  const { decimals = 2, showSymbol = true, locale = "en-US" } = options;

  if (amount === null || amount === undefined) return "N/A";

  const formattedAmount = amount.toLocaleString(locale, {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });

  if (!showSymbol) return formattedAmount;

  const currencySymbol = getCurrencySymbol(currency);
  return `${currencySymbol}${formattedAmount}`;
}

/**
 * Calculate and format volume from dimensions
 * @param length - Length value
 * @param width - Width value
 * @param height - Height value
 * @param unit - Dimension unit
 * @param options - Formatting options
 * @returns Formatted calculated volume string
 */
export function calculateAndFormatVolume(
  length: number | null | undefined,
  width: number | null | undefined,
  height: number | null | undefined,
  unit: DimensionUnitEnum | string | null | undefined,
  options: {
    decimals?: number;
    showUnit?: boolean;
  } = {}
): string {
  if (!length || !width || !height) return "N/A";

  const volume = length * width * height;

  // Convert linear unit to cubic unit
  let volumeUnit = unit;
  if (unit) {
    const upperUnit = unit.toString().toUpperCase();
    switch (upperUnit) {
      case "METERS":
      case "M":
        volumeUnit = "METER_CUBIC";
        break;
      case "FEET":
      case "FT":
        volumeUnit = "FEET_CUBIC";
        break;
      case "CENTIMETERS":
      case "CM":
        volumeUnit = "CENTIMETER_CUBIC";
        break;
      case "INCHES":
      case "IN":
        volumeUnit = "INCH_CUBIC";
        break;
    }
  }

  return formatVolume(volume, volumeUnit, options);
}
