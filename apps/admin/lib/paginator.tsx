interface PaginatorConfig {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
  onLoadMore?: () => Promise<void>;
  hasNextPage?: boolean;
  isLoading?: boolean;
}

interface InfiniteScrollConfig {
  hasMore: boolean;
  loadMore: () => Promise<void>;
  isLoading: boolean;
  threshold?: number;
}

class Paginator {
  private currentPage: number;
  private totalPages: number;
  private totalItems: number;
  private itemsPerPage: number;
  private onPageChange: (page: number) => void;
  private onLoadMore?: () => Promise<void>;
  private hasNextPage: boolean;
  private isLoading: boolean;

  constructor(config: PaginatorConfig) {
    this.currentPage = config.currentPage;
    this.totalPages = config.totalPages;
    this.totalItems = config.totalItems;
    this.itemsPerPage = config.itemsPerPage;
    this.onPageChange = config.onPageChange;
    this.onLoadMore = config.onLoadMore;
    this.hasNextPage = config.hasNextPage ?? this.currentPage < this.totalPages;
    this.isLoading = config.isLoading ?? false;
  }

  // Basic pagination methods
  getCurrentPage() {
    return this.currentPage;
  }

  getTotalPages() {
    return this.totalPages;
  }

  getTotalItems() {
    return this.totalItems;
  }

  getItemsPerPage() {
    return this.itemsPerPage;
  }

  changePage(page: number) {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.onPageChange(page);
    }
  }

  getStartIndex() {
    return (this.currentPage - 1) * this.itemsPerPage;
  }

  getEndIndex() {
    return Math.min(this.currentPage * this.itemsPerPage, this.totalItems);
  }

  getItems() {
    return this.totalItems;
  }

  getPages() {
    return this.totalPages;
  }

  // Infinite scroll methods
  hasMore() {
    return this.hasNextPage;
  }

  async loadMore() {
    if (this.onLoadMore && this.hasNextPage && !this.isLoading) {
      this.isLoading = true;
      try {
        await this.onLoadMore();
      } finally {
        this.isLoading = false;
      }
    }
  }

  getInfiniteScrollConfig(): InfiniteScrollConfig {
    return {
      hasMore: this.hasNextPage,
      loadMore: this.loadMore.bind(this),
      isLoading: this.isLoading,
      threshold: 0.8, // Load more when 80% scrolled
    };
  }

  // Update paginator state
  updateState(updates: Partial<PaginatorConfig>) {
    if (updates.currentPage !== undefined)
      this.currentPage = updates.currentPage;
    if (updates.totalPages !== undefined) this.totalPages = updates.totalPages;
    if (updates.totalItems !== undefined) this.totalItems = updates.totalItems;
    if (updates.itemsPerPage !== undefined)
      this.itemsPerPage = updates.itemsPerPage;
    if (updates.hasNextPage !== undefined)
      this.hasNextPage = updates.hasNextPage;
    if (updates.isLoading !== undefined) this.isLoading = updates.isLoading;
  }

  // Navigation helpers
  canGoNext() {
    return this.currentPage < this.totalPages;
  }

  canGoPrevious() {
    return this.currentPage > 1;
  }

  nextPage() {
    if (this.canGoNext()) {
      this.changePage(this.currentPage + 1);
    }
  }

  previousPage() {
    if (this.canGoPrevious()) {
      this.changePage(this.currentPage - 1);
    }
  }

  firstPage() {
    this.changePage(1);
  }

  lastPage() {
    this.changePage(this.totalPages);
  }
}

export { Paginator, type PaginatorConfig, type InfiniteScrollConfig };
