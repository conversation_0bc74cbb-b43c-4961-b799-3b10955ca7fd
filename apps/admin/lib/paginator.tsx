class Paginator {
  private currentPage: number;
  private totalPages: number;
  private totalItems: number;
  private itemsPerPage: number;
  private onPageChange: (page: number) => void;

  constructor(
    currentPage: number,
    totalPages: number,
    totalItems: number,
    itemsPerPage: number,
    onPageChange: (page: number) => void
  ) {
    this.currentPage = currentPage;
    this.totalPages = totalPages;
    this.totalItems = totalItems;
    this.itemsPerPage = itemsPerPage;
    this.onPageChange = onPageChange;
  }

  getCurrentPage() {
    return this.currentPage;
  }

  getTotalPages() {
    return this.totalPages;
  }

  getTotalItems() {
    return this.totalItems;
  }

  getItemsPerPage() {
    return this.itemsPerPage;
  }

  onPageChange(page: number) {
    this.onPageChange(page);
  }

  getStartIndex() {
    return (this.currentPage - 1) * this.itemsPerPage;
  }

  getEndIndex() {
    return Math.min(this.currentPage * this.itemsPerPage, this.totalItems);
  }

  getItems() {
    return this.totalItems;
  }

  getPages() {
    return this.totalPages;
  }
}
