import {
  generateReleaseAuthorizationInvoice,
  generateDocumentNumber,
  downloadPDF,
} from "./invoice-template-generator";
import type {
  ReleaseAuthorizationData,
  InvoiceTemplateOptions,
  FreightInfo,
  BatchInfo,
  CargoItem,
  PaymentInfo,
  QRCodeData,
  DocumentMetadata,
  PersonnelInfo,
  CompanyInfo,
} from "./types/invoice-template";
import type { ShipmentForRelease } from "./logistics/operations/release-authorizations";

/**
 * Default company configuration
 */
const COMPANY_CONFIG: CompanyInfo = {
  name: "Shamwaa Logistics",
  address:
    "Mezzanine Floor, Victoria Place Building, Old Bagamoyo Road Dar es Salaam, Tanzania",
  phone:
    "+*********** 000 / +*********** 418 / +86 191 574 65832 / +86 135 0224 6489",
  email: "<EMAIL>",
  website: "www.shamwaa.com",
};

/**
 * Converts cargo data with relations to release authorization document data
 */
export function convertCargoToReleaseData(
  cargo: any, // CargoWithRelations from database
  personnelInfo: PersonnelInfo,
  additionalData?: {
    freight?: Partial<FreightInfo>;
    batch?: Partial<BatchInfo>;
    cargoItems?: CargoItem[];
    payment?: PaymentInfo;
    notes?: string;
    specialInstructions?: string;
    attachments?: any;
  }
): ReleaseAuthorizationData {
  const now = new Date();
  const documentNumber = generateDocumentNumber("REL");

  // Generate metadata with real location data
  const metadata: DocumentMetadata = {
    documentNumber,
    generatedDate: now.toISOString(),
    generatedTime: now.toLocaleTimeString(),
    location: cargo.users?.location || "Main Warehouse",
    documentType: "RELEASE_AUTHORIZATION",
    version: "1.0",
    validUntil: new Date(
      now.getTime() + 30 * 24 * 60 * 60 * 1000
    ).toISOString(), // 30 days validity
  };

  // Create freight info from real database data
  const freight: FreightInfo = {
    id: cargo.batches?.freights?.id || cargo.batch_id || cargo.id,
    name: cargo.batches?.freights?.name || `Freight-${cargo.id.slice(-6)}`,
    type: (cargo.batches?.freights?.type as "AIR" | "SEA") || "AIR",
    origin: cargo.batches?.freights?.origin || "Unknown Origin",
    destination: cargo.batches?.freights?.destination || "Unknown Destination",
    arrivalDate: cargo.estimated_arrival || new Date().toISOString(),
    ...additionalData?.freight,
  };

  // Create batch info from real database data
  const batch: BatchInfo = {
    id: cargo.batches?.id || cargo.batch_id || `batch-${cargo.id}`,
    code: cargo.batches?.code || `BATCH-${cargo.id.slice(-6)}`,
    freightId: freight.id,
    totalWeight: cargo.batches?.weight || cargo.weight_value || 0,
    weightUnit: (cargo.batches?.weight_unit || cargo.weight_unit || "kg") as
      | "kg"
      | "lb",
    totalValue: 0, // Will be calculated from cargo items
    currency: "USD",
    cargoCount: 1,
    customerCount: 1,
    ...additionalData?.batch,
  };

  // Create cargo item from real database data
  const defaultCargoItem: CargoItem = {
    id: cargo.id,
    trackingNumber: cargo.tracking_number || `TRK-${cargo.id.slice(-8)}`,
    description: cargo.particular || "General Cargo",
    category: cargo.category || "SAFE",
    quantity: 1,
    weight: cargo.weight_value || 0,
    weightUnit: (cargo.weight_unit || "kg") as "kg" | "lb",
    value: cargo.total_price || cargo.unit_price || 0,
    currency: "USD",
    customerName: cargo.customers?.name || "Unknown Customer",
    customerCompany: cargo.customers?.name || "Unknown Company",
    customerPhone: cargo.customers?.phone,
    dimensions: {
      length: cargo.dimension_length || 0,
      width: cargo.dimension_width || 0,
      height: cargo.dimension_height || 0,
      unit: (cargo.dimension_unit || "cm") as "cm" | "m" | "in" | "ft",
    },
    specialInstructions: additionalData?.specialInstructions,
  };

  const cargoItems = additionalData?.cargoItems || [defaultCargoItem];

  // Calculate batch totals
  batch.totalValue = cargoItems.reduce((sum, item) => sum + item.value, 0);
  batch.cargoCount = cargoItems.length;
  batch.customerCount = new Set(
    cargoItems.map((item) => item.customerName)
  ).size;

  // Create payment info from real data
  const payment: PaymentInfo = {
    status: "PAID", // Assume paid for release authorization
    totalAmount: batch.totalValue,
    paidAmount: batch.totalValue,
    currency: "USD",
    paymentMethod: "Bank Transfer",
    paymentDate: new Date().toISOString(),
    ...additionalData?.payment,
  };

  // Create QR code data
  const qrCode: QRCodeData = {
    id: cargo.id,
    releaseCode: "REL-000000", // This will be set by the calling function
    cargoIds: cargoItems.map((item) => item.id),
    batchId: batch.id,
    authorizedBy: personnelInfo.authorizedBy,
    authorizedDate: now.toISOString(),
    expiryDate: metadata.validUntil,
    verificationUrl: `${window.location.origin}/release-authorization/${cargo.id}`,
  };

  return {
    metadata,
    company: COMPANY_CONFIG,
    personnel: personnelInfo,
    freight,
    batch,
    cargoItems,
    payment,
    qrCode,
    attachments: additionalData?.attachments?.success
      ? additionalData.attachments.data
      : [],
    notes: additionalData?.notes,
    specialInstructions: additionalData?.specialInstructions,
    requiresPhotoId: true,
    requiresSignature: true,
    requiresWitness: false,
  };
}

/**
 * Backward compatibility function - converts shipment data to release authorization document data
 * @deprecated Use convertCargoToReleaseData instead
 */
export function convertShipmentToReleaseData(
  shipment: ShipmentForRelease,
  personnelInfo: PersonnelInfo,
  additionalData?: {
    freight?: Partial<FreightInfo>;
    batch?: Partial<BatchInfo>;
    cargoItems?: CargoItem[];
    payment?: PaymentInfo;
    notes?: string;
    specialInstructions?: string;
  }
): ReleaseAuthorizationData {
  // Convert the old shipment format to the new cargo format for compatibility
  const cargoData = {
    id: shipment.id,
    tracking_number: shipment.trackingNumber,
    particular: shipment.cargoType || "General Cargo",
    category: "SAFE",
    weight_value: parseFloat(shipment.weight?.replace(/[^\d.]/g, "") || "0"),
    weight_unit: "kg",
    total_price: 1000,
    unit_price: 1000,
    estimated_arrival: shipment.arrivalDate,
    customers: {
      name: shipment.customer,
      email: null,
      phone: null,
      location: null,
    },
    batches: null,
    users: null,
    dimension_length: 0,
    dimension_width: 0,
    dimension_height: 0,
    dimension_unit: "cm",
    cbm_value: 0,
  };

  return convertCargoToReleaseData(cargoData, personnelInfo, additionalData);
}

/**
 * Generates and downloads a release authorization document for a shipment
 */
export async function generateReleaseDocument(
  shipment: ShipmentForRelease,
  personnelInfo: PersonnelInfo,
  options?: {
    additionalData?: Parameters<typeof convertShipmentToReleaseData>[2];
    templateOptions?: InvoiceTemplateOptions;
    autoDownload?: boolean;
  }
): Promise<{
  success: boolean;
  data?: {
    pdfBlob: Blob;
    documentNumber: string;
    fileName: string;
    qrCodeData: string;
  };
  error?: string;
}> {
  try {
    // Convert shipment to release authorization data
    const releaseData = convertShipmentToReleaseData(
      shipment,
      personnelInfo,
      options?.additionalData
    );

    // Generate the PDF document
    const result = await generateReleaseAuthorizationInvoice(
      releaseData,
      options?.templateOptions
    );

    if (!result.success || !result.data) {
      return {
        success: false,
        error: result.error || "Failed to generate document",
      };
    }

    // Auto-download if requested
    if (options?.autoDownload !== false) {
      downloadPDF(result.data.pdfBlob, result.data.fileName);
    }

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    console.error("Error generating release document:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Generates a release document with custom cargo items
 */
export async function generateBatchReleaseDocument(
  batchData: {
    batchId: string;
    batchCode: string;
    freight: FreightInfo;
    cargoItems: CargoItem[];
    payment?: PaymentInfo;
  },
  personnelInfo: PersonnelInfo,
  options?: {
    templateOptions?: InvoiceTemplateOptions;
    notes?: string;
    specialInstructions?: string;
    autoDownload?: boolean;
  }
): Promise<{
  success: boolean;
  data?: {
    pdfBlob: Blob;
    documentNumber: string;
    fileName: string;
    qrCodeData: string;
  };
  error?: string;
}> {
  try {
    const now = new Date();
    const documentNumber = generateDocumentNumber("BATCH-REL");

    // Calculate batch totals
    const totalWeight = batchData.cargoItems.reduce(
      (sum, item) => sum + item.weight,
      0
    );
    const totalValue = batchData.cargoItems.reduce(
      (sum, item) => sum + item.value,
      0
    );
    const customerCount = new Set(
      batchData.cargoItems.map((item) => item.customerName)
    ).size;

    const releaseData: ReleaseAuthorizationData = {
      metadata: {
        documentNumber,
        generatedDate: now.toISOString(),
        generatedTime: now.toLocaleTimeString(),
        location: "Main Warehouse",
        documentType: "RELEASE_AUTHORIZATION",
        version: "1.0",
        validUntil: new Date(
          now.getTime() + 30 * 24 * 60 * 60 * 1000
        ).toISOString(),
      },
      company: COMPANY_CONFIG,
      personnel: personnelInfo,
      freight: batchData.freight,
      batch: {
        id: batchData.batchId,
        code: batchData.batchCode,
        freightId: batchData.freight.id,
        totalWeight,
        weightUnit: "kg",
        totalValue,
        currency: "USD",
        cargoCount: batchData.cargoItems.length,
        customerCount,
      },
      cargoItems: batchData.cargoItems,
      payment: batchData.payment || {
        status: "PAID",
        totalAmount: totalValue,
        paidAmount: totalValue,
        currency: "USD",
      },
      qrCode: {
        id: batchData.batchId,
        releaseCode: batchData.batchCode,
        cargoIds: batchData.cargoItems.map((item) => item.id),
        batchId: batchData.batchId,
        authorizedBy: personnelInfo.authorizedBy,
        authorizedDate: now.toISOString(),
        expiryDate: new Date(
          now.getTime() + 30 * 24 * 60 * 60 * 1000
        ).toISOString(),
        verificationUrl: `${window.location.origin}/release-authorization/${batchData.batchId}`,
      },
      notes: options?.notes,
      specialInstructions: options?.specialInstructions,
      requiresPhotoId: true,
      requiresSignature: true,
      requiresWitness: false,
    };

    // Generate the PDF document
    const result = await generateReleaseAuthorizationInvoice(
      releaseData,
      options?.templateOptions
    );

    if (!result.success || !result.data) {
      return {
        success: false,
        error: result.error || "Failed to generate document",
      };
    }

    // Auto-download if requested
    if (options?.autoDownload !== false) {
      downloadPDF(result.data.pdfBlob, result.data.fileName);
    }

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    console.error("Error generating batch release document:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Preview release document data without generating PDF
 */
export function previewReleaseData(
  shipment: ShipmentForRelease,
  personnelInfo: PersonnelInfo,
  additionalData?: Parameters<typeof convertShipmentToReleaseData>[2]
): ReleaseAuthorizationData {
  return convertShipmentToReleaseData(shipment, personnelInfo, additionalData);
}
