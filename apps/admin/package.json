{"name": "admin", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack -H shamwaafrica.host -p 3000 --experimental-https", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit"}, "dependencies": {"@aws-sdk/client-s3": "^3.828.0", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@nestjs/config": "^3.3.0", "@reduxjs/toolkit": "^2.8.2", "@supabase/realtime-js": "^2.11.10", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.9", "@types/bcryptjs": "^3.0.0", "@types/google-map-react": "^2.1.10", "bcryptjs": "^3.0.2", "cookies-next": "^6.0.0", "framer-motion": "^12.9.4", "google-map-react": "^2.2.5", "js-abbreviation-number": "^1.4.0", "lucide-react": "^0.475.0", "next": "^15.2.3", "next-themes": "^0.4.4", "qrcode": "^1.5.4", "react": "^19.0.0", "react-doc-viewer": "^0.1.14", "react-dom": "^19.0.0", "react-qr-scanner": "1.0.0-alpha.11", "react-redux": "^9.2.0", "recharts": "^2.15.3", "redux-persist": "^6.0.0", "uuid": "^9.0.1", "vaul": "^1.1.2", "xlsx": "^0.18.5"}, "devDependencies": {"@types/node": "^20", "@types/qrcode": "^1.5.5", "@types/react": "^19", "@types/react-dom": "^19", "@types/redux-persist": "^4.3.1", "@types/xlsx": "^0.0.36", "@workspace/eslint-config": "workspace:^", "@workspace/mail": "workspace:*", "@workspace/messaging": "workspace:*", "@workspace/pdf-generator": "workspace:*", "@workspace/typescript-config": "workspace:*", "@workspace/ui": "workspace:*", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5.7.3"}}