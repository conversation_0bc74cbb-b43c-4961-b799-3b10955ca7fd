FROM node:lts-alpine AS base

ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable

# ----------------------
# Build Stage
# ----------------------
FROM base AS build

WORKDIR /repo

ENV NEXT_TELEMETRY_DISABLED=1
ENV TURBO_TELEMETRY_DISABLED=1

RUN apk add --no-cache libc6-compat

# Copy lockfiles and manifests
COPY .npmrc pnpm-lock.yaml package.json turbo.json pnpm-workspace.yaml ./
COPY services/api/ services/api/


# Install dependencies with cache
RUN --mount=type=cache,id=pnpm,target=/pnpm/store \
    pnpm install --frozen-lockfile

# Copy the whole repo (after installing deps to preserve cache)
COPY . .

# Build only the api service
RUN pnpm --filter shamwaa-logistics --prod deploy api
RUN cd api && pnpm build

# ----------------------
# Runtime Stage
# ----------------------
FROM base AS web

WORKDIR /app

ENV PORT=8080

# Copy standalone build output from Next.js
COPY --from=build /repo/api/dist ./
COPY --from=build /repo/api/node_modules node_modules

EXPOSE 8880

CMD ["node", "main.js"]
