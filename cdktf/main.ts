import { App, TerraformOutput, TerraformStack } from "cdktf";
import { ec2, ecs, iam, provider } from "@cdktf/provider-aws";

import { Construct } from "constructs";

class ShamwaaInfraStack extends TerraformStack {
  constructor(scope: Construct, id: string) {
    super(scope, id);

    new provider.AwsProvider(this, "AWS", {
      region: "eu-west-1", // or your region
    });

    // 1. VPC
    const vpc = new ec2.Vpc(this, "Vpc", {
      cidrBlock: "10.0.0.0/16",
      enableDnsSupport: true,
      enableDnsHostnames: true,
      tags: { Name: "shadcn-vpc" },
    });

    const subnet = new ec2.Subnet(this, "Subnet", {
      vpcId: vpc.id,
      cidrBlock: "********/24",
      availabilityZone: "us-east-1a",
      mapPublicIpOnLaunch: true,
      tags: { Name: "shadcn-subnet" },
    });

    const igw = new ec2.InternetGateway(this, "InternetGateway", {
      vpcId: vpc.id,
      tags: { Name: "shadcn-igw" },
    });

    const routeTable = new ec2.RouteTable(this, "RouteTable", {
      vpcId: vpc.id,
      tags: { Name: "shadcn-rt" },
    });

    new ec2.Route(this, "Route", {
      routeTableId: routeTable.id,
      destinationCidrBlock: "0.0.0.0/0",
      gatewayId: igw.id,
    });

    new ec2.RouteTableAssociation(this, "RouteTableAssoc", {
      subnetId: subnet.id,
      routeTableId: routeTable.id,
    });

    // 2. Security Group
    const sg = new ec2.SecurityGroup(this, "SecurityGroup", {
      name: "shamwaa-sg",
      vpcId: vpc.id,
      ingress: [
        {
          fromPort: 3000,
          toPort: 3000,
          protocol: "tcp",
          cidrBlocks: ["0.0.0.0/0"],
        },
      ],
      egress: [
        {
          fromPort: 0,
          toPort: 0,
          protocol: "-1",
          cidrBlocks: ["0.0.0.0/0"],
        },
      ],
    });

    // 3. ECR Repository
    const ecrRepo = new ec2.DataAwsEcrRepository(this, "ECRRepo", {
      name: "shamwaa-repo",
    });

    // 4. ECS Cluster
    const cluster = new ecs.EcsCluster(this, "EcsCluster", {
      name: "shamwaa-cluster",
    });

    // 5. IAM Role for ECS
    const taskExecutionRole = new iam.IamRole(this, "TaskExecutionRole", {
      name: "ecsTaskExecutionRole",
      assumeRolePolicy: JSON.stringify({
        Version: "2012-10-17",
        Statement: [
          {
            Effect: "Allow",
            Principal: { Service: "ecs-tasks.amazonaws.com" },
            Action: "sts:AssumeRole",
          },
        ],
      }),
    });

    // 6. ECS Task Definition
    const taskDef = new ecs.EcsTaskDefinition(this, "TaskDef", {
      family: "shamwaa-task",
      requiresCompatibilities: ["FARGATE"],
      networkMode: "awsvpc",
      cpu: "256",
      memory: "512",
      executionRoleArn: taskExecutionRole.arn,
      containerDefinitions: JSON.stringify([
        {
          name: "web",
          image: `${ecrRepo.repositoryUrl}:latest`,
          portMappings: [{ containerPort: 3000 }],
        },
      ]),
    });

    // 7. ECS Service
    new ecs.EcsService(this, "EcsService", {
      name: "shamwaa-service",
      cluster: cluster.id,
      taskDefinition: taskDef.arn,
      launchType: "FARGATE",
      desiredCount: 1,
      networkConfiguration: {
        subnets: [subnet.id],
        securityGroups: [sg.id],
        assignPublicIp: true,
      },
    });

    // Outputs
    new TerraformOutput(this, "repository_url", {
      value: ecrRepo.repositoryUrl,
    });

    new TerraformOutput(this, "vpc_id", {
      value: vpc.id,
    });
  }
}

const app = new App();
new ShamwaaInfraStack(app, "shamwaa-infra");
app.synth();
