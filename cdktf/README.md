# CDKTF

## Step 1: Create an IAM Role in AWS for GitHub OIDC

Policy: **Trust Relationship**
Go to AWS → IAM → Roles → Create Role
Choose Web Identity → Provider = GitHub

Use this trust policy:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Federated": "arn:aws:iam::<ACCOUNT_ID>:oidc-provider/token.actions.githubusercontent.com"
      },
      "Action": "sts:AssumeRoleWithWebIdentity",
      "Condition": {
        "StringLike": {
          "token.actions.githubusercontent.com:sub": "repo:<YOUR_GITHUB_USERNAME>/<REPO_NAME>:ref:refs/heads/main"
        }
      }
    }
  ]
}
```

## Attach a Policy

Give the role the permissions needed for:

- ECR access
- ECS deploys
- Logs (optional)
- CDKTF deploy (optional)

Start with something like this:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "ecr:GetAuthorizationToken",
        "ecr:BatchCheckLayerAvailability",
        "ecr:GetDownloadUrlForLayer",
        "ecr:PutImage",
        "ecr:InitiateLayerUpload",
        "ecr:CompleteLayerUpload",
        "ecr:UploadLayerPart",
        "ecs:UpdateService",
        "ecs:DescribeServices",
        "logs:*"
      ],
      "Resource": "*"
    }
  ]
}
```

📛 Name the role: GitHubActionsOIDCRole

## OUTPUTS

```text
Outputs:
shamwaa-infra  repository_url = "505787607892.dkr.ecr.ap-southeast-1.amazonaws.com/shamwaa-repo"
               vpc_id = "vpc-0da24776d6e570ce5"

  shamwaa-infra
  repository_url = 505787607892.dkr.ecr.ap-southeast-1.amazonaws.com/shamwaa-repo
  vpc_id = vpc-0da24776d6e570ce5
```
