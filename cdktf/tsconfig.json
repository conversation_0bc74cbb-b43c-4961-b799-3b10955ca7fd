{"compilerOptions": {"alwaysStrict": true, "declaration": true, "experimentalDecorators": true, "inlineSourceMap": true, "inlineSources": true, "lib": ["ES2019"], "module": "CommonJS", "noEmitOnError": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "resolveJsonModule": true, "strict": true, "strictNullChecks": true, "strictPropertyInitialization": true, "stripInternal": true, "target": "ES2018", "incremental": true, "skipLibCheck": true}, "include": ["**/*.ts", "./.gen/**/*"], "exclude": ["node_modules", "cdktf.out"]}