{"name": "cdktf", "version": "1.0.0", "main": "main.js", "types": "main.ts", "license": "MPL-2.0", "private": true, "scripts": {"get": "cdktf get", "build": "tsc", "synth": "cdktf synth", "compile": "tsc --pretty", "watch": "tsc -w", "test": "jest", "test:watch": "jest --watch", "upgrade": "npm i cdktf@latest cdktf-cli@latest", "upgrade:next": "npm i cdktf@next cdktf-cli@next"}, "engines": {"node": ">=18.0"}, "dependencies": {"@cdktf/provider-aws": "19.62.0", "cdktf": "^0.20.12", "constructs": "^10.4.2"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^22.15.2", "jest": "^29.7.0", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}