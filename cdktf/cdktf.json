{"language": "typescript", "app": "npx ts-node infra.ts", "projectId": "635294a1-b9ce-41b4-b047-b26f7c659eac", "sendCrashReports": "false", "terraformProviders": ["aws@~>5.0"], "terraformModules": [], "context": {"backend": {"s3": {"bucket": "shamwaa-cdktf-state", "key": "infra/terraform.tfstate", "region": "ap-southeast-1", "dynamodb_table": "shamwaa-cdktf-locks", "encrypt": true, "profile": "shamwaa"}}}, "codeMakerOutput": ".gen"}