import { TerraformOutput, TerraformStack } from "cdktf";

import { AcmCertificate } from "../.gen/providers/aws/acm-certificate";
import { AcmCertificateValidation } from "../.gen/providers/aws/acm-certificate-validation";
import { Alb } from "../.gen/providers/aws/alb";
import { AlbListener } from "../.gen/providers/aws/alb-listener";
import { AlbTargetGroup } from "../.gen/providers/aws/alb-target-group";
import { AwsProvider } from "../.gen/providers/aws/provider";
import { CloudwatchLogGroup } from "../.gen/providers/aws/cloudwatch-log-group";
import { Construct } from "constructs";
import { EcrRepository } from "../.gen/providers/aws/ecr-repository";
import { EcsCluster } from "../.gen/providers/aws/ecs-cluster";
import { EcsService } from "../.gen/providers/aws/ecs-service";
import { EcsTaskDefinition } from "../.gen/providers/aws/ecs-task-definition";
import { Eip } from "../.gen/providers/aws/eip";
import { IamRole } from "../.gen/providers/aws/iam-role";
import { IamRolePolicy } from "../.gen/providers/aws/iam-role-policy";
import { IamRolePolicyAttachment } from "../.gen/providers/aws/iam-role-policy-attachment";
import { InternetGateway } from "../.gen/providers/aws/internet-gateway";
import { NatGateway } from "../.gen/providers/aws/nat-gateway";
import { Route } from "../.gen/providers/aws/route";
import { Route53Record } from "../.gen/providers/aws/route53-record";
import { Route53Zone } from "../.gen/providers/aws/route53-zone";
import { RouteTable } from "../.gen/providers/aws/route-table";
import { RouteTableAssociation } from "../.gen/providers/aws/route-table-association";
import { SecurityGroup } from "../.gen/providers/aws/security-group";
import { Subnet } from "../.gen/providers/aws/subnet";
import { Vpc } from "../.gen/providers/aws/vpc";

export class ShamwaaInfraStack extends TerraformStack {
  constructor(scope: Construct, id: string) {
    super(scope, id);

    new AwsProvider(this, "AWS", {
      region: "ap-southeast-1",
      profile: "shamwaa",
      sharedConfigFiles: ["$HOME/.aws/config"],
      sharedCredentialsFiles: ["$HOME/.aws/credentials"],
    });

    const hostedZoneId = "Z09875453EIG4AJS0BZDK";

    // Request certificate
    const certificate = new AcmCertificate(this, "AcmCert", {
      domainName: "qa.shamwaafrica.com",
      validationMethod: "DNS",
      tags: { Name: "mydomain-cert" },
    });
    
    // Create DNS record for validation
    const certValidationRecord = new Route53Record(this, "CertValidationRecord", {
      zoneId: hostedZoneId,
      name: certificate.domainValidationOptions.get(0).resourceRecordName,
      type: certificate.domainValidationOptions.get(0).resourceRecordType,
      records: [certificate.domainValidationOptions.get(0).resourceRecordValue],
      ttl: 60,
    });

    // Validate certificate
    const certValidation = new AcmCertificateValidation(this, "CertValidation", {
      certificateArn: certificate.arn,
      validationRecordFqdns: [certValidationRecord.fqdn],
    });

    // VPC
    const vpc = new Vpc(this, "Vpc", {
      cidrBlock: "10.0.0.0/16",
      enableDnsSupport: true,
      enableDnsHostnames: true,
      tags: { Name: "shadcn-vpc" },
    });

    const eip = new Eip(this, "NatEip", {
      vpc: true,
    });

    // Public Subnets
    const publicSubnet1 = new Subnet(this, "PublicSubnet1", {
      vpcId: vpc.id,
      cidrBlock: "********/24",
      availabilityZone: "ap-southeast-1a",
      mapPublicIpOnLaunch: true,
      tags: { Name: "shamwaa-public-1" },
    });

    const publicSubnet2 = new Subnet(this, "PublicSubnet2", {
      vpcId: vpc.id,
      cidrBlock: "********/24",
      availabilityZone: "ap-southeast-1b",
      mapPublicIpOnLaunch: true,
      tags: { Name: "shamwaa-public-2" },
    });

    // Private Subnets
    const privateSubnet1 = new Subnet(this, "PrivateSubnet1", {
      vpcId: vpc.id,
      cidrBlock: "********/24",
      availabilityZone: "ap-southeast-1a",
      mapPublicIpOnLaunch: false,
      tags: { Name: "shamwaa-private-1" },
    });

    const privateSubnet2 = new Subnet(this, "PrivateSubnet2", {
      vpcId: vpc.id,
      cidrBlock: "********/24",
      availabilityZone: "ap-southeast-1b",
      mapPublicIpOnLaunch: false,
      tags: { Name: "shamwaa-private-2" },
    });

    const natGateway = new NatGateway(this, "NatGateway", {
      allocationId: eip.id,
      subnetId: publicSubnet1.id, // Put in a public subnet
    });

    const igw = new InternetGateway(this, "InternetGateway", {
      vpcId: vpc.id,
      tags: { Name: "shamwaa-igw" },
    });

    const routeTable = new RouteTable(this, "RouteTable", {
      vpcId: vpc.id,
      tags: { Name: "shamwaa-rt" },
    });

    new Route(this, "Route", {
      routeTableId: routeTable.id,
      destinationCidrBlock: "0.0.0.0/0",
      gatewayId: igw.id,
    });

    new RouteTableAssociation(this, "PublicAssoc1", {
      subnetId: publicSubnet1.id,
      routeTableId: routeTable.id,
    });

    new RouteTableAssociation(this, "PublicAssoc2", {
      subnetId: publicSubnet2.id,
      routeTableId: routeTable.id,
    });

    const privateRouteTable = new RouteTable(this, "PrivateRouteTable", {
      vpcId: vpc.id,
      tags: { Name: "shamwaa-private-rt" },
    });

    new Route(this, "PrivateDefaultRoute", {
      routeTableId: privateRouteTable.id,
      destinationCidrBlock: "0.0.0.0/0",
      natGatewayId: natGateway.id,
    });

    new RouteTableAssociation(this, "PrivateAssoc1", {
      subnetId: privateSubnet1.id,
      routeTableId: privateRouteTable.id,
    });

    new RouteTableAssociation(this, "PrivateAssoc2", {
      subnetId: privateSubnet2.id,
      routeTableId: privateRouteTable.id,
    });

    // Security Group
    const sg = new SecurityGroup(this, "SecurityGroup", {
      name: "shamwaa-sg",
      vpcId: vpc.id,
      ingress: [
        {
          fromPort: 3000,
          toPort: 3000,
          protocol: "tcp",
          cidrBlocks: ["0.0.0.0/0"],
        },
        {
          fromPort: 80,
          toPort: 80,
          protocol: "tcp",
          cidrBlocks: ["0.0.0.0/0"],
        },
        {
          fromPort: 443,
          toPort: 443,
          protocol: "tcp",
          cidrBlocks: ["0.0.0.0/0"],
        },
      ],
      egress: [
        {
          fromPort: 0,
          toPort: 0,
          protocol: "-1",
          cidrBlocks: ["0.0.0.0/0"],
        },
      ],
    });

    // ECR Repository
    const ecrRepo = new EcrRepository(this, "ECRRepo", {
      name: "shamwaa-repo",
      imageScanningConfiguration: {
        scanOnPush: true,
      },
    });

    // ECS Cluster
    const cluster = new EcsCluster(this, "EcsCluster", {
      name: "shamwaa-cluster",
    });

    // IAM Role
    const taskExecutionRole = new IamRole(this, "TaskExecutionRole", {
      name: "ecsTaskExecutionRole",
      assumeRolePolicy: JSON.stringify({
        Version: "2012-10-17",
        Statement: [
          {
            Effect: "Allow",
            Principal: { Service: "ecs-tasks.amazonaws.com" },
            Action: "sts:AssumeRole",
          },
        ],
      }),
    });

    new IamRolePolicyAttachment(this, "AttachManagedEcsPolicy", {
      role: taskExecutionRole.name,
      policyArn:
        "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy",
    });

    new IamRolePolicy(this, "AttachExtraPermissions", {
      role: taskExecutionRole.name,
      policy: JSON.stringify({
        Version: "2012-10-17",
        Statement: [
          {
            Sid: "AllowTaggingLogs",
            Effect: "Allow",
            Action: ["logs:*"],
            Resource: "*",
          },
        ],
      }),
    });

    new CloudwatchLogGroup(this, "EcsLogGroup", {
      name: "/ecs/shamwaa-service",
      retentionInDays: 7,
    });

    // ECS Task Definition
    const taskDef = new EcsTaskDefinition(this, "TaskDef", {
      family: "shamwaa-task",
      requiresCompatibilities: ["FARGATE"],
      networkMode: "awsvpc",
      cpu: "256",
      memory: "512",
      executionRoleArn: taskExecutionRole.arn,
      containerDefinitions: JSON.stringify([
        {
          name: "web",
          image: `${ecrRepo.repositoryUrl}:latest`,
          portMappings: [{ containerPort: 3000 }],
          logConfiguration: {
            logDriver: "awslogs",
            options: {
              "awslogs-region": "ap-southeast-1",
              "awslogs-group": "/ecs/shamwaa-service",
              "awslogs-stream-prefix": "ecs",
            },
          },
          environment: [
            {
              name: "NEXT_PUBLIC_SUPABASE_URL",
              value: "https://ocserfgcbpxtfdombtcc.supabase.co/",
            },
            {
              name: "NEXT_PUBLIC_SUPABASE_ANON_KEY",
              value: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9jc2VyZmdjYnB4dGZkb21idGNjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg5MTI5NjksImV4cCI6MjA2NDQ4ODk2OX0.CCmAxT9MzvXfTN5igYaC3v00tKR4cupNZFkNEHJsSWs",
            },
          ],
        },
      ]),
    });

    // Load Balancer
    const alb = new Alb(this, "Alb", {
      name: "shamwaa-alb",
      internal: false,
      loadBalancerType: "application",
      securityGroups: [sg.id],
      subnets: [publicSubnet1.id, publicSubnet2.id],
      tags: {
        Name: "shamwaa-alb",
      },
    });

    const targetGroup = new AlbTargetGroup(this, "TargetGroup", {
      name: "shamwaa-tg",
      port: 3000,
      protocol: "HTTP",
      targetType: "ip",
      vpcId: vpc.id,
      healthCheck: {
        path: "/",
        port: "3000",
        protocol: "HTTP",
      },
    });

    const albListener = new AlbListener(this, "AlbListener", {
      loadBalancerArn: alb.arn,
      port: 443,
      protocol: "HTTPS",
      sslPolicy: "ELBSecurityPolicy-2016-08",
      certificateArn: certificate.arn,
      defaultAction: [
        {
          type: "forward",
          targetGroupArn: targetGroup.arn,
        },
      ],
      dependsOn: [certValidation],
    });

    new AlbListener(this, "HttpRedirectListener", {
      loadBalancerArn: alb.arn,
      port: 80,
      protocol: "HTTP",
      defaultAction: [
        {
          type: "redirect",
          redirect: {
            port: "443",
            protocol: "HTTPS",
            statusCode: "HTTP_301",
          },
        },
      ],
    });

    // ECS Service
    new EcsService(this, "EcsService", {
      name: "shamwaa-service",
      cluster: cluster.id,
      taskDefinition: taskDef.arn,
      launchType: "FARGATE",
      desiredCount: 1,
      networkConfiguration: {
        subnets: [privateSubnet1.id, privateSubnet2.id],
        securityGroups: [sg.id],
        assignPublicIp: false,
      },
      loadBalancer: [
        {
          targetGroupArn: targetGroup.arn,
          containerName: "web",
          containerPort: 3000,
        },
      ],
      dependsOn: [albListener],
    });

    new Route53Record(this, "AlbCnameRecord", {
      zoneId: hostedZoneId,
      name: "qa.shamwaafrica.com.com",
      type: "CNAME",
      ttl: 300,
      records: [alb.dnsName],
    });

    // Outputs
    new TerraformOutput(this, "repository_url", {
      value: ecrRepo.repositoryUrl,
    });

    new TerraformOutput(this, "vpc_id", {
      value: vpc.id,
    });

    new TerraformOutput(this, "alb_dns_name", {
      value: alb.dnsName,
    });
  }
}
