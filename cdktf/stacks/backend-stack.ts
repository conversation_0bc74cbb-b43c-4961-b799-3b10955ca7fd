import { AwsProvider } from "../.gen/providers/aws/provider";
import { Construct } from "constructs";
import { DynamodbTable } from "../.gen/providers/aws/dynamodb-table";
import { S3Bucket } from "../.gen/providers/aws/s3-bucket";
import { TerraformStack } from "cdktf";

export class BackendStack extends TerraformStack {
  constructor(scope: Construct, id: string) {
    super(scope, id);

    new AwsProvider(this, "AWS", {
      region: "ap-southeast-1",
      profile: "shamwaa",
      sharedConfigFiles: ["$HOME/.aws/config"],
      sharedCredentialsFiles: ["$HOME/.aws/credentials"],
    });

    new S3Bucket(this, "StateBucket", {
      bucket: "shamwaa-cdktf-state",
      versioning: { enabled: true },
      tags: { Name: "shamwaa-cdktf-state" },
    });

    new DynamodbTable(this, "StateLockTable", {
      name: "shamwaa-cdktf-locks",
      hashKey: "LockID",
      billingMode: "PAY_PER_REQUEST",
      attribute: [{ name: "LockID", type: "S" }],
      tags: { Name: "shamwaa-cdktf-locks" },
    });
  }
}
