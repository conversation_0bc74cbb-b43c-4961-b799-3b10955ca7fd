SHAMWAA LOGISTICS MANAGEMENT SYSTEM - API DOCUMENTATION
==================================================================

This document provides a comprehensive overview of how the Shamwaa Logistics Management System API works, focusing on each individual TypeORM entity, their relationships, and business logic.

==================================================================
SYSTEM OVERVIEW
==================================================================

The Shamwaa Logistics Management System is a cloud-based Progressive Web Application (PWA) designed to streamline cargo management processes. The API is built using NestJS with TypeORM for database operations and follows a modular architecture with three main domains:

1. SYSTEM MODULE - Core system functionality (users, authentication, notifications, etc.)
2. OPERATIONS MODULE - Cargo and logistics operations (cargo, batches, shipments, etc.)
3. FINANCES MODULE - Financial management (ledgers, transactions)

==================================================================
BASE ENTITY STRUCTURE
==================================================================

All entities in the system extend from a BaseEntity class that provides common fields:

- id: string (UUID, Primary Key)
- created_at: timestamp (Auto-generated creation time)
- updated_at: timestamp (Auto-updated modification time)

This ensures consistent identification and audit trail across all entities.

==================================================================
STATUS ENUM SYSTEM
==================================================================

The system uses a comprehensive Status enum for tracking entity states:

- ACTIVE: Entity is active and operational
- INACTIVE: Entity is deactivated but not deleted
- PENDING: Entity awaiting approval or processing
- COMPLETED: Entity has been successfully processed
- CANCELLED: Entity has been cancelled
- CREATED: Entity has been created but not yet processed
- PROCESSING: Entity is currently being processed
- IN_TRANSIT: Entity (cargo/shipment) is in transit
- DELIVERED: Entity has been delivered
- PICKED_UP: Entity has been picked up
- RELEASED: Entity has been released
- APPROVED: Entity has been approved
- REJECTED: Entity has been rejected

==================================================================
SYSTEM MODULE ENTITIES
==================================================================

1. USER ENTITY (users table)
------------------------------------------------------------------
Purpose: Stores personal information for system users
Fields:
- name: string (required) - User's full name
- status: Status enum (default: ACTIVE) - User's account status
- phone: string (optional, max 20 chars) - Contact phone number
- location: string (optional, max 100 chars) - User's location
- avatar: string (optional, max 255 chars) - Profile picture URL

Relationships:
- OneToOne with Account entity - Each user has one account for authentication

Business Logic:
- Users represent the personal profile information
- Status controls whether user can perform actions
- Avatar and location are optional for enhanced profile information
- Phone number is used for communication and verification

2. ACCOUNT ENTITY (accounts table)
------------------------------------------------------------------
Purpose: Handles authentication and authorization for users
Fields:
- email: string (unique, required) - Login email address
- password: string (required) - Hashed password
- status: Status enum (default: ACTIVE) - Account status

Relationships:
- OneToOne with User entity - Personal profile information
- ManyToOne with Role entity - Determines permissions and access level
- OneToMany with Session entities - Active login sessions
- OneToMany with Recovery entities - Password recovery tokens

Business Logic:
- Central authentication hub linking users to their roles
- Email must be unique across the system
- Password is hashed before storage
- Status controls login access
- Multiple sessions supported for different devices
- Recovery tokens for password reset functionality

3. ROLE ENTITY (roles table)
------------------------------------------------------------------
Purpose: Defines user roles and permissions within departments
Fields:
- name: string (required) - Role name (e.g., "Manager", "Operator")
- status: Status enum (default: ACTIVE) - Role availability

Relationships:
- ManyToOne with Department entity - Roles belong to departments
- OneToMany with Account entities - Multiple accounts can have same role

Business Logic:
- Implements role-based access control (RBAC)
- Roles are department-specific for better organization
- Status controls whether role can be assigned to new accounts
- Hierarchical permission system through department structure

4. DEPARTMENT ENTITY (departments table)
------------------------------------------------------------------
Purpose: Organizes roles and users into functional departments
Fields:
- name: string (required) - Department name
- status: Status enum (default: ACTIVE) - Department status

Relationships:
- OneToMany with Role entities - Departments contain multiple roles

Business Logic:
- Organizational structure for the company
- Groups related roles together
- Status controls whether department is operational
- Enables department-based reporting and access control

5. SESSION ENTITY (sessions table)
------------------------------------------------------------------
Purpose: Manages user authentication sessions
Fields:
- expiry: Date (required) - Session expiration time
- status: Status enum (default: ACTIVE) - Session validity
- token: string (unique, required) - Session token
- refreshToken: string (required) - Token for session renewal

Relationships:
- ManyToOne with Account entity - Sessions belong to accounts

Business Logic:
- Supports multiple concurrent sessions per account
- Automatic expiry management
- Refresh token mechanism for session renewal
- Status allows for session invalidation
- Token uniqueness ensures security

6. RECOVERY ENTITY (recoveries table)
------------------------------------------------------------------
Purpose: Handles password recovery tokens
Fields:
- expiry: Date (required) - Token expiration time
- token: string (unique, required) - Recovery token

Relationships:
- ManyToOne with Account entity - Recovery tokens for accounts

Business Logic:
- Time-limited password recovery process
- Unique tokens prevent token reuse
- Automatic expiry for security
- One-time use tokens

7. NOTIFICATION ENTITY (notifications table)
------------------------------------------------------------------
Purpose: System-wide notification management
Fields:
- name: string (required) - Notification title
- message: string (max 255 chars, required) - Notification content
- details: JSON (optional) - Additional structured data
- status: Status enum (default: ACTIVE) - Notification state
- associatedTable: string (optional) - Related entity table
- associatedId: string (optional) - Related entity ID

Relationships:
- ManyToOne with Account entity - Notifications target specific accounts

Business Logic:
- Generic notification system for all modules
- Can be linked to any entity via associatedTable/associatedId
- JSON details field for rich notification content
- Status controls notification visibility and delivery
- Supports targeted notifications per account

8. DOCUMENT ENTITY (documents table)
------------------------------------------------------------------
Purpose: Document and file management system
Fields:
- name: string (required) - Document name
- path: string (unique, required) - File system path
- category: string (optional) - Document category
- status: Status enum (default: ACTIVE) - Document availability
- details: JSON (optional) - Additional metadata
- description: string (optional) - Document description
- associatedTable: string (optional) - Related entity table
- associatedId: string (optional) - Related entity ID

Relationships:
- ManyToOne with Account entity - Document ownership

Business Logic:
- Central document management for all system entities
- Unique file paths prevent conflicts
- Can be associated with any entity type
- Category system for organization
- JSON details for flexible metadata storage
- Status controls document access and visibility

9. LOGS ENTITY (logs table)
------------------------------------------------------------------
Purpose: System activity logging and audit trail
Fields:
- event: string (required) - Type of event logged
- message: string (required) - Event description
- status: Status enum (default: ACTIVE) - Log entry status
- associatedTable: string (optional) - Related entity table
- associatedId: string (optional) - Related entity ID

Relationships:
- ManyToOne with Account entity - User who triggered the event

Business Logic:
- Comprehensive audit logging system
- Can track events for any entity type
- Event categorization for filtering and analysis
- Account tracking for accountability
- Status allows for log entry management
- Essential for compliance and debugging

10. SCHEDULE ENTITY (schedules table)
------------------------------------------------------------------
Purpose: Task and activity scheduling system
Fields:
- name: string (required) - Schedule name
- status: Status enum (default: ACTIVE) - Schedule status
- start: Date (required) - Schedule start time
- deadline: Date (required) - Schedule deadline
- comment: string (optional) - Additional notes
- associatedTable: string (optional) - Related entity table
- associatedId: string (optional) - Related entity ID
- assigned: string[] (optional) - List of assigned user IDs

Relationships:
- ManyToOne with Account entity - Schedule creator

Business Logic:
- Flexible scheduling system for any entity
- Multiple user assignment capability
- Time-bound activities with start and deadline
- Can be linked to any business entity
- Status tracks schedule lifecycle
- Comments for additional context

11. ASSIGNMENT ENTITY (assignments table)
------------------------------------------------------------------
Purpose: Task assignment and delegation system
Fields:
- name: string (required) - Assignment name
- description: text (required) - Detailed assignment description
- attachment: string (optional) - Supporting document path
- status: Status enum (default: CREATED) - Assignment progress
- associatedTable: string (optional) - Related entity table
- associatedId: string (optional) - Related entity ID
- toList: string[] (optional) - Primary assignees
- ccList: string[] (optional) - Copy recipients

Relationships:
- ManyToOne with Account entity - Assignment creator

Business Logic:
- Task delegation system with multiple recipients
- Rich text descriptions for detailed instructions
- File attachments for supporting materials
- Status tracking from creation to completion
- Can be linked to any business entity
- Separate primary and copy recipient lists

12. APPROVAL ENTITY (approvals table)
------------------------------------------------------------------
Purpose: Approval workflow management
Fields:
- details: JSON (optional) - Approval details and context
- status: Status enum (default: PENDING) - Approval state
- associatedTable: string (optional) - Entity requiring approval
- associatedId: string (optional) - Entity ID requiring approval

Relationships:
- ManyToOne with Account entity - Approver account

Business Logic:
- Generic approval system for any entity
- JSON details for flexible approval context
- Status tracks approval workflow (PENDING/APPROVED/REJECTED)
- Can be applied to any business entity
- Account tracking for approval accountability

==================================================================
OPERATIONS MODULE ENTITIES
==================================================================

13. CUSTOMER ENTITY (customers table)
------------------------------------------------------------------
Purpose: Customer information management
Fields:
- name: string (required) - Customer name
- status: Status enum (default: ACTIVE) - Customer status
- email: string (optional) - Customer email
- phone: string (optional) - Customer phone
- location: text (optional) - Customer address

Relationships:
- ManyToOne with Account entity - Account managing the customer
- OneToMany with Cargo entities - Customer's cargo shipments

Business Logic:
- Central customer database for CRM functionality
- Links customers to their cargo shipments
- Contact information for communication
- Status controls customer account activity
- Location information for delivery coordination

14. CARGO ENTITY (cargos table)
------------------------------------------------------------------
Purpose: Individual cargo item management
Fields:
- trackingNumber: string (required) - Unique cargo identifier
- particular: string (required) - Cargo description
- status: Status enum (default: CREATED) - Cargo lifecycle status
- category: Category enum (DANGEROUS/SAFE) - Safety classification
- dimensionUnit: DimensionUnit enum - Unit for measurements
- dimensionLength: number (required) - Cargo length
- dimensionWidth: number (required) - Cargo width
- dimensionHeight: number (required) - Cargo height
- cbmUnit: DimensionUnit enum - CBM measurement unit
- cbmValue: number (required) - Cubic meter value
- weightUnit: WeightUnit enum - Weight measurement unit
- weightValue: number (required) - Cargo weight
- unitPrice: float (required) - Price per unit
- totalPrice: float (required) - Total cargo price

Relationships:
- ManyToOne with Customer entity - Cargo owner
- ManyToOne with Account entity - Account managing the cargo
- ManyToOne with Batch entity - Batch containing this cargo
- ManyToOne with User entity (receiver) - Designated receiver

Business Logic:
- Core entity representing individual cargo items
- Comprehensive tracking from creation to delivery
- Detailed physical specifications for logistics planning
- Safety categorization for handling requirements
- Pricing information for financial tracking
- Multiple measurement units for international compatibility
- Links to batch for consolidated shipping
- Designated receiver for delivery verification

15. BATCH ENTITY (batches table)
------------------------------------------------------------------
Purpose: Cargo consolidation and batch management
Fields:
- name: string (required) - Batch name
- code: string (unique, required) - Batch identifier
- type: BatchType enum - Batch classification
- status: Status enum (default: CREATED) - Batch processing status
- length: float (required) - Batch container length
- width: float (required) - Batch container width
- height: float (required) - Batch container height
- cbmUnit: DimensionUnit enum - CBM measurement unit
- cbmValue: number (required) - Total cubic meter value
- weight: number (required) - Total batch weight

Relationships:
- ManyToOne with Freight entity - Transport method
- ManyToOne with Account entity - Account managing the batch
- OneToMany with Cargo entities - Cargo items in the batch
- OneToMany with Shipment entities - Shipment tracking records

Business Logic:
- Consolidates multiple cargo items for efficient shipping
- Container specifications for logistics planning
- Unique batch codes for easy identification
- Status tracking through batch lifecycle
- Weight and volume calculations for freight planning
- Links to freight for transport method
- Multiple shipment records for tracking

16. FREIGHT ENTITY (freights table)
------------------------------------------------------------------
Purpose: Transportation method and vehicle management
Fields:
- name: string (required) - Freight name
- code: string (unique, required) - Freight identifier
- type: FreightType enum - Transportation method
- vehicle: string (optional) - Vehicle information
- capacityUnit: WeightUnit enum - Capacity measurement unit
- capacityValue: float (required) - Maximum capacity

Relationships:
- ManyToOne with Account entity - Account managing the freight
- OneToMany with Batch entities - Batches using this freight
- OneToMany with Shipment entities - Shipment records

Business Logic:
- Defines transportation methods and vehicles
- Capacity management for load planning
- Unique freight codes for identification
- Type classification for operational planning
- Vehicle details for logistics coordination
- Links to batches and shipments for tracking

17. SHIPMENT ENTITY (shipments table)
------------------------------------------------------------------
Purpose: Real-time cargo tracking and shipment management
Fields:
- trackingNumberType: TrackingType enum - Type of tracking
- trackingNumber: string (max 500 chars) - External tracking number
- arrival: Date (optional) - Actual arrival time
- departure: Date (optional) - Actual departure time
- estimatedTimeOfArrival: Date (optional) - ETA
- estimatedTimeOfElapsed: Date (optional) - Estimated completion
- coordinates: string (optional) - GPS coordinates
- attachment: string (max 500 chars, optional) - Supporting documents

Relationships:
- ManyToOne with Account entity - Account managing the shipment
- ManyToOne with Freight entity - Transportation method
- ManyToOne with Batch entity - Batch being shipped

Business Logic:
- Real-time tracking of cargo movements
- Integration with external tracking systems
- GPS coordinate tracking for live updates
- Estimated vs actual time tracking
- Document attachments for proof of delivery
- Links freight, batch, and tracking information
- Supports multiple tracking number types

18. HANDOVER ENTITY (handovers table)
------------------------------------------------------------------
Purpose: Secure cargo handover and verification
Fields:
- handoverDate: Date (required) - When handover occurred
- handoverNotes: string (optional) - Additional notes
- qrVerification: boolean (default: false) - QR code verification status
- biometricVerification: boolean (default: false) - Biometric verification status
- verificationData: JSON (optional) - Verification details
- status: Status enum (default: COMPLETED) - Handover status
- locationData: JSON (optional) - Location information

Relationships:
- ManyToOne with Account entity (sender) - Account handing over cargo
- ManyToOne with Account entity (receiver) - Account receiving cargo
- ManyToOne with Cargo entity - Cargo being handed over

Business Logic:
- Secure cargo transfer verification system
- Dual verification methods (QR code and biometric)
- Timestamp tracking for accountability
- Location data for delivery confirmation
- JSON storage for flexible verification data
- Notes field for additional context
- Status tracking for handover completion

==================================================================
FINANCES MODULE ENTITIES
==================================================================

19. LEDGER ENTITY (ledgers table)
------------------------------------------------------------------
Purpose: Financial account and category management
Fields:
- name: string (required) - Ledger name
- status: Status enum (default: ACTIVE) - Ledger status
- tags: string[] (optional) - Categorization tags
- associatedTable: string (optional) - Related entity table
- associatedId: string (optional) - Related entity ID

Relationships:
- ManyToOne with Account entity - Account managing the ledger
- OneToMany with Transaction entities - Financial transactions

Business Logic:
- Financial categorization and organization
- Can be linked to any business entity
- Tag system for flexible categorization
- Status controls ledger availability
- Container for related transactions
- Supports entity-specific financial tracking

20. TRANSACTION ENTITY (transactions table)
------------------------------------------------------------------
Purpose: Individual financial transaction records
Fields:
- name: string (required) - Transaction description
- status: Status enum (default: PENDING) - Transaction status
- tags: string[] (optional) - Categorization tags
- context: string (optional) - Additional context
- value: number (required) - Transaction value
- amount: float (required) - Transaction amount

Relationships:
- ManyToOne with Ledger entity - Parent ledger
- ManyToOne with Account entity - Account managing the transaction

Business Logic:
- Individual financial transaction records
- Dual value/amount fields for different purposes
- Status tracking for transaction processing
- Tag system for categorization
- Context field for additional information
- Links to specific ledgers for organization

==================================================================
ENTITY RELATIONSHIPS AND BUSINESS FLOW
==================================================================

AUTHENTICATION FLOW:
User <-> Account <-> Role <-> Department
- Users have one account for authentication
- Accounts have roles that determine permissions
- Roles belong to departments for organization

CARGO MANAGEMENT FLOW:
Customer -> Cargo -> Batch -> Freight -> Shipment -> Handover
- Customers own cargo items
- Cargo items are consolidated into batches
- Batches are assigned to freight methods
- Shipments track the cargo movement
- Handovers verify secure delivery

SYSTEM OPERATIONS FLOW:
Account -> Notifications/Documents/Logs/Schedules/Assignments
- All system operations are linked to accounts
- Notifications keep users informed
- Documents provide file management
- Logs maintain audit trails
- Schedules manage time-based activities
- Assignments delegate tasks

FINANCIAL FLOW:
Account -> Ledger -> Transaction
- Accounts manage financial ledgers
- Ledgers categorize financial activities
- Transactions record individual financial events

APPROVAL FLOW:
Any Entity -> Approval -> Account (Approver)
- Any entity can require approval
- Approvals are managed by specific accounts
- Status tracking through approval workflow

==================================================================
STATUS LIFECYCLE MANAGEMENT
==================================================================

CARGO STATUS FLOW:
CREATED -> PROCESSING -> IN_TRANSIT -> DELIVERED -> COMPLETED

BATCH STATUS FLOW:
CREATED -> PROCESSING -> IN_TRANSIT -> DELIVERED -> COMPLETED

SHIPMENT STATUS FLOW:
CREATED -> IN_TRANSIT -> DELIVERED -> COMPLETED

HANDOVER STATUS FLOW:
PENDING -> COMPLETED

TRANSACTION STATUS FLOW:
PENDING -> COMPLETED/CANCELLED

APPROVAL STATUS FLOW:
PENDING -> APPROVED/REJECTED

==================================================================
SECURITY AND AUDIT FEATURES
==================================================================

1. ROLE-BASED ACCESS CONTROL:
   - Department -> Role -> Account hierarchy
   - Status-based access control
   - Account-level permissions

2. AUDIT TRAIL:
   - All entities have created_at/updated_at timestamps
   - Logs entity tracks all system events
   - Account linking for accountability

3. SECURE HANDOVERS:
   - QR code verification
   - Biometric authentication
   - Location tracking
   - Dual verification system

4. SESSION MANAGEMENT:
   - Token-based authentication
   - Session expiry management
   - Refresh token mechanism
   - Multiple session support

==================================================================
INTEGRATION CAPABILITIES
==================================================================

1. EXTERNAL TRACKING:
   - Multiple tracking number types
   - External system integration
   - Real-time coordinate updates

2. DOCUMENT MANAGEMENT:
   - File system integration
   - Document categorization
   - Entity association

3. NOTIFICATION SYSTEM:
   - Real-time notifications
   - Multi-channel delivery
   - Entity-specific notifications

4. FINANCIAL INTEGRATION:
   - Flexible transaction recording
   - Multi-currency support
   - Entity-linked financial tracking

==================================================================
SCALABILITY FEATURES
==================================================================

1. MODULAR ARCHITECTURE:
   - System, Operations, and Finances modules
   - Independent entity management
   - Cross-module relationships

2. FLEXIBLE ASSOCIATIONS:
   - Generic entity linking (associatedTable/associatedId)
   - JSON data fields for extensibility
   - Tag-based categorization

3. STATUS-BASED CONTROL:
   - Entity lifecycle management
   - Operational status control
   - Flexible state transitions

==================================================================
CONCLUSION
==================================================================

The Shamwaa Logistics Management System API provides a comprehensive, scalable, and secure platform for managing logistics operations. The TypeORM entity structure supports:

- Complete cargo lifecycle management
- Robust authentication and authorization
- Comprehensive audit and logging
- Flexible financial tracking
- Real-time notifications and updates
- Secure cargo handover processes
- Integrated document management
- Scalable approval workflows

The system's modular design and flexible entity relationships enable it to adapt to various business requirements while maintaining data integrity and security standards. 