# Swagger Documentation Guide

## Introduction

This document explains how to use the Swagger UI to explore and interact with the Shamwaa Logistics API.

## Available Commands

The following npm commands are available for working with <PERSON>wagger:

```bash
# Start the application with Swagger UI enabled
npm run swagger

# Build the application and generate static Swagger documentation
npm run swagger:build

# Generate only the Swagger documentation (after building)
npm run swagger:generate
```

## Accessing Swagger UI

When the application is running, Swagger UI is available at:
```
http://localhost:3000/api/docs
```

## Authentication

To use protected endpoints:

1. First, make a request to `/api/auth/login` to get a JWT token
2. Click the "Authorize" button at the top of the Swagger UI
3. Enter your token in the format: `Bearer your_token_here`
4. Click "Authorize" to apply the token to all requests

## Endpoints Organization

The API endpoints are organized into the following categories:

- **Auth** - Authentication and user management
- **Users** - User administration
- **Customers** - Customer management
- **Cargos** - Cargo handling and tracking
- **Batches** - Batch operations for groups of cargo
- **Shipments** - Shipment tracking and updates
- **Handovers** - Cargo handover operations
- **Finances** - Financial operations
- **Reports** - Reporting and analytics
- **System** - System utilities and settings
- **Biometrics** - Biometric authentication

## Making API Requests

To test an API endpoint:

1. Click on the endpoint you want to try
2. Click the "Try it out" button
3. Fill in the required parameters
4. Click "Execute"
5. View the response below

## Models

The Swagger UI provides detailed information about the data models used in the API. Click on the "Schema" section to explore the available models.

## Exporting Documentation

You can export the API documentation in different formats:

1. Click on the server dropdown at the top of the page
2. Select "Export" from the menu
3. Choose the format you want (JSON or YAML)

## Best Practices

- Always check the required parameters for each endpoint
- Use the "Models" section to understand the expected data structures
- For file uploads, use the multi-part form data fields provided
- Check the response codes to understand possible outcomes