{"name": "shamwaa-logistics", "version": "0.0.1", "description": "Shamwaa Logistics Management System", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "swagger": "nest start --watch --entryFile=main.js", "swagger:build": "nest build && npm run swagger:generate", "swagger:generate": "node -e \"require('./dist/main.js'); console.log('Swagger documentation generated at api/docs');\""}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.4.17", "@nestjs/jwt": "^10.1.1", "@nestjs/passport": "^10.0.2", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^10.0.0", "@nestjs/swagger": "^7.4.2", "@nestjs/typeorm": "^10.0.0", "@nestjs/websockets": "^10.0.0", "@scarf/scarf": "^1.4.0", "@socket.io/redis-adapter": "^8.3.0", "@socket.io/redis-emitter": "^5.1.0", "@supabase/supabase-js": "^2.39.0", "@types/bcryptjs": "^3.0.0", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "canvas": "^3.1.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "express-list-routes": "^1.2.4", "ioredis": "^5.3.2", "jsqr": "^1.4.0", "multer": "^1.4.5-lts.2", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "pg": "^8.11.3", "qrcode": "^1.5.3", "redis": "^4.6.13", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "socket.io": "^4.7.2", "swagger-ui-express": "^5.0.0", "typeorm": "^0.3.17", "uuid": "^9.0.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.1", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^1.4.11", "@types/node": "^20.17.19", "@types/passport-jwt": "^3.0.13", "@types/qrcode": "^1.5.5", "@types/redis": "^4.0.11", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.5", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}