import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiQuery, 
  ApiBearerAuth 
} from '@nestjs/swagger';
import { ReportsService } from './reports.service';
import { SupabaseAuthGuard } from '../auth/guards/supabase-auth.guard';

@ApiTags('Reports')
@ApiBearerAuth()
@Controller('reports')
@UseGuards(SupabaseAuthGuard)
export class ReportsController {
  constructor(private readonly reportsService: ReportsService) {}

  @Get('cargo-status')
  @ApiOperation({ summary: 'Get cargo status distribution report' })
  @ApiQuery({ name: 'startDate', required: true, description: 'Report start date (YYYY-MM-DD)' })
  @ApiQuery({ name: 'endDate', required: true, description: 'Report end date (YYYY-MM-DD)' })
  @ApiResponse({ status: 200, description: 'Returns status distribution of cargo' })
  getCargoStatusReport(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ) {
    return this.reportsService.getCargoStatusReport(
      new Date(startDate),
      new Date(endDate),
    );
  }

  @Get('cargo-volume')
  @ApiOperation({ summary: 'Get cargo volume over time report' })
  @ApiQuery({ name: 'startDate', required: true, description: 'Report start date (YYYY-MM-DD)' })
  @ApiQuery({ name: 'endDate', required: true, description: 'Report end date (YYYY-MM-DD)' })
  @ApiQuery({ 
    name: 'groupBy', 
    required: false, 
    description: 'Group results by time period',
    enum: ['day', 'week', 'month'],
  })
  @ApiResponse({ status: 200, description: 'Returns cargo volume over time' })
  getCargoVolumeReport(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Query('groupBy') groupBy: 'day' | 'week' | 'month',
  ) {
    return this.reportsService.getCargoVolumeReport(
      new Date(startDate),
      new Date(endDate),
      groupBy,
    );
  }

  @Get('revenue')
  @ApiOperation({ summary: 'Get revenue over time report' })
  @ApiQuery({ name: 'startDate', required: true, description: 'Report start date (YYYY-MM-DD)' })
  @ApiQuery({ name: 'endDate', required: true, description: 'Report end date (YYYY-MM-DD)' })
  @ApiQuery({
    name: 'groupBy',
    required: false,
    description: 'Group results by time period',
    enum: ['day', 'week', 'month'],
  })
  @ApiResponse({ status: 200, description: 'Returns revenue over time' })
  getRevenueReport(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Query('groupBy') groupBy: 'day' | 'week' | 'month',
  ) {
    return this.reportsService.getRevenueReport(
      new Date(startDate),
      new Date(endDate),
      groupBy,
    );
  }

  @Get('customer-activity')
  @ApiOperation({ summary: 'Get customer activity report' })
  @ApiQuery({ name: 'startDate', required: true, description: 'Report start date (YYYY-MM-DD)' })
  @ApiQuery({ name: 'endDate', required: true, description: 'Report end date (YYYY-MM-DD)' })
  @ApiResponse({ status: 200, description: 'Returns customer activity metrics' })
  getCustomerActivityReport(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ) {
    return this.reportsService.getCustomerActivityReport(
      new Date(startDate),
      new Date(endDate),
    );
  }

  @Get('shipment-performance')
  @ApiOperation({ summary: 'Get shipment performance report' })
  @ApiQuery({ name: 'startDate', required: true, description: 'Report start date (YYYY-MM-DD)' })
  @ApiQuery({ name: 'endDate', required: true, description: 'Report end date (YYYY-MM-DD)' })
  @ApiResponse({ status: 200, description: 'Returns shipment performance metrics' })
  getShipmentPerformanceReport(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ) {
    return this.reportsService.getShipmentPerformanceReport(
      new Date(startDate),
      new Date(endDate),
    );
  }

  @Get('dashboard-stats')
  @ApiOperation({ summary: 'Get dashboard statistics' })
  @ApiResponse({ status: 200, description: 'Returns key metrics for dashboard' })
  getDashboardStats() {
    return this.reportsService.getDashboardStats();
  }
}