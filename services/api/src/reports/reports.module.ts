import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { ReportsController } from './reports.controller';
import { ReportsService } from './reports.service';

import { Cargo } from '../operations/entities/cargo.entity';
import { Batch } from '../operations/entities/batch.entity';
import { Freight } from '../operations/entities/freight.entity';
import { Shipment } from '../operations/entities/shipment.entity';
import { Customer } from '../operations/entities/customer.entity';
import { Transaction } from '../finances/entities/transaction.entity';
import { Ledger } from '../finances/entities/ledger.entity';
import { SupabaseModule } from '../auth/supabase/supabase.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Cargo,
      Batch,
      Freight,
      Shipment,
      Customer,
      Transaction,
      Ledger,
    ]),
    SupabaseModule,
  ],
  controllers: [ReportsController],
  providers: [ReportsService],
  exports: [ReportsService],
})
export class ReportsModule {}