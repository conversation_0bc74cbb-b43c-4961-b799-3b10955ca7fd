import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, In, Not, IsNull } from 'typeorm';
import { Cargo } from '../operations/entities/cargo.entity';
import { Batch } from '../operations/entities/batch.entity';
import { Freight } from '../operations/entities/freight.entity';
import { Shipment } from '../operations/entities/shipment.entity';
import { Customer } from '../operations/entities/customer.entity';
import { Transaction } from '../finances/entities/transaction.entity';
import { Ledger } from '../finances/entities/ledger.entity';

@Injectable()
export class ReportsService {
  constructor(
    @InjectRepository(Cargo)
    private cargoRepository: Repository<Cargo>,
    @InjectRepository(Batch)
    private batchRepository: Repository<Batch>,
    @InjectRepository(Freight)
    private freightRepository: Repository<Freight>,
    @InjectRepository(Shipment)
    private shipmentRepository: Repository<Shipment>,
    @InjectRepository(Customer)
    private customerRepository: Repository<Customer>,
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
    @InjectRepository(Ledger)
    private ledgerRepository: Repository<Ledger>,
  ) {}

  async getCargoStatusReport(
    startDate: Date,
    endDate: Date,
  ): Promise<{ status: string; count: number }[]> {
    const result = await this.cargoRepository
      .createQueryBuilder('cargo')
      .select('cargo.status', 'status')
      .addSelect('COUNT(cargo.id)', 'count')
      .where('cargo.created_at BETWEEN :startDate AND :endDate', { startDate, endDate })
      .groupBy('cargo.status')
      .getRawMany();

    return result;
  }

  async getCargoVolumeReport(
    startDate: Date,
    endDate: Date,
    groupBy: 'day' | 'week' | 'month' = 'day',
  ): Promise<{ date: string; count: number; volume: number }[]> {
    let dateFormat: string;
    
    switch (groupBy) {
      case 'day':
        dateFormat = 'YYYY-MM-DD';
        break;
      case 'week':
        dateFormat = 'YYYY-WW';
        break;
      case 'month':
        dateFormat = 'YYYY-MM';
        break;
      default:
        dateFormat = 'YYYY-MM-DD';
    }

    const result = await this.cargoRepository
      .createQueryBuilder('cargo')
      .select(`TO_CHAR(cargo.created_at, '${dateFormat}')`, 'date')
      .addSelect('COUNT(cargo.id)', 'count')
      .addSelect('SUM(cargo.cbm_value)', 'volume')
      .where('cargo.created_at BETWEEN :startDate AND :endDate', { startDate, endDate })
      .groupBy(`TO_CHAR(cargo.created_at, '${dateFormat}')`)
      .orderBy('date', 'ASC')
      .getRawMany();

    return result;
  }

  async getRevenueReport(
    startDate: Date,
    endDate: Date,
    groupBy: 'day' | 'week' | 'month' = 'day',
  ): Promise<{ date: string; revenue: number }[]> {
    let dateFormat: string;
    
    switch (groupBy) {
      case 'day':
        dateFormat = 'YYYY-MM-DD';
        break;
      case 'week':
        dateFormat = 'YYYY-WW';
        break;
      case 'month':
        dateFormat = 'YYYY-MM';
        break;
      default:
        dateFormat = 'YYYY-MM-DD';
    }

    const result = await this.transactionRepository
      .createQueryBuilder('transaction')
      .select(`TO_CHAR(transaction.created_at, '${dateFormat}')`, 'date')
      .addSelect('SUM(transaction.amount)', 'revenue')
      .where('transaction.created_at BETWEEN :startDate AND :endDate', { startDate, endDate })
      .andWhere("transaction.status = 'completed'")
      .groupBy(`TO_CHAR(transaction.created_at, '${dateFormat}')`)
      .orderBy('date', 'ASC')
      .getRawMany();

    return result;
  }

  async getCustomerActivityReport(
    startDate: Date,
    endDate: Date,
  ): Promise<{ customerId: string; customerName: string; cargoCount: number; totalValue: number }[]> {
    const result = await this.cargoRepository
      .createQueryBuilder('cargo')
      .select('customer.id', 'customerId')
      .addSelect('customer.name', 'customerName')
      .addSelect('COUNT(cargo.id)', 'cargoCount')
      .addSelect('SUM(cargo.total_price)', 'totalValue')
      .leftJoin('cargo.customer', 'customer')
      .where('cargo.created_at BETWEEN :startDate AND :endDate', { startDate, endDate })
      .groupBy('customer.id')
      .addGroupBy('customer.name')
      .orderBy('cargoCount', 'DESC')
      .getRawMany();

    return result;
  }

  async getShipmentPerformanceReport(
    startDate: Date,
    endDate: Date,
  ): Promise<{ freightId: string; freightName: string; shipmentCount: number; avgTransitDays: number }[]> {
    const result = await this.shipmentRepository
      .createQueryBuilder('shipment')
      .select('freight.id', 'freightId')
      .addSelect('freight.name', 'freightName')
      .addSelect('COUNT(shipment.id)', 'shipmentCount')
      .addSelect('AVG(EXTRACT(EPOCH FROM (shipment.arrival - shipment.departure))/86400)', 'avgTransitDays')
      .leftJoin('shipment.freight', 'freight')
      .where('shipment.created_at BETWEEN :startDate AND :endDate', { startDate, endDate })
      .andWhere('shipment.arrival IS NOT NULL')
      .andWhere('shipment.departure IS NOT NULL')
      .groupBy('freight.id')
      .addGroupBy('freight.name')
      .orderBy('shipmentCount', 'DESC')
      .getRawMany();

    return result;
  }

  async getDashboardStats(): Promise<any> {
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    
    // Active cargo count
    const activeCargos = await this.cargoRepository.count({
      where: {
        status: In(['created', 'processing', 'in_transit']),
      },
    });
    
    // Customers count
    const customers = await this.customerRepository.count();
    
    // Shipments in transit
    const inTransitShipments = await this.shipmentRepository.count({
      where: {
        departure: Not(IsNull()),
        arrival: IsNull(),
      },
    });
    
    // Monthly revenue
    const monthlyRevenue = await this.transactionRepository
      .createQueryBuilder('transaction')
      .select('SUM(transaction.amount)', 'total')
      .where('transaction.created_at >= :startDate', { startDate: startOfMonth })
      .andWhere("transaction.status = 'completed'")
      .getRawOne();
    
    return {
      activeCargos,
      customers,
      inTransitShipments,
      monthlyRevenue: monthlyRevenue?.total || 0,
    };
  }
}