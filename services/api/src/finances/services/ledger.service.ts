import { Injectable, NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Ledger } from '../entities/ledger.entity';
import { Account } from '../../system/entities/account.entity';
import { CreateLedgerDto } from '../dto/create-ledger.dto';
import { UpdateLedgerDto } from '../dto/update-ledger.dto';
import { LogsService } from '../../system/services/logs.service';
import { Status } from '../../common/enums/status.enum';

@Injectable()
export class LedgerService {
  constructor(
    @InjectRepository(Ledger)
    private ledgerRepository: Repository<Ledger>,
    private logsService: LogsService,
  ) {}

  async create(createLedgerDto: CreateLedgerDto, account: Account): Promise<Ledger> {
    try {
      const ledger = this.ledgerRepository.create({
        ...createLedgerDto,
        account,
      });

      const savedLedger = await this.ledgerRepository.save(ledger);

      // Log the ledger creation
      await this.logsService.create({
        event: 'Ledger Created',
        message: `Ledger "${createLedgerDto.name}" created`,
        associatedTable: 'ledgers',
        associatedId: savedLedger.id,
      }, account);

      return savedLedger;
    } catch (error) {
      throw new InternalServerErrorException('Failed to create ledger');
    }
  }

  async findAll(options: {
    skip: number;
    take: number;
    associatedTable?: string;
    associatedId?: string;
  }): Promise<{ ledgers: Ledger[]; total: number }> {
    const { skip, take, associatedTable, associatedId } = options;

    const queryBuilder = this.ledgerRepository.createQueryBuilder('ledger')
      .leftJoinAndSelect('ledger.account', 'account')
      .leftJoinAndSelect('account.user', 'user')
      .leftJoinAndSelect('ledger.transactions', 'transactions');

    if (associatedTable) {
      queryBuilder.andWhere('ledger.associatedTable = :associatedTable', { associatedTable });
    }

    if (associatedId) {
      queryBuilder.andWhere('ledger.associatedId = :associatedId', { associatedId });
    }

    queryBuilder.skip(skip).take(take);

    const [ledgers, total] = await queryBuilder.getManyAndCount();

    return { ledgers, total };
  }

  async findOne(id: string): Promise<Ledger> {
    const ledger = await this.ledgerRepository.findOne({
      where: { id },
      relations: ['account', 'account.user', 'transactions'],
    });

    if (!ledger) {
      throw new NotFoundException(`Ledger with ID ${id} not found`);
    }

    return ledger;
  }

  async update(id: string, updateLedgerDto: UpdateLedgerDto, account: Account): Promise<Ledger> {
    const ledger = await this.findOne(id);

    try {
      // Update ledger
      Object.assign(ledger, updateLedgerDto);

      const updatedLedger = await this.ledgerRepository.save(ledger);

      // Log the update
      await this.logsService.create({
        event: 'Ledger Updated',
        message: `Ledger "${ledger.name}" updated`,
        associatedTable: 'ledgers',
        associatedId: ledger.id,
      }, account);

      return updatedLedger;
    } catch (error) {
      throw new InternalServerErrorException('Failed to update ledger');
    }
  }

  async remove(id: string, account: Account): Promise<void> {
    const ledger = await this.findOne(id);

    try {
      // Don't actually delete, just mark as inactive
      ledger.status = Status.INACTIVE;
      await this.ledgerRepository.save(ledger);

      // Log the deletion
      await this.logsService.create({
        event: 'Ledger Deactivated',
        message: `Ledger "${ledger.name}" has been deactivated`,
        associatedTable: 'ledgers',
        associatedId: ledger.id,
      }, account);
    } catch (error) {
      throw new InternalServerErrorException('Failed to deactivate ledger');
    }
  }
}