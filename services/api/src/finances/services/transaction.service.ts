import { Injectable, NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Transaction } from '../entities/transaction.entity';
import { Ledger } from '../entities/ledger.entity';
import { Account } from '../../system/entities/account.entity';
import { CreateTransactionDto } from '../dto/create-transaction.dto';
import { UpdateTransactionDto } from '../dto/update-transaction.dto';
import { LogsService } from '../../system/services/logs.service';
import { Status } from '../../common/enums/status.enum';

@Injectable()
export class TransactionService {
  constructor(
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
    @InjectRepository(Ledger)
    private ledgerRepository: Repository<Ledger>,
    private logsService: LogsService,
  ) {}

  async create(createTransactionDto: CreateTransactionDto, account: Account): Promise<Transaction> {
    try {
      const { ledgerId, ...transactionData } = createTransactionDto;

      // Find ledger
      const ledger = await this.ledgerRepository.findOne({
        where: { id: ledgerId },
      });

      if (!ledger) {
        throw new NotFoundException(`Ledger with ID ${ledgerId} not found`);
      }

      // Create transaction
      const transaction = this.transactionRepository.create({
        ...transactionData,
        ledger,
        account,
      });

      const savedTransaction = await this.transactionRepository.save(transaction);

      // Log the transaction creation
      await this.logsService.create({
        event: 'Transaction Created',
        message: `Transaction "${createTransactionDto.name}" created for ledger ${ledger.name}`,
        associatedTable: 'transactions',
        associatedId: savedTransaction.id,
      }, account);

      return savedTransaction;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to create transaction');
    }
  }

  async findAll(options: {
    skip: number;
    take: number;
    ledgerId?: string;
    status?: string;
  }): Promise<{ transactions: Transaction[]; total: number }> {
    const { skip, take, ledgerId, status } = options;

    const queryBuilder = this.transactionRepository.createQueryBuilder('transaction')
      .leftJoinAndSelect('transaction.ledger', 'ledger')
      .leftJoinAndSelect('transaction.account', 'account')
      .leftJoinAndSelect('account.user', 'user');

    if (ledgerId) {
      queryBuilder.andWhere('transaction.ledger.id = :ledgerId', { ledgerId });
    }

    if (status) {
      queryBuilder.andWhere('transaction.status = :status', { status });
    }

    queryBuilder.skip(skip).take(take)
      .orderBy('transaction.created_at', 'DESC');

    const [transactions, total] = await queryBuilder.getManyAndCount();

    return { transactions, total };
  }

  async findOne(id: string): Promise<Transaction> {
    const transaction = await this.transactionRepository.findOne({
      where: { id },
      relations: ['ledger', 'account', 'account.user'],
    });

    if (!transaction) {
      throw new NotFoundException(`Transaction with ID ${id} not found`);
    }

    return transaction;
  }

  async update(id: string, updateTransactionDto: UpdateTransactionDto, account: Account): Promise<Transaction> {
    const transaction = await this.findOne(id);

    try {
      const { ledgerId, ...transactionData } = updateTransactionDto;

      // Update ledger if provided
      if (ledgerId && ledgerId !== transaction.ledger?.id) {
        const ledger = await this.ledgerRepository.findOne({
          where: { id: ledgerId },
        });

        if (!ledger) {
          throw new NotFoundException(`Ledger with ID ${ledgerId} not found`);
        }

        transaction.ledger = ledger;
      }

      // Check if status is changing
      const statusChanged = updateTransactionDto.status && updateTransactionDto.status !== transaction.status;
      const oldStatus = transaction.status;

      // Update transaction data
      Object.assign(transaction, transactionData);

      const updatedTransaction = await this.transactionRepository.save(transaction);

      // Log the update
      await this.logsService.create({
        event: 'Transaction Updated',
        message: `Transaction "${transaction.name}" updated${statusChanged ? ` - Status changed from ${oldStatus} to ${transaction.status}` : ''}`,
        associatedTable: 'transactions',
        associatedId: transaction.id,
      }, account);

      return updatedTransaction;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to update transaction');
    }
  }

  async remove(id: string, account: Account): Promise<void> {
    const transaction = await this.findOne(id);

    try {
      // Don't actually delete, just mark as cancelled
      transaction.status = Status.CANCELLED;
      await this.transactionRepository.save(transaction);

      // Log the cancellation
      await this.logsService.create({
        event: 'Transaction Cancelled',
        message: `Transaction "${transaction.name}" has been cancelled`,
        associatedTable: 'transactions',
        associatedId: transaction.id,
      }, account);
    } catch (error) {
      throw new InternalServerErrorException('Failed to cancel transaction');
    }
  }
}