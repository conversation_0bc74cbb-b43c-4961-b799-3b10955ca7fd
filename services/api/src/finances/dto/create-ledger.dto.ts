import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>num, <PERSON>NotEmpty, IsOptional, IsString } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Status } from '../../common/enums/status.enum';

export class CreateLedgerDto {
  @ApiProperty({ description: 'Name of the ledger', example: 'Sales Q2 2025' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiPropertyOptional({ 
    description: 'Ledger status',
    enum: Status,
    default: Status.ACTIVE,
    example: Status.ACTIVE
  })
  @IsOptional()
  @IsEnum(Status)
  status?: Status = Status.ACTIVE;

  @ApiPropertyOptional({ 
    description: 'Tags for categorizing the ledger',
    example: ['sales', 'q2-2025', 'north-region'] 
  })
  @IsOptional()
  @IsArray()
  tags?: string[];

  @ApiPropertyOptional({ description: 'Associated table name', example: 'cargos' })
  @IsOptional()
  @IsString()
  associatedTable?: string;

  @ApiPropertyOptional({ description: 'Associated ID', example: '550e8400-e29b-41d4-a716-446655440000' })
  @IsOptional()
  @IsString()
  associatedId?: string;
}