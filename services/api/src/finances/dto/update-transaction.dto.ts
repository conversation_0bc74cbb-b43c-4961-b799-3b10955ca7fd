import { 
  <PERSON><PERSON>rray, 
  IsEnum, 
  <PERSON>N<PERSON>ber, 
  <PERSON><PERSON>ptional, 
  IsString, 
  IsUUID,
} from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Status } from '../../common/enums/status.enum';

export class UpdateTransactionDto {
  @ApiPropertyOptional({ description: 'Transaction name', example: 'Updated cargo shipping payment' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ 
    description: 'Transaction status',
    enum: Status,
    example: Status.COMPLETED
  })
  @IsOptional()
  @IsEnum(Status)
  status?: Status;

  @ApiPropertyOptional({ 
    description: 'Tags for categorizing the transaction',
    example: ['invoice', 'shipping', 'cargo-123', 'completed'] 
  })
  @IsOptional()
  @IsArray()
  tags?: string[];

  @ApiPropertyOptional({ description: 'Transaction context', example: 'Payment completed for shipping services' })
  @IsOptional()
  @IsString()
  context?: string;

  @ApiPropertyOptional({ description: 'Transaction value', example: 1600 })
  @IsOptional()
  @IsNumber()
  value?: number;

  @ApiPropertyOptional({ description: 'Transaction amount', example: 1600 })
  @IsOptional()
  @IsNumber()
  amount?: number;

  @ApiPropertyOptional({ description: 'ID of the ledger for this transaction', example: '550e8400-e29b-41d4-a716-************' })
  @IsOptional()
  @IsUUID()
  ledgerId?: string;
}