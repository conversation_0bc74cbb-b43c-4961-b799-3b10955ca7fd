import { IsArray, IsEnum, IsOptional, IsString } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Status } from '../../common/enums/status.enum';

export class UpdateLedgerDto {
  @ApiPropertyOptional({ description: 'Name of the ledger', example: 'Updated Sales Q2 2025' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ 
    description: 'Ledger status',
    enum: Status,
    example: Status.INACTIVE
  })
  @IsOptional()
  @IsEnum(Status)
  status?: Status;

  @ApiPropertyOptional({ 
    description: 'Tags for categorizing the ledger',
    example: ['updated-sales', 'q2-2025', 'north-region', 'verified'] 
  })
  @IsOptional()
  @IsArray()
  tags?: string[];

  @ApiPropertyOptional({ description: 'Associated table name', example: 'cargos' })
  @IsOptional()
  @IsString()
  associatedTable?: string;

  @ApiPropertyOptional({ description: 'Associated ID', example: '550e8400-e29b-41d4-a716-446655440000' })
  @IsOptional()
  @IsString()
  associatedId?: string;
}