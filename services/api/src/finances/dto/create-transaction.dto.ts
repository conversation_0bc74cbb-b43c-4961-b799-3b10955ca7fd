import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPositive,
  IsString,
  IsUUID,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Status } from '../../common/enums/status.enum';

export class CreateTransactionDto {
  @ApiProperty({ description: 'Transaction name', example: 'Cargo shipping payment' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiPropertyOptional({ 
    description: 'Transaction status',
    enum: Status,
    default: Status.PENDING,
    example: Status.PENDING
  })
  @IsOptional()
  @IsEnum(Status)
  status?: Status = Status.PENDING;

  @ApiPropertyOptional({ 
    description: 'Tags for categorizing the transaction',
    example: ['invoice', 'shipping', 'cargo-123'] 
  })
  @IsOptional()
  @IsArray()
  tags?: string[];

  @ApiPropertyOptional({ description: 'Transaction context', example: 'Payment for shipping services' })
  @IsOptional()
  @IsString()
  context?: string;

  @ApiProperty({ description: 'Transaction value', example: 1500 })
  @IsNotEmpty()
  @IsNumber()
  value: number;

  @ApiProperty({ description: 'Transaction amount', example: 1500 })
  @IsNotEmpty()
  @IsNumber()
  @IsPositive()
  amount: number;

  @ApiProperty({ description: 'ID of the ledger for this transaction', example: '550e8400-e29b-41d4-a716-************' })
  @IsNotEmpty()
  @IsUUID()
  ledgerId: string;
}