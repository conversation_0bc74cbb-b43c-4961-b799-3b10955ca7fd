import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne } from 'typeorm';
import { BaseEntity } from '../../common/base.entity';
import { Status } from '../../common/enums/status.enum';
import { Account } from '../../system/entities/account.entity';
import { Ledger } from './ledger.entity';

@Entity('transactions')
export class Transaction extends BaseEntity {
  @Column()
  name: string;

  @Column({
    type: 'enum',
    enum: Status,
    default: Status.PENDING
  })
  status: Status;

  @Column('text', { array: true, nullable: true })
  tags: string[];

  @Column({ nullable: true })
  context: string;

  @Column()
  value: number;

  @Column({ type: 'float' })
  amount: number;

  @ManyToOne(() => Ledger, ledger => ledger.transactions)
  @JoinColumn({ name: 'ledger_id' })
  ledger: Ledger;

  @ManyToOne(() => Account)
  @JoinColumn({ name: 'account_id' })
  account: Account;
}