import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { Ledger } from './entities/ledger.entity';
import { Transaction } from './entities/transaction.entity';

import { LedgerController } from './controllers/ledger.controller';
import { TransactionController } from './controllers/transaction.controller';

import { LedgerService } from './services/ledger.service';
import { TransactionService } from './services/transaction.service';
import { SystemModule } from '../system/system.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Ledger,
      Transaction,
    ]),
    SystemModule,
  ],
  controllers: [
    LedgerController,
    TransactionController,
  ],
  providers: [
    LedgerService,
    TransactionService,
  ],
  exports: [
    LedgerService,
    TransactionService,
  ],
})
export class FinancesModule {}