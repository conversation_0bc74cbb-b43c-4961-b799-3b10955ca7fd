import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiParam, 
  ApiQuery, 
  ApiBearerAuth 
} from '@nestjs/swagger';
import { LedgerService } from '../services/ledger.service';
import { CreateLedgerDto } from '../dto/create-ledger.dto';
import { UpdateLedgerDto } from '../dto/update-ledger.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { GetUser } from '../../auth/decorators/get-user.decorator';
import { Account } from '../../system/entities/account.entity';

@ApiTags('Finances')
@ApiBearerAuth()
@Controller('ledgers')
@UseGuards(JwtAuthGuard)
export class LedgerController {
  constructor(private readonly ledgerService: LedgerService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new ledger' })
  @ApiResponse({ status: 201, description: 'The ledger has been successfully created.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  create(@Body() createLedgerDto: CreateLedgerDto, @GetUser() account: Account) {
    return this.ledgerService.create(createLedgerDto, account);
  }

  @Get()
  @ApiOperation({ summary: 'Get all ledgers with pagination' })
  @ApiQuery({ name: 'skip', required: false, description: 'Number of records to skip' })
  @ApiQuery({ name: 'take', required: false, description: 'Number of records to take' })
  @ApiQuery({ name: 'associatedTable', required: false, description: 'Filter by associated table' })
  @ApiQuery({ name: 'associatedId', required: false, description: 'Filter by associated ID' })
  @ApiResponse({ status: 200, description: 'Returns paginated list of ledgers' })
  findAll(
    @Query('skip') skip = 0,
    @Query('take') take = 10,
    @Query('associatedTable') associatedTable?: string,
    @Query('associatedId') associatedId?: string,
  ) {
    return this.ledgerService.findAll({
      skip: +skip,
      take: +take,
      associatedTable,
      associatedId,
    });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get ledger by ID' })
  @ApiParam({ name: 'id', description: 'Ledger ID' })
  @ApiResponse({ status: 200, description: 'Returns the ledger' })
  @ApiResponse({ status: 404, description: 'Ledger not found' })
  findOne(@Param('id') id: string) {
    return this.ledgerService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update ledger details' })
  @ApiParam({ name: 'id', description: 'Ledger ID' })
  @ApiResponse({ status: 200, description: 'The ledger has been successfully updated.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 404, description: 'Ledger not found.' })
  update(
    @Param('id') id: string,
    @Body() updateLedgerDto: UpdateLedgerDto,
    @GetUser() account: Account,
  ) {
    return this.ledgerService.update(id, updateLedgerDto, account);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete ledger' })
  @ApiParam({ name: 'id', description: 'Ledger ID' })
  @ApiResponse({ status: 200, description: 'The ledger has been successfully deleted.' })
  @ApiResponse({ status: 404, description: 'Ledger not found.' })
  remove(@Param('id') id: string, @GetUser() account: Account) {
    return this.ledgerService.remove(id, account);
  }
}