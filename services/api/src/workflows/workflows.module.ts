import { Module } from '@nestjs/common';
import { CargoWorkflowService } from './cargo-workflow.service';
import { OperationsModule } from '../operations/operations.module';
import { SystemModule } from '../system/system.module';
import { FinancesModule } from '../finances/finances.module';
import { WebsocketModule } from '../websocket/websocket.module';

@Module({
  imports: [
    OperationsModule,
    SystemModule,
    FinancesModule,
    WebsocketModule,
  ],
  providers: [
    CargoWorkflowService,
  ],
  exports: [
    CargoWorkflowService,
  ],
})
export class WorkflowsModule {}