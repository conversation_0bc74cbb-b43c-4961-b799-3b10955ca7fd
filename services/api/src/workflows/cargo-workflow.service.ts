import { Injectable } from '@nestjs/common';
import { Status } from '../common/enums/status.enum';
import { CargoService } from '../operations/services/cargo.service';
import { BatchService } from '../operations/services/batch.service';
import { ShipmentService } from '../operations/services/shipment.service';
import { DocumentService } from '../system/services/document.service';
import { NotificationService } from '../system/services/notification.service';
import { ApprovalService } from '../system/services/approval.service';
import { WebsocketGateway } from '../websocket/websocket.gateway';
import { Account } from '../system/entities/account.entity';
import { LedgerService } from '../finances/services/ledger.service';
import { TransactionService } from '../finances/services/transaction.service';

@Injectable()
export class CargoWorkflowService {
  constructor(
    private cargoService: CargoService,
    private batchService: BatchService,
    private shipmentService: ShipmentService,
    private documentService: DocumentService,
    private notificationService: NotificationService,
    private approvalService: ApprovalService,
    private ledgerService: LedgerService,
    private transactionService: TransactionService,
    private websocketGateway: WebsocketGateway,
  ) {}

  // Register Cargo
  async registerCargo(cargoData: any, account: Account) {
    // Create the cargo record
    const cargo = await this.cargoService.create(cargoData, account);
    
    // Create cargo ledger for financial tracking
    const ledger = await this.ledgerService.create({
      name: `Cargo ${cargo.trackingNumber} Ledger`,
      status: Status.ACTIVE,
      tags: ['cargo', 'invoice'],
      associatedTable: 'cargos',
      associatedId: cargo.id,
    }, account);
    
    // Create invoice transaction
    await this.transactionService.create({
      name: `Invoice for Cargo ${cargo.trackingNumber}`,
      status: Status.PENDING,
      value: cargo.totalPrice,
      amount: cargo.totalPrice,
      tags: ['invoice', 'pending'],
      context: 'Cargo registration invoice',
      ledgerId: ledger.id,
    }, account);
    
    // Create invoice document
    await this.documentService.create({
      name: `Invoice for Cargo ${cargo.trackingNumber}`,
      path: `/invoices/${cargo.id}.pdf`,
      category: 'invoice',
      description: 'Cargo registration invoice',
      associatedTable: 'cargos',
      associatedId: cargo.id,
      details: {
        cargoId: cargo.id,
        customerId: cargo.customer.id,
        amount: cargo.totalPrice,
        date: new Date(),
      },
    }, account);
    
    // Notify customer
    await this.notificationService.create({
      name: 'Cargo Registered',
      message: `Your cargo ${cargo.trackingNumber} has been registered. Please complete payment.`,
      associatedTable: 'cargos',
      associatedId: cargo.id,
    }, account);
    
    // Emit websocket event
    this.websocketGateway.emitToCargo(cargo.id, 'cargo_registered', {
      id: cargo.id,
      trackingNumber: cargo.trackingNumber,
      status: cargo.status,
    });
    
    return cargo;
  }
  
  // Assign cargo to batch
  async assignCargoToBatch(cargoId: string, batchId: string, account: Account) {
    // Update cargo with batch assignment
    const cargo = await this.cargoService.update(cargoId, { batchId }, account);
    
    // Notify customer
    await this.notificationService.create({
      name: 'Cargo Assigned to Batch',
      message: `Your cargo ${cargo.trackingNumber} has been assigned to a shipping batch.`,
      associatedTable: 'cargos',
      associatedId: cargo.id,
    }, account);
    
    // Emit websocket events
    this.websocketGateway.emitToCargo(cargo.id, 'cargo_assigned_to_batch', {
      id: cargo.id,
      trackingNumber: cargo.trackingNumber,
      batchId,
    });
    
    this.websocketGateway.emitToBatch(batchId, 'cargo_added_to_batch', {
      cargoId: cargo.id,
      trackingNumber: cargo.trackingNumber,
    });
    
    return cargo;
  }
  
  // Update cargo status (tracking)
  async updateCargoStatus(cargoId: string, status: Status, account: Account) {
    // Update cargo status
    const cargo = await this.cargoService.update(cargoId, { status }, account);
    
    // If status is "delivered", create shipment record
    if (status === Status.DELIVERED) {
      await this.shipmentService.createShipmentForCargo(cargo, account);
    }
    
    // If status is "released", handle cargo release workflow
    if (status === Status.RELEASED) {
      await this.processCargReleaseWorkflow(cargoId, account);
    }
    
    // Notify customer of status change
    await this.notificationService.create({
      name: 'Cargo Status Updated',
      message: `Your cargo ${cargo.trackingNumber} status has been updated to ${status}.`,
      associatedTable: 'cargos',
      associatedId: cargo.id,
    }, account);
    
    // Emit websocket event
    this.websocketGateway.emitToCargo(cargo.id, 'cargo_status_updated', {
      id: cargo.id,
      trackingNumber: cargo.trackingNumber,
      status: cargo.status,
    });
    
    return cargo;
  }
  
  // Process payment for cargo
  async processPayment(cargoId: string, paymentData: any, account: Account) {
    // Get cargo and associated ledger
    const cargo = await this.cargoService.findOne(cargoId);
    
    const ledgers = await this.ledgerService.findAll({
      skip: 0,
      take: 1,
      associatedTable: 'cargos',
      associatedId: cargoId,
    });
    
    if (ledgers.total === 0) {
      throw new Error('Ledger not found for cargo');
    }
    
    const ledger = ledgers.ledgers[0];
    
    // Update transaction status to completed
    const transactions = await this.transactionService.findAll({
      skip: 0,
      take: 1,
      ledgerId: ledger.id,
      status: Status.PENDING,
    });
    
    if (transactions.total === 0) {
      throw new Error('Pending transaction not found for cargo');
    }
    
    const transaction = transactions.transactions[0];
    
    await this.transactionService.update(transaction.id, {
      status: Status.COMPLETED,
      context: `Payment processed: ${paymentData.method}`,
    }, account);
    
    // Create payment receipt document
    await this.documentService.create({
      name: `Payment Receipt for Cargo ${cargo.trackingNumber}`,
      path: `/receipts/${cargo.id}.pdf`,
      category: 'receipt',
      description: 'Payment receipt for cargo',
      associatedTable: 'cargos',
      associatedId: cargo.id,
      details: {
        cargoId: cargo.id,
        customerId: cargo.customer.id,
        amount: cargo.totalPrice,
        paymentMethod: paymentData.method,
        paymentReference: paymentData.reference,
        date: new Date(),
      },
    }, account);
    
    // Create approval record for cargo release
    await this.approvalService.create({
      details: {
        cargoId: cargo.id,
        paymentId: transaction.id,
        approvalType: 'payment',
      },
      status: Status.APPROVED,
      associatedTable: 'cargos',
      associatedId: cargo.id,
    }, account);
    
    // Notify customer
    await this.notificationService.create({
      name: 'Payment Received',
      message: `Payment for cargo ${cargo.trackingNumber} has been received and processed.`,
      associatedTable: 'cargos',
      associatedId: cargo.id,
    }, account);
    
    // Emit websocket event
    this.websocketGateway.emitToCargo(cargo.id, 'cargo_payment_processed', {
      id: cargo.id,
      trackingNumber: cargo.trackingNumber,
      paymentStatus: 'completed',
    });
    
    return { cargo, transaction };
  }
  
  // Process cargo release workflow
  async processCargReleaseWorkflow(cargoId: string, account: Account) {
    // Check if payment is approved
    const approvals = await this.approvalService.findAll({
      skip: 0, 
      take: 1,
      associatedTable: 'cargos',
      associatedId: cargoId,
      status: Status.APPROVED,
    });
    
    if (approvals.total === 0) {
      throw new Error('No approval found for cargo release');
    }
    
    // Update cargo status to released
    const cargo = await this.cargoService.update(cargoId, { status: Status.RELEASED }, account);
    
    // Create release document
    await this.documentService.create({
      name: `Release Form for Cargo ${cargo.trackingNumber}`,
      path: `/releases/${cargo.id}.pdf`,
      category: 'release',
      description: 'Cargo release authorization form',
      associatedTable: 'cargos',
      associatedId: cargo.id,
      details: {
        cargoId: cargo.id,
        customerId: cargo.customer.id,
        releaseDate: new Date(),
        releaseAgent: account.user.name,
      },
    }, account);
    
    // Notify field agent
    await this.notificationService.create({
      name: 'Cargo Ready for Release',
      message: `Cargo ${cargo.trackingNumber} is approved for release.`,
      associatedTable: 'cargos',
      associatedId: cargo.id,
    }, account);
    
    // Notify customer
    await this.notificationService.create({
      name: 'Cargo Ready for Pickup',
      message: `Your cargo ${cargo.trackingNumber} is ready for pickup.`,
      associatedTable: 'cargos',
      associatedId: cargo.id,
    }, account);
    
    // Emit websocket event
    this.websocketGateway.emitToCargo(cargo.id, 'cargo_release_authorized', {
      id: cargo.id,
      trackingNumber: cargo.trackingNumber,
      status: cargo.status,
    });
    
    return cargo;
  }
}