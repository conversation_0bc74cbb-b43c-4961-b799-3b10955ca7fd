import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SystemModule } from './system/system.module';
import { OperationsModule } from './operations/operations.module';
import { FinancesModule } from './finances/finances.module';
import { AuthModule } from './auth/auth.module';
import { WebsocketModule } from './websocket/websocket.module';
import { ReportsModule } from './reports/reports.module';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({ isGlobal: true }),

    // Database
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        // Get database configuration
        const isProduction = configService.get('NODE_ENV') === 'production';

        return {
          type: 'postgres',
          url: configService.get('DB_URL'),
          entities: [__dirname + '/**/*.entity{.ts,.js}'],
          // synchronize: false, // configService.get('NODE_ENV') !== 'production',
          logging: configService.get('NODE_ENV') !== 'production',

          // Optimized connection pooling for Kubernetes
          extra: {
            // Maximum number of clients in the pool
            max: configService.get<number>('DB_POOL_MAX', 20),

            // Minimum number of clients in the pool
            min: configService.get<number>('DB_POOL_MIN', 5),

            // Close idle clients after 30 seconds
            idleTimeoutMillis: 30000,

            // Return errors from query directly to the client
            connectionTimeoutMillis: 3000,

            // Maximum time a client can stay checked out
            maxUses: 7500,
          },

          // Retry connection logic
          retryAttempts: configService.get<number>('DB_RETRY_ATTEMPTS', 10),
          retryDelay: configService.get<number>('DB_RETRY_DELAY', 3000),

          // Keep connections alive
          keepConnectionAlive: true,

          // Log connection issues in production
          verboseRetryLog: isProduction,
        };
      },
    }),

    // Application modules
    AuthModule,
    SystemModule,
    OperationsModule,
    FinancesModule,
    WebsocketModule,
    ReportsModule,
  ],
})
export class AppModule {}
