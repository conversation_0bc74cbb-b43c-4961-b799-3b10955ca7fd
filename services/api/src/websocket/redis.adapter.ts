import { IoAdapter } from '@nestjs/platform-socket.io';
import { ServerOptions } from 'socket.io';
import { createAdapter } from '@socket.io/redis-adapter';
import { Redis } from 'ioredis';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';

export class RedisIoAdapter extends IoAdapter {
  private readonly logger = new Logger('RedisIoAdapter');
  private redisPublisher: Redis;
  private redisSubscriber: Redis;

  constructor(
    private app,
    private configService: ConfigService,
  ) {
    super(app);
    this.initializeRedisConnections();
  }

  private initializeRedisConnections() {
    // Connection options with built-in retry mechanisms for Kubernetes environments
    const redisOptions = {
      host: this.configService.get<string>('REDIS_HOST', 'localhost'),
      port: this.configService.get<number>('REDIS_PORT', 6379),
      password: this.configService.get<string>('REDIS_PASSWORD', ''),
      username: this.configService.get<string>('REDIS_USERNAME', ''),
      db: this.configService.get<number>('REDIS_DB', 0),
      lazyConnect: true, // Connect when needed
      retryStrategy: (times) => {
        // Retry connection with exponential backoff
        const delay = Math.min(Math.pow(2, times) * 50, 5000);
        this.logger.log(`Redis connection retry in ${delay}ms (attempt ${times})`);
        return delay;
      },
      enableReadyCheck: true,
      maxRetriesPerRequest: null, // Retry forever
      reconnectOnError: (err) => {
        this.logger.error(`Redis connection error: ${err.message}`);
        return true; // Always try to reconnect
      },
      autoResubscribe: true, // Automatically resubscribe when reconnecting
    };

    this.redisPublisher = new Redis(redisOptions);
    this.redisSubscriber = new Redis(redisOptions);

    // Handle connection events for better observability
    this.redisPublisher.on('connect', () => this.logger.log('Redis publisher connected'));
    this.redisSubscriber.on('connect', () => this.logger.log('Redis subscriber connected'));
    
    this.redisPublisher.on('error', (err) => this.logger.error(`Redis publisher error: ${err.message}`));
    this.redisSubscriber.on('error', (err) => this.logger.error(`Redis subscriber error: ${err.message}`));
    
    this.redisPublisher.on('reconnecting', () => this.logger.warn('Redis publisher reconnecting...'));
    this.redisSubscriber.on('reconnecting', () => this.logger.warn('Redis subscriber reconnecting...'));
  }

  createIOServer(port: number, options?: ServerOptions): any {
    const server = super.createIOServer(port, options);
    
    try {
      // Create and use Redis adapter
      const redisAdapter = createAdapter(this.redisPublisher, this.redisSubscriber);
      server.adapter(redisAdapter);
      this.logger.log('Socket.IO server configured with Redis adapter');
      
      return server;
    } catch (error) {
      this.logger.error(`Failed to create Redis adapter: ${error.message}`);
      
      // Fallback to in-memory adapter for development
      if (this.configService.get<string>('NODE_ENV') === 'development') {
        this.logger.warn('Falling back to in-memory adapter (development only)');
        return server;
      }
      
      // In production, rethrow the error
      throw error;
    }
  }

  // Cleanup Redis connections on application shutdown
  async close() {
    this.logger.log('Closing Redis connections...');
    await this.redisPublisher.quit();
    await this.redisSubscriber.quit();
    this.logger.log('Redis connections closed');
  }
}