import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  ConnectedSocket,
  MessageBody,
  OnGatewayInit,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Redis } from 'ioredis';
import { Emitter } from '@socket.io/redis-emitter';

@WebSocketGateway({
  cors: {
    origin: '*',
  },
})
@Injectable()
export class WebsocketGateway implements OnGatewayConnection, OnGatewayDisconnect, OnGatewayInit {
  @WebSocketServer()
  server: Server;

  private logger = new Logger('WebsocketGateway');
  private userSockets = new Map<string, string[]>();
  private socketUsers = new Map<string, string>();
  private redisClient: Redis;
  private redisEmitter: Emitter;
  private isKubernetesEnv: boolean;

  constructor(private configService: ConfigService) {
    // Check if we're in a distributed environment
    this.isKubernetesEnv = this.configService.get<string>('KUBERNETES_ENABLED') === 'true' ||
      this.configService.get<string>('REDIS_ENABLED') === 'true';
    
    // Initialize Redis client if in distributed environment
    if (this.isKubernetesEnv) {
      this.initRedisClient();
    }
  }

  private initRedisClient() {
    try {
      this.redisClient = new Redis({
        host: this.configService.get<string>('REDIS_HOST', 'localhost'),
        port: this.configService.get<number>('REDIS_PORT', 6379),
        password: this.configService.get<string>('REDIS_PASSWORD', ''),
        username: this.configService.get<string>('REDIS_USERNAME', ''),
        keyPrefix: 'shamwaa:socket:',
        retryStrategy: (times) => {
          const delay = Math.min(times * 100, 3000);
          return delay;
        },
      });

      // Initialize Redis emitter to broadcast across all Socket.IO servers
      this.redisEmitter = new Emitter(this.redisClient);
      
      this.redisClient.on('connect', () => {
        this.logger.log('Redis client connected for WebSocket communication');
      });
      
      this.redisClient.on('error', (err) => {
        this.logger.error(`Redis client error: ${err.message}`);
      });
    } catch (error) {
      this.logger.error(`Failed to initialize Redis client: ${error.message}`);
      this.isKubernetesEnv = false;
    }
  }

  afterInit(server: Server) {
    this.logger.log('WebSocket server initialized');
  }

  handleConnection(client: Socket) {
    this.logger.log(`Client connected: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);
    
    // Remove from maps
    const userId = this.socketUsers.get(client.id);
    if (userId) {
      this.socketUsers.delete(client.id);
      
      const userSocketIds = this.userSockets.get(userId) || [];
      const updatedUserSocketIds = userSocketIds.filter(id => id !== client.id);
      
      if (updatedUserSocketIds.length > 0) {
        this.userSockets.set(userId, updatedUserSocketIds);
      } else {
        this.userSockets.delete(userId);
      }
    }
  }

  @SubscribeMessage('authenticate')
  handleAuthenticate(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { userId: string },
  ) {
    const { userId } = data;
    this.logger.log(`User ${userId} authenticated on socket ${client.id}`);
    
    // Associate the socket with the user
    this.socketUsers.set(client.id, userId);
    
    // Add the socket to the user's list of sockets
    const userSocketIds = this.userSockets.get(userId) || [];
    userSocketIds.push(client.id);
    this.userSockets.set(userId, userSocketIds);

    // Store in Redis if in Kubernetes env
    if (this.isKubernetesEnv && this.redisClient) {
      this.redisClient.sadd(`user:${userId}:sockets`, client.id);
      this.redisClient.set(`socket:${client.id}:user`, userId);
    }
    
    return { status: 'authenticated' };
  }

  @SubscribeMessage('subscribe_cargo')
  handleSubscribeCargo(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { cargoId: string },
  ) {
    const { cargoId } = data;
    this.logger.log(`Socket ${client.id} subscribed to cargo ${cargoId}`);
    client.join(`cargo:${cargoId}`);
    return { status: 'subscribed', topic: `cargo:${cargoId}` };
  }

  @SubscribeMessage('subscribe_batch')
  handleSubscribeBatch(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { batchId: string },
  ) {
    const { batchId } = data;
    this.logger.log(`Socket ${client.id} subscribed to batch ${batchId}`);
    client.join(`batch:${batchId}`);
    return { status: 'subscribed', topic: `batch:${batchId}` };
  }

  @SubscribeMessage('subscribe_shipment')
  handleSubscribeShipment(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { shipmentId: string },
  ) {
    const { shipmentId } = data;
    this.logger.log(`Socket ${client.id} subscribed to shipment ${shipmentId}`);
    client.join(`shipment:${shipmentId}`);
    return { status: 'subscribed', topic: `shipment:${shipmentId}` };
  }

  emitToUser(userId: string, event: string, data: any) {
    // If using Redis emitter
    if (this.isKubernetesEnv && this.redisEmitter) {
      try {
        this.redisClient.smembers(`user:${userId}:sockets`, (err, socketIds) => {
          if (err) {
            this.logger.error(`Error getting sockets for user ${userId}: ${err.message}`);
            return;
          }
          
          if (socketIds && socketIds.length > 0) {
            socketIds.forEach(socketId => {
              this.redisEmitter.to(socketId).emit(event, data);
            });
            this.logger.log(`Emitted ${event} to user ${userId} via Redis emitter`);
          }
        });
        return;
      } catch (error) {
        this.logger.error(`Redis emission error: ${error.message}`);
      }
    }
    
    // Fallback to in-memory socket tracking
    const socketIds = this.userSockets.get(userId);
    
    if (socketIds && socketIds.length > 0) {
      socketIds.forEach(socketId => {
        this.server.to(socketId).emit(event, data);
      });
      this.logger.log(`Emitted ${event} to user ${userId}`);
    }
  }

  emitToCargo(cargoId: string, event: string, data: any) {
    if (this.isKubernetesEnv && this.redisEmitter) {
      try {
        this.redisEmitter.to(`cargo:${cargoId}`).emit(event, data);
        this.logger.log(`Emitted ${event} to cargo ${cargoId} via Redis emitter`);
        return;
      } catch (error) {
        this.logger.error(`Redis emission error: ${error.message}`);
      }
    }
    
    this.server.to(`cargo:${cargoId}`).emit(event, data);
    this.logger.log(`Emitted ${event} to cargo ${cargoId}`);
  }

  emitToBatch(batchId: string, event: string, data: any) {
    if (this.isKubernetesEnv && this.redisEmitter) {
      try {
        this.redisEmitter.to(`batch:${batchId}`).emit(event, data);
        this.logger.log(`Emitted ${event} to batch ${batchId} via Redis emitter`);
        return;
      } catch (error) {
        this.logger.error(`Redis emission error: ${error.message}`);
      }
    }
    
    this.server.to(`batch:${batchId}`).emit(event, data);
    this.logger.log(`Emitted ${event} to batch ${batchId}`);
  }

  emitToShipment(shipmentId: string, event: string, data: any) {
    if (this.isKubernetesEnv && this.redisEmitter) {
      try {
        this.redisEmitter.to(`shipment:${shipmentId}`).emit(event, data);
        this.logger.log(`Emitted ${event} to shipment ${shipmentId} via Redis emitter`);
        return;
      } catch (error) {
        this.logger.error(`Redis emission error: ${error.message}`);
      }
    }
    
    this.server.to(`shipment:${shipmentId}`).emit(event, data);
    this.logger.log(`Emitted ${event} to shipment ${shipmentId}`);
  }

  emitToAll(event: string, data: any) {
    if (this.isKubernetesEnv && this.redisEmitter) {
      try {
        this.redisEmitter.emit(event, data);
        this.logger.log(`Emitted ${event} to all connected clients via Redis emitter`);
        return;
      } catch (error) {
        this.logger.error(`Redis emission error: ${error.message}`);
      }
    }
    
    this.server.emit(event, data);
    this.logger.log(`Emitted ${event} to all connected clients`);
  }

  // For cleanup on application shutdown
  async onModuleDestroy() {
    if (this.redisClient) {
      this.logger.log('Closing Redis client for WebSocket gateway...');
      await this.redisClient.quit();
    }
  }
}