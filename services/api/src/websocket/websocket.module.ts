import { Module, Global, OnApplicationShutdown } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { WebsocketGateway } from './websocket.gateway';
import { RedisConfig } from '../config/redis.config';

@Global()
@Module({
  imports: [ConfigModule],
  providers: [WebsocketGateway, RedisConfig],
  exports: [WebsocketGateway],
})
export class WebsocketModule implements OnApplicationShutdown {
  constructor(private readonly websocketGateway: WebsocketGateway) {}

  async onApplicationShutdown(signal?: string) {
    // Clean up resources when app is shutting down
    if (signal) {
      console.log(`Application shutdown triggered by ${signal}`);
    }
    
    // Call onModuleDestroy on the websocket gateway to close Redis connections
    await this.websocketGateway.onModuleDestroy();
  }
}