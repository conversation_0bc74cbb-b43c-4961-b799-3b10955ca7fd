import { 
  Controller, 
  Post, 
  Body, 
  UseGuards, 
  Get, 
  Param,
  BadRequestException
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiBody, 
  ApiBearerAuth 
} from '@nestjs/swagger';
import { SupabaseAuthGuard } from '../guards/supabase-auth.guard';
import { GetUser } from '../decorators/get-user.decorator';
import { Account } from '../../system/entities/account.entity';
import * as crypto from 'crypto';

@ApiTags('Biometrics')
@ApiBearerAuth()
@Controller('biometrics')
@UseGuards(SupabaseAuthGuard)
export class BiometricController {
  // In a real production app, biometric templates would be stored securely,
  // and validation would be done against actual biometric hardware/SDK
  private readonly tempBiometricDatabase = new Map<string, any>();

  @Post('register')
  @ApiOperation({ summary: 'Register biometric data for a user' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        biometricType: { 
          type: 'string', 
          enum: ['fingerprint', 'facial', 'retinal', 'other'],
          description: 'Type of biometric data' 
        },
        biometricTemplate: { 
          type: 'string', 
          description: 'Encoded biometric template data (in real implementation, this would come from a secure biometric capture device)' 
        },
      },
      required: ['biometricType', 'biometricTemplate'],
    },
  })
  @ApiResponse({ status: 201, description: 'Biometric data successfully registered' })
  @ApiResponse({ status: 400, description: 'Invalid biometric data' })
  registerBiometric(
    @Body() body: { biometricType: string; biometricTemplate: string },
    @GetUser() account: Account,
  ) {
    // Validate input
    if (!body.biometricType || !body.biometricTemplate || body.biometricTemplate.length < 20) {
      throw new BadRequestException('Invalid biometric data');
    }

    // In a real system, the template would be verified for quality
    // and securely stored, possibly encrypted
    
    // For this demo, we'll store it in memory with a user ID reference
    const templatedId = crypto.randomUUID();
    this.tempBiometricDatabase.set(account.id, {
      id: templatedId,
      type: body.biometricType,
      template: body.biometricTemplate,
      createdAt: new Date(),
      userId: account.id,
    });

    return {
      success: true,
      message: 'Biometric data registered successfully',
      templateId: templatedId,
    };
  }

  @Get('status')
  @ApiOperation({ summary: 'Check if user has registered biometrics' })
  @ApiResponse({ status: 200, description: 'Returns biometric registration status' })
  checkBiometricStatus(@GetUser() account: Account) {
    const hasRegistration = this.tempBiometricDatabase.has(account.id);
    
    return {
      registered: hasRegistration,
      biometricTypes: hasRegistration ? [this.tempBiometricDatabase.get(account.id).type] : [],
    };
  }

  @Post('generate-auth-token')
  @ApiOperation({ summary: 'Generate a biometric authentication token' })
  @ApiResponse({ status: 200, description: 'Returns a biometric authentication token' })
  generateAuthToken(@GetUser() account: Account) {
    // In a real implementation, this would generate a time-limited, 
    // securely signed token for biometric authentication
    
    // 1. Create a base64 encoded user identifier
    const encodedId = Buffer.from(account.id).toString('base64');
    
    // 2. Add a timestamp (unix seconds)
    const timestamp = Math.floor(Date.now() / 1000);
    
    // 3. Add a signature (in real implementation would be signed with a private key)
    const signature = crypto
      .createHash('sha256')
      .update(`${account.id}:${timestamp}:biometric_auth_secret`)
      .digest('hex');
    
    // 4. Combine the parts
    const authToken = `${encodedId}.${timestamp}.${signature.substring(0, 16)}`;
    
    return {
      token: authToken,
      expiresIn: 300, // 5 minutes
    };
  }

  @Post('verify')
  @ApiOperation({ summary: 'Verify biometric data' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        biometricToken: { 
          type: 'string', 
          description: 'Biometric authentication token' 
        },
        biometricSample: { 
          type: 'string', 
          description: 'Encoded biometric sample data' 
        },
      },
      required: ['biometricToken', 'biometricSample'],
    },
  })
  @ApiResponse({ status: 200, description: 'Returns verification result' })
  @ApiResponse({ status: 400, description: 'Invalid biometric data or token' })
  verifyBiometric(
    @Body() body: { biometricToken: string; biometricSample: string },
    @GetUser() account: Account,
  ) {
    // Validate token format
    const tokenParts = body.biometricToken.split('.');
    if (tokenParts.length !== 3) {
      throw new BadRequestException('Invalid token format');
    }
    
    // Extract token parts
    const [encodedId, timestamp, signature] = tokenParts;
    
    // Verify token is not expired
    const tokenTimestamp = parseInt(timestamp, 10);
    const currentTime = Math.floor(Date.now() / 1000);
    
    if (currentTime - tokenTimestamp > 300) { // 5 minutes
      throw new BadRequestException('Token expired');
    }
    
    // In a real implementation, verify the signature is valid
    
    // Check if user has registered biometrics
    if (!this.tempBiometricDatabase.has(account.id)) {
      throw new BadRequestException('No biometric template found for user');
    }
    
    // In a real implementation, the biometric sample would be matched against
    // the stored template using biometric matching algorithms
    
    // For this demo, we'll simulate a successful match
    // In reality, this would return a confidence score or match/no-match result
    return {
      verified: true,
      confidence: 0.95, // Simulated confidence score
    };
  }
}