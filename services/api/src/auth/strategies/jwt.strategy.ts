import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { InjectRepository } from '@nestjs/typeorm';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { Account } from '../../system/entities/account.entity';
import { Status } from '../../common/enums/status.enum';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    @InjectRepository(Account)
    private accountRepository: Repository<Account>,
    private configService: ConfigService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: configService.get<string>('JWT_SECRET', 'supersecret'),
    });
  }

  async validate(payload: any) {
    const { sub: id } = payload;
    
    const account = await this.accountRepository.findOne({
      where: { id },
      relations: ['user', 'role'],
    });

    if (!account || account.status !== Status.ACTIVE) {
      throw new UnauthorizedException('Invalid credentials or inactive account');
    }

    // Exclude password from returned user object
    const { password, ...result } = account;
    return result;
  }
}