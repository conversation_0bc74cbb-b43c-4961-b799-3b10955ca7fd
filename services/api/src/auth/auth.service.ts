import { Injectable, UnauthorizedException, ConflictException, InternalServerErrorException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

import { Account } from '../system/entities/account.entity';
import { User } from '../system/entities/user.entity';
import { Session } from '../system/entities/session.entity';
import { Recovery } from '../system/entities/recovery.entity';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { Status } from '../common/enums/status.enum';
import { SupabaseService } from './supabase/supabase.service';
import { SessionService } from './session/session.service'; // Import the new session service

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(Account)
    private accountRepository: Repository<Account>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Session)
    private sessionRepository: Repository<Session>,
    @InjectRepository(Recovery)
    private recoveryRepository: Repository<Recovery>,
    private supabaseService: SupabaseService,
    private sessionService: SessionService, // Inject the session service
  ) {}

  async login(loginDto: LoginDto): Promise<any> {
    const { email, password } = loginDto;

    // Sign in with Supabase
    const { data: authData, error: authError } = await this.supabaseService.signIn(email, password);
    
    if (authError) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Find or create account by email
    let account = await this.accountRepository.findOne({
      where: { email },
      relations: ['user', 'role'],
    });

    if (!account) {
      throw new UnauthorizedException('Account not found');
    }

    // Check if account is active
    if (account.status !== Status.ACTIVE) {
      throw new UnauthorizedException('Account is not active');
    }

    // Create session in both the database and the Redis/memory session store
    await this.createSession(account, authData.session.refresh_token);
    
    // Also store in the distributed session store
    await this.sessionService.createSession(
      authData.session.refresh_token, 
      account.id, 
      account.email, 
      {
        userId: account.user.id,
        name: account.user.name,
        role: account.role ? account.role.name : null,
        sessionStartedAt: new Date(),
      }
    );

    return {
      accessToken: authData.session.access_token,
      refreshToken: authData.session.refresh_token,
      user: {
        id: account.user.id,
        name: account.user.name,
        email: account.email,
        role: account.role ? account.role.name : null,
      },
    };
  }

  async register(registerDto: RegisterDto): Promise<any> {
    const { name, email, password, phone, location } = registerDto;

    // Check if account with email already exists
    const existingAccount = await this.accountRepository.findOne({
      where: { email },
    });

    if (existingAccount) {
      throw new ConflictException('Email already exists');
    }

    try {
      // Register with Supabase using admin API
      const { data: userData, error: authError } = await this.supabaseService.signUp(email, password);
      
      if (authError) {
        throw new InternalServerErrorException(authError.message);
      }

      // Create new user
      const user = new User();
      user.name = name;
      user.phone = phone;
      user.location = location ?? '';
      user.status = Status.ACTIVE;
      
      const savedUser = await this.userRepository.save(user);

      // Hash password for local storage
      const hashedPassword = await bcrypt.hash(password, 10);

      // Create new account
      const account = new Account();
      account.email = email;
      account.password = hashedPassword;
      account.status = Status.ACTIVE;
      account.user = savedUser;

      const savedAccount = await this.accountRepository.save(account);

      // Since we're using admin createUser API, we don't get a session back
      // We need to sign in to get a session
      const { data: authData, error: signInError } = await this.supabaseService.signIn(email, password);
      
      if (signInError) {
        // User created but couldn't sign in
        return {
          user: {
            id: savedUser.id,
            name: savedUser.name,
            email: savedAccount.email,
          },
          message: 'User created successfully but could not automatically sign in',
        };
      }

      // Create session with the token from sign in
      const refreshToken = authData.session.refresh_token;
      await this.createSession(savedAccount, refreshToken);
      
      // Store in the distributed session store
      await this.sessionService.createSession(
        refreshToken,
        savedAccount.id,
        savedAccount.email,
        {
          userId: savedUser.id,
          name: savedUser.name,
          sessionStartedAt: new Date(),
        }
      );

      return {
        accessToken: authData.session.access_token,
        refreshToken: authData.session.refresh_token,
        user: {
          id: savedUser.id,
          name: savedUser.name,
          email: savedAccount.email,
        },
      };
    } catch (error) {
      throw new InternalServerErrorException('Failed to register user: ' + error.message);
    }
  }

  async refreshToken(refreshToken: string): Promise<any> {
    // Refresh token with Supabase
    const { data: authData, error: authError } = await this.supabaseService.refreshToken(refreshToken);
    
    if (authError) {
      throw new UnauthorizedException('Invalid refresh token');
    }

    // Find session by refresh token
    const session = await this.sessionRepository.findOne({
      where: { refreshToken },
      relations: ['account', 'account.user', 'account.role'],
    });

    if (!session || session.status !== Status.ACTIVE || new Date() > session.expiry) {
      throw new UnauthorizedException('Invalid or expired session');
    }

    // Also check the Redis/memory session store
    const distributedSession = await this.sessionService.getSession(refreshToken);
    if (!distributedSession) {
      // If not found in distributed store, create it now
      await this.sessionService.createSession(
        refreshToken,
        session.account.id,
        session.account.email,
        {
          userId: session.account.user.id,
          name: session.account.user.name,
          role: session.account.role ? session.account.role.name : null,
          sessionStartedAt: new Date(),
        }
      );
    }
    // Update session
    if (!authData.session) {
      throw new InternalServerErrorException('Invalid auth data received');
    }
    session.token = authData.session.access_token;
    session.refreshToken = authData.session.refresh_token;
    session.expiry = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days
    await this.sessionRepository.save(session);
    // Update the distributed session store with new token
    await this.sessionService.updateSession(refreshToken, {
      newRefreshToken: authData.session.refresh_token,
      refreshedAt: new Date(),
    });

    return {
      accessToken: authData.session.access_token,
      refreshToken: authData.session.refresh_token,
      user: {
        id: session.account.user.id,
        name: session.account.user.name,
        email: session.account.email,
        role: session.account.role ? session.account.role.name : null,
      },
    };
  }

  async getProfile(account: Account): Promise<any> {
    const user = await this.userRepository.findOne({
      where: { id: account.user.id },
    });

    return {
      id: user?.id,
      name: user?.name,
      email: account.email,
      phone: user?.phone,
      location: user?.location,
      avatar: user?.avatar,
      role: account.role ? account.role.name : null,
    };
  }

  async resetPassword(email: string): Promise<void> {
    const account = await this.accountRepository.findOne({
      where: { email },
    });

    if (!account) {
      // Don't reveal that the email doesn't exist
      return;
    }

    // Request password reset through Supabase
    await this.supabaseService.resetPassword(email);

    // Create recovery record
    const token = uuidv4();
    const recovery = new Recovery();
    recovery.account = account;
    recovery.token = token;
    recovery.expiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    await this.recoveryRepository.save(recovery);
  }

  private async createSession(account: Account, refreshToken: string): Promise<Session> {
    // Invalidate all existing sessions for this account
    await this.sessionRepository.update(
      { account: { id: account.id }, status: Status.ACTIVE },
      { status: Status.INACTIVE },
    );

    // Create new session
    const session = new Session();
    session.account = account;
    session.token = refreshToken; // Use the refresh token as a reference
    session.refreshToken = refreshToken;
    session.expiry = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days
    session.status = Status.ACTIVE;

    return this.sessionRepository.save(session);
  }
}