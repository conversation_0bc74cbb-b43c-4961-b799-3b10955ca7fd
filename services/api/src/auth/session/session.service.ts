import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Redis } from 'ioredis';
import { RedisConfig } from '../../config/redis.config';

interface Session {
  id: string;
  userId: string;
  email: string;
  data: any;
  expiresAt: number;
}

@Injectable()
export class SessionService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger('SessionService');
  private redisClient: Redis;
  private readonly useRedis: boolean;
  private readonly sessionTTL: number = 7 * 24 * 60 * 60; // 7 days in seconds
  
  // Fallback in-memory storage for non-Redis environments
  private readonly inMemorySessions = new Map<string, Session>();

  constructor(
    private configService: ConfigService,
    private redisConfig: RedisConfig,
  ) {
    this.useRedis = configService.get<string>('REDIS_ENABLED') === 'true';
  }

  async onModuleInit() {
    if (this.useRedis) {
      this.logger.log('Initializing Redis session store');
      try {
        this.redisClient = this.redisConfig.createRedisClient();
        // Override prefix for sessions
        this.redisClient.options.keyPrefix = 'shamwaa:session:';
        
        this.redisClient.on('connect', () => {
          this.logger.log('Redis session store connected');
        });
        
        this.redisClient.on('error', (err) => {
          this.logger.error(`Redis session store error: ${err.message}`);
        });
      } catch (error) {
        this.logger.error(`Failed to initialize Redis session store: ${error.message}`);
        this.logger.warn('Falling back to in-memory session store');
      }
    } else {
      this.logger.log('Using in-memory session store');
    }
  }

  async onModuleDestroy() {
    if (this.useRedis && this.redisClient) {
      this.logger.log('Closing Redis session store connection');
      await this.redisClient.quit();
    }
  }

  async createSession(sessionId: string, userId: string, email: string, data: any, ttlSeconds?: number): Promise<void> {
    const expiresAt = Math.floor(Date.now() / 1000) + (ttlSeconds || this.sessionTTL);
    const session: Session = {
      id: sessionId,
      userId,
      email,
      data,
      expiresAt,
    };

    if (this.useRedis && this.redisClient) {
      try {
        await this.redisClient.set(
          sessionId,
          JSON.stringify(session),
          'EX',
          ttlSeconds || this.sessionTTL
        );
        // Also set a lookup by user ID for quick access to all user sessions
        await this.redisClient.sadd(`user:${userId}:sessions`, sessionId);
        this.logger.log(`Session created in Redis: ${sessionId} for user ${userId}`);
      } catch (error) {
        this.logger.error(`Failed to store session in Redis: ${error.message}`);
        // Fallback to in-memory
        this.inMemorySessions.set(sessionId, session);
      }
    } else {
      this.inMemorySessions.set(sessionId, session);
      this.logger.log(`Session created in memory: ${sessionId} for user ${userId}`);
    }
  }

  async getSession(sessionId: string): Promise<Session | null> {
    if (this.useRedis && this.redisClient) {
      try {
        const sessionData = await this.redisClient.get(sessionId);
        if (!sessionData) {
          return null;
        }
        
        const session = JSON.parse(sessionData) as Session;
        
        // Check if session is expired
        const now = Math.floor(Date.now() / 1000);
        if (session.expiresAt < now) {
          await this.deleteSession(sessionId);
          return null;
        }
        
        return session;
      } catch (error) {
        this.logger.error(`Failed to retrieve session from Redis: ${error.message}`);
        // Fallback to in-memory
        return this.inMemorySessions.get(sessionId) || null;
      }
    } else {
      const session = this.inMemorySessions.get(sessionId);
      if (!session) {
        return null;
      }
      
      // Check if session is expired
      const now = Math.floor(Date.now() / 1000);
      if (session.expiresAt < now) {
        this.inMemorySessions.delete(sessionId);
        return null;
      }
      
      return session;
    }
  }

  async getUserSessions(userId: string): Promise<Session[]> {
    if (this.useRedis && this.redisClient) {
      try {
        // Get all session IDs for this user
        const sessionIds = await this.redisClient.smembers(`user:${userId}:sessions`);
        if (!sessionIds.length) {
          return [];
        }
        
        // Get all sessions
        const sessionPromises = sessionIds.map(id => this.getSession(id));
        const sessions = await Promise.all(sessionPromises);
        
        // Filter out null values (expired sessions)
        return sessions.filter(session => session !== null);
      } catch (error) {
        this.logger.error(`Failed to retrieve user sessions from Redis: ${error.message}`);
        // Fallback to in-memory
        return this.getUserSessionsFromMemory(userId);
      }
    } else {
      return this.getUserSessionsFromMemory(userId);
    }
  }

  private getUserSessionsFromMemory(userId: string): Session[] {
    const now = Math.floor(Date.now() / 1000);
    const sessions: Session[] = [];
    
    this.inMemorySessions.forEach(session => {
      if (session.userId === userId && session.expiresAt > now) {
        sessions.push(session);
      }
    });
    
    return sessions;
  }

  async updateSession(sessionId: string, data: any): Promise<void> {
    const session = await this.getSession(sessionId);
    if (!session) {
      return;
    }
    
    const updatedSession: Session = {
      ...session,
      data: { ...session.data, ...data },
    };
    
    if (this.useRedis && this.redisClient) {
      try {
        // Calculate remaining TTL
        const now = Math.floor(Date.now() / 1000);
        const ttlRemaining = session.expiresAt - now;
        
        if (ttlRemaining <= 0) {
          await this.deleteSession(sessionId);
          return;
        }
        
        await this.redisClient.set(
          sessionId,
          JSON.stringify(updatedSession),
          'EX',
          ttlRemaining
        );
      } catch (error) {
        this.logger.error(`Failed to update session in Redis: ${error.message}`);
        // Fallback to in-memory
        this.inMemorySessions.set(sessionId, updatedSession);
      }
    } else {
      this.inMemorySessions.set(sessionId, updatedSession);
    }
  }

  async deleteSession(sessionId: string): Promise<void> {
    const session = await this.getSession(sessionId);
    
    if (this.useRedis && this.redisClient) {
      try {
        await this.redisClient.del(sessionId);
        
        // Remove from user sessions set if session exists
        if (session) {
          await this.redisClient.srem(`user:${session.userId}:sessions`, sessionId);
        }
      } catch (error) {
        this.logger.error(`Failed to delete session from Redis: ${error.message}`);
      }
    }
    
    // Always remove from in-memory store as fallback
    this.inMemorySessions.delete(sessionId);
  }

  async deleteAllUserSessions(userId: string): Promise<void> {
    if (this.useRedis && this.redisClient) {
      try {
        // Get all session IDs for this user
        const sessionIds = await this.redisClient.smembers(`user:${userId}:sessions`);
        
        // Delete each session
        if (sessionIds.length) {
          const pipeline = this.redisClient.pipeline();
          
          sessionIds.forEach(sessionId => {
            pipeline.del(sessionId);
          });
          
          // Remove all sessions from user sessions set
          pipeline.del(`user:${userId}:sessions`);
          
          await pipeline.exec();
        }
      } catch (error) {
        this.logger.error(`Failed to delete user sessions from Redis: ${error.message}`);
      }
    }
    
    // Also clean in-memory sessions in case fallback is being used
    this.inMemorySessions.forEach((session, key) => {
      if (session.userId === userId) {
        this.inMemorySessions.delete(key);
      }
    });
  }
}