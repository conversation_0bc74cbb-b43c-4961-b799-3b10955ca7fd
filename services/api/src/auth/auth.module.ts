import { Module } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AuthController } from './auth.controller';
import { BiometricController } from './controllers/biometric.controller';
import { AuthService } from './auth.service';
import { JwtStrategy } from './strategies/jwt.strategy';
import { Account } from '../system/entities/account.entity';
import { User } from '../system/entities/user.entity';
import { Session } from '../system/entities/session.entity';
import { Recovery } from '../system/entities/recovery.entity';
import { SupabaseModule } from './supabase/supabase.module';
import { SessionService } from './session/session.service';
import { RedisConfig } from '../config/redis.config';

@Module({
  imports: [
    TypeOrmModule.forFeature([Account, User, Session, Recovery]),
    PassportModule.register({ defaultStrategy: 'jwt' }),
    ConfigModule,
    SupabaseModule,
  ],
  controllers: [AuthController, BiometricController],
  providers: [
    AuthService, 
    JwtStrategy, 
    SessionService,
    RedisConfig,
  ],
  exports: [AuthService, JwtStrategy, PassportModule, SessionService],
})
export class AuthModule {}