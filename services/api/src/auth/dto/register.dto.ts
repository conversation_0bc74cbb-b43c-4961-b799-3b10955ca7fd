import { <PERSON><PERSON><PERSON>, <PERSON>N<PERSON>Empt<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class RegisterDto {
  @ApiProperty({
    example: '<PERSON>',
    description: 'Full name of the user',
  })
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'User email address',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    example: 'password123',
    description: 'User password (minimum 6 characters)',
  })
  @IsNotEmpty()
  @MinLength(6)
  password: string;

  @ApiProperty({
    example: '+1234567890',
    description: 'User phone number',
  })
  @IsNotEmpty()
  phone: string;

  @ApiPropertyOptional({
    example: 'New York, USA',
    description: 'User location',
  })
  location?: string;
}