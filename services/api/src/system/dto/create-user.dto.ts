import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eng<PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Status } from '../../common/enums/status.enum';

export class CreateUserDto {
  @ApiProperty({
    example: '<PERSON>',
    description: 'Full name of the user',
  })
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'User email address',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    example: 'password123',
    description: 'User password (minimum 6 characters)',
  })
  @IsNotEmpty()
  @MinLength(6)
  password: string;

  @ApiProperty({
    example: '+1234567890',
    description: 'User phone number',
  })
  @IsNotEmpty()
  phone: string;

  @ApiPropertyOptional({
    example: 'New York, USA',
    description: 'User location',
  })
  @IsOptional()
  location?: string;

  @ApiPropertyOptional({
    example: 'https://example.com/avatar.jpg',
    description: 'User avatar URL',
  })
  @IsOptional()
  avatar?: string;

  @ApiPropertyOptional({
    example: 'active',
    description: 'User status',
    enum: Status,
    default: Status.ACTIVE
  })
  @IsOptional()
  @IsEnum(Status)
  status?: Status;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'ID of the role to assign to this user',
  })
  @IsOptional()
  roleId?: string;
}