import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ption<PERSON>, <PERSON><PERSON>ength } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Status } from '../../common/enums/status.enum';

export class UpdateUserDto {
  @ApiPropertyOptional({
    example: '<PERSON>',
    description: 'Full name of the user',
  })
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({
    example: '<EMAIL>',
    description: 'User email address',
  })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({
    example: 'password123',
    description: 'User password (minimum 6 characters)',
  })
  @IsOptional()
  @MinLength(6)
  password?: string;

  @ApiPropertyOptional({
    example: '+1234567890',
    description: 'User phone number',
  })
  @IsOptional()
  phone?: string;

  @ApiPropertyOptional({
    example: 'New York, USA',
    description: 'User location',
  })
  @IsOptional()
  location?: string;

  @ApiPropertyOptional({
    example: 'https://example.com/avatar.jpg',
    description: 'User avatar URL',
  })
  @IsOptional()
  avatar?: string;

  @ApiPropertyOptional({
    example: 'active',
    description: 'User status',
    enum: Status
  })
  @IsOptional()
  @IsEnum(Status)
  status?: Status;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'ID of the role to assign to this user',
  })
  @IsOptional()
  roleId?: string;
}