import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne } from 'typeorm';
import { BaseEntity } from '../../common/base.entity';
import { Status } from '../../common/enums/status.enum';
import { Account } from './account.entity';

@Entity('schedules')
export class Schedule extends BaseEntity {
  @Column()
  name: string;

  @Column({
    type: 'enum',
    enum: Status,
    default: Status.ACTIVE
  })
  status: Status;

  @Column()
  start: Date;

  @Column()
  deadline: Date;

  @Column({ nullable: true })
  comment: string;

  @Column({ name: 'associated_table', nullable: true })
  associatedTable: string;

  @Column({ name: 'associated_id', nullable: true })
  associatedId: string;

  @ManyToOne(() => Account)
  @JoinColumn({ name: 'account_id' })
  account: Account;

  @Column('varchar', { array: true, nullable: true })
  assigned: string[];
}