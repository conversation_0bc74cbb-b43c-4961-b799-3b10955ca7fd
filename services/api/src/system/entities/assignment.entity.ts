import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne } from 'typeorm';
import { BaseEntity } from '../../common/base.entity';
import { Status } from '../../common/enums/status.enum';
import { Account } from './account.entity';

@Entity('assignments')
export class Assignment extends BaseEntity {
  @Column()
  name: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ nullable: true })
  attachment: string;

  @Column({
    type: 'enum',
    enum: Status,
    default: Status.CREATED
  })
  status: Status;

  @Column({ name: 'associated_table', nullable: true })
  associatedTable: string;

  @Column({ name: 'associated_id', nullable: true })
  associatedId: string;

  @Column('varchar', { array: true, nullable: true, name: 'to_list' })
  toList: string[];

  @Column('varchar', { array: true, nullable: true, name: 'cc_list' })
  ccList: string[];

  @ManyToOne(() => Account)
  @Join<PERSON><PERSON>umn({ name: 'account_id' })
  account: Account;
}