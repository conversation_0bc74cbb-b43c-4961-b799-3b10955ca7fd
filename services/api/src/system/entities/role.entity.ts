import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm';
import { BaseEntity } from '../../common/base.entity';
import { Status } from '../../common/enums/status.enum';
import { Department } from './department.entity';
import { Account } from './account.entity';

@Entity('roles')
export class Role extends BaseEntity {
  @Column()
  name: string;

  @Column({
    type: 'enum',
    enum: Status,
    default: Status.ACTIVE
  })
  status: Status;

  @ManyToOne(() => Department, department => department.roles)
  @JoinColumn({ name: 'department_id' })
  department: Department;

  @OneToMany(() => Account, account => account.role)
  accounts: Account[];
}