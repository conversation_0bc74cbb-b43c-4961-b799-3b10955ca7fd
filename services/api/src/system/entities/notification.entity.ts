import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne } from 'typeorm';
import { BaseEntity } from '../../common/base.entity';
import { Status } from '../../common/enums/status.enum';
import { Account } from './account.entity';

@Entity('notifications')
export class Notification extends BaseEntity {
  @Column()
  name: string;

  @Column({ length: 255 })
  message: string;

  @Column({ type: 'json', nullable: true })
  details: any;

  @Column({
    type: 'enum',
    enum: Status,
    default: Status.ACTIVE
  })
  status: Status;

  @Column({ name: 'associated_table', nullable: true })
  associatedTable: string;

  @Column({ name: 'associated_id', nullable: true })
  associatedId: string;

  @ManyToOne(() => Account)
  @JoinColumn({ name: 'account_id' })
  account: Account;
}