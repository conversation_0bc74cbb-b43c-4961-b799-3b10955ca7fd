import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { BaseEntity } from '../../common/base.entity';
import { Account } from './account.entity';

@Entity('recoveries')
export class Recovery extends BaseEntity {
  @Column()
  expiry: Date;

  @Column({ unique: true })
  token: string;

  @ManyToOne(() => Account, account => account.recoveries)
  @JoinColumn({ name: 'account_id' })
  account: Account;
}