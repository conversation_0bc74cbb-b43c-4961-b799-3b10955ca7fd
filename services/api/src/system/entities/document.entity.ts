import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne } from 'typeorm';
import { BaseEntity } from '../../common/base.entity';
import { Status } from '../../common/enums/status.enum';
import { Account } from './account.entity';

@Entity('documents')
export class Document extends BaseEntity {
  @Column()
  name: string;

  @Column({ unique: true })
  path: string;

  @Column({ nullable: true })
  category: string;

  @Column({
    type: 'enum',
    enum: Status,
    default: Status.ACTIVE
  })
  status: Status;

  @Column({ type: 'json', nullable: true })
  details: any;

  @Column({ nullable: true })
  description: string;

  @Column({ name: 'associated_table', nullable: true })
  associatedTable: string;

  @Column({ name: 'associated_id', nullable: true })
  associatedId: string;

  @ManyToOne(() => Account)
  @JoinColumn({ name: 'account_id' })
  account: Account;
}