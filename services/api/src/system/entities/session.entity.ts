import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne } from 'typeorm';
import { BaseEntity } from '../../common/base.entity';
import { Status } from '../../common/enums/status.enum';
import { Account } from './account.entity';

@Entity('sessions')
export class Session extends BaseEntity {
  @Column()
  expiry: Date;

  @Column({
    type: 'enum',
    enum: Status,
    default: Status.ACTIVE
  })
  status: Status;

  @Column({ unique: true })
  token: string;

  @Column({ name: 'refresh_tk' })
  refreshToken: string;

  @ManyToOne(() => Account, account => account.sessions)
  @JoinColumn({ name: 'account_id' })
  account: Account;
}