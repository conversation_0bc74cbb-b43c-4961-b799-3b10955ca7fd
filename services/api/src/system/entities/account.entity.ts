import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, OneToOne } from 'typeorm';
import { BaseEntity } from '../../common/base.entity';
import { Status } from '../../common/enums/status.enum';
import { User } from './user.entity';
import { Role } from './role.entity';
import { Session } from './session.entity';
import { Recovery } from './recovery.entity';

@Entity('accounts')
export class Account extends BaseEntity {
  @Column({ unique: true })
  email: string;

  @Column()
  password: string;

  @Column({
    type: 'enum',
    enum: Status,
    default: Status.ACTIVE
  })
  status: Status;

  @ManyToOne(() => Role, role => role.accounts)
  @JoinColumn({ name: 'role_id' })
  role: Role;

  @OneToOne(() => User, user => user.account)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @OneToMany(() => Session, session => session.account)
  sessions: Session[];

  @OneToMany(() => Recovery, recovery => recovery.account)
  recoveries: Recovery[];
}