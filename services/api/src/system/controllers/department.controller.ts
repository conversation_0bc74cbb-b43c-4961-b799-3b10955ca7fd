import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiParam, 
  ApiQuery, 
  ApiBearerAuth,
  ApiBody
} from '@nestjs/swagger';
import { DepartmentService } from '../services/department.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { GetUser } from '../../auth/decorators/get-user.decorator';
import { Account } from '../entities/account.entity';

@ApiTags('System')
@ApiBearerAuth()
@Controller('departments')
@UseGuards(JwtAuthGuard)
export class DepartmentController {
  constructor(private readonly departmentService: DepartmentService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new department' })
  @ApiResponse({ status: 201, description: 'The department has been successfully created.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { 
          type: 'string', 
          description: 'Department name' 
        },
        status: { 
          type: 'string',
          enum: ['active', 'inactive'],
          description: 'Department status'
        },
      },
      required: ['name'],
    },
  })
  create(@Body() createDepartmentDto: any, @GetUser() account: Account) {
    return this.departmentService.create(createDepartmentDto, account);
  }

  @Get()
  @ApiOperation({ summary: 'Get all departments with pagination' })
  @ApiQuery({ name: 'skip', required: false, description: 'Number of records to skip' })
  @ApiQuery({ name: 'take', required: false, description: 'Number of records to take' })
  @ApiResponse({ status: 200, description: 'Returns paginated list of departments' })
  findAll(@Query('skip') skip = 0, @Query('take') take = 10) {
    return this.departmentService.findAll({ skip: +skip, take: +take });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get department by ID' })
  @ApiParam({ name: 'id', description: 'Department ID' })
  @ApiResponse({ status: 200, description: 'Returns the department' })
  @ApiResponse({ status: 404, description: 'Department not found' })
  findOne(@Param('id') id: string) {
    return this.departmentService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update department details' })
  @ApiParam({ name: 'id', description: 'Department ID' })
  @ApiResponse({ status: 200, description: 'The department has been successfully updated.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 404, description: 'Department not found.' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { 
          type: 'string', 
          description: 'Department name' 
        },
        status: { 
          type: 'string',
          enum: ['active', 'inactive'],
          description: 'Department status'
        },
      }
    },
  })
  update(
    @Param('id') id: string,
    @Body() updateDepartmentDto: any,
    @GetUser() account: Account,
  ) {
    return this.departmentService.update(id, updateDepartmentDto, account);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Deactivate a department' })
  @ApiParam({ name: 'id', description: 'Department ID' })
  @ApiResponse({ status: 200, description: 'The department has been successfully deactivated.' })
  @ApiResponse({ status: 404, description: 'Department not found.' })
  remove(@Param('id') id: string, @GetUser() account: Account) {
    return this.departmentService.remove(id, account);
  }
}