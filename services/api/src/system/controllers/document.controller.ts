import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
  ApiBody
} from '@nestjs/swagger';
import { DocumentService } from '../services/document.service';
import { SupabaseAuthGuard } from '../../auth/guards/supabase-auth.guard';
import { GetUser } from '../../auth/decorators/get-user.decorator';
import { Account } from '../entities/account.entity';

interface CreateDocumentDto {
  name: string;
  path: string;
  category?: string;
  status?: string;
  details?: any;
  description?: string;
  associatedTable?: string;
  associatedId?: string;
}

@ApiTags('Documents')
@ApiBearerAuth()
@Controller('documents')
@UseGuards(SupabaseAuthGuard)
export class DocumentController {
  constructor(private readonly documentService: DocumentService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new document' })
  @ApiResponse({ status: 201, description: 'The document has been successfully created.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { 
          type: 'string', 
          description: 'Document name',
          example: 'Invoice for Cargo ABC-12345'
        },
        path: { 
          type: 'string', 
          description: 'Document file path',
          example: '/invoices/cargo-123.pdf'
        },
        category: {
          type: 'string',
          description: 'Document category',
          example: 'invoice'
        },
        status: {
          type: 'string',
          enum: ['active', 'inactive'],
          description: 'Document status'
        },
        details: {
          type: 'object',
          description: 'Additional document details'
        },
        description: {
          type: 'string',
          description: 'Document description'
        },
        associatedTable: {
          type: 'string',
          description: 'The table associated with this document'
        },
        associatedId: {
          type: 'string',
          description: 'ID of the record in the associated table'
        }
      },
      required: ['name', 'path'],
    },
  })
  create(@Body() createDocumentDto: CreateDocumentDto, @GetUser() account: Account) {
    return this.documentService.create(createDocumentDto, account);
  }

  @Get()
  @ApiOperation({ summary: 'Get all documents with pagination' })
  @ApiQuery({ name: 'skip', required: false, description: 'Number of records to skip' })
  @ApiQuery({ name: 'take', required: false, description: 'Number of records to take' })
  @ApiQuery({ name: 'category', required: false, description: 'Filter by document category' })
  @ApiResponse({ status: 200, description: 'Returns paginated list of documents' })
  findAll(
    @Query('skip') skip = 0,
    @Query('take') take = 10,
    @Query('category') category?: string,
  ) {
    return this.documentService.findAll({ skip: +skip, take: +take, category });
  }

  @Get('entity/:table/:id')
  @ApiOperation({ summary: 'Get all documents for a specific entity' })
  @ApiParam({ name: 'table', description: 'Entity table name' })
  @ApiParam({ name: 'id', description: 'Entity ID' })
  @ApiQuery({ name: 'skip', required: false, description: 'Number of records to skip' })
  @ApiQuery({ name: 'take', required: false, description: 'Number of records to take' })
  @ApiQuery({ name: 'category', required: false, description: 'Filter by document category' })
  @ApiResponse({ status: 200, description: 'Returns paginated list of entity documents' })
  findByEntity(
    @Param('table') table: string,
    @Param('id') id: string,
    @Query('skip') skip = 0,
    @Query('take') take = 10,
    @Query('category') category?: string,
  ) {
    return this.documentService.findByEntity(table, id, { skip: +skip, take: +take, category });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get document by ID' })
  @ApiParam({ name: 'id', description: 'Document ID' })
  @ApiResponse({ status: 200, description: 'Returns the document' })
  @ApiResponse({ status: 404, description: 'Document not found' })
  findOne(@Param('id') id: string) {
    return this.documentService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update document details' })
  @ApiParam({ name: 'id', description: 'Document ID' })
  @ApiResponse({ status: 200, description: 'The document has been successfully updated.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 404, description: 'Document not found.' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { 
          type: 'string', 
          description: 'Document name' 
        },
        path: { 
          type: 'string', 
          description: 'Document file path' 
        },
        category: {
          type: 'string',
          description: 'Document category'
        },
        status: {
          type: 'string',
          description: 'Document status'
        },
        details: {
          type: 'object',
          description: 'Additional document details'
        },
        description: {
          type: 'string',
          description: 'Document description'
        }
      }
    }
  })
  update(@Param('id') id: string, @Body() updateDocumentDto: Partial<CreateDocumentDto>) {
    return this.documentService.update(id, updateDocumentDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete document' })
  @ApiParam({ name: 'id', description: 'Document ID' })
  @ApiResponse({ status: 200, description: 'The document has been successfully deleted.' })
  @ApiResponse({ status: 404, description: 'Document not found.' })
  remove(@Param('id') id: string) {
    return this.documentService.remove(id);
  }

  @Post('qrcode/generate')
  @ApiOperation({ summary: 'Generate a QR code' })
  @ApiResponse({ status: 200, description: 'QR code generated successfully' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        data: { 
          type: 'string', 
          description: 'Data to encode in the QR code' 
        }
      },
      required: ['data'],
    },
  })
  generateQRCode(@Body() body: { data: string }) {
    return this.documentService.generateQRCode(body.data);
  }

  @Post('qrcode/tracking')
  @ApiOperation({ summary: 'Generate a tracking QR code' })
  @ApiResponse({ status: 200, description: 'Tracking QR code generated successfully' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        id: { 
          type: 'string', 
          description: 'Entity ID to encode' 
        },
        type: { 
          type: 'string', 
          description: 'Entity type' 
        }
      },
      required: ['id', 'type'],
    },
  })
  generateTrackingQRCode(@Body() body: { id: string, type: string }) {
    return this.documentService.generateTrackingQRCode(body.id, body.type);
  }
}