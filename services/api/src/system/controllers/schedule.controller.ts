import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
  ApiBody
} from '@nestjs/swagger';
import { ScheduleService } from '../services/schedule.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { GetUser } from '../../auth/decorators/get-user.decorator';
import { Account } from '../entities/account.entity';

@ApiTags('System')
@ApiBearerAuth()
@Controller('schedules')
@UseGuards(JwtAuthGuard)
export class ScheduleController {
  constructor(private readonly scheduleService: ScheduleService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new schedule' })
  @ApiResponse({ status: 201, description: 'The schedule has been successfully created.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { 
          type: 'string', 
          description: 'Schedule name' 
        },
        status: { 
          type: 'string',
          enum: ['active', 'inactive', 'completed', 'cancelled'],
          description: 'Schedule status'
        },
        start: {
          type: 'string',
          format: 'date-time',
          description: 'Start date and time'
        },
        deadline: {
          type: 'string',
          format: 'date-time',
          description: 'Deadline date and time'
        },
        comment: {
          type: 'string',
          description: 'Optional comment'
        },
        associatedTable: {
          type: 'string',
          description: 'The table associated with this schedule'
        },
        associatedId: {
          type: 'string',
          description: 'ID of the record in the associated table'
        },
        assigned: {
          type: 'array',
          items: { type: 'string' },
          description: 'List of user IDs assigned to this schedule'
        }
      },
      required: ['name', 'start', 'deadline'],
    },
  })
  create(@Body() createScheduleDto: any, @GetUser() account: Account) {
    return this.scheduleService.create(createScheduleDto, account);
  }

  @Get()
  @ApiOperation({ summary: 'Get all schedules with pagination' })
  @ApiQuery({ name: 'skip', required: false, description: 'Number of records to skip' })
  @ApiQuery({ name: 'take', required: false, description: 'Number of records to take' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by schedule status' })
  @ApiResponse({ status: 200, description: 'Returns paginated list of schedules' })
  findAll(
    @Query('skip') skip = 0,
    @Query('take') take = 10,
    @Query('status') status?: string,
  ) {
    return this.scheduleService.findAll({ skip: +skip, take: +take, status });
  }

  @Get('entity/:table/:id')
  @ApiOperation({ summary: 'Get all schedules for a specific entity' })
  @ApiParam({ name: 'table', description: 'Entity table name' })
  @ApiParam({ name: 'id', description: 'Entity ID' })
  @ApiQuery({ name: 'skip', required: false, description: 'Number of records to skip' })
  @ApiQuery({ name: 'take', required: false, description: 'Number of records to take' })
  @ApiResponse({ status: 200, description: 'Returns paginated list of entity schedules' })
  findByEntity(
    @Param('table') table: string,
    @Param('id') id: string,
    @Query('skip') skip = 0,
    @Query('take') take = 10,
  ) {
    return this.scheduleService.findByEntity(table, id, { skip: +skip, take: +take });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get schedule by ID' })
  @ApiParam({ name: 'id', description: 'Schedule ID' })
  @ApiResponse({ status: 200, description: 'Returns the schedule' })
  @ApiResponse({ status: 404, description: 'Schedule not found' })
  findOne(@Param('id') id: string) {
    return this.scheduleService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update schedule details' })
  @ApiParam({ name: 'id', description: 'Schedule ID' })
  @ApiResponse({ status: 200, description: 'The schedule has been successfully updated.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 404, description: 'Schedule not found.' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { 
          type: 'string', 
          description: 'Schedule name' 
        },
        status: { 
          type: 'string',
          enum: ['active', 'inactive', 'completed', 'cancelled'],
          description: 'Schedule status'
        },
        start: {
          type: 'string',
          format: 'date-time',
          description: 'Start date and time'
        },
        deadline: {
          type: 'string',
          format: 'date-time',
          description: 'Deadline date and time'
        },
        comment: {
          type: 'string',
          description: 'Optional comment'
        },
        assigned: {
          type: 'array',
          items: { type: 'string' },
          description: 'List of user IDs assigned to this schedule'
        }
      }
    },
  })
  update(
    @Param('id') id: string,
    @Body() updateScheduleDto: any,
    @GetUser() account: Account,
  ) {
    return this.scheduleService.update(id, updateScheduleDto, account);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Cancel a schedule' })
  @ApiParam({ name: 'id', description: 'Schedule ID' })
  @ApiResponse({ status: 200, description: 'The schedule has been successfully cancelled.' })
  @ApiResponse({ status: 404, description: 'Schedule not found.' })
  remove(@Param('id') id: string, @GetUser() account: Account) {
    return this.scheduleService.remove(id, account);
  }
}