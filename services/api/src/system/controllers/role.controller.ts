import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiParam, 
  ApiQuery, 
  ApiBearerAuth,
  ApiBody
} from '@nestjs/swagger';
import { RoleService } from '../services/role.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { GetUser } from '../../auth/decorators/get-user.decorator';
import { Account } from '../entities/account.entity';

@ApiTags('System')
@ApiBearerAuth()
@Controller('roles')
@UseGuards(JwtAuthGuard)
export class RoleController {
  constructor(private readonly roleService: RoleService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new role' })
  @ApiResponse({ status: 201, description: 'The role has been successfully created.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 404, description: 'Department not found.' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { 
          type: 'string', 
          description: 'Role name' 
        },
        status: { 
          type: 'string',
          enum: ['active', 'inactive'],
          description: 'Role status'
        },
        departmentId: {
          type: 'string',
          description: 'ID of the department this role belongs to'
        }
      },
      required: ['name', 'departmentId'],
    },
  })
  create(@Body() createRoleDto: any, @GetUser() account: Account) {
    return this.roleService.create(createRoleDto, account);
  }

  @Get()
  @ApiOperation({ summary: 'Get all roles with pagination' })
  @ApiQuery({ name: 'skip', required: false, description: 'Number of records to skip' })
  @ApiQuery({ name: 'take', required: false, description: 'Number of records to take' })
  @ApiQuery({ name: 'departmentId', required: false, description: 'Filter by department ID' })
  @ApiResponse({ status: 200, description: 'Returns paginated list of roles' })
  findAll(@Query('skip') skip = 0, @Query('take') take = 10, @Query('departmentId') departmentId?: string) {
    return this.roleService.findAll({ skip: +skip, take: +take, departmentId });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get role by ID' })
  @ApiParam({ name: 'id', description: 'Role ID' })
  @ApiResponse({ status: 200, description: 'Returns the role' })
  @ApiResponse({ status: 404, description: 'Role not found' })
  findOne(@Param('id') id: string) {
    return this.roleService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update role details' })
  @ApiParam({ name: 'id', description: 'Role ID' })
  @ApiResponse({ status: 200, description: 'The role has been successfully updated.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 404, description: 'Role not found.' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { 
          type: 'string', 
          description: 'Role name' 
        },
        status: { 
          type: 'string',
          enum: ['active', 'inactive'],
          description: 'Role status'
        },
        departmentId: {
          type: 'string',
          description: 'ID of the department this role belongs to'
        }
      }
    },
  })
  update(
    @Param('id') id: string,
    @Body() updateRoleDto: any,
    @GetUser() account: Account,
  ) {
    return this.roleService.update(id, updateRoleDto, account);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Deactivate a role' })
  @ApiParam({ name: 'id', description: 'Role ID' })
  @ApiResponse({ status: 200, description: 'The role has been successfully deactivated.' })
  @ApiResponse({ status: 404, description: 'Role not found.' })
  remove(@Param('id') id: string, @GetUser() account: Account) {
    return this.roleService.remove(id, account);
  }
}