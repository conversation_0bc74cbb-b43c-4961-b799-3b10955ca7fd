import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiParam, 
  ApiQuery, 
  ApiBearerAuth 
} from '@nestjs/swagger';
import { LogsService } from '../services/logs.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@ApiTags('System')
@ApiBearerAuth()
@Controller('logs')
@UseGuards(JwtAuthGuard)
export class LogsController {
  constructor(private readonly logsService: LogsService) {}

  @Get()
  @ApiOperation({ summary: 'Get all logs with pagination' })
  @ApiQuery({ name: 'skip', required: false, description: 'Number of records to skip' })
  @ApiQuery({ name: 'take', required: false, description: 'Number of records to take' })
  @ApiResponse({ status: 200, description: 'Returns paginated list of logs' })
  findAll(@Query('skip') skip = 0, @Query('take') take = 10) {
    return this.logsService.findAll({ skip: +skip, take: +take });
  }

  @Get('entity/:table/:id')
  @ApiOperation({ summary: 'Get all logs for a specific entity' })
  @ApiParam({ name: 'table', description: 'Entity table name' })
  @ApiParam({ name: 'id', description: 'Entity ID' })
  @ApiQuery({ name: 'skip', required: false, description: 'Number of records to skip' })
  @ApiQuery({ name: 'take', required: false, description: 'Number of records to take' })
  @ApiResponse({ status: 200, description: 'Returns paginated list of entity logs' })
  findByEntity(
    @Param('table') table: string,
    @Param('id') id: string,
    @Query('skip') skip = 0,
    @Query('take') take = 10,
  ) {
    return this.logsService.findByEntity(table, id, { skip: +skip, take: +take });
  }
}