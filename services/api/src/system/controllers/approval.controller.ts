import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiParam, 
  ApiQuery, 
  ApiBearerAuth, 
  ApiBody
} from '@nestjs/swagger';
import { ApprovalService } from '../services/approval.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { GetUser } from '../../auth/decorators/get-user.decorator';
import { Account } from '../entities/account.entity';

@ApiTags('System')
@ApiBearerAuth()
@Controller('approvals')
@UseGuards(JwtAuthGuard)
export class ApprovalController {
  constructor(private readonly approvalService: ApprovalService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new approval' })
  @ApiResponse({ status: 201, description: 'The approval has been successfully created.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        details: { 
          type: 'object',
          description: 'Detailed information about the approval'
        },
        status: { 
          type: 'string',
          enum: ['pending', 'approved', 'rejected'],
          description: 'Status of the approval'
        },
        associatedTable: {
          type: 'string',
          description: 'The table associated with this approval'
        },
        associatedId: {
          type: 'string',
          description: 'ID of the record in the associated table'
        },
      },
      required: ['details', 'status'],
    },
  })
  create(@Body() createApprovalDto: any, @GetUser() account: Account) {
    return this.approvalService.create(createApprovalDto, account);
  }

  @Get()
  @ApiOperation({ summary: 'Get all approvals with pagination' })
  @ApiQuery({ name: 'skip', required: false, description: 'Number of records to skip' })
  @ApiQuery({ name: 'take', required: false, description: 'Number of records to take' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by approval status' })
  @ApiQuery({ name: 'associatedTable', required: false, description: 'Filter by associated table' })
  @ApiQuery({ name: 'associatedId', required: false, description: 'Filter by associated ID' })
  @ApiResponse({ status: 200, description: 'Returns paginated list of approvals' })
  findAll(
    @Query('skip') skip = 0,
    @Query('take') take = 10,
    @Query('status') status?: string,
    @Query('associatedTable') associatedTable?: string,
    @Query('associatedId') associatedId?: string,
  ) {
    return this.approvalService.findAll({
      skip: +skip,
      take: +take,
      status,
      associatedTable,
      associatedId,
    });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get approval by ID' })
  @ApiParam({ name: 'id', description: 'Approval ID' })
  @ApiResponse({ status: 200, description: 'Returns the approval' })
  @ApiResponse({ status: 404, description: 'Approval not found' })
  findOne(@Param('id') id: string) {
    return this.approvalService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update approval details' })
  @ApiParam({ name: 'id', description: 'Approval ID' })
  @ApiResponse({ status: 200, description: 'The approval has been successfully updated.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 404, description: 'Approval not found.' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        details: { 
          type: 'object',
          description: 'Detailed information about the approval'
        },
        status: { 
          type: 'string',
          enum: ['pending', 'approved', 'rejected'],
          description: 'Status of the approval'
        },
      }
    },
  })
  update(
    @Param('id') id: string,
    @Body() updateApprovalDto: any,
    @GetUser() account: Account,
  ) {
    return this.approvalService.update(id, updateApprovalDto, account);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete approval' })
  @ApiParam({ name: 'id', description: 'Approval ID' })
  @ApiResponse({ status: 200, description: 'The approval has been successfully deleted.' })
  @ApiResponse({ status: 404, description: 'Approval not found.' })
  remove(@Param('id') id: string, @GetUser() account: Account) {
    return this.approvalService.remove(id, account);
  }
}