import {
  Controller,
  Get,
  Post,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiParam, 
  ApiQuery, 
  ApiBearerAuth 
} from '@nestjs/swagger';
import { NotificationService } from '../services/notification.service';
import { SupabaseAuthGuard } from '../../auth/guards/supabase-auth.guard';

@ApiTags('Notifications')
@ApiBearerAuth()
@Controller('notifications')
@UseGuards(SupabaseAuthGuard)
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @Get()
  @ApiOperation({ summary: 'Get all notifications with pagination' })
  @ApiQuery({ name: 'skip', required: false, description: 'Number of records to skip' })
  @ApiQuery({ name: 'take', required: false, description: 'Number of records to take' })
  @ApiResponse({ status: 200, description: 'Returns paginated list of notifications' })
  findAll(@Query('skip') skip = 0, @Query('take') take = 10) {
    return this.notificationService.findAll({ skip: +skip, take: +take });
  }

  @Get('entity/:table/:id')
  @ApiOperation({ summary: 'Get all notifications for a specific entity' })
  @ApiParam({ name: 'table', description: 'Entity table name' })
  @ApiParam({ name: 'id', description: 'Entity ID' })
  @ApiQuery({ name: 'skip', required: false, description: 'Number of records to skip' })
  @ApiQuery({ name: 'take', required: false, description: 'Number of records to take' })
  @ApiResponse({ status: 200, description: 'Returns paginated list of entity notifications' })
  findByEntity(
    @Param('table') table: string,
    @Param('id') id: string,
    @Query('skip') skip = 0,
    @Query('take') take = 10,
  ) {
    return this.notificationService.findByEntity(table, id, { skip: +skip, take: +take });
  }

  @Post(':id/read')
  @ApiOperation({ summary: 'Mark notification as read' })
  @ApiParam({ name: 'id', description: 'Notification ID' })
  @ApiResponse({ status: 200, description: 'Notification marked as read' })
  @ApiResponse({ status: 404, description: 'Notification not found' })
  markAsRead(@Param('id') id: string) {
    return this.notificationService.markAsRead(id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a notification' })
  @ApiParam({ name: 'id', description: 'Notification ID' })
  @ApiResponse({ status: 200, description: 'Notification deleted successfully' })
  @ApiResponse({ status: 404, description: 'Notification not found' })
  remove(@Param('id') id: string) {
    return this.notificationService.deleteNotification(id);
  }
}