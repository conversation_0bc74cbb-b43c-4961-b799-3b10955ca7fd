import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
  ApiBody
} from '@nestjs/swagger';
import { AssignmentService } from '../services/assignment.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { GetUser } from '../../auth/decorators/get-user.decorator';
import { Account } from '../entities/account.entity';

@ApiTags('System')
@ApiBearerAuth()
@Controller('assignments')
@UseGuards(JwtAuthGuard)
export class AssignmentController {
  constructor(private readonly assignmentService: AssignmentService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new assignment' })
  @ApiResponse({ status: 201, description: 'The assignment has been successfully created.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { 
          type: 'string', 
          description: 'Assignment name' 
        },
        description: { 
          type: 'string', 
          description: 'Assignment description' 
        },
        attachment: { 
          type: 'string', 
          description: 'Optional attachment path' 
        },
        status: {
          type: 'string',
          enum: ['created', 'in_progress', 'completed', 'cancelled'],
          description: 'Assignment status'
        },
        associatedTable: {
          type: 'string',
          description: 'The table associated with this assignment'
        },
        associatedId: {
          type: 'string',
          description: 'ID of the record in the associated table'
        },
        toList: {
          type: 'array',
          items: { type: 'string' },
          description: 'List of user IDs assigned to this task'
        },
        ccList: {
          type: 'array',
          items: { type: 'string' },
          description: 'List of user IDs to be notified about this task'
        }
      },
      required: ['name', 'description'],
    },
  })
  create(@Body() createAssignmentDto: any, @GetUser() account: Account) {
    return this.assignmentService.create(createAssignmentDto, account);
  }

  @Get()
  @ApiOperation({ summary: 'Get all assignments with pagination' })
  @ApiQuery({ name: 'skip', required: false, description: 'Number of records to skip' })
  @ApiQuery({ name: 'take', required: false, description: 'Number of records to take' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by assignment status' })
  @ApiResponse({ status: 200, description: 'Returns paginated list of assignments' })
  findAll(
    @Query('skip') skip = 0,
    @Query('take') take = 10,
    @Query('status') status?: string,
  ) {
    return this.assignmentService.findAll({ skip: +skip, take: +take, status });
  }

  @Get('entity/:table/:id')
  @ApiOperation({ summary: 'Get all assignments for a specific entity' })
  @ApiParam({ name: 'table', description: 'Entity table name' })
  @ApiParam({ name: 'id', description: 'Entity ID' })
  @ApiQuery({ name: 'skip', required: false, description: 'Number of records to skip' })
  @ApiQuery({ name: 'take', required: false, description: 'Number of records to take' })
  @ApiResponse({ status: 200, description: 'Returns paginated list of entity assignments' })
  findByEntity(
    @Param('table') table: string,
    @Param('id') id: string,
    @Query('skip') skip = 0,
    @Query('take') take = 10,
  ) {
    return this.assignmentService.findByEntity(table, id, { skip: +skip, take: +take });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get assignment by ID' })
  @ApiParam({ name: 'id', description: 'Assignment ID' })
  @ApiResponse({ status: 200, description: 'Returns the assignment' })
  @ApiResponse({ status: 404, description: 'Assignment not found' })
  findOne(@Param('id') id: string) {
    return this.assignmentService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update assignment details' })
  @ApiParam({ name: 'id', description: 'Assignment ID' })
  @ApiResponse({ status: 200, description: 'The assignment has been successfully updated.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 404, description: 'Assignment not found.' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        name: { 
          type: 'string', 
          description: 'Assignment name' 
        },
        description: { 
          type: 'string', 
          description: 'Assignment description' 
        },
        attachment: { 
          type: 'string', 
          description: 'Optional attachment path' 
        },
        status: {
          type: 'string',
          enum: ['created', 'in_progress', 'completed', 'cancelled'],
          description: 'Assignment status'
        },
        toList: {
          type: 'array',
          items: { type: 'string' },
          description: 'List of user IDs assigned to this task'
        },
        ccList: {
          type: 'array',
          items: { type: 'string' },
          description: 'List of user IDs to be notified about this task'
        }
      }
    },
  })
  update(
    @Param('id') id: string,
    @Body() updateAssignmentDto: any,
    @GetUser() account: Account,
  ) {
    return this.assignmentService.update(id, updateAssignmentDto, account);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Cancel an assignment' })
  @ApiParam({ name: 'id', description: 'Assignment ID' })
  @ApiResponse({ status: 200, description: 'The assignment has been successfully cancelled.' })
  @ApiResponse({ status: 404, description: 'Assignment not found.' })
  remove(@Param('id') id: string, @GetUser() account: Account) {
    return this.assignmentService.remove(id, account);
  }
}