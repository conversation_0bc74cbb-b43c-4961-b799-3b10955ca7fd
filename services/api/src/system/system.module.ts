import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { User } from './entities/user.entity';
import { Account } from './entities/account.entity';
import { Session } from './entities/session.entity';
import { Recovery } from './entities/recovery.entity';
import { Department } from './entities/department.entity';
import { Role } from './entities/role.entity';
import { Notification } from './entities/notification.entity';
import { Document } from './entities/document.entity';
import { Assignment } from './entities/assignment.entity';
import { Logs } from './entities/logs.entity';
import { Approval } from './entities/approval.entity';
import { Schedule } from './entities/schedule.entity';
import { SupabaseModule } from '../auth/supabase/supabase.module';

import { UserController } from './controllers/user.controller';
import { DepartmentController } from './controllers/department.controller';
import { RoleController } from './controllers/role.controller';
import { NotificationController } from './controllers/notification.controller';
import { DocumentController } from './controllers/document.controller';
import { AssignmentController } from './controllers/assignment.controller';
import { LogsController } from './controllers/logs.controller';
import { ApprovalController } from './controllers/approval.controller';
import { ScheduleController } from './controllers/schedule.controller';

import { UserService } from './services/user.service';
import { DepartmentService } from './services/department.service';
import { RoleService } from './services/role.service';
import { NotificationService } from './services/notification.service';
import { DocumentService } from './services/document.service';
import { AssignmentService } from './services/assignment.service';
import { LogsService } from './services/logs.service';
import { ApprovalService } from './services/approval.service';
import { ScheduleService } from './services/schedule.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      Account,
      Session,
      Recovery,
      Department,
      Role,
      Notification,
      Document,
      Assignment,
      Logs,
      Approval,
      Schedule,
    ]),
    SupabaseModule,
  ],
  controllers: [
    UserController,
    DepartmentController,
    RoleController,
    NotificationController,
    DocumentController,
    AssignmentController,
    LogsController,
    ApprovalController,
    ScheduleController,
  ],
  providers: [
    UserService,
    DepartmentService,
    RoleService,
    NotificationService,
    DocumentService,
    AssignmentService,
    LogsService,
    ApprovalService,
    ScheduleService,
  ],
  exports: [
    UserService,
    DepartmentService,
    RoleService,
    NotificationService,
    DocumentService,
    AssignmentService,
    LogsService,
    ApprovalService,
    ScheduleService,
  ],
})
export class SystemModule {}