import { Injectable, NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Schedule } from '../entities/schedule.entity';
import { Account } from '../entities/account.entity';
import { LogsService } from './logs.service';
import { Status } from '../../common/enums/status.enum';

@Injectable()
export class ScheduleService {
  constructor(
    @InjectRepository(Schedule)
    private scheduleRepository: Repository<Schedule>,
    private logsService: LogsService,
  ) {}

  async create(createScheduleDto: any, account: Account): Promise<Schedule> {
    try {
      const schedule = this.scheduleRepository.create({
        ...createScheduleDto,
        account,
      });

      const savedSchedule = await this.scheduleRepository.save(schedule);

      // Log the schedule creation
      await this.logsService.create({
        event: 'Schedule Created',
        message: `Schedule "${createScheduleDto.name}" created`,
        associatedTable: 'schedules',
        associatedId: savedSchedule[0].id,
      }, account);


      if (!schedule) {
        throw new NotFoundException('Schedule not found after creation');
      }

      return savedSchedule[0];
    } catch (error) {
      throw new InternalServerErrorException('Failed to create schedule');
    }
  }

  async findAll(options: { 
    skip: number; 
    take: number;
    status?: string;
  }): Promise<{ schedules: Schedule[]; total: number }> {
    const { skip, take, status } = options;

    const queryBuilder = this.scheduleRepository
      .createQueryBuilder('schedule')
      .leftJoinAndSelect('schedule.account', 'account')
      .leftJoinAndSelect('account.user', 'user')
      .skip(skip)
      .take(take)
      .orderBy('schedule.deadline', 'ASC');

    if (status) {
      queryBuilder.andWhere('schedule.status = :status', { status });
    }

    const [schedules, total] = await queryBuilder.getManyAndCount();

    return { schedules, total };
  }

  async findByEntity(
    table: string,
    id: string,
    options: { skip: number; take: number },
  ): Promise<{ schedules: Schedule[]; total: number }> {
    const { skip, take } = options;

    const queryBuilder = this.scheduleRepository
      .createQueryBuilder('schedule')
      .leftJoinAndSelect('schedule.account', 'account')
      .leftJoinAndSelect('account.user', 'user')
      .where('schedule.associatedTable = :table', { table })
      .andWhere('schedule.associatedId = :id', { id })
      .skip(skip)
      .take(take)
      .orderBy('schedule.deadline', 'ASC');

    const [schedules, total] = await queryBuilder.getManyAndCount();

    return { schedules, total };
  }

  async findOne(id: string): Promise<Schedule> {
    const schedule = await this.scheduleRepository.findOne({
      where: { id },
      relations: ['account', 'account.user'],
    });

    if (!schedule) {
      throw new NotFoundException(`Schedule with ID ${id} not found`);
    }

    return schedule;
  }

  async update(id: string, updateScheduleDto: any, account: Account): Promise<Schedule> {
    const schedule = await this.findOne(id);

    try {
      // Check if status is changing
      const statusChanged = updateScheduleDto.status && updateScheduleDto.status !== schedule.status;
      const oldStatus = schedule.status;

      // Update schedule data
      Object.assign(schedule, updateScheduleDto);

      const updatedSchedule = await this.scheduleRepository.save(schedule);

      // Log the update
      await this.logsService.create({
        event: 'Schedule Updated',
        message: `Schedule "${schedule.name}" updated${statusChanged ? ` - Status changed from ${oldStatus} to ${schedule.status}` : ''}`,
        associatedTable: 'schedules',
        associatedId: schedule.id,
      }, account);

      return updatedSchedule;
    } catch (error) {
      throw new InternalServerErrorException('Failed to update schedule');
    }
  }

  async remove(id: string, account: Account): Promise<void> {
    const schedule = await this.findOne(id);

    try {
      // Don't actually delete, just mark as cancelled
      schedule.status = Status.CANCELLED;
      await this.scheduleRepository.save(schedule);

      // Log the deletion
      await this.logsService.create({
        event: 'Schedule Cancelled',
        message: `Schedule "${schedule.name}" has been cancelled`,
        associatedTable: 'schedules',
        associatedId: schedule.id,
      }, account);
    } catch (error) {
      throw new InternalServerErrorException('Failed to cancel schedule');
    }
  }
}