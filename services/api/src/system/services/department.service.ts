import { Injectable, NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Department } from '../entities/department.entity';
import { Account } from '../entities/account.entity';
import { LogsService } from './logs.service';
import { Status } from '../../common/enums/status.enum';

@Injectable()
export class DepartmentService {
  constructor(
    @InjectRepository(Department)
    private departmentRepository: Repository<Department>,
    private logsService: LogsService,
  ) {}

  async create(createDepartmentDto: any, account: Account): Promise<Department> {
    try {
      const department = this.departmentRepository.create({
        ...createDepartmentDto,
      });

      const savedDepartment = await this.departmentRepository.save(department);

      // Log the department creation
      await this.logsService.create({
        event: 'Department Created',
        message: `Department "${createDepartmentDto.name}" created`,
        associatedTable: 'departments',
        associatedId: savedDepartment[0].id,
      }, account);

      return savedDepartment[0];
    } catch (error) {
      throw new InternalServerErrorException('Failed to create department');
    }
  }

  async findAll(options: { skip: number; take: number }): Promise<{ departments: Department[]; total: number }> {
    const { skip, take } = options;

    const [departments, total] = await this.departmentRepository.findAndCount({
      skip,
      take,
      relations: ['roles'],
      order: { name: 'ASC' },
    });

    return { departments, total };
  }

  async findOne(id: string): Promise<Department> {
    const department = await this.departmentRepository.findOne({
      where: { id },
      relations: ['roles'],
    });

    if (!department) {
      throw new NotFoundException(`Department with ID ${id} not found`);
    }

    return department;
  }

  async update(id: string, updateDepartmentDto: any, account: Account): Promise<Department> {
    const department = await this.findOne(id);

    try {
      // Update department
      Object.assign(department, updateDepartmentDto);

      const updatedDepartment = await this.departmentRepository.save(department);

      // Log the update
      await this.logsService.create({
        event: 'Department Updated',
        message: `Department "${department.name}" updated`,
        associatedTable: 'departments',
        associatedId: department.id,
      }, account);

      return updatedDepartment;
    } catch (error) {
      throw new InternalServerErrorException('Failed to update department');
    }
  }

  async remove(id: string, account: Account): Promise<void> {
    const department = await this.findOne(id);

    try {
      // Don't actually delete, just mark as inactive
      department.status = Status.INACTIVE;
      await this.departmentRepository.save(department);

      // Log the deletion
      await this.logsService.create({
        event: 'Department Deactivated',
        message: `Department "${department.name}" has been deactivated`,
        associatedTable: 'departments',
        associatedId: department.id,
      }, account);
    } catch (error) {
      throw new InternalServerErrorException('Failed to deactivate department');
    }
  }
}