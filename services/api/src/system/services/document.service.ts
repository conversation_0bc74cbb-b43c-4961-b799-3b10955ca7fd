import {
  Injectable,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Document } from '../entities/document.entity';
import { Account } from '../entities/account.entity';
import * as QRCode from 'qrcode';
import { Status } from 'src/common/enums/status.enum';
import { ConfigService } from '@nestjs/config';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';

interface CreateDocumentDto {
  name: string;
  path: string;
  category?: string;
  status?: string;
  details?: any;
  description?: string;
  associatedTable?: string;
  associatedId?: string;
}

interface UploadDocumentOptions {
  content: Buffer | string;
  fileName: string;
  contentType: string;
  folder?: string;
}

@Injectable()
export class DocumentService {
  private s3Client: S3Client;
  private bucketName: string;

  constructor(
    @InjectRepository(Document)
    private documentRepository: Repository<Document>,
    private configService: ConfigService,
  ) {
    // Initialize S3 client
    this.s3Client = new S3Client({
      region: this.configService.get<string>('SUPABASE_REGION', 'us-east-1'),
      credentials: {
        accessKeyId: this.configService.get<string>(
          'SUPABASE_ACCESS_KEY_ID',
          '',
        ),
        secretAccessKey: this.configService.get<string>(
          'SUPABASE_SECRET_ACCESS_KEY',
          '',
        ),
      },
    });

    this.bucketName = this.configService.get<string>('SUPABASE_S3_BUCKET', '');
  }

  async create(
    createDocumentDto: CreateDocumentDto,
    account: Account,
  ): Promise<Document> {
    const document = new Document();
    document.name = createDocumentDto.name;
    document.path = createDocumentDto.path;
    document.category = createDocumentDto.category || '';
    document.status = (createDocumentDto.status as Status) || Status.ACTIVE;
    document.details = createDocumentDto.details;
    document.description = createDocumentDto.description || '';
    document.associatedTable = createDocumentDto.associatedTable || '';
    document.associatedId = createDocumentDto.associatedId || '';
    document.account = account;

    return this.documentRepository.save(document);
  }

  async findAll(options: {
    skip: number;
    take: number;
    category?: string;
  }): Promise<{ documents: Document[]; total: number }> {
    const { skip, take, category } = options;

    const queryBuilder = this.documentRepository
      .createQueryBuilder('document')
      .leftJoinAndSelect('document.account', 'account')
      .leftJoinAndSelect('account.user', 'user');

    if (category) {
      queryBuilder.andWhere('document.category = :category', { category });
    }

    queryBuilder.skip(skip).take(take);

    const [documents, total] = await queryBuilder.getManyAndCount();

    return { documents, total };
  }

  async findByEntity(
    table: string,
    id: string,
    options: { skip: number; take: number; category?: string },
  ): Promise<{ documents: Document[]; total: number }> {
    const { skip, take, category } = options;

    const queryBuilder = this.documentRepository
      .createQueryBuilder('document')
      .leftJoinAndSelect('document.account', 'account')
      .leftJoinAndSelect('account.user', 'user')
      .where('document.associatedTable = :table', { table })
      .andWhere('document.associatedId = :id', { id });

    if (category) {
      queryBuilder.andWhere('document.category = :category', { category });
    }

    queryBuilder.skip(skip).take(take);

    const [documents, total] = await queryBuilder.getManyAndCount();

    return { documents, total };
  }

  async findOne(id: string): Promise<Document> {
    const document = await this.documentRepository.findOne({
      where: { id },
      relations: ['account', 'account.user'],
    });

    if (!document) {
      throw new NotFoundException(`Document with ID ${id} not found`);
    }

    return document;
  }

  async update(
    id: string,
    updateDocumentDto: Partial<CreateDocumentDto>,
  ): Promise<Document> {
    const document = await this.findOne(id);

    Object.assign(document, updateDocumentDto);

    return this.documentRepository.save(document);
  }

  async remove(id: string): Promise<void> {
    const document = await this.findOne(id);
    await this.documentRepository.remove(document);
  }

  async generateQRCode(data: string): Promise<string> {
    try {
      // Generate QR code as data URL
      return await QRCode.toDataURL(data);
    } catch (error) {
      throw new Error(`Failed to generate QR code: ${error.message}`);
    }
  }

  async generateTrackingQRCode(
    trackingId: string,
    entityType: string,
  ): Promise<string> {
    // Create tracking URL with entity info
    const trackingData = {
      id: trackingId,
      type: entityType,
      timestamp: new Date().toISOString(),
    };

    // Convert to URL-safe JSON string
    const trackingDataString = JSON.stringify(trackingData);

    // Generate QR code
    return this.generateQRCode(trackingDataString);
  }

  async uploadToObjectStorage(options: UploadDocumentOptions): Promise<string> {
    try {
      const { content, fileName, contentType, folder = '' } = options;

      // Create a path with optional folder
      const path = folder ? `${folder}/${fileName}` : fileName;

      // Upload to S3
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: path,
        Body: content,
        ContentType: contentType,
      });

      await this.s3Client.send(command);

      // Return the full path to the uploaded file
      return path;
    } catch (error) {
      console.error('Error uploading to object storage:', error);
      throw new InternalServerErrorException(
        `Failed to upload document: ${error.message}`,
      );
    }
  }

  async createWithFileUpload(
    createDocumentDto: CreateDocumentDto,
    fileContent: Buffer,
    contentType: string,
    account: Account,
  ): Promise<Document> {
    try {
      // Generate a unique filename
      const timestamp = new Date().getTime();
      const fileName = `${createDocumentDto.category || 'document'}-${timestamp}-${createDocumentDto.name.replace(/\s+/g, '-').toLowerCase()}.pdf`;

      // Upload the file
      const path = await this.uploadToObjectStorage({
        content: fileContent,
        fileName,
        contentType,
        folder: createDocumentDto.category,
      });

      // Create document record with the path
      return this.create(
        {
          ...createDocumentDto,
          path,
        },
        account,
      );
    } catch (error) {
      throw new InternalServerErrorException(
        `Failed to create document with file upload: ${error.message}`,
      );
    }
  }
}
