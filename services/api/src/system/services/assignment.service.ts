import { Injectable, NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Assignment } from '../entities/assignment.entity';
import { Account } from '../entities/account.entity';
import { LogsService } from './logs.service';
import { Status } from '../../common/enums/status.enum';

@Injectable()
export class AssignmentService {
  constructor(
    @InjectRepository(Assignment)
    private assignmentRepository: Repository<Assignment>,
    private logsService: LogsService,
  ) {}

  async create(createAssignmentDto: any, account: Account): Promise<Assignment> {
    try {
      const assignment = this.assignmentRepository.create({
        ...createAssignmentDto,
        account,
      });

      const savedAssignment = await this.assignmentRepository.save(assignment);

      // Log the assignment creation
      await this.logsService.create({
        event: 'Assignment Created',
        message: `Assignment "${createAssignmentDto.name}" created`,
        associatedTable: 'assignments',
        associatedId: savedAssignment[0].id,
      }, account);

      return savedAssignment[0];
    } catch (error) {
      throw new InternalServerErrorException('Failed to create assignment');
    }
  }

  async findAll(options: { 
    skip: number; 
    take: number;
    status?: string;
  }): Promise<{ assignments: Assignment[]; total: number }> {
    const { skip, take, status } = options;

    const queryBuilder = this.assignmentRepository
      .createQueryBuilder('assignment')
      .leftJoinAndSelect('assignment.account', 'account')
      .leftJoinAndSelect('account.user', 'user')
      .skip(skip)
      .take(take)
      .orderBy('assignment.created_at', 'DESC');

    if (status) {
      queryBuilder.andWhere('assignment.status = :status', { status });
    }

    const [assignments, total] = await queryBuilder.getManyAndCount();

    return { assignments, total };
  }

  async findByEntity(
    table: string,
    id: string,
    options: { skip: number; take: number },
  ): Promise<{ assignments: Assignment[]; total: number }> {
    const { skip, take } = options;

    const queryBuilder = this.assignmentRepository
      .createQueryBuilder('assignment')
      .leftJoinAndSelect('assignment.account', 'account')
      .leftJoinAndSelect('account.user', 'user')
      .where('assignment.associatedTable = :table', { table })
      .andWhere('assignment.associatedId = :id', { id })
      .skip(skip)
      .take(take)
      .orderBy('assignment.created_at', 'DESC');

    const [assignments, total] = await queryBuilder.getManyAndCount();

    return { assignments, total };
  }

  async findOne(id: string): Promise<Assignment> {
    const assignment = await this.assignmentRepository.findOne({
      where: { id },
      relations: ['account', 'account.user'],
    });

    if (!assignment) {
      throw new NotFoundException(`Assignment with ID ${id} not found`);
    }

    return assignment;
  }

  async update(id: string, updateAssignmentDto: any, account: Account): Promise<Assignment> {
    const assignment = await this.findOne(id);

    try {
      // Update assignment data
      Object.assign(assignment, updateAssignmentDto);

      const updatedAssignment = await this.assignmentRepository.save(assignment);

      // Log the update
      await this.logsService.create({
        event: 'Assignment Updated',
        message: `Assignment "${assignment.name}" updated`,
        associatedTable: 'assignments',
        associatedId: assignment.id,
      }, account);

      return updatedAssignment;
    } catch (error) {
      throw new InternalServerErrorException('Failed to update assignment');
    }
  }

  async remove(id: string, account: Account): Promise<void> {
    const assignment = await this.findOne(id);

    try {
      // Don't actually delete, just mark as cancelled
      assignment.status = Status.CANCELLED;
      await this.assignmentRepository.save(assignment);

      // Log the deletion
      await this.logsService.create({
        event: 'Assignment Cancelled',
        message: `Assignment "${assignment.name}" has been cancelled`,
        associatedTable: 'assignments',
        associatedId: assignment.id,
      }, account);
    } catch (error) {
      throw new InternalServerErrorException('Failed to cancel assignment');
    }
  }
}