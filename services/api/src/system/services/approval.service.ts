import { Injectable, NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Approval } from '../entities/approval.entity';
import { Account } from '../entities/account.entity';
import { LogsService } from './logs.service';
import { Status } from '../../common/enums/status.enum';

@Injectable()
export class ApprovalService {
  constructor(
    @InjectRepository(Approval)
    private approvalRepository: Repository<Approval>,
    private logsService: LogsService,
  ) {}

  async create(createApprovalDto: any, account: Account): Promise<Approval> {
    try {
      const approval = this.approvalRepository.create({
        ...createApprovalDto,
        account,
      });

      // Use save() and explicitly type the return as a single entity
      const savedApproval = await this.approvalRepository.save(approval);

      // Log the approval creation
      await this.logsService.create({
        event: 'Approval Created',
        message: `Approval record created for ${createApprovalDto.associatedTable} (${createApprovalDto.associatedId})`,
        associatedTable: 'approvals',
        associatedId: savedApproval[0].id,
      }, account);

      return savedApproval[0];
    } catch (error) {
      throw new InternalServerErrorException('Failed to create approval');
    }
  }

  async findAll(options: { 
    skip: number; 
    take: number;
    status?: string;
    associatedTable?: string;
    associatedId?: string;
  }): Promise<{ approvals: Approval[]; total: number }> {
    const { skip, take, status, associatedTable, associatedId } = options;

    const queryBuilder = this.approvalRepository
      .createQueryBuilder('approval')
      .leftJoinAndSelect('approval.account', 'account')
      .leftJoinAndSelect('account.user', 'user')
      .skip(skip)
      .take(take)
      .orderBy('approval.created_at', 'DESC');

    if (status) {
      queryBuilder.andWhere('approval.status = :status', { status });
    }

    if (associatedTable) {
      queryBuilder.andWhere('approval.associatedTable = :associatedTable', { associatedTable });
    }

    if (associatedId) {
      queryBuilder.andWhere('approval.associatedId = :associatedId', { associatedId });
    }

    const [approvals, total] = await queryBuilder.getManyAndCount();

    return { approvals, total };
  }

  async findOne(id: string): Promise<Approval> {
    const approval = await this.approvalRepository.findOne({
      where: { id },
      relations: ['account', 'account.user'],
    });

    if (!approval) {
      throw new NotFoundException(`Approval with ID ${id} not found`);
    }

    return approval;
  }

  async update(id: string, updateApprovalDto: any, account: Account): Promise<Approval> {
    const approval = await this.findOne(id);

    try {
      // Check if status is changing
      const statusChanged = updateApprovalDto.status && updateApprovalDto.status !== approval.status;
      const oldStatus = approval.status;

      // Update approval data
      Object.assign(approval, updateApprovalDto);

      const updatedApproval = await this.approvalRepository.save(approval);

      // Log the update
      await this.logsService.create({
        event: 'Approval Updated',
        message: `Approval record updated${statusChanged ? ` - Status changed from ${oldStatus} to ${approval.status}` : ''}`,
        associatedTable: 'approvals',
        associatedId: approval.id,
      }, account);

      return updatedApproval;
    } catch (error) {
      throw new InternalServerErrorException('Failed to update approval');
    }
  }

  async remove(id: string, account: Account): Promise<void> {
    const approval = await this.findOne(id);

    try {
      await this.approvalRepository.remove(approval);

      // Log the deletion
      await this.logsService.create({
        event: 'Approval Deleted',
        message: `Approval record deleted`,
        associatedTable: 'approvals',
        associatedId: id,
      }, account);
    } catch (error) {
      throw new InternalServerErrorException('Failed to delete approval');
    }
  }
}