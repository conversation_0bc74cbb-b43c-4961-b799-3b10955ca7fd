import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcryptjs';

import { User } from '../entities/user.entity';
import { Account } from '../entities/account.entity';
import { Role } from '../entities/role.entity';
import { CreateUserDto } from '../dto/create-user.dto';
import { UpdateUserDto } from '../dto/update-user.dto';
import { LogsService } from './logs.service';
import { Status } from '../../common/enums/status.enum';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Account)
    private accountRepository: Repository<Account>,
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
    private logsService: LogsService,
  ) {}

  async create(createUserDto: CreateUserDto, currentAccount: Account): Promise<User> {
    const {
      name,
      email,
      password,
      phone,
      location,
      avatar,
      status,
      roleId,
    } = createUserDto;

    // Check if email already exists
    const existingAccount = await this.accountRepository.findOne({
      where: { email },
    });

    if (existingAccount) {
      throw new ConflictException('Email already exists');
    }

    try {
      // Create user
      const user = new User();
      user.name = name;
      user.phone = phone ?? '';
      user.location = location ?? '';
      user.avatar = avatar ?? '';
      user.status = status ?? Status.ACTIVE;

      const savedUser = await this.userRepository.save(user);

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 10);

      // Create account
      const account = new Account();
      account.email = email;
      account.password = hashedPassword;
      account.status = status ?? Status.ACTIVE;
      account.user = savedUser;

      // Assign role if provided
      if (roleId) {
        const role = await this.roleRepository.findOne({ where: { id: roleId } });
        if (!role) {
          throw new NotFoundException(`Role with ID ${roleId} not found`);
        }
        account.role = role;
      }

      await this.accountRepository.save(account);

      // Log the action
      await this.logsService.create({
        event: 'User Created',
        message: `User ${name} (${email}) was created`,
        associatedTable: 'users',
        associatedId: savedUser.id,
        status: Status.ACTIVE,
      }, currentAccount);

      return savedUser;
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to create user');
    }
  }

  async findAll(options: { skip: number; take: number }): Promise<{ users: User[]; total: number }> {
    const [users, total] = await this.userRepository.findAndCount({
      skip: options.skip,
      take: options.take,
      relations: ['account', 'account.role'],
    });

    return { users, total };
  }

  async findOne(id: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['account', 'account.role'],
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    return user;
  }

  async update(id: string, updateUserDto: UpdateUserDto, currentAccount: Account): Promise<User> {
    const {
      name,
      email,
      password,
      phone,
      location,
      avatar,
      status,
      roleId,
    } = updateUserDto;

    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['account'],
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    const account = user.account;
    if (!account) {
      throw new BadRequestException('User has no associated account');
    }

    try {
      // Update user fields
      if (name) user.name = name;
      if (phone) user.phone = phone;
      if (location !== undefined) user.location = location;
      if (avatar !== undefined) user.avatar = avatar;
      if (status) user.status = status;

      // Update account fields
      if (email && email !== account.email) {
        // Check if new email already exists
        const existingAccount = await this.accountRepository.findOne({
          where: { email },
        });

        if (existingAccount && existingAccount.id !== account.id) {
          throw new ConflictException('Email already exists');
        }

        account.email = email;
      }

      if (password) {
        account.password = await bcrypt.hash(password, 10);
      }

      if (status) {
        account.status = status;
      }

      // Update role if provided
      if (roleId) {
        const role = await this.roleRepository.findOne({ where: { id: roleId } });
        if (!role) {
          throw new NotFoundException(`Role with ID ${roleId} not found`);
        }
        account.role = role;
      }

      await this.userRepository.save(user);
      await this.accountRepository.save(account);

      // Log the action
      await this.logsService.create({
        event: 'User Updated',
        message: `User ${user.name} was updated`,
        associatedTable: 'users',
        associatedId: user.id,
        status: Status.ACTIVE,
      }, currentAccount);

      return this.findOne(id);
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to update user');
    }
  }

  async remove(id: string, currentAccount: Account): Promise<void> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['account'],
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    // Don't actually delete, just mark as inactive
    user.status = Status.INACTIVE;
    
    if (user.account) {
      user.account.status = Status.INACTIVE;
      await this.accountRepository.save(user.account);
    }
    
    await this.userRepository.save(user);

    // Log the action
    await this.logsService.create({
      event: 'User Deactivated',
      message: `User ${user.name} was deactivated`,
      associatedTable: 'users',
      associatedId: user.id,
      status: Status.ACTIVE,
    }, currentAccount);
  }

  async updatePassword(id: string, password: string): Promise<void> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['account'],
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    const account = user.account;
    if (!account) {
      throw new BadRequestException('User has no associated account');
    }

    try {
      account.password = await bcrypt.hash(password, 10);
      await this.accountRepository.save(account);

      // Log the action
      await this.logsService.create({
        event: 'User Password Updated',
        message: `User ${user.name} password was updated`,
        associatedTable: 'users',
        associatedId: user.id,
        status: Status.ACTIVE,
      }, account);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to update user password');
    }
  }
}