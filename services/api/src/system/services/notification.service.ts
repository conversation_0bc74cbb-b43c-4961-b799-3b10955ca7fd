import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Notification } from '../entities/notification.entity';
import { Account } from '../entities/account.entity';
import { WebsocketGateway } from '../../websocket/websocket.gateway';
import { Status } from '../../common/enums/status.enum';

interface CreateNotificationDto {
  name: string;
  message: string;
  details?: any;
  status?: string;
  associatedTable?: string;
  associatedId?: string;
}

@Injectable()
export class NotificationService {
  constructor(
    @InjectRepository(Notification)
    private notificationRepository: Repository<Notification>,
    private websocketGateway: WebsocketGateway,
  ) {}

  async create(createNotificationDto: CreateNotificationDto, account: Account): Promise<Notification> {
    const notification = new Notification();
    notification.name = createNotificationDto.name;
    notification.message = createNotificationDto.message;
    notification.details = createNotificationDto.details;
    notification.status = createNotificationDto.status as Status || Status.ACTIVE;
    notification.associatedTable = createNotificationDto.associatedTable || '';
    notification.associatedId = createNotificationDto.associatedId || '';
    notification.account = account;

    const savedNotification = await this.notificationRepository.save(notification);

    // Emit websocket event if there's an associated entity for specific users
    if (createNotificationDto.associatedTable && createNotificationDto.associatedId) {
      this.websocketGateway.emitToAll('notification', {
        id: savedNotification.id,
        name: savedNotification.name,
        message: savedNotification.message,
        associatedTable: savedNotification.associatedTable,
        associatedId: savedNotification.associatedId,
        timestamp: savedNotification.created_at,
      });
    }

    return savedNotification;
  }

  async findAll(options: { skip: number; take: number }): Promise<{ notifications: Notification[]; total: number }> {
    const [notifications, total] = await this.notificationRepository.findAndCount({
      skip: options.skip,
      take: options.take,
      relations: ['account', 'account.user'],
      order: { created_at: 'DESC' },
    });

    return { notifications, total };
  }

  async findByEntity(
    table: string,
    id: string,
    options: { skip: number; take: number },
  ): Promise<{ notifications: Notification[]; total: number }> {
    const [notifications, total] = await this.notificationRepository.findAndCount({
      where: {
        associatedTable: table,
        associatedId: id,
      },
      skip: options.skip,
      take: options.take,
      relations: ['account', 'account.user'],
      order: { created_at: 'DESC' },
    });

    return { notifications, total };
  }

  async markAsRead(id: string): Promise<Notification> {
    const notification = await this.notificationRepository.findOne({
      where: { id },
    });

    if (!notification) {
      throw new Error(`Notification with ID ${id} not found`);
    }

    notification.status = Status.INACTIVE;
    return this.notificationRepository.save(notification);
  }

  async deleteNotification(id: string): Promise<void> {
    await this.notificationRepository.delete(id);
  }
}