import { Injectable, NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Role } from '../entities/role.entity';
import { Department } from '../entities/department.entity';
import { Account } from '../entities/account.entity';
import { LogsService } from './logs.service';
import { Status } from '../../common/enums/status.enum';

@Injectable()
export class RoleService {
  constructor(
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
    @InjectRepository(Department)
    private departmentRepository: Repository<Department>,
    private logsService: LogsService,
  ) {}

  async create(createRoleDto: any, account: Account): Promise<Role> {
    try {
      const { departmentId, ...roleData } = createRoleDto;

      // Find department
      const department = await this.departmentRepository.findOne({
        where: { id: departmentId },
      });

      if (!department) {
        throw new NotFoundException(`Department with ID ${departmentId} not found`);
      }

      // Create role
      const role = this.roleRepository.create({
        ...roleData,
        department,
      });

      const savedRole = await this.roleRepository.save(role);

      // Log the role creation
      await this.logsService.create({
        event: 'Role Created',
        message: `Role "${createRoleDto.name}" created for department "${department.name}"`,
        associatedTable: 'roles',
        associatedId: savedRole[0].id,
      }, account);

      return savedRole[0];
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to create role');
    }
  }

  async findAll(options: { 
    skip: number; 
    take: number;
    departmentId?: string;
  }): Promise<{ roles: Role[]; total: number }> {
    const { skip, take, departmentId } = options;

    const queryBuilder = this.roleRepository
      .createQueryBuilder('role')
      .leftJoinAndSelect('role.department', 'department')
      .skip(skip)
      .take(take)
      .orderBy('role.name', 'ASC');

    if (departmentId) {
      queryBuilder.andWhere('department.id = :departmentId', { departmentId });
    }

    const [roles, total] = await queryBuilder.getManyAndCount();

    return { roles, total };
  }

  async findOne(id: string): Promise<Role> {
    const role = await this.roleRepository.findOne({
      where: { id },
      relations: ['department', 'accounts'],
    });

    if (!role) {
      throw new NotFoundException(`Role with ID ${id} not found`);
    }

    return role;
  }

  async update(id: string, updateRoleDto: any, account: Account): Promise<Role> {
    const role = await this.findOne(id);

    try {
      const { departmentId, ...roleData } = updateRoleDto;

      // Update department if provided
      if (departmentId && departmentId !== role.department?.id) {
        const department = await this.departmentRepository.findOne({
          where: { id: departmentId },
        });

        if (!department) {
          throw new NotFoundException(`Department with ID ${departmentId} not found`);
        }

        role.department = department;
      }

      // Update role data
      Object.assign(role, roleData);

      const updatedRole = await this.roleRepository.save(role);

      // Log the update
      await this.logsService.create({
        event: 'Role Updated',
        message: `Role "${role.name}" updated`,
        associatedTable: 'roles',
        associatedId: role.id,
      }, account);

      return updatedRole;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to update role');
    }
  }

  async remove(id: string, account: Account): Promise<void> {
    const role = await this.findOne(id);

    try {
      // Don't actually delete, just mark as inactive
      role.status = Status.INACTIVE;
      await this.roleRepository.save(role);

      // Log the deletion
      await this.logsService.create({
        event: 'Role Deactivated',
        message: `Role "${role.name}" has been deactivated`,
        associatedTable: 'roles',
        associatedId: role.id,
      }, account);
    } catch (error) {
      throw new InternalServerErrorException('Failed to deactivate role');
    }
  }
}