import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Logs } from '../entities/logs.entity';
import { Account } from '../entities/account.entity';
import { Status } from 'src/common/enums/status.enum';

interface CreateLogDto {
  event: string;
  message: string;
  status?: string;
  associatedTable?: string;
  associatedId?: string;
}

@Injectable()
export class LogsService {
  constructor(
    @InjectRepository(Logs)
    private logsRepository: Repository<Logs>,
  ) {}

  async create(createLogDto: CreateLogDto, account: Account): Promise<Logs> {
    const log = new Logs();
    log.event = createLogDto.event;
    log.message = createLogDto.message;
    log.status = (createLogDto.status || 'active') as Status;
    log.associatedTable = createLogDto.associatedTable || '';
    log.associatedId = createLogDto.associatedId || '';
    log.account = account;

    return this.logsRepository.save(log);
  }

  async findAll(options: { skip: number; take: number }): Promise<{ logs: Logs[]; total: number }> {
    const [logs, total] = await this.logsRepository.findAndCount({
      skip: options.skip,
      take: options.take,
      relations: ['account', 'account.user'],
      order: { created_at: 'DESC' },
    });

    return { logs, total };
  }

  async findByEntity(
    table: string,
    id: string,
    options: { skip: number; take: number },
  ): Promise<{ logs: Logs[]; total: number }> {
    const [logs, total] = await this.logsRepository.findAndCount({
      where: {
        associatedTable: table,
        associatedId: id,
      },
      skip: options.skip,
      take: options.take,
      relations: ['account', 'account.user'],
      order: { created_at: 'DESC' },
    });

    return { logs, total };
  }
}