import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';

/**
 * Standalone script for generating Swagger documentation
 */
async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // Set global prefix for all routes
  app.setGlobalPrefix('api');

  // Enable validation pipes
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }),
  );

  // Setup comprehensive Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('Shamwaa Logistics API')
    .setDescription(
      'Complete API documentation for Shamwaa Logistics Management System',
    )
    .setVersion('1.0')
    .addBearerAuth({
      type: 'http',
      scheme: 'bearer',
      bearerFormat: 'JWT',
      description: 'Enter JWT token',
      in: 'header',
    })
    .addTag('Auth', 'Authentication endpoints')
    .addTag('Users', 'User management endpoints')
    .addTag('Customers', 'Customer management endpoints')
    .addTag('Cargos', 'Cargo management endpoints')
    .addTag('Batches', 'Batch management endpoints')
    .addTag('Shipments', 'Shipment tracking endpoints')
    .addTag('Handovers', 'Cargo handover endpoints')
    .addTag('Finances', 'Finance management endpoints')
    .addTag('Reports', 'Reporting and analytics endpoints')
    .addTag('System', 'System utilities and settings')
    .addTag('Biometrics', 'Biometric authentication endpoints')
    .addServer(
      `http://localhost:${configService.get('PORT') || 3000}`,
      'Local Development Server',
    )
    .setContact(
      'Shamwaa Support',
      'https://shamwaa.com/support',
      '<EMAIL>',
    )
    .setExternalDoc('Additional Documentation', 'https://shamwaa.com/docs')
    .setLicense('Proprietary', 'https://shamwaa.com/license')
    .build();
  const document = SwaggerModule.createDocument(app, config, {
    deepScanRoutes: true,
    operationIdFactory: (controllerKey: string, methodKey: string) => methodKey,
  });

  SwaggerModule.setup('api/docs', app, document, {
    explorer: true, // Enable searching operations feature
    swaggerOptions: {
      persistAuthorization: true,
      docExpansion: 'none',
      filter: true,
      showExtensions: true,
      showCommonExtensions: true,
      displayRequestDuration: true,
    },
    customSiteTitle: 'Shamwaa API Documentation',
  });

  // Configure app port with fallback
  const port = configService.get<number>('PORT', 3000);
  await app.listen(port);
}

bootstrap();
