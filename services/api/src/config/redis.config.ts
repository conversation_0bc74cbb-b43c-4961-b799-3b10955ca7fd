import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';

@Injectable()
export class RedisConfig {
  constructor(private configService: ConfigService) {}

  createRedisClient(): Redis {
    return new Redis({
      host: this.configService.get<string>('REDIS_HOST', 'localhost'),
      port: this.configService.get<number>('REDIS_PORT', 6379),
      password: this.configService.get<string>('REDIS_PASSWORD', ''),
      username: this.configService.get<string>('REDIS_USERNAME', ''),
      db: this.configService.get<number>('REDIS_DB', 0),
      keyPrefix: this.configService.get<string>('REDIS_KEY_PREFIX', 'shamwaa:'),
      retryStrategy: (times) => {
        // Retry with exponential backoff
        const delay = Math.min(times * 100, 3000);
        return delay;
      },
      // Add connection management for Kubernetes environment
      enableReadyCheck: true,
      maxRetriesPerRequest: 3,
      reconnectOnError: (err) => {
        const targetError = 'READONLY';
        if (err.message.includes(targetError)) {
          // Only reconnect on READONLY error (when Redis is failing over)
          return true;
        }
        return false;
      },
    });
  }
}