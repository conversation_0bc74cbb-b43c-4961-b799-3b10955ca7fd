import { Injectable, NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Shipment } from '../entities/shipment.entity';
import { Freight } from '../entities/freight.entity';
import { Batch } from '../entities/batch.entity';
import { Cargo } from '../entities/cargo.entity';
import { Account } from '../../system/entities/account.entity';
import { CreateShipmentDto } from '../dto/create-shipment.dto';
import { UpdateShipmentDto } from '../dto/update-shipment.dto';
import { LogsService } from '../../system/services/logs.service';
import { NotificationService } from '../../system/services/notification.service';
import { WebsocketGateway } from '../../websocket/websocket.gateway';
import { Status } from '../../common/enums/status.enum';
import { TrackingType } from '../../common/enums/tracking.enum';

@Injectable()
export class ShipmentService {
  constructor(
    @InjectRepository(Shipment)
    private shipmentRepository: Repository<Shipment>,
    @InjectRepository(Freight)
    private freightRepository: Repository<Freight>,
    @InjectRepository(Batch)
    private batchRepository: Repository<Batch>,
    @InjectRepository(Cargo)
    private cargoRepository: Repository<Cargo>,
    private logsService: LogsService,
    private notificationService: NotificationService,
    private websocketGateway: WebsocketGateway,
  ) {}

  async create(createShipmentDto: CreateShipmentDto, account: Account): Promise<Shipment> {
    try {
      const { freightId, batchId, ...shipmentData } = createShipmentDto;

      // Find freight
      const freight = await this.freightRepository.findOne({
        where: { id: freightId },
      });

      if (!freight) {
        throw new NotFoundException(`Freight with ID ${freightId} not found`);
      }

      // Find batch
      const batch = await this.batchRepository.findOne({
        where: { id: batchId },
      });

      if (!batch) {
        throw new NotFoundException(`Batch with ID ${batchId} not found`);
      }

      // Create shipment
      const shipment = this.shipmentRepository.create({
        ...shipmentData,
        freight,
        batch,
        account,
      });

      const savedShipment = await this.shipmentRepository.save(shipment);

      // Log the shipment creation
      await this.logsService.create({
        event: 'Shipment Created',
        message: `Shipment with tracking number ${createShipmentDto.trackingNumber} created`,
        associatedTable: 'shipments',
        associatedId: savedShipment.id,
      }, account);

      // Update batch status if it's the first shipment
      const shipmentCount = await this.shipmentRepository.count({
        where: { batch: { id: batch.id } },
      });

      if (shipmentCount === 1 && batch.status === Status.CREATED) {
        batch.status = Status.IN_TRANSIT;
        await this.batchRepository.save(batch);

        // Log batch status change
        await this.logsService.create({
          event: 'Batch Status Updated',
          message: `Batch ${batch.code} status updated to ${Status.IN_TRANSIT}`,
          associatedTable: 'batches',
          associatedId: batch.id,
        }, account);

        // Update related cargos to in_transit status
        await this.cargoRepository.update(
          { batch: { id: batch.id }, status: Status.CREATED },
          { status: Status.IN_TRANSIT }
        );

        // Emit batch status change event
        this.websocketGateway.emitToBatch(batch.id, 'batch_status_changed', {
          id: batch.id,
          code: batch.code,
          oldStatus: Status.CREATED,
          newStatus: Status.IN_TRANSIT,
        });

        // Notify all customers with cargo in this batch
        const cargos = await this.cargoRepository.find({
          where: { batch: { id: batch.id } },
          relations: ['customer'],
        });

        const customerIds = [...new Set(cargos.map(cargo => cargo.customer.id))];
        
        for (const customerId of customerIds) {
          // Create notification
          await this.notificationService.create({
            name: 'Shipment Started',
            message: `Your cargo shipment is now in transit with tracking number ${shipment.trackingNumber}`,
            associatedTable: 'shipments',
            associatedId: savedShipment.id,
          }, account);

          // Emit customer event
          this.websocketGateway.emitToUser(customerId, 'cargo_in_transit', {
            shipmentId: savedShipment.id,
            trackingNumber: savedShipment.trackingNumber,
          });
        }
      }

      // Emit websocket event
      this.websocketGateway.emitToShipment(savedShipment.id, 'shipment_created', {
        id: savedShipment.id,
        trackingNumber: savedShipment.trackingNumber,
        trackingNumberType: savedShipment.trackingNumberType,
      });

      return savedShipment;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to create shipment');
    }
  }

  async findAll(options: {
    skip: number;
    take: number;
    freightId?: string;
    batchId?: string;
  }): Promise<{ shipments: Shipment[]; total: number }> {
    const { skip, take, freightId, batchId } = options;

    const queryBuilder = this.shipmentRepository.createQueryBuilder('shipment')
      .leftJoinAndSelect('shipment.freight', 'freight')
      .leftJoinAndSelect('shipment.batch', 'batch')
      .leftJoinAndSelect('shipment.account', 'account')
      .leftJoinAndSelect('account.user', 'user');

    if (freightId) {
      queryBuilder.andWhere('shipment.freight.id = :freightId', { freightId });
    }

    if (batchId) {
      queryBuilder.andWhere('shipment.batch.id = :batchId', { batchId });
    }

    queryBuilder.skip(skip).take(take);

    const [shipments, total] = await queryBuilder.getManyAndCount();

    return { shipments, total };
  }

  async findOne(id: string): Promise<Shipment> {
    const shipment = await this.shipmentRepository.findOne({
      where: { id },
      relations: ['freight', 'batch', 'account', 'account.user'],
    });

    if (!shipment) {
      throw new NotFoundException(`Shipment with ID ${id} not found`);
    }

    return shipment;
  }

  async findByTrackingNumber(trackingNumber: string): Promise<Shipment> {
    const shipment = await this.shipmentRepository.findOne({
      where: { trackingNumber },
      relations: ['freight', 'batch', 'account', 'account.user'],
    });

    if (!shipment) {
      throw new NotFoundException(`Shipment with tracking number ${trackingNumber} not found`);
    }

    return shipment;
  }

  async update(id: string, updateShipmentDto: UpdateShipmentDto, account: Account): Promise<Shipment> {
    const shipment = await this.findOne(id);

    try {
      const { freightId, batchId, ...shipmentData } = updateShipmentDto;

      // Update freight if provided
      if (freightId && freightId !== shipment.freight.id) {
        const freight = await this.freightRepository.findOne({
          where: { id: freightId },
        });

        if (!freight) {
          throw new NotFoundException(`Freight with ID ${freightId} not found`);
        }

        shipment.freight = freight;
      }

      // Update batch if provided
      if (batchId && batchId !== shipment.batch.id) {
        const batch = await this.batchRepository.findOne({
          where: { id: batchId },
        });

        if (!batch) {
          throw new NotFoundException(`Batch with ID ${batchId} not found`);
        }

        shipment.batch = batch;
      }

      // Check for ETA updates to notify customers
      const etaChanged = updateShipmentDto.estimatedTimeOfArrival && 
                        updateShipmentDto.estimatedTimeOfArrival.toString() !== 
                        shipment.estimatedTimeOfArrival?.toString();

      // Check for arrival update
      const arrivalUpdated = updateShipmentDto.arrival && !shipment.arrival;

      // Update shipment
      Object.assign(shipment, shipmentData);

      const updatedShipment = await this.shipmentRepository.save(shipment);

      // Log the update
      await this.logsService.create({
        event: 'Shipment Updated',
        message: `Shipment with tracking number ${shipment.trackingNumber} updated`,
        associatedTable: 'shipments',
        associatedId: shipment.id,
      }, account);

      // Handle special case: Shipment Arrived
      if (arrivalUpdated) {
        // Update batch status to delivered if not already
        const batch = await this.batchRepository.findOne({
          where: { id: shipment.batch.id },
        });

        if (batch && batch.status !== Status.DELIVERED) {
          batch.status = Status.DELIVERED;
          await this.batchRepository.save(batch);

          // Log batch status change
          await this.logsService.create({
            event: 'Batch Status Updated',
            message: `Batch ${batch.code} status updated to ${Status.DELIVERED}`,
            associatedTable: 'batches',
            associatedId: batch.id,
          }, account);

          // Update related cargos to delivered status
          await this.cargoRepository.update(
            { batch: { id: batch.id }, status: Status.IN_TRANSIT },
            { status: Status.DELIVERED }
          );

          // Emit batch status change event
          this.websocketGateway.emitToBatch(batch.id, 'batch_status_changed', {
            id: batch.id,
            code: batch.code,
            oldStatus: Status.IN_TRANSIT,
            newStatus: Status.DELIVERED,
          });

          // Notify all customers with cargo in this batch
          const cargos = await this.cargoRepository.find({
            where: { batch: { id: batch.id } },
            relations: ['customer'],
          });

          const customerIds = [...new Set(cargos.map(cargo => cargo.customer.id))];
          
          for (const customerId of customerIds) {
            // Create notification
            await this.notificationService.create({
              name: 'Shipment Arrived',
              message: `Your cargo has arrived at its destination`,
              associatedTable: 'shipments',
              associatedId: updatedShipment.id,
            }, account);

            // Emit customer event
            this.websocketGateway.emitToUser(customerId, 'cargo_arrived', {
              shipmentId: updatedShipment.id,
              trackingNumber: updatedShipment.trackingNumber,
            });
          }
        }
      }

      // Handle ETA changes - notify customers
      if (etaChanged) {
        // Notify all customers with cargo in this batch
        const cargos = await this.cargoRepository.find({
          where: { batch: { id: shipment.batch.id } },
          relations: ['customer'],
        });

        const customerIds = [...new Set(cargos.map(cargo => cargo.customer.id))];
        
        for (const customerId of customerIds) {
          // Create notification
          await this.notificationService.create({
            name: 'ETA Updated',
            message: `Estimated arrival time for your shipment has been updated`,
            associatedTable: 'shipments',
            associatedId: updatedShipment.id,
          }, account);

          // Emit customer event
          this.websocketGateway.emitToUser(customerId, 'shipment_eta_updated', {
            shipmentId: updatedShipment.id,
            trackingNumber: updatedShipment.trackingNumber,
            eta: updatedShipment.estimatedTimeOfArrival,
          });
        }
      }

      // Emit websocket event
      this.websocketGateway.emitToShipment(updatedShipment.id, 'shipment_updated', {
        id: updatedShipment.id,
        trackingNumber: updatedShipment.trackingNumber,
      });

      return updatedShipment;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to update shipment');
    }
  }

  async remove(id: string, account: Account): Promise<void> {
    const shipment = await this.findOne(id);

    try {
      await this.shipmentRepository.remove(shipment);

      // Log the deletion
      await this.logsService.create({
        event: 'Shipment Deleted',
        message: `Shipment with tracking number ${shipment.trackingNumber} deleted`,
        associatedTable: 'shipments',
        associatedId: id,
      }, account);

      // Emit websocket event
      this.websocketGateway.emitToAll('shipment_deleted', {
        id,
        trackingNumber: shipment.trackingNumber,
      });
    } catch (error) {
      throw new InternalServerErrorException('Failed to delete shipment');
    }
  }

  async createShipmentForCargo(cargo: Cargo, account: Account): Promise<Shipment> {
    if (!cargo.batch) {
      throw new InternalServerErrorException('Cargo must be assigned to a batch to create a shipment');
    }

    // Check if shipment already exists for this batch
    const existingShipment = await this.shipmentRepository.findOne({
      where: { batch: { id: cargo.batch.id } },
    });

    if (existingShipment) {
      return existingShipment;
    }

    // Generate tracking number
    const trackingNumber = this.generateTrackingNumber(cargo.batch);

    // Create shipment
    const shipment = this.shipmentRepository.create({
      trackingNumberType: cargo.batch.type === 'container' ? TrackingType.BL : TrackingType.AWB,
      trackingNumber,
      freight: cargo.batch.freight,
      batch: cargo.batch,
      account,
      estimatedTimeOfArrival: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
    });

    return await this.shipmentRepository.save(shipment);
  }

  private generateTrackingNumber(batch: Batch): string {
    const prefix = batch.freight.type === 'air' ? 'AIR' : 'SEA';
    const locationCode = 'G'; // Placeholder for location code
    const year = new Date().getFullYear().toString().slice(-2);
    const month = ('0' + (new Date().getMonth() + 1)).slice(-2);
    const index = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    const weightCategory = batch.weight > 1000 ? 'H' : 'L'; // Heavy if over 1000 kg
    
    return `${prefix}-${locationCode}-${year}-${month}-${index}-${weightCategory}`;
  }
}