import {
  Injectable,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Batch } from '../entities/batch.entity';
import { Freight } from '../entities/freight.entity';
import { Cargo } from '../entities/cargo.entity';
import { Account } from '../../system/entities/account.entity';
import { CreateBatchDto } from '../dto/create-batch.dto';
import { UpdateBatchDto } from '../dto/update-batch.dto';
import { LogsService } from '../../system/services/logs.service';
import { WebsocketGateway } from '../../websocket/websocket.gateway';

@Injectable()
export class BatchService {
  constructor(
    @InjectRepository(Batch)
    private batchRepository: Repository<Batch>,
    @InjectRepository(Freight)
    private freightRepository: Repository<Freight>,
    @InjectRepository(Cargo)
    private cargoRepository: Repository<Cargo>,
    private logsService: LogsService,
    private websocketGateway: WebsocketGateway,
  ) {}

  async create(
    createBatchDto: CreateBatchDto,
    account: Account,
  ): Promise<Batch> {
    try {
      const { freightId, ...batchData } = createBatchDto;

      // Find freight
      const freight = await this.freightRepository.findOne({
        where: { id: freightId },
      });

      if (!freight) {
        throw new NotFoundException(`Freight with ID ${freightId} not found`);
      }

      // Create batch
      const batch = this.batchRepository.create({
        ...batchData,
        freight,
        account,
      });

      const savedBatch = await this.batchRepository.save(batch);

      // Log the batch creation
      await this.logsService.create(
        {
          event: 'Batch Created',
          message: `Batch ${createBatchDto.name} (${createBatchDto.code}) created for freight ${freight.name}`,
          associatedTable: 'batches',
          associatedId: savedBatch.id,
        },
        account,
      );

      // Emit websocket event
      this.websocketGateway.emitToAll('batch_created', {
        id: savedBatch.id,
        code: savedBatch.code,
        name: savedBatch.name,
        freightId: freight.id,
      });

      return savedBatch;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to create batch');
    }
  }

  async findAll(options: {
    skip: number;
    take: number;
    status?: string;
    freightId?: string;
  }): Promise<{ batches: Batch[]; total: number }> {
    const { skip, take, status, freightId } = options;

    const queryBuilder = this.batchRepository
      .createQueryBuilder('batch')
      .leftJoinAndSelect('batch.freight', 'freight')
      .leftJoinAndSelect('batch.account', 'account')
      .leftJoinAndSelect('account.user', 'user')
      .leftJoinAndSelect('batch.cargos', 'cargos')
      .leftJoinAndSelect('batch.shipments', 'shipments');

    if (status) {
      queryBuilder.andWhere('batch.status = :status', { status });
    }

    if (freightId) {
      queryBuilder.andWhere('batch.freight.id = :freightId', { freightId });
    }

    queryBuilder.skip(skip).take(take);

    const [batches, total] = await queryBuilder.getManyAndCount();

    return { batches, total };
  }

  async findOne(id: string): Promise<Batch> {
    const batch = await this.batchRepository.findOne({
      where: { id },
      relations: ['freight', 'account', 'account.user', 'cargos', 'shipments'],
    });

    if (!batch) {
      throw new NotFoundException(`Batch with ID ${id} not found`);
    }

    return batch;
  }

  async update(
    id: string,
    updateBatchDto: UpdateBatchDto,
    account: Account,
  ): Promise<Batch> {
    const batch = await this.findOne(id);

    try {
      const { freightId, ...batchData } = updateBatchDto;

      // Update freight if provided
      if (freightId && freightId !== batch.freight.id) {
        const freight = await this.freightRepository.findOne({
          where: { id: freightId },
        });

        if (!freight) {
          throw new NotFoundException(`Freight with ID ${freightId} not found`);
        }

        batch.freight = freight;
      }

      // Check if status is changing
      const statusChanged =
        updateBatchDto.status && updateBatchDto.status !== batch.status;
      const oldStatus = batch.status;

      // Update batch
      Object.assign(batch, batchData);

      const updatedBatch = await this.batchRepository.save(batch);

      // Log the update
      await this.logsService.create(
        {
          event: 'Batch Updated',
          message: `Batch ${batch.name} (${batch.code}) updated${statusChanged ? ` - Status changed from ${oldStatus} to ${batch.status}` : ''}`,
          associatedTable: 'batches',
          associatedId: batch.id,
        },
        account,
      );

      // Emit websocket event if status changed
      if (statusChanged) {
        this.websocketGateway.emitToAll('batch_status_changed', {
          id: updatedBatch.id,
          code: updatedBatch.code,
          oldStatus,
          newStatus: updatedBatch.status,
        });
      }

      return updatedBatch;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to update batch');
    }
  }

  async remove(id: string, account: Account): Promise<void> {
    const batch = await this.findOne(id);

    // Check if batch has cargos
    const cargoCount = await this.cargoRepository.count({
      where: { batch: { id: batch.id } },
    });

    if (cargoCount > 0) {
      throw new InternalServerErrorException(
        'Cannot delete batch with associated cargos',
      );
    }

    try {
      await this.batchRepository.remove(batch);

      // Log the deletion
      await this.logsService.create(
        {
          event: 'Batch Deleted',
          message: `Batch ${batch.name} (${batch.code}) deleted`,
          associatedTable: 'batches',
          associatedId: id,
        },
        account,
      );

      // Emit websocket event
      this.websocketGateway.emitToAll('batch_deleted', {
        id,
        code: batch.code,
      });
    } catch (error) {
      throw new InternalServerErrorException('Failed to delete batch');
    }
  }

  async findBatchCargos(
    batchId: string,
    options: { skip: number; take: number },
  ): Promise<{ cargos: Cargo[]; total: number }> {
    const batch = await this.findOne(batchId);

    const queryBuilder = this.cargoRepository
      .createQueryBuilder('cargo')
      .leftJoinAndSelect('cargo.customer', 'customer')
      .leftJoinAndSelect('cargo.account', 'account')
      .leftJoinAndSelect('account.user', 'user')
      .where('cargo.batch.id = :batchId', { batchId: batch.id })
      .skip(options.skip)
      .take(options.take);

    const [cargos, total] = await queryBuilder.getManyAndCount();

    return { cargos, total };
  }
}
