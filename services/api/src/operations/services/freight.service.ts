import { Injectable, NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Freight } from '../entities/freight.entity';
import { Account } from '../../system/entities/account.entity';
import { CreateFreightDto } from '../dto/create-freight.dto';
import { UpdateFreightDto } from '../dto/update-freight.dto';
import { LogsService } from '../../system/services/logs.service';

@Injectable()
export class FreightService {
  constructor(
    @InjectRepository(Freight)
    private freightRepository: Repository<Freight>,
    private logsService: LogsService,
  ) {}

  async create(createFreightDto: CreateFreightDto, account: Account): Promise<Freight> {
    try {
      const freight = this.freightRepository.create({
        ...createFreightDto,
        account,
      });

      const savedFreight = await this.freightRepository.save(freight);

      // Log the creation
      await this.logsService.create({
        event: 'Freight Created',
        message: `Freight ${createFreightDto.name} (${createFreightDto.code}) created`,
        associatedTable: 'freights',
        associatedId: savedFreight.id,
      }, account);

      return savedFreight;
    } catch (error) {
      throw new InternalServerErrorException('Failed to create freight');
    }
  }

  async findAll(options: {
    skip: number;
    take: number;
    type?: string;
  }): Promise<{ freights: Freight[]; total: number }> {
    const { skip, take, type } = options;

    const queryBuilder = this.freightRepository.createQueryBuilder('freight')
      .leftJoinAndSelect('freight.account', 'account')
      .leftJoinAndSelect('account.user', 'user')
      .leftJoinAndSelect('freight.batches', 'batches')
      .leftJoinAndSelect('freight.shipments', 'shipments');

    if (type) {
      queryBuilder.andWhere('freight.type = :type', { type });
    }

    queryBuilder.skip(skip).take(take);

    const [freights, total] = await queryBuilder.getManyAndCount();

    return { freights, total };
  }

  async findOne(id: string): Promise<Freight> {
    const freight = await this.freightRepository.findOne({
      where: { id },
      relations: ['account', 'account.user', 'batches', 'shipments'],
    });

    if (!freight) {
      throw new NotFoundException(`Freight with ID ${id} not found`);
    }

    return freight;
  }

  async update(id: string, updateFreightDto: UpdateFreightDto, account: Account): Promise<Freight> {
    const freight = await this.findOne(id);

    try {
      // Update freight
      Object.assign(freight, updateFreightDto);

      const updatedFreight = await this.freightRepository.save(freight);

      // Log the update
      await this.logsService.create({
        event: 'Freight Updated',
        message: `Freight ${freight.name} (${freight.code}) updated`,
        associatedTable: 'freights',
        associatedId: freight.id,
      }, account);

      return updatedFreight;
    } catch (error) {
      throw new InternalServerErrorException('Failed to update freight');
    }
  }

  async remove(id: string, account: Account): Promise<void> {
    const freight = await this.findOne(id);

    try {
      await this.freightRepository.remove(freight);

      // Log the deletion
      await this.logsService.create({
        event: 'Freight Deleted',
        message: `Freight ${freight.name} (${freight.code}) deleted`,
        associatedTable: 'freights',
        associatedId: id,
      }, account);
    } catch (error) {
      throw new InternalServerErrorException('Failed to delete freight');
    }
  }
}