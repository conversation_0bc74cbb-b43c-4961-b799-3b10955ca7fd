import { Injectable } from '@nestjs/common';

/**
 * Centralized Code Generator Service
 *
 * This service consolidates all tracking number, code generation functions
 * from across the application into a single, reusable service.
 *
 * Migrated from:
 * - apps/admin/lib/tracking-generator.ts
 * - apps/admin/lib/logistics/operations/shipments.ts
 * - apps/admin/lib/logistics/operations/cargos.ts
 * - apps/admin/app/(app)/release-authorization/
 * - apps/admin/app/(app)/cargo-management/
 * - apps/admin/app/(app)/invoice-workflow/
 * - apps/admin/lib/invoice-template-generator.ts
 * - services/api/src/operations/services/shipment.service.ts
 * - services/api/src/operations/services/cargo.service.ts
 */

// Location codes mapping for worldwide shipping locations
const LOCATION_CODES: Record<string, string> = {
  guangzhou: 'G',
  shanghai: 'S',
  beijing: 'B',
  shenzhen: 'SZ',
  'hong kong': 'HK',
  singapore: 'SG',
  dubai: 'D',
  london: 'L',
  'new york': 'NY',
  'los angeles': 'LA',
  mumbai: 'M',
  karachi: 'K',
  'dar es salaam': 'DSM',
  nairobi: 'NBO',
  lagos: 'LOS',
  default: 'G', // Default to Guangzhou
};

// Shipping mode prefixes
const SHIPPING_MODES: Record<string, string> = {
  AIR: 'AIR',
  SEA: 'SEA',
  ROAD: 'ROAD',
  LAND: 'LAND',
};

// Weight categories
const WEIGHT_CATEGORIES = {
  LIGHT: 'L', // < 100kg
  HEAVY: 'H', // >= 100kg
};

// Cargo categories for tracking prefixes
const CARGO_CATEGORIES = {
  DANGEROUS: 'DGR',
  REGULAR: 'REG',
  SHAMWAA: 'SHAMWAA',
};

export interface TrackingNumberParams {
  shippingMode: 'AIR' | 'SEA' | 'ROAD' | 'LAND';
  origin: string;
  weight?: number; // in kg
  batchIndex?: number; // If provided, use this index, otherwise generate
}

export interface BatchCodeParams {
  freightName: string;
  shippingMode: string;
  origin: string;
}

export interface CargoTrackingParams {
  category?: 'dangerous' | 'regular' | 'shamwaa';
  customPrefix?: string;
}

export interface InvoiceNumberParams {
  prefix?: string;
  customSuffix?: string;
}

export interface DocumentNumberParams {
  prefix?: string;
  includeDate?: boolean;
}

@Injectable()
export class CodeGeneratorService {
  /**
   * Extracts location code from origin string
   */
  private getLocationCode(origin: string): string {
    const normalizedOrigin = origin.toLowerCase().trim();

    // Check for exact matches first
    if (LOCATION_CODES[normalizedOrigin]) {
      return LOCATION_CODES[normalizedOrigin];
    }

    // Check for partial matches
    for (const [location, code] of Object.entries(LOCATION_CODES)) {
      if (normalizedOrigin.includes(location) && location !== 'default') {
        return code;
      }
    }

    // Default fallback
    return LOCATION_CODES.default;
  }

  /**
   * Determines weight category based on weight
   */
  private getWeightCategory(weight?: number): string {
    if (!weight) return WEIGHT_CATEGORIES.LIGHT;
    return weight >= 100 ? WEIGHT_CATEGORIES.HEAVY : WEIGHT_CATEGORIES.LIGHT;
  }

  /**
   * Generates a random batch index (3 digits)
   */
  private generateBatchIndex(): string {
    return Math.floor(Math.random() * 999 + 1)
      .toString()
      .padStart(3, '0');
  }

  /**
   * Generates a batch code following the format:
   * BCH/S{FreightType}/{year}{month}/{index}
   * Example: BCH/SA/2412/001
   */
  generateBatchCode(params: BatchCodeParams & { index?: number }): string {
    const { shippingMode, index } = params;

    // Get freight type code (A for air, S for sea)
    const freightType =
      shippingMode.toUpperCase() === 'AIR'
        ? 'A'
        : shippingMode.toUpperCase() === 'SEA'
          ? 'S'
          : 'R'; // R for road/land

    // Get current date components
    const now = new Date();
    const year = now.getFullYear().toString().slice(-2); // Last 2 digits
    const month = (now.getMonth() + 1).toString().padStart(2, '0'); // Zero-padded month

    // Generate or use provided index
    const batchIndex = index
      ? index.toString().padStart(3, '0')
      : this.generateBatchIndex();

    // Construct batch code: BCH/S{FreightType}/{year}{month}/{index}
    return `BCH/S${freightType}/${year}${month}/${batchIndex}`;
  }

  /**
   * Generates a tracking number following the format:
   * {Prefix}-{LocationCode}-{Year}-{Month}-{Index}-{WeightCategory}
   * Example: AIR-G-24-03-002-H
   */
  generateTrackingNumber(params: TrackingNumberParams): string {
    const { shippingMode, origin, weight, batchIndex } = params;

    // Get shipping mode prefix
    const prefix = SHIPPING_MODES[shippingMode] || 'AIR';

    // Get location code
    const locationCode = this.getLocationCode(origin);

    // Get current date components
    const now = new Date();
    const year = now.getFullYear().toString().slice(-2); // Last 2 digits
    const month = (now.getMonth() + 1).toString().padStart(2, '0'); // Zero-padded month

    // Generate or use provided batch index
    const index = batchIndex
      ? batchIndex.toString().padStart(3, '0')
      : this.generateBatchIndex();

    // Determine weight category
    const weightCategory = this.getWeightCategory(weight);

    // Construct tracking number
    return `${prefix}-${locationCode}-${year}-${month}-${index}-${weightCategory}`;
  }

  /**
   * Generates a shipment tracking number (simplified format)
   * Format: {Prefix}-{LocationCode}-{YearMonth}-{Sequence}
   */
  generateShipmentTrackingNumber(
    freightType: string,
    origin: string,
    trackingType: 'INTERNAL' | 'EXTERNAL' = 'INTERNAL',
  ): string {
    const typePrefix =
      freightType === 'AIR' ? 'AIR' : freightType === 'SEA' ? 'SEA' : 'LAND';
    const locationCode = this.getLocationCode(origin);
    const year = new Date().getFullYear().toString().slice(-2);
    const month = (new Date().getMonth() + 1).toString().padStart(2, '0');
    const sequence = Math.floor(Math.random() * 9999)
      .toString()
      .padStart(4, '0');

    return `${typePrefix}-${locationCode}-${year}${month}-${sequence}`;
  }

  /**
   * Generates a cargo tracking number
   * Format varies based on category
   */
  generateCargoTrackingNumber(
    params: CargoTrackingParams & {
      batchCode: string;
      index?: number;
    },
  ): string {
    const { batchCode, index } = params;

    // Generate or use provided index
    const cargoIndex = index
      ? index.toString().padStart(3, '0')
      : this.generateBatchIndex();

    // Construct cargo tracking number: CG/{BatchCode}/{index}
    return `CG/${batchCode}/${cargoIndex}`;
  }

  /**
   * Generates a release authorization code
   * Format: REL-{Random6Chars}
   */
  generateReleaseCode(): string {
    return `REL-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
  }

  /**
   * Generates an invoice number
   * Format: INV-{Timestamp}-{Random3Digits} or custom format
   */
  generateInvoiceNumber(params: InvoiceNumberParams = {}): string {
    const { prefix = 'INV', customSuffix } = params;

    if (customSuffix) {
      return `${prefix}-${customSuffix}`;
    }

    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    return `${prefix}-${timestamp}-${random}`;
  }

  /**
   * Generates a document number (generic)
   * Format: {Prefix}-{YYMMDD}-{Random4Digits}
   */
  generateDocumentNumber(params: DocumentNumberParams = {}): string {
    const { prefix = 'DOC', includeDate = true } = params;

    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const random = Math.floor(Math.random() * 9999)
      .toString()
      .padStart(4, '0');

    if (includeDate) {
      return `${prefix}-${year}${month}${day}-${random}`;
    } else {
      return `${prefix}-${random}`;
    }
  }

  /**
   * Generates the next sequential tracking number for a given month/year
   * This would typically query the database to get the last used index
   */
  async generateSequentialTrackingNumber(
    params: Omit<TrackingNumberParams, 'batchIndex'>,
    getLastIndexFn?: (
      year: number,
      month: number,
      shippingMode: string,
      locationCode: string,
    ) => Promise<number>,
  ): Promise<string> {
    const { shippingMode, origin, weight } = params;

    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    const locationCode = this.getLocationCode(origin);

    let nextIndex = 1;

    if (getLastIndexFn) {
      try {
        const lastIndex = await getLastIndexFn(
          year,
          month,
          shippingMode,
          locationCode,
        );
        nextIndex = lastIndex + 1;
      } catch (error) {
        console.warn('Failed to get last index, using default:', error);
      }
    }

    return this.generateTrackingNumber({
      shippingMode,
      origin,
      weight,
      batchIndex: nextIndex,
    });
  }

  // ============ VALIDATION FUNCTIONS ============

  /**
   * Validates a batch code format
   * Format: {FreightName}-{ShippingMode}-{LocationCode}-{Year}-{Month}
   */
  validateBatchCode(batchCode: string): boolean {
    const pattern = /^([A-Z]+)-(AIR|SEA|ROAD)-([A-Z]{1,3})-(\d{2})-(\d{2})$/;
    return pattern.test(batchCode);
  }

  /**
   * Validates a tracking number format
   * Format: {Prefix}-{LocationCode}-{Year}-{Month}-{Index}-{WeightCategory}
   */
  validateTrackingNumber(trackingNumber: string): boolean {
    const pattern =
      /^(AIR|SEA|ROAD|LAND)-([A-Z]{1,3})-(\d{2})-(\d{2})-(\d{3})-(H|L)$/;
    return pattern.test(trackingNumber);
  }

  /**
   * Validates a shipment tracking number format
   * Format: {Prefix}-{LocationCode}-{YearMonth}-{Sequence}
   */
  validateShipmentTrackingNumber(trackingNumber: string): boolean {
    const pattern = /^(AIR|SEA|LAND)-([A-Z]{1,3})-(\d{4})-(\d{4})$/;
    return pattern.test(trackingNumber);
  }

  /**
   * Validates a cargo tracking number format
   */
  validateCargoTrackingNumber(trackingNumber: string): boolean {
    // Pattern for DGR/REG format: {PREFIX}-{YYMM}-{Random4}
    const standardPattern = /^(DGR|REG)-(\d{4})-(\d{4})$/;

    // Pattern for SHAMWAA format: SHAMWAA-{Timestamp}-{Random6}
    const shamwaaPattern = /^SHAMWAA-([A-Z0-9]+)-([A-Z0-9]{6})$/;

    return (
      standardPattern.test(trackingNumber) ||
      shamwaaPattern.test(trackingNumber)
    );
  }

  /**
   * Validates a release code format
   * Format: REL-{Random6Chars}
   */
  validateReleaseCode(releaseCode: string): boolean {
    const pattern = /^REL-[A-Z0-9]{6}$/;
    return pattern.test(releaseCode);
  }

  /**
   * Validates an invoice number format
   */
  validateInvoiceNumber(invoiceNumber: string): boolean {
    // Basic pattern: {PREFIX}-{...}
    const pattern = /^[A-Z]{2,}-(.+)$/;
    return pattern.test(invoiceNumber);
  }

  /**
   * Validates a document number format
   */
  validateDocumentNumber(documentNumber: string): boolean {
    // Pattern with date: {PREFIX}-{YYMMDD}-{Random4}
    const datePattern = /^[A-Z]{2,}-(\d{6})-(\d{4})$/;

    // Pattern without date: {PREFIX}-{Random4}
    const simplePattern = /^[A-Z]{2,}-(\d{4})$/;

    return (
      datePattern.test(documentNumber) || simplePattern.test(documentNumber)
    );
  }

  // ============ UTILITY FUNCTIONS ============

  /**
   * Parses a tracking number into its components
   */
  parseTrackingNumber(trackingNumber: string): {
    prefix: string;
    locationCode: string;
    year: string;
    month: string;
    index: string;
    weightCategory: string;
  } | null {
    const pattern =
      /^(AIR|SEA|ROAD|LAND)-([A-Z]{1,3})-(\d{2})-(\d{2})-(\d{3})-(H|L)$/;
    const match = trackingNumber.match(pattern);

    if (!match) return null;

    return {
      prefix: match[1],
      locationCode: match[2],
      year: match[3],
      month: match[4],
      index: match[5],
      weightCategory: match[6],
    };
  }

  /**
   * Parses a batch code into its components
   */
  parseBatchCode(batchCode: string): {
    freightName: string;
    shippingMode: string;
    locationCode: string;
    year: string;
    month: string;
  } | null {
    const pattern = /^([A-Z]+)-(AIR|SEA|ROAD)-([A-Z]{1,3})-(\d{2})-(\d{2})$/;
    const match = batchCode.match(pattern);

    if (!match) return null;

    return {
      freightName: match[1],
      shippingMode: match[2],
      locationCode: match[3],
      year: match[4],
      month: match[5],
    };
  }

  // ============ CONSTANTS EXPORT ============

  /**
   * Get all location codes
   */
  getLocationCodes(): Record<string, string> {
    return { ...LOCATION_CODES };
  }

  /**
   * Get all shipping modes
   */
  getShippingModes(): Record<string, string> {
    return { ...SHIPPING_MODES };
  }

  /**
   * Get all weight categories
   */
  getWeightCategories(): typeof WEIGHT_CATEGORIES {
    return { ...WEIGHT_CATEGORIES };
  }

  /**
   * Get all cargo categories
   */
  getCargoCategoriesMap(): typeof CARGO_CATEGORIES {
    return { ...CARGO_CATEGORIES };
  }
}

// Export singleton instance for global application reference
export const codeGeneratorService = new CodeGeneratorService();

// Export types for external use
export type {
  TrackingNumberParams,
  BatchCodeParams,
  CargoTrackingParams,
  InvoiceNumberParams,
  DocumentNumberParams,
};
