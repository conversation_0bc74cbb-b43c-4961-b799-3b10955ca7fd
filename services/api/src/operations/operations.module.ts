import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { Freight } from './entities/freight.entity';
import { Batch } from './entities/batch.entity';
import { Cargo } from './entities/cargo.entity';
import { Shipment } from './entities/shipment.entity';
import { Customer } from './entities/customer.entity';
import { Handover } from './entities/handover.entity';
import { Account } from '../system/entities/account.entity';
import { User } from '../system/entities/user.entity';

import { FreightController } from './controllers/freight.controller';
import { BatchController } from './controllers/batch.controller';
import { CargoController } from './controllers/cargo.controller';
import { ShipmentController } from './controllers/shipment.controller';
import { CustomerController } from './controllers/customer.controller';
import { HandoverController } from './controllers/handover.controller';

import { FreightService } from './services/freight.service';
import { BatchService } from './services/batch.service';
import { CargoService } from './services/cargo.service';
import { ShipmentService } from './services/shipment.service';
import { CustomerService } from './services/customer.service';
import { HandoverService } from './services/handover.service';
import { CodeGeneratorService } from './services/code-generator.service';

import { SystemModule } from '../system/system.module';
import { WebsocketModule } from '../websocket/websocket.module';
import { SupabaseModule } from '../auth/supabase/supabase.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Freight,
      Batch,
      Cargo,
      Shipment,
      Customer,
      Handover,
      Account,
      User,
    ]),
    SystemModule,
    WebsocketModule,
    SupabaseModule,
  ],
  controllers: [
    FreightController,
    BatchController,
    CargoController,
    ShipmentController,
    CustomerController,
    HandoverController,
  ],
  providers: [
    FreightService,
    BatchService,
    CargoService,
    ShipmentService,
    CustomerService,
    HandoverService,
    CodeGeneratorService,
  ],
  exports: [
    FreightService,
    BatchService,
    CargoService,
    ShipmentService,
    CustomerService,
    HandoverService,
    CodeGeneratorService,
  ],
})
export class OperationsModule {}
