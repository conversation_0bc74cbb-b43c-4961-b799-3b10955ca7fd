import { Column, <PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm';
import { BaseEntity } from '../../common/base.entity';
import { Status } from '../../common/enums/status.enum';
import { Account } from '../../system/entities/account.entity';
import { Cargo } from './cargo.entity';

@Entity('handovers')
export class Handover extends BaseEntity {
  @Column({ name: 'handover_date' })
  handoverDate: Date;
  
  @Column({ name: 'handover_notes', nullable: true })
  handoverNotes: string;
  
  @Column({ name: 'qr_verification', default: false })
  qrVerification: boolean;
  
  @Column({ name: 'biometric_verification', default: false })
  biometricVerification: boolean;
  
  @Column({ name: 'verification_data', type: 'json', nullable: true })
  verificationData: any;
  
  @Column({
    type: 'enum',
    enum: Status,
    default: Status.COMPLETED
  })
  status: Status;
  
  @ManyToOne(() => Account)
  @JoinColumn({ name: 'sender_id' })
  sender: Account;
  
  @ManyToOne(() => Account)
  @JoinColumn({ name: 'receiver_id' })
  receiver: Account;
  
  @ManyToOne(() => Cargo)
  @JoinColumn({ name: 'cargo_id' })
  cargo: Cargo;
  
  @Column({ name: 'location_data', type: 'json', nullable: true })
  locationData: any;
}