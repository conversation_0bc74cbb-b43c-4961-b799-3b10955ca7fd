import { Column, <PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn, ManyToOne, OneToMany } from 'typeorm';
import { BaseEntity } from '../../common/base.entity';
import { BatchType } from '../../common/enums/batch.enum';
import { Status } from '../../common/enums/status.enum';
import { DimensionUnit } from '../../common/enums/dimension-unit.enum';
import { Account } from '../../system/entities/account.entity';
import { Freight } from './freight.entity';
import { Cargo } from './cargo.entity';
import { Shipment } from './shipment.entity';

@Entity('batches')
export class Batch extends BaseEntity {
  @Column()
  name: string;

  @Column({ unique: true })
  code: string;

  @Column({
    type: 'enum',
    enum: BatchType,
  })
  type: BatchType;

  @Column({
    type: 'enum',
    enum: Status,
    default: Status.CREATED
  })
  status: Status;

  @Column({ type: 'float' })
  length: number;

  @Column({ type: 'float' })
  width: number;

  @Column({ type: 'float' })
  height: number;

  @Column({
    type: 'enum',
    enum: DimensionUnit,
    name: 'cbm_unit'
  })
  cbmUnit: DimensionUnit;

  @Column({ name: 'cbm_value' })
  cbmValue: number;

  @Column()
  weight: number;

  @ManyToOne(() => Freight, freight => freight.batches)
  @JoinColumn({ name: 'freight_id' })
  freight: Freight;

  @ManyToOne(() => Account)
  @JoinColumn({ name: 'account_id' })
  account: Account;

  @OneToMany(() => Cargo, cargo => cargo.batch)
  cargos: Cargo[];

  @OneToMany(() => Shipment, shipment => shipment.batch)
  shipments: Shipment[];
}