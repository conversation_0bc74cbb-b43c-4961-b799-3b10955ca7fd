import { Column, <PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm';
import { BaseEntity } from '../../common/base.entity';
import { TrackingType } from '../../common/enums/tracking.enum';
import { Account } from '../../system/entities/account.entity';
import { Freight } from './freight.entity';
import { Batch } from './batch.entity';

@Entity('shipments')
export class Shipment extends BaseEntity {
  @Column({
    name: 'tracking_number_type',
    type: 'enum',
    enum: TrackingType,
  })
  trackingNumberType: TrackingType;

  @Column({ name: 'tracking_number', length: 500 })
  trackingNumber: string;

  @Column({ nullable: true })
  arrival: Date;

  @Column({ nullable: true })
  departure: Date;

  @Column({ name: 'estimated_time_of_arrival', nullable: true })
  estimatedTimeOfArrival: Date;

  @Column({ name: 'estimated_time_of_elapsed', nullable: true })
  estimatedTimeOfElapsed: Date;

  @Column({ nullable: true })
  coordinates: string;

  @Column({ length: 500, nullable: true })
  attachment: string;

  @ManyToOne(() => Account)
  @JoinColumn({ name: 'account_id' })
  account: Account;

  @ManyToOne(() => Freight, freight => freight.shipments)
  @JoinColumn({ name: 'freight_id' })
  freight: Freight;

  @ManyToOne(() => Batch, batch => batch.shipments)
  @JoinColumn({ name: 'batch_id' })
  batch: Batch;
}