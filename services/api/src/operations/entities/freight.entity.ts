import { Column, <PERSON><PERSON><PERSON>, <PERSON>in<PERSON>olum<PERSON>, ManyToOne, OneToMany } from 'typeorm';
// import { StatusEnum } from '@logistics/types';
import { BaseEntity } from '../../common/base.entity';
import { FreightType } from '../../common/enums/freight.enum';
import { WeightUnit } from '../../common/enums/weight-unit.enum';
import { Account } from '../../system/entities/account.entity';
import { Batch } from './batch.entity';
import { Shipment } from './shipment.entity';
import { Status } from 'src/common/enums/status.enum';

@Entity('freights')
export class Freight extends BaseEntity {
  @Column()
  name: string;

  @Column({ unique: true })
  code: string;

  @Column({
    type: 'enum',
    enum: FreightType,
  })
  type: FreightType;

  @Column()
  origin: string;

  @Column()
  destination: string;

  // Todo: Migrate Add status enum
  @Column({
    type: 'enum',
    enum: WeightUnit,
    name: 'status',
  })
  status: Status;

  @Column({ nullable: true })
  vehicle: string;

  @Column({
    type: 'enum',
    enum: WeightUnit,
    name: 'capacity_unit',
  })
  capacityUnit: WeightUnit;

  @Column({ type: 'float', name: 'capacity_value' })
  capacityValue: number;

  @ManyToOne(() => Account)
  @JoinColumn({ name: 'account_id' })
  account: Account;

  @OneToMany(() => Batch, (batch) => batch.freight)
  batches: Batch[];

  @OneToMany(() => Shipment, (shipment) => shipment.freight)
  shipments: Shipment[];
}
