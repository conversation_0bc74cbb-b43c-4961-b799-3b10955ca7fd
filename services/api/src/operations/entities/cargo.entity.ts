import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { BaseEntity } from '../../common/base.entity';
import { Status } from '../../common/enums/status.enum';
import { Category } from '../../common/enums/category.enum';
import { DimensionUnit } from '../../common/enums/dimension-unit.enum';
import { WeightUnit } from '../../common/enums/weight-unit.enum';
import { Account } from '../../system/entities/account.entity';
import { Batch } from './batch.entity';
import { Customer } from './customer.entity';
import { User } from 'src/system/entities/user.entity';

@Entity('cargos')
export class Cargo extends BaseEntity {
  @Column({ name: 'tracking_number' })
  trackingNumber: string;

  @Column()
  particular: string;

  @Column({
    type: 'enum',
    enum: Status,
    default: Status.CREATED,
  })
  status: Status;

  @Column({
    type: 'enum',
    enum: Category,
  })
  category: Category;

  @Column({
    name: 'dimension_unit',
    type: 'enum',
    enum: DimensionUnit,
  })
  dimensionUnit: DimensionUnit;

  @Column({ name: 'dimension_length', nullable: true })
  dimensionLength: number;

  @Column({ name: 'dimension_width', nullable: true })
  dimensionWidth: number;

  @Column({ name: 'dimension_height', nullable: true })
  dimensionHeight: number;

  @Column({
    name: 'cbm_unit',
    type: 'enum',
    enum: DimensionUnit,
    nullable: true,
  })
  cbmUnit: DimensionUnit;

  @Column({ name: 'cbm_value', nullable: true })
  cbmValue: number;

  @Column({
    name: 'weight_unit',
    type: 'enum',
    nullable: true,
    enum: WeightUnit,
  })
  weightUnit: WeightUnit;

  @Column({ name: 'weight_value' })
  weightValue: number;

  @Column({ name: 'unit_price', type: 'float' })
  unitPrice: number;

  @Column({ name: 'total_price', type: 'float' })
  totalPrice: number;

  @ManyToOne(() => Customer, (customer) => customer.cargos)
  @JoinColumn({ name: 'customer_id' })
  customer: Customer;

  @ManyToOne(() => Account)
  @JoinColumn({ name: 'account_id' })
  account: Account;

  @ManyToOne(() => Batch, (batch) => batch.cargos)
  @JoinColumn({ name: 'batch_id' })
  batch: Batch;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'receiver_id' })
  receiver: User;
}
