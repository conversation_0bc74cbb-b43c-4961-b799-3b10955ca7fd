import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiParam, 
  ApiQuery, 
  ApiBearerAuth 
} from '@nestjs/swagger';
import { FreightService } from '../services/freight.service';
import { CreateFreightDto } from '../dto/create-freight.dto';
import { UpdateFreightDto } from '../dto/update-freight.dto';
import { SupabaseAuthGuard } from '../../auth/guards/supabase-auth.guard';
import { GetUser } from '../../auth/decorators/get-user.decorator';
import { Account } from '../../system/entities/account.entity';

@ApiTags('Freights')
@ApiBearerAuth()
@Controller('freights')
@UseGuards(SupabaseAuthGuard)
export class FreightController {
  constructor(private readonly freightService: FreightService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new freight' })
  @ApiResponse({ status: 201, description: 'The freight has been successfully created.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  create(@Body() createFreightDto: CreateFreightDto, @GetUser() account: Account) {
    return this.freightService.create(createFreightDto, account);
  }

  @Get()
  @ApiOperation({ summary: 'Get all freights with pagination' })
  @ApiQuery({ name: 'skip', required: false, description: 'Number of records to skip' })
  @ApiQuery({ name: 'take', required: false, description: 'Number of records to take' })
  @ApiQuery({ name: 'type', required: false, description: 'Filter by freight type (air/sea)' })
  @ApiResponse({ status: 200, description: 'Returns paginated list of freights' })
  findAll(
    @Query('skip') skip = 0,
    @Query('take') take = 10,
    @Query('type') type?: string,
  ) {
    return this.freightService.findAll({
      skip: +skip,
      take: +take,
      type,
    });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get freight by ID' })
  @ApiParam({ name: 'id', description: 'Freight ID' })
  @ApiResponse({ status: 200, description: 'Returns the freight' })
  @ApiResponse({ status: 404, description: 'Freight not found' })
  findOne(@Param('id') id: string) {
    return this.freightService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update freight details' })
  @ApiParam({ name: 'id', description: 'Freight ID' })
  @ApiResponse({ status: 200, description: 'The freight has been successfully updated.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 404, description: 'Freight not found.' })
  update(
    @Param('id') id: string,
    @Body() updateFreightDto: UpdateFreightDto,
    @GetUser() account: Account,
  ) {
    return this.freightService.update(id, updateFreightDto, account);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete freight' })
  @ApiParam({ name: 'id', description: 'Freight ID' })
  @ApiResponse({ status: 200, description: 'The freight has been successfully deleted.' })
  @ApiResponse({ status: 404, description: 'Freight not found.' })
  @ApiResponse({ status: 400, description: 'Freight has associated batches and cannot be deleted.' })
  remove(@Param('id') id: string, @GetUser() account: Account) {
    return this.freightService.remove(id, account);
  }
}