import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiParam, 
  ApiQuery, 
  ApiBearerAuth 
} from '@nestjs/swagger';
import { BatchService } from '../services/batch.service';
import { CreateBatchDto } from '../dto/create-batch.dto';
import { UpdateBatchDto } from '../dto/update-batch.dto';
import { SupabaseAuthGuard } from '../../auth/guards/supabase-auth.guard';
import { GetUser } from '../../auth/decorators/get-user.decorator';
import { Account } from '../../system/entities/account.entity';

@ApiTags('Batches')
@ApiBearerAuth()
@Controller('batches')
@UseGuards(SupabaseAuthGuard)
export class BatchController {
  constructor(private readonly batchService: BatchService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new batch' })
  @ApiResponse({ status: 201, description: 'The batch has been successfully created.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  create(@Body() createBatchDto: CreateBatchDto, @GetUser() account: Account) {
    return this.batchService.create(createBatchDto, account);
  }

  @Get()
  @ApiOperation({ summary: 'Get all batches with pagination' })
  @ApiQuery({ name: 'skip', required: false, description: 'Number of records to skip' })
  @ApiQuery({ name: 'take', required: false, description: 'Number of records to take' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by batch status' })
  @ApiQuery({ name: 'freightId', required: false, description: 'Filter by freight ID' })
  @ApiResponse({ status: 200, description: 'Returns paginated list of batches' })
  findAll(
    @Query('skip') skip = 0,
    @Query('take') take = 10,
    @Query('status') status?: string,
    @Query('freightId') freightId?: string,
  ) {
    return this.batchService.findAll({
      skip: +skip,
      take: +take,
      status,
      freightId,
    });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get batch by ID' })
  @ApiParam({ name: 'id', description: 'Batch ID' })
  @ApiResponse({ status: 200, description: 'Returns the batch' })
  @ApiResponse({ status: 404, description: 'Batch not found' })
  findOne(@Param('id') id: string) {
    return this.batchService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update batch details' })
  @ApiParam({ name: 'id', description: 'Batch ID' })
  @ApiResponse({ status: 200, description: 'The batch has been successfully updated.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 404, description: 'Batch not found.' })
  update(
    @Param('id') id: string,
    @Body() updateBatchDto: UpdateBatchDto,
    @GetUser() account: Account,
  ) {
    return this.batchService.update(id, updateBatchDto, account);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete batch' })
  @ApiParam({ name: 'id', description: 'Batch ID' })
  @ApiResponse({ status: 200, description: 'The batch has been successfully deleted.' })
  @ApiResponse({ status: 404, description: 'Batch not found.' })
  remove(@Param('id') id: string, @GetUser() account: Account) {
    return this.batchService.remove(id, account);
  }

  @Get(':id/cargos')
  @ApiOperation({ summary: 'Get all cargos in a batch' })
  @ApiParam({ name: 'id', description: 'Batch ID' })
  @ApiQuery({ name: 'skip', required: false, description: 'Number of records to skip' })
  @ApiQuery({ name: 'take', required: false, description: 'Number of records to take' })
  @ApiResponse({ status: 200, description: 'Returns paginated list of cargos in the batch' })
  @ApiResponse({ status: 404, description: 'Batch not found.' })
  findCargos(
    @Param('id') id: string,
    @Query('skip') skip = 0,
    @Query('take') take = 10,
  ) {
    return this.batchService.findBatchCargos(id, { skip: +skip, take: +take });
  }
}