import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiParam, 
  ApiQuery, 
  ApiBearerAuth 
} from '@nestjs/swagger';
import { CargoService } from '../services/cargo.service';
import { CreateCargoDto } from '../dto/create-cargo.dto';
import { UpdateCargoDto } from '../dto/update-cargo.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { GetUser } from '../../auth/decorators/get-user.decorator';
import { Account } from '../../system/entities/account.entity';

@ApiTags('Cargos')
@ApiBearerAuth()
@Controller('cargos')
@UseGuards(JwtAuthGuard)
export class CargoController {
  constructor(private readonly cargoService: CargoService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new cargo' })
  @ApiResponse({ status: 201, description: 'The cargo has been successfully created.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  create(@Body() createCargoDto: CreateCargoDto, @GetUser() account: Account) {
    return this.cargoService.create(createCargoDto, account);
  }

  @Get()
  @ApiOperation({ summary: 'Get all cargos with pagination' })
  @ApiQuery({ name: 'skip', required: false, description: 'Number of records to skip' })
  @ApiQuery({ name: 'take', required: false, description: 'Number of records to take' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by cargo status' })
  @ApiQuery({ name: 'batchId', required: false, description: 'Filter by batch ID' })
  @ApiQuery({ name: 'customerId', required: false, description: 'Filter by customer ID' })
  @ApiResponse({ status: 200, description: 'Returns paginated list of cargos' })
  findAll(
    @Query('skip') skip = 0,
    @Query('take') take = 10,
    @Query('status') status?: string,
    @Query('batchId') batchId?: string,
    @Query('customerId') customerId?: string,
  ) {
    return this.cargoService.findAll({
      skip: +skip,
      take: +take,
      status,
      batchId,
      customerId,
    });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get cargo by ID' })
  @ApiParam({ name: 'id', description: 'Cargo ID' })
  @ApiResponse({ status: 200, description: 'Returns the cargo' })
  @ApiResponse({ status: 404, description: 'Cargo not found' })
  findOne(@Param('id') id: string) {
    return this.cargoService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update cargo details' })
  @ApiParam({ name: 'id', description: 'Cargo ID' })
  @ApiResponse({ status: 200, description: 'The cargo has been successfully updated.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 404, description: 'Cargo not found.' })
  update(
    @Param('id') id: string,
    @Body() updateCargoDto: UpdateCargoDto,
    @GetUser() account: Account,
  ) {
    return this.cargoService.update(id, updateCargoDto, account);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete cargo' })
  @ApiParam({ name: 'id', description: 'Cargo ID' })
  @ApiResponse({ status: 200, description: 'The cargo has been successfully deleted.' })
  @ApiResponse({ status: 404, description: 'Cargo not found.' })
  remove(@Param('id') id: string, @GetUser() account: Account) {
    return this.cargoService.remove(id, account);
  }
}