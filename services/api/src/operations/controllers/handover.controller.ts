import {
  Controller,
  Post,
  Body,
  UseGuards,
  UseInterceptors,
  UploadedFile,
  ParseFilePipe,
  MaxFileSizeValidator,
  FileTypeValidator,
  Get,
  Param,
  Query,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { SupabaseAuthGuard } from '../../auth/guards/supabase-auth.guard';
import { GetUser } from '../../auth/decorators/get-user.decorator';
import { Account } from '../../system/entities/account.entity';
import { HandoverService } from '../services/handover.service';
import { ApiTags, ApiOperation, ApiResponse, ApiConsumes, ApiBody, ApiBearerAuth } from '@nestjs/swagger';

@ApiTags('Handovers')
@ApiBearerAuth()
@Controller('handovers')
@UseGuards(SupabaseAuthGuard)
export class HandoverController {
  constructor(private readonly handoverService: HandoverService) {}

  @Post('verify-qr')
  @ApiOperation({ summary: 'Verify cargo using QR code' })
  @ApiResponse({ status: 200, description: 'QR code successfully verified' })
  @ApiResponse({ status: 400, description: 'Invalid QR code' })
  @ApiResponse({ status: 404, description: 'Cargo not found' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        qrData: { type: 'string', description: 'Data from the scanned QR code' }
      },
    },
  })
  async verifyQrCode(
    @Body() body: { qrData: string },
    @GetUser() account: Account,
  ) {
    return this.handoverService.verifyQrCode(body.qrData, account);
  }

  @Post('scan-qr')
  @ApiOperation({ 
    summary: 'Process QR code data (image scanning no longer supported)',
    description: 'This endpoint no longer supports image-based QR code scanning. Please use the verify-qr endpoint instead with the extracted QR code data.'
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'This endpoint no longer supports image-based QR code scanning. Use verify-qr instead.'
        },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'QR code image scanning is not supported' })
  @UseInterceptors(FileInterceptor('file'))
  async scanQrCode(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 1024 * 1024 }), // 1MB
          new FileTypeValidator({ fileType: /(jpg|jpeg|png)$/ }),
        ],
      }),
    )
    file: Express.Multer.File,
    @GetUser() account: Account,
  ) {
    // Inform clients that this functionality is deprecated
    throw new BadRequestException(
      'Image-based QR code scanning is no longer supported. ' +
      'Please use the /handovers/verify-qr endpoint with the extracted QR code data directly.'
    );
  }

  @Post('complete')
  @ApiOperation({ summary: 'Complete handover process' })
  @ApiResponse({ status: 200, description: 'Handover completed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid handover data' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        cargoId: { type: 'string', description: 'ID of the cargo being handed over' },
        receiverId: { type: 'string', description: 'ID of the person receiving the cargo' },
        biometricData: { type: 'string', description: 'Encoded biometric verification data' },
        handoverNotes: { type: 'string', description: 'Optional notes for the handover' },
      },
      required: ['cargoId', 'receiverId', 'biometricData'],
    },
  })
  async completeHandover(
    @Body() 
    body: { 
      cargoId: string; 
      receiverId: string; 
      biometricData: string; 
      handoverNotes?: string;
    },
    @GetUser() account: Account,
  ) {
    return this.handoverService.completeHandover(
      body.cargoId,
      body.receiverId,
      body.biometricData,
      body.handoverNotes ?? '',
      account,
    );
  }

  @Get('history')
  @ApiOperation({ summary: 'Get handover history' })
  @ApiResponse({ status: 200, description: 'Handover history retrieved' })
  async getHandoverHistory(
    @Query('cargoId') cargoId?: string,
    @Query('skip') skip = 0,
    @Query('take') take = 10,
  ) {
    return this.handoverService.getHandoverHistory(cargoId, {
      skip: +skip,
      take: +take,
    });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get handover details by ID' })
  @ApiResponse({ status: 200, description: 'Handover details retrieved' })
  @ApiResponse({ status: 404, description: 'Handover not found' })
  async getHandoverById(@Param('id') id: string) {
    return this.handoverService.getHandoverById(id);
  }
}