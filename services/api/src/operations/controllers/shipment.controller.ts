import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiParam, 
  ApiQuery, 
  ApiBearerAuth 
} from '@nestjs/swagger';
import { ShipmentService } from '../services/shipment.service';
import { CreateShipmentDto } from '../dto/create-shipment.dto';
import { UpdateShipmentDto } from '../dto/update-shipment.dto';
import { SupabaseAuthGuard } from '../../auth/guards/supabase-auth.guard';
import { GetUser } from '../../auth/decorators/get-user.decorator';
import { Account } from '../../system/entities/account.entity';

@ApiTags('Shipments')
@ApiBearerAuth()
@Controller('shipments')
@UseGuards(SupabaseAuthGuard)
export class ShipmentController {
  constructor(private readonly shipmentService: ShipmentService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new shipment' })
  @ApiResponse({ status: 201, description: 'The shipment has been successfully created.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 404, description: 'Freight or Batch not found.' })
  create(@Body() createShipmentDto: CreateShipmentDto, @GetUser() account: Account) {
    return this.shipmentService.create(createShipmentDto, account);
  }

  @Get()
  @ApiOperation({ summary: 'Get all shipments with pagination' })
  @ApiQuery({ name: 'skip', required: false, description: 'Number of records to skip' })
  @ApiQuery({ name: 'take', required: false, description: 'Number of records to take' })
  @ApiQuery({ name: 'freightId', required: false, description: 'Filter by freight ID' })
  @ApiQuery({ name: 'batchId', required: false, description: 'Filter by batch ID' })
  @ApiResponse({ status: 200, description: 'Returns paginated list of shipments' })
  findAll(
    @Query('skip') skip = 0,
    @Query('take') take = 10,
    @Query('freightId') freightId?: string,
    @Query('batchId') batchId?: string,
  ) {
    return this.shipmentService.findAll({
      skip: +skip,
      take: +take,
      freightId,
      batchId,
    });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get shipment by ID' })
  @ApiParam({ name: 'id', description: 'Shipment ID' })
  @ApiResponse({ status: 200, description: 'Returns the shipment' })
  @ApiResponse({ status: 404, description: 'Shipment not found' })
  findOne(@Param('id') id: string) {
    return this.shipmentService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update shipment details' })
  @ApiParam({ name: 'id', description: 'Shipment ID' })
  @ApiResponse({ status: 200, description: 'The shipment has been successfully updated.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 404, description: 'Shipment not found.' })
  update(
    @Param('id') id: string,
    @Body() updateShipmentDto: UpdateShipmentDto,
    @GetUser() account: Account,
  ) {
    return this.shipmentService.update(id, updateShipmentDto, account);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete shipment' })
  @ApiParam({ name: 'id', description: 'Shipment ID' })
  @ApiResponse({ status: 200, description: 'The shipment has been successfully deleted.' })
  @ApiResponse({ status: 404, description: 'Shipment not found.' })
  remove(@Param('id') id: string, @GetUser() account: Account) {
    return this.shipmentService.remove(id, account);
  }

  @Get('tracking/:trackingNumber')
  @ApiOperation({ summary: 'Find shipment by tracking number' })
  @ApiParam({ name: 'trackingNumber', description: 'Tracking number' })
  @ApiResponse({ status: 200, description: 'Returns the shipment' })
  @ApiResponse({ status: 404, description: 'Shipment not found' })
  findByTrackingNumber(@Param('trackingNumber') trackingNumber: string) {
    return this.shipmentService.findByTrackingNumber(trackingNumber);
  }
}