import { IsEnum, IsOptional, IsString } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { FreightType } from '../../common/enums/freight.enum';
import { WeightUnit } from '../../common/enums/weight-unit.enum';

export class UpdateFreightDto {
  @ApiPropertyOptional({
    description: 'Name of the freight',
    example: 'Updated Winter Shipment 2025',
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Unique code for the freight',
    example: 'F-2025-001-UPD',
  })
  @IsOptional()
  @IsString()
  code?: string;

  @ApiPropertyOptional({
    description: 'Type of freight',
    enum: FreightType,
    example: FreightType.SEA,
  })
  @IsOptional()
  @IsEnum(FreightType)
  type?: FreightType;

  @ApiPropertyOptional({
    description: 'Origin location',
    example: 'Shanghai, China',
  })
  @IsOptional()
  @IsString()
  origin?: string;

  @ApiPropertyOptional({
    description: 'Destination location',
    example: 'New York, USA',
  })
  @IsOptional()
  @IsString()
  destination?: string;

  @ApiPropertyOptional({
    description: 'Vehicle information',
    example: 'Cargo Ship XYZ',
  })
  @IsOptional()
  @IsString()
  vehicle?: string;

  @ApiPropertyOptional({
    description: 'Capacity unit',
    enum: WeightUnit,
    example: WeightUnit.LONG_TON,
  })
  @IsOptional()
  @IsEnum(WeightUnit)
  capacityUnit?: WeightUnit;

  @ApiPropertyOptional({
    description: 'Capacity value in the specified unit',
    example: 150,
  })
  @IsOptional()
  capacityValue?: number;
}
