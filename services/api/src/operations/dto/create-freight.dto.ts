import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { FreightType } from '../../common/enums/freight.enum';
import { WeightUnit } from '../../common/enums/weight-unit.enum';

export class CreateFreightDto {
  @ApiProperty({
    description: 'Name of the freight',
    example: 'Winter Shipment 2025',
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Unique code for the freight',
    example: 'F-2025-001',
  })
  @IsNotEmpty()
  @IsString()
  code: string;

  @ApiProperty({
    description: 'Type of freight',
    enum: FreightType,
    example: FreightType.AIR,
  })
  @IsEnum(FreightType)
  type: FreightType;

  @ApiProperty({ description: 'Origin location', example: 'Guangzhou, China' })
  @IsNotEmpty()
  @IsString()
  origin: string;

  @ApiProperty({
    description: 'Destination location',
    example: 'Los Angeles, USA',
  })
  @IsNotEmpty()
  @IsString()
  destination: string;

  @ApiPropertyOptional({
    description: 'Vehicle information',
    example: 'Boeing 747',
  })
  @IsOptional()
  @IsString()
  vehicle?: string;

  @ApiProperty({
    description: 'Capacity unit',
    enum: WeightUnit,
    example: WeightUnit.T,
  })
  @IsEnum(WeightUnit)
  capacityUnit: WeightUnit;

  @ApiProperty({
    description: 'Capacity value in the specified unit',
    example: 120,
  })
  @IsNotEmpty()
  capacityValue: number;
}
