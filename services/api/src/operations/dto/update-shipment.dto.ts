import {
  IsDateString,
  IsEnum,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { TrackingType } from '../../common/enums/tracking.enum';

export class UpdateShipmentDto {
  @ApiPropertyOptional({ 
    description: 'Type of tracking number',
    enum: TrackingType,
    example: TrackingType.BL
  })
  @IsOptional()
  @IsEnum(TrackingType)
  trackingNumberType?: TrackingType;

  @ApiPropertyOptional({ description: 'Tracking number', example: 'SEA-G-23-05-456-H' })
  @IsOptional()
  @IsString()
  trackingNumber?: string;

  @ApiPropertyOptional({ description: 'Arrival date and time', example: '2025-05-16T10:00:00Z' })
  @IsOptional()
  @IsDateString()
  arrival?: Date;

  @ApiPropertyOptional({ description: 'Departure date and time', example: '2025-05-02T14:30:00Z' })
  @IsOptional()
  @IsDateString()
  departure?: Date;

  @ApiPropertyOptional({ description: 'Estimated time of arrival', example: '2025-05-16T10:00:00Z' })
  @IsOptional()
  @IsDateString()
  estimatedTimeOfArrival?: Date;

  @ApiPropertyOptional({ description: 'Estimated time elapsed', example: '2025-05-17T10:00:00Z' })
  @IsOptional()
  @IsDateString()
  estimatedTimeOfElapsed?: Date;

  @ApiPropertyOptional({ description: 'Shipment coordinates', example: '34.0522,-118.2437' })
  @IsOptional()
  @IsString()
  coordinates?: string;

  @ApiPropertyOptional({ description: 'Attachment path', example: '/shipments/documents/updated-manifest-123.pdf' })
  @IsOptional()
  @IsString()
  attachment?: string;

  @ApiPropertyOptional({ description: 'ID of the freight for this shipment', example: '550e8400-e29b-41d4-a716-************' })
  @IsOptional()
  @IsUUID()
  freightId?: string;

  @ApiPropertyOptional({ description: 'ID of the batch for this shipment', example: '550e8400-e29b-41d4-a716-************' })
  @IsOptional()
  @IsUUID()
  batchId?: string;
}