import {
  <PERSON><PERSON><PERSON>,
  <PERSON>N<PERSON>ber,
  IsOptional,
  IsPositive,
  IsString,
  IsUUID,
} from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { BatchType } from '../../common/enums/batch.enum';
import { DimensionUnit } from '../../common/enums/dimension-unit.enum';
import { Status } from '../../common/enums/status.enum';

export class UpdateBatchDto {
  @ApiPropertyOptional({ description: 'Name of the batch', example: 'Updated Electronics Shipment Batch' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'Unique code for the batch', example: 'BATCH-2025-001-UPD' })
  @IsOptional()
  @IsString()
  code?: string;

  @ApiPropertyOptional({ 
    description: 'Type of batch',
    enum: BatchType,
    example: BatchType.BOX
  })
  @IsOptional()
  @IsEnum(BatchType)
  type?: BatchType;

  @ApiPropertyOptional({ 
    description: 'Batch status',
    enum: Status,
    example: Status.PROCESSING
  })
  @IsOptional()
  @IsEnum(Status)
  status?: Status;

  @ApiPropertyOptional({ description: 'Length of the batch', example: 1300 })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  length?: number;

  @ApiPropertyOptional({ description: 'Width of the batch', example: 250 })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  width?: number;

  @ApiPropertyOptional({ description: 'Height of the batch', example: 270 })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  height?: number;

  @ApiPropertyOptional({ 
    description: 'CBM unit',
    enum: DimensionUnit,
    example: DimensionUnit.FT3
  })
  @IsOptional()
  @IsEnum(DimensionUnit)
  cbmUnit?: DimensionUnit;

  @ApiPropertyOptional({ description: 'CBM value', example: 80 })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  cbmValue?: number;

  @ApiPropertyOptional({ description: 'Weight of the batch in kg', example: 9000 })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  weight?: number;

  @ApiPropertyOptional({ description: 'ID of the freight this batch belongs to', example: '550e8400-e29b-41d4-a716-************' })
  @IsOptional()
  @IsUUID()
  freightId?: string;
}