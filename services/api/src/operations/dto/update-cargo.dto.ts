import {
  IsEnum,
  IsN<PERSON>ber,
  IsOptional,
  IsPositive,
  IsString,
  IsUUID,
} from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Category } from '../../common/enums/category.enum';
import { DimensionUnit } from '../../common/enums/dimension-unit.enum';
import { WeightUnit } from '../../common/enums/weight-unit.enum';
import { Status } from '../../common/enums/status.enum';

export class UpdateCargoDto {
  @ApiPropertyOptional({ description: 'Cargo particular/name', example: 'Updated Electronics Shipment' })
  @IsOptional()
  @IsString()
  particular?: string;

  @ApiPropertyOptional({ 
    description: 'Cargo status', 
    enum: Status,
    example: Status.PROCESSING 
  })
  @IsOptional()
  @IsEnum(Status)
  status?: Status;

  @ApiPropertyOptional({ 
    description: 'Cargo category', 
    enum: Category,
    example: Category.DANGEROUS
  })
  @IsOptional()
  @IsEnum(Category)
  category?: Category;

  @ApiPropertyOptional({ 
    description: 'Dimension unit', 
    enum: DimensionUnit,
    example: DimensionUnit.IN
  })
  @IsOptional()
  @IsEnum(DimensionUnit)
  dimensionUnit?: DimensionUnit;

  @ApiPropertyOptional({ description: 'Length of the cargo', example: 55 })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  dimensionLength?: number;

  @ApiPropertyOptional({ description: 'Width of the cargo', example: 35 })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  dimensionWidth?: number;

  @ApiPropertyOptional({ description: 'Height of the cargo', example: 25 })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  dimensionHeight?: number;

  @ApiPropertyOptional({ 
    description: 'CBM unit', 
    enum: DimensionUnit,
    example: DimensionUnit.FT3
  })
  @IsOptional()
  @IsEnum(DimensionUnit)
  cbmUnit?: DimensionUnit;

  @ApiPropertyOptional({ description: 'CBM value', example: 0.05 })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  cbmValue?: number;

  @ApiPropertyOptional({ 
    description: 'Weight unit', 
    enum: WeightUnit,
    example: WeightUnit.LB
  })
  @IsOptional()
  @IsEnum(WeightUnit)
  weightUnit?: WeightUnit;

  @ApiPropertyOptional({ description: 'Weight value', example: 30 })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  weightValue?: number;

  @ApiPropertyOptional({ description: 'Unit price', example: 12 })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  unitPrice?: number;

  @ApiPropertyOptional({ description: 'Total price', example: 600 })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  totalPrice?: number;

  @ApiPropertyOptional({ description: 'Customer ID', example: '550e8400-e29b-41d4-a716-************' })
  @IsOptional()
  @IsUUID()
  customerId?: string;

  @ApiPropertyOptional({ description: 'Batch ID', example: '550e8400-e29b-41d4-a716-************' })
  @IsOptional()
  @IsUUID()
  batchId?: string;
}