import {
  IsEmail,
  <PERSON>Enum,
  <PERSON>NotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Status } from '../../common/enums/status.enum';

export class CreateCustomerDto {
  @ApiProperty({ description: 'Customer name', example: 'Acme Corporation' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiPropertyOptional({ 
    description: 'Customer status',
    enum: Status,
    default: Status.ACTIVE,
    example: Status.ACTIVE
  })
  @IsOptional()
  @IsEnum(Status)
  status?: Status = Status.ACTIVE;

  @ApiPropertyOptional({ description: 'Customer email', example: '<EMAIL>' })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({ description: 'Customer phone', example: '+1234567890' })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiPropertyOptional({ description: 'Customer location', example: 'New York, USA' })
  @IsOptional()
  @IsString()
  location?: string;
}