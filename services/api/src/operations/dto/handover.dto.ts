import { 
  IsNotEmpty, 
  IsString, 
  IsUUID, 
  IsOptional, 
  IsBoolean, 
  IsObject, 
  ValidateNested 
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class QrCodeVerifyDto {
  @ApiProperty({ 
    description: 'Data from the scanned QR code',
    example: '{"id":"a1b2c3d4-e5f6","type":"cargo","timestamp":"2023-04-01T12:00:00Z"}'
  })
  @IsNotEmpty()
  @IsString()
  qrData: string;
}

export class LocationDataDto {
  @ApiProperty({ description: 'Latitude coordinate', example: 40.7128 })
  @IsNotEmpty()
  latitude: number;
  
  @ApiProperty({ description: 'Longitude coordinate', example: -74.0060 })
  @IsNotEmpty()
  longitude: number;
  
  @ApiPropertyOptional({ description: 'Accuracy in meters', example: 10 })
  @IsOptional()
  accuracy?: number;
}

export class CompleteHandoverDto {
  @ApiProperty({ description: 'ID of the cargo being handed over' })
  @IsUUID()
  @IsNotEmpty()
  cargoId: string;
  
  @ApiProperty({ description: 'ID of the person receiving the cargo' })
  @IsUUID()
  @IsNotEmpty()
  receiverId: string;
  
  @ApiProperty({ 
    description: 'Encoded biometric verification data',
    example: 'YWJjMTIzLjE2ODE5MTI0MDAuOTgzNGZiZWE3MjllNWY2'
  })
  @IsNotEmpty()
  @IsString()
  biometricData: string;
  
  @ApiPropertyOptional({ description: 'Optional notes for the handover' })
  @IsOptional()
  @IsString()
  handoverNotes?: string;
  
  @ApiPropertyOptional({ 
    description: 'Location data of the handover',
    type: LocationDataDto
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => LocationDataDto)
  locationData?: LocationDataDto;
}

export class HandoverResponseDto {
  @ApiProperty({ description: 'Success status of the operation' })
  @IsBoolean()
  success: boolean;
  
  @ApiProperty({ description: 'ID of the created handover record' })
  @IsString()
  handoverId: string;
  
  @ApiProperty({ description: 'Cargo information' })
  @IsObject()
  cargo: {
    id: string;
    trackingNumber: string;
    status: string;
  };
  
  @ApiProperty({ description: 'Receiver information' })
  @IsObject()
  receiver: {
    id: string;
    name: string;
  };
  
  @ApiProperty({ description: 'Date and time of the handover' })
  handoverDate: Date;
}