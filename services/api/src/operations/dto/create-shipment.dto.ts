import {
  IsDateString,
  IsEnum,
  <PERSON>NotEmpty,
  <PERSON><PERSON>ptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TrackingType } from '../../common/enums/tracking.enum';

export class CreateShipmentDto {
  @ApiProperty({ 
    description: 'Type of tracking number',
    enum: TrackingType,
    example: TrackingType.AWB
  })
  @IsEnum(TrackingType)
  trackingNumberType: TrackingType;

  @ApiProperty({ description: 'Tracking number', example: 'AIR-G-23-04-123-H' })
  @IsNotEmpty()
  @IsString()
  trackingNumber: string;

  @ApiPropertyOptional({ description: 'Arrival date and time', example: '2025-05-15T10:00:00Z' })
  @IsOptional()
  @IsDateString()
  arrival?: Date;

  @ApiPropertyOptional({ description: 'Departure date and time', example: '2025-05-01T14:30:00Z' })
  @IsOptional()
  @IsDateString()
  departure?: Date;

  @ApiPropertyOptional({ description: 'Estimated time of arrival', example: '2025-05-15T10:00:00Z' })
  @IsOptional()
  @IsDateString()
  estimatedTimeOfArrival?: Date;

  @ApiPropertyOptional({ description: 'Estimated time elapsed', example: '2025-05-16T10:00:00Z' })
  @IsOptional()
  @IsDateString()
  estimatedTimeOfElapsed?: Date;

  @ApiPropertyOptional({ description: 'Shipment coordinates', example: '40.7128,-74.0060' })
  @IsOptional()
  @IsString()
  coordinates?: string;

  @ApiPropertyOptional({ description: 'Attachment path', example: '/shipments/documents/manifest-123.pdf' })
  @IsOptional()
  @IsString()
  attachment?: string;

  @ApiProperty({ description: 'ID of the freight for this shipment', example: '550e8400-e29b-41d4-a716-************' })
  @IsUUID()
  freightId: string;

  @ApiProperty({ description: 'ID of the batch for this shipment', example: '550e8400-e29b-41d4-a716-************' })
  @IsUUID()
  batchId: string;
}