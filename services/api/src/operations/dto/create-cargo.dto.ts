import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPositive,
  IsString,
  IsUUID,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Category } from '../../common/enums/category.enum';
import { DimensionUnit } from '../../common/enums/dimension-unit.enum';
import { WeightUnit } from '../../common/enums/weight-unit.enum';
import { Status } from '../../common/enums/status.enum';

export class CreateCargoDto {
  @ApiProperty({ description: 'Cargo particular/name', example: 'Electronics Shipment' })
  @IsNotEmpty()
  @IsString()
  particular: string;

  @ApiPropertyOptional({ 
    description: 'Cargo status', 
    enum: Status, 
    default: Status.CREATED,
    example: Status.CREATED
  })
  @IsOptional()
  @IsEnum(Status)
  status?: Status = Status.CREATED;

  @ApiProperty({ 
    description: 'Cargo category', 
    enum: Category,
    example: Category.SAFE
  })
  @IsEnum(Category)
  category: Category;

  @ApiProperty({ 
    description: 'Dimension unit', 
    enum: DimensionUnit,
    example: DimensionUnit.CM
  })
  @IsEnum(DimensionUnit)
  dimensionUnit: DimensionUnit;

  @ApiProperty({ description: 'Length of the cargo', example: 50 })
  @IsNumber()
  @IsPositive()
  dimensionLength: number;

  @ApiProperty({ description: 'Width of the cargo', example: 30 })
  @IsNumber()
  @IsPositive()
  dimensionWidth: number;

  @ApiProperty({ description: 'Height of the cargo', example: 20 })
  @IsNumber()
  @IsPositive()
  dimensionHeight: number;

  @ApiProperty({ 
    description: 'CBM unit', 
    enum: DimensionUnit,
    example: DimensionUnit.M3
  })
  @IsEnum(DimensionUnit)
  cbmUnit: DimensionUnit;

  @ApiProperty({ description: 'CBM value', example: 0.03 })
  @IsNumber()
  @IsPositive()
  cbmValue: number;

  @ApiProperty({ 
    description: 'Weight unit', 
    enum: WeightUnit,
    example: WeightUnit.KG
  })
  @IsEnum(WeightUnit)
  weightUnit: WeightUnit;

  @ApiProperty({ description: 'Weight value', example: 25 })
  @IsNumber()
  @IsPositive()
  weightValue: number;

  @ApiProperty({ description: 'Unit price', example: 10.5 })
  @IsNumber()
  @IsPositive()
  unitPrice: number;

  @ApiProperty({ description: 'Total price', example: 525 })
  @IsNumber()
  @IsPositive()
  totalPrice: number;

  @ApiProperty({ description: 'Customer ID', example: '550e8400-e29b-41d4-a716-************' })
  @IsUUID()
  customerId: string;

  @ApiPropertyOptional({ description: 'Batch ID', example: '550e8400-e29b-41d4-a716-************' })
  @IsOptional()
  @IsUUID()
  batchId?: string;
}