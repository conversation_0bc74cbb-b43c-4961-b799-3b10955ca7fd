import {
  <PERSON><PERSON>num,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPositive,
  IsString,
  IsUUID,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { BatchType } from '../../common/enums/batch.enum';
import { DimensionUnit } from '../../common/enums/dimension-unit.enum';
import { Status } from '../../common/enums/status.enum';

export class CreateBatchDto {
  @ApiProperty({ description: 'Name of the batch', example: 'Electronics Shipment Batch' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: 'Unique code for the batch', example: 'BATCH-2025-001' })
  @IsNotEmpty()
  @IsString()
  code: string;

  @ApiProperty({ 
    description: 'Type of batch',
    enum: BatchType,
    example: BatchType.CONTAINER
  })
  @IsEnum(BatchType)
  type: BatchType;

  @ApiPropertyOptional({ 
    description: 'Batch status',
    enum: Status,
    default: Status.CREATED,
    example: Status.CREATED
  })
  @IsOptional()
  @IsEnum(Status)
  status?: Status = Status.CREATED;

  @ApiProperty({ description: 'Length of the batch', example: 1200 })
  @IsNumber()
  @IsPositive()
  length: number;

  @ApiProperty({ description: 'Width of the batch', example: 240 })
  @IsNumber()
  @IsPositive()
  width: number;

  @ApiProperty({ description: 'Height of the batch', example: 260 })
  @IsNumber()
  @IsPositive()
  height: number;

  @ApiProperty({ 
    description: 'CBM unit',
    enum: DimensionUnit,
    example: DimensionUnit.M3
  })
  @IsEnum(DimensionUnit)
  cbmUnit: DimensionUnit;

  @ApiProperty({ description: 'CBM value', example: 75 })
  @IsNumber()
  @IsPositive()
  cbmValue: number;

  @ApiProperty({ description: 'Weight of the batch in kg', example: 8500 })
  @IsNumber()
  @IsPositive()
  weight: number;

  @ApiProperty({ description: 'ID of the freight this batch belongs to', example: '550e8400-e29b-41d4-a716-************' })
  @IsUUID()
  freightId: string;
}