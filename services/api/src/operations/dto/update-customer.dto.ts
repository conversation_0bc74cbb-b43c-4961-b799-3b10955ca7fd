import {
  IsEmail,
  IsEnum,
  IsOptional,
  IsString,
} from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Status } from '../../common/enums/status.enum';

export class UpdateCustomerDto {
  @ApiPropertyOptional({ description: 'Customer name', example: 'Updated Acme Corporation' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ 
    description: 'Customer status',
    enum: Status,
    example: Status.INACTIVE
  })
  @IsOptional()
  @IsEnum(Status)
  status?: Status;

  @ApiPropertyOptional({ description: 'Customer email', example: '<EMAIL>' })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({ description: 'Customer phone', example: '+1987654321' })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiPropertyOptional({ description: 'Customer location', example: 'Los Angeles, USA' })
  @IsOptional()
  @IsString()
  location?: string;
}