import {
  MigrationInterface,
  QueryRunner,
  TableColumn,
  TableIndex,
} from 'typeorm';

export class AddOriginDestinationToFreights1705234800000
  implements MigrationInterface
{
  name = 'AddOriginDestinationToFreights1705234800000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add origin column
    await queryRunner.addColumn(
      'freights',
      new TableColumn({
        name: 'origin',
        type: 'varchar',
        length: '255',
        isNullable: false,
        default: "''",
        comment: 'Origin location for the freight shipment',
      }),
    );

    // Add destination column
    await queryRunner.addColumn(
      'freights',
      new TableColumn({
        name: 'destination',
        type: 'varchar',
        length: '255',
        isNullable: false,
        default: "''",
        comment: 'Destination location for the freight shipment',
      }),
    );

    // Add indexes for better query performance
    await queryRunner.createIndex(
      'freights',
      new TableIndex({
        name: 'idx_freights_origin',
        columnNames: ['origin'],
      }),
    );

    await queryRunner.createIndex(
      'freights',
      new TableIndex({
        name: 'idx_freights_destination',
        columnNames: ['destination'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.dropIndex('freights', 'idx_freights_destination');
    await queryRunner.dropIndex('freights', 'idx_freights_origin');

    // Drop columns
    await queryRunner.dropColumn('freights', 'destination');
    await queryRunner.dropColumn('freights', 'origin');
  }
}
