-- Migration: Add origin and destination columns to freights table
-- Date: 2025-01-14
-- Description: Add origin and destination fields to support shipping location tracking

-- Add origin column
ALTER TABLE freights 
ADD COLUMN origin VARCHAR(255) NOT NULL DEFAULT '';

-- Add destination column  
ALTER TABLE freights 
ADD COLUMN destination VARCHAR(255) NOT NULL DEFAULT '';

-- Update existing records with placeholder values (optional - can be updated manually)
-- UPDATE freights SET origin = 'Not specified' WHERE origin = '';
-- UPDATE freights SET destination = 'Not specified' WHERE destination = '';

-- Add indexes for better query performance
CREATE INDEX idx_freights_origin ON freights(origin);
CREATE INDEX idx_freights_destination ON freights(destination);

-- Add comments for documentation
COMMENT ON COLUMN freights.origin IS 'Origin location for the freight shipment';
COMMENT ON COLUMN freights.destination IS 'Destination location for the freight shipment';
