import { MigrationInterface, QueryRunner, Index } from 'typeorm';

export class RemoveOriginDestinationFromCargos1705234800002
  implements MigrationInterface
{
  name = 'RemoveOriginDestinationFromCargos1705234800002';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes first if they exist
    try {
      await queryRunner.dropIndex('cargos', 'idx_cargos_origin');
    } catch (error) {
      // Index might not exist, continue
      console.log('Index idx_cargos_origin does not exist, skipping...');
    }

    try {
      await queryRunner.dropIndex('cargos', 'idx_cargos_destination');
    } catch (error) {
      // Index might not exist, continue
      console.log('Index idx_cargos_destination does not exist, skipping...');
    }

    // Drop origin column if it exists
    const originColumnExists = await queryRunner.hasColumn('cargos', 'origin');
    if (originColumnExists) {
      await queryRunner.dropColumn('cargos', 'origin');
      console.log('Dropped origin column from cargos table');
    }

    // Drop destination column if it exists
    const destinationColumnExists = await queryRunner.hasColumn(
      'cargos',
      'destination',
    );
    if (destinationColumnExists) {
      await queryRunner.dropColumn('cargos', 'destination');
      console.log('Dropped destination column from cargos table');
    }

    // Create a view to easily access cargo with origin/destination information
    await queryRunner.query(`
      CREATE OR REPLACE VIEW cargos_with_locations AS
      SELECT 
        c.*,
        f.origin,
        f.destination,
        f.name as freight_name,
        f.code as freight_code,
        b.name as batch_name,
        b.code as code
      FROM cargos c
      LEFT JOIN batches b ON c.batch_id = b.id
      LEFT JOIN freights f ON b.freight_id = f.id;
    `);

    // Add comment to the view
    await queryRunner.query(`
      COMMENT ON VIEW cargos_with_locations IS 'View that provides cargo information with origin/destination from associated freight';
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the view
    await queryRunner.query(`DROP VIEW IF EXISTS cargos_with_locations;`);

    // Re-add origin column
    await queryRunner.query(`
      ALTER TABLE cargos 
      ADD COLUMN origin VARCHAR(255) NULL;
    `);

    // Re-add destination column
    await queryRunner.query(`
      ALTER TABLE cargos 
      ADD COLUMN destination VARCHAR(255) NULL;
    `);

    // Re-create indexes
    await queryRunner.createIndex(
      'cargos',
      new Index('idx_cargos_origin', ['origin']),
    );

    await queryRunner.createIndex(
      'cargos',
      new Index('idx_cargos_destination', ['destination']),
    );
  }
}
