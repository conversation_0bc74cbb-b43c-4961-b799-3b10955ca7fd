import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import { RedisIoAdapter } from './websocket/redis.adapter';
import expressListRoutes from 'express-list-routes';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Get ConfigService for shared configuration
  const configService = app.get(ConfigService);

  // Set up WebSocket adapter with Redis for Kubernetes scaling
  if (configService.get<string>('REDIS_ENABLED') === 'true') {
    const redisIoAdapter = new RedisIoAdapter(app, configService);
    await app.useWebSocketAdapter(redisIoAdapter);
  }

  // Enable CORS
  app.enableCors();

  // Set global prefix for all routes
  app.setGlobalPrefix('api');

  // Enable validation pipes
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }),
  );

  // Setup Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('Shamwaa Logistics API')
    .setDescription('API documentation for Shamwaa Logistics Management System')
    .setVersion('1.0')
    .addBearerAuth({
      type: 'http',
      scheme: 'bearer',
      bearerFormat: 'JWT',
      description: 'Enter JWT token',
      in: 'header',
    })
    .addTag('Auth', 'Authentication endpoints')
    .addTag('Users', 'User management endpoints')
    .addTag('Customers', 'Customer management endpoints')
    .addTag('Cargos', 'Cargo management endpoints')
    .addTag('Batches', 'Batch management endpoints')
    .addTag('Shipments', 'Shipment tracking endpoints')
    .addTag('Handovers', 'Cargo handover endpoints')
    .addTag('Finances', 'Finance management endpoints')
    .addTag('Reports', 'Reporting and analytics endpoints')
    .addTag('System', 'System utilities and settings')
    .addTag('Biometrics', 'Biometric authentication endpoints')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  // Configure app port with fallback
  const port = configService.get<number>('PORT', 3000);
  await app.listen(port);
}

bootstrap();
