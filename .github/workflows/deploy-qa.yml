name: Build and Deploy to A<PERSON>

on:
  push:
    branches:
      - qa

env:
  AWS_REGION: ap-southeast-1
  ECR_REPOSITORY: shamwaa-repo
  IMAGE_TAG: latest
  ECS_CLUSTER: shamwaa-cluster
  ECS_SERVICE: shamwaa-service
  CONTAINER_NAME: web

jobs:
  deploy:
    name: Build Docker and Deploy to ECS
    runs-on: ubuntu-latest

    permissions:
      id-token: write
      contents: read

    steps:
      - name: 🧾 Checkout repo
        uses: actions/checkout@v4

      - name: 🔐 Configure AWS credentials using OIDC
        uses: aws-actions/configure-aws-credentials@v3
        with:
          role-to-assume: arn:aws:iam::505787607892:role/GitHubActionsOIDCRole
          aws-region: ${{ env.AWS_REGION }}

      - name: 🐳 Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: 🔨 Build, tag and push Docker image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}

          TURBO_TELEMETRY_DISABLED: 1

          NEXT_TELEMETRY_DISABLED: 1
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
          NEXT_PUBLIC_GOOGLE_MAPS_API_KEY: ${{ secrets.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY }}
          NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY }}

          SMTP_HOST: ${{ secrets.SMTP_HOST }}
          SMTP_USER: ${{ secrets.SMTP_USER }}
          SMTP_PASS: ${{ secrets.SMTP_PASS }}
          SMTP_FROM_NAME: ${{ secrets.SMTP_FROM_NAME }}
          SMTP_FROM_EMAIL: ${{ secrets.SMTP_FROM_EMAIL }}

          TWILIO_AUTH_TOKEN: ${{ secrets.TWILIO_AUTH_TOKEN }}
          TWILIO_ACCOUNT_SID: ${{ secrets.TWILIO_ACCOUNT_SID }}
          TWILIO_PHONE_NUMBER: ${{ secrets.TWILIO_PHONE_NUMBER }}
          TWILIO_WHATSAPP_NUMBER: ${{ secrets.TWILIO_WHATSAPP_NUMBER }}
          TWILIO_WEBHOOK_URL: ${{ secrets.TWILIO_WEBHOOK_URL }}

          SAFECUBE_API_KEY: ${{ secrets.SAFECUBE_API_KEY }}

        run: |
          docker build --build-arg NEXT_TELEMETRY_DISABLED=$NEXT_TELEMETRY_DISABLED --build-arg TURBO_TELEMETRY_DISABLED=$TURBO_TELEMETRY_DISABLED --build-arg NEXT_PUBLIC_SUPABASE_URL=$NEXT_PUBLIC_SUPABASE_URL --build-arg NEXT_PUBLIC_SUPABASE_ANON_KEY=$NEXT_PUBLIC_SUPABASE_ANON_KEY --build-arg NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=$NEXT_PUBLIC_GOOGLE_MAPS_API_KEY --build-arg NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY=$NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY --build-arg SMTP_HOST=$SMTP_HOST --build-arg SMTP_USER=$SMTP_USER --build-arg SMTP_PASS=$SMTP_PASS --build-arg SMTP_FROM_NAME=$SMTP_FROM_NAME --build-arg SMTP_FROM_EMAIL=$SMTP_FROM_EMAIL --build-arg TWILIO_AUTH_TOKEN=$TWILIO_AUTH_TOKEN --build-arg TWILIO_ACCOUNT_SID=$TWILIO_ACCOUNT_SID --build-arg TWILIO_PHONE_NUMBER=$TWILIO_PHONE_NUMBER --build-arg TWILIO_WHATSAPP_NUMBER=$TWILIO_WHATSAPP_NUMBER --build-arg TWILIO_WEBHOOK_URL=$TWILIO_WEBHOOK_URL --build-arg SAFECUBE_API_KEY=$SAFECUBE_API_KEY -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG -f Dockerfile.web .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

      - name: 🚀 Update ECS service
        run: |
          aws ecs update-service \
            --cluster $ECS_CLUSTER \
            --service $ECS_SERVICE \
            --force-new-deployment \
            --region $AWS_REGION
