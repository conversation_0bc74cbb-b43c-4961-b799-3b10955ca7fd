name: Build and Deploy to AWS

on:
  push:
    branches:
      - release/prod

env:
  AWS_REGION: ap-southeast-1
  ECR_REPOSITORY: shamwaa-repo
  IMAGE_TAG: latest
  ECS_CLUSTER: shamwaa-cluster
  ECS_SERVICE: shamwaa-service
  CONTAINER_NAME: web

jobs:
  deploy:
    name: Build Docker and Deploy to ECS
    runs-on: ubuntu-latest

    permissions:
      id-token: write
      contents: read

    steps:
      - name: 🧾 Checkout repo
        uses: actions/checkout@v4

      - name: 🔐 Configure AWS credentials using OIDC
        uses: aws-actions/configure-aws-credentials@v3
        with:
          role-to-assume: arn:aws:iam::505787607892:role/GitHubActionsOIDCRole
          aws-region: ${{ env.AWS_REGION }}

      - name: 🐳 Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: 🔨 Build, tag and push Docker image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG -f Dockerfile.web .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

      - name: 🚀 Update ECS service
        run: |
          aws ecs update-service \
            --cluster $ECS_CLUSTER \
            --service $ECS_SERVICE \
            --force-new-deployment \
            --region $AWS_REGION
