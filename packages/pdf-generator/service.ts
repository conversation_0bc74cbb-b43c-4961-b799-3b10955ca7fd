import jsPDF from "jspdf";
import autoTable from "jspdf-autotable"; // Optional plugin for table support
import { JSONDocumentData, PDFOptions } from "./types";

/**
 * Generates a PDF blob from JSON text data
 *
 * @param jsonData - Structured JSON data for the PDF content
 * @param options - Configuration options for the PDF
 * @returns Promise resolving to PDF blob
 */
export default async function generatePDFFromJSON(
  jsonData: JSONDocumentData,
  options: PDFOptions = {}
): Promise<Blob> {
  // Set default options
  const defaultOptions: PDFOptions = {
    format: "a4",
    orientation: "portrait",
    unit: "mm",
    fontName: "helvetica",
    fontSize: 12,
    textColor: "#000000",
    margins: {
      top: 20,
      right: 20,
      bottom: 20,
      left: 20,
    },
    showHeader: false,
    showFooter: false,
    includePageNumbers: false,
  };

  // Merge options
  const mergedOptions: any = { ...defaultOptions, ...options };

  // Initialize PDF document
  const doc = new jsPDF({
    orientation: mergedOptions.orientation,
    unit: mergedOptions.unit,
    format: mergedOptions.format,
  });

  // Set document properties if metadata is provided
  if (jsonData.metadata) {
    if (jsonData.metadata.author)
      doc.setProperties({ author: jsonData.metadata.author });
    if (jsonData.metadata.subject)
      doc.setProperties({ subject: jsonData.metadata.subject });
    if (jsonData.metadata.keywords)
      doc.setProperties({ keywords: jsonData.metadata.keywords });
    if (jsonData.metadata.creator)
      doc.setProperties({ creator: jsonData.metadata.creator });
  }

  // Set default font
  doc.setFont(mergedOptions.fontName || "helvetica");
  doc.setFontSize(mergedOptions.fontSize || 12);
  doc.setTextColor(mergedOptions.textColor || "#000000");

  // Add title if provided
  let yPosition = mergedOptions.margins?.top || 20;

  if (jsonData.title) {
    doc.setFontSize((mergedOptions.fontSize || 12) * 1.5);
    doc.setFont(mergedOptions.fontName || "helvetica", "bold");
    doc.text(jsonData.title, mergedOptions.margins?.left || 20, yPosition);
    yPosition += 10;
    doc.setFontSize(mergedOptions.fontSize || 12);
    doc.setFont(mergedOptions.fontName || "helvetica", "normal");
  }

  // Process content items
  for (const item of jsonData.content) {
    // Apply custom style if available
    if (item.style) {
      if (item.style.fontName) doc.setFont(item.style.fontName);
      if (item.style.fontSize) doc.setFontSize(item.style.fontSize);
      if (item.style.color) doc.setTextColor(item.style.color);
      if (item.style.bold && item.style.italic) {
        doc.setFont(doc.getFont().fontName, "bolditalic");
      } else if (item.style.bold) {
        doc.setFont(doc.getFont().fontName, "bold");
      } else if (item.style.italic) {
        doc.setFont(doc.getFont().fontName, "italic");
      }
    }

    // Process based on content type
    switch (item.type) {
      case "heading":
        // Add some spacing before headings
        yPosition += 5;

        // Determine alignment
        const headingX = mergedOptions.margins?.left || 20;
        const alignment = item.style?.alignment || "left";

        if (alignment === "center") {
          doc.text(
            item.value,
            doc.internal.pageSize.getWidth() / 2,
            yPosition,
            { align: "center" }
          );
        } else if (alignment === "right") {
          doc.text(
            item.value,
            doc.internal.pageSize.getWidth() -
              (mergedOptions.margins?.right || 20),
            yPosition,
            { align: "right" }
          );
        } else {
          doc.text(item.value, headingX, yPosition);
        }

        yPosition += 8;
        break;

      case "paragraph":
        // Split long paragraphs into lines that fit the page width
        const textLines = doc.splitTextToSize(
          item.value,
          doc.internal.pageSize.getWidth() -
            ((mergedOptions.margins?.left || 20) +
              (mergedOptions.margins?.right || 20))
        );

        // Determine alignment
        const paragraphX = mergedOptions.margins?.left || 20;
        const paragraphAlignment = item.style?.alignment || "left";

        if (paragraphAlignment === "center") {
          doc.text(textLines, doc.internal.pageSize.getWidth() / 2, yPosition, {
            align: "center",
          });
        } else if (paragraphAlignment === "right") {
          doc.text(
            textLines,
            doc.internal.pageSize.getWidth() -
              (mergedOptions.margins?.right || 20),
            yPosition,
            { align: "right" }
          );
        } else {
          doc.text(textLines, paragraphX, yPosition);
        }

        // Increment Y position based on number of lines
        yPosition +=
          textLines.length * (mergedOptions.fontSize || 12) * 0.3527 + 5;
        break;

      case "list":
        if (Array.isArray(item.value)) {
          for (let i = 0; i < item.value.length; i++) {
            // Check if we need a new page
            if (
              yPosition >
              doc.internal.pageSize.getHeight() -
                (mergedOptions.margins?.bottom || 20)
            ) {
              doc.addPage();
              yPosition = mergedOptions.margins?.top || 20;

              // Add header if needed on new page
              if (mergedOptions.showHeader && mergedOptions.headerText) {
                doc.setFont(mergedOptions.fontName || "helvetica", "italic");
                doc.setFontSize((mergedOptions.fontSize || 12) * 0.8);
                doc.text(
                  mergedOptions.headerText,
                  doc.internal.pageSize.getWidth() / 2,
                  10,
                  { align: "center" }
                );
                doc.setFont(mergedOptions.fontName || "helvetica", "normal");
                doc.setFontSize(mergedOptions.fontSize || 12);
              }
            }

            // Calculate bullet position
            const bulletX = (mergedOptions.margins?.left || 20) + 5;
            const textX = bulletX + 5;

            // Add bullet point
            doc.text("•", bulletX, yPosition);

            // Split long list items into lines that fit
            const listItemLines = doc.splitTextToSize(
              item.value[i],
              doc.internal.pageSize.getWidth() -
                textX -
                (mergedOptions.margins?.right || 20)
            );

            // Add list item text
            doc.text(listItemLines, textX, yPosition);

            // Increment Y position based on number of lines
            yPosition +=
              listItemLines.length * (mergedOptions.fontSize || 12) * 0.3527 +
              2;
          }

          // Add some spacing after the list
          yPosition += 5;
        }
        break;

      case "table":
        if (
          item.value &&
          Array.isArray(item.value.head) &&
          Array.isArray(item.value.body)
        ) {
          // @ts-ignore - Using the jspdf-autotable plugin
          autoTable(doc, {
            head: [item.value.head],
            body: item.value.body,
            startY: yPosition,
            margin: {
              left: mergedOptions.margins?.left || 20,
              right: mergedOptions.margins?.right || 20,
            },
            styles: {
              fontSize: item.style?.fontSize || mergedOptions.fontSize || 10,
              textColor:
                item.style?.color || mergedOptions.textColor || "#000000",
            },
            headStyles: {
              fillColor: [240, 240, 240],
              textColor: "#000000",
              fontStyle: "bold",
            },
          });

          // @ts-ignore - Update position after table
          yPosition = doc.lastAutoTable.finalY + 10;
        }
        break;

      case "image":
        if (item.value && item.value.url) {
          try {
            // For local development, you might need to handle base64 images differently
            // This is a simplified example - in real applications, you'd need to handle async loading
            // or use a pre-loaded image

            const imgWidth = item.value.width || 100;
            const imgHeight = item.value.height || 100;

            // Determine alignment
            let imgX = mergedOptions.margins?.left || 20;
            const imgAlignment = item.style?.alignment || "left";

            if (imgAlignment === "center") {
              imgX = (doc.internal.pageSize.getWidth() - imgWidth) / 2;
            } else if (imgAlignment === "right") {
              imgX =
                doc.internal.pageSize.getWidth() -
                imgWidth -
                (mergedOptions.margins?.right || 20);
            }

            doc.addImage(
              item.value.url,
              "PNG",
              imgX,
              yPosition,
              imgWidth,
              imgHeight,
              "",
              "FAST"
            );
            yPosition += imgHeight + 10;
          } catch (error) {
            console.error("Error adding image to PDF:", error);
          }
        }
        break;

      default:
        // Unknown content type - skip
        break;
    }

    // Reset to default styles after processing this item
    doc.setFont(mergedOptions.fontName || "helvetica", "normal");
    doc.setFontSize(mergedOptions.fontSize || 12);
    doc.setTextColor(mergedOptions.textColor || "#000000");

    // Check if we need a new page for the next item
    if (
      yPosition >
      doc.internal.pageSize.getHeight() -
        (mergedOptions.margins?.bottom || 20) -
        20
    ) {
      doc.addPage();
      yPosition = mergedOptions.margins?.top || 20;

      // Add header if needed on new page
      if (mergedOptions.showHeader && mergedOptions.headerText) {
        doc.setFont(mergedOptions.fontName || "helvetica", "italic");
        doc.setFontSize((mergedOptions.fontSize || 12) * 0.8);
        doc.text(
          mergedOptions.headerText,
          doc.internal.pageSize.getWidth() / 2,
          10,
          { align: "center" }
        );
        doc.setFont(mergedOptions.fontName || "helvetica", "normal");
        doc.setFontSize(mergedOptions.fontSize || 12);
      }
    }
  }

  // Add footer with page numbers if requested
  if (
    mergedOptions.includePageNumbers ||
    (mergedOptions.showFooter && mergedOptions.footerText)
  ) {
    const totalPages = doc.internal.pages.length;

    for (let i = 1; i <= totalPages; i++) {
      doc.setPage(i);

      // Add footer text if provided
      if (mergedOptions.showFooter && mergedOptions.footerText) {
        doc.setFont(mergedOptions.fontName || "helvetica", "italic");
        doc.setFontSize((mergedOptions.fontSize || 12) * 0.8);
        doc.text(
          mergedOptions.footerText,
          doc.internal.pageSize.getWidth() / 2,
          doc.internal.pageSize.getHeight() - 10,
          { align: "center" }
        );
      }

      // Add page numbers if requested
      if (mergedOptions.includePageNumbers) {
        doc.setFont(mergedOptions.fontName || "helvetica", "normal");
        doc.setFontSize((mergedOptions.fontSize || 12) * 0.8);
        doc.text(
          `Page ${i} of ${totalPages}`,
          doc.internal.pageSize.getWidth() -
            (mergedOptions.margins?.right || 20),
          doc.internal.pageSize.getHeight() - 10,
          { align: "right" }
        );
      }
    }
  }

  // Generate PDF blob
  return doc.output("blob");
}

/**
 * Example usage:
 *
 * const jsonData: JSONDocumentData = {
 *   title: "Sample Report",
 *   metadata: {
 *     author: "John Doe",
 *     subject: "PDF Generation",
 *     keywords: "pdf,json,typescript",
 *     creator: "My Application"
 *   },
 *   content: [
 *     {
 *       type: "heading",
 *       value: "Introduction",
 *       style: { fontSize: 18, bold: true, alignment: "center" }
 *     },
 *     {
 *       type: "paragraph",
 *       value: "This is a sample document generated from JSON data...",
 *       style: { alignment: "left" }
 *     },
 *     {
 *       type: "list",
 *       value: ["Item 1", "Item 2", "Item 3"]
 *     },
 *     {
 *       type: "table",
 *       value: {
 *         head: ["Name", "Age", "City"],
 *         body: [
 *           ["John", "30", "New York"],
 *           ["Alice", "25", "London"],
 *           ["Bob", "40", "Paris"]
 *         ]
 *       }
 *     }
 *   ]
 * };
 *
 * async function saveAsPDF() {
 *   const pdfBlob = await generatePDFFromJSON(jsonData, {
 *     includePageNumbers: true,
 *     showHeader: true,
 *     headerText: "Confidential Report"
 *   });
 *
 *   // Create a download link
 *   const url = URL.createObjectURL(pdfBlob);
 *   const link = document.createElement('a');
 *   link.href = url;
 *   link.download = 'document.pdf';
 *   link.click();
 * }
 */
