/**
 * Configuration options for PDF generation
 */
export interface PDFOptions {
  // Document settings
  format?: string; // 'a4', 'letter', etc.
  orientation?: "portrait" | "landscape";
  unit?: "pt" | "mm" | "cm" | "in";

  // Text styling
  fontName?: string;
  fontSize?: number;
  textColor?: string; // Hex color code

  // Layout
  margins?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };

  // Header/footer
  showHeader?: boolean;
  headerText?: string;
  showFooter?: boolean;
  footerText?: string;

  // Page numbering
  includePageNumbers?: boolean;
}

/**
 * Expected structure of the input JSON data
 */
export interface JSONDocumentData {
  title?: string;
  metadata?: {
    author?: string;
    subject?: string;
    keywords?: string;
    creator?: string;
  };
  content: Array<{
    type: "heading" | "paragraph" | "list" | "table" | "image";
    value: any; // Content specific to type
    style?: {
      fontName?: string;
      fontSize?: number;
      alignment?: "left" | "center" | "right";
      bold?: boolean;
      italic?: boolean;
      underline?: boolean;
      color?: string;
    };
  }>;
}
