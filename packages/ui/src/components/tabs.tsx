"use client";

import * as React from "react";
import * as TabsPrimitive from "@radix-ui/react-tabs";
import { motion } from "framer-motion";

import { cn } from "@workspace/ui/lib/utils";

function Tabs({
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.Root>) {
  return (
    <TabsPrimitive.Root
      data-slot="tabs"
      className={cn("flex flex-col gap-2", className)}
      {...props}
    />
  );
}

function TabsList({
  className,
  children,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.List>) {
  const [activeTabBounds, setActiveTabBounds] = React.useState<{
    left: number;
    width: number;
  } | null>(null);

  const listRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    const updateActiveTabBounds = () => {
      if (!listRef.current) return;

      const activeTab = listRef.current.querySelector(
        '[data-state="active"]'
      ) as HTMLElement;
      if (!activeTab) return;

      const listRect = listRef.current.getBoundingClientRect();
      const activeRect = activeTab.getBoundingClientRect();

      setActiveTabBounds({
        left: activeRect.left - listRect.left,
        width: activeRect.width,
      });
    };

    updateActiveTabBounds();

    // Use MutationObserver to watch for data-state changes
    const observer = new MutationObserver(updateActiveTabBounds);
    if (listRef.current) {
      observer.observe(listRef.current, {
        attributes: true,
        attributeFilter: ["data-state"],
        subtree: true,
      });
    }

    return () => observer.disconnect();
  }, [children]);

  return (
    <React.Fragment>
      <TabsPrimitive.List
        ref={listRef}
        data-slot="tabs-list"
        className={cn(
          "text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px] relative",
          className
        )}
        {...props}
      >
        {activeTabBounds && (
          <motion.div
            className="absolute top-[3px] bottom-[3px] bg-background dark:bg-input/30 rounded-md shadow-sm border dark:border-input"
            initial={false}
            animate={{
              left: activeTabBounds.left,
              width: activeTabBounds.width,
            }}
            transition={{
              type: "spring",
              stiffness: 500,
              damping: 30,
            }}
          />
        )}
        {children}
      </TabsPrimitive.List>
      <div className="flex bg-zinc-300 dark:bg-zinc-300 h-[.8px]" />
    </React.Fragment>
  );
}

function TabsTrigger({
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {
  return (
    <TabsPrimitive.Trigger
      data-slot="tabs-trigger"
      className={cn(
        "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring text-foreground dark:text-muted-foreground data-[state=active]:text-foreground dark:data-[state=active]:text-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 relative z-10",
        className
      )}
      {...props}
    />
  );
}

function TabsContent({
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.Content>) {
  return (
    <TabsPrimitive.Content
      data-slot="tabs-content"
      className={cn("flex-1 outline-none", className)}
      {...props}
    />
  );
}

export { Tabs, TabsList, TabsTrigger, TabsContent };
