"use client";

/**
 * Combobox - A compound, composable combobox component following shadcn standards
 *
 * Features:
 * - Integrated ScrollArea for smooth mouse wheel scrolling
 * - Maximum height of 300px with automatic scrolling
 * - Keyboard navigation support
 * - Search functionality with real-time filtering
 * - Accessible design with proper ARIA attributes
 *
 * @example
 * ```tsx
 * const [open, setOpen] = React.useState(false);
 * const [value, setValue] = React.useState("");
 *
 * const frameworks = [
 *   { value: "next.js", label: "Next.js" },
 *   { value: "sveltekit", label: "SvelteKit" },
 *   { value: "nuxt.js", label: "Nuxt.js" },
 * ];
 *
 * <Combobox open={open} onOpenChange={setOpen}>
 *   <ComboboxTrigger>
 *     {value
 *       ? frameworks.find((framework) => framework.value === value)?.label
 *       : "Select framework..."}
 *   </ComboboxTrigger>
 *   <ComboboxContent>
 *     <ComboboxCommand>
 *       <ComboboxInput placeholder="Search framework..." />
 *       <ComboboxList>
 *         <ComboboxEmpty>No framework found.</ComboboxEmpty>
 *         <ComboboxGroup>
 *           {frameworks.map((framework) => (
 *             <ComboboxItem
 *               key={framework.value}
 *               value={framework.value}
 *               onSelect={(currentValue) => {
 *                 setValue(currentValue === value ? "" : currentValue);
 *                 setOpen(false);
 *               }}
 *             >
 *               {framework.label}
 *               <ComboboxItemIndicator isSelected={value === framework.value} />
 *             </ComboboxItem>
 *           ))}
 *         </ComboboxGroup>
 *       </ComboboxList>
 *     </ComboboxCommand>
 *   </ComboboxContent>
 * </Combobox>
 * ```
 */

import * as React from "react";
import { CheckIcon, ChevronsUpDownIcon } from "lucide-react";

import { cn } from "@workspace/ui/lib/utils";
import { Button } from "@workspace/ui/components/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@workspace/ui/components/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@workspace/ui/components/popover";
import { ScrollArea } from "@workspace/ui/components/scroll-area";

function Combobox({ ...props }: React.ComponentProps<typeof Popover>) {
  return <Popover data-slot="combobox" {...props} />;
}

function ComboboxTrigger({
  className,
  children,
  placeholder = "Select option...",
  ...props
}: React.ComponentProps<typeof PopoverTrigger> & {
  placeholder?: string;
}) {
  return (
    <PopoverTrigger data-slot="combobox-trigger" asChild {...props}>
      <Button
        variant="outline"
        role="combobox"
        className={cn("w-full justify-between", className)}
      >
        {children || placeholder}
        <ChevronsUpDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
      </Button>
    </PopoverTrigger>
  );
}

function ComboboxContent({
  className,
  align = "start",
  sideOffset = 4,
  ...props
}: React.ComponentProps<typeof PopoverContent>) {
  return (
    <PopoverContent
      data-slot="combobox-content"
      align={align}
      sideOffset={sideOffset}
      className={cn("w-[--radix-popover-trigger-width] p-0", className)}
      {...props}
    />
  );
}

function ComboboxCommand({
  className,
  ...props
}: React.ComponentProps<typeof Command>) {
  return (
    <Command
      data-slot="combobox-command"
      className={cn("", className)}
      {...props}
    />
  );
}

function ComboboxInput({
  className,
  placeholder = "Search...",
  ...props
}: React.ComponentProps<typeof CommandInput>) {
  return (
    <CommandInput
      data-slot="combobox-input"
      placeholder={placeholder}
      className={cn("", className)}
      {...props}
    />
  );
}

function ComboboxList({
  className,
  ...props
}: React.ComponentProps<typeof CommandList>) {
  return (
    <CommandList
      data-slot="combobox-list"
      className={cn("", className)}
      {...props}
    />
  );
}

function ComboboxEmpty({
  className,
  children = "No results found.",
  ...props
}: React.ComponentProps<typeof CommandEmpty>) {
  return (
    <CommandEmpty
      data-slot="combobox-empty"
      className={cn("", className)}
      {...props}
    >
      {children}
    </CommandEmpty>
  );
}

function ComboboxGroup({
  className,
  ...props
}: React.ComponentProps<typeof CommandGroup>) {
  return (
    <CommandGroup
      data-slot="combobox-group"
      className={cn("", className)}
      {...props}
    />
  );
}

function ComboboxItem({
  className,
  children,
  ...props
}: React.ComponentProps<typeof CommandItem>) {
  return (
    <CommandItem
      data-slot="combobox-item"
      className={cn(
        "relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none pointer-events-auto data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50",
        className
      )}
      {...props}
    >
      {children}
    </CommandItem>
  );
}

function ComboboxItemIndicator({
  className,
  isSelected = false,
  ...props
}: React.ComponentProps<"span"> & {
  isSelected?: boolean;
}) {
  return (
    <span
      data-slot="combobox-item-indicator"
      className={cn(
        "absolute right-2 flex h-3.5 w-3.5 items-center justify-center",
        className
      )}
      {...props}
    >
      <CheckIcon
        className={cn("h-4 w-4", isSelected ? "opacity-100" : "opacity-0")}
      />
    </span>
  );
}

function ComboboxSeparator({
  className,
  ...props
}: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="combobox-separator"
      className={cn("bg-border -mx-1 my-1 h-px", className)}
      {...props}
    />
  );
}

export {
  Combobox,
  ComboboxTrigger,
  ComboboxContent,
  ComboboxCommand,
  ComboboxInput,
  ComboboxList,
  ComboboxEmpty,
  ComboboxGroup,
  ComboboxItem,
  ComboboxItemIndicator,
  ComboboxSeparator,
};
