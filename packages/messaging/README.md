# Shamwaa Logistics Messaging Service

Twilio-powered SMS and WhatsApp messaging service for logistics operations.

## Features

- 📱 SMS messaging with MMS support
- 💬 WhatsApp messaging with media and templates
- 📊 Message status tracking
- 🔄 Bulk messaging with rate limiting
- 📋 Pre-built logistics message templates
- 🚨 Emergency and urgent notifications
- 🎯 Customer service templates

## Installation

```bash
npm install @workspace/messaging
```

## Environment Variables

Add these to your `.env` file:

```env
TWILIO_ACCOUNT_SID=your_account_sid
TWILIO_AUTH_TOKEN=your_auth_token
TWILIO_PHONE_NUMBER=+**********
TWILIO_WHATSAPP_NUMBER=whatsapp:+**********
```

## Quick Start

```typescript
import { MessagingService, LogisticsTemplates } from '@workspace/messaging';

// Initialize the service
const messaging = new MessagingService({
  accountSid: process.env.TWILIO_ACCOUNT_SID!,
  authToken: process.env.TWILIO_AUTH_TOKEN!,
  phoneNumber: process.env.TWILIO_PHONE_NUMBER!,
  whatsappNumber: process.env.TWILIO_WHATSAPP_NUMBER!,
});

// Send SMS
const smsResult = await messaging.sendSMS({
  to: '+**********',
  message: 'Your shipment is on the way!',
});

// Send WhatsApp message
const whatsappResult = await messaging.sendWhatsApp({
  to: '+**********',
  message: LogisticsTemplates.shipmentCreated('SH123456', 'New York', 'Los Angeles'),
});
```

## Usage Examples

### Basic SMS

```typescript
const result = await messaging.sendSMS({
  to: '+**********',
  message: 'Your cargo has been delivered successfully!',
});

if (result.success) {
  console.log('Message sent:', result.messageId);
} else {
  console.error('Failed to send:', result.error);
}
```

### WhatsApp with Media

```typescript
const result = await messaging.sendWhatsApp({
  to: '+**********',
  message: 'Your delivery confirmation with photo:',
  mediaUrl: ['https://example.com/delivery-photo.jpg'],
});
```

### Using Logistics Templates

```typescript
import { LogisticsTemplates, WhatsAppTemplates } from '@workspace/messaging';

// Shipment notification
const message = LogisticsTemplates.shipmentCreated(
  'SH123456',
  'New York',
  'Los Angeles'
);

// Rich WhatsApp message
const richMessage = WhatsAppTemplates.shipmentStatusWithLocation(
  'SH123456',
  'In Transit',
  'Chicago, IL',
  '2024-01-15 14:30',
  'https://maps.google.com/...'
);
```

### Bulk Messaging

```typescript
const recipients = ['+**********', '+0987654321', '+1122334455'];

const bulkResult = await messaging.sendBulkSMS({
  recipients,
  message: 'Important update about your shipments...',
  batchSize: 5,
  delayBetweenBatches: 2000, // 2 seconds between batches
});

console.log(`Sent: ${bulkResult.totalSent}, Failed: ${bulkResult.totalFailed}`);
```

### Message Status Tracking

```typescript
const status = await messaging.getMessageStatus('SM**********abcdef');

if (status) {
  console.log('Message status:', status.status);
  console.log('Delivered at:', status.dateSent);
}
```

## Template Categories

### Logistics Templates
- `shipmentCreated` - New shipment notification
- `shipmentInTransit` - Location updates
- `shipmentDelivered` - Delivery confirmation
- `shipmentDelayed` - Delay notifications
- `invoiceGenerated` - Invoice alerts
- `paymentReceived` - Payment confirmations
- `documentReady` - Document notifications
- `releaseAuthorization` - Cargo release codes

### WhatsApp Rich Templates
- `shipmentStatusWithLocation` - Status with map links
- `invoiceWithPaymentOptions` - Invoice with payment links
- `cargoReleaseWithQR` - Release codes with QR codes
- `deliveryConfirmation` - Delivery with photos

### Urgent Templates
- `cargoDamage` - Damage reports
- `securityAlert` - Security notifications
- `weatherDelay` - Weather-related delays

### Customer Service Templates
- `welcome` - New customer welcome
- `supportTicket` - Support ticket creation
- `feedbackRequest` - Service feedback requests

## Integration with Shamwaa Logistics

### In Services (Backend)

```typescript
// services/api/src/messaging/messaging.service.ts
import { MessagingService } from '@workspace/messaging';

@Injectable()
export class LogisticsMessagingService {
  private messaging: MessagingService;

  constructor() {
    this.messaging = new MessagingService({
      accountSid: process.env.TWILIO_ACCOUNT_SID!,
      authToken: process.env.TWILIO_AUTH_TOKEN!,
      phoneNumber: process.env.TWILIO_PHONE_NUMBER!,
      whatsappNumber: process.env.TWILIO_WHATSAPP_NUMBER!,
    });
  }

  async notifyShipmentUpdate(phoneNumber: string, trackingNumber: string, status: string) {
    return await this.messaging.sendSMS({
      to: phoneNumber,
      message: LogisticsTemplates.shipmentInTransit(trackingNumber, status),
    });
  }
}
```

### In Admin App

```typescript
// apps/admin/lib/messaging/index.ts
import { MessagingService, LogisticsTemplates } from '@workspace/messaging';

const messaging = new MessagingService({
  accountSid: process.env.NEXT_PUBLIC_TWILIO_ACCOUNT_SID!,
  authToken: process.env.TWILIO_AUTH_TOKEN!,
  phoneNumber: process.env.TWILIO_PHONE_NUMBER!,
  whatsappNumber: process.env.TWILIO_WHATSAPP_NUMBER!,
});

export { messaging, LogisticsTemplates };
```

## Error Handling

```typescript
try {
  const result = await messaging.sendSMS({
    to: '+**********',
    message: 'Test message',
  });

  if (!result.success) {
    // Handle specific error
    console.error('Messaging error:', result.error);
  }
} catch (error) {
  // Handle network or service errors
  console.error('Service error:', error);
}
```

## Rate Limiting

The service includes built-in rate limiting for bulk operations:
- Default batch size: 10 messages
- Default delay between batches: 1 second
- Configurable per operation

## Security Notes

- Never expose auth tokens in client-side code
- Use environment variables for all credentials
- Validate phone numbers before sending
- Implement webhook verification for status updates

## Support

For issues or questions, contact the Shamwaa Logistics development team.
