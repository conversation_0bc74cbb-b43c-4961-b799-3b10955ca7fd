/**
 * Shamwaa Logistics Messaging Package
 * Twilio SMS and WhatsApp Integration
 */

// Main service class
export { MessagingService } from "./service";

// Type definitions
export type {
  TwilioConfig,
  SMSOptions,
  WhatsAppOptions,
  MessageResponse,
  MessageStatus,
  BulkMessageOptions,
  BulkMessageResponse,
  LogisticsMessageTemplates,
  TwilioWebhookEvent,
} from "./types";

// Message templates
export {
  LogisticsTemplates,
  WhatsAppTemplates,
  UrgentTemplates,
  CustomerServiceTemplates,
} from "./templates";

// Import the service for re-export
import { MessagingService } from "./service";
import type { TwilioConfig } from "./types";

// Utility functions for quick setup
export const createMessagingService = (config: TwilioConfig) => {
  return new MessagingService(config);
};

// Default export for convenience
export default MessagingService;
