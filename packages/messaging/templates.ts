/**
 * Predefined Message Templates for Shamwaa Logistics
 * SMS and WhatsApp message templates for common logistics operations
 */

import type { LogisticsMessageTemplates } from './types';

export const LogisticsTemplates: LogisticsMessageTemplates = {
  /**
   * Shipment created notification
   */
  shipmentCreated: (trackingNumber: string, origin: string, destination: string): string => {
    return `🚚 Shipment Created!\n\nTracking: ${trackingNumber}\nFrom: ${origin}\nTo: ${destination}\n\nYour cargo is now in our system and will be processed shortly.\n\n- Shamwaa Logistics`;
  },

  /**
   * Shipment in transit notification
   */
  shipmentInTransit: (trackingNumber: string, currentLocation: string): string => {
    return `📍 Shipment Update!\n\nTracking: ${trackingNumber}\nCurrent Location: ${currentLocation}\n\nYour cargo is on the move and progressing as scheduled.\n\n- Shamwaa Logistics`;
  },

  /**
   * Shipment delivered notification
   */
  shipmentDelivered: (trackingNumber: string, deliveryTime: string): string => {
    return `✅ Delivery Confirmed!\n\nTracking: ${trackingNumber}\nDelivered: ${deliveryTime}\n\nYour cargo has been successfully delivered. Thank you for choosing Shamwaa Logistics!\n\n- Shamwaa Logistics`;
  },

  /**
   * Shipment delayed notification
   */
  shipmentDelayed: (trackingNumber: string, newETA: string, reason: string): string => {
    return `⚠️ Shipment Delay Notice\n\nTracking: ${trackingNumber}\nNew ETA: ${newETA}\nReason: ${reason}\n\nWe apologize for the inconvenience and are working to minimize further delays.\n\n- Shamwaa Logistics`;
  },

  /**
   * Invoice generated notification
   */
  invoiceGenerated: (invoiceNumber: string, amount: string, dueDate: string): string => {
    return `💰 Invoice Generated\n\nInvoice: ${invoiceNumber}\nAmount: ${amount}\nDue Date: ${dueDate}\n\nPlease review and process payment by the due date.\n\n- Shamwaa Logistics`;
  },

  /**
   * Payment received confirmation
   */
  paymentReceived: (invoiceNumber: string, amount: string): string => {
    return `✅ Payment Received\n\nInvoice: ${invoiceNumber}\nAmount: ${amount}\n\nThank you! Your payment has been processed successfully.\n\n- Shamwaa Logistics`;
  },

  /**
   * Document ready notification
   */
  documentReady: (documentType: string, trackingNumber: string, downloadUrl?: string): string => {
    const baseMessage = `📄 Document Ready\n\nType: ${documentType}\nTracking: ${trackingNumber}\n\nYour document is ready for collection.`;
    
    if (downloadUrl) {
      return `${baseMessage}\n\nDownload: ${downloadUrl}\n\n- Shamwaa Logistics`;
    }
    
    return `${baseMessage}\n\nPlease contact us to arrange collection.\n\n- Shamwaa Logistics`;
  },

  /**
   * Release authorization notification
   */
  releaseAuthorization: (releaseCode: string, trackingNumber: string, instructions: string): string => {
    return `🔓 Release Authorization\n\nRelease Code: ${releaseCode}\nTracking: ${trackingNumber}\n\nInstructions: ${instructions}\n\nPresent this code for cargo release.\n\n- Shamwaa Logistics`;
  },
};

/**
 * WhatsApp-specific templates with rich formatting
 */
export const WhatsAppTemplates = {
  /**
   * Shipment status with location tracking
   */
  shipmentStatusWithLocation: (
    trackingNumber: string,
    status: string,
    location: string,
    estimatedDelivery: string,
    mapUrl?: string
  ): string => {
    let message = `🚚 *Shipment Status Update*\n\n`;
    message += `📦 *Tracking:* ${trackingNumber}\n`;
    message += `📍 *Status:* ${status}\n`;
    message += `🌍 *Location:* ${location}\n`;
    message += `⏰ *Est. Delivery:* ${estimatedDelivery}\n\n`;
    
    if (mapUrl) {
      message += `🗺️ Track on map: ${mapUrl}\n\n`;
    }
    
    message += `_Shamwaa Logistics - Your trusted shipping partner_`;
    return message;
  },

  /**
   * Invoice with payment options
   */
  invoiceWithPaymentOptions: (
    invoiceNumber: string,
    amount: string,
    dueDate: string,
    paymentUrl?: string
  ): string => {
    let message = `💰 *Invoice Generated*\n\n`;
    message += `📄 *Invoice:* ${invoiceNumber}\n`;
    message += `💵 *Amount:* ${amount}\n`;
    message += `📅 *Due Date:* ${dueDate}\n\n`;
    
    if (paymentUrl) {
      message += `💳 *Pay Online:* ${paymentUrl}\n\n`;
    }
    
    message += `_Please ensure payment is made by the due date to avoid delays._\n\n`;
    message += `_Shamwaa Logistics_`;
    return message;
  },

  /**
   * Cargo release with QR code
   */
  cargoReleaseWithQR: (
    releaseCode: string,
    trackingNumber: string,
    location: string,
    qrCodeUrl?: string
  ): string => {
    let message = `🔓 *Cargo Release Authorization*\n\n`;
    message += `🔑 *Release Code:* ${releaseCode}\n`;
    message += `📦 *Tracking:* ${trackingNumber}\n`;
    message += `📍 *Collection Point:* ${location}\n\n`;
    
    if (qrCodeUrl) {
      message += `📱 *QR Code for quick release:*\n${qrCodeUrl}\n\n`;
    }
    
    message += `⚠️ *Important:* Present this code and valid ID for cargo collection.\n\n`;
    message += `_Shamwaa Logistics_`;
    return message;
  },

  /**
   * Delivery confirmation with photo
   */
  deliveryConfirmation: (
    trackingNumber: string,
    deliveryTime: string,
    recipient: string,
    photoUrl?: string
  ): string => {
    let message = `✅ *Delivery Confirmed*\n\n`;
    message += `📦 *Tracking:* ${trackingNumber}\n`;
    message += `⏰ *Delivered:* ${deliveryTime}\n`;
    message += `👤 *Received by:* ${recipient}\n\n`;
    
    if (photoUrl) {
      message += `📸 *Delivery Photo:* ${photoUrl}\n\n`;
    }
    
    message += `🎉 *Thank you for choosing Shamwaa Logistics!*\n\n`;
    message += `⭐ _Rate our service and help us improve_`;
    return message;
  },
};

/**
 * Emergency and urgent notification templates
 */
export const UrgentTemplates = {
  /**
   * Cargo damage notification
   */
  cargoDamage: (trackingNumber: string, damageDescription: string, contactNumber: string): string => {
    return `🚨 *URGENT: Cargo Damage Report*\n\n📦 *Tracking:* ${trackingNumber}\n🔍 *Damage:* ${damageDescription}\n\n📞 *Immediate Contact Required:* ${contactNumber}\n\nPlease contact us immediately to discuss next steps.\n\n_Shamwaa Logistics_`;
  },

  /**
   * Security alert
   */
  securityAlert: (trackingNumber: string, alertType: string, location: string): string => {
    return `🔒 *SECURITY ALERT*\n\n📦 *Tracking:* ${trackingNumber}\n⚠️ *Alert:* ${alertType}\n📍 *Location:* ${location}\n\nImmediate attention required. Our security team has been notified.\n\n_Shamwaa Logistics Security_`;
  },

  /**
   * Weather delay notification
   */
  weatherDelay: (trackingNumber: string, weatherCondition: string, newETA: string): string => {
    return `🌧️ *Weather Delay Notice*\n\n📦 *Tracking:* ${trackingNumber}\n🌪️ *Condition:* ${weatherCondition}\n⏰ *New ETA:* ${newETA}\n\nSafety is our priority. We'll resume operations as soon as conditions improve.\n\n_Shamwaa Logistics_`;
  },
};

/**
 * Customer service templates
 */
export const CustomerServiceTemplates = {
  /**
   * Welcome message for new customers
   */
  welcome: (customerName: string, accountNumber: string): string => {
    return `🎉 *Welcome to Shamwaa Logistics!*\n\nHi ${customerName},\n\nYour account is now active!\n📋 *Account:* ${accountNumber}\n\n🚚 We're here to handle all your shipping needs with care and reliability.\n\n📞 Need help? Contact our support team anytime.\n\n_Welcome aboard!_`;
  },

  /**
   * Support ticket created
   */
  supportTicket: (ticketNumber: string, issue: string): string => {
    return `🎫 *Support Ticket Created*\n\n🔢 *Ticket:* ${ticketNumber}\n📝 *Issue:* ${issue}\n\nOur team will respond within 24 hours. Thank you for your patience.\n\n_Shamwaa Logistics Support_`;
  },

  /**
   * Feedback request
   */
  feedbackRequest: (trackingNumber: string, surveyUrl?: string): string => {
    let message = `⭐ *How was your experience?*\n\n📦 *Shipment:* ${trackingNumber}\n\nWe'd love to hear about your experience with our service.\n\n`;
    
    if (surveyUrl) {
      message += `📝 *Quick Survey:* ${surveyUrl}\n\n`;
    }
    
    message += `Your feedback helps us serve you better!\n\n_Shamwaa Logistics_`;
    return message;
  },
};
