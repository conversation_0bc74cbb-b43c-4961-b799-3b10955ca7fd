/**
 * Twilio Messaging Service for SMS and WhatsApp
 * Shamwaa Logistics Management System
 */

import { Twilio } from "twilio";
import type {
  TwilioConfig,
  SMSOptions,
  WhatsAppOptions,
  MessageResponse,
  MessageStatus,
  BulkMessageOptions,
  BulkMessageResponse,
  LogisticsMessageTemplates,
} from "./types";

export class MessagingService {
  private client: Twilio;
  private config: TwilioConfig;

  constructor(config: TwilioConfig) {
    this.config = config;
    this.client = new Twilio(config.accountSid, config.authToken);
  }

  /**
   * Send SMS message
   */
  async sendSMS(options: SMSOptions): Promise<MessageResponse> {
    try {
      const message = await this.client.messages.create({
        body: options.message,
        from: this.config.phoneNumber,
        to: options.to,
        ...(options.mediaUrl && { mediaUrl: options.mediaUrl }),
      });

      return {
        success: true,
        messageId: message.sid,
        status: message.status,
        to: message.to,
        from: message.from,
      };
    } catch (error: any) {
      console.error("❌ Failed to send SMS:", error);
      return {
        success: false,
        error: error.message || "Failed to send SMS",
      };
    }
  }

  /**
   * Send WhatsApp message
   */
  async sendWhatsApp(options: WhatsAppOptions): Promise<MessageResponse> {
    try {
      const messageData: any = {
        body: options.message,
        from: this.config.whatsappNumber,
        to: options.to.startsWith("whatsapp:")
          ? options.to
          : `whatsapp:${options.to}`,
      };

      // Add media URLs if provided
      if (options.mediaUrl && options.mediaUrl.length > 0) {
        messageData.mediaUrl = options.mediaUrl;
      }

      // Use template if provided
      if (options.templateSid) {
        messageData.contentSid = options.templateSid;
        if (options.templateParameters) {
          messageData.contentVariables = JSON.stringify(
            options.templateParameters
          );
        }
      }

      const message = await this.client.messages.create(messageData);

      return {
        success: true,
        messageId: message.sid,
        status: message.status,
        to: message.to,
        from: message.from,
      };
    } catch (error: any) {
      console.error("❌ Failed to send WhatsApp message:", error);
      return {
        success: false,
        error: error.message || "Failed to send WhatsApp message",
      };
    }
  }

  /**
   * Get message status
   */
  async getMessageStatus(messageId: string): Promise<MessageStatus | null> {
    try {
      const message = await this.client.messages(messageId).fetch();

      return {
        messageId: message.sid,
        status: message.status as any,
        errorCode: message.errorCode?.toString(),
        errorMessage: message.errorMessage || undefined,
        dateCreated: message.dateCreated,
        dateSent: message.dateSent,
        dateUpdated: message.dateUpdated,
      };
    } catch (error: any) {
      console.error("❌ Failed to get message status:", error);
      return null;
    }
  }

  /**
   * Send bulk SMS messages
   */
  async sendBulkSMS(options: BulkMessageOptions): Promise<BulkMessageResponse> {
    const batchSize = options.batchSize || 10;
    const delay = options.delayBetweenBatches || 1000;
    const results: MessageResponse[] = [];
    const errors: string[] = [];
    let totalSent = 0;
    let totalFailed = 0;

    // Process recipients in batches
    for (let i = 0; i < options.recipients.length; i += batchSize) {
      const batch = options.recipients.slice(i, i + batchSize);

      // Send messages in parallel for current batch
      const batchPromises = batch.map(async (recipient) => {
        const result = await this.sendSMS({
          to: recipient,
          message: options.message,
          mediaUrl: options.mediaUrl,
        });

        if (result.success) {
          totalSent++;
        } else {
          totalFailed++;
          errors.push(`${recipient}: ${result.error}`);
        }

        return result;
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // Add delay between batches (except for the last batch)
      if (i + batchSize < options.recipients.length) {
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }

    return {
      success: totalFailed === 0,
      totalSent,
      totalFailed,
      results,
      errors,
    };
  }

  /**
   * Send bulk WhatsApp messages
   */
  async sendBulkWhatsApp(
    options: BulkMessageOptions
  ): Promise<BulkMessageResponse> {
    const batchSize = options.batchSize || 10;
    const delay = options.delayBetweenBatches || 1000;
    const results: MessageResponse[] = [];
    const errors: string[] = [];
    let totalSent = 0;
    let totalFailed = 0;

    // Process recipients in batches
    for (let i = 0; i < options.recipients.length; i += batchSize) {
      const batch = options.recipients.slice(i, i + batchSize);

      // Send messages in parallel for current batch
      const batchPromises = batch.map(async (recipient) => {
        const result = await this.sendWhatsApp({
          to: recipient,
          message: options.message,
          mediaUrl: options.mediaUrl,
        });

        if (result.success) {
          totalSent++;
        } else {
          totalFailed++;
          errors.push(`${recipient}: ${result.error}`);
        }

        return result;
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // Add delay between batches (except for the last batch)
      if (i + batchSize < options.recipients.length) {
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }

    return {
      success: totalFailed === 0,
      totalSent,
      totalFailed,
      results,
      errors,
    };
  }

  /**
   * Get current configuration
   */
  getConfig(): Omit<TwilioConfig, "authToken"> {
    return {
      accountSid: this.config.accountSid,
      phoneNumber: this.config.phoneNumber,
      whatsappNumber: this.config.whatsappNumber,
    };
  }

  /**
   * Validate phone number format
   */
  static validatePhoneNumber(phoneNumber: string): boolean {
    // Basic E.164 format validation
    const phoneRegex = /^\+[1-9]\d{1,14}$/;
    return phoneRegex.test(phoneNumber);
  }

  /**
   * Format phone number for WhatsApp
   */
  static formatWhatsAppNumber(phoneNumber: string): string {
    if (phoneNumber.startsWith("whatsapp:")) {
      return phoneNumber;
    }
    return `whatsapp:${phoneNumber}`;
  }
}
