/**
 * Messaging Service Types for Twilio SMS and WhatsApp
 * Shamwaa Logistics Management System
 */

export interface TwilioConfig {
  accountSid: string;
  authToken: string;
  phoneNumber: string; // Twilio phone number for SMS
  whatsappNumber: string; // Twilio WhatsApp number (format: whatsapp:+**********)
}

export interface SMSOptions {
  to: string; // Recipient phone number (format: +**********)
  message: string;
  mediaUrl?: string[]; // Optional media URLs for MMS
}

export interface WhatsAppOptions {
  to: string; // Recipient WhatsApp number (format: whatsapp:+**********)
  message: string;
  mediaUrl?: string[]; // Optional media URLs
  templateSid?: string; // For WhatsApp templates
  templateParameters?: Record<string, string>; // Template parameters
}

export interface MessageResponse {
  success: boolean;
  messageId?: string;
  error?: string;
  status?: string;
  to?: string;
  from?: string;
}

export interface MessageStatus {
  messageId: string;
  status: 'queued' | 'sending' | 'sent' | 'failed' | 'delivered' | 'undelivered' | 'received' | 'read';
  errorCode?: string;
  errorMessage?: string;
  dateCreated?: Date;
  dateSent?: Date;
  dateUpdated?: Date;
}

// Predefined message templates for logistics
export interface LogisticsMessageTemplates {
  shipmentCreated: (trackingNumber: string, origin: string, destination: string) => string;
  shipmentInTransit: (trackingNumber: string, currentLocation: string) => string;
  shipmentDelivered: (trackingNumber: string, deliveryTime: string) => string;
  shipmentDelayed: (trackingNumber: string, newETA: string, reason: string) => string;
  invoiceGenerated: (invoiceNumber: string, amount: string, dueDate: string) => string;
  paymentReceived: (invoiceNumber: string, amount: string) => string;
  documentReady: (documentType: string, trackingNumber: string, downloadUrl?: string) => string;
  releaseAuthorization: (releaseCode: string, trackingNumber: string, instructions: string) => string;
}

// Bulk messaging options
export interface BulkMessageOptions {
  recipients: string[]; // Array of phone numbers
  message: string;
  mediaUrl?: string[];
  batchSize?: number; // Number of messages to send per batch (default: 10)
  delayBetweenBatches?: number; // Delay in milliseconds between batches (default: 1000)
}

export interface BulkMessageResponse {
  success: boolean;
  totalSent: number;
  totalFailed: number;
  results: MessageResponse[];
  errors: string[];
}

// Webhook event types for message status updates
export interface TwilioWebhookEvent {
  MessageSid: string;
  MessageStatus: string;
  To: string;
  From: string;
  Body?: string;
  NumMedia?: string;
  ErrorCode?: string;
  ErrorMessage?: string;
  DateCreated?: string;
  DateSent?: string;
  DateUpdated?: string;
}
