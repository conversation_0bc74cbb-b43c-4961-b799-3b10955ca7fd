// Email template type definitions for Shamwaa Logistics

// Shared company info interface
export interface CompanyInfo {
  name: string;
  logo?: string;
  address: string;
  phone: string;
  email: string;
  website?: string;
}

export interface EmailTemplateData {
  recipient: {
    name: string;
    email: string;
  };
  company: Partial<CompanyInfo>;
  subject: string;
  preheader?: string;
}

export interface ButtonProps {
  text: string;
  url: string;
  color?: "primary" | "secondary" | "success" | "warning" | "danger";
  size?: "small" | "medium" | "large";
}

export interface ImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  style?: string;
}

export interface SocialLink {
  platform: "facebook" | "twitter" | "linkedin" | "instagram" | "whatsapp";
  url: string;
}

export interface TrackingInfo {
  trackingNumber: string;
  status: string;
  estimatedDelivery?: string;
  currentLocation?: string;
}

export interface ShipmentNotificationData extends EmailTemplateData {
  shipment: {
    id: string;
    trackingNumber: string;
    origin: string;
    destination: string;
    status: string;
    estimatedDelivery?: string;
    items: Array<{
      description: string;
      quantity: number;
      weight: string;
    }>;
  };
  actionUrl?: string | undefined;
}

export interface DeliveryConfirmationData extends EmailTemplateData {
  delivery: {
    trackingNumber: string;
    deliveredAt: string;
    deliveredTo: string;
    signature?: string;
    photos?: string[];
  };
}

export interface InvoiceNotificationData extends EmailTemplateData {
  invoice: {
    number: string;
    amount: string;
    dueDate: string;
    downloadUrl: string;
    items: Array<{
      description: string;
      quantity: number;
      rate: string;
      amount: string;
    }>;
  };
}

export interface WelcomeEmailData extends EmailTemplateData {
  user: {
    name: string;
    role?: string;
  };
  loginUrl: string;
  supportUrl?: string;
}

export interface PasswordResetData extends EmailTemplateData {
  resetUrl: string;
  expiresIn: string;
}

export interface NotificationEmailData extends EmailTemplateData {
  notification: {
    title: string;
    message: string;
    type: "info" | "success" | "warning" | "error";
    actionUrl?: string | undefined;
    actionText?: string | undefined;
  };
}

export interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

export interface TemplateOptions {
  primaryColor?: string;
  secondaryColor?: string;
  backgroundColor?: string;
  textColor?: string;
  linkColor?: string;
  fontFamily?: string;
  maxWidth?: string;
}
