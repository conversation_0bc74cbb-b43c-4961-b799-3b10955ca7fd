// Logistics-specific email templates for Shamwaa Logistics

import { EmailTemplateBuilder, createSimpleTemplate } from "./base-template";
import {
  createButton,
  createStatusBadge,
  createA<PERSON>t,
  BRAND_COLORS,
  TYPOGRAPHY,
} from "./components";
import type {
  EmailTemplate,
  ShipmentNotificationData,
  DeliveryConfirmationData,
  InvoiceNotificationData,
  WelcomeEmailData,
  PasswordResetData,
  NotificationEmailData,
} from "./types";

/**
 * Shipment Status Update Email Template
 */
export const createShipmentNotificationTemplate = (
  data: ShipmentNotificationData
): EmailTemplate => {
  const builder = new EmailTemplateBuilder(data);

  builder
    .addHeading(`Shipment Update: ${data.shipment.trackingNumber}`)
    .addParagraph(`Dear ${data.recipient.name},`)
    .addParagraph(
      `We have an update on your shipment from ${data.shipment.origin} to ${data.shipment.destination}.`
    )
    .addSpacer("20px");

  // Status badge
  builder.addSection(`
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
      <tr>
        <td style="padding: 20px; background-color: ${BRAND_COLORS.backgroundLight}; border-radius: 8px; border: 1px solid ${BRAND_COLORS.border};">
          <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
            <tr>
              <td style="padding-bottom: 12px;">
                <h3 style="margin: 0; font-family: ${TYPOGRAPHY.fontFamily}; font-size: 18px; font-weight: 600; color: ${BRAND_COLORS.text};">
                  Shipment Details
                </h3>
              </td>
            </tr>
            <tr>
              <td style="padding: 8px 0;">
                <strong>Tracking Number:</strong> ${data.shipment.trackingNumber}
              </td>
            </tr>
            <tr>
              <td style="padding: 8px 0;">
                <strong>Status:</strong> ${createStatusBadge(data.shipment.status, "info")}
              </td>
            </tr>
            <tr>
              <td style="padding: 8px 0;">
                <strong>Route:</strong> ${data.shipment.origin} → ${data.shipment.destination}
              </td>
            </tr>
            ${
              data.shipment.estimatedDelivery
                ? `
            <tr>
              <td style="padding: 8px 0;">
                <strong>Estimated Delivery:</strong> ${data.shipment.estimatedDelivery}
              </td>
            </tr>
            `
                : ""
            }
          </table>
        </td>
      </tr>
    </table>
  `);

  // Items list if available
  if (data.shipment.items && data.shipment.items.length > 0) {
    builder.addSpacer("20px");
    builder.addHeading("Items in Shipment", 3);

    const itemsHtml = data.shipment.items
      .map(
        (item) => `
      <tr>
        <td style="padding: 8px 0; border-bottom: 1px solid ${BRAND_COLORS.border};">
          ${item.description}
        </td>
        <td style="padding: 8px 0; border-bottom: 1px solid ${BRAND_COLORS.border}; text-align: center;">
          ${item.quantity}
        </td>
        <td style="padding: 8px 0; border-bottom: 1px solid ${BRAND_COLORS.border}; text-align: right;">
          ${item.weight}
        </td>
      </tr>
    `
      )
      .join("");

    builder.addSection(`
      <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
        <tr>
          <th style="padding: 12px 0; text-align: left; font-family: ${TYPOGRAPHY.fontFamily}; font-size: 14px; font-weight: 600; color: ${BRAND_COLORS.text};">
            Description
          </th>
          <th style="padding: 12px 0; text-align: center; font-family: ${TYPOGRAPHY.fontFamily}; font-size: 14px; font-weight: 600; color: ${BRAND_COLORS.text};">
            Quantity
          </th>
          <th style="padding: 12px 0; text-align: right; font-family: ${TYPOGRAPHY.fontFamily}; font-size: 14px; font-weight: 600; color: ${BRAND_COLORS.text};">
            Weight
          </th>
        </tr>
        ${itemsHtml}
      </table>
    `);
  }

  // Action button if URL provided
  if (data.actionUrl) {
    builder.addSpacer("30px");
    builder.addSection(`
      <div style="text-align: center;">
        ${createButton({ text: "Track Shipment", url: data.actionUrl, color: "primary" })}
      </div>
    `);
  }

  builder.addSpacer("20px");
  builder.addParagraph(
    "If you have any questions about your shipment, please don't hesitate to contact our customer service team."
  );

  return builder.build();
};

/**
 * Delivery Confirmation Email Template
 */
export const createDeliveryConfirmationTemplate = (
  data: DeliveryConfirmationData
): EmailTemplate => {
  const builder = new EmailTemplateBuilder(data);

  builder
    .addHeading("✅ Delivery Confirmed")
    .addParagraph(`Dear ${data.recipient.name},`)
    .addParagraph("Great news! Your shipment has been successfully delivered.")
    .addSpacer("20px");

  // Delivery details
  builder.addSection(`
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
      <tr>
        <td style="padding: 20px; background-color: #F0FFF4; border-radius: 8px; border: 1px solid #9AE6B4;">
          <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
            <tr>
              <td style="padding-bottom: 12px;">
                <h3 style="margin: 0; font-family: ${TYPOGRAPHY.fontFamily}; font-size: 18px; font-weight: 600; color: ${BRAND_COLORS.text};">
                  Delivery Details
                </h3>
              </td>
            </tr>
            <tr>
              <td style="padding: 8px 0;">
                <strong>Tracking Number:</strong> ${data.delivery.trackingNumber}
              </td>
            </tr>
            <tr>
              <td style="padding: 8px 0;">
                <strong>Delivered At:</strong> ${data.delivery.deliveredAt}
              </td>
            </tr>
            <tr>
              <td style="padding: 8px 0;">
                <strong>Delivered To:</strong> ${data.delivery.deliveredTo}
              </td>
            </tr>
            ${
              data.delivery.signature
                ? `
            <tr>
              <td style="padding: 8px 0;">
                <strong>Signature:</strong> ${data.delivery.signature}
              </td>
            </tr>
            `
                : ""
            }
          </table>
        </td>
      </tr>
    </table>
  `);

  builder.addSpacer("20px");
  builder.addParagraph(
    "Thank you for choosing Shamwaa Logistics for your shipping needs. We appreciate your business!"
  );

  return builder.build();
};

/**
 * Invoice Notification Email Template
 */
export const createInvoiceNotificationTemplate = (
  data: InvoiceNotificationData
): EmailTemplate => {
  const builder = new EmailTemplateBuilder(data);

  builder
    .addHeading(`Invoice ${data.invoice.number}`)
    .addParagraph(`Dear ${data.recipient.name},`)
    .addParagraph(
      "Please find your invoice details below. Payment is due by the specified date."
    )
    .addSpacer("20px");

  // Invoice summary
  builder.addSection(`
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
      <tr>
        <td style="padding: 20px; background-color: ${BRAND_COLORS.backgroundLight}; border-radius: 8px; border: 1px solid ${BRAND_COLORS.border};">
          <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
            <tr>
              <td style="padding-bottom: 12px;">
                <h3 style="margin: 0; font-family: ${TYPOGRAPHY.fontFamily}; font-size: 18px; font-weight: 600; color: ${BRAND_COLORS.text};">
                  Invoice Summary
                </h3>
              </td>
            </tr>
            <tr>
              <td style="padding: 8px 0;">
                <strong>Invoice Number:</strong> ${data.invoice.number}
              </td>
            </tr>
            <tr>
              <td style="padding: 8px 0;">
                <strong>Amount:</strong> <span style="font-size: 18px; font-weight: 600; color: ${BRAND_COLORS.primary};">${data.invoice.amount}</span>
              </td>
            </tr>
            <tr>
              <td style="padding: 8px 0;">
                <strong>Due Date:</strong> ${data.invoice.dueDate}
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  `);

  // Download button
  builder.addSpacer("30px");
  builder.addSection(`
    <div style="text-align: center;">
      ${createButton({ text: "Download Invoice", url: data.invoice.downloadUrl, color: "primary" })}
    </div>
  `);

  builder.addSpacer("20px");
  builder.addParagraph(
    "If you have any questions about this invoice, please contact our billing department."
  );

  return builder.build();
};

/**
 * Welcome Email Template
 */
export const createWelcomeEmailTemplate = (
  data: WelcomeEmailData
): EmailTemplate => {
  return createSimpleTemplate(data, {
    heading: `Welcome to Shamwaa Logistics, ${data.user.name}! 🎉`,
    message: `We're excited to have you join our logistics family. Your account has been successfully created and you can now access all our services. ${data.user.role ? `You have been assigned the role of ${data.user.role}.` : ""}`,
    buttonText: "Access Your Dashboard",
    buttonUrl: data.loginUrl,
  });
};

/**
 * Password Reset Email Template
 */
export const createPasswordResetTemplate = (
  data: PasswordResetData
): EmailTemplate => {
  const builder = new EmailTemplateBuilder(data);

  builder
    .addHeading("🔐 Password Reset Request")
    .addParagraph(`Dear ${data.recipient.name},`)
    .addParagraph(
      "You requested to reset your password. Click the button below to set a new password:"
    )
    .addSpacer("30px");

  builder.addSection(`
    <div style="text-align: center;">
      ${createButton({ text: "Reset Password", url: data.resetUrl, color: "danger" })}
    </div>
  `);

  builder.addSpacer("20px");
  builder.addSection(
    createAlert(
      `This link will expire in ${data.expiresIn}. If you didn't request a password reset, please ignore this email.`,
      "warning"
    )
  );

  return builder.build();
};

/**
 * General Notification Email Template
 */
export const createNotificationEmailTemplate = (
  data: NotificationEmailData
): EmailTemplate => {
  const builder = new EmailTemplateBuilder(data);

  builder
    .addHeading(data.notification.title)
    .addParagraph(`Dear ${data.recipient.name},`)
    .addParagraph(data.notification.message)
    .addSpacer("20px");

  if (data.notification.actionUrl && data.notification.actionText) {
    builder.addSection(`
      <div style="text-align: center;">
        ${createButton({
          text: data.notification.actionText,
          url: data.notification.actionUrl,
          color: data.notification.type === "error" ? "danger" : "primary",
        })}
      </div>
    `);
    builder.addSpacer("20px");
  }

  return builder.build();
};
