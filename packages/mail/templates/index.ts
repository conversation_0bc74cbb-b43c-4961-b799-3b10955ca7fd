// Export all email templates and components for Shamwaa Logistics

// Base template system
export * from "./base-template";
export * from "./components";
export * from "./types";

// Logistics-specific templates
export * from "./logistics-templates";

// Template factory functions for common use cases
import type { EmailTemplate } from "./types";
import {
  createShipmentNotificationTemplate,
  createDeliveryConfirmationTemplate,
  createInvoiceNotificationTemplate,
  createWelcomeEmailTemplate,
  createPasswordResetTemplate,
  createNotificationEmailTemplate,
} from "./logistics-templates";

/**
 * Template factory object for easy access to all templates
 */
export const EmailTemplates = {
  // Logistics templates
  shipmentNotification: createShipmentNotificationTemplate,
  deliveryConfirmation: createDeliveryConfirmationTemplate,
  invoiceNotification: createInvoiceNotificationTemplate,

  // Authentication templates
  welcome: createWelcomeEmailTemplate,
  passwordReset: createPasswordResetTemplate,

  // General templates
  notification: createNotificationEmailTemplate,
} as const;

/**
 * Quick template creation functions with common defaults
 */
export const QuickTemplates = {
  /**
   * Create a shipment status update email
   */
  shipmentUpdate: (
    recipientName: string,
    recipientEmail: string,
    trackingNumber: string,
    status: string,
    origin: string,
    destination: string,
    trackingUrl?: string
  ): EmailTemplate => {
    return createShipmentNotificationTemplate({
      recipient: { name: recipientName, email: recipientEmail },
      company: {}, // Will use defaults
      subject: `Shipment Update: ${trackingNumber}`,
      preheader: `Your shipment status has been updated to: ${status}`,
      shipment: {
        id: trackingNumber,
        trackingNumber,
        status,
        origin,
        destination,
        items: [],
      },
      ...(trackingUrl && { actionUrl: trackingUrl }),
    });
  },

  /**
   * Create a delivery confirmation email
   */
  deliveryConfirmed: (
    recipientName: string,
    recipientEmail: string,
    trackingNumber: string,
    deliveredAt: string,
    deliveredTo: string
  ): EmailTemplate => {
    return createDeliveryConfirmationTemplate({
      recipient: { name: recipientName, email: recipientEmail },
      company: {}, // Will use defaults
      subject: `Delivery Confirmed: ${trackingNumber}`,
      preheader: "Your shipment has been successfully delivered",
      delivery: {
        trackingNumber,
        deliveredAt,
        deliveredTo,
      },
    });
  },

  /**
   * Create an invoice notification email
   */
  invoiceReady: (
    recipientName: string,
    recipientEmail: string,
    invoiceNumber: string,
    amount: string,
    dueDate: string,
    downloadUrl: string
  ): EmailTemplate => {
    return createInvoiceNotificationTemplate({
      recipient: { name: recipientName, email: recipientEmail },
      company: {}, // Will use defaults
      subject: `Invoice ${invoiceNumber} - ${amount}`,
      preheader: `Your invoice is ready for download`,
      invoice: {
        number: invoiceNumber,
        amount,
        dueDate,
        downloadUrl,
        items: [],
      },
    });
  },

  /**
   * Create a welcome email for new users
   */
  welcomeUser: (
    recipientName: string,
    recipientEmail: string,
    loginUrl: string,
    role?: string
  ): EmailTemplate => {
    return createWelcomeEmailTemplate({
      recipient: { name: recipientName, email: recipientEmail },
      company: {}, // Will use defaults
      subject: "Welcome to Shamwaa Logistics!",
      preheader: "Your account has been successfully created",
      user: { name: recipientName, ...(role && { role }) },
      loginUrl,
    });
  },

  /**
   * Create a password reset email
   */
  resetPassword: (
    recipientName: string,
    recipientEmail: string,
    resetUrl: string,
    expiresIn: string = "1 hour"
  ): EmailTemplate => {
    return createPasswordResetTemplate({
      recipient: { name: recipientName, email: recipientEmail },
      company: {}, // Will use defaults
      subject: "Reset Your Password - Shamwaa Logistics",
      preheader: "You requested to reset your password",
      resetUrl,
      expiresIn,
    });
  },

  /**
   * Create a general notification email
   */
  notify: (
    recipientName: string,
    recipientEmail: string,
    title: string,
    message: string,
    type: "info" | "success" | "warning" | "error" = "info",
    actionText?: string,
    actionUrl?: string
  ): EmailTemplate => {
    return createNotificationEmailTemplate({
      recipient: { name: recipientName, email: recipientEmail },
      company: {}, // Will use defaults
      subject: title,
      preheader: message.substring(0, 100) + "...",
      notification: {
        title,
        message,
        type,
        ...(actionText && { actionText }),
        ...(actionUrl && { actionUrl }),
      },
    });
  },
} as const;

/**
 * Template validation utility
 */
export const validateTemplate = (template: EmailTemplate): boolean => {
  return !!(
    template.subject &&
    template.html &&
    template.text &&
    template.subject.length > 0 &&
    template.html.length > 0 &&
    template.text.length > 0
  );
};

/**
 * Template preview utility (for development/testing)
 */
export const previewTemplate = (template: EmailTemplate): void => {
  if (typeof window !== "undefined") {
    const previewWindow = window.open("", "_blank");
    if (previewWindow) {
      previewWindow.document.write(template.html);
      previewWindow.document.close();
    }
  } else {
    console.log("Email Template Preview:");
    console.log("Subject:", template.subject);
    console.log("HTML Length:", template.html.length);
    console.log("Text Version:", template.text);
  }
};
