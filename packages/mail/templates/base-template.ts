// Base email template structure for Shamwaa Logistics

import {
  getBaseStyles,
  createHeader,
  createFooter,
  BRAND_COLORS,
  TYPOGRAPHY,
} from "./components";
import type {
  EmailTemplateData,
  TemplateOptions,
  SocialLink,
  EmailTemplate,
} from "./types";

// Default Shamwaa Logistics company information
export const DEFAULT_COMPANY_INFO = {
  name: "Shamwaa Africa",
  logo: "https://service.shamwaafrica.com/storage/v1/object/public/cdn/logos/shamwaa-logo-black.png",
  address:
    "Mezzanine Floor, Victoria Place Building, Old Bagamoyo Road Dar es Salaam, Tanzania",
  phone:
    "+*********** 000 / +*********** 418 / +86 191 574 65832 / +86 135 0224 6489",
  email: "<EMAIL>",
  website: "www.shamwaa.com",
};

// Default social media links for Shamwaa Logistics
export const DEFAULT_SOCIAL_LINKS: SocialLink[] = [
  {
    platform: "linkedin",
    url: "https://linkedin.com/company/shamwaa-logistics",
  },
  { platform: "facebook", url: "https://facebook.com/shamwaalogistics" },
  { platform: "twitter", url: "https://twitter.com/shamwaalogistics" },
  { platform: "whatsapp", url: "https://wa.me/255659900000" },
];

// Default template options
export const DEFAULT_TEMPLATE_OPTIONS: TemplateOptions = {
  primaryColor: BRAND_COLORS.primary,
  secondaryColor: BRAND_COLORS.secondary,
  backgroundColor: BRAND_COLORS.background,
  textColor: BRAND_COLORS.text,
  linkColor: BRAND_COLORS.primary,
  fontFamily: TYPOGRAPHY.fontFamily,
  maxWidth: "600px",
};

/**
 * Creates the base HTML structure for all email templates
 */
export const createBaseTemplate = (
  data: EmailTemplateData,
  content: string,
  options: Partial<TemplateOptions> = {}
): string => {
  const opts = { ...DEFAULT_TEMPLATE_OPTIONS, ...options };
  const company = { ...DEFAULT_COMPANY_INFO, ...data.company };

  // Preheader text for email preview
  const preheader = data.preheader
    ? `
    <div class="preheader">${data.preheader}</div>
  `
    : "";

  return `
    <!DOCTYPE html>
    <html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta http-equiv="X-UA-Compatible" content="IE=edge">
      <meta name="x-apple-disable-message-reformatting">
      <title>${data.subject}</title>
      
      ${getBaseStyles()}
      
      <!--[if mso]>
      <noscript>
        <xml>
          <o:OfficeDocumentSettings>
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml>
      </noscript>
      <![endif]-->
    </head>
    
    <body style="margin: 0; padding: 0; background-color: ${opts.backgroundColor}; font-family: ${opts.fontFamily};">
      ${preheader}
      
      <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
        <tr>
          <td style="padding: 0;">
            <div style="max-width: ${opts.maxWidth}; margin: 0 auto; background-color: ${opts.backgroundColor};">
              
              <!-- Header -->
              ${createHeader(company)}
              
              <!-- Main Content -->
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" class="container">
                <tr>
                  <td style="padding: 0 30px 40px 30px; background-color: ${opts.backgroundColor};" class="content">
                    ${content}
                  </td>
                </tr>
              </table>
              
              <!-- Footer -->
              ${createFooter(company, DEFAULT_SOCIAL_LINKS)}
              
            </div>
          </td>
        </tr>
      </table>
    </body>
    </html>
  `;
};

/**
 * Creates a simple text version of the email
 */
export const createTextVersion = (
  data: EmailTemplateData,
  content: string
): string => {
  const company = { ...DEFAULT_COMPANY_INFO, ...data.company };

  return `
${company.name}
${data.subject}

${content}

---
${company.name}
${company.address}
Phone: ${company.phone}
Email: ${company.email}
Website: ${company.website}

© ${new Date().getFullYear()} ${company.name}. All rights reserved.
  `.trim();
};

/**
 * Base template builder class
 */
export class EmailTemplateBuilder {
  private data: EmailTemplateData;
  private options: Partial<TemplateOptions>;
  private contentSections: string[] = [];

  constructor(data: EmailTemplateData, options: Partial<TemplateOptions> = {}) {
    this.data = data;
    this.options = options;
  }

  /**
   * Add a content section to the email
   */
  addSection(html: string): this {
    this.contentSections.push(html);
    return this;
  }

  /**
   * Add a heading section
   */
  addHeading(text: string, level: 1 | 2 | 3 = 1): this {
    const styles = {
      1: { fontSize: "24px", fontWeight: "600", margin: "0 0 20px 0" },
      2: { fontSize: "20px", fontWeight: "600", margin: "0 0 16px 0" },
      3: { fontSize: "18px", fontWeight: "500", margin: "0 0 12px 0" },
    };

    const style = styles[level];

    this.addSection(`
      <h${level} style="font-family: ${TYPOGRAPHY.fontFamily}; font-size: ${style.fontSize}; 
                        font-weight: ${style.fontWeight}; color: ${BRAND_COLORS.text}; 
                        margin: ${style.margin}; line-height: 1.2;">
        ${text}
      </h${level}>
    `);

    return this;
  }

  /**
   * Add a paragraph section
   */
  addParagraph(text: string, style?: string): this {
    this.addSection(`
      <p style="font-family: ${TYPOGRAPHY.fontFamily}; font-size: 16px; line-height: 1.5; 
                 color: ${BRAND_COLORS.text}; margin: 0 0 16px 0; ${style || ""}">
        ${text}
      </p>
    `);

    return this;
  }

  /**
   * Add a spacer
   */
  addSpacer(height: string = "20px"): this {
    this.addSection(`
      <div style="height: ${height};"></div>
    `);

    return this;
  }

  /**
   * Build the final email template
   */
  build(): EmailTemplate {
    const htmlContent = this.contentSections.join("\n");
    const textContent = this.contentSections
      .map((section) => section.replace(/<[^>]*>/g, "").trim())
      .filter((section) => section.length > 0)
      .join("\n\n");

    return {
      subject: this.data.subject,
      html: createBaseTemplate(this.data, htmlContent, this.options),
      text: createTextVersion(this.data, textContent),
    };
  }
}

/**
 * Utility function to create a simple email template
 */
export const createSimpleTemplate = (
  data: EmailTemplateData,
  content: {
    heading?: string;
    message: string;
    buttonText?: string;
    buttonUrl?: string;
  },
  options: Partial<TemplateOptions> = {}
): EmailTemplate => {
  const builder = new EmailTemplateBuilder(data, options);

  if (content.heading) {
    builder.addHeading(content.heading);
  }

  builder.addParagraph(content.message);

  if (content.buttonText && content.buttonUrl) {
    builder.addSpacer("30px");
    builder.addSection(`
      <div style="text-align: center;">
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="margin: 0 auto;">
          <tr>
            <td style="border-radius: 6px; background-color: ${BRAND_COLORS.primary};">
              <a href="${content.buttonUrl}" 
                 style="display: inline-block; padding: 12px 24px; font-family: ${TYPOGRAPHY.fontFamily}; 
                        font-size: 16px; font-weight: 600; color: #ffffff; text-decoration: none; 
                        border-radius: 6px;">
                ${content.buttonText}
              </a>
            </td>
          </tr>
        </table>
      </div>
    `);
  }

  return builder.build();
};
