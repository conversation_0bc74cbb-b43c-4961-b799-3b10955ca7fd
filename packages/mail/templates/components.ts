// Reusable email components for Shamwaa Logistics templates

import type { ButtonProps, ImageProps, SocialLink, CompanyInfo } from './types';

// Shamwaa Logistics brand colors and styling
export const BRAND_COLORS = {
  primary: '#E53E3E', // hsl(11 98% 46%) converted to hex
  primaryHover: '#C53030', // hsl(11 98% 42%) converted to hex
  secondary: '#4A5568',
  success: '#38A169',
  warning: '#D69E2E',
  danger: '#E53E3E',
  text: '#2D3748',
  textSecondary: '#4A5568',
  textLight: '#718096',
  background: '#FFFFFF',
  backgroundLight: '#F7FAFC',
  border: '#E2E8F0',
};

export const TYPOGRAPHY = {
  fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
  heading: {
    fontSize: '24px',
    fontWeight: '600',
    lineHeight: '1.2',
    color: BRAND_COLORS.text,
  },
  subheading: {
    fontSize: '18px',
    fontWeight: '500',
    lineHeight: '1.3',
    color: BRAND_COLORS.text,
  },
  body: {
    fontSize: '16px',
    fontWeight: '400',
    lineHeight: '1.5',
    color: BRAND_COLORS.text,
  },
  small: {
    fontSize: '14px',
    fontWeight: '400',
    lineHeight: '1.4',
    color: BRAND_COLORS.textSecondary,
  },
};

// Base email container styles
export const getBaseStyles = () => `
  <style>
    @media only screen and (max-width: 600px) {
      .container { width: 100% !important; }
      .content { padding: 20px !important; }
      .button { width: 100% !important; display: block !important; }
      .mobile-hide { display: none !important; }
      .mobile-center { text-align: center !important; }
    }
    
    .preheader {
      display: none !important;
      visibility: hidden;
      mso-hide: all;
      font-size: 1px;
      color: transparent;
      line-height: 1px;
      max-height: 0;
      max-width: 0;
      opacity: 0;
      overflow: hidden;
    }
  </style>
`;

// Email header with logo and company info
export const createHeader = (company: CompanyInfo): string => `
  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
    <tr>
      <td style="padding: 40px 0 20px 0; text-align: center; background-color: ${BRAND_COLORS.background};">
        <img src="${company.logo}" alt="${company.name}" width="120" height="auto" style="display: block; margin: 0 auto; max-width: 120px;">
        <div style="margin-top: 16px;">
          <h1 style="margin: 0; font-family: ${TYPOGRAPHY.fontFamily}; font-size: 28px; font-weight: 600; color: ${BRAND_COLORS.primary};">
            ${company.name}
          </h1>
          <p style="margin: 8px 0 0 0; font-family: ${TYPOGRAPHY.fontFamily}; font-size: 14px; color: ${BRAND_COLORS.textLight};">
            Professional Logistics Solutions
          </p>
        </div>
      </td>
    </tr>
  </table>
`;

// Email footer with company info and social links
export const createFooter = (company: CompanyInfo, socialLinks?: SocialLink[]): string => {
  const socialLinksHtml = socialLinks ? createSocialLinks(socialLinks) : '';
  
  return `
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
      <tr>
        <td style="padding: 40px 30px; background-color: ${BRAND_COLORS.backgroundLight}; border-top: 1px solid ${BRAND_COLORS.border};">
          <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
            <tr>
              <td style="text-align: center;">
                ${socialLinksHtml}
                <div style="margin-top: 20px;">
                  <p style="margin: 0 0 8px 0; font-family: ${TYPOGRAPHY.fontFamily}; font-size: 14px; font-weight: 600; color: ${BRAND_COLORS.text};">
                    ${company.name}
                  </p>
                  <p style="margin: 0 0 8px 0; font-family: ${TYPOGRAPHY.fontFamily}; font-size: 12px; color: ${BRAND_COLORS.textLight};">
                    ${company.address}
                  </p>
                  <p style="margin: 0 0 8px 0; font-family: ${TYPOGRAPHY.fontFamily}; font-size: 12px; color: ${BRAND_COLORS.textLight};">
                    Phone: ${company.phone}
                  </p>
                  <p style="margin: 0 0 8px 0; font-family: ${TYPOGRAPHY.fontFamily}; font-size: 12px; color: ${BRAND_COLORS.textLight};">
                    Email: <a href="mailto:${company.email}" style="color: ${BRAND_COLORS.primary}; text-decoration: none;">${company.email}</a>
                  </p>
                  <p style="margin: 0; font-family: ${TYPOGRAPHY.fontFamily}; font-size: 12px; color: ${BRAND_COLORS.textLight};">
                    Website: <a href="https://${company.website}" style="color: ${BRAND_COLORS.primary}; text-decoration: none;">${company.website}</a>
                  </p>
                </div>
                <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid ${BRAND_COLORS.border};">
                  <p style="margin: 0; font-family: ${TYPOGRAPHY.fontFamily}; font-size: 11px; color: ${BRAND_COLORS.textLight};">
                    © ${new Date().getFullYear()} ${company.name}. All rights reserved.
                  </p>
                </div>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  `;
};

// Reusable button component
export const createButton = (props: ButtonProps): string => {
  const colors = {
    primary: { bg: BRAND_COLORS.primary, hover: BRAND_COLORS.primaryHover },
    secondary: { bg: BRAND_COLORS.secondary, hover: '#2D3748' },
    success: { bg: BRAND_COLORS.success, hover: '#2F855A' },
    warning: { bg: BRAND_COLORS.warning, hover: '#B7791F' },
    danger: { bg: BRAND_COLORS.danger, hover: '#C53030' },
  };

  const sizes = {
    small: { padding: '8px 16px', fontSize: '14px' },
    medium: { padding: '12px 24px', fontSize: '16px' },
    large: { padding: '16px 32px', fontSize: '18px' },
  };

  const color = colors[props.color || 'primary'];
  const size = sizes[props.size || 'medium'];

  return `
    <table role="presentation" cellspacing="0" cellpadding="0" border="0">
      <tr>
        <td style="border-radius: 6px; background-color: ${color.bg};">
          <a href="${props.url}" 
             style="display: inline-block; padding: ${size.padding}; font-family: ${TYPOGRAPHY.fontFamily}; 
                    font-size: ${size.fontSize}; font-weight: 600; color: #ffffff; text-decoration: none; 
                    border-radius: 6px; transition: background-color 0.2s;"
             onmouseover="this.style.backgroundColor='${color.hover}'"
             onmouseout="this.style.backgroundColor='${color.bg}'">
            ${props.text}
          </a>
        </td>
      </tr>
    </table>
  `;
};

// Image component with responsive styling
export const createImage = (props: ImageProps): string => `
  <img src="${props.src}" 
       alt="${props.alt}" 
       width="${props.width || 'auto'}" 
       height="${props.height || 'auto'}"
       style="display: block; max-width: 100%; height: auto; ${props.style || ''}"
  />
`;

// Social media links
export const createSocialLinks = (links: SocialLink[]): string => {
  const socialIcons = {
    facebook: '📘',
    twitter: '🐦',
    linkedin: '💼',
    instagram: '📷',
    whatsapp: '💬',
  };

  const linksHtml = links.map(link => `
    <a href="${link.url}" style="display: inline-block; margin: 0 8px; text-decoration: none; font-size: 20px;">
      ${socialIcons[link.platform]}
    </a>
  `).join('');

  return `
    <div style="text-align: center; margin-bottom: 20px;">
      ${linksHtml}
    </div>
  `;
};

// Status badge component
export const createStatusBadge = (status: string, type: 'info' | 'success' | 'warning' | 'error' = 'info'): string => {
  const colors = {
    info: { bg: '#EBF8FF', text: '#2B6CB0', border: '#90CDF4' },
    success: { bg: '#F0FFF4', text: '#276749', border: '#9AE6B4' },
    warning: { bg: '#FFFBEB', text: '#B7791F', border: '#F6E05E' },
    error: { bg: '#FED7D7', text: '#C53030', border: '#FEB2B2' },
  };

  const color = colors[type];

  return `
    <span style="display: inline-block; padding: 4px 12px; background-color: ${color.bg}; 
                 color: ${color.text}; border: 1px solid ${color.border}; border-radius: 16px; 
                 font-family: ${TYPOGRAPHY.fontFamily}; font-size: 12px; font-weight: 500;">
      ${status}
    </span>
  `;
};

// Divider component
export const createDivider = (margin: string = '20px'): string => `
  <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
    <tr>
      <td style="padding: ${margin} 0;">
        <div style="height: 1px; background-color: ${BRAND_COLORS.border};"></div>
      </td>
    </tr>
  </table>
`;

// Alert box component
export const createAlert = (message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info'): string => {
  const colors = {
    info: { bg: '#EBF8FF', text: '#2B6CB0', border: '#3182CE' },
    success: { bg: '#F0FFF4', text: '#276749', border: '#38A169' },
    warning: { bg: '#FFFBEB', text: '#B7791F', border: '#D69E2E' },
    error: { bg: '#FED7D7', text: '#C53030', border: '#E53E3E' },
  };

  const color = colors[type];

  return `
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
      <tr>
        <td style="padding: 16px; background-color: ${color.bg}; border-left: 4px solid ${color.border}; border-radius: 4px;">
          <p style="margin: 0; font-family: ${TYPOGRAPHY.fontFamily}; font-size: 14px; color: ${color.text};">
            ${message}
          </p>
        </td>
      </tr>
    </table>
  `;
};
