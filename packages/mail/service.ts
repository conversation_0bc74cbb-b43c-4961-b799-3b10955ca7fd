import nodemailer from "nodemailer";
import type { Transporter, SendMailOptions } from "nodemailer";
//
import { EmailTemplate } from "./templates/types";

// Email configuration interface
export interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
  from: {
    name: string;
    email: string;
  };
}

// Send email options interface
export interface SendEmailOptions {
  from: string;
  to: string | string[];
  subject: string;
  html?: string;
  text?: string;
  template?: EmailTemplate;
  attachments?: Array<{
    filename: string;
    content?: string | Buffer;
    path?: string;
    contentType?: string;
  }>;
}

// Email response interface
export interface EmailResponse {
  success: boolean;
  messageId?: string;
  error?: string;
}

/**
 * Default SMTP configuration with placeholder values
 * Replace these with your actual SMTP provider settings
 */
const defaultConfig: EmailConfig = {
  host: process.env.SMTP_HOST!, // Placeholder: Gmail SMTP
  port: 465,
  secure: true, // true for 465, false for other ports
  auth: {
    user: process.env.SMTP_USER!,
    pass: process.env.SMTP_PASS!,
  },
  from: {
    name: process.env.SMTP_FROM_NAME!,
    email: process.env.SMTP_FROM_EMAIL!,
  },
};

/**
 * Mailer Class - Reusable Nodemailer instance
 */
export class Mailer {
  private transporter: Transporter;
  private config: EmailConfig;

  constructor(config?: Partial<EmailConfig>) {
    this.config = { ...defaultConfig, ...config };
    this.transporter = this.createTransporter();
  }

  /**
   * Create Nodemailer transporter with SMTP configuration
   */

  private createTransporter(): Transporter {
    return nodemailer.createTransport({
      host: this.config.host,
      port: this.config.port,
      secure: this.config.secure,
      auth: {
        user: this.config.auth.user,
        pass: this.config.auth.pass,
      },
      // Additional options for better reliability
      pool: true, // Use connection pooling
      maxConnections: 5, // Limit concurrent connections
      maxMessages: 100, // Limit messages per connection
      rateDelta: 1000, // Rate limiting: 1 second between messages
      rateLimit: 5, // Rate limiting: max 5 messages per rateDelta
    });
  }

  /**
   * Verify SMTP connection
   */
  async verifyConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      console.log("✅ SMTP connection verified successfully");
      return true;
    } catch (error) {
      console.error("❌ SMTP connection failed:", error);
      return false;
    }
  }

  /**
   * Send email with comprehensive options
   */
  async sendEmail(options: SendEmailOptions): Promise<EmailResponse> {
    try {
      const mailOptions: SendMailOptions = {
        from: options.from,
        to: Array.isArray(options.to) ? options.to.join(", ") : options.to,
        subject: options.subject,
        html: options.html || options.template?.html,
        text: options.text || options.template?.text,
        attachments: options.attachments,
      };

      const result = await this.transporter.sendMail(mailOptions);

      return {
        success: true,
        messageId: result.messageId,
      };
    } catch (error: any) {
      console.error("❌ Failed to send email:", error);
      return {
        success: false,
        error: error.message || "Failed to send email",
      };
    }
  }

  /**
   * Send verification email
   */
  async sendVerificationEmail(
    to: string,
    verificationToken: string
  ): Promise<EmailResponse> {
    const baseUrl: string = process.env.AUTH_URL!;
    const verificationUrl = `${baseUrl}/email/verified?token=${verificationToken}`;

    return this.sendEmail({
      from: "",
      to,
      subject: "Verify Your Email Address",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">Verify Your Email Address</h2>
          <p>Thank you for signing up! Please click the button below to verify your email address:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${verificationUrl}"
               style="background-color: #007bff; color: white; padding: 12px 24px;
                      text-decoration: none; border-radius: 5px; display: inline-block;">
              Verify Email Address
            </a>
          </div>
          <p>If the button doesn't work, copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #666;">${verificationUrl}</p>
          <p style="color: #666; font-size: 14px;">
            This link will expire in 24 hours. If you didn't create an account, please ignore this email.
          </p>
        </div>
      `,
      text: `
        Verify Your Email Address

        Thank you for signing up! Please visit the following link to verify your email address:
        ${verificationUrl}

        This link will expire in 24 hours. If you didn't create an account, please ignore this email.
      `,
    });
  }

  /**
   * Send password reset email
   */
  async sendPasswordResetEmail(
    to: string,
    resetToken: string
  ): Promise<EmailResponse> {
    const baseUrl: string = process.env.AUTH_URL!;
    const resetUrl = `${baseUrl}/auth/reset-password?token=${resetToken}`;

    return this.sendEmail({
      from: "",
      to,
      subject: "Reset Your Password",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">Reset Your Password</h2>
          <p>You requested to reset your password. Click the button below to set a new password:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}"
               style="background-color: #dc3545; color: white; padding: 12px 24px;
                      text-decoration: none; border-radius: 5px; display: inline-block;">
              Reset Password
            </a>
          </div>
          <p>If the button doesn't work, copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #666;">${resetUrl}</p>
          <p style="color: #666; font-size: 14px;">
            This link will expire in 1 hour. If you didn't request a password reset, please ignore this email.
          </p>
        </div>
      `,
      text: `
        Reset Your Password

        You requested to reset your password. Please visit the following link to set a new password:
        ${resetUrl}

        This link will expire in 1 hour. If you didn't request a password reset, please ignore this email.
      `,
    });
  }

  /**
   * Send welcome email
   */
  async sendWelcomeEmail(
    to: string,
    userName: string,
    dashboardUrl: string = "http://localhost:3000/dashboard"
  ): Promise<EmailResponse> {
    return this.sendEmail({
      from: "",
      to,
      subject: "Welcome to LegionEdge!",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">Welcome to LegionEdge, ${userName}!</h2>
          <p>Thank you for joining us! Your account has been successfully created.</p>
          <p>You can now access your dashboard and start exploring all the features we have to offer:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${dashboardUrl}"
               style="background-color: #28a745; color: white; padding: 12px 24px;
                      text-decoration: none; border-radius: 5px; display: inline-block;">
              Go to Dashboard
            </a>
          </div>
          <p>If you have any questions, feel free to reach out to our support team.</p>
          <p style="color: #666; font-size: 14px;">
            Best regards,<br>
            The LegionEdge Team
          </p>
        </div>
      `,
      text: `
        Welcome to LegionEdge, ${userName}!

        Thank you for joining us! Your account has been successfully created.

        You can now access your dashboard: ${dashboardUrl}

        If you have any questions, feel free to reach out to our support team.

        Best regards,
        The LegionEdge Team
      `,
    });
  }

  /**
   * Send notification email
   */
  async sendNotificationEmail(
    to: string,
    title: string,
    message: string,
    actionUrl?: string,
    actionText?: string
  ): Promise<EmailResponse> {
    const actionButton =
      actionUrl && actionText
        ? `
      <div style="text-align: center; margin: 30px 0;">
        <a href="${actionUrl}"
           style="background-color: #007bff; color: white; padding: 12px 24px;
                  text-decoration: none; border-radius: 5px; display: inline-block;">
          ${actionText}
        </a>
      </div>
    `
        : "";

    return this.sendEmail({
      from: "",
      to,
      subject: title,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">${title}</h2>
          <p>${message}</p>
          ${actionButton}
          <p style="color: #666; font-size: 14px;">
            Best regards,<br>
            The LegionEdge Team
          </p>
        </div>
      `,
      text: `
        ${title}

        ${message}

        ${actionUrl ? `Action link: ${actionUrl}` : ""}

        Best regards,
        The LegionEdge Team
      `,
    });
  }

  /**
   * Close the transporter connection
   */
  async close(): Promise<void> {
    this.transporter.close();
  }

  /**
   * Get current configuration
   */
  getConfig(): EmailConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<EmailConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.transporter = this.createTransporter();
  }
}

// Create and export default mail instance
export const mail = new Mailer();

// Export factory function for custom configurations
export const createMailer = (config?: Partial<EmailConfig>): Mailer => {
  return new Mailer(config);
};

// Export utility functions
export const emailUtils = {
  /**
   * Validate email address format
   */
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  /**
   * Extract domain from email
   */
  getDomain: (email: string): string => {
    return email.split("@")[1] || "";
  },

  /**
   * Sanitize email for display
   */
  sanitizeEmail: (email: string): string => {
    const [local, domain] = email.split("@");
    if (!local || !domain) return email;
    if (local.length <= 3) return email;
    return `${local.substring(0, 2)}***@${domain}`;
  },

  /**
   * Generate verification token (simple implementation)
   */
  generateToken: (): string => {
    return (
      Math.random().toString(36).substring(2, 15) +
      Math.random().toString(36).substring(2, 15)
    );
  },
};
