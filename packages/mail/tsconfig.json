{
  "extends": "@workspace/typescript-config/base.json",
  "compilerOptions": {
    // Language and Environment
    "target": "ES2020",
    "lib": [
      "DOM",
      "DOM.Iterable",
      "ES2020"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    // ES6 Module Support
    "module": "ES2020",
    "moduleResolution": "node",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "incremental": true,
    // Decorator Support
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    // Path Mapping for External Files
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "./*"
      ]
    },
    // ES6 Import/Export Configuration
    "forceConsistentCasingInFileNames": true,
    "verbatimModuleSyntax": false,
    "allowImportingTsExtensions": false,
    // Type Checking
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "exactOptionalPropertyTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    // Emit
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    // Interop Constraints
    "allowUmdGlobalAccess": true,
    "jsx": "preserve",
  },
  "include": [
    "**/*.js",
    "**/*.ts",
  ],
  "exclude": [
    "node_modules"
  ]
}