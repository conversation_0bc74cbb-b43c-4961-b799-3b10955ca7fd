{"name": "@workspace/mail", "version": "1.0.0", "description": "Composable email template system and Nodemailer instance for Shamwaa Logistics", "exports": {".": "./index.ts"}, "keywords": ["email", "nodemailer", "smtp", "mail", "templates", "logistics", "shamwaa"], "author": "Shamwaa Logistics", "license": "ISC", "packageManager": "pnpm@10.4.1", "dependencies": {"@aws-sdk/client-ses": "^3.830.0", "net": "^1.0.2", "nodemailer": "^7.0.3"}, "devDependencies": {"@types/node": "^24.0.3", "@types/nodemailer": "^6.4.17", "typescript": "^5.7.3"}}